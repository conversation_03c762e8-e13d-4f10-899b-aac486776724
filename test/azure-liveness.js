const {
  app, mongoose, validImagePath, validVideoPath,
} = require('./common');
const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const temp = require('temp').track();
const fs = require('fs');
const sinon = require('sinon');
const stub = require('./stub');
const AzureLivenessChallenge = require('../models/azure-liveness-challenge');
const { notifs, reset, waitFor } = require('./stub');
const User = require('../models/user');

/*
describe('azure liveness', () => {
  const challengeId = 'mocking_session';
  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    expect(res.body.pictures.length).to.equal(1);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
  });
  describe('generate session', () => {
    it('should generate liveness session', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      const livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].id).to.equal(challengeId);
      expect(livenessChallenges[0].deviceCorrelationId).to.equal('some_id');
    });

    it('should return application error when azure response unspecified err code', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'error', error: { code: 'Unspecified', message: 'some message' } });
        };
        return new Promise(impl);
      }();
      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(403);
      expect(res.body).to.eql({});
    });

    it('should return application error when azure response forbidden err code', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'error', error: { code: 'Forbidden', message: 'some message' } });
        };
        return new Promise(impl);
      }();
      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(403);
      expect(res.body).to.eql({});
    });

    it('should return application error when azure response BadArgument err code', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'error', error: { code: 'BadArgument', message: 'some message' } });
        };
        return new Promise(impl);
      }();
      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(400);
      expect(res.body).to.eql({});
    });

    it('should return application error when azure response TooManyRequest err code', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'error', error: { code: 'TooManyRequest', message: 'some message' } });
        };
        return new Promise(impl);
      }();
      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(401);
      expect(res.body).to.eql({});
    });

    it('should return application error when azure response other err code', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'error', error: { code: 'SomeCode', message: 'some message' } });
        };
        return new Promise(impl);
      }();
      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(401);
      expect(res.body).to.eql({});
      reset();
    });
  })

  describe('get liveness result', () => {
    it('should get error when file empty', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success', data: {
              "status": "ResultAvailable",
              "result": {
                "id": 1,
                "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
                "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
                "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
                "request": {
                  "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
                  "method": "POST",
                  "contentLength": 521802,
                  "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
                  "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
                },
                "response": {
                  "body": {
                    "livenessDecision": "realface",
                    "target": {
                      "faceRectangle": {
                        "top": 657,
                        "left": 162,
                        "width": 786,
                        "height": 813
                      },
                      "fileName": "video.webp",
                      "timeOffsetWithinFile": 0,
                      "imageType": "Color"
                    },
                    "modelVersionUsed": "2022-10-15-preview.04"
                  },
                  "statusCode": 200,
                  "latencyInMilliseconds": 898
                },
                "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
              },
              "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
              "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
              "authTokenTimeToLiveInSeconds": 600,
              "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
              "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
              "sessionExpired": true
            }
          });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .query({ challengeId });
      expect(res.status).to.equal(422);

      livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].frames.length).to.equal(0);
    });

    it('should get error when challenge id used by different user', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success', data: {
              "status": "ResultAvailable",
              "result": {
                "id": 1,
                "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
                "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
                "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
                "request": {
                  "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
                  "method": "POST",
                  "contentLength": 521802,
                  "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
                  "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
                },
                "response": {
                  "body": {
                    "livenessDecision": "realface",
                    "target": {
                      "faceRectangle": {
                        "top": 657,
                        "left": 162,
                        "width": 786,
                        "height": 813
                      },
                      "fileName": "video.webp",
                      "timeOffsetWithinFile": 0,
                      "imageType": "Color"
                    },
                    "modelVersionUsed": "2022-10-15-preview.04"
                  },
                  "statusCode": 200,
                  "latencyInMilliseconds": 898
                },
                "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
              },
              "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
              "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
              "authTokenTimeToLiveInSeconds": 600,
              "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
              "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
              "sessionExpired": true
            }
          });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 1)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(403);

      livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].frames.length).to.equal(0);
    });

    it('should get error when challenge id empty', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success', data: {
              "status": "ResultAvailable",
              "result": {
                "id": 1,
                "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
                "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
                "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
                "request": {
                  "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
                  "method": "POST",
                  "contentLength": 521802,
                  "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
                  "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
                },
                "response": {
                  "body": {
                    "livenessDecision": "realface",
                    "target": {
                      "faceRectangle": {
                        "top": 657,
                        "left": 162,
                        "width": 786,
                        "height": 813
                      },
                      "fileName": "video.webp",
                      "timeOffsetWithinFile": 0,
                      "imageType": "Color"
                    },
                    "modelVersionUsed": "2022-10-15-preview.04"
                  },
                  "statusCode": 200,
                  "latencyInMilliseconds": 898
                },
                "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
              },
              "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
              "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
              "authTokenTimeToLiveInSeconds": 600,
              "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
              "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
              "sessionExpired": true
            }
          });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
      expect(res.status).to.equal(403);

      livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].frames.length).to.equal(0);
    });

    it('should get error when challenge id different', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success', data: {
              "status": "ResultAvailable",
              "result": {
                "id": 1,
                "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
                "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
                "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
                "request": {
                  "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
                  "method": "POST",
                  "contentLength": 521802,
                  "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
                  "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
                },
                "response": {
                  "body": {
                    "livenessDecision": "realface",
                    "target": {
                      "faceRectangle": {
                        "top": 657,
                        "left": 162,
                        "width": 786,
                        "height": 813
                      },
                      "fileName": "video.webp",
                      "timeOffsetWithinFile": 0,
                      "imageType": "Color"
                    },
                    "modelVersionUsed": "2022-10-15-preview.04"
                  },
                  "statusCode": 200,
                  "latencyInMilliseconds": 898
                },
                "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
              },
              "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
              "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
              "authTokenTimeToLiveInSeconds": 600,
              "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
              "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
              "sessionExpired": true
            }
          });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .query({ challengeId: 'another_challenge_id' });
      expect(res.status).to.equal(403);

      livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].frames.length).to.equal(0);
    });

    it('should response failed verification when user is on shadow banned', async () => {
      user = await User.findOne({ _id: 0 });
      user.shadowBanned = true;
      await user.save();

      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success', data: {
              "status": "ResultAvailable",
              "result": {
                "id": 1,
                "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
                "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
                "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
                "request": {
                  "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
                  "method": "POST",
                  "contentLength": 521802,
                  "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
                  "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
                },
                "response": {
                  "body": {
                    "livenessDecision": "realface",
                    "target": {
                      "faceRectangle": {
                        "top": 657,
                        "left": 162,
                        "width": 786,
                        "height": 813
                      },
                      "fileName": "video.webp",
                      "timeOffsetWithinFile": 0,
                      "imageType": "Color"
                    },
                    "modelVersionUsed": "2022-10-15-preview.04"
                  },
                  "statusCode": 200,
                  "latencyInMilliseconds": 898
                },
                "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
              },
              "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
              "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
              "authTokenTimeToLiveInSeconds": 600,
              "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
              "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
              "sessionExpired": true
            }
          });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        rejectionReason: 'Face challenge failed.',
        verificationStatus: 'rejected',
      });

      livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].frames.length).to.equal(1);
      expect(livenessChallenges[0].rejectionReason).to.equal('User is banned.');
      expect(livenessChallenges[0].livenessSuccess).to.equal(null);
      expect(livenessChallenges[0].livenessFailureReason).to.equal(null);

      user = await User.findOne({ _id: 0 });
      expect(user.verification.verifiedBy).to.equal(null);
      expect(user.verification.verifiedDate).to.not.equal(null);
      expect(user.verification.rejectionReason).to.equal('Face challenge failed.');
    });

    it('should not re-verify if user already verified', async () => {
      user = await User.findOne({ _id: 0 });
      user.verification.status = 'verified';
      await user.save();

      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success', data: {
              "status": "ResultAvailable",
              "result": {
                "id": 1,
                "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
                "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
                "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
                "request": {
                  "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
                  "method": "POST",
                  "contentLength": 521802,
                  "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
                  "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
                },
                "response": {
                  "body": {
                    "livenessDecision": "realface",
                    "target": {
                      "faceRectangle": {
                        "top": 657,
                        "left": 162,
                        "width": 786,
                        "height": 813
                      },
                      "fileName": "video.webp",
                      "timeOffsetWithinFile": 0,
                      "imageType": "Color"
                    },
                    "modelVersionUsed": "2022-10-15-preview.04"
                  },
                  "statusCode": 200,
                  "latencyInMilliseconds": 898
                },
                "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
              },
              "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
              "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
              "authTokenTimeToLiveInSeconds": 600,
              "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
              "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
              "sessionExpired": true
            }
          });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        verificationStatus: 'verified',
      });
    });

    it('should get error when file empty', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success', data: {
              "status": "ResultAvailable",
              "result": {
                "id": 1,
                "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
                "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
                "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
                "request": {
                  "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
                  "method": "POST",
                  "contentLength": 521802,
                  "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
                  "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
                },
                "response": {
                  "body": {
                    "livenessDecision": "realface",
                    "target": {
                      "faceRectangle": {
                        "top": 657,
                        "left": 162,
                        "width": 786,
                        "height": 813
                      },
                      "fileName": "video.webp",
                      "timeOffsetWithinFile": 0,
                      "imageType": "Color"
                    },
                    "modelVersionUsed": "2022-10-15-preview.04"
                  },
                  "statusCode": 200,
                  "latencyInMilliseconds": 898
                },
                "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
              },
              "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
              "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
              "authTokenTimeToLiveInSeconds": 600,
              "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
              "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
              "sessionExpired": true
            }
          });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .query({ challengeId });
      expect(res.status).to.equal(422);

      livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].frames.length).to.equal(0);
    });

    it('should get error when azure service return Unspecified err', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'error', error: { code: 'Unspecified', message: 'some message' } });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(403);

      livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].livenessSuccess).to.equal(false);
      expect(livenessChallenges[0].livenessFailureReason).to.equal('some message');
      expect(livenessChallenges[0].frames.length).to.equal(1);
    });

    it('should get error when azure service return Forbidden err', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'error', error: { code: 'Forbidden', message: 'some message' } });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(403);

      livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].livenessSuccess).to.equal(false);
      expect(livenessChallenges[0].livenessFailureReason).to.equal('some message');
      expect(livenessChallenges[0].frames.length).to.equal(1);
    });

    it('should get error when azure service return BadArgument err', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'error', error: { code: 'BadArgument', message: 'some message' } });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(400);

      livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].livenessSuccess).to.equal(false);
      expect(livenessChallenges[0].livenessFailureReason).to.equal('some message');
      expect(livenessChallenges[0].frames.length).to.equal(1);
    });

    it('should get error when azure service return TooManyRequest err', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'error', error: { code: 'TooManyRequest', message: 'some message' } });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(401);

      livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].livenessSuccess).to.equal(false);
      expect(livenessChallenges[0].livenessFailureReason).to.equal('some message');
      expect(livenessChallenges[0].frames.length).to.equal(1);
    });

    it('should get error when azure service return other err', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'error', error: { code: 'SomeError', message: 'some message' } });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(403);

      livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].livenessSuccess).to.equal(false);
      expect(livenessChallenges[0].livenessFailureReason).to.equal('some message');
      expect(livenessChallenges[0].frames.length).to.equal(1);
    });

    it('should pending verification when there is no decision', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success', data: {
              "status": "ResultAvailable",
              "result": {
                "id": 1,
                "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
                "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
                "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
                "request": {
                  "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
                  "method": "POST",
                  "contentLength": 521802,
                  "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
                  "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
                },
                "response": {},
                "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
              },
              "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
              "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
              "authTokenTimeToLiveInSeconds": 600,
              "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
              "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
              "sessionExpired": true
            }
          });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        verificationStatus: 'pending',
      });

      const livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].rejectionReason).to.equal('Automated face comparison could not be performed.');

      user = await User.findOne({ _id: 0 });
      expect(user.verification.status).to.equal('pending');
      expect(user.verification.verifiedDate).to.not.equal(null);
    });

    it('should pending verification when liveness detection result is uncertain', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success', data: {
              "status": "ResultAvailable",
              "result": {
                "id": 1,
                "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
                "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
                "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
                "request": {
                  "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
                  "method": "POST",
                  "contentLength": 521802,
                  "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
                  "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
                },
                "response": {
                  "body": {
                    "livenessDecision": "uncertain",
                    "target": {
                      "faceRectangle": {
                        "top": 657,
                        "left": 162,
                        "width": 786,
                        "height": 813
                      },
                      "fileName": "video.webp",
                      "timeOffsetWithinFile": 0,
                      "imageType": "Color"
                    },
                    "modelVersionUsed": "2022-10-15-preview.04"
                  },
                  "statusCode": 200,
                  "latencyInMilliseconds": 898
                },
                "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
              },
              "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
              "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
              "authTokenTimeToLiveInSeconds": 600,
              "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
              "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
              "sessionExpired": true
            }
          });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        verificationStatus: 'pending',
      });

      const livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].rejectionReason).to.equal('Automated face comparison could not be performed.');

      user = await User.findOne({ _id: 0 });
      expect(user.verification.status).to.equal('pending');
      expect(user.verification.verifiedDate).to.not.equal(null);
    });

    it('should reject verification when liveness detection result is spoofface', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success', data: {
              "status": "ResultAvailable",
              "result": {
                "id": 1,
                "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
                "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
                "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
                "request": {
                  "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
                  "method": "POST",
                  "contentLength": 521802,
                  "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
                  "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
                },
                "response": {
                  "body": {
                    "livenessDecision": "spoofface",
                    "target": {
                      "faceRectangle": {
                        "top": 657,
                        "left": 162,
                        "width": 786,
                        "height": 813
                      },
                      "fileName": "video.webp",
                      "timeOffsetWithinFile": 0,
                      "imageType": "Color"
                    },
                    "modelVersionUsed": "2022-10-15-preview.04"
                  },
                  "statusCode": 200,
                  "latencyInMilliseconds": 898
                },
                "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
              },
              "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
              "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
              "authTokenTimeToLiveInSeconds": 600,
              "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
              "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
              "sessionExpired": true
            }
          });
        };
        return new Promise(impl);
      }();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        verificationStatus: 'rejected',
        rejectionReason: 'Face challenge failed.',
      });

      const livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].rejectionReason).to.equal('Face challenge failed.');

      user = await User.findOne({ _id: 0 });
      expect(user.verification.status).to.equal('rejected');
      expect(user.verification.verifiedDate).to.not.equal(null);
      expect(user.verification.rejectionReason).to.equal('Face challenge failed.');
    });

    it('should verified when liveness detection success and face compare return success', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success', data: {
              "status": "ResultAvailable",
              "result": {
                "id": 1,
                "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
                "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
                "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
                "request": {
                  "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
                  "method": "POST",
                  "contentLength": 521802,
                  "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
                  "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
                },
                "response": {
                  "body": {
                    "livenessDecision": "realface",
                    "target": {
                      "faceRectangle": {
                        "top": 657,
                        "left": 162,
                        "width": 786,
                        "height": 813
                      },
                      "fileName": "video.webp",
                      "timeOffsetWithinFile": 0,
                      "imageType": "Color"
                    },
                    "modelVersionUsed": "2022-10-15-preview.04"
                  },
                  "statusCode": 200,
                  "latencyInMilliseconds": 898
                },
                "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
              },
              "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
              "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
              "authTokenTimeToLiveInSeconds": 600,
              "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
              "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
              "sessionExpired": true
            }
          });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceCompareFaces = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success',
            data: {
              "targetImageFace": {
                  "faceRectangle": {
                      "top": 36,
                      "left": 22,
                      "width": 57,
                      "height": 57
                  },
                  "confidence": 1.0,
                  "isIdentical": true
              },
              "sourceImageFace": {
                  "faceRectangle": {
                      "top": 36,
                      "left": 22,
                      "width": 57,
                      "height": 57
                  }
              },
              "detectionModel": "detection_01",
              "recognitionModel": "recognition_03"
            }
          });
        };
        return new Promise(impl);
      }();
      fakeRekognition.compareFaces = function (params) {
        const impl = function (resolve, reject) {
          resolve({ FaceMatches: [ {} ] });
        };
        return {
          promise: () => new Promise(impl),
        };
      };
      user = await User.findOne({ _id: 0 });
      user.appVersion = '1.13.22';
      user.locale = 'en';
      await user.save();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        verificationStatus: 'verified',
      });

      const livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].id).to.equal(challengeId);
      expect(livenessChallenges[0].user).to.equal('0');
      expect(livenessChallenges[0].deviceCorrelationId).to.equal('some_id');
      expect(livenessChallenges[0].date).to.not.null;
      expect(livenessChallenges[0].frames.length).to.equal(1);
      expect(livenessChallenges[0].frames[0].key).to.contain(`0/liveness/${challengeId}/`);
      expect(livenessChallenges[0].latencyMillis).to.equal(898);
      expect(livenessChallenges[0].livenessDecision).to.equal('realface');
      expect(livenessChallenges[0].livenessModelVersion).to.equal('2022-10-15-preview.04');
      expect(livenessChallenges[0].livenessImageType).to.equal('Color');
      expect(livenessChallenges[0].faceTop).to.equal(657);
      expect(livenessChallenges[0].faceLeft).to.equal(162);
      expect(livenessChallenges[0].faceWidth).to.equal(786);
      expect(livenessChallenges[0].faceHeight).to.equal(813);
      expect(livenessChallenges[0].livenessSuccess).to.equal(true);
      expect(livenessChallenges[0].compareFacesSuccess).to.equal(true);

      user = await User.findOne({ _id: 0 });
      expect(user.verification.status).to.equal('verified');
      expect(user.verification.verifiedDate).to.not.equal(null);
    });

    it('should rejected when liveness detection success and face compare return failed', async () => {
      fakeAzureFaceCreateLivenessSession = async function () {
        const impl = function (resolve, reject) {
          resolve({ status: 'success', data: { sessionId: 'mocking_session', authToken: 'mocking_token', deviceCorrelationId: 'some_id' } });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceGetLivenessResult = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success', data: {
              "status": "ResultAvailable",
              "result": {
                "id": 1,
                "sessionId": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
                "requestId": "fe19babb-7e54-413b-8965-4273a5ac4ed6",
                "receivedDateTime": "2024-02-15T10:16:59.9258508+00:00",
                "request": {
                  "url": "/face/v1.1-preview.1/detectLiveness/singleModal",
                  "method": "POST",
                  "contentLength": 521802,
                  "contentType": "multipart/form-data; boundary=\"1bd4ea72-8a37-4046-86a2-4c43a9a50431\"",
                  "userAgent": "VisionSDK-objective-c/0.16.1-beta.1 (Darwin iOS 17.2.1 iPhone14,5 23.2.0)"
                },
                "response": {
                  "body": {
                    "livenessDecision": "realface",
                    "target": {
                      "faceRectangle": {
                        "top": 657,
                        "left": 162,
                        "width": 786,
                        "height": 813
                      },
                      "fileName": "video.webp",
                      "timeOffsetWithinFile": 0,
                      "imageType": "Color"
                    },
                    "modelVersionUsed": "2022-10-15-preview.04"
                  },
                  "statusCode": 200,
                  "latencyInMilliseconds": 898
                },
                "digest": "D3BF335498B72E4A29DFA97598D2011C2AE32D9BE3124CFF9EEFE627AAAC7F52"
              },
              "id": "2d12b871-c4d8-40c3-a8f2-5ca4e9c8de33",
              "createdDateTime": "2024-02-15T10:15:48.0990503+00:00",
              "authTokenTimeToLiveInSeconds": 600,
              "sessionStartDateTime": "2024-02-15T10:16:42.1558515+00:00",
              "deviceCorrelationId": "4a792a85-4897-4e94-b2dc-44855f25626d",
              "sessionExpired": true
            }
          });
        };
        return new Promise(impl);
      }();
      fakeAzureFaceCompareFaces = async function () {
        const impl = function (resolve, reject) {
          resolve({
            status: 'success',
            data: {
              "targetImageFace": {
                  "faceRectangle": {
                      "top": 36,
                      "left": 22,
                      "width": 57,
                      "height": 57
                  },
                  "confidence": 1.0,
                  "isIdentical": false
              },
              "sourceImageFace": {
                  "faceRectangle": {
                      "top": 36,
                      "left": 22,
                      "width": 57,
                      "height": 57
                  }
              },
              "detectionModel": "detection_01",
              "recognitionModel": "recognition_03"
            }
          });
        };
        return new Promise(impl);
      }();
      fakeRekognition.compareFaces = function (params) {
        const impl = function (resolve, reject) {
          resolve({ UnmatchedFaces: [ {} ] });
        };
        return {
          promise: () => new Promise(impl),
        };
      };
      user = await User.findOne({ _id: 0 });
      user.appVersion = '1.13.22';
      user.locale = 'en';
      await user.save();

      res = await request(app)
        .post('/v1/liveness-new/session')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        authToken: 'mocking_token',
        challengeId: challengeId,
      });

      res = await request(app)
        .post('/v1/liveness-new/result')
        .set('authorization', 0)
        .attach('image', validImagePath)
        .query({ challengeId });
      expect(res.status).to.equal(200);
      expect(res.body).to.eql({
        verificationStatus: 'rejected',
        rejectionReason: 'Make sure your first profile picture is a picture of you.'
      });

      const livenessChallenges = await AzureLivenessChallenge.find();
      expect(livenessChallenges.length).to.equal(1);
      expect(livenessChallenges[0].id).to.equal(challengeId);
      expect(livenessChallenges[0].user).to.equal('0');
      expect(livenessChallenges[0].deviceCorrelationId).to.equal('some_id');
      expect(livenessChallenges[0].date).to.not.null;
      expect(livenessChallenges[0].frames.length).to.equal(1);
      expect(livenessChallenges[0].frames[0].key).to.contain(`0/liveness/${challengeId}/`);
      expect(livenessChallenges[0].latencyMillis).to.equal(898);
      expect(livenessChallenges[0].livenessDecision).to.equal('realface');
      expect(livenessChallenges[0].livenessModelVersion).to.equal('2022-10-15-preview.04');
      expect(livenessChallenges[0].livenessImageType).to.equal('Color');
      expect(livenessChallenges[0].faceTop).to.equal(657);
      expect(livenessChallenges[0].faceLeft).to.equal(162);
      expect(livenessChallenges[0].faceWidth).to.equal(786);
      expect(livenessChallenges[0].faceHeight).to.equal(813);
      expect(livenessChallenges[0].livenessSuccess).to.equal(true);
      expect(livenessChallenges[0].compareFacesSuccess).to.equal(false);
      expect(livenessChallenges[0].rejectionReason).to.equal('Verification does not match profile pictures.');

      user = await User.findOne({ _id: 0 });
      expect(user.verification.status).to.equal('rejected');
      expect(user.verification.verifiedDate).to.not.equal(null);
      expect(user.verification.rejectionReason).to.equal('Make sure your first profile picture is a picture of you.');
    });
  });
});
*/
