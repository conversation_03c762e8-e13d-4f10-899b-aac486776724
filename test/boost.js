const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const { app, mongoose, validImagePath } = require('./common');
const User = require('../models/user');
const UserMetadata = require('../models/user-metadata');
const BoostMetric = require('../models/boost-metric');
const coinsConstants = require('../lib/coins-constants');
const { updateUserScores } = require('../lib/score');
const { fakeAdminMessaging } = require('./stub');
const axios = require('axios');

let durationMinutes;

describe('boost', () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 3; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.10.50' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: new Date().getFullYear() - 31,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          friends: ['female', 'male'],
          personality: ['ISTP'],
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/personality')
        .set('authorization', uid)
        .send({
          mbti: 'ISTP',
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', uid)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          latitude: 21.30,
          longitude: -157.85,
        });
      expect(res.status).to.equal(200);
    }
  });

  it('basic functionality', async () => {
    // set user 1 to have a high decayed score
    user = await User.findById('1');
    user.scores.decayedScore = 80;

    await user.save();
    user = await User.findById('1');
    user.scores.decayedScore2 = 1;
    await user.save();
    user = await User.findById('2');
    user.scores.decayedScore2 = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');

    // now activate boost for user 2
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    user = await User.findById('2');
    expect(user.scores.decayedScore2).to.equal(3);

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('2');
    expect(res.body.profiles[1]._id).to.equal('1');

    // send like, then check boost metrics
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    const metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('2');
    expect(metrics[0].numActionsReceived).to.equal(1);
    expect(metrics[0].numLikesReceived).to.equal(1);
    expect(metrics[0].numLocalActionsReceived).to.equal(1);
    expect(metrics[0].numLocalLikesReceived).to.equal(1);
    expect(metrics[0].numBoosts).to.equal(1);
    expect(metrics[0].coinsSpent).to.equal(200);

    user = await User.findById('2');
    expect(user.metrics.numBoostUsed).to.equal(1);
  });

  it('boost pop up on init', async () => {

    res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 3)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 3)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    // use boosts
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 0)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    // init before boosts expire
    res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);
      expect(res.numLikesReceivedDuringBoost).to.equal(undefined);

    // receive 3 likes when boost is active
    for(let uid = 1; uid <= 3; uid++){
        res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', uid)
        .send({ user: '0' });
      expect(res.status).to.equal(200);
    }

    const metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('0');
    expect(metrics[0].numActionsReceived).to.equal(3);
    expect(metrics[0].numLikesReceived).to.equal(3);
    expect(metrics[0].numLocalActionsReceived).to.equal(3);
    expect(metrics[0].numLocalLikesReceived).to.equal(3);
    expect(metrics[0].numBoosts).to.equal(1);
    expect(metrics[0].coinsSpent).to.equal(200);

    user = await User.findById('0');
    expect(user.metrics.numBoostUsed).to.equal(1);

    user = await User.findById('0');
    user.boostExpiration = new Date();
    await user.save();


    // init after boosts expire
    //expect numLikesReceivedDuringBoost = 3
    res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);
      expect(res.body.numLikesReceivedDuringBoost).to.equal(3);

  })

  it('get boost pop up', async () => {
    res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);

    res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 3)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 3)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    // use boosts
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 0)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    // init before boosts expire
    res = await request(app)
        .get('/v1/user/boostPopup')
        .set('authorization', 0)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);
      expect(res.numLikesReceivedDuringBoost).to.equal(undefined);

    // receive 3 likes when boost is active
    for(let uid = 1; uid <= 3; uid++){
        res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', uid)
        .send({ user: '0' });
      expect(res.status).to.equal(200);
    }

    const metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('0');
    expect(metrics[0].numActionsReceived).to.equal(3);
    expect(metrics[0].numLikesReceived).to.equal(3);
    expect(metrics[0].numLocalActionsReceived).to.equal(3);
    expect(metrics[0].numLocalLikesReceived).to.equal(3);
    expect(metrics[0].numBoosts).to.equal(1);
    expect(metrics[0].coinsSpent).to.equal(200);

    user = await User.findById('0');
    expect(user.metrics.numBoostUsed).to.equal(1);

    user = await User.findById('0');
    user.boostExpiration = new Date();
    await user.save();


    // init after boosts expire
    //expect numLikesReceivedDuringBoost = 3
    res = await request(app)
        .get('/v1/user/boostPopup')
        .set('authorization', 0)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);
      expect(res.body.numLikesReceivedDuringBoost).to.equal(3);

  })

  it('distance loop still takes precedence', async () => {
    // set user 2 far away (GB)
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 2)
      .send({
        latitude: 51.75,
        longitude: 0,
      });
    expect(res.status).to.equal(200);

    // clear pending report
    user = await User.findById('2');
    user.metrics.numPendingReports = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');

    // now activate boost for user 2
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');
  });

  it('expiration', async () => {
    // set user 1 to have a high decayed score
    user = await User.findById('1');
    user.scores.decayedScore = 80;

    user = await User.findById('1');
    user.scores.decayedScore2 = 1;
    await user.save();
    user = await User.findById('2');
    user.scores.decayedScore2 = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');

    // now activate boost for user 2
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.coinProducts.boost.durationMinutes).to.equal(30);
    const expirationDate = new Date(res.body.user.boostExpiration).getTime();
    const duration = (expirationDate - Date.now()) / 60000;
    assert(duration > 29 && duration < 30);

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('2');
    expect(res.body.profiles[1]._id).to.equal('1');

    // let boost expire
    user = await User.findById('2');
    user.boostExpiration = new Date();
    await user.save();
    await updateUserScores();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.user.boostExpiration).to.equal();

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');

    // send like, then check boost metrics
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('2');
    expect(metrics[0].numActionsReceived).to.equal(0);
    expect(metrics[0].numLikesReceived).to.equal(0);
    expect(metrics[0].numLocalActionsReceived).to.equal(0);
    expect(metrics[0].numLocalLikesReceived).to.equal(0);
    expect(metrics[0].postBoostNumActionsReceived).to.equal(1);
    expect(metrics[0].postBoostNumLikesReceived).to.equal(1);

    // let more time pass
    user = await User.findById('2');
    user.boostExpiration = new Date(2000,1,1);
    await user.save();

    // send like, then check boost metrics
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('2');
    expect(metrics[0].numActionsReceived).to.equal(0);
    expect(metrics[0].numLikesReceived).to.equal(0);
    expect(metrics[0].numLocalActionsReceived).to.equal(0);
    expect(metrics[0].numLocalLikesReceived).to.equal(0);
    expect(metrics[0].postBoostNumActionsReceived).to.equal(1);
    expect(metrics[0].postBoostNumLikesReceived).to.equal(1);
  });

  it('increase duration to 60 minutes for 1.11.14', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.11.14' });
    expect(res.status).to.equal(200);
    expect(res.body.coinProducts.boost.durationMinutes).to.equal(60);

    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    const expirationDate = new Date(res.body.user.boostExpiration).getTime();
    const duration = (expirationDate - Date.now()) / 60000;
    assert(duration > 59 && duration < 60);
  });

  it('level up', async () => {
    // check coin product
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.coinProducts.levelUp.price).to.equal(coinsConstants.levelUpCost);
    expect(res.body.coinProducts.levelUp.durationMinutes).to.equal(24 * 60);
    expect(res.body.user.boostExpiration).to.equal();

    // set user 1 to have a high decayed score
    user = await User.findById('1');
    user.scores.decayedScore = 80;

    user = await User.findById('1');
    user.scores.decayedScore2 = 1;
    await user.save();
    user = await User.findById('2');
    user.scores.decayedScore2 = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');

    // not enough coins
    res = await request(app)
      .put('/v1/coins/levelUp')
      .set('authorization', 2)
      .send({ price: coinsConstants.levelUpCost });
    expect(res.status).to.equal(403);

    // now activate boost for user 2
    doc = await UserMetadata.findOne({ user: '2' });
    doc.coins = coinsConstants.levelUpCost;
    await doc.save();

    res = await request(app)
      .put('/v1/coins/levelUp')
      .set('authorization', 2)
      .send({ price: coinsConstants.levelUpCost });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('2');
    expect(res.body.profiles[1]._id).to.equal('1');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.user.boostExpiration).to.not.equal();
    const expirationDate = new Date(res.body.user.boostExpiration).getTime();
    const duration = (expirationDate - Date.now()) / 60000;
    assert(duration > 24 * 60 - 1 && duration < 24 * 60);

    doc = await UserMetadata.findOne({ user: '2' });
    expect(doc.coins).to.equal(0);

    // send like, then check boost metrics
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    const metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('2');
    expect(metrics[0].numActionsReceived).to.equal(1);
    expect(metrics[0].numLikesReceived).to.equal(1);
    expect(metrics[0].numLocalActionsReceived).to.equal(1);
    expect(metrics[0].numLocalLikesReceived).to.equal(1);

    user = await User.findById('2');
    expect(user.metrics.numLiftOffUsed).to.equal(1);
  });

  it('boosted users should come before new users', async () => {
    user = await User.findById('1');
    expect(user.scores.decayedScore2).to.equal(2);

    user = await User.findById('2');
    expect(user.scores.decayedScore2).to.equal(2);
    user.scores.decayedScore2 = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');

    // now activate boost for user 2
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    user = await User.findById('2');
    expect(user.scores.decayedScore2).to.equal(3);

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('2');
    expect(res.body.profiles[1]._id).to.equal('1');
  });

  it('stack boosts', async () => {
    clock = sinon.useFakeTimers();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.11.14' });
    expect(res.status).to.equal(200);

    doc = await UserMetadata.findOne({ user: '2' });
    doc.coins = 1000;
    await doc.save();

    // activate two boosts
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    const boostExpiration = 2 * 60 * 60 * 1000;

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(new Date(res.body.user.boostExpiration).getTime()).to.equal(boostExpiration);

    const metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('2');
    expect(metrics[0].numBoosts).to.equal(2);
    expect(metrics[0].coinsSpent).to.equal(400);
    expect(metrics[0].durationMinutes).to.equal(120);
    expect(new Date(metrics[0].boostExpiration).getTime()).to.equal(boostExpiration);
  });
});

it('app 366 false / not set', async () => {

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
    .send({ appVersion: '1.13.59' });
  expect(res.status).to.equal(200);

  user = await User.findById('2');
  user.numBoosts = 5
  await user.save();


  // activate first boost pop up show
  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 2)
  expect(res.status).to.equal(200);
  expect(res.body.boostedPopup).to.be.undefined;
  expectedDate = new Date(new Date().getTime() + 60 * 60 * 1000);
  truncatedExpectedDate = new Date(expectedDate.setSeconds(0, 0)).toISOString();
  actualDate = new Date(res.body.boostExpiration);
  truncatedActualDate = new Date(actualDate.setSeconds(0, 0)).toISOString();

  expect(truncatedActualDate).to.equal(truncatedExpectedDate);

  user = await User.findById('2')
  expect(user.boostDurationMinutes).to.equal(60);
  console.log('boost duration : ',user.boostExpiration)

  // activate second boost pop up not show
  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 2)
  expect(res.status).to.equal(200);
  expect(res.body.boostedPopup).to.be.undefined;
  expectedDate = new Date(new Date().getTime() + 120 * 60 * 1000);
  truncatedExpectedDate = new Date(expectedDate.setSeconds(0, 0)).toISOString();
  actualDate = new Date(res.body.boostExpiration);
  truncatedActualDate = new Date(actualDate.setSeconds(0, 0)).toISOString();

  expect(truncatedActualDate).to.equal(truncatedExpectedDate);
  console.log('res : ',res.body)

  user = await User.findById('2')
  expect(user.boostDurationMinutes).to.equal(120);
  console.log('boost duration : ',user.boostExpiration)

  // activate third boost pop up not show
  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 2)
  expect(res.status).to.equal(200);
  expect(res.body.boostedPopup).to.be.undefined;
  console.log('res : ',res.body)

  user = await User.findById('2')
  expect(user.boostDurationMinutes).to.equal(180);
  expectedDate = new Date(new Date().getTime() + 180 * 60 * 1000);
  truncatedExpectedDate = new Date(expectedDate.setSeconds(0, 0)).toISOString();
  actualDate = new Date(res.body.boostExpiration);
  truncatedActualDate = new Date(actualDate.setSeconds(0, 0)).toISOString();
  console.log('boost duration : ',user.boostExpiration)

  // set the boost to expire
  user.boostExpiration = new Date()
  user.save()

  // activate fourth boost after third is end pop up show
  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 2)
  expect(res.status).to.equal(200);
  expect(res.body.boostedPopup).to.be.undefined;
  console.log('res : ',res.body)

  user = await User.findById('2')
  expect(user.boostDurationMinutes).to.equal(60);
  console.log('boost duration : ',user.boostExpiration)
});

it('admin increment boost', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.50'});
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  user.admin = true;
  user.adminPermissions = { support: true };
  res = await user.save();

  res = await request(app)
    .patch('/v1/admin/incrementBoosts')
    .set('authorization', 1)
    .send({
      user: '0',
      numBoosts: 3,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.50'});
  expect(res.status).to.equal(200);
  expect(res.body.user.numBoosts).to.equal(3);

  res = await request(app)
    .patch('/v1/admin/incrementBoosts')
    .set('authorization', 1)
    .send({
      user: '0',
      numBoosts: 10,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.50'});
  expect(res.status).to.equal(200);
  expect(res.body.user.numBoosts).to.equal(13);

  // check boost transactions
  res = await request(app)
    .get('/v1/admin/user/boostTransactions')
    .set('authorization', 1)
    .query({ user: '0' });
  expect(res.status).to.equal(200);
  console.log(res.body.transactions);
  expect(res.body.transactions.length).to.equal(2);
  expect(res.body.transactions[0].transactionAmount).to.equal(10);
  expect(res.body.transactions[0].newBalance).to.equal(13);
  expect(res.body.transactions[0].description).to.equal('admin incremented boosts');
  expect(res.body.transactions[1].transactionAmount).to.equal(3);
  expect(res.body.transactions[1].newBalance).to.equal(3);
  expect(res.body.transactions[1].description).to.equal('admin incremented boosts');
});

it('boost live activity token', async () => {
  const sendStub = sinon.stub(axios, 'post').callsFake((url, body, options) => new Promise((resolve, reject) => {
    console.log(`Override axios.post() ${url} ${JSON.stringify(body,null,2)} ${JSON.stringify(options,null,2)}`);
    resolve({ data: 'success' });
  }));

  clock = sinon.useFakeTimers();

  // create two users who both use boost
  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
    expect(res.status).to.equal(200);

    doc = await UserMetadata.findOne({ user: i.toString() });
    doc.coins = 1000;
    await doc.save();

    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', i)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
    expect(new Date(res.body.user.boostExpiration).getTime()).to.equal(30 * 60 * 1000);
  }

  // set fcm and activity tokens on user 0 only
  res = await request(app)
    .put('/v1/user/fcmToken')
    .set('authorization', 0)
    .send({
      fcmToken: 'fcmToken',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/boosts/activityToken')
    .set('authorization', 0)
    .send({ activityToken: 'activityToken' });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.boostActivityToken).to.equal('activityToken');

  user = await User.findById('1');
  expect(user.boostActivityToken).to.equal();

  // trigger worker job - no message sent because boost not expired yet
  res = await request(app)
    .post('/v1/worker/endBoostLiveActivity')
  expect(res.status).to.equal(200);

  assert(sendStub.notCalled);

  // tick clock to expire boost
  clock.tick(30 * 60 * 1000);

  // trigger worker job - only one message sent to user 0
  res = await request(app)
    .post('/v1/worker/endBoostLiveActivity')
  expect(res.status).to.equal(200);

  sinon.assert.calledOnce(sendStub);
  sinon.assert.calledWith(
    sendStub,
    'https://fcm.googleapis.com/v1/projects/boo-dating-beta/messages:send',
    {
      message: {
        'token': 'fcmToken',
        'apns': {
          'live_activity_token': 'activityToken',
          'headers': {
            'apns-priority': '10'
          },
          'payload': {
            'aps': {
              'timestamp': 1800,
              'dismissal-date': 1800,
              'event': 'end',
              'content-state': {},
            }
          }
        }
      },
    },
    {
      headers: { Authorization: 'Bearer mockToken', 'Content-Type': 'application/json' },
      timeout: 10000
    }
  );
  sendStub.resetHistory();

  // activity token should be cleared after message sent
  user = await User.findById('0');
  expect(user.boostActivityToken).to.equal();

  user = await User.findById('1');
  expect(user.boostActivityToken).to.equal();

  // trigger worker job again - no message sent because no users have activity token
  res = await request(app)
    .post('/v1/worker/endBoostLiveActivity')
  expect(res.status).to.equal(200);

  assert(sendStub.notCalled);
});
