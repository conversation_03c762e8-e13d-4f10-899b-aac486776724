const { expect } = require('chai');
const { shouldRunForPercentageOfUser } = require('../lib/basic');

describe('shouldRunForPercentageOfUser', () => {
  // Fixed reference date for deterministic testing
  const REF_DATE = new Date('2023-10-20T02:46:48.550+00:00');
  
  describe('Standard percentage tests', () => {
    it('should return true for ~10% of users when percentage is 10', () => {
      const user = { createdAt: REF_DATE };
      // This specific timestamp ends with 550, 550 % 10 = 0 → should be included
      expect(shouldRunForPercentageOfUser(user, 10)).to.be.true;
    });

    it('should return false for users not in the 10% group', () => {
      const user = { createdAt: new Date(REF_DATE.getTime() + 1) }; // +1ms → 551
      expect(shouldRunForPercentageOfUser(user, 10)).to.be.false;
    });

    it('should return true for ~25% of users when percentage is 25', () => {
      const testValues = [
        REF_DATE,                      // 550 % 4 = 2 → false
        new Date(REF_DATE.getTime()+2), // 552 % 4 = 0 → true
        new Date(REF_DATE.getTime()+3), // 553 % 4 = 1 → false
        new Date(REF_DATE.getTime()+4)  // 554 % 4 = 2 → false
      ];
      
      const results = testValues.map(date => 
        shouldRunForPercentageOfUser({ createdAt: date }, 25));
      
      expect(results).to.deep.equal([false, true, false, false]);
    });
  });

  describe('Edge cases', () => {
    it('should return false for 0%', () => {
      const user = { createdAt: REF_DATE };
      expect(shouldRunForPercentageOfUser(user, 0)).to.be.false;
    });

    it('should return true for 100%', () => {
      const user = { createdAt: REF_DATE };
      expect(shouldRunForPercentageOfUser(user, 100)).to.be.true;
    });

    it('should handle percentages >100% by returning true', () => {
      const user = { createdAt: REF_DATE };
      expect(shouldRunForPercentageOfUser(user, 150)).to.be.true;
    });
  });

  describe('Small percentages (<1%)', () => {
    it('should work with 0.1%', () => {
      // Find a timestamp that would be in the 0.1% group
      const modValue = Math.floor(10000 / (0.1 * 100)); // =1000
      const targetTime = REF_DATE.getTime() - (REF_DATE.getTime() % modValue);
      const user = { createdAt: new Date(targetTime) };
      
      expect(shouldRunForPercentageOfUser(user, 0.1)).to.be.true;
      expect(shouldRunForPercentageOfUser({ createdAt: new Date(targetTime + 1) }, 0.1)).to.be.false;
    });
  });

  describe('Input validation', () => {
    it('should return false for missing user', () => {
      expect(shouldRunForPercentageOfUser(null, 10)).to.be.false;
    });

    it('should return false for missing createdAt', () => {
      expect(shouldRunForPercentageOfUser({}, 10)).to.be.false;
    });

    it('should handle string dates', () => {
      const user = { createdAt: '2023-10-20T02:46:48.550+00:00' };
      expect(shouldRunForPercentageOfUser(user, 10)).to.be.true;
    });

    it('should handle Date objects', () => {
      const user = { createdAt: new Date('2023-10-20T02:46:48.550+00:00') };
      expect(shouldRunForPercentageOfUser(user, 10)).to.be.true;
    });
  });

  describe('Statistical distribution', () => {
        const percentageTests = [
            { percentage: 10, tolerance: 0.5 },
            { percentage: 15, tolerance: 2.0 },
            { percentage: 25, tolerance: 0.7 },
            { percentage: 1, tolerance: 0.2 },
            { percentage: 0.1, sampleSize: 100000, tolerance: 0.05 }
        ];

        percentageTests.forEach(({ percentage, tolerance = 1, sampleSize = 10000 }) => {
            it(`should match ${percentage}% within ±${tolerance}%`, function() {
            this.timeout(5000);
            
            let hits = 0;
            const startTime = Date.now();
            
            for (let i = 0; i < sampleSize; i++) {
                const user = {
                createdAt: new Date(startTime + i)
                };
                if (shouldRunForPercentageOfUser(user, percentage)) {
                hits++;
                }
            }
            
            const actualPercentage = (hits / sampleSize) * 100;
            expect(actualPercentage).to.be.closeTo(percentage, tolerance);
            });
        });
    });
});