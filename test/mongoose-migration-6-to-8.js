const { expect } = require('chai');
const request = require('supertest');
const { app } = require('./common');
const User = require('../models/user');
const Interest = require('../models/interest')
const mongoose = require('mongoose');

it('query update for bulk work as expected', async () => {
      for (let uid = 0; uid < 3; uid++) {
            res = await request(app)
            .put('/v1/user/initApp')
            .set('authorization', uid);
            expect(res.status).to.equal(200);
      }

      await User.updateMany({},{ $set:{ age: 30 }})

      let user = await User.findOne({ _id: 0 })
      expect(user.age).to.equal(30);
      user = await User.findOne({ _id: 1 })
      expect(user.age).to.equal(30);
      user = await User.findOne({ _id: 2 })
      expect(user.age).to.equal(30);

      const users = await User.find({});
      let bulk = User.collection.initializeUnorderedBulkOp();
      for await (const user of users) {
      const filter = { _id: user._id };
      const updates = { $set: { age: 45 } };
      bulk.find(filter).update(updates);
      }
      await bulk.execute();

      user = await User.findOne({ _id: 0 })
      expect(user.age).to.equal(45);
      user = await User.findOne({ _id: 1 })
      expect(user.age).to.equal(45);
      user = await User.findOne({ _id: 2 })
      expect(user.age).to.equal(45);

});

it('query deleteOne has deletedCount in response', async () => {
      for (let uid = 0; uid < 3; uid++) {
            res = await request(app)
            .put('/v1/user/initApp')
            .set('authorization', uid);
            expect(res.status).to.equal(200);
      }

      let user = await User.findOne({ _id: 0 })
      let deletedAccount = await user.deleteOne()
      expect(deletedAccount).to.eql({
            acknowledged: true,
            deletedCount: 1,
      });

      deletedAccount = await User.deleteOne({ _id: '1' });
      expect(deletedAccount).to.eql({
            acknowledged: true,
            deletedCount: 1,
      });

      user = await User.findOne({ _id: 0 })
      expect(user).to.equal(null);
      user = await User.findOne({ _id: 1 })
      expect(user).to.equal(null);
      user = await User.findOne({ _id: 2 })
      expect(user._id).to.equal('2');

});

it('should throw an error for top level query as $not but will work for internal $not', async () => {
      try {
            await Interest.findOne({ $not: { category: 'Music' } });
            throw new Error('Expected findOne to throw');
          } catch (err) {
            expect(err).to.exist;
            expect(err.name).to.equal('MongoServerError'); // optional: check error type
          }
          let interest = await Interest.findOne({ sortIndex: { $not: { $gt: 2 } }})
          expect(interest.name).to.equal('kpop');
});