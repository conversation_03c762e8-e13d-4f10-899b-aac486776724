const AIImage = require("../../models/ai-image");
const { batchStatus, taskStatus, AI_IMAGE_NUMBER_OF_RESULTS, IMGPLN_SERVER_ID } = require("../../lib/ai-image/const")
const { sendSocketEvent } = require('../../lib/socket');
const { translate } = require('../../lib/translate');
const admin = require('../../config/firebase-admin');

async function setQUEUEBatchToFailure(batch) {
    try {
        // Atomic update
        const updateResult = await AIImage.updateOne(
            { _id: batch._id },
            { 
                $set: { batchStatus: batchStatus.FAILURE },
                $currentDate: { updatedAt: true }
            }
        );

        if (updateResult.modifiedCount > 0) {
            sendSocketEvent(batch.user._id, 'ai image ready', {
                batchId: batch._id,
                imageKey: batch.originalImage
            });

            //send notification
            admin.sendNotification(
                batch.user,
                null,
                translate('Try Again', batch.user.locale),
                translate('There was an issue generating your AI photos, please try again.', batch.user.locale),
                { openPage: "aiImagesResults"},
                null,
                'general',
                'ai-image-ready',
            );
            return { success: true, id: batch._id };
        }
        return { success: false, id: batch._id };
        
    } catch (error) {
        console.error(`Error updating batch ${batch._id} to FAILURE:`, error);
        return { success: false, id: batch._id, error: error.message };
    }
}

async function setSuccessfulBatches(batch) {
    try {
        // Atomic update
        const updateResult = await AIImage.updateOne(
            { _id: batch._id },
            { 
                $set: { batchStatus: batchStatus.DONE },
                $currentDate: { updatedAt: true }
            }
        );

        if (updateResult.modifiedCount > 0) {
            // Use originalImage as you originally intended
            sendSocketEvent(batch.user._id, 'ai image ready', {
                batchId: batch._id,
                imageKey: batch.originalImage,
            });

            //send notification
            admin.sendNotification(
                batch.user,
                null,
                translate('Your AI Photos Are Ready ✨', batch.user.locale),
                translate('You look amazing. Check out your new profile pics.', batch.user.locale),
                { openPage: "aiImagesResults"},
                null,
                'general',
                'ai-image-ready',
            );
            return { success: true, id: batch._id };
        }
        return { success: false, id: batch._id };
    } catch (error) {
        console.error(`Error updating batch ${batch._id} to DONE:`, error);
        return { success: false, id: batch._id, error: error.message };
    }
}

async function processStaleBatches(req, res, next) {
    try {
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

        const staleBatches = await AIImage.find({
            batchStatus: batchStatus.QUEUE,
            createdAt: { $lt: oneHourAgo },
        }).populate('user').lean();

        const result = {
            succesUpdateToDone: 0,
            failUpdateToDone: 0,
            succesUpdateToFAILURE: 0,
            faillUpdateToFAILURE: 0
        }
        

        for(let batch of staleBatches){
            hasSuccessTask = batch.results.find(result => result.status === taskStatus.SUCCESS)
            if(hasSuccessTask){
                const process = await setSuccessfulBatches(batch)
                if(process.success){
                    result.succesUpdateToDone++
                }else{
                    result.failUpdateToDone++
                }
            }else{
                const process = await setQUEUEBatchToFailure(batch)
                if(process.success){
                    result.succesUpdateToFAILURE++
                }else{
                    result.faillUpdateToFAILURE++
                }
            }

        }
        console.log('result: ', result)

        res.json(result);
    } catch (error) {
        console.error('Error in processStaleBatches:', error);
        next(error);
    }
}

module.exports = {processStaleBatches}
