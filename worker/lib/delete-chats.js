const moment = require('moment');
const Bottleneck = require('bottleneck');
const Chat = require('../../models/chat');
const Message = require('../../models/message');
const s3 = require('../../lib/s3');

let deleteJobRunning = false;

const limiter = new Bottleneck({
  maxConcurrent: 50,
});

async function task(chatId) {
  try {
    await s3.emptyS3Directory(`chats/${chatId}/`, 'deleteChatFiles');
  } catch (err) {
    console.log(`Error while deleting files for chat ${chatId}: ${err}`);
  }
}

async function deleteChats(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }

  if (deleteJobRunning) {
    console.log('deleteChats job already running, skipping this job');
    return;
  }

  console.log('deleteChats job starting');
  deleteJobRunning = true;

  try {
    const cutoff = new Date('2025-09-04');
    const thirtyDaysAgo = new Date(Date.now() - 30*24*60*60*1000);

    const doomed = await Chat.find(
      { deletedAt: { $lte: thirtyDaysAgo } },
      '_id createdAt hasMediaFiles'
    ).lean();

    const allChatIdsToDelete = doomed.map(d => d._id);

    const s3ChatIds = doomed
      .filter(d => d.createdAt < cutoff || d.createdAt >= cutoff && d.hasMediaFiles)
      .map(d => d._id);
    console.log(`deleteChats job: deleting S3 files for ${s3ChatIds.length} chats`);
    await Promise.all(s3ChatIds.map(id => limiter.schedule(() => task(id))));
    console.log(`deleteChats job: deleted chat files`);

    console.log(`deleteChats job: deleting chat data from db for ${allChatIdsToDelete.length} chats`);
    {
      const result = await Message.deleteMany({ chat: { $in: allChatIdsToDelete } });
      console.log(`deleteChats job: deleted ${result.deletedCount} messages from db`);
    }
    {
      const result = await Chat.deleteMany({ _id: { $in: allChatIdsToDelete } });
      console.log(`deleteChats job: deleted ${result.deletedCount} chats from db`);
    }
  } catch (error) {
    console.log(`deleteChats error: ${error}`);
  }

  deleteJobRunning = false;
  console.log('deleteChats job finished');

  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

module.exports = {
  deleteChats,
};
