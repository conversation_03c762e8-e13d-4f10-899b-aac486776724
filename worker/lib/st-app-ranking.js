require('dotenv').config();
const axios = require('axios');
const Bottleneck = require('bottleneck');
const StAppRankingSnapshot = require('./../../models/st-app-ranking-snapshot');

// ---------- ENV ----------
const BASE_URL = process.env.ST_BASE_URL || 'https://api.sensortower.com';
const AUTH_TOKEN = process.env.SENSOR_TOWER_AUTH_TOKEN; // required (query param)
const APP_ID_ANDROID = 'enterprises.dating.boo'; 
const APP_ID_IOS = '1498407272';
const ST_OS_LIST = 'android'
const ST_CATEGORY_FILTER_ANDROID = 'dating'

const CATEGORY_FILTERS = {
  android:  'dating',
  ios: 6005,
};

const CHART_TYPES = {
  android: {
    free:  ['topselling_free'],
    gross: ['topgrossing'],
  },
  ios: {
    free:  ['topfreeapplications'],         // ignore ipad variant
    gross: ['topgrossingapplications'],     // ignore ipad variant
  },
};

if (!AUTH_TOKEN) throw new Error('Missing SENSOR_TOWER_AUTH_TOKEN');

// Which OSes to poll. Path must be exactly what the API wants: "android" or "ios".
const OS_LIST = ( ST_OS_LIST || 'android,ios')
    .split(',').map(s => s.trim()).filter(Boolean);

// Country list (https://app.sensortower.com/api/docs/static/country_ids.json)
const COUNTRY_IDS = {
    AE: 'United Arab Emirates', AO: 'Angola', AR: 'Argentina', AT: 'Austria', AU: 'Australia', AZ: 'Azerbaijan',
    BD: 'Bangladesh', BE: 'Belgium', BF: 'Burkina Faso', BG: 'Bulgaria', BH: 'Bahrain', BJ: 'Benin', BO: 'Bolivia',
    BR: 'Brazil', BY: 'Belarus', CA: 'Canada', CG: 'Republic of the Congo', CH: 'Switzerland', CI: 'Cote D Ivoire',
    CL: 'Chile', CM: 'Cameroon', CN: 'China', CO: 'Colombia', CR: 'Costa Rica', CY: 'Cyprus', CZ: 'Czech Republic',
    DE: 'Germany', DK: 'Denmark', DO: 'Dominican Republic', DZ: 'Algeria', EC: 'Ecuador', EE: 'Estonia', EG: 'Egypt',
    ES: 'Spain', FI: 'Finland', FR: 'France', GB: 'United Kingdom', GE: 'Georgia', GH: 'Ghana', GR: 'Greece',
    GT: 'Guatemala', HK: 'Hong Kong', HR: 'Croatia', HU: 'Hungary', ID: 'Indonesia', IE: 'Ireland', IL: 'Israel',
    IN: 'India', IQ: 'Iraq', IT: 'Italy', JO: 'Jordan', JP: 'Japan', KE: 'Kenya', KH: 'Cambodia', KR: 'South Korea',
    KW: 'Kuwait', KZ: 'Kazakhstan', LA: 'Laos', LB: 'Lebanon', LK: 'Sri Lanka', LT: 'Lithuania', LU: 'Luxembourg',
    LV: 'Latvia', LY: 'Libya', MA: 'Morocco', ML: 'Mali', MM: 'Myanmar', MO: 'Macau', MT: 'Malta', MX: 'Mexico',
    MY: 'Malaysia', MZ: 'Mozambique', NG: 'Nigeria', NI: 'Nicaragua', NL: 'Netherlands', NO: 'Norway', NZ: 'New Zealand',
    OM: 'Oman', PA: 'Panama', PE: 'Peru', PH: 'Philippines', PK: 'Pakistan', PL: 'Poland', PT: 'Portugal', PY: 'Paraguay',
    QA: 'Qatar', RO: 'Romania', RS: 'Serbia', RU: 'Russia', SA: 'Saudi Arabia', SE: 'Sweden', SG: 'Singapore',
    SI: 'Slovenia', SK: 'Slovakia', SN: 'Senegal', SV: 'El Salvador', TH: 'Thailand', TN: 'Tunisia', TR: 'Turkey',
    TW: 'Taiwan', TZ: 'Tanzania', UA: 'Ukraine', UG: 'Uganda', US: 'US', UY: 'Uruguay', UZ: 'Uzbekistan', VE: 'Venezuela',
    VN: 'Vietnam', YE: 'Yemen', ZA: 'South Africa', ZM: 'Zambia', ZW: 'Zimbabwe'
};

/**
 * process.env.ST_COUNTRY_WHITELIST
 *   Purpose:
 *   Safety/control knob to LIMIT which countries the worker fetches.
 *   If set, the job polls ONLY the listed country codes.
 *   If empty/unset, the job polls the FULL COUNTRY_IDS list.
 *   
 *   Examples:
 *   ST_COUNTRY_WHITELIST=US,GB,IDExamples:
 *   ST_COUNTRY_WHITELIST=US,GB,ID
 */
const COUNTRY_WHITELIST = (process.env.ST_COUNTRY_WHITELIST || '')
    .split(',').map(s => s.trim().toUpperCase()).filter(Boolean);
const COUNTRY_CODES = COUNTRY_WHITELIST.length ? COUNTRY_WHITELIST : Object.keys(COUNTRY_IDS);

// Rate limit safety
const limiter = new Bottleneck({ minTime: 1000 }); // 1 req/sec

const http = axios.create({
    baseURL: BASE_URL,
    timeout: 30000,
});

// Simple backoff: 1s → 300s → 600s → 600s …
async function withRetries(fn, tries = 5) {
    let attempt = 0;
    while (true) {
        try {
            return await fn();
        } catch (e) {
            attempt++;
            const status = e.response?.status;
            const retriable = status === 429 || (status >= 500) || !status;
            if (!retriable || attempt >= tries) throw e;
            const wait = attempt === 1 ? 1000 : attempt === 2 ? 300000 : 600000;
            console.warn(`Retry #${attempt} after ${wait}ms (status=${status || 'n/a'})`);
            await new Promise(r => setTimeout(r, wait));
        }
    }
}

function extractRanks(data, os) {
  const rows = Array.isArray(data) ? data : [];
  let free, gross;

  const isAndroid = os === 'android';
  const freeSet  = new Set((CHART_TYPES[os]?.free  || []).map(s => s.toLowerCase()));
  const grossSet = new Set((CHART_TYPES[os]?.gross || []).map(s => s.toLowerCase()));

  for (const row of rows) {
    const ct = String(row.chart_type_id || '').toLowerCase();
    const r  = Number(row.rank);
    const cat = row.category_id;

    // Skip bad ranks
    if (!Number.isFinite(r) || r <= 0) continue;

    // Category filter: string 'dating' for Android, numeric 6005 for iOS
    if (isAndroid) {
      if (String(cat || '').toLowerCase() !== (CATEGORY_FILTERS.android || '')) continue;
    } else {
      if (Number(cat) !== CATEGORY_FILTERS.ios) continue;
    }

    // Keep only the right chart types (ignore iPad variants etc.)
    if (freeSet.has(ct))  free  = Math.min(free  ?? Infinity, r);
    if (grossSet.has(ct)) gross = Math.min(gross ?? Infinity, r);
  }

  return {
    free:  Number.isFinite(free)  ? free  : undefined,
    gross: Number.isFinite(gross) ? gross : undefined,
  };
}

function getAppIdForOs(os) {
    if (os === 'android') return APP_ID_ANDROID;
    if (os === 'ios') return APP_ID_IOS;
    return undefined;
}

async function fetchCountrySummary(os, country) {
    const app_id = getAppIdForOs(os);
    if (!app_id) throw new Error(`Missing APP_ID for ${os}`);

    const endpoint = `/v1/${os}/category/category_ranking_summary`;
    const params = { app_id, country, auth_token: AUTH_TOKEN };

    const res = await withRetries(() =>
        limiter.schedule(() => http.get(endpoint, { params }))
    );
    return extractRanks(res.data, os);
}

async function runOnceForOs(os) {
    const topFree = new Map();
    const topGross = new Map();
    const topCombined = new Map();

    const norm = v => (Number.isFinite(v) && v > 0) ? v : undefined;

    for (const cc of COUNTRY_CODES) {
        try {
            const { free, gross } = await fetchCountrySummary(os, cc);

            const f = norm(Number(free));
            const g = norm(Number(gross));

            if (f !== undefined) topFree.set(cc, f);
            if (g !== undefined) topGross.set(cc, g);

            const vals = [f, g].filter(v => v !== undefined);
            if (vals.length) topCombined.set(cc, Math.min(...vals));

            const show = r => (Number.isFinite(r) && r > 0) ? r : 'unranked';
            console.log(`[${os}] ${cc}: free=${show(f)} gross=${show(g)} combined=${vals.length ? Math.min(...vals) : 'unranked'}`);
        } catch (e) {
            const status = e.response?.status;
            console.error(`[${os}] ${cc} failed`, status || '', e.response?.data || e.message);
        }
    }

    const doc = new StAppRankingSnapshot({
        os,
        topFreeRanks: Object.fromEntries(topFree),
        topGrossingRanks: Object.fromEntries(topGross),
        topCombinedRanks: Object.fromEntries(topCombined),
    });
    // console.log('doc: ', doc)
    await doc.save();
    // console.log(`[${os}] Snapshot saved at ${doc.createdAt.toISOString()}`);
}

async function fetchAppRanking() {
    // const startedAt = new Date();
    for (const os of OS_LIST) {
        await runOnceForOs(os);
    }
    // const newDocs = await StRankingSnapshot.find({ createdAt: { $gte: startedAt } }).lean();
    // console.log('new snapshoot: ', JSON.stringify(newDocs, null, 2));
}

module.exports = { fetchAppRanking };
