const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const sitemapPaginatedSubcategories = new mongoose.Schema({
  subcategoryId: { type: Number, required: true },
  pageNo: { type: Number, required: true },
  startId: { type: Number },
  profilesId: [{ type: Number }],
  totalPages: { type: Number },
  updatedAt: { type: Date, default: Date.now }
});

sitemapPaginatedSubcategories.index({
  subcategoryId: 1,
  pageNo: 1,
});

// Export schema
let conn = connectionLib.getPersonalityDatabaseConnection() || mongoose;
module.exports = conn.model('SitemapPaginatedSubcategories', sitemapPaginatedSubcategories);
