const mongoose = require('mongoose');

const stAppRangkinSnapshotschema = new mongoose.Schema(
  {
    os: { type: String, enum: ['android', 'ios'], index: true, required: true },
    createdAt: { type: Date, default: Date.now, index: true },

    // Map<countryCode, rankNumber>
    topFreeRanks:     { type: Map, of: Number, default: {} },
    topGrossingRanks: { type: Map, of: Number, default: {} },
    topCombinedRanks: { type: Map, of: Number, default: {} },
  },
);

module.exports = mongoose.model('StAppRankingSnapshot', stAppRangkinSnapshotschema);