const mongoose = require('mongoose');

const chatMetricSchema = new mongoose.Schema({
  date: { type: Date, required: true, unique: true, index: true },
  numMeetUp: { type: Number, default: 0 },
  numMatchesAnalyzed: { type: Number, default: 0 },
  numContactExchange: { type: Number, default: 0 },
  createdAt: { type: Date, default: Date.now },
}, {
  versionKey: false
});

chatMetricSchema.index({ date: -1 });

chatMetricSchema.statics.incrementDailyMetrics = async function(numMeetUp = 0, numContactExchange = 0, numMatchesAnalyzed = 0) {
  if (numMeetUp <= 0 && numMatchesAnalyzed <= 0 && numContactExchange <= 0) return;

  const incFields = {};
  const now = new Date();
  const startOfDay = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));

  if (numMeetUp > 0) incFields['numMeetUp'] = numMeetUp;
  if (numMatchesAnalyzed > 0) incFields['numMatchesAnalyzed'] = numMatchesAnalyzed;
  if (numContactExchange > 0) incFields['numContactExchange'] = numContactExchange;

  try {
    await this.updateOne({ date: startOfDay }, { $inc: incFields }, { upsert: true, setDefaultsOnInsert: true });
  } catch (err) {
    if (err.code === 11000) {
      await this.updateOne({ date: startOfDay }, { $inc: incFields });
    } else {
      throw err;
    }
  }
};


module.exports = mongoose.model('ChatMetric', chatMetricSchema);
