const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const infinityCopySchema = new mongoose.Schema(
  {
    user: { type: String, ref: 'User' },
    model: { type: String, default: undefined },
    prompt: { type: String, trim: true },
    output: { type: String, trim: true },
    isError: { type: Boolean, default: undefined },
    errorMessage: { type: String, default: undefined },
    cost: { type: Number, default: 0 },
    openAiResponse: { type: String, default: undefined },
    processingTime: { type: Number, default: 0 },
  },
  {
    timestamps: true,
  },
);

infinityCopySchema.index({ user: 1 });
infinityCopySchema.index({ createdAt: 1 });

// Export schema
const connection = connectionLib.getEventsConnection() || mongoose;
module.exports = connection.model('InfinityCopy', infinityCopySchema);
