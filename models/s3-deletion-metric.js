const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema(
  {
    date: { type: Date, required: true, unique: true },
    deletePictureNumCalls: { type: Number, default: 0 },
    removePicturesNumCalls: { type: Number, default: 0 },
    deleteS3ObjectsInBulkNumKeysDeleted: { type: Number, default: 0 },
    emptyS3Directory: {
      deletePicture: { type: Number, default: 0 },
      removeUserPictures: { type: Number, default: 0 },
      deleteChatFiles: { type: Number, default: 0 },
      cleanupDeletedBannedUsers: { type: Number, default: 0 },
    },
  },
);

schema.statics.increment = async function (metric, n) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const update = {
    $inc: {
      [metric]: n,
    },
  };

  await this.updateOne(
    { date: today },
    update,
    { upsert: true },
  );
}

const connection = connectionLib.getEventsConnection() || mongoose;
module.exports = connection.model('S3DeletionMetric', schema);
