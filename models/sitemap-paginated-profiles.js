const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const sitemapPaginatedProfiles = new mongoose.Schema({
  pageNo: { type: Number, required: true },
  startId: { type: Number },
  totalPages: { type: Number },
  updatedAt: { type: Date, default: Date.now }
});

sitemapPaginatedProfiles.index({
  pageNo: 1,
});

// Export schema
let conn = connectionLib.getPersonalityDatabaseConnection() || mongoose;
module.exports = conn.model('SitemapPaginatedProfiles', sitemapPaginatedProfiles);
