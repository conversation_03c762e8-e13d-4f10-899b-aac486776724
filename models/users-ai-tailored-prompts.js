const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: () => Date.now() },
  userId: { type: String, ref: 'User' },
  prompt: { type: String },
  outputPrompts: { type: mongoose.Mixed, default: undefined },
  output: { type: String },
  promptTokens: { type: Number },
  outputTokens: { type: Number },
  isError: { type: Boolean, default: false },
  errorMessage: { type: String },
  cost: { type: Number },
  model: { type: String },
  processingTime: { type: Number },
});

schema.index({
  userId: 1,
  createdAt: 1,
});

// Export schema =======================================================================
let conn = connectionLib.getEventsConnection() || mongoose;
module.exports = conn.model('UsersAiTailoredPrompts', schema);
