const connectionLib = require('../lib/connection');
const mongoose = require("mongoose");

const CategoryCountryProfileFilters = new mongoose.Schema({
  category: { type: Number },
  updatedAt: { type: Date, default: Date.now },
  countryFilterMap: {
    type: Map,
    of: {
      type: [String], 
      default: []
    }
  },
});

CategoryCountryProfileFilters.index({
  category: 1,
});

let conn = connectionLib.getPersonalityDatabaseConnection() || mongoose;
module.exports = conn.model("categoryCountryProfileFilters", CategoryCountryProfileFilters);
