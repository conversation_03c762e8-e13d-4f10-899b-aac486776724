const mongoose = require('mongoose');

const schema = new mongoose.Schema({
  cachedAt: { type: Date, default: Date.now },
  groupId: { type: String, unique: true },
  agentIds: [{ type: String }],
  lastUsedAgentId: { type: String },
});


// Export schema =====================================================================================================================================================================
module.exports = mongoose.model('ZendeskAgent', schema);
