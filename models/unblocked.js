const mongoose = require('mongoose');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now, expires: 60 * 60 * 24 * 7 }, // Documents expire after 7 days
  from: { type: String, ref: 'User' },
  to: { type: String, ref: 'User' },
});

schema.index({
  from: 1,
  to: 1,
});

schema.index({
  from: 1,
  createdAt: -1,
});

schema.statics.recentlyUnblocked = async function (from, to) {
  const doc = await this.findOne({ from, to });
  return !!doc;
};

module.exports = mongoose.model('UnBlocked', schema);
