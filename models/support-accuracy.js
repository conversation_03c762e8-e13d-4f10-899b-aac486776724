const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema(
  {
    supportAgentId: { type: String, required: true },
    supportAgentEmail: { type: String },
    date: { type: Date, required: true },
    totalDecisions: { type: Number, default: 0 },
    incorrectDecisions: { type: Number, default: 0 },
  },
  {
    timestamps: true,
  },
);

schema.index({ date: 1, supportAgentId: 1 }, { unique: true });

const connection = connectionLib.getRecordsConnection() || mongoose;
module.exports = connection.model('SupportAgentAccuracy', schema);
