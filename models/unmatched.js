const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now, expires: 60 * 60 * 24 * 30 }, // Documents expire after 30 days
  user: { type: String },
  otherUser: { type: String },
  lastMessageTime: { type: Date },
  isShadowBanUnmatch: { type: Boolean, default: undefined },
  otherUserName: { type: String },
});

schema.index({
  user: 1,
  otherUser: 1,
});

schema.index({
  user: 1,
  lastMessageTime: -1,
});

schema.index(
  {
    otherUser: 1,
    isShadowBanUnmatch: 1,
  },
  {
    partialFilterExpression: { isShadowBanUnmatch: { $exists: true } },
  },
);

const connection = connectionLib.getRecordsConnection() || mongoose;
module.exports = connection.model('Unmatched', schema);
