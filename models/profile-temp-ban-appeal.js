const mongoose = require('mongoose');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: Date.now },
  user: { type: String, ref: 'User' },
  profileTempBanReason: { type: String },
  profileTempBanReportId: { type: mongoose.Schema.Types.ObjectId, ref: 'Report' },
  profileTempBanInfringingText: { type: [{ type: String }], default: undefined },
  profileTempBanInfringingPictures: { type: [{ type: String }], default: undefined },
  comment: { type: String },
  reviewedBy: { type: String, ref: 'User' },
  reviewedAt: { type: Date },
  decision: { type: String, enum: [ 'approved', 'rejected', 'dismissed' ] },
  notes: { type: String },
});

schema.index(
  {
    // createdAt: 1,
  },
  { partialFilterExpression: { decision: { $exists: false } } },
);

// Export schema =====================================================================================================================================================================
module.exports = mongoose.model('ProfileTempBanAppeal', schema);
