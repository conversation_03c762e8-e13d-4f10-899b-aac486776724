const mongoose = require('mongoose');

const openaiSchema = new mongoose.Schema({
  date: { type: Date, default: undefined },
  prompt: { type: String, default: undefined },
  output: { type: String },
  reasoning_content: { type: String },
  promptTokens: { type: Number },
  outputTokens: { type: Number },
  isError: { type: Boolean },
  errorMessage: { type: String },
  cost: { type: Number },
  provider: { type: String },
  model: { type: String },
  processingTime: { type: Number },
  ban: { type: Boolean },
  banReason: { type: String },
  violationLocation: { type: String },
  decision: { type: String },
  infringingText: { type: [String], default: undefined },
  infringingPictures: { type: [Number], default: undefined },
  infringingPictureKeys: { type: [String], default: undefined },
  infringingTextFoundInName: { type: Boolean },
  socialMediaHandle: { type: String, default: undefined },
  socialMediaHandles: { type: [String] },
}, { _id: false });

module.exports = openaiSchema;
