const mongoose = require('mongoose');
const User = require('../models/user');
const BanAppeal = require('../models/ban-appeal');
const reportConstants = require('../lib/report-constants');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const appeals = await BanAppeal.find({
    bannedReasons: { $in: reportConstants.banReasonsAutoRejectAppeals },
    decision: { $exists: false },
  });
  console.log(`Num appeals: ${appeals.length}`);

  let i = 0;
  for (const appeal of appeals) {
    appeal.decision = 'rejected';
    await appeal.save();

    const user = await User.findById(appeal.user);
    if (user) {
      if (user.banNotice?.appealStatus == 'pending') {
        user.banNotice.appealStatus = 'rejected';
      }
      user.banHistory.push({
        action: 'appealRejected',
        by: null,
        date: Date.now(),
      });
      await user.save();
    }
    i++;
    console.log(`Processed: ${i}`);
  }

  console.log('Done');
  await mongoose.disconnect();
})();
