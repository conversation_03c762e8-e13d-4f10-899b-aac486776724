/* eslint-disable no-continue */
/*
ENV REQUIRED:
  - NODE_ENV
  - MONGODB_URI
  - PUSHER_APP_ID
  - PUSHER_KEY
  - PUSHER_SECRET
  - GOOGLE_APPLICATION_CREDENTIALS
*/

const mongoose = require('mongoose');
const Chat = require('../models/chat');
const Message = require('../models/message');
const chatLib = require('../lib/chat');
const { translate } = require('../lib/translate');
const { sendSocketEvent } = require('../lib/socket');
const { sendMessageNotifications } = require('../lib/message');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 5000;
const CONCURRENCY_LIMIT = parseInt(process.env.CONCURRENCY_LIMIT, 10) || 300;

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log(`Connected to MongoDB`);

    let hasMore = true;
    let lastId = null;
    let totalCount = 0;

    while (hasMore) {
      const filter = {
        ...(lastId && { _id: { $gt: lastId } }),
        users: chatLib.BOO_SUPPORT_ID,
        pendingUser: null,
        lastMessageTime: { $gte: new Date('2025-05-21T00:00:00Z') },
        groupChat: { $ne: true },
        deletedAt: { $exists: false },
      };

      const chats = await Chat.find(filter).sort({ _id: 1 }).limit(BATCH_SIZE).populate('users').populate('lastMessage');

      if (chats.length === 0) {
        hasMore = false;
        break;
      }

      lastId = chats[chats.length - 1]._id;

      const relevantChats = chats.filter((chat) => {
        const lastMessageSenderId = chat.lastMessage?.sender?.toString();
        return lastMessageSenderId !== chatLib.BOO_SUPPORT_ID && (!chat.automatedChat || lastMessageSenderId);
      });

      const messagesToInsert = [];
      const chatMeta = [];

      for (const chat of relevantChats) {
        const booSupportUser = chat.users.find((u) => u._id.toString() === chatLib.BOO_SUPPORT_ID);
        const otherUser = chat.users.find((u) => u._id.toString() !== chatLib.BOO_SUPPORT_ID);

        messagesToInsert.push({
          chat: chat._id,
          text: translate(chatLib.AUTOMATED_SUPPORT_REPLY, otherUser.locale || 'en'),
          sender: booSupportUser._id,
        });

        chatMeta.push({ chat, booSupportUser, otherUser });
      }

      const insertedMessages = await Message.insertMany(messagesToInsert);
      console.log(`Inserted ${insertedMessages.length} messages.`);

      const messageMap = new Map(insertedMessages.map((msg) => [msg.chat.toString(), msg]));
      const bulkUpdates = [];

      for (const { chat, booSupportUser, otherUser } of chatMeta) {
        const replyMessage = messageMap.get(chat._id.toString());

        if (!replyMessage) {
          console.log(`[Issue]: No message added for chat ${chat._id}`);
          continue;
        }

        const perUserState = chat.users.map((user) => ({
          userId: user._id,
          unread: user._id.toString() !== booSupportUser._id.toString(),
        }));

        const readReceiptsObj = {
          [otherUser._id.toString()]: {
            numUnreadMessages: (chat.readReceipts?.get(otherUser._id.toString())?.numUnreadMessages || 0) + 1,
          },
          [booSupportUser._id.toString()]: {
            numUnreadMessages: 0,
          },
        };

        bulkUpdates.push({
          updateOne: {
            filter: { _id: chat._id },
            update: {
              $set: {
                lastMessage: replyMessage._id,
                lastMessageTime: replyMessage.createdAt,
                readReceipts: readReceiptsObj,
                perUserState,
                hidden: [],
              },
              $inc: { numMessages: 1 },
            },
          },
        });
      }

      if (bulkUpdates.length > 0) {
        const bulkWriteResult = await Chat.bulkWrite(bulkUpdates);
        console.log(`Bulk updated ${bulkWriteResult.modifiedCount} chats.`);

        for (let i = 0; i < chatMeta.length; i += CONCURRENCY_LIMIT) {
          const batch = chatMeta.slice(i, i + CONCURRENCY_LIMIT);

          await Promise.all(
            batch.map(async ({ chat, booSupportUser, otherUser }) => {
              const replyMessage = messageMap.get(chat._id.toString());
              if (!replyMessage) return;

              await sendSocketEvent(booSupportUser._id, 'message', replyMessage);
              await sendMessageNotifications(booSupportUser, chat, replyMessage, replyMessage.text);

              totalCount++;
              console.log(`Notified user ${otherUser._id} for chat ${chat._id} (${totalCount} total)`);
            }),
          );
        }
      }
    }
    await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait for all socket events to be processed
  } catch (error) {
    console.log('Error processing chats:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
})();
