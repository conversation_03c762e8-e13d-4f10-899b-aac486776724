const mongoose = require('mongoose');
const User = require('../models/user');
const reportLib = require('../lib/report');

(async () => {
  await mongoose.connect(process.env.MONGODB_URI);
  console.log('Connected to database');

  // await reportLib.renameBannedReasonForPhotoVerificationFirstReview();

  {
    const res = await User.updateMany(
      {
        shadowBanned: true,
        bannedReason: { $regex: '!=' },
      },
      {
        bannedReason: 'Auto-ban: timezone does not match country',
      },
    );
    console.log('timezone does not match country', res);
  }

  {
    const res = await User.updateMany(
      {
        shadowBanned: true,
        bannedReason: { $regex: 'loginSource already banned: ' },
      },
      {
        bannedReason: 'Auto-ban: loginSource already banned',
      },
    );
    console.log('loginSource already banned', res);
  }

  {
    const res = await User.updateMany(
      {
        shadowBanned: true,
        bannedReason: { $regex: "tengo |tehno |ich bin |i'm |im |i am" },
      },
      {
        bannedReason: 'Auto-ban: underage',
      },
    );
    console.log('underage', res);
  }

  await mongoose.disconnect();
})();
