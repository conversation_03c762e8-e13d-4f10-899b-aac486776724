const dotenv = require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/user');
const emailLib = require('../lib/email');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

const emails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const locale = 'en';
  const globalMergeVars = await emailLib.getGlobalMergeVars(locale);

  for (const email of emails) {
    const user = await User.findOne({email});
    for (const configValue of [false, true]) {
      user.config.app_633 = configValue;
      user.locale = locale;
      // await emailLib.sendDailyDigestToCursor({email});
      await emailLib.sendDailyDigestEmailHelper([user], globalMergeVars, locale, configValue ? 'daily-digest-v6' : 'daily-digest-v4');
      await emailLib.sendDeleteAccountEmail(user);
      await emailLib.sendFlashSaleEmail(user);
      await emailLib.sendInactiveEmail(user);
      if (configValue == false) {
        await emailLib.sendNewLikeEmail(user);
      } else {
        await emailLib.sendNewLikeEmail(user, 'new-like-dating-v6');
        await emailLib.sendNewLikeEmail(user, 'new-like-friends-v6');
      }
      await emailLib.sendNewMatchEmail(user);
      await emailLib.sendNewMessageEmail(user, 'Bob');
      await emailLib.sendNewRecommendationsEmail(user);
      await emailLib.sendVerificationSuccessfulEmail(user);
      await emailLib.sendVerificationFailedEmail(user, 'Reason: Photo Unclear. Please follow the profile verification guidelines.');
      await emailLib.sendWelcomeEmail(user);
      await emailLib.sendSignInEmailSES({ templateName: configValue ? 'sign-in-v6' : 'sign-in-v3', email, locale, timezone: user.timezone, signInLink: 'https://boo.world' });
    }
  }

  await mongoose.disconnect();
  console.log('Done');
  process.exit();
})();
