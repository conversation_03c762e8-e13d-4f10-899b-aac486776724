require('log-timestamp');
require('dotenv').config();
const mongoose = require('mongoose');
const Blog = require('../models/blogs');
const PersonalityBlogs = require('../models/personalityBlogs');
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';
const BATCH_SIZE = 50;


function extractMarkdownImages(markdown = '') {
  if (typeof markdown !== 'string') return [];
  const regex = /!\[.*?\]\((.*?)\)/gi;
  const matches = [];
  let match;

  while ((match = regex.exec(markdown)) !== null) {
    matches.push(match[1]);
  }

  const imagePaths = matches.filter(path =>
    ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.avif'].some(ext => path.toLowerCase().endsWith(ext))
  );

  return imagePaths;
}

async function processMarkdownImagesForBlogs(MODEL) {
  try {
    let lastId = null;
    let totalProcessed = 0;
    let totalUpdated = 0;

    while (true) {
      console.time('Batch processing time');

      const filter = {
        'translations.en.markdown': { $exists: true },
      };
      if (lastId) {
        filter._id = { $gt: lastId };
      }

      const blogs = await MODEL.find(filter)
        .sort({ _id: 1 })
        .limit(BATCH_SIZE)
        .select({ _id: 1, 'translations.en.markdown': 1, 'previews.en.image': 1 })
        .lean();

      if (blogs.length === 0) break;

      const bulkOps = [];

      for (const blog of blogs) {
        const markdown = blog?.translations?.en?.markdown;
        const images = extractMarkdownImages(markdown);
        const previewImage = blog?.previews?.en?.image;
        if (previewImage) images.push(previewImage);
        const uniqueImages = [...new Set(images)];
        if (uniqueImages.length === 0) continue;

        bulkOps.push({
          updateOne: {
            filter: { _id: blog._id },
            update: { $set: { 'previews.en.images': uniqueImages } }
          }
        });
      }

      if (bulkOps.length > 0) {
        await MODEL.bulkWrite(bulkOps);
        totalUpdated += bulkOps.length;
        console.log(`Updated ${bulkOps.length} blogs with images`);
      }

      totalProcessed += blogs.length;
      lastId = blogs[blogs.length - 1]._id;

      console.log(`Processed ${totalProcessed} blogs total`);
      console.timeEnd('Batch processing time');
    }

    console.log(`Finished processing ${totalProcessed} blogs, updated ${totalUpdated} with images`);
  } catch (error) {
    console.error('Error processing blogs:', error.message);
    throw error;
  }
}

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    console.log('Processing blogs');
    await processMarkdownImagesForBlogs(Blog);
    console.log('Processing personality blogs');
    await processMarkdownImagesForBlogs(PersonalityBlogs);

    console.log('Blog image extraction complete');
  } catch (error) {
    console.error('Error during processing:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
})();
