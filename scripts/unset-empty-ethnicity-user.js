const mongoose = require('mongoose');
const User = require('../models/user')

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

(async function unsetInvalidEthnicities() {
  try {

    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Database Connected')

    const result = await User.updateMany({ ethnicities: { $size: 0 } },{ $unset: { ethnicities: 1 }});
    
    console.log(`Modified ${result.modifiedCount} documents.`);
    
  } catch (err) {
    console.error('Error unsetting ethnicities:', err);
  } finally {
    await mongoose.disconnect();
  }
})();
