const mongoose = require('mongoose');
const { processPendingRequest } = require('../worker/lib/process-data-request');

/*
Run script with the following env var:
USER_ID
MONGODB_URI
MONGODB_URI_EVENT
MONGODB_URI_RECORDS
MONGODB_URI_PROFILE_VIEW
IMAGE_DOMAIN
*/


(async () => {
  await mongoose.connect(process.env.MONGODB_URI);
  console.log('Connected to database');

  const dataRequest = {
    userId: process.env.USER_ID,
  };
  await processPendingRequest(dataRequest, true);

  console.log('Done');
  await mongoose.disconnect();
})();
