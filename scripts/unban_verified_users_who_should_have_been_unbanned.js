const dotenv = require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/user');
const reportLib = require('../lib/report');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/experiment';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  const users = await User.find({
    'verification.status': 'verified',
    shadowBanned: true,
    bannedReason: {
      $in: [
        'unverified with infringing text (preemptive moderation)',
        'unverified with infringing text (sexual content in image found by preemptive moderation)',
        'unverified with banned email domain',
        'unverified web with unfamiliar email domain',
        'unverified with infringing text',
        'unverified with instagram spam in photos',
        'unverified with infringing text (found by OCR moderation)',
        'banned file found in profile picture',
      ]
    },
  });
  console.log(`Num users: ${users.length}`);

  let count = 0;
  let promisesArr = [];
  for (const user of users) {
    promisesArr.push(reportLib.unban(user, null, 'passed verification'));
    if (promisesArr.length == 100) {
      await Promise.all(promisesArr);
      count = count + 100;
      console.log(`Completed ${count}`);
      promisesArr = [];
    }
  }
  if (promisesArr.length) {
    await Promise.all(promisesArr);
  }

  console.log('done');
  await mongoose.disconnect();
})();
