const mongoose = require('mongoose');
const User = require('../models/user')

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

(async function unsetBackfillNumYourTurnChats() {
  try {
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Database Connected')
    const result = await User.updateMany({ backfillNumYourTurnChatsBegan: { $exists: true } }, { $unset: { backfillNumYourTurnChatsBegan: 1, backfillNumYourTurnChatsComplete: 1, 'metrics.numYourTurnChats': 1 }});
    console.log(`Modified ${result.modifiedCount} documents.`);
    
  } catch (err) {
    console.error('Error unsetting unsetBackfillNumYourTurnChats:', err);
  } finally {
    await mongoose.disconnect();
  }
})();