const dotenv = require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/user');
const emailLib = require('../lib/email');
const { locales } = require('../lib/translate');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

const emails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  for (const locale of ['en','zh-Hans']) {
    if (!locale) continue;
    const globalMergeVars = await emailLib.getGlobalMergeVars(locale);

    for (const email of emails) {
      const user = await User.findOne({email});
      user.locale = locale;
      await emailLib.sendDailyDigestEmailHelper([user], globalMergeVars, locale, 'daily-digest-v6');
      await emailLib.sendDeleteAccountEmail(user);
      await emailLib.sendFlashSaleEmail(user);
      await emailLib.sendInactiveEmail(user);
      await emailLib.sendNewLikeEmail(user, 'new-like-dating-v6');
      await emailLib.sendNewLikeEmail(user, 'new-like-friends-v6');
      await emailLib.sendNewMatchEmail(user);
      await emailLib.sendNewMessageEmail(user, 'Bob');
      await emailLib.sendNewRecommendationsEmail(user);
      await emailLib.sendVerificationSuccessfulEmail(user);
      await emailLib.sendVerificationFailedEmail(user, 'Reason: Photo Unclear. Please follow the profile verification guidelines.');
      await emailLib.sendWelcomeEmail(user);
      await emailLib.sendSignInEmailSES({ templateName: 'sign-in-v6', email, locale, timezone: user.timezone, signInLink: 'https://boo.world' });
    }
  }

  await mongoose.disconnect();
  console.log('Done');
  process.exit();
})();
