const fs = require('fs');
const { parse } = require('csv-parse/sync');
const { stringify } = require('csv-stringify/sync');
const openaiClient = require('../lib/openai-client');
const { OpenAI, ClaudeAPI, DeepseekTogetherAI } = require('../lib/prompt');

const runPrompt = async (subject, body) => {
  const client = openaiClient.getOpenaiClient();
  const provider = new OpenAI(client, 'gpt-4o-mini');
  const params = {
    prompt: `
You are a support agent for <PERSON><PERSON>'s contact email, a social networking and dating app. Please review the contents of this email and identify whether the request is legal in nature or legally sensitive. Examples include mentions of digital privacy acts like GDPR, DSA, or requests for data from law enforcement agencies, lawyers, or regulatory agencies. Output either true or false, along with an explanation.

Subject: ${subject}
Body: ${body}
`,
  };
  const response = await provider.executePrompt(params);
  console.log(params.prompt, response?.output);
  return response?.output;
};

(async () => {

  const pathToCsv = `emails.csv`;
  const input = fs.readFileSync(pathToCsv);
  const records = parse(input, {
    columns: true,
    skip_empty_lines: true,
    trim: true,
  });

  for (const record of records) {
    console.log(record.subject, record.body);
    record.output = await runPrompt(record.subject, record.body);
  }

  const output = stringify(records, {header: true});
  const outputFile = `${__dirname}/output.csv`;
  fs.writeFileSync(outputFile, output);
})();
