const mongoose = require('mongoose');
const sitemapPaginationLib = require('../lib/sitemap-pagination');
const databaseLib = require('../lib/database');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  console.log('Creating paginated subcategories for categories');
  await sitemapPaginationLib.createSitemapPaginatedForProfiles();
  console.log('Creating paginated profiles for Complete');
  console.log('-----------------------------------')

  const batchSize = 100
  console.log('Creating paginated subcategories for categories');
  await sitemapPaginationLib.createSitemapPaginationForCategory(0, batchSize);
  console.log('Creating paginated subcategories for categories Complete');
  console.log('-----------------------------------')

  console.log('Creating paginated profiles for subcategories');
  await sitemapPaginationLib.createSitemapPaginationForSubcategory(0, batchSize);
  console.log('Creating paginated profiles for subcategories Complete');
  console.log('-----------------------------------')

  console.log('Marking duplicate profiles by slugs');
  await await databaseLib.markDuplicateProfilesBySlugs();
  console.log('Marking duplicate profiles by slugs Complete');
  console.log('-----------------------------------')

  console.log('Script Completed');
  await mongoose.disconnect();
})();
