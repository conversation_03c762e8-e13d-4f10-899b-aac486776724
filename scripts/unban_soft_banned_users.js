const mongoose = require('mongoose');
const User = require('../models/user');
const reportLib = require('../lib/report');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/experiment';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  let batch = 0;
  let lastId;
  while (true) {
    const query = {
      shadowBanned: true,
      bannedReason: {
        $in: [
          'soft ban for unverified users',
          'unverified with infringing text (preemptive moderation)',
          'unverified with infringing text (sexual content in image found by preemptive moderation)',
          'new signup on old version (soft ban)',
          'unverified with banned email domain',
          'unverified web with unfamiliar email domain',
          'unverified with infringing text',
          'unverified with instagram spam in photos',
          'unverified with infringing text (found by OCR moderation)',
          'unverified with infringing text (saved banned infringing text)',
          'unverified with infringing text (saved banned infringing text backfill)',
          'unverified with banned email domain (backfill)',
          'unverified with instagram spam in photos (banned by cleanup script)',
        ]
      },
    };
    if (lastId) {
      query._id = { $gt: lastId };
    }
    const users = await User.find(query).sort('_id').limit(100);
    if (!users.length) {
      break;
    }
    batch++;
    lastId = users[users.length - 1]._id;
    console.log(`batch: ${batch}, num users: ${users.length}, lastId: ${lastId}`);

    let promisesArr = [];
    for (const user of users) {
      promisesArr.push(reportLib.unban(user, null, 'undo bans for unverified users'));
    }
    await Promise.all(promisesArr);
    console.log(`Completed batch ${batch}`);
  }

  console.log('done');
  await mongoose.disconnect();
})();
