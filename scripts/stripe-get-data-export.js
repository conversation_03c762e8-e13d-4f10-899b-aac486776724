const { getStripeDataExport } = require('../lib/stripe-data-export');

// Example usage:
(async () => {
  try {
    const customerId = "cus_Rvbp6Wgwk8wysL"; // <-- your Stripe customer ID
    const exportPayload = await getStripeDataExport(customerId);
    console.log(JSON.stringify(exportPayload, null, 2));
  } catch (err) {
    console.error("Failed to export stripe data:", err);
    process.exit(1);
  }
})();
