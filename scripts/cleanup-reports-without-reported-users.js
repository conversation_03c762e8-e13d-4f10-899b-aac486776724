/* eslint-disable no-continue */
/*
ENV Requirements
    - MONGODB_URI
    - MONGODB_URI_EVENT
*/

require('log-timestamp');
const mongoose = require('mongoose');
const User = require('../models/user');
const Report = require('../models/report');
const PreemptiveModerationLog = require('../models/preemptive-moderation-log');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/test';
const BATCH_SIZE = 500;

const extractUserIdFromImageUrl = (url) => {
  const match = url.match(/https?:\/\/[^/]+\/([^/]+)\//);
  return match ? match[1] : null;
};

const processReport = async (report) => {
  let userId = null;
  let imageUrl = null;

  try {
    const prompt = JSON.parse(report.openai.prompt);
    imageUrl = prompt.imageUrls?.[0] ?? null;
    if (!imageUrl) throw new Error(`No image URL, can not extract user ID!`);

    userId = extractUserIdFromImageUrl(imageUrl);
    if (!userId) throw new Error(`Cannot extract user ID from image URL: ${imageUrl}`);

    const user = await User.findById(userId);
    if (!user) throw new Error(`User not found for ID: ${userId}`);

    const { _id, ...rest } = report;
    const newLog = new PreemptiveModerationLog({
      ...rest,
      comment: null,
      reportedUser: user._id,
      reason: ['Spam'],
      status: 'verified',
    });
    await newLog.save();

    if (user.profileTempBanReportId?.toString() === report._id.toString()) {
      user.profileTempBanReportId = newLog._id;
      await user.save();
    }

    return report._id;
  } catch (err) {
    console.log(`[ERROR] Failed processing report | reportId=${report._id} | error=${err}`);
    return null;
  }
};

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log(`[INFO] Connected to MongoDB`);

    let lastId = null;
    let hasMore = true;

    while (hasMore) {
      const query = {
        reportedUser: { $exists: false },
        status: 'needs review',
        createdAt: { $gt: new Date('2025-08-20T00:00:00Z') },
      };

      if (lastId) query._id = { $gt: lastId };

      const reports = await Report.find(query).sort({ _id: 1 }).limit(BATCH_SIZE).lean();
      console.log(`[INFO] Processing batch | reportCount=${reports.length}`);

      if (!reports.length) {
        hasMore = false;
        break;
      }

      const deletableIds = (await Promise.all(reports.map(processReport))).filter(Boolean);

      if (deletableIds.length) {
        await Report.deleteMany({ _id: { $in: deletableIds } });
        console.log(`[INFO] Deleted reports | deletedReportCount=${deletableIds.length}`);
      }

      lastId = reports[reports.length - 1]._id;
    }
  } catch (err) {
    console.log(`[ERROR] Script failed | error=${err}`);
  } finally {
    await mongoose.disconnect();
    console.log(`[INFO] Disconnected from MongoDB`);
    process.exit(0);
  }
})();
