const { rekognition, BANNED_COLL_ID } = require('../lib/rekognition');

(async () => {
  console.log('Starting Rekognition collection cleanup...');

  let data;
  try {
    data = await rekognition.listCollections().promise();
  } catch (err) {
    console.log('Error listing Rekognition collections:', err.message);
    return;
  }

  if (!data?.CollectionIds || !Array.isArray(data.CollectionIds)) {
    console.log('Invalid or missing CollectionIds in Rekognition response.');
    return;
  }

  if (data.CollectionIds.includes(BANNED_COLL_ID)) {
    console.log(`Collection "${BANNED_COLL_ID}" found. Attempting to delete...`);

    try {
      await rekognition.deleteCollection({ CollectionId: BANNED_COLL_ID }).promise();
      console.log(`Collection "${BANNED_COLL_ID}" deleted successfully.`);
    } catch (err) {
      console.log(`Failed to delete collection "${BANNED_COLL_ID}":`, err.message);
      return;
    }
  } else {
    console.log(`Collection "${BANNED_COLL_ID}" does not exist.`);
  }

  console.log('Rekognition cleanup complete.');
})();
