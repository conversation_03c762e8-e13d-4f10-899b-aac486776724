// Adapted from https://cloud.google.com/translate/docs/basic/detecting-language
// Run with GOOGLE_APPLICATION_CREDENTIALS

// Imports the Google Cloud client library
const {Translate} = require('@google-cloud/translate').v2;

// Creates a client
const translate = new Translate();

const textEn = 'The text for which to detect language, e.g. Hello, world!';
const textId = 'Teks yang ingin dideteksi bahasanya, misalnya Halo, dunia!';
const textJa ='言語を検出するテキスト (例: Hello, world!)'
const textEmoji = '🥰';

// Detects the language. "text" can be a string for detecting the language of
// a single piece of text, or an array of strings for detecting the languages
// of multiple texts.
async function detectLanguage(text) {
  const res = await translate.detect(text);
  console.log(JSON.stringify(res, null, 2));
  return res
}

async function translateToEnglish(text, sourceLanguage) {
  try {
    const [translation] = await translate.translate(text, {
      from: sourceLanguage,
      to: 'en',
    });
    return translation;
  } catch (error) {
    console.error('Translation failed:', error.message);
    throw new Error('Failed to translate text');
  }
}


async function detectAndTranslate(text) {
  try {
    // Detect language first
    const detection = await detectLanguage(text);
    const { language, confidence } = detection[0];

    // If not English, translate
    if (language !== 'en' && language !== 'und') {
      console.log(`the text '${text}' language is: ${language}`)
      const translatedText = await translateToEnglish(text, language);
      console.log(`translate to English result: '${translatedText}'`)
      return {
        originalText: text,
        detectedLanguage: language,
        confidence,
        translatedText,
      };
    }

    // If already English, return as-is
    console.log(`the text '${text}' already English, return as-is`)
    return {
      originalText: text,
      detectedLanguage: 'en',
      confidence,
      translatedText: text,
    };
  } catch (error) {
    console.error('Detect & Translate failed:', error.message);
    throw error;
  }
}

(async () => {
  console.log('Test with EN text')
  await detectAndTranslate(textEn);
  console.log('===================')
  console.log('Test with ID text')
  await detectAndTranslate(textId);
  console.log('===================')
  console.log('Test with JA text')
  await detectAndTranslate(textJa);
  console.log('===================')
  console.log('Test with emoji text')
  await detectAndTranslate(textEmoji);
})();
