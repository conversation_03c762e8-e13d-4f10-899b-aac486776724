require('log-timestamp');
const dotenv = require("dotenv").config();
const fs = require('fs');
const request = require('requestretry');
const path = require('path')
const mongoose = require("mongoose");
const Comment = require("../models/comment");
const Question = require("../models/question");

const USER_ID = process.env.USER_ID;
const questionProjection = 'createdAt createdBy title text image audio audioTranscription gif poll interestName numComments';
const commentProjection = 'createdAt createdBy text image audio audioTranscription gif repliedTo postRepliedTo depth numComments';

const mediaFolder = 'post_threads_media';
fs.mkdirSync(mediaFolder);
let numMediaFiles = 0;
async function downloadFile(key) {
  const url = `https://images.prod.boo.dating/${key}`;
  const ext = path.extname(key);
  const mediaId = numMediaFiles++;
  const fileName = `${mediaId}${ext}`;

  await request({ url }).pipe(fs.createWriteStream(`${mediaFolder}/${fileName}`));
  return fileName;
}

async function downloadMediaOnPost(post) {
  if (post.image) {
    post.imageDownloaded = await downloadFile(post.image);
  }
  if (post.audio) {
    post.audioDownloaded = await downloadFile(post.audio);
  }
}

async function downloadCommentReplies(comment) {
  console.log(`Processing comment ${comment._id}`);
  await downloadMediaOnPost(comment);
  const comments = await Comment.find({ parent: comment._id }, commentProjection).sort('createdAt').lean();
  for (const reply of comments) {
    await downloadMediaOnPost(reply);
  }
  comment.comments = comments;
  console.log(`Processed comment ${comment._id}`);
}

async function downloadQuestionComments(question) {
  console.log(`Processing question ${question._id}`);
  await downloadMediaOnPost(question);
  const comments = await Comment.find({ parent: question._id }, commentProjection).sort('createdAt').lean();
  const promises = [];
  for (const comment of comments) {
    promises.push(downloadCommentReplies(comment));
  }
  await Promise.all(promises);
  question.comments = comments;
  console.log(`Processed question ${question._id}`);
}


(async () => {
  await mongoose.connect(process.env.MONGODB_URI);
  console.log("Connected to MongoDB");

  const questionIds = await Question.distinct('_id', { createdBy: USER_ID });
  console.log(`Found ${questionIds.length} questionIds`);
  const questionIdsFromComments = await Comment.distinct('question', { createdBy: USER_ID });
  console.log(`Found ${questionIdsFromComments.length} questionIdsFromComments`);
  const uniqueQuestionIds = [...new Set(questionIds.concat(questionIdsFromComments))];
  console.log(`Found ${uniqueQuestionIds.length} uniqueQuestionIds`);

  const questions = await Question.find({ _id: { $in: uniqueQuestionIds } }, questionProjection).lean();
  console.log(`Found ${questions.length} questions`);
  const promises = [];
  for (const question of questions) {
    promises.push(downloadQuestionComments(question));
  }
  await Promise.all(promises);

  fs.writeFileSync('post_threads.json', JSON.stringify(questions,null,2));

  mongoose.disconnect();
  console.log("Disconnected from MongoDB");
})();
