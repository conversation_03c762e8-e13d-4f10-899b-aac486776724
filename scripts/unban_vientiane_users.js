const mongoose = require('mongoose');
const User = require('../models/user');
const reportLib = require('../lib/report');

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/experiment';

(async () => {
  await mongoose.connect(MONGODB_URI);
  console.log('Connected to database');

  let batch = 0;
  let lastId;
  while (true) {
    const query = {
      shadowBanned: true,
      bannedReason: {
        $in: [
          'Auto-ban: Vientiane',
          'Vientiane',
        ]
      },
    };
    if (lastId) {
      query._id = { $gt: lastId };
    }
    const users = await User.find(query).sort('_id').limit(100);
    if (!users.length) {
      break;
    }
    batch++;
    lastId = users[users.length - 1]._id;
    console.log(`batch: ${batch}, num users: ${users.length}, lastId: ${lastId}`);

    let promisesArr = [];
    for (const user of users) {
      promisesArr.push(reportLib.unban(user, null, 'undo bans for vientiane users'));
    }
    await Promise.all(promisesArr);
    console.log(`Completed batch ${batch}`);
  }

  console.log('done');
  await mongoose.disconnect();
})();
