/**
 * Export consumer-specific Stripe data for DSAR/CCPA responses.
 *
 * Notes
 * - This script fetches the fields typically needed for a DSAR, including
 *   identifiers, transaction facts, risk flags (if present), and metadata.
 * - Stripe never returns full PAN/CVV; we output brand/last4/expiry where relevant.
 * - Refunds and disputes are derived from the customer's charges.
 */

const stripe = require('../lib/stripe').stripe;

/** Utility: iterate an ApiListPromise to a plain array */
async function toArray(apiListPromise) {
  const out = [];
  for await (const item of apiListPromise) out.push(item);
  return out;
}

/** Narrow an address object to a clean, DSAR-friendly shape (nulls instead of undefined). */
function normalizeAddress(addr) {
  if (!addr) return null;
  return {
    line1: addr.line1 ?? null,
    line2: addr.line2 ?? null,
    city: addr.city ?? null,
    region: addr.state ?? addr.region ?? null,
    postal_code: addr.postal_code ?? null,
    country: addr.country ?? null,
  };
}

/** Build a slim copy of billing_details */
function pickBillingDetails(bd) {
  if (!bd) return null;
  return {
    name: bd.name ?? null,
    email: bd.email ?? null,
    phone: bd.phone ?? null,
    address: normalizeAddress(bd.address ?? null),
  };
}

/** Get the Customer core record */
async function getCustomer(customerId) {
  const c = await stripe.customers.retrieve(customerId);
  return {
    id: c.id,
    email: c.email ?? null,
    name: c.name ?? null,
    phone: c.phone ?? null,
    address: normalizeAddress(c.address ?? null),
    shipping: c.shipping
      ? {
          name: c.shipping.name ?? null,
          address: normalizeAddress(c.shipping.address ?? null),
          phone: c.shipping.phone ?? null,
        }
      : null,
    preferred_locales: Array.isArray(c.preferred_locales) ? c.preferred_locales : [],
    metadata: c.metadata ?? {},
  };
}

/**
 * Get PaymentMethods for a set of types.
 * Add or remove types to match your implementation.
 */
const PAYMENT_METHOD_TYPES = ["card", "us_bank_account", "sepa_debit"]; // extend as needed

async function getPaymentMethods(customerId) {
  const all = [];
  for (const type of PAYMENT_METHOD_TYPES) {
    const items = await toArray(
      stripe.paymentMethods.list({ customer: customerId, type, limit: 100 })
    );
    for (const pm of items) {
      const base = {
        id: pm.id,
        type: pm.type,
        billing_details: pickBillingDetails(pm.billing_details),
        metadata: pm.metadata ?? {},
      };

      if (pm.type === "card") {
        all.push({
          ...base,
          card: {
            brand: pm.card?.brand ?? null,
            funding: pm.card?.funding ?? null,
            country: pm.card?.country ?? null,
            last4: pm.card?.last4 ?? null,
            exp_month: pm.card?.exp_month ?? null,
            exp_year: pm.card?.exp_year ?? null,
            fingerprint: pm.card?.fingerprint ?? null,
            wallet: pm.card?.wallet ? { type: pm.card.wallet.type ?? null } : null,
          },
        });
      } else if (pm.type === "us_bank_account") {
        all.push({
          ...base,
          us_bank_account: {
            bank_name: pm.us_bank_account?.bank_name ?? null,
            last4: pm.us_bank_account?.last4 ?? null,
            account_holder_type: pm.us_bank_account?.account_holder_type ?? null,
            account_type: pm.us_bank_account?.account_type ?? null,
            fingerprint: pm.us_bank_account?.fingerprint ?? null,
          },
        });
      } else if (pm.type === "sepa_debit") {
        all.push({
          ...base,
          sepa_debit: {
            bank_code: pm.sepa_debit?.bank_code ?? null,
            branch_code: pm.sepa_debit?.branch_code ?? null,
            country: pm.sepa_debit?.country ?? null,
            fingerprint: pm.sepa_debit?.fingerprint ?? null,
            last4: pm.sepa_debit?.last4 ?? null,
          },
        });
      } else {
        // Generic fallback shape for other PM types you might add
        all.push({ ...base, details: pm[pm.type] ?? {} });
      }
    }
  }
  return all;
}

/** Get Charges for the customer (and slim to DSAR-fields) */
async function getCharges(customerId) {
  const charges = await toArray(
    stripe.charges.list({
      customer: customerId,
      limit: 100,
      expand: ["data.balance_transaction"], // optional; useful if you need fee/net (not PI)
    })
  );
  return charges.map((ch) => ({
    id: ch.id,
    amount: ch.amount,
    currency: ch.currency,
    created: ch.created ? new Date(ch.created * 1000).toISOString() : null,
    status: ch.status ?? null,
    captured: ch.captured ?? null,
    description: ch.description ?? null,
    receipt_url: ch.receipt_url ?? null,
    billing_details: pickBillingDetails(ch.billing_details),
    payment_method_details: ch.payment_method_details
      ? {
          type: ch.payment_method_details.type ?? null,
          card:
            ch.payment_method_details.card
              ? {
                  brand: ch.payment_method_details.card.brand ?? null,
                  last4: ch.payment_method_details.card.last4 ?? null,
                  exp_month: ch.payment_method_details.card.exp_month ?? null,
                  exp_year: ch.payment_method_details.card.exp_year ?? null,
                }
              : null,
          us_bank_account:
            ch.payment_method_details.us_bank_account
              ? {
                  bank_name: ch.payment_method_details.us_bank_account.bank_name ?? null,
                  last4: ch.payment_method_details.us_bank_account.last4 ?? null,
                }
              : null,
        }
      : null,
    outcome: ch.outcome
      ? { risk_level: ch.outcome.risk_level ?? null, risk_score: ch.outcome.risk_score ?? null }
      : null,
    metadata: ch.metadata ?? {},
    dispute_id:
      typeof ch.dispute === "string" ? ch.dispute : (ch.dispute && ch.dispute.id) || null,
  }));
}

/** Gather Refunds for each charge (Stripe API doesn't filter refunds by customer directly) */
async function getRefundsForCharges(charges) {
  const all = [];
  for (const ch of charges) {
    const refunds = await toArray(
      stripe.refunds.list({ charge: ch.id, limit: 100 })
    );
    for (const rf of refunds) {
      all.push({
        id: rf.id,
        amount: rf.amount,
        currency: rf.currency,
        status: rf.status ?? null,
        reason: rf.reason ?? null,
        created: rf.created ? new Date(rf.created * 1000).toISOString() : null,
        charge: ch.id,
        payment_intent: typeof rf.payment_intent === "string" ? rf.payment_intent : rf.payment_intent?.id ?? null,
        metadata: rf.metadata ?? {},
      });
    }
  }
  return all;
}

/** Gather Disputes tied to the customer's charges */
async function getDisputesForCharges(charges) {
  const all = [];
  for (const ch of charges) {
    const dpId = typeof ch.dispute_id === "string" ? ch.dispute_id : null;
    if (!dpId) continue;
    const dp = await stripe.disputes.retrieve(dpId);
    all.push({
      id: dp.id,
      amount: dp.amount,
      currency: dp.currency,
      reason: dp.reason ?? null,
      status: dp.status ?? null,
      created: dp.created ? new Date(dp.created * 1000).toISOString() : null,
      charge: typeof dp.charge === "string" ? dp.charge : dp.charge?.id ?? null,
      evidence_summary: dp.evidence ? {
        product_description: dp.evidence.product_description ?? null,
        customer_email_address: dp.evidence.customer_email_address ?? null,
        customer_name: dp.evidence.customer_name ?? null,
        // Add other evidence fields you actually use; be mindful of third-party PI redactions.
      } : null,
    });
  }
  return all;
}

/** PaymentIntents for the customer */
async function getPaymentIntents(customerId) {
  const pis = await toArray(
    stripe.paymentIntents.list({
      customer: customerId,
      limit: 100,
      expand: ["data.payment_method", "data.latest_charge"],
    })
  );
  return pis.map((pi) => ({
    id: pi.id,
    amount: pi.amount,
    currency: pi.currency,
    status: pi.status ?? null,
    created: pi.created ? new Date(pi.created * 1000).toISOString() : null,
    payment_method: typeof pi.payment_method === "string" ? pi.payment_method : pi.payment_method?.id ?? null,
    latest_charge: typeof pi.latest_charge === "string" ? pi.latest_charge : pi.latest_charge?.id ?? null,
    cancellation_reason: pi.cancellation_reason ?? null,
    last_payment_error: pi.last_payment_error
      ? {
          code: pi.last_payment_error.code ?? null,
          message: pi.last_payment_error.message ?? null,
          type: pi.last_payment_error.type ?? null,
        }
      : null,
    shipping: pi.shipping
      ? {
          name: pi.shipping.name ?? null,
          address: normalizeAddress(pi.shipping.address ?? null),
          phone: pi.shipping.phone ?? null,
        }
      : null,
    metadata: pi.metadata ?? {},
  }));
}

/** SetupIntents (if you use them to save PMs) */
async function getSetupIntents(customerId) {
  const setis = await toArray(
    stripe.setupIntents.list({
      customer: customerId,
      limit: 100,
      expand: ["data.payment_method"],
    })
  );
  return setis.map((si) => ({
    id: si.id,
    status: si.status ?? null,
    created: si.created ? new Date(si.created * 1000).toISOString() : null,
    payment_method: typeof si.payment_method === "string" ? si.payment_method : si.payment_method?.id ?? null,
    last_setup_error: si.last_setup_error
      ? {
          code: si.last_setup_error.code ?? null,
          message: si.last_setup_error.message ?? null,
          type: si.last_setup_error.type ?? null,
        }
      : null,
    metadata: si.metadata ?? {},
  }));
}

/** Invoices (Stripe Billing) */
async function getInvoices(customerId) {
  const invs = await toArray(
    stripe.invoices.list({
      customer: customerId,
      limit: 100,
      expand: ["data.charge"],
    })
  );
  return invs.map((inv) => ({
    id: inv.id,
    number: inv.number ?? null,
    status: inv.status ?? null,
    amount_due: inv.amount_due ?? null,
    amount_paid: inv.amount_paid ?? null,
    amount_remaining: inv.amount_remaining ?? null,
    created: inv.created ? new Date(inv.created * 1000).toISOString() : null,
    customer_email: inv.customer_email ?? null,
    hosted_invoice_url: inv.hosted_invoice_url ?? null,
    charge: typeof inv.charge === "string" ? inv.charge : inv.charge?.id ?? null,
    billing_reason: inv.billing_reason ?? null,
    metadata: inv.metadata ?? {},
  }));
}

/** Subscriptions (Stripe Billing) */
async function getSubscriptions(customerId) {
  const subs = await toArray(
    stripe.subscriptions.list({
      customer: customerId,
      limit: 100,
      expand: ["data.items.data.price"],
    })
  );
  return subs.map((sub) => ({
    id: sub.id,
    status: sub.status ?? null,
    start_date: sub.start_date ? new Date(sub.start_date * 1000).toISOString() : null,
    current_period_start: sub.current_period_start ? new Date(sub.current_period_start * 1000).toISOString() : null,
    current_period_end: sub.current_period_end ? new Date(sub.current_period_end * 1000).toISOString() : null,
    cancel_at_period_end: sub.cancel_at_period_end ?? null,
    canceled_at: sub.canceled_at ? new Date(sub.canceled_at * 1000).toISOString() : null,
    items: (sub.items?.data || []).map((it) => ({
      id: it.id,
      price_id: typeof it.price === "string" ? it.price : it.price?.id ?? null,
      price_nickname: typeof it.price === "object" ? it.price?.nickname ?? null : null,
      product: typeof it.price === "object" ? (typeof it.price.product === "string" ? it.price.product : it.price.product?.id ?? null) : null,
      quantity: it.quantity ?? null,
    })),
    metadata: sub.metadata ?? {},
  }));
}

/** Main: fetch everything in parallel and print as JSON */
async function getStripeDataExport(customerId) {

  if (!customerId) {
    return null;
  }

  const [customer, paymentMethods, charges, paymentIntents, setupIntents, invoices, subscriptions] =
    await Promise.all([
      getCustomer(customerId).catch((e) => {
        console.error("Failed to retrieve customer:", e.message);
        return null;
      }),
      getPaymentMethods(customerId).catch((e) => {
        console.error("Failed to list payment methods:", e.message);
        return [];
      }),
      getCharges(customerId).catch((e) => {
        console.error("Failed to list charges:", e.message);
        return [];
      }),
      getPaymentIntents(customerId).catch((e) => {
        console.error("Failed to list payment intents:", e.message);
        return [];
      }),
      getSetupIntents(customerId).catch((e) => {
        console.error("Failed to list setup intents:", e.message);
        return [];
      }),
      getInvoices(customerId).catch((e) => {
        console.error("Failed to list invoices:", e.message);
        return [];
      }),
      getSubscriptions(customerId).catch((e) => {
        console.error("Failed to list subscriptions:", e.message);
        return [];
      }),
    ]);

  // Refunds & Disputes depend on charges
  const [refunds, disputes] = await Promise.all([
    getRefundsForCharges(charges).catch((e) => {
      console.error("Failed to list refunds:", e.message);
      return [];
    }),
    getDisputesForCharges(charges).catch((e) => {
      console.error("Failed to retrieve disputes:", e.message);
      return [];
    }),
  ]);

  const exportPayload = {
    stripe: {
      customer,
      payment_methods: paymentMethods,
      charges,
      payment_intents: paymentIntents,
      setup_intents: setupIntents,
      refunds,
      disputes,
      invoices,
      subscriptions,
    },
    generated_at: new Date().toISOString(),
  };

  return exportPayload;
}

module.exports = {
  getStripeDataExport,
};
