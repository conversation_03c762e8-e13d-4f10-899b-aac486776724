const basicLib = require('../lib/basic');

const nicheToSubnichesObj = {
  gaming: ['Word Games', 'Gacha RPG', 'Action Survival Games', 'Adventure Games', 'Casual Games'],
};

const normalize = (s) => s.toLowerCase().trim();

// Flat, lowercased list of all subniches
const allSubniches = Object.values(nicheToSubnichesObj)
  .flat()
  .map(normalize);

// Map from lowercased subniche -> niche
const subnicheToNiche = new Map(
  Object.entries(nicheToSubnichesObj).flatMap(([niche, subs]) =>
    subs.map((s) => [normalize(s), niche])
  )
);


const allNiches = [
  'anime',
  'gaming',
  'lifting',
  'kpop',
  'movies',
  'music',
  'art',
  'fashion',

  // alternate spellings
  'weightlifting',
  'game',
  'movie',

  ...allSubniches,
];

function formatNiche(niche) {
  if (niche == 'game') {
    return 'gaming';
  }
  if (niche == 'weightlifting') {
    return 'lifting';
  }
  if (niche == 'movie') {
    return 'movies';
  }
  return niche;
}

function getNicheFromCampaign(user) {

  let niche;

  // handle influencer campaigns
  if (!niche && user?.kochava?.network?.toLowerCase()?.startsWith('influencer')) {
    niche = allNiches.find(word => basicLib.containsEntireWord(user.kochava?.network?.toLowerCase(), word));
  }

  // handle all other campaigns
  if (!niche) {
    niche = allNiches.find(word => basicLib.containsEntireWord(user.kochava?.partner_campaign_name?.toLowerCase(), word));
  }

  if (niche) {
    // handle subniches
    const nicheFromSubniche = subnicheToNiche.get(niche);
    if (nicheFromSubniche) {
      return {
        niche: nicheFromSubniche,
        subniche: niche,
      };
    }

    return {
      niche: formatNiche(niche),
    };
  }

  return {};
}

module.exports = { getNicheFromCampaign };
