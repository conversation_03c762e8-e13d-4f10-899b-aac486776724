const { SQSClient, SendMessageCommand } = require("@aws-sdk/client-sqs");

// Initialize SQS client with configuration
const sqsClient = new SQSClient({
  region: process.env.AWS_REGION || "us-east-1",
  maxAttempts: 3, // Automatic retries for transient errors
});

let SQS_URL_AI_IMAGE = 'MOCK_SQS_URL_AI_IMAGE';
if (process.env.NODE_ENV == 'prod') {
  SQS_URL_AI_IMAGE = 'https://sqs.us-east-1.amazonaws.com/408437307763/ai-image-worker-prod';
}
if (process.env.NODE_ENV == 'beta') {
  SQS_URL_AI_IMAGE = 'https://sqs.us-east-1.amazonaws.com/408437307763/ai-image-worker-beta';
}

async function sendToFeatureQueue(payload) {
  const command = new SendMessageCommand({
    QueueUrl: SQS_URL_AI_IMAGE,
    MessageBody: JSON.stringify(payload),
    MessageAttributes: {
      FeatureName: {
        DataType: "String",
        StringValue: "AI-Image",
      },
      SentTimestamp: {
        DataType: "String",
        StringValue: new Date().toISOString(),
      },
    },
  });

  try {
    const result = await sqsClient.send(command);
    console.log(`Message sent successfully. Message ID: ${result.MessageId}`);
    return result.MessageId;
  } catch (error) {
    console.error("Error sending payload to queue:", error);

    // Handle specific error cases
    if (error.name === "QueueDoesNotExist") {
      throw new Error(`The queue ${SQS_URL_AI_IMAGE} does not exist`);
    }
    throw new Error(`Error sending payload to queue: ${error}`);
  }
}

module.exports = { sendToFeatureQueue };
