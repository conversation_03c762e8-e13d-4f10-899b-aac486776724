function assemble(user, suggestionDetail, inputPrompt = '') {
  const gender = user.gender ? user.gender : "nonbinary";
  const ethnicities = user.ethnicities?.[0] || null

  let prompt
  if(suggestionDetail && suggestionDetail.type.toLowerCase() === 'visual style'){
    prompt = `${suggestionDetail.prompt}, ${inputPrompt}`
  }else{
    prompt = inputPrompt
  }
  
  const basePrompt = `I am a ${user.age}-year-old ${ethnicities ? ethnicities : ''} ${gender} wearing casual clothes`

  const fullPrompt = `${basePrompt}. ${prompt.replace(/"/g, '').trim()}`

  let appliedSuggestion = {}
  if(suggestionDetail){
    appliedSuggestion = {
      type: suggestionDetail.type.toLowerCase(),
      id: suggestionDetail.id,
      prompt: suggestionDetail.prompt
    }
  }
  
  return { fullPrompt, appliedSuggestion };
}

module.exports = { assemble };
