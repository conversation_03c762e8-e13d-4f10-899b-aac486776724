const promptsLib = require('./prompts');
const languageLib = require('./languages');
const { translate_frontend } = require('./translate');

const getUserBioAndPrompts = (user) => {
  let bio = null;
  let bioPrompts = null;

  const bioParts = [];
  if (user.description?.length) bioParts.push(user.description);
  if (user.audioDescriptionTranscription) bioParts.push(`AI Transcript: ${user.audioDescriptionTranscription}`);
  bio = bioParts.length ? `${bioParts.join('\n')}` : null;

  if (Array.isArray(user.prompts) && user.prompts.length) {
    const validPrompts = user.prompts
      .map((prompt) => {
        const promptText = promptsLib.getPromptText(prompt);
        return promptText ? `${promptText}: ${prompt.answer}` : null;
      })
      .filter(Boolean);

    if (validPrompts.length) {
      bioPrompts = validPrompts.map((text, index) => `${index + 1}. ${text}`).join('\n');
    }
  }
  return { bio, bioPrompts };
};

const getMatchingFilters = (user) => {
  const matchingFilters = [];
  const {
    relationshipStatus,
    relationshipType,
    minAge,
    maxAge,
    local,
    distance,
    global,
    countries,
    interestNames,
    bioLength,
    keywords,
    languages,
    personality,
    horoscopes,
    enneagrams,
    exercise,
    educationLevel,
    drinking,
    smoking,
    kids,
    ethnicities,
    religion,
  } = user?.preferences || {};

  if (Array.isArray(relationshipStatus) && relationshipStatus.length) {
    matchingFilters.push(`Relationship Status: ${relationshipStatus.join(', ')}`);
  }

  if (Array.isArray(relationshipType) && relationshipType.length) {
    matchingFilters.push(`Relationship Type: ${relationshipType.join(', ')}`);
  }

  if (minAge) matchingFilters.push(`Min Age: ${minAge}`);
  if (maxAge) matchingFilters.push(`Max Age: ${maxAge}`);
  if (bioLength) matchingFilters.push(`Bio Length: ${bioLength}`);

  if (local && distance && !global) {
    matchingFilters.push(`Users Within: ${distance} miles`);
  }

  if (global) {
    if (Array.isArray(countries) && countries.length) {
      matchingFilters.push(`Countries: ${countries.join(', ')}`);
    } else {
      matchingFilters.push('Countries: All');
    }
  }

  if (Array.isArray(interestNames) && interestNames.length) {
    matchingFilters.push(`Interests: ${interestNames.join(', ')}`);
  }

  if (Array.isArray(keywords) && keywords.length) {
    matchingFilters.push(`Profile Keywords: ${keywords.join(', ')}`);
  }

  if (Array.isArray(languages) && languages.length) {
    const languageNames = languages.map((code) => languageLib.mapLanguageCodeToName(code === 'he' ? 'iw' : code));
    matchingFilters.push(`Languages: ${languageNames.join(', ')}`);
  }

  if (Array.isArray(personality) && personality.length && personality.length <= 5) {
    matchingFilters.push(`Personality: ${personality.join(', ')}`);
  }

  if (Array.isArray(horoscopes) && horoscopes.length) {
    matchingFilters.push(`Zodiacs: ${horoscopes.join(', ')}`);
  }

  if (Array.isArray(enneagrams) && enneagrams.length) {
    matchingFilters.push(`Enneagrams: ${enneagrams.join(', ')}`);
  }

  if (Array.isArray(exercise) && exercise.length) {
    matchingFilters.push(`Exercise: ${exercise.join(', ')}`);
  }

  if (Array.isArray(educationLevel) && educationLevel.length) {
    matchingFilters.push(`Education Level: ${educationLevel.join(', ')}`);
  }

  if (Array.isArray(drinking) && drinking.length) {
    matchingFilters.push(`Drinking: ${drinking.join(', ')}`);
  }

  if (Array.isArray(smoking) && smoking.length) {
    matchingFilters.push(`Smoking: ${smoking.join(', ')}`);
  }

  if (Array.isArray(kids) && kids.length) {
    matchingFilters.push(`Kids: ${kids.join(', ')}`);
  }

  if (Array.isArray(religion) && religion.length) {
    matchingFilters.push(`Religion: ${religion.join(', ')}`);
  }

  if (Array.isArray(ethnicities) && ethnicities.length) {
    matchingFilters.push(`Ethnicities: ${ethnicities.join(', ')}`);
  }

  return matchingFilters.length ? `Matching Filters: \n${matchingFilters.join('\n')}` : null;
};

const prepareProfileDetails = (user) => {
  const profileParts = [];

  if (user.age) profileParts.push(`Age: ${user.age}`);
  if (user.gender) profileParts.push(`Gender: ${user.gender}`);

  const { bio, bioPrompts } = getUserBioAndPrompts(user);
  profileParts.push(`Bio: ${bio || '(none)'}`);
  profileParts.push(`Prompts: ${bioPrompts ? '\n' + bioPrompts : '(none)'}`);

  if (user.education) profileParts.push(`Education: ${user.education}`);
  if (user.work) profileParts.push(`Work: ${user.work}`);
  if (Array.isArray(user.ethnicities) && user.ethnicities.length) {
    profileParts.push(`Ethnicities: ${user.ethnicities.join(', ')}`);
  }

  const { exercise, educationLevel, drinking, smoking, kids, religion } = user.moreAboutUser || {};
  if (exercise) profileParts.push(`Exercise: ${exercise}`);
  if (educationLevel) profileParts.push(`Education Level: ${educationLevel}`);
  if (drinking) profileParts.push(`Drinking: ${drinking}`);
  if (smoking) profileParts.push(`Smoking: ${smoking}`);
  if (kids) profileParts.push(`Kids: ${kids}`);
  if (religion) profileParts.push(`Religion: ${religion}`);

  const locationParts = [];
  if (user.city) locationParts.push(user.city);
  if (user.state) locationParts.push(user.state);
  if (user.country) locationParts.push(user.country);
  if (locationParts.length) {
    profileParts.push(`Location: ${locationParts.join(', ')}`);
  }

  if (Array.isArray(user.interestNames) && user.interestNames.length) {
    profileParts.push(`Interests: ${user.interestNames.join(', ')}`);
  }

  const preferences = [];
  const formatPreference = (type, values, subPreferences) => {
    if (!Array.isArray(values) || !values.length) return null;

    const isOnlyNonBinary = values.length === 1 && values[0] === 'non-binary';
    const mainText = subPreferences?.length ? `${type}: ${subPreferences}` : type;

    return isOnlyNonBinary ? mainText : `${mainText} with ${values.join(', ')}`;
  };

  const datingPreference = formatPreference('Dating', user.preferences?.dating, user.datingSubPreferences);
  const friendsPreference = formatPreference('Friends', user.preferences?.friends);

  if (datingPreference) preferences.push(datingPreference);
  if (friendsPreference) preferences.push(friendsPreference);

  if (preferences.length) {
    profileParts.push(``);
    profileParts.push(`Looking For: ${preferences.join(' / ')}`);
  }

  const matchingFilters = getMatchingFilters(user);
  if (matchingFilters) {
    profileParts.push(matchingFilters);
  }

  return profileParts;
};

const buildExampleBlock = (user, locale) => {
  const translate = (text) => translate_frontend(text, locale);

  const hasDating = Array.isArray(user.preferences?.dating) && user.preferences.dating.length > 0;
  const hasFriends = !hasDating && Array.isArray(user.preferences?.friends) && user.preferences.friends.length > 0;

  const dataMap = {
    dating: {
      tagline: translate('Find your best match faster.'),
      heading: translate('Your Soulmate ✨'),
      text: translate('Someone who effortlessly understands and appreciates you for who you naturally are.'),
    },
    friends: {
      tagline: translate('Find your best match faster.'),
      heading: translate('Your Best Friends ✨'),
      text: translate('People who effortlessly understand and appreciate you for who you naturally are.'),
    },
  };

  const data = hasDating ? dataMap.dating : hasFriends ? dataMap.friends : null;

  if (!data) return '';

  return `- Tagline: ${data.tagline}
- Heading: ${data.heading}
- Text: ${data.text}`;
};

module.exports = {
  prepareProfileDetails,
  buildExampleBlock,
};
