const Category = require('../models/category');
const Profile = require('../models/profile');
const Subcategory = require('../models/subcategory');
const PersonalityDatabaseChangeTracker = require('../models/personality-database-change-tracker');

async function getPersonalityDatabaseMaxIds() {
  const [maxCategory, maxSubcategory, maxProfile] = await Promise.all([
    Category.findOne({}, { id: 1 }).sort({ id: -1 }).lean(),
    Subcategory.findOne({}, { id: 1 }).sort({ id: -1 }).lean(),
    Profile.findOne({}, { id: 1 }).sort({ id: -1 }).lean()
  ]);

  return {
    category: maxCategory?.id ?? 0,
    subcategory: maxSubcategory?.id ?? 0,
    profile: maxProfile?.id ?? 0,
  };
}


async function checkPersonalityDatabaseChanges() {
  const currentMaxIds = await getPersonalityDatabaseMaxIds();

  const previousDoc = await PersonalityDatabaseChangeTracker.findOneAndUpdate(
    { _id: 'singleton' },
    {
      $setOnInsert: { createdAt: new Date() },
      $max: {
        highestCategoryId: currentMaxIds.category,
        highestSubcategoryId: currentMaxIds.subcategory,
        highestProfileId: currentMaxIds.profile,
      },
    },
    {
      upsert: true,
      new: false,
      lean: true,
    }
  );

  const prevValues = {
    highestCategoryId: previousDoc?.highestCategoryId ?? 0,
    highestSubcategoryId: previousDoc?.highestSubcategoryId ?? 0,
    highestProfileId: previousDoc?.highestProfileId ?? 0,
  };

  const changes = {
    categories: currentMaxIds.category > prevValues.highestCategoryId,
    subcategories: currentMaxIds.subcategory > prevValues.highestSubcategoryId,
    profiles: currentMaxIds.profile > prevValues.highestProfileId,
  };
  changes.hasAnyChanges = changes.categories || changes.subcategories || changes.profiles;

  if (changes.hasAnyChanges) {
    await PersonalityDatabaseChangeTracker.updateOne(
      { 
        _id: 'singleton' 
      },
      { 
        $set: { lastUpdateAt: new Date() }
      }
    );
  }

  return changes;
}

module.exports = {
  checkPersonalityDatabaseChanges,
};
