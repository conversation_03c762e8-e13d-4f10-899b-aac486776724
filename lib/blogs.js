const Blogs = require('../models/blogs');
const LocationBlogs = require('../models/locationBlogs');
const PersonalityBlogs = require('../models/personalityBlogs');

async function createBlogsSitemaps() {
  let resourceUrls = []
  let pillarUrls = []

  console.log("LOADING BLOGS");

  const blogCursor = Blogs.find({}, { _id: 0, url: 1, previews: 1, lastUpdated: 1 }).lean().cursor(); 
  for await (const blog of blogCursor) {
    if (blog.previews) {
      for (const [language, preview] of Object.entries(blog.previews)) {
        if (!blog.url.includes('&')) {
          const page = {
            url: `https://boo.world${language === "en" ? "" : "/" + language}${blog.url}`,
          };
          if (preview.images) {
            page.image = preview.images.map(image => `https://boo-media.b-cdn.net/blogs/${image.replace(/^\/+/, '')}`);
          }else if (preview.image) {
            const imageUrl = preview.image.replace(/^\/+/, '');
            page.image = `https://boo-media.b-cdn.net/blogs/${imageUrl}`;
          }
          if(blog.lastUpdated) {
            page.lastModified = blog.lastUpdated
          }
          if (blog.url.includes('resources')) {
            resourceUrls.push(page);
          } else {
            pillarUrls.push(page);
          }
        }
      }
    }
  }

  console.log("LOADING PERSONALITY BLOGS");

  const personalityBlogCursor = PersonalityBlogs.find({}, { _id: 0, url: 1, previews: 1, lastUpdated: 1 }).lean().cursor();
  for await (const blog of personalityBlogCursor) {
    if (blog.previews) {
      for (const [language, preview] of Object.entries(blog.previews)) {
        if (!blog.url.includes('&')) {
          const page = {
            url: `https://boo.world${language === "en" ? "" : "/" + language}${blog.url}`,
          };
          if (preview.images) {
            page.image = preview.images.map(image => `https://boo.world/personalities/blogs/${image.replace(/^\/+/, '')}`);
          }else if (preview.image) {
            const imageUrl = preview.image.replace(/^\/+/, '');
            page.image = `https://boo.world/personalities/blogs/${imageUrl}`;
          }
          if(blog.lastUpdated) {
            page.lastModified = blog.lastUpdated
          }
          if (blog.url.includes('resources')) {
            resourceUrls.push(page);
          } else {
            pillarUrls.push(page);
          }
        }
      }
    }
  }

  console.log("LOADING LOCATION BLOGS");

  const locationBlogCursor = LocationBlogs.find({}, { _id: 0, url: 1, articleType: 1, country: 1, region: 1, previews: 1, lastUpdated: 1 }).lean().cursor();
  for await (const blog of locationBlogCursor) {
    if (blog.previews) {
      for (const [language, preview] of Object.entries(blog.previews)) {
        if (!blog.url.includes('&')) {
          const page = {
            url: `https://boo.world${language === "en" ? "" : "/" + language}${blog.url}`,
          };
          if(blog.lastUpdated) {
            page.lastModified = blog.lastUpdated
          }
          if (blog.url.includes('resources')) {
            resourceUrls.push(page);
          } else {
            pillarUrls.push(page);
          }
        }
      }
    }
  }
  return { resourceUrls, pillarUrls };
}

module.exports = { createBlogsSitemaps };
