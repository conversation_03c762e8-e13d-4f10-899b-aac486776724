const { expect } = require('chai');
const sinon = require('sinon');
const axios = require('axios');
const AppsFlyerClient = require('./AppsFlyerClient'); // Adjust path as needed

describe('AppsFlyerClient', () => {
  let axiosPostStub;
  const originalAPIKey = AppsFlyerClient.APIKey;
  const originalAndroidAppID = AppsFlyerClient.androidAppID;
  const originalIOSAppID = AppsFlyerClient.iOSAppID;

  before(() => {
    axiosPostStub = sinon.stub(axios, 'post');
  });

  beforeEach(() => {
    // Reset static properties before each test
    AppsFlyerClient.APIKey = 'test-api-key';
    AppsFlyerClient.androidAppID = 'android-test-id';
    AppsFlyerClient.iOSAppID = 'ios-test-id';
  });

  afterEach(() => {
    axiosPostStub.reset();
  });

  after(() => {
    sinon.restore();
    // Restore original values
    AppsFlyerClient.APIKey = originalAPIKey;
    AppsFlyerClient.androidAppID = originalAndroidAppID;
    AppsFlyerClient.iOSAppID = originalIOSAppID;
  });

  describe('sendEvent', () => {
    const baseParams = {
      appsflyerId: 'test-id',
      eventName: 'test-event',
      eventValue: { revenue: 9.99 }
    };

    it('should throw error when API key is not configured', async () => {
      AppsFlyerClient.APIKey = null;
      
      try {
        await AppsFlyerClient.sendEvent('ios', baseParams);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Missing AppsFlyer config. Set APPSFLYER_APP_ID and APPSFLYER_DEV_KEY in .env');
      }
    });

    it('should throw error when required params are missing', async () => {
      try {
        await AppsFlyerClient.sendEvent('ios', {});
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('appsflyerId, eventName and eventValue is required');
      }
    });

    it('should accept eventValue as an object', async () => {
      const params = {
        appsflyerId: 'test-id',
        eventName: 'purchase',
        eventValue: { revenue: 9.99, currency: 'USD' }
      };
      
      axiosPostStub.resolves({ data: { success: true } });
      
      await AppsFlyerClient.sendEvent('ios', params);
      
      expect(axiosPostStub.calledOnce).to.be.true;
      const [, payload] = axiosPostStub.firstCall.args;
      expect(payload.eventValue).to.deep.equal(params.eventValue);
    });

    it('should use iOS app ID for ios platform', async () => {
      axiosPostStub.resolves({ data: { success: true } });
      
      await AppsFlyerClient.sendEvent('ios', baseParams);
      
      expect(axiosPostStub.calledOnce).to.be.true;
      const [url] = axiosPostStub.firstCall.args;
      expect(url).to.equal(`https://api3.appsflyer.com/inappevent/${AppsFlyerClient.iOSAppID}`);
    });

    it('should use Android app ID for android platform', async () => {
      axiosPostStub.resolves({ data: { success: true } });
      
      await AppsFlyerClient.sendEvent('android', baseParams);
      
      expect(axiosPostStub.calledOnce).to.be.true;
      const [url] = axiosPostStub.firstCall.args;
      expect(url).to.equal(`https://api3.appsflyer.com/inappevent/${AppsFlyerClient.androidAppID}`);
    });

    it('should be case insensitive for platform parameter', async () => {
      axiosPostStub.resolves({ data: { success: true } });
      
      await AppsFlyerClient.sendEvent('IOS', baseParams);
      
      expect(axiosPostStub.calledOnce).to.be.true;
      const [url] = axiosPostStub.firstCall.args;
      expect(url).to.include(AppsFlyerClient.iOSAppID);
    });

    it('should include default timestamp if not provided', async () => {
      axiosPostStub.resolves({ data: { success: true } });
      
      await AppsFlyerClient.sendEvent('ios', baseParams);
      
      expect(axiosPostStub.calledOnce).to.be.true;
      const [, payload] = axiosPostStub.firstCall.args;
      expect(payload).to.have.property('eventTime');
      expect(new Date(payload.eventTime).toString()).to.not.equal('Invalid Date');
    });

    it('should use provided timestamp if available', async () => {
      const testTime = '2023-01-01T00:00:00.000Z';
      const params = {
        ...baseParams,
        eventTime: testTime
      };
      
      axiosPostStub.resolves({ data: { success: true } });
      
      await AppsFlyerClient.sendEvent('ios', params);
      
      expect(axiosPostStub.calledOnce).to.be.true;
      const [, payload] = axiosPostStub.firstCall.args;
      expect(payload.eventTime).to.equal(testTime);
    });

    it('should include all optional parameters in payload', async () => {
      const params = {
        ...baseParams,
        customerUserId: 'user123',
        advertisingId: 'ad-id-123',
        idfa: 'idfa-123',
        idfv: 'idfv-123',
        customData: { campaign: 'summer_sale' }
      };
      
      axiosPostStub.resolves({ data: { success: true } });
      
      await AppsFlyerClient.sendEvent('ios', params);
      
      expect(axiosPostStub.calledOnce).to.be.true;
      const [, payload] = axiosPostStub.firstCall.args;
      expect(payload).to.deep.include(params);
    });

    it('should include correct headers in the request', async () => {
      axiosPostStub.resolves({ data: { success: true } });
      
      await AppsFlyerClient.sendEvent('ios', baseParams);
      
      expect(axiosPostStub.calledOnce).to.be.true;
      const [, , { headers }] = axiosPostStub.firstCall.args;
      expect(headers).to.deep.equal({
        'Authentication': AppsFlyerClient.APIKey,
        'Content-Type': 'application/json'
      });
    });

    it('should return the response data on success', async () => {
      const testResponse = { success: true, data: { id: '123' } };
      axiosPostStub.resolves({ data: testResponse });
      
      const result = await AppsFlyerClient.sendEvent('ios', baseParams);
      expect(result).to.deep.equal(testResponse);
    });

    it('should throw error and log when API call fails', async () => {
      const testError = {
        response: {
          data: { error: 'Invalid API key' }
        },
        message: 'Request failed'
      };
      
      axiosPostStub.rejects(testError);
      
      try {
        await AppsFlyerClient.sendEvent('ios', baseParams);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.equal(testError);
      }
    });

    it('should handle network errors without response', async () => {
      const testError = new Error('Network error');
      axiosPostStub.rejects(testError);
      
      try {
        await AppsFlyerClient.sendEvent('ios', baseParams);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Network error');
      }
    });
  });

  describe('getBaseUrl', () => {
    it('should return the correct base URL', () => {
      expect(AppsFlyerClient.getBaseUrl()).to.equal('https://api3.appsflyer.com/inappevent');
    });
  });
});