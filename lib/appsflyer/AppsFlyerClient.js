const axios = require('axios');

class AppsFlyerClient {
  static APIKey = process.env.APPSFLYER_S2S_TOKEN;
  static androidAppID = 'enterprises.dating.boo'
  static iOSAppID = 'id1498407272'

  static getBaseUrl() {
    return 'https://api3.appsflyer.com/inappevent'
  }

  /**
   * Send an event to AppsFlyer with all possible fields.
   * @param {Object} params - Event parameters.
   * @param {string} [params.appsflyerId] - Required if no other ID is provided.
   * @param {string} [params.eventName] - Event name (e.g., 'purchase').
   * @param {Object} [params.eventValue] - Custom event data (e.g., { revenue: 9.99 }).
   * @param {string} [params.eventTime] - ISO 8601 timestamp (default: current time).
   * @param {string} [params.customerUserId] - For web events.
   * @param {string} [params.advertisingId] - Android GAID.
   * @param {string} [params.idfa] - iOS IDFA.
   * @param {string} [params.idfv] - iOS IDFV.
   * @param {Object} [params.customData] - Additional custom fields.
   */
  static async sendEvent(os,params = {}) {
    if (!this.APIKey) {
      throw new Error('Missing AppsFlyer config. Set APPSFLYER_APP_ID and APPSFLYER_DEV_KEY in .env');
    }

    let appId
    if(os.toLowerCase() === 'android'){
        appId = this.androidAppID
    }else{
        appId = this.iOSAppID
    }

    // Validate at least one identifier is present
    const { appsflyerId, eventName, eventValue} = params;
    if (!appsflyerId &&  !eventName && !eventValue) {
      throw new Error('appsflyerId, eventName and eventValue is required');
    }

    const url = `${this.getBaseUrl()}/${appId}`;
    const headers = {
      'Authentication': this.APIKey,
      'Content-Type': 'application/json',
    };

    // Build payload with all valid fields
    const payload = {
      ...params, // Spread all provided fields
      eventTime: params.eventTime || new Date().toISOString(), // Default timestamp
    };

    try {
      const response = await axios.post(url, payload, { headers });
      return response.data;
    } catch (error) {
      console.error('AppsFlyer S2S Error:', error.response?.data || error.message);
      throw error;
    }
  }
}

module.exports = AppsFlyerClient;
