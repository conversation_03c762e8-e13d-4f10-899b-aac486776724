const ScammerCleanup = require('../models/scammer-cleanup');

function getRandomInt(max) {
  // return random int between 1 and max, inclusive
  return 1 + Math.floor(Math.random() * max);
}

function getQueueNumbers() {
  const first = getRandomInt(7);
  let second = first;
  while (second == first) {
    second = getRandomInt(7);
  }
  return [ first, second ];
}

async function insertScammerCleanup(user) {
  const doc = await ScammerCleanup.findOne({ user: user._id }).sort('-createdAt');
  if (doc) {
    if (!doc.review1.decision && !doc.review2.decision) {
      // existing record has not been reviewed yet, can reuse it
      return;
    }
    else if (doc.review1.decision && !doc.review2.decision) {
      // existing record partially reviewed, cancel it then create a new record
      doc.review2.decision = 'cancelled';
      await doc.save();
    }
    else if (!doc.review1.decision && doc.review2.decision) {
      // existing record partially reviewed, cancel it then create a new record
      doc.review1.decision = 'cancelled';
      await doc.save();
    }
  }

  const queueNumbers = getQueueNumbers();
  await ScammerCleanup.create({
    user: user._id,
    review1: {
      queue: queueNumbers[0],
    },
    review2: {
      queue: queueNumbers[1],
    },
  });
}

module.exports = {
  insertScammerCleanup,
}
