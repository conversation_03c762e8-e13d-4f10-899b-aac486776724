const { WEB_DOMAIN } = require('./constants');
const { locales } = require('./translate');

function urlify(s) {
  // Trim string
  s = s.trim();

  // only unicode letters and numbers allowed
  s = s.replace(/[^\p{L}\p{N}\s-]/gu, '');

  // replace all runs of whitespace with a single dash
  s = s.replace(/\s+/g, '-');

  // replace all runs of dash with a single dash
  s = s.replace(/-+/g, '-');

  // convert to lowercase
  return s.toLowerCase();
}

const maxSlugLength = 50;
function createSlug(s) {
  if (!s) {
    return '';
  }
  s = urlify(s);
  if (s.length < maxSlugLength) {
    return s;
  }
  let index = maxSlugLength;
  for (let i = 0; i < maxSlugLength; i++) {
    if (s[i] === '-') {
      index = i;
    }
  }
  return s.substring(0, index);
}

function generateInterestUrl(interestName, language) {
  interestName = encodeURIComponent(interestName);
  let url = `${WEB_DOMAIN}/u/${interestName}`;
  if (language && language != 'en') {
    if (language == 'iw') language = 'he'
    if (language == 'zh') language = 'zh-Hans'
    if(locales.includes(language)) url = `${WEB_DOMAIN}/${language}/u/${interestName}`;
  }
  return url;
}

function generateQuestionUrl(question, includeLanguage) {
  let slug = createSlug(question.title);
  if (slug.length < 20) {
    slug = createSlug(`${slug} ${question.text || ''}`);
  }
  let language = includeLanguage ? question.language : undefined;
  let url = `${generateInterestUrl(question.interestName, language)}/${question.webId}/${encodeURIComponent(slug)}`;
  // Remove trailing slashes
  url = url.replace(/\/+$/, '');
  return url;
}

function createProfileUrl(profile) {
  return `/database/profile/${profile.id}/${createSlug(profile.name)}-personality-type`;
}

module.exports = {
  createSlug,
  generateQuestionUrl,
  createProfileUrl,
};
