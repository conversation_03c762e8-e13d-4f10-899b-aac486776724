{"2048": "2048", "mbti": "mbti", "enneagram": "enneagramma", "astrology": "astrologiya", "cognitivefunctions": "kogitivfunksiyalar", "psychology": "psixologiya", "philosophy": "falsafa", "history": "tarix", "physics": "fizika", "science": "ilm", "culture": "madani<PERSON><PERSON>", "languages": "tillar", "technology": "texnologiya", "memes": "memlar", "mbtimemes": "mbtimem<PERSON>", "astrologymemes": "astrologiyamemlar", "enneagrammemes": "enneagrammemelar", "showerthoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "kulgili", "videos": "videolar", "gadgets": "g<PERSON><PERSON><PERSON>", "politics": "siyosat", "relationshipadvice": "munosa<PERSON><PERSON><PERSON><PERSON>sla<PERSON>", "lifeadvice": "<PERSON><PERSON>iymaslahat", "crypto": "kripto", "news": "ya<PERSON><PERSON><PERSON><PERSON>", "worldnews": "dunyoyangiliklaridanxabar", "archaeology": "arxeologi<PERSON>", "learning": "organish", "debates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "konspirasiyalar", "universe": "koinot", "meditation": "meditatsiya", "mythology": "mifologiya", "art": "sanat", "crafts": "hunarmandchilik", "dance": "raqs", "design": "<PERSON><PERSON><PERSON>", "makeup": "ma<PERSON><PERSON><PERSON>", "beauty": "gozallik", "fashion": "moda", "singing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "writing": "yozish", "photography": "fotografiya", "cosplay": "kosplay", "painting": "rassomchilik", "drawing": "chizmachilik", "books": "kitoblar", "movies": "kinolar", "poetry": "<PERSON><PERSON><PERSON>", "television": "televizor", "filmmaking": "<PERSON><PERSON><PERSON><PERSON>", "animation": "animatsiya", "anime": "anime", "scifi": "fantastika", "fantasy": "<PERSON><PERSON><PERSON><PERSON>", "documentaries": "<PERSON>u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mystery": "sirli", "comedy": "komediya", "crime": "ji<PERSON>at", "drama": "drama", "bollywood": "boll<PERSON>ud", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "qor<PERSON><PERSON><PERSON>", "romance": "romantika", "realitytv": "realitytv", "action": "harakat", "music": "musiqa", "blues": "bluz", "classical": "klassik", "country": "ma<PERSON><PERSON><PERSON>", "desi": "desi", "edm": "edm", "electronic": "elektronika", "folk": "xalq", "funk": "fank", "hiphop": "xipxop", "house": "uy", "indie": "indi", "jazz": "jazz", "kpop": "kpop", "latin": "lotin", "metal": "metal", "pop": "pop", "punk": "pank", "rnb": "rnb", "rap": "rap", "reggae": "reggi", "rock": "rok", "techno": "texno", "travel": "sayohat", "concerts": "konsertlar", "festivals": "festivallar", "museums": "muze<PERSON><PERSON>", "standup": "stendap", "theater": "teatr", "outdoors": "ochiqhavoda", "gardening": "bogdorchilik", "partying": "tusoy<PERSON>", "gaming": "gaming", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "zindonlarvaajdarlar", "chess": "shax<PERSON>", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "ovqat", "baking": "nonpishirish", "cooking": "oshpazlik", "vegetarian": "vegetarian", "vegan": "vegan", "birds": "qushlar", "cats": "mush<PERSON><PERSON>", "dogs": "itlar", "fish": "baliq", "animals": "<PERSON><PERSON><PERSON>", "blacklivesmatter": "qora<PERSON><PERSON><PERSON><PERSON><PERSON>", "environmentalism": "ekologizm", "feminism": "feminizm", "humanrights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "lgbtqittifoqchi", "stopasianhate": "osiyoliklarganafratnitoxtat", "transally": "transittifoqchi", "volunteering": "koʻngillilik", "sports": "sport", "badminton": "badminton", "baseball": "beysbol", "basketball": "basketbol", "boxing": "boks", "cricket": "kriket", "cycling": "velosiped", "fitness": "fitnes", "football": "futbol", "golf": "golf", "gym": "jym<PERSON>", "gymnastics": "gimnast<PERSON>", "hockey": "xokkey", "martialarts": "<PERSON><PERSON><PERSON><PERSON>", "netball": "netbol", "pilates": "pilates", "pingpong": "sto<PERSON><PERSON>", "running": "yugurish", "skateboarding": "skeytbording", "skiing": "changi", "snowboarding": "snoub<PERSON>", "surfing": "serfing", "swimming": "su<PERSON>sh", "tennis": "tennis", "volleyball": "voleybol", "weightlifting": "ogirathlet<PERSON>", "yoga": "yoga", "scubadiving": "suvos<PERSON><PERSON>ish", "hiking": "sayr", "capricorn": "qovgoq", "aquarius": "suv_soqchi", "pisces": "baliq", "aries": "qoy", "taurus": "buzoq", "gemini": "gemini", "cancer": "saraton", "leo": "leo", "virgo": "bokira", "libra": "ta<PERSON>zi", "scorpio": "skorpion", "sagittarius": "oqotar", "shortterm": "q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "oddiy", "longtermrelationship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "single": "yolgiz", "polyamory": "k<PERSON>evgic<PERSON><PERSON>", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gey", "lesbian": "<PERSON><PERSON><PERSON><PERSON>", "bisexual": "biseksual", "pansexual": "<PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "itboqar", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "qirolqidirish", "soulreaver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "<PERSON><PERSON>", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "quyosh<PERSON><PERSON><PERSON>_zakatida", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "ochiqolam", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "ruhga_oxshash", "dungeoncrawling": "<PERSON><PERSON><PERSON>ish", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribesofmidgard", "planescape": "samolyotmanzar<PERSON>", "lordsoftherealm2": "xalqaroyhukmdorlari2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "rang<PERSON><PERSON>", "medabots": "medabotlar", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersivsimulyatorlar", "okage": "okage", "juegoderol": "rolli_oyini", "witcher": "vitcher", "dishonored": "<PERSON><PERSON><PERSON>a", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modlash", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "immersiv", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyes<PERSON>ak<PERSON><PERSON>", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "qorongumotivatsiya", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "ishqsuvoq", "otomegames": "otome_oʻyinlar", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampiremaskarad", "dimension20": "dimension20", "gaslands": "choll<PERSON><PERSON>", "pathfinder": "yo<PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2nchieditioni", "shadowrun": "shadowrun", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravityrush", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "bittaurinlik", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "<PERSON><PERSON><PERSON><PERSON>", "yourturntodie": "<PERSON>nn<PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "qorqinchlirpg", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "maroder", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgmatn", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "bombaboshpana", "gurps": "gurps", "darkestdungeon": "qorongudungeon", "eclipsephase": "tutilishfazasi", "disgaea": "disgaea", "outerworlds": "ta<PERSON><PERSON><PERSON><PERSON>o", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dinastiyajangovchilari", "skullgirls": "skullgirls", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "hog<PERSON><PERSON><PERSON><PERSON>", "madnesscombat": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "yol96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelike_oyinlar", "gothamknights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "unutilgandunyolar", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childoflight": "yoruglikbolasi", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "monsterfer<PERSON><PERSON>", "ecopunk": "ekopank", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "urish", "lastepoch": "oxirizamonga", "starfinder": "yulduzqidirgich", "goldensun": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "qorongudaqilichlar", "twilight2000": "girashira2000", "sandevistan": "sandevistan", "cyberpunk": "kiberpank", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "kiberpankqizil", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "tushibketganbuйruq", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "qadi<PERSON>yr<PERSON><PERSON><PERSON>", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "eskidunyo_qay<PERSON>lari", "adventurequest": "sarg<PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "rolevazifa<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "quyoshbog", "talesofsymphonia": "talesofsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "myfarog", "sacredunderworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chainedechoes": "zanjirlangangoslar", "darksoul": "qoro<PERSON><PERSON><PERSON><PERSON>", "soulslikes": "ruhga_oxshash_oyinlar", "othercide": "bosh<PERSON>tomon", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "xronotrigger", "pillarsofeternity": "<PERSON><PERSON><PERSON><PERSON><PERSON>lar<PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "riftlar", "tibia": "tibia", "thedivision": "thedivision", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "legendofdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirningqobogi", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "b<PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "kichikyogoch", "childrenofmorta": "mort<PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "motorqalb", "fable3": "fable3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON>z<PERSON>", "hiveswap": "hiveswap", "rollenspiel": "roly<PERSON><PERSON><PERSON>n", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "yulduzlarmaydonı", "oldschoolrevival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nyolar", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "rp<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "hosil", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "arcadiakos<PERSON>", "shadowhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnoziya", "pennyblood": "pennyblood", "breathoffire4": "nafasolish4", "mother3": "ona3", "cyberpunk2020": "kiberpank2020", "falloutbos": "falloutbos", "anothereden": "b<PERSON><PERSON><PERSON>", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "rolo<PERSON>yin", "fabulaultima": "ajoyibultima", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "ha<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "jang<PERSON>", "spelljammer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "xron<PERSON><PERSON><PERSON><PERSON><PERSON>", "cocttrpg": "kaktusrpg", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monstrlar<PERSON><PERSON><PERSON>", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "shadowheartscovenant", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "qiyomatkunigacha", "awplanet": "awplanet", "theworldendswithyou": "dunyoseningbilantugar", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "qorong<PERSON>es", "shoptitans": "shoptitanlar", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON>", "blackbook": "qoraqalamcha", "skychildrenoflight": "osmon<PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "castlecrashers": "qal<PERSON><PERSON><PERSON><PERSON>ar", "gothicgame": "gotikgame", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gamingrpg", "prophunt": "prov<PERSON><PERSON><PERSON>", "starrails": "y<PERSON><PERSON><PERSON>relslar", "cityofmist": "tumanlikshahar", "indierpg": "indierpg", "pointandclick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON>", "freeside": "be<PERSON><PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathroadtocanada": "kana<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladium": "palladium", "knightjdr": "ritsar<PERSON><PERSON>", "monsterhunter": "monsterhunter", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "g<PERSON><PERSON><PERSON>", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothexak", "ys": "ys", "souleater": "ruh<PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "ikkilikbojinoyinlar", "tacticalrpg": "taktikrpg", "mahoyo": "mahoyo", "animegames": "animeoʻyinlari", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "x<PERSON>x<PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "a<PERSON><PERSON><PERSON><PERSON>", "princessconnect": "princessconnect", "hexenzirkel": "hexenzirkel", "cristales": "krist<PERSON><PERSON>", "vcs": "vench<PERSON><PERSON><PERSON><PERSON><PERSON>", "pes": "pes", "pocketsage": "chontakdono", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valo<PERSON><PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efutbol", "nba2k": "nba2k", "egames": "e_oyinlar", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "orzularligasi", "fifa14": "fifa14", "midlaner": "<PERSON><PERSON><PERSON>", "efootball": "efutbol", "dreamhack": "dreamhack", "gaimin": "g<PERSON>merlik", "overwatchleague": "overwatchligasi", "cybersport": "kibersport", "crazyraccoon": "aqlbovargandayon", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "oʻ<PERSON><PERSON><PERSON>_tashlash", "brasilgameshow": "brasilgeymshow", "valorantcompetitive": "valorantraqobat", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "yarimolik", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "valve", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "abadiyozx", "goatsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "battlefield4", "nightinthewoods": "tung<PERSON><PERSON>a", "halflife2": "halflife2", "hacknslash": "hackvaslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanialar", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helltaker": "helltaker", "inscryption": "inskriptsiya", "7d2d": "7k2k", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "okop", "stray": "<PERSON><PERSON><PERSON>", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "qosh", "blackdesert": "qoraqum", "tabletopsimulator": "tabletopsimulator", "partyhard": "partytushamaguncha", "hardspaceshipbreaker": "qattiqkosmikkemasindirgich", "hades": "gades", "gunsmith": "qurolusta", "okami": "<PERSON>ami", "trappedwithjester": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinkum": "rostgap", "predecessor": "oldingi", "rainworld": "yom<PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolo<PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "urushninton<PERSON>", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "qoronguvayanadaqorongu", "motox": "motox", "blackmesa": "qoramesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "kubdanqochish", "hifirush": "salom_yug<PERSON>k", "svencoop": "svencoop", "newcity": "ya<PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "s<PERSON><PERSON><PERSON>haftlari", "defconheavy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsiya", "virtualkenopsia": "virtualkenopsiya", "snowrunner": "qor<PERSON><PERSON><PERSON>", "libraryofruina": "kutubxonaviron", "l4d2": "l4d2", "thenonarygames": "nonbinaryoyinlar", "omegastrikers": "omegastrikers", "wayfinder": "yolkorsatu<PERSON><PERSON>", "kenabridgeofspirits": "kenaspiritsbridge", "placidplasticduck": "osoyishtaplastikordak", "battlebit": "jang<PERSON><PERSON><PERSON>", "ultimatechickenhorse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dialtown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "meninguchuntabassum", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supergoshliprikboy", "tinnybunny": "kichkinaquyoncha", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "halokat", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "apeks", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paladins": "paladinlar", "earthdefenseforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "huntshowdown", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "urushlar", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "insurgencysandstorm", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "<PERSON><PERSON><PERSON><PERSON>", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "bolimlar2", "killzone": "olimzonasi", "helghan": "hel<PERSON>", "coldwarzombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "eyscombat", "crosscode": "krosdastur", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "snayperchempion", "modernwarfare": "modernurush", "neonabyss": "neontuqunlik", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "chegara<PERSON><PERSON><PERSON>", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "tarkovdanqochish", "metalslug": "metalslug", "primalcarnage": "i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofwarships": "worldofwarships", "back4blood": "qonuchunqa<PERSON>ish", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "qotil", "masseffect": "masseffect", "systemshock": "t<PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doometernal": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantigr", "generationzero": "avlodlarxaqida", "enterthegungeon": "qurolotozgaoʻt", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "sosiskachovvoq", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "fantomogriq", "warface": "warface", "crossfire": "krossfar", "atomicheart": "atomicyurak", "blackops3": "blackops3", "vampiresurvivors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybattleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "<PERSON><PERSON><PERSON>", "battlegrounds": "j<PERSON><PERSON><PERSON><PERSON>", "frag": "frag", "tinytina": "kich<PERSON><PERSON>na", "gamepubg": "gamepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "fps<PERSON><PERSON><PERSON><PERSON>", "convertstrike": "converturdirish", "warzone2": "warzone2", "shatterline": "shatter<PERSON><PERSON>sh", "blackopszombies": "blackopszombies", "bloodymess": "qonlitartibsizlik", "republiccommando": "respublikakommandosi", "elitedangerous": "elitexavfli", "soldat": "soldat", "groundbranch": "yerdagifilial", "squad": "<PERSON><PERSON>", "destiny1": "taqdir1", "gamingfps": "geymingfps", "redfall": "redfall", "pubggirl": "pubgqiz", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON><PERSON>", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "oylikolishkuni2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgomaniya", "empyrion": "empyrion", "pubgczech": "pubg<PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "sovunbaliq", "ghostcod": "arvohcod", "csplay": "cosplay", "unrealtournament": "anrealchempionat", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechempionlar<PERSON>", "halo3": "halo3", "halo": "salom", "killingfloor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonoq", "remnant": "qoldiq", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "qay<PERSON>h", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "soyaodam", "quake2": "quake2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harakat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "dengiz<PERSON><PERSON>q<PERSON><PERSON>", "rust": "rust", "conqueronline": "onlaynzabtqilish", "dauntless": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warships": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "sirli", "phantasystaronline2": "phantasystaronline2", "maidenless": "yolgiz", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "worldoftanks", "crossout": "kesi<PERSON><PERSON><PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "ikkin<PERSON><PERSON>ot", "aion": "aion", "toweroffantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netplay": "netplay", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superhayvonqirol", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubpingvin", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON>", "newworld": "yang<PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "kop<PERSON><PERSON><PERSON>", "pirate101": "qaroqchi101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsjangmaydoni2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponitown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonlayn", "mabinogi": "mabinogi", "ashesofcreation": "ashesofcreationgame", "riotmmo": "riotmmo", "silkroad": "ipakyoli", "spiralknights": "spiralknights", "mulegend": "afsona", "startrekonline": "startrekonlayn", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "koʻpoyinchi", "angelsonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarseskirespublika", "grandfantasia": "ulkanfantaziya", "blueprotocol": "blueprotocol", "perfectworld": "muka<PERSON>olam", "riseonline": "onlayntikil", "corepunk": "corepunk", "adventurequestworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flyforfun": "u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "kucha_jang<PERSON>si", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "s<PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtualjangchi", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "thekingoffighters", "likeadragon": "ajdahodek", "retrofightinggames": "retro<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blasphemous": "kufr", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "superzarba", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "kiberrobotlar", "armoredwarriors": "zirhlijangarchilar", "finalfight": "oxirgijang", "poweredgear": "quvvatlanganus<PERSON>na", "beatemup": "kaltaklash", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "qotilinstinkt", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "shivalri2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightdavomi", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksongoyini", "silksongnews": "silksongnews", "silksong": "ipakqoshiq", "undernight": "tunkech<PERSON>", "typelumina": "yo<PERSON><PERSON><PERSON><PERSON>", "evolutiontournament": "evolyutsiyaturniri", "evomoment": "evooniyat", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "bloodborne", "horizon": "ufq", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodborne", "uncharted": "nomalum", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "mashhur", "playstationbuddies": "playstationdustlari", "ps1": "ps1", "oddworld": "galatdunyo", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "xazina", "detroitbecomehuman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "tongotguncha", "touristtrophy": "turistkubogi", "lspdfr": "lspdfr", "shadowofthecolossus": "ulkanti<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "crashteamracing", "fivepd": "beshpd", "tekken7": "tekken7", "devilmaycry": "shay<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraiwarriors": "samuraijanggichilari", "psvr2": "psvr2", "thelastguardian": "oxi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "qilichruh", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "erkaklarovchisi", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "partihayvon", "warharmmer40k": "warhammer40k", "fightnightchampion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "psixonavtlar", "mhw": "mhw", "princeofpersia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "j<PERSON><PERSON><PERSON>", "dontstarvetogether": "dontstarvebirgalikda", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>rab", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "uyotkazuvchi", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseriyasi", "r6xbox": "r6xbox", "leagueofkingdoms": "qirolliklarligasi", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skycotl": "skycotl", "erica": "erika", "ancestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cuphead": "<PERSON><PERSON><PERSON><PERSON>", "littlemisfortune": "kichkinaqizaloqbadbaxtlik", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterprom", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "mototsikllar", "outerwilds": "kosmiklandshaftlar", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duckgame": "<PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "sten<PERSON><PERSON><PERSON>", "towerunite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "occulto": "maxfiy", "longdrive": "uzoqhaydash", "satisfactory": "qoniqarli", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "<PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalkosmikdasturi", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "qorong<PERSON><PERSON>", "pizzatower": "pizzatower", "indiegame": "indie<PERSON>in<PERSON>", "itchio": "itchio", "golfit": "<PERSON><PERSON><PERSON>", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "oyin", "rockpaperscissors": "toshqaychiiqogoz", "trampoline": "trampolin", "hulahoop": "h<PERSON><PERSON><PERSON><PERSON>", "dare": "jurat", "scavengerhunt": "qidiru<PERSON>in", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "r<PERSON><PERSON><PERSON><PERSON>", "beerpong": "pivopong", "dicegoblin": "zargavsar", "cosygames": "qula<PERSON><PERSON><PERSON><PERSON>", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "bepulgame", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sudoku", "juegos": "oyin<PERSON>", "mahjong": "mahjong", "jeux": "oyin<PERSON>", "simulationgames": "simulyats<PERSON>oyinlar<PERSON>", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "z<PERSON>karlioyinlar", "oyun": "oyun", "interactivegames": "interaktivoyinlar", "amtgard": "amtgard", "staringcontests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "spiele", "giochi": "oyin<PERSON>", "geoguessr": "geoguessr", "iphonegames": "iphoneoʻyinlari", "boogames": "boo_oyin<PERSON>i", "cranegame": "kranmashinasi", "hideandseek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hopscotch": "klassiki", "arcadegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "klassikoyun", "mindgames": "on<PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "teksnitop", "galagames": "galagames", "romancegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4xgames": "4xoyinlar", "gamefi": "gamefi", "jeuxdarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "oyinlar90", "idareyou": "senju<PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "haqiqiyvosoxta", "playgames": "oʻ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "onlaynoy<PERSON>lar", "onlinegames": "onlaynoy<PERSON>lar", "jogosonline": "onlaynoy<PERSON>lar", "writtenroleplay": "yo<PERSON><PERSON><PERSON><PERSON>", "playaballgame": "topoyinihamqilamiz", "pictionary": "rasmtopish", "coopgames": "koop<PERSON>inlar", "jenga": "jenga", "wiigames": "wii<PERSON><PERSON><PERSON>i", "highscore": "yuqorinatiija", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "burgeroʻyinlari", "kidsgames": "bola<PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwqoraversiya", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "savolja<PERSON><PERSON><PERSON>", "gioco": "oyin", "managementgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "ya<PERSON><PERSON><PERSON><PERSON>", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formula1oyini", "citybuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drdriving": "drdriving", "juegosarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "memorygames": "xotira<PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "<PERSON>ks<PERSON><PERSON>inlar", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "oyino", "lasergame": "lazeroyin", "imessagegames": "imess<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "boshjoynitoldir", "jeuxpc": "jeuxpc", "rétrogaming": "retrogeyming", "logicgames": "man<PERSON>qi<PERSON><PERSON><PERSON><PERSON><PERSON>", "japangame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "subwaysurf": "metroserfing", "jeuxdecelebrite": "jeuxdecelebrite", "exitgames": "chiq<PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5ga5", "rolgame": "roloyin", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "fpsoyin", "textbasedgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantakalcho", "retrospel": "retrospel", "thiefgame": "ogril<PERSON>_oyini", "lawngames": "may<PERSON><PERSON>_oyin<PERSON>i", "fliperama": "fliperama", "heroclix": "xerokliks", "tablesoccer": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "tischfußball": "stolfutboli", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "oddi<PERSON><PERSON><PERSON>_oyinlar", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "qochishoyinlari", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "k<PERSON>yin<PERSON>i", "játék": "oyin", "bordfodbold": "stolgafutbol", "jogosorte": "jogosorte", "mage": "<PERSON><PERSON><PERSON>", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "onlayn_oyin", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "sumkabingolari", "randomizer": "<PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagramma", "gamespc": "kompyuteroyinlari", "socialdeductiongames": "ijtimoiyyukushliosimlar", "dominos": "dominos", "domino": "domino", "isometricgames": "izometrikoyinlar", "goodoldgames": "eskimazemunoyinlar", "truthanddare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "riichimahjong", "scavengerhunts": "q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romxak", "f2pgamer": "f2pgeymer", "free2play": "bepul2oynash", "fantasygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "<PERSON><PERSON><PERSON>", "gamesotomes": "oyin<PERSON><PERSON><PERSON><PERSON>lar<PERSON>", "halotvseriesandgames": "halotvseriallarivaoʻyinlar", "mushroomoasis": "qoziqorinvoha", "anythingwithanengine": "har_q<PERSON><PERSON>_dvigate<PERSON><PERSON>_narsa", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "<PERSON><PERSON><PERSON>vas<PERSON><PERSON>", "goodgamegiving": "ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "j<PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "kompyuteroʻyinlari", "virgogami": "virgogami", "gogame": "oyinga", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "miniatyuraoʻyinlar", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "oyin<PERSON>dlash", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "stoltengizuvoy<PERSON>i", "spelletjes": "oyin<PERSON>", "spacenerf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "j<PERSON><PERSON><PERSON><PERSON>", "singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgame": "<PERSON>m<PERSON>lik<PERSON>ini", "gamed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "kingdiscord", "scrabble": "scrabble", "schach": "shax<PERSON>", "shogi": "shogi", "dandd": "dandd", "catan": "katan", "ludo": "ludo", "backgammon": "taq<PERSON>rd", "onitama": "onitama", "pandemiclegacy": "pandemiyaizlari", "camelup": "camelup", "monopolygame": "monopoly<PERSON><PERSON><PERSON><PERSON>", "brettspiele": "brettspiele", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planszowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "risiko": "tavakkal", "permainanpapan": "sto<PERSON><PERSON>_oyin<PERSON>i", "zombicide": "<PERSON><PERSON><PERSON><PERSON>", "tabletop": "s<PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "qonlichosa", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "tax<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectfour": "torttanitut", "heroquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidatavolo": "giochidata<PERSON>lo", "farkle": "farkle", "carrom": "karrom", "tablegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "zarlar_oyinlari", "yatzy": "yatzi", "parchis": "parchis", "jogodetabuleiro": "stolus<PERSON>_oyini", "jocuridesocietate": "jamiyatl<PERSON>yinlar", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "kosmiku<PERSON>rashuv", "creationludique": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "stolus<PERSON>_rollik_oyin", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "switchboardoʻyinlari", "infinitythegame": "ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "snakesandladders", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "sto<PERSON>yin", "planszówki": "stolustalar", "rednecklife": "qishloqhayoti", "boardom": "zerikish", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "sto<PERSON>yin", "gameboard": "oyintaxtasi", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "krokinol", "jeuxdesociétés": "stol<PERSON><PERSON>n", "twilightimperium": "twilightimperium", "horseopoly": "otopoly", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "jinn<PERSON>kqasrlari", "gomoku": "gomoku", "giochidatavola": "sto<PERSON><PERSON>_oyin<PERSON>i", "shadowsofbrimstone": "brimstonening_soyalari", "kingoftokyo": "tokyoshohiuz", "warcaby": "shashka", "táblajátékok": "stoljátékok", "battleship": "j<PERSON><PERSON><PERSON>", "tickettoride": "poyezdgachiptaol", "deskovehry": "desktopoyinlar", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "sto<PERSON>yin", "stolníhry": "stolusti_oyinlar", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "sto<PERSON>yin", "gesellschaftsspiele": "sto<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gochess": "gochess", "weiqi": "weiqi", "jeuxdesocietes": "jeuxdes<PERSON>iet<PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "u<PERSON>_may<PERSON>i", "arksurvivalevolved": "arksurvivalevolved", "dayz": "de<PERSON>z", "identityv": "identityv", "theisle": "orol", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "<PERSON><PERSON><PERSON>hun<PERSON><PERSON>", "bendyandtheinkmachine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "conanexiles": "conanexiles", "eft": "eft", "amongus": "amongus", "eco": "eko", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "planetkreytor", "daysgone": "otgankunlar", "fobia": "fobiya", "witchit": "<PERSON><PERSON><PERSON>", "pathologic": "patologik", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7ktk", "thelongdark": "uzunqorongulik", "ark": "ark", "grounded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "jinn<PERSON><PERSON>", "dontstarve": "ochq<PERSON>a", "eternalreturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "pathoftitans", "frictionalgames": "frictionalgames", "hexen": "jodugar", "theevilwithin": "ichkiyovuzlik", "realrac": "realrac", "thebackrooms": "orqaxonalar", "backrooms": "orqaxonalar", "empiressmp": "empiressmp", "blockstory": "blokxikoya", "thequarry": "karyer", "tlou": "tlou", "dyinglight": "olimyorugi", "thewalkingdeadgame": "thewalkingdeadoʻyini", "wehappyfew": "bizkamchilikbaxtlimiz", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "stateofsurvivaloyin", "vintagestory": "vintajhikoya", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "bar<PERSON><PERSON><PERSON><PERSON>", "breathedge": "nafasolish", "alisa": "alisa", "westlendsurvival": "westlendsurvival", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "qoronғiormon", "survivalhorror": "qorqi<PERSON><PERSON><PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "rezidentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "boshliqpoyezdi", "lifeaftergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpf<PERSON>", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "qorquvdanyiglash", "raft": "sal", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "buvijon", "littlenightmares2": "kichkintungikabuslari2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "rustvideooyin", "outlasttrials": "<PERSON>last<PERSON><PERSON><PERSON><PERSON>", "alienisolation": "begonaviyizolyatsiya", "undawn": "tong", "7day2die": "7kunolik2ulmoq", "sunlesssea": "quyoshsizdengiz", "sopravvivenza": "omon_qolish", "propnight": "propnightkechasi", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "<PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "kata<PERSON><PERSON><PERSON><PERSON>q<PERSON><PERSON><PERSON>i", "soma": "soma", "fearandhunger": "qoʻrquvvaochlik", "stalkercieńczarnobyla": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeafter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "soattower3", "aloneinthedark": "qorongudayolgiz", "medievaldynasty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "projectnimbusoyin", "eternights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftopia": "hunarmandlik", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "<PERSON>as<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "necron": "nekron", "wfrp": "wfrp", "dwarfslayer": "mittiqorgon", "warhammer40kcrush": "warhammer40kga_oshiq", "wh40": "wh40", "warhammer40klove": "warhammer40ksev<PERSON>i", "warhammer40klore": "warhammer40ktirix", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iloveassasinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templovenenum": "templovenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "qotillarningkasbi", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40ming", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "ikkitalikkerak", "wingspan": "qanotqulochi", "terraformingmars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofmightandmagic": "qud<PERSON><PERSON><PERSON><PERSON><PERSON>", "btd6": "btd6", "supremecommander": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofmythology": "mifologiyadavri", "args": "argumentlar", "rime": "qircha", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "surgun", "caesar3": "caesar3", "redalert": "qizilsignal", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "buyruqvaegall<PERSON>", "warcraft3": "warcraft3", "eternalwar": "a<PERSON><PERSON><PERSON><PERSON>", "strategygames": "strategi<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization4": "tsivilizatsiya4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spora", "totalwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "travian": "travian", "forts": "qalalar", "goodcompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civ": "tsivilizatsiya", "homeworld": "dunyo_uyi", "heidentum": "butparastlik", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "uchunqirollar", "realtimestrategy": "realtimestrategiya", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "kingdomtwocrownsgame", "eu4": "eu4", "vainglory": "<PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "xettrik", "davesfunalgebraclass": "deyvningqiziqijalgebradarsii", "plagueinc": "plagueinc", "theorycraft": "nazar<PERSON>lar", "mesbg": "mesbg", "civilization3": "sivilizatsiya3", "4inarow": "4taketmaket", "crusaderkings3": "crusaderkings3", "heroes3": "qahramonlar3", "advancewars": "<PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "ageofempires2", "disciples2": "disciples2", "plantsvszombies": "zombilargaqarshiosimliklar", "giochidistrategia": "strategi<PERSON><PERSON><PERSON><PERSON><PERSON>", "stratejioyunları": "strategi<PERSON><PERSON><PERSON><PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "dinozavrs<PERSON><PERSON>", "worldconquest": "dunyoniza<PERSON><PERSON>lish", "heartsofiron4": "temirdanyurak4", "companyofheroes": "q<PERSON><PERSON><PERSON>larkompaniyas<PERSON>", "battleforwesnoth": "battleforwesnoth", "aoe3": "aoe3", "forgeofempires": "forgeofempiressozon", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gozgozordak", "phobies": "fob<PERSON>lar", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "nav<PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "xochkeroklar", "cultris2": "cultris2", "spellcraft": "afsun_sanati", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategiya", "popfulmail": "popfulmail", "shiningforce": "nurafshonkuch", "masterduel": "masterduel", "dysonsphereprogram": "daysonsferadasturi", "transporttycoon": "transportmagnati", "unrailed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "<PERSON><PERSON><PERSON><PERSON>g<PERSON>oll<PERSON><PERSON>", "galaxylife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "qasrq<PERSON>lionlayn", "slaythespire": "slaythe<PERSON><PERSON><PERSON>hdi<PERSON>", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simshaxar", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "tezlikkamuhtoj", "needforspeedcarbon": "needforspeedcarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "bardoshberish", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "al<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "qoraquyonantolog<PERSON><PERSON>", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "freddinikbeshkechasi", "saiko": "sayko", "fatalframe": "qorqinchlikadr", "littlenightmares": "kichikkechatundushl<PERSON>", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "ladydi<PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadisland": "oliklar_oroli", "litlemissfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzero": "projectnol", "horory": "qor<PERSON><PERSON><PERSON>", "jogosterror": "<PERSON><PERSON><PERSON><PERSON><PERSON>dahsha<PERSON>", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "salomqoshni2", "gamingdbd": "gamingdbd", "thecatlady": "mushuk<PERSON><PERSON>m", "jeuxhorreur": "j<PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "insoniya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "<PERSON><PERSON><PERSON><PERSON>", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinokel", "codenames": "kodadlar", "dixit": "dixit", "bicyclecards": "ve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "evro", "thegwent": "thegwent", "legendofrunetera": "runeternafsonas", "solitaire": "solitaire", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "kartafokuslar", "playingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ji<PERSON><PERSON><PERSON>", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "sportkartochkalari", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "️", "warcry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "<PERSON><PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "q<PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "kokkozboyoqajdar", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON>", "burraco": "burrako", "rummy": "<PERSON><PERSON>", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "karta<PERSON><PERSON><PERSON>i", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonlayn", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "karta<PERSON><PERSON><PERSON>ni", "žolíky": "jokerlar", "facecard": "<PERSON><PERSON><PERSON>", "cardfight": "karta<PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "kartajamlash", "marvelchampions": "marve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magiccartas": "<PERSON><PERSON><PERSON>artalar", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "beqarorotbirshoxlar", "cyberse": "kiber", "classicarcadegames": "klassikarkadaoʻyinlari", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "jumaoxshomikayfiyat", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "<PERSON><PERSON>ham<PERSON><PERSON>", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "raqsgaqil", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "roksmit", "idolish7": "idolish7", "rockthedead": "oliklarnitebrat", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rhythmgamer": "ritmgeymer", "stepmania": "stepmania", "highscorerythmgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>yin<PERSON><PERSON>", "pkxd": "pkxd", "sidem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "rit<PERSON><PERSON><PERSON>", "hypmic": "hip<PERSON>", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "onlaynauditsiya", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "ritmshifokor", "cubing": "kubing", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "jum<PERSON><PERSON><PERSON><PERSON><PERSON>", "spotit": "topish", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blokdoku", "logicpuzzles": "mantiqiymuammolar", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "a<PERSON>lt<PERSON><PERSON><PERSON>", "rubikscube": "<PERSON><PERSON><PERSON><PERSON>", "crossword": "krossvord", "motscroisés": "motscroisés", "krzyżówki": "krzyżówki", "nonogram": "nonogramma", "bookworm": "kit<PERSON><PERSON>", "jigsawpuzzles": "jigsawpazzllar", "indovinello": "topishmoq", "riddle": "jumboq", "riddles": "jumbo<PERSON><PERSON>", "rompecabezas": "jumboq", "tekateki": "jumboq", "inside": "<PERSON><PERSON><PERSON>", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "qochishsimulyatori", "minesweeper": "<PERSON><PERSON><PERSON>", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "krossvordlar", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesoʻyini", "puzzlesport": "jumboqsport", "escaperoomgames": "qochish<PERSON><PERSON><PERSON>", "escapegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "3dgolovolomka", "homescapesgame": "homescapesoʻyini", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "jumboq", "kulaworld": "kulaworld", "myst": "sirli", "riddletales": "riddle<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "fishdom", "theimpossiblequiz": "imkonsizviktorina", "candycrush": "candycrush", "littlebigplanet": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "match3puzzle": "match3jumboq", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "kvirkiy", "rubikcube": "<PERSON><PERSON><PERSON><PERSON>", "cuborubik": "<PERSON><PERSON><PERSON><PERSON>", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "menitop", "tycoongames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cruciverba": "krossvord", "ciphers": "shi<PERSON><PERSON>ar", "rätselwörter": "jumboq<PERSON>zlar", "buscaminas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesolving": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "turnipboy", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "<PERSON><PERSON><PERSON><PERSON>", "guessing": "taxmin", "nonograms": "nonogrammalar", "kostkirubika": "kostkirubika", "crypticcrosswords": "sirlijavvoblarkrossvord", "syberia2": "siberiya2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "bosh<PERSON><PERSON><PERSON><PERSON>", "hlavolamy": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poptropica": "<PERSON><PERSON>pi<PERSON>", "thelastcampfire": "oxi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "picopark": "pikopark", "wandersong": "sayohatqoʻshigʻi", "carto": "karto", "untitledgoosegame": "qiziqoyinsiz", "cassetête": "bosh<PERSON><PERSON><PERSON><PERSON>", "limbo": "limbo", "rubiks": "rubiks", "maze": "labirint", "tinykin": "tinykin", "rubikovakostka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "speedcube": "speedkub", "pieces": "bolaklar", "portalgame": "<PERSON><PERSON><PERSON>", "bilmece": "bilmece", "puzzelen": "jumboq", "picross": "picross", "rubixcube": "<PERSON><PERSON><PERSON><PERSON>", "indovinelli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cubomagico": "<PERSON><PERSON><PERSON><PERSON>", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopoly": "monopoliya", "futurefight": "kelajakjang", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "yolgizbori", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemystars", "stateofsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "giperfrontal", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkayimpakt", "soccerbattle": "futboljangi", "a3": "a3", "phonegames": "telefon_oyinlari", "kingschoice": "q<PERSON><PERSON><PERSON><PERSON><PERSON>", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "<PERSON><PERSON><PERSON><PERSON>", "tacticool": "takt<PERSON><PERSON><PERSON><PERSON>", "cookierun": "cookierun", "pixeldungeon": "p<PERSON><PERSON><PERSON><PERSON>", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftsman": "<PERSON><PERSON><PERSON><PERSON>", "supersus": "shu<PERSON><PERSON>", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "ogohboldim", "wordfeud": "wordfeud", "bedwars": "bedwars", "freefire": "fri<PERSON><PERSON>", "mobilegaming": "mobilgeyming", "lilysgarden": "lilyningbogi", "farmville2": "farmville2", "animalcrossing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8topmilliard", "emergencyhq": "<PERSON><PERSON><PERSON><PERSON>", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "titroqvabesabrlik", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "vaqtmalika", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "androidgames": "and<PERSON><PERSON><PERSON><PERSON>", "criminalcase": "<PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "pazandalikovoshi", "dokkan": "dokkan", "aov": "aov", "triviacrack": "savoljavob", "leagueofangels": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "neyrontarmoq", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "jang<PERSON><PERSON>", "mirrorverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "ertaklar", "futime": "qiziqarvaqt", "antiyoy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apexlegendmobile": "apexlegendmobile", "ingress": "kirish", "slugitout": "urishibolishamiz", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "<PERSON><PERSON>qi<PERSON>", "wolfy": "bori", "runcitygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegodemovil": "mobiloyin", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "mi<PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "bombmeniuzbekiston", "ldoe": "ldoe", "legendonline": "onlaynafsonalar", "otomegame": "otomeoyin", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftpoyga2", "pathtonowhere": "yoly<PERSON>q", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "taloqinderbi3", "wordswithfriends2": "dostlarbilansozoyini2", "soulknight": "soulknight", "purrfecttale": "purrfekt<PERSON>hon", "showbyrock": "shouby<PERSON>", "ladypopular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "lolmobil", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "perfectworldmobile", "empiresandpuzzles": "imperiyalarvajumboqlar", "empirespuzzles": "imperiya<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "qiziq", "littlenightmare": "kichikqob<PERSON>", "aethergazer": "aethergazer", "mudrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON>", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiqochqinlar", "eveechoes": "eveechoes", "jogocelular": "mobiloyin", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "ov<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "streetfighterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "qizlarfronti", "jurassicworldalive": "jurassic<PERSON><PERSON><PERSON>", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "us<PERSON>danke<PERSON>sh<PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "mobiloyinlar", "legendofneverland": "legendofneverland", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "vaqtbosqinchilari", "gamingmobile": "mobilgeyming", "marvelstrikeforce": "marve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "kvest", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "stolusti_rpg", "worldofdarkness": "qorongʻulik<PERSON><PERSON>o", "travellerttrpg": "sayohatchitrpg", "2300ad": "2300yil", "larp": "r<PERSON><PERSON><PERSON>n", "romanceclub": "romantikklub", "d20": "d20", "pokemongames": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonsirlimanzara", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemonkristal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonqi<PERSON>l", "pokemongo": "pokemongo", "pokemonshowdown": "poke<PERSON><PERSON><PERSON>", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "libleb", "porygon": "porygon", "pokemonunite": "pokemonbirlashing", "entai": "entai", "hypno": "gipnoz", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "<PERSON><PERSON>yu", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamroket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlaks", "pocketmonsters": "cho<PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlok", "pokemonplush": "pokemonqogirchoq", "teamystic": "teamystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "yosh_mushuk", "shinypokemon": "ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psaydak", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON><PERSON>u", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "poke<PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "bola<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbazavr", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "shax<PERSON>", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "dunyoblits", "jeudéchecs": "shax<PERSON>", "japanesechess": "<PERSON><PERSON><PERSON>hax<PERSON><PERSON>", "chinesechess": "<PERSON>ian<PERSON><PERSON>", "chesscanada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "bosh<PERSON><PERSON><PERSON>", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "qala<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "<PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "vox<PERSON><PERSON><PERSON><PERSON><PERSON>", "doungenoanddragons": "doungenoanddragons", "darkmoor": "qoro<PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "minecraftchempionati", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodlari", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "moddedminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftshahri", "pcgamer": "kompgeymer", "jeuxvideo": "videoyinlar", "gambit": "gambit", "gamers": "<PERSON><PERSON><PERSON><PERSON>", "levelup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermobile": "geymermobil", "gameover": "o<PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "kompyuteroʻyinlari", "gamen": "geymeruz", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "kompyuteroyinlari", "casualgaming": "kul<PERSON><PERSON>n", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "kompyuterbozlari", "pcgame": "<PERSON><PERSON><PERSON><PERSON>", "gamerboy": "g<PERSON><PERSON><PERSON>", "vrgaming": "v<PERSON><PERSON>ing", "drdisrespect": "drdisrespect", "4kgaming": "4k<PERSON><PERSON>ing", "gamerbr": "geymer", "gameplays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "konsolgeymeri", "boxi": "boxi", "pro": "pro", "epicgamers": "epikgeymerlar", "onlinegaming": "onlaynoy<PERSON>lar", "semigamer": "yarimgeymer", "gamergirls": "geymergirls", "gamermoms": "geymermomalar", "gamerguy": "geymermen", "gamewatcher": "oyinkozatuvchi", "gameur": "geymer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "geymerqizlar", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "<PERSON>att<PERSON><PERSON><PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "topshir<PERSON>lar", "alax": "alax", "avgn": "avgn", "oldgamer": "eskigeymer", "cozygaming": "qula<PERSON><PERSON>yin", "gamelpay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepc": "kompyuteroyinlari", "dsswitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "competitivegaming": "raqobatch<PERSON>yinlar", "minecraftnewjersey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "faker": "soxta", "pc4gamers": "kompyutergeymerlar", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heterose<PERSON><PERSON><PERSON><PERSON>ing", "gamepc": "gamekomp", "girlsgamer": "qizlargeymer", "fnfmods": "fnfmodlar", "dailyquest": "kunlikvazifa", "gamegirl": "g<PERSON><PERSON><PERSON>", "chicasgamer": "geymerqizlar", "gamesetup": "oyint<PERSON><PERSON>", "overpowered": "juda_kuchli", "socialgamer": "ijtimoiygeymer", "gamejam": "oyinjam", "proplayer": "proplayer", "roleplayer": "<PERSON><PERSON><PERSON><PERSON>", "myteam": "<PERSON><PERSON><PERSON><PERSON>", "republicofgamers": "geymerlarrespublikasi", "aorus": "aorus", "cougargaming": "pumageyming", "triplelegend": "uchmartaafsonaviy", "gamerbuddies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "butuhcewekgamers": "gamerqizlarkerak", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nerdgamer": "nerd<PERSON><PERSON>r", "afk": "afk", "andregamer": "andregamer", "casualgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "89squad", "inicaramainnyagimana": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "insec": "is<PERSON><PERSON><PERSON>", "gemers": "<PERSON><PERSON><PERSON><PERSON>", "oyunizlemek": "o<PERSON><PERSON><PERSON><PERSON>", "gamertag": "g<PERSON>mer<PERSON>", "lanparty": "lanpartiya", "videogamer": "videogeymer", "wspólnegranie": "birga<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "playstationgeymer", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "soglomgeymer", "gtracing": "gtracing", "notebookgamer": "notebuktegeymer", "protogen": "protogen", "womangamer": "ayo<PERSON><PERSON><PERSON>", "obviouslyimagamer": "elbettegeymerboʻlganmanku", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "ovchi", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nolqochish", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusiqa", "sonicthehedgehog": "sonikkirpi", "sonic": "sonik", "fallguys": "fallguys", "switch": "ozgartir", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON>eld<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megamen", "majorasmask": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "ace_advokat", "ssbm": "ssbm", "skychildrenofthelight": "osmon<PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ahatintime": "vaq<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "yurish_simulyatorlari", "nintendogames": "nintendooy<PERSON><PERSON><PERSON>", "thelegendofzelda": "thelegendofzelda", "dragonquest": "dragonquest", "harvestmoon": "kuzoyiorugi", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON>", "celeste": "seleste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "zeldaafsonalari", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51<PERSON>in", "earthbound": "yergatortilgan", "tales": "<PERSON><PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "uchburchakstrategiyasi", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "konkersningunsizjumasikuni", "nintendos": "nintendolar", "new3ds": "yangi3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartyyulduzlar<PERSON>", "marioandsonic": "mariovanisonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "ma<PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "qizilitsimonlar", "vanillalol": "vanillaqiziq", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "tezjang", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "reklamatashuvchi", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsispaniya", "aatrox": "aatrox", "euw": "yoq", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolliga", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "gey<PERSON><PERSON>l", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "heksgeytlar", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamefortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideooyinlar", "scaryvideogames": "qorqinchlivideooyinlar", "videogamemaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "videooyin", "videosgame": "videooyinlar", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "<PERSON><PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "puffdos<PERSON>ar", "farmingsimulator": "fermersimulator", "robloxchile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxgermaniya", "robloxdeutsch": "robloxnemischa", "erlc": "erlc", "sanboxgames": "sanboxoyinlar", "videogamelore": "videooyintarixlari", "rollerdrome": "rollerdrom", "parasiteeve": "parazitevakecha", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "tush<PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto": "grandtheftauto", "deadspace": "oʻlikmaydon", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "videooyinlar", "theoldrepublic": "eskijarespublika", "videospiele": "videooyinlar", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "sarguzash<PERSON>io<PERSON>yi<PERSON>lar", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON>ks<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "retro_oyinlar", "retroarcade": "retroarkeyd", "vintagecomputing": "vintajkompyuter", "retrogaming": "retrogeyming", "vintagegaming": "vintageoyinlar", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "adolatsizlik2", "shadowthehedgehog": "shadowkirpi", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON>", "zenlife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "tik", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievil": "medievil", "consolegaming": "konsoloyinlar", "konsolen": "konsolen", "outrun": "oldindanoz", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "qor<PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiant": "supergigant", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "fermersimlar", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "interaktivfantastika", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "sevishganlarbirxilfikrl<PERSON>di", "visualnovel": "vizualroman", "visualnovels": "vizualromanlar", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tcrghost": "tcrsharpa", "payday": "oylik", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "tort<PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "qumdon", "aestheticgames": "estetikoyinlar", "novelavisual": "vizualnovella", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retro_oyin", "tonyhawkproskater": "tonihokproskeyteri", "smbz": "smbz", "lamento": "lamento", "godhand": "xudoning_qoli", "leafblowerrevolution": "bargpurkagichq<PERSON>", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starrail": "starrail", "keyblade": "keyblade", "aplaguetale": "vab<PERSON><PERSON><PERSON>", "fnafsometimes": "fnafsometimes", "novelasvisuales": "vizualromanlar", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "retrogeyming", "videojuejos": "video<PERSON>ʻyinlar", "videogamedates": "videooyins<PERSON>lar", "mycandylove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "shundayham3", "hulkgames": "xalkoyinlari", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergeyming", "dayofthetantacle": "tanta<PERSON><PERSON><PERSON>", "maniacmansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashracing": "avtopoyga", "3dplatformers": "3dplatformerlar", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "eskiramkaligeym", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "to<PERSON><PERSON>danchetla<PERSON>", "beyondtwosouls": "ikkijanortasida", "gameuse": "oyi<PERSON><PERSON><PERSON>", "offmortisghost": "offmortisghost", "tinybunny": "kichkinaquyon", "retroarch": "retroarch", "powerup": "kuch<PERSON><PERSON><PERSON><PERSON>", "katanazero": "katananol", "famicom": "famicom", "aventurasgraficas": "grafikasarguzashtlar", "quickflash": "<PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "g<PERSON><PERSON><PERSON><PERSON>", "retroarcades": "retroarkadalar", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "powerwashsim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coralisland": "korallorol", "syberia3": "siberiya3", "grymmorpg": "qoʻrqinchlirpg", "bloxfruit": "bloxfruit", "anotherworld": "bosh<PERSON><PERSON><PERSON>o", "metaquest": "metaquest", "animewarrios2": "animejangovarlar2", "footballfusion": "futbolq<PERSON>lish", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "buralmametall", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "uyatliuyum", "simulator": "simulyator", "symulatory": "simulyatorlar", "speedrunner": "speedrunner", "epicx": "epikx", "superrobottaisen": "superrobotjang", "dcuo": "dcuo", "samandmax": "samvamax", "grywideo": "gryvideo", "gaiaonline": "gaiaonline", "korkuoyunu": "qor<PERSON><PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "mo<PERSON><PERSON><PERSON><PERSON>", "skylander": "skylander", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "toontownqaytayozilgan", "simracing": "simracing", "simrace": "simreys", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "hafsala", "partyvideogames": "partyvideooyinlari", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "kosmikparvozsimulyatori", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "ovqatvavideoyinlar", "oyunvideoları": "<PERSON>yun<PERSON><PERSON><PERSON>", "thewolfamongus": "oramizdagibori", "truckingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "horizonworlds", "handygame": "qolo<PERSON>yin", "leyendasyvideojuegos": "afsonalarvavideooʻyinlar", "oldschoolvideogames": "eskimaktab<PERSON><PERSON><PERSON>yinlar", "racingsimulator": "oyinchoqpoyga", "beemov": "bee<PERSON>v", "agentsofmayhem": "tartibsizlikagentalari", "songpop": "q<PERSON>q<PERSON>", "famitsu": "famitsu", "gatesofolympus": "olimpdarvozalari", "monsterhunternow": "monsterhunternow", "rebelstar": "qoʻzgʻolonchiyulduz", "indievideogaming": "indie<PERSON>in<PERSON>", "indiegaming": "indioʻyinlar", "indievideogames": "indievideooyinlar", "indievideogame": "<PERSON><PERSON><PERSON>", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spider<PERSON><PERSON><PERSON><PERSON><PERSON>", "bufffortress": "k<PERSON><PERSON><PERSON>i", "unbeatable": "tengs<PERSON>", "projectl": "proyektl", "futureclubgames": "kelajakkluboyinlari", "mugman": "mugman", "insomniacgames": "uyqusizgeymerlar", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "g<PERSON><PERSON><PERSON><PERSON>", "celestegame": "c<PERSON><PERSON><PERSON><PERSON>", "aperturescience": "aperturescience", "backlog": "ortdaqolganishlar", "gamebacklog": "oyinlarnavbati", "gamingbacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "yomonit", "beastlord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosretro": "retro_oyinlar", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "orivakoʻrtoʻgʻon", "alanwake": "alanwake", "stanleyparable": "stenliparable", "reservatoriodedopamin": "do<PERSON><PERSON><PERSON><PERSON>", "staxel": "staxel", "videogameost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsync": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "sirli", "neoy2k": "neoy2k", "pcracing": "pcpoyga", "berserk": "jinni", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "bosh<PERSON><PERSON><PERSON>", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "gamginanime", "darkerthanblack": "qoradanqora", "animescaling": "animescaling", "animewithplot": "animesyujetli", "pesci": "pesci", "retroanime": "retroanime", "animes": "animeler", "supersentai": "supersentai", "samuraichamploo": "samuraychamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "80yillikanime", "90sanime": "90<PERSON><PERSON><PERSON><PERSON>", "darklord": "qoraqorgon", "popeetheperformer": "pop<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samurayx", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "2000yillaranime", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonebirinchimavsum", "rapanime": "rapanime", "chargemanken": "modelzar<PERSON><PERSON>", "animecover": "animequshiq", "thevisionofescaflowne": "eskaflouneningkuchi", "slayers": "qotillar", "tokyomajin": "tokyomajin", "anime90s": "anime90yillar", "animcharlotte": "animshar<PERSON>ta", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "ban<PERSON><PERSON>iq", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "xaykyu", "toiletboundhanakokun": "hojat<PERSON><PERSON><PERSON>_hanako_kun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "moriart<PERSON><PERSON><PERSON>ot", "futurediary": "kelajakkundaligi", "fairytail": "ertaklar", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "<PERSON><PERSON><PERSON>", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "suvpar<PERSON><PERSON><PERSON>", "kamisamakiss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemangas": "romantik_mangalar", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "ajdarxizmatkor", "blacklagoon": "qoralaguna", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsixo100", "terraformars": "terraformars", "geniusinc": "geniusinc", "shamanking": "<PERSON><PERSON><PERSON><PERSON>", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "qandaydirsihrliindeks", "sao": "sao", "blackclover": "qoragiriftor", "tokyoghoul": "tokyoghoul", "onepunchman": "birurishbilanodam", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8cheksizlik", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "gipnozlanishomuzik", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monsterqiz", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaylanime", "sagaoftanyatheevil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "y<PERSON>tvahayvon", "fistofthenorthstar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "qoraxizmatkor", "towerofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "qiziqarliyaxolos", "martialpeak": "jang<PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "yuqorinatijaligaqiz", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "jonconstantine", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "ikkinolnol", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstergirl", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffit", "ririn": "ririn", "korra": "korra", "vanny": "<PERSON><PERSON>", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "s<PERSON><PERSON><PERSON>", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluton", "aloy": "aloy", "runa": "runa", "oldanime": "eski<PERSON><PERSON>", "chainsawman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "qarobasqinchi", "ergoproxy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "claymore": "kleymor", "loli": "loli", "horroranime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "i<PERSON><PERSON>yigitchayiglar", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "jonlitranslatsiya", "sakuracardcaptor": "sa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "v<PERSON>lashgan<PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermanga": "monstermanga", "yourlieinapril": "aprelchongizningyolgon", "buggytheclown": "baggitheclovn", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "kiborg009", "magi": "magi", "deepseaprisoner": "<PERSON><PERSON>z<PERSON>ridagia<PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "olik<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "bananabaliq", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON>", "husbu": "turmushortogim", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "koʻkdavr", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "yashirinital<PERSON>s", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "se<PERSON><PERSON>ningxotini", "yuki": "yuki", "erased": "oʻchirilgan", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "<PERSON><PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "kokmavzunsuruvchi", "slamdunk": "s<PERSON><PERSON><PERSON><PERSON>", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "airgear": "ha<PERSON><PERSON>k", "magicalgirl": "<PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "qamoqxonamaktabi", "thegodofhighschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kissxsis": "opaqizniop", "grandblue": "kattakok", "mydressupdarling": "meningkiyintiruvchiqogirchogim", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeolamи", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoqisqartma", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpsixo100", "hajimenoippo": "hajimenoippo", "undeadunluck": "omadsiznohalokat", "romancemanga": "romantikamanga", "blmanhwa": "b<PERSON><PERSON><PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animesevgi", "senpai": "senpay", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animegentina", "lolicon": "lolikon", "demonslayertothesword": "demonslayerqilichgacha", "bloodlad": "qonli<PERSON>", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON>", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceanime": "sevgia<PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "rekordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "oliklarningmaktabi", "germantechno": "nemistexno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "tennis<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "qotillarsinfi", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "olimparadi", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animespace": "animefazo", "girlsundpanzer": "qizlarvatanklar", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedublaj", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "<PERSON><PERSON><PERSON><PERSON>", "indieanime": "indiani", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animefantastika", "ratman": "ka<PERSON><PERSON>", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "shaftoliqiz", "cavalieridellozodiaco": "zodiakritsarlari", "mechamusume": "mexaqizlar", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinkaltakklubi", "dragonquestdai": "dragonquestdai", "heartofmanga": "mangayurag<PERSON>", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "ragnarokyozuvlar<PERSON>", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON>yin", "overgeared": "<PERSON><PERSON>oz<PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "reyvmaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "joduga<PERSON><PERSON>a", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON>", "dropsofgod": "x<PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "zodiakritsarlari", "animeshojo": "animeshojo", "reverseharem": "teskarigaremkon", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "bossimbobom", "gear5": "gear5", "grandbluedreaming": "kattakoʻkorzular", "bloodplus": "qonplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "qonlianime", "bloodc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofdemonsandgods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "anime_tomosha_qilish", "animegirls": "animequtulalar", "sharingan": "<PERSON><PERSON>", "crowsxworst": "qargalarxengyomon", "splatteranime": "splatteranime", "splatter": "sochilish", "risingoftheshieldhero": "qalqonqahramonningko<PERSON>tarilis<PERSON>", "somalianime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riodejaneiroanime": "riodejaneyroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "animeispaniya", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "kitlarningbolalari", "liarliar": "yolgonchiyolgonchi", "supercampeones": "superchempionlar", "animeidols": "animequshonalar", "isekaiwasmartphone": "isekaysmartfonbilanedi", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "sehrligizlar", "callofthenight": "tungitaqbirgi", "bakuganbrawler": "bakuganjang<PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "soyabog", "tsubasachronicle": "tsubasachronicle", "findermanga": "<PERSON><PERSON><PERSON><PERSON>", "princessjellyfish": "meduza_malika", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "j<PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeverse": "animeolam", "persocoms": "persokompyuterlar", "omniscientreadersview": "hammanibiluvchiningkozkarashi", "animecat": "animemushuk", "animerecommendations": "animerekomendatsiyalari", "openinganime": "ochi<PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "menoshalik_romantik_komediyam", "evangelion": "evangelion", "gundam": "gundam", "macross": "makross", "gundams": "gundamlar", "voltesv": "voltesv", "giantrobots": "ulkanrobot<PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mex", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deathnote": "olimdaftari", "cowboybebop": "kovboybibop", "jjba": "jjba", "jojosbizarreadventure": "j<PERSON><PERSON>inggaroyibsar<PERSON>zashtlari", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "greenranger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animeshahar", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broli", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "hu<PERSON><PERSON><PERSON><PERSON><PERSON>", "erenyeager": "erenyeager", "myheroacademia": "boh<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "tadqiqotkorpusi", "onepieceanime": "onepieceanime", "attaquedestitans": "titan<PERSON><PERSON><PERSON><PERSON>", "theonepieceisreal": "onepiecehaqiqiy", "revengers": "qasos<PERSON><PERSON>", "mobpsycho": "mobpsixo", "aonoexorcist": "aonoekzortsist", "joyboyeffect": "joyboyeffekt", "digimonstory": "digimonstoriya", "digimontamers": "digimonus<PERSON>ar", "superjail": "superjinoyatxona", "metalocalypse": "metalokalisps", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "benuqsonvebtoon", "kemonofriends": "kemonodostlar", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "u<PERSON>v<PERSON>jo<PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "shun<PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "barqoa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "<PERSON><PERSON><PERSON><PERSON>"}