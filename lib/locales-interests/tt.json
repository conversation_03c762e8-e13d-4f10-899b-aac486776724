{"2048": "2048", "mbti": "mbti", "enneagram": "эннеаграмма", "astrology": "астрология", "cognitivefunctions": "танубелемфункцияләре", "psychology": "психология", "philosophy": "философия", "history": "тарих", "physics": "физика", "science": "фән", "culture": "мәдәният", "languages": "тел_өйрәнәбез", "technology": "технология", "memes": "мем<PERSON>р", "mbtimemes": "mbt<PERSON><PERSON><PERSON><PERSON><PERSON>", "astrologymemes": "астрологиямемнары", "enneagrammemes": "эннеаграммемнар", "showerthoughts": "суастындауйлар", "funny": "кызыклы", "videos": "видеолар", "gadgets": "гадж<PERSON><PERSON><PERSON><PERSON>р", "politics": "сәясәт", "relationshipadvice": "мөнәсәбәткиңәшләре", "lifeadvice": "тормыштуры", "crypto": "крипто", "news": "яңалыклар", "worldnews": "дөньяяңалыклары", "archaeology": "археология", "learning": "өйрәнү", "debates": "деба<PERSON><PERSON><PERSON>р", "conspiracytheories": "сергәсүзләр", "universe": "галәм", "meditation": "медитация", "mythology": "мифология", "art": "сәнгать", "crafts": "һөнәрчелек", "dance": "биюләр", "design": "ди<PERSON><PERSON><PERSON>н", "makeup": "макияж", "beauty": "матурлык", "fashion": "мода", "singing": "җырлау", "writing": "язу", "photography": "фотография", "cosplay": "косплей", "painting": "рәсем", "drawing": "рәсем", "books": "кита<PERSON>лар", "movies": "фильм<PERSON>р", "poetry": "шигърият", "television": "телевизор", "filmmaking": "фильмтөшерү", "animation": "анимация", "anime": "аниме", "scifi": "фантастика", "fantasy": "фэнтези", "documentaries": "документальфильмнар", "mystery": "мистика", "comedy": "комедия", "crime": "җинаять", "drama": "драма", "bollywood": "болливуд", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "куркыныч", "romance": "мәхәббәт", "realitytv": "реалити<PERSON><PERSON>у", "action": "гамәл", "music": "музыка", "blues": "блюз", "classical": "клас<PERSON>ик", "country": "ил", "desi": "дези", "edm": "edm", "electronic": "электроника", "folk": "халык", "funk": "фанк", "hiphop": "хипхоп", "house": "йорт", "indie": "инди", "jazz": "д<PERSON><PERSON><PERSON>", "kpop": "kpop", "latin": "ла<PERSON>ин", "metal": "металл", "pop": "поп", "punk": "панк", "rnb": "рнб", "rap": "рэп", "reggae": "регги", "rock": "рок", "techno": "техно", "travel": "сәяхәт", "concerts": "концертлар", "festivals": "фестивальләр", "museums": "музейлар", "standup": "стендап", "theater": "театр", "outdoors": "табигать", "gardening": "бакчачылык", "partying": "тусовкалар", "gaming": "гейминг", "boardgames": "өстәлуеннары", "dungeonsanddragons": "дөньяларһәмаждаһалар", "chess": "шахмат", "fortnite": "фортнайт", "leagueoflegends": "лигалегенд", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "покемон", "food": "ашамлык", "baking": "пешерү", "cooking": "ашпешү", "vegetarian": "вегетариан", "vegan": "веган", "birds": "чыпчыклар", "cats": "мәчеләр", "dogs": "иттәр", "fish": "балык", "animals": "хай<PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklivesmatter": "каратормышларәһәмиятле", "environmentalism": "экологизм", "feminism": "феминизм", "humanrights": "кешехокуклары", "lgbtqally": "лгбткярдәмче", "stopasianhate": "азиягәкаршынәфрәтнетуктат", "transally": "транслыялтерәфтар", "volunteering": "волонтерлык", "sports": "спорт", "badminton": "бадминтон", "baseball": "бейсбол", "basketball": "баскетбол", "boxing": "бокс", "cricket": "крикет", "cycling": "велосипедчылык", "fitness": "фитнес", "football": "футбол", "golf": "гольф", "gym": "спортзал", "gymnastics": "гимнастика", "hockey": "хоккей", "martialarts": "сугышсәнгате", "netball": "нетбол", "pilates": "пилатес", "pingpong": "пингпонг", "running": "йөгерү", "skateboarding": "скейтбординг", "skiing": "чаңгы", "snowboarding": "сноуборд", "surfing": "серфинг", "swimming": "йөзү", "tennis": "теннис", "volleyball": "волейбол", "weightlifting": "авырлыкҗитештерү", "yoga": "йога", "scubadiving": "скубадайвинг", "hiking": "походка", "capricorn": "капрога", "aquarius": "суүрчән", "pisces": "балыклар", "aries": "учак", "taurus": "үгез", "gemini": "икезәк", "cancer": "ракончире", "leo": "арыслан", "virgo": "кыз", "libra": "үлчәүләр", "scorpio": "скорпион", "sagittarius": "сагиттариус", "shortterm": "кыскавакытлы", "casual": "гади", "longtermrelationship": "узаквакытлымөнәсәбәтләр", "single": "ялгыз", "polyamory": "полиамория", "enm": "энм", "lgbt": "лгбт", "lgbtq": "лг<PERSON><PERSON>к", "gay": "гей", "lesbian": "лесбиянка", "bisexual": "бисексуал", "pansexual": "пансексуал", "asexual": "асексуал", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "ассасинскрид", "saintsrow": "saintsrow", "danganronpa": "данганронпа", "deltarune": "deltarune", "watchdogs": "этсакчылар", "dislyte": "dislyte", "rougelikes": "rougelike", "kingsquest": "кингсквест", "soulreaver": "җанйолгычы", "suikoden": "суйкодэн", "subverse": "подвселенная", "legendofspyro": "spyrolegendası", "rouguelikes": "rouguelikes", "syberia": "себер", "rdr2": "rdr2", "spyrothedragon": "спайродракон", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "күнбатыш_драйвы", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "рокстеди", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "гильдсугышлары", "openworld": "ачыкдөнья", "heroesofthestorm": "героеслардавыл", "cytus": "cytus", "soulslike": "җанкебек", "dungeoncrawling": "зин<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribesofmidgard", "planescape": "очкычлар", "lordsoftherealm2": "патшаларханлыгы2", "baldursgate": "балдурсгейт", "colorvore": "төсашар", "medabots": "медаботлар", "lodsoftherealm2": "патшалыкиялары2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "чынкүңелсимнар", "okage": "okage", "juegoderol": "рольуены", "witcher": "витчер", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "котор", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "элдерскроллс", "modding": "моддинг", "charactercreation": "персонажясау", "immersive": "тулысынча", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyискечә", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "файналфэнтези", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "лалафель", "dissidia": "дисидия", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "үлеммотивациясе", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "отоме", "suckerforlove": "мәхәббәткәчыгалык", "otomegames": "отомеуеннары", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "оқарина_вақыты", "yiikrpg": "yiikrpg", "vampirethemasquerade": "вампирларныңмаскарады", "dimension20": "dimension20", "gaslands": "газҗирләре", "pathfinder": "юлтабучы", "pathfinder2ndedition": "pathfinder2нчеэдиция", "shadowrun": "күләгәйөгерү", "bloodontheclocktower": "сәгатьманарасындаканбар", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "loveникки", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravityrush", "rpg": "рольуеннары", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "берсүрәт", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "хуҗа", "yourturntodie": "үлергәчираттасиңа", "persona3": "persona3", "rpghorror": "rpgкуркыныч", "elderscrollsonline": "элдерскроллсонлайн", "reka": "река", "honkai": "хонкай", "marauders": "марод<PERSON><PERSON><PERSON><PERSON>р", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "эпиксемь", "rpgtext": "rpgтекст", "genshin": "ген<PERSON>ин", "eso": "эсо", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "минминемминулар", "falloutshelter": "ядрәнсыгынучы", "gurps": "gurps", "darkestdungeon": "караңгытөнге", "eclipsephase": "элгергәнвакыт", "disgaea": "disgaea", "outerworlds": "тышкыдөньялар", "arpg": "арпг", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "династиясугышчылары", "skullgirls": "skullgirls", "nightcity": "төнгешәһәр", "hogwartslegacy": "хогвартсмирасы", "madnesscombat": "маднесскомбат", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "йул96", "vtmb": "втмб", "chimeraland": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelike", "gothamknights": "gothamрыцарьлары", "forgottenrealms": "онытылганпатшалыклар", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "орнарпг", "toontown": "мультшәһәр", "childoflight": "балаягълык", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonдөньясы", "monsterrancher": "монстрфермасы", "ecopunk": "экопанк", "vermintide2": "vermintide2", "xeno": "ксено", "vulcanverse": "vulcanverse", "fracturedthrones": "ватылган<PERSON><PERSON><PERSON><PERSON>ар", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "күләгәпанк", "finalfantasyxv": "finalfantasyxv", "everoasis": "һәрвакытоазис", "hogwartmystery": "хогвартсеры", "deltagreen": "дельта<PERSON><PERSON><PERSON>л", "diablo": "диабло", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "үтер", "lastepoch": "соңгычор", "starfinder": "йолдызтабучы", "goldensun": "алтынкояш", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "караңгыдапычаклар", "twilight2000": "алакараңгы2000", "sandevistan": "сандевистан", "cyberpunk": "киб<PERSON><PERSON><PERSON>анк", "cyberpunk2077": "киберпанк2077", "cyberpunkred": "кибербанкочкылу", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "төшкәнфәрман", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "явызҗирләр", "genshinimact": "геншинимакт", "aethyr": "aethyr", "devilsurvivor": "иблисбелəнбəргəкөрəш", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "файналфэнтези10", "anime5e": "аниме5е", "divinity": "илаһият", "pf2": "pf2", "farmrpg": "фермаrpg", "oldworldblues": "искедөньякайгысы", "adventurequest": "маҗараэзләү", "dagorhir": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayingames": "рольле_уеннар", "roleplayinggames": "рольуеннары", "finalfantasy9": "finalfantasy9", "sunhaven": "кояшавылы", "talesofsymphonia": "симфонияхикәяләре", "honkaistarrail": "honkaistarrail", "wolong": "улыганҗан", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "ертылганшәһәр", "myfarog": "myfarog", "sacredunderworld": "изгеҗәһәннәм", "chainedechoes": "чылбырлыяңгырашлар", "darksoul": "караңгыҗан", "soulslikes": "җанкебекләр", "othercide": "башкаякүзлек", "mountandblade": "mountandbladeуены", "inazumaeleven": "инадзумаиләвен", "acvalhalla": "acvalhalla", "chronotrigger": "хронотриггер", "pillarsofeternity": "pillarsofeternity", "palladiumrpg": "palladiumrpg", "rifts": "ярыклар", "tibia": "тибия", "thedivision": "дивизия", "hellocharlotte": "сәламшарлотта", "legendofdragoon": "легендадракона", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "вампирларныңмаскарады", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "убырлыкахыргамәт", "aveyond": "aveyond", "littlewood": "кечкенәурман", "childrenofmorta": "mortaбалалары", "engineheart": "йөрәкмотор", "fable3": "fable3", "fablethelostchapter": "әкиятнеңюгалганбүлеге", "hiveswap": "hiveswap", "rollenspiel": "роллиуен", "harpg": "harpg", "baldursgates": "балдурсгейтс", "edeneternal": "мәңгелекҗәннәт", "finalfantasy16": "finalfantasy16", "andyandleyley": "эндиһәмлейли", "ff15": "ff15", "starfield": "йолдызлыкыр", "oldschoolrevival": "искемәктәпкайтыша", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "явысдөньялар", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "караңгылыкзинданы", "juegosrpg": "rpgуеннары", "kingdomhearts": "kingdomhearts", "kingdomheart3": "патшалыкйөрәк3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "кланмалкавиан", "harvestella": "урыпҗыю", "gloomhaven": "глумхейвен", "wildhearts": "кыргыййөрәкләр", "bastion": "бастион", "drakarochdemoner": "аждаһаларһәмҗеннәр", "skiesofarcadia": "arcadiaкүкләре", "shadowhearts": "күләгәйөрәкләр", "nierreplicant": "nierreplicant", "gnosia": "гносия", "pennyblood": "канташлык", "breathoffire4": "утыңчагы4", "mother3": "ана3", "cyberpunk2020": "киберпанк2020", "falloutbos": "falloutбратство", "anothereden": "башкаэден", "roleplaygames": "рольуеннары", "roleplaygame": "рольлеуен", "fabulaultima": "гаҗәепультима", "witchsheart": "сихерчежүрәк", "harrypottergame": "гаррипоттеруены", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "вампирлармаскарады", "dračák": "аждаһачык", "spelljammer": "очмаҗирәбәсе", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "хронокросс", "cocttrpg": "коктрпг", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "альбертодиссея", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "ксеногир", "temtem": "темтем", "rpgforum": "rpgфорум", "shadowheartscovenant": "шэдоухартсковенант", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "патшалыккилә", "awplanet": "awпланета", "theworldendswithyou": "дөньясинеңбеләнбетә", "dragalialost": "dragalialost", "elderscroll": "өлкәнязма", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "грандия", "darkheresy": "караңгыересь", "shoptitans": "сәүдәтитаннары", "forumrpg": "форумрпг", "golarion": "голарион", "earthmagic": "җирсихере", "blackbook": "карасәйфәт", "skychildrenoflight": "күкбалалары", "gryrpg": "соррпг", "sacredgoldedition": "изгеалтынбасма", "castlecrashers": "корольлекүтәрелеше", "gothicgame": "готикуен", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "уенrpg", "prophunt": "пророныч", "starrails": "йолдызлыюллар", "cityofmist": "томанлышәһәр", "indierpg": "индирпг", "pointandclick": "күрсәтһәмбас", "emilyisawaytoo": "эмилиҗитештеюк", "emilyisaway": "эмилиюк", "indivisible": "бүленмәс", "freeside": "ирекягы", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "посткиберпанк", "deathroadtocanada": "канадагаүлемюлы", "palladium": "пал<PERSON><PERSON><PERSON><PERSON>", "knightjdr": "рыцарьмладший", "monsterhunter": "монстравчы", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "геоөстенлек", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "секиро", "monsterhunterrise": "монстрҗәяүчеләрнеңкүтәрелеше", "nier": "nier", "dothack": "dothack", "ys": "ыс", "souleater": "җанашаучы", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "бинард<PERSON><PERSON><PERSON>л<PERSON>аган<PERSON>р", "tacticalrpg": "тактикрпг", "mahoyo": "mahoyo", "animegames": "анимеуеннары", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "аллад<PERSON><PERSON>рткыч", "diluc": "diluc", "venti": "венти", "eternalsonata": "мәңгедаваз", "princessconnect": "принцесскаконнект", "hexenzirkel": "hexenzirkel", "cristales": "криста<PERSON><PERSON><PERSON>р", "vcs": "вклар", "pes": "пес", "pocketsage": "кесәакыллысы", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantинди", "dota": "дота", "madden": "мэддэн", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "еүенннар", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "киберспорт", "mlg": "mlg", "leagueofdreamers": "хыялл<PERSON><PERSON><PERSON>ыларлигасы", "fifa14": "fifa14", "midlaner": "мид<PERSON><PERSON><PERSON><PERSON><PERSON>р", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "гейминг", "overwatchleague": "overwatchлигасы", "cybersport": "киберспорт", "crazyraccoon": "акылсызенот", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "еүкүяү", "brasilgameshow": "бразилияуенкүргәзмәсе", "valorantcompetitive": "valorantярыш", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "яртыгомер", "left4dead": "солтүлгән", "left4dead2": "left4dead2", "valve": "клапан", "portal": "портал", "teamfortress2": "teamfortress2", "everlastingsummer": "мәңгелекҗәй", "goatsimulator": "козасимулятор", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "иреказаныт", "transformice": "transformice", "justshapesandbeats": "формаларһәмритмнар", "battlefield4": "battlefield4", "nightinthewoods": "урмандагытөн", "halflife2": "halflife2", "hacknslash": "хакнслэш", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "метроидваниялар", "overcooked": "артыкпешкән", "interplanetary": "планетала<PERSON><PERSON><PERSON><PERSON>а<PERSON>ы", "helltaker": "тәмугалып", "inscryption": "шиф<PERSON><PERSON><PERSON><PERSON>", "7d2d": "7к2к", "deadcells": "үлегәнкүзәнәкләр", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "ергекныйкальга", "foxhole": "окоп", "stray": "җулдәш", "battlefield": "сугышмәйданы", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "суасткаймы", "eyeb": "күзкаш", "blackdesert": "карачүл", "tabletopsimulator": "өстәлөенсимуляторы", "partyhard": "кызупкит", "hardspaceshipbreaker": "катыгалымкорабльватучы", "hades": "гадес", "gunsmith": "коралчы", "okami": "оками", "trappedwithjester": "джестербеләнкапкында", "dinkum": "чын<PERSON>ап", "predecessor": "элгәреге", "rainworld": "яңгырдөньясы", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON>", "colonysim": "колониясим", "noita": "noita", "dawnofwar": "сугышныңтаңы", "minionmasters": "миньоносталары", "grimdawn": "grimdawn", "darkanddarker": "караңгыдакараңгырак", "motox": "motox", "blackmesa": "карамеса", "soulworker": "күңелһөнәрчесе", "datingsims": "уенсимуляторлары", "yaga": "яга", "cubeescape": "кубтанкотылу", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "яңашәһәр", "citiesskylines": "шәһәрләрсилуэты", "defconheavy": "дефконавыр", "kenopsia": "бушкалганурынхисе", "virtualkenopsia": "виртуалькенопсия", "snowrunner": "карйөгерүче", "libraryofruina": "руинакитапханәсе", "l4d2": "l4d2", "thenonarygames": "бина<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ган<PERSON>р", "omegastrikers": "омегастрайкерлар", "wayfinder": "юлтабучы", "kenabridgeofspirits": "кенарухларкүпере", "placidplasticduck": "тынычпластикүрдәк", "battlebit": "battlebit", "ultimatechickenhorse": "иңсуңгытавыкат", "dialtown": "диа<PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "сиңаөченелмайсам", "catnight": "мәчекичәсе", "supermeatboy": "суперитастыегыр", "tinnybunny": "беренчебаем", "cozygrove": "уютурман", "doom": "бәла", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "код", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "апекс", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "пала<PERSON><PERSON><PERSON><PERSON><PERSON>", "earthdefenseforce": "җирнесаклаукөче", "huntshowdown": "huntshowdown", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "варз", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "үтерергәультра", "joinsquad": "командагакушыл", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "инсургенсисандсторм", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "макспейн", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "деатһстрандинг", "b4b": "б4б", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "көзгеякасы", "divisions2": "дивизияләр2", "killzone": "үтерүзонасы", "helghan": "хелган", "coldwarzombies": "салкынсугышзомбилар", "metro2033": "метро2033", "metalgear": "<PERSON>гир", "acecombat": "әйсконбат", "crosscode": "кроскод", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "снайперэлит", "modernwarfare": "модернсугыш", "neonabyss": "неонбатыру", "planetside2": "planetside2", "mechwarrior": "механмехсугышчы", "boarderlands": "чикләрүкенарында", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "тарковданкачу", "metalslug": "metalslug", "primalcarnage": "башлангычүтерү", "worldofwarships": "worldofwarships", "back4blood": "канбәйрәмекайта", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "киллер", "masseffect": "masseffect", "systemshock": "системашок", "valkyriachronicles": "валькириялетъязмалары", "specopstheline": "specopsсызык", "killingfloor2": "killingfloor2", "cavestory": "мәгарәхикәясе", "doometernal": "мәңгеләемхәләкәт", "centuryageofashes": "гасырҗанугызлары", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "мюо", "division2": "division2", "tythetasmaniantiger": "tytasmaniaюлбарысы", "generationzero": "нольбуын", "enterthegungeon": "гангеонгакер", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "колбасакеше", "ratchetandclank": "рэтчетһәмкланк", "chexquest": "chexquest", "thephantompain": "элекбулганавырту", "warface": "warface", "crossfire": "кроссфайр", "atomicheart": "атомйөрәк", "blackops3": "карадезант3", "vampiresurvivors": "вампирлардансаулыккалучылар", "callofdutybatleroyale": "callofdutybattleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "ирек", "battlegrounds": "сугышмәйданнары", "frag": "фраг", "tinytina": "кечкенәтина", "gamepubg": "gamepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "fpsуеннары", "convertstrike": "әйләндерүһөҗүме", "warzone2": "warzone2", "shatterline": "ватылганюл", "blackopszombies": "blackopszomb<PERSON>", "bloodymess": "ка<PERSON><PERSON><PERSON>ан", "republiccommando": "республиккомандо", "elitedangerous": "элитакуркыныч", "soldat": "солдат", "groundbranch": "җирботагы", "squad": "дуслар", "destiny1": "такдир1", "gamingfps": "гейминфпс", "redfall": "redfall", "pubggirl": "pubgкызы", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "кергән", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "бронякиенүзәк", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "кечкенәтинаныңмогҗизаилләре", "halo2": "halo2", "payday2": "эшхакы2", "cs16": "cs16", "pubgindonesia": "pubgиндонезия", "pubgukraine": "pubgук<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgмания", "empyrion": "эмпирион", "pubgczech": "pubgчех", "titanfall2": "titanfall2", "soapcod": "сабынтреска", "ghostcod": "арвахтреска", "csplay": "косплей", "unrealtournament": "unrealtournament", "callofdutydmz": "callofdutyдмз", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "револьверныйбөтергеч", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "нурсачагы", "killingfloor": "үтерүидән", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "неонак", "remnant": "калдык", "azurelane": "azurelane", "worldofwar": "дөньясугышы", "gunvolt": "gunvolt", "returnal": "кайтучы", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "күләгәкеше", "quake2": "quake2", "microvolts": "микровольтлар", "reddead": "үлегенкызыл", "standoff2": "standoff2", "harekat": "хәрәкәт", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "seaofthieves", "rust": "тутык", "conqueronline": "conqueronline", "dauntless": "батыр", "warships": "хәрбисуднолар", "dayofdragons": "аждаһаларкөне", "warthunder": "warthunder", "flightrising": "очканатлар", "recroom": "рекрум", "legendsofruneterra": "руналегендалары", "pso2": "pso2", "myster": "мистери", "phantasystaronline2": "phantasystaronline2", "maidenless": "кызсыз", "ninokuni": "нинокуни", "worldoftanks": "worldoftanks", "crossout": "кроссаут", "agario": "agario", "secondlife": "икенчетормыш", "aion": "aion", "toweroffantasy": "фантазияб<PERSON>шня", "netplay": "челтәргәуйнау", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "суперхайванбатыршалыгы", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "найтон<PERSON>айн", "gw2": "gw2", "tboi": "тбои", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "клубпингвин", "lotro": "lotro", "wakfu": "вакфу", "scum": "ярамаган", "newworld": "янидөнья", "blackdesertonline": "blackdesertonline", "multiplayer": "уенчылар", "pirate101": "пират101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsбаттлфронт", "karmaland": "кар<PERSON><PERSON><PERSON><PERSON><PERSON>д", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "фигрос", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "понита<PERSON>н", "3dchat": "3dчат", "nostale": "ностале", "tauriwow": "таурвау", "wowclassic": "wowклас<PERSON>ик", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "неопетлар", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "яратылышкөле", "riotmmo": "riotmmo", "silkroad": "ефәкюлы", "spiralknights": "spiralknights", "mulegend": "mulegend", "startrekonline": "стартрекончик", "vindictus": "виндиктус", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "əждаһапəйгамбəре", "grymmo": "grymmo", "warmane": "җылыкеше", "multijugador": "уртак<PERSON>йнаучылар", "angelsonline": "фәрештәләронлайн", "lunia": "луния", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcюниверсон<PERSON><PERSON><PERSON>н", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsискереспублика", "grandfantasia": "зургаллюзия", "blueprotocol": "блюпротокол", "perfectworld": "дөньякамил", "riseonline": "онлайнкүтәрел", "corepunk": "корпанк", "adventurequestworlds": "маҗаралардөньясыквест", "flyforfun": "очыпкүңелач", "animaljam": "animaljam", "kingdomofloathing": "патшалыкнефрәтләнү", "cityofheroes": "батырларшәһәре", "mortalkombat": "морталкомбат", "streetfighter": "көчурамчысы", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "батырлыкөчен", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "мультиверсус", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "виртуалсугышчы", "streetsofrage": "урамнарҗәһләме", "mkdeadlyalliance": "mkүлемсоюзы", "nomoreheroes": "герыйларыбетте", "mhr": "мгс", "mortalkombat12": "mortalkombat12", "thekingoffighters": "королькөрәшчеләр", "likeadragon": "аждаһакебек", "retrofightinggames": "ретроу<PERSON>ннар", "blasphemous": "богохульство", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "суперватыш", "mugen": "муген", "warofthemonsters": "монстрларсугышы", "jogosdeluta": "уеннарлута", "cyberbots": "киберботлар", "armoredwarriors": "көбегеҗәңчеләр", "finalfight": "финальбугыш", "poweredgear": "көчлеҗиһаз", "beatemup": "суктылаеграләр", "blazblue": "blazblue", "mortalkombat9": "мортәлкомбат9", "fightgames": "файтинг_уеннары", "killerinstinct": "үтерүинстинкты", "kingoffigthers": "сугышчыларпатшасы", "ghostrunner": "елмаючы", "chivalry2": "chivalry2", "demonssouls": "җеннәррухы", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagсугыш", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightдәвамы", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksonguены", "silksongnews": "silksongnewsъяңалыклары", "silksong": "йомшакҗыр", "undernight": "төнгеүткәреп", "typelumina": "типлумина", "evolutiontournament": "эволюциятурниры", "evomoment": "эвоһвакыт", "lollipopchainsaw": "чупачупсбызкылыч", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "bloodborne", "horizon": "офык", "pathofexile": "pathofexile", "slimerancher": "slimeранчер", "crashbandicoot": "крашбандикут", "bloodbourne": "канлыуен", "uncharted": "яңа_юллар", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationплюс", "lastofus": "ахыргысыбез", "infamous": "чатлаккы", "playstationbuddies": "playstationдуслары", "ps1": "ps1", "oddworld": "гаҗәпдөнья", "playstation5": "playstation5", "slycooper": "сләйкупер", "psp": "psp", "rabbids": "рэббидлар", "splitgate": "сплитгейт", "persona4": "persona4", "hellletloose": "сәламбулсын", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "аллаһысугыш", "gris": "грис", "trove": "хәзинә", "detroitbecomehuman": "detroitкешегәайлана", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "таңгакадәр", "touristtrophy": "туристкубогы", "lspdfr": "lspdfr", "shadowofthecolossus": "shadowofthecolossus", "crashteamracing": "крэштимрейсинг", "fivepd": "бишполиция", "tekken7": "tekken7", "devilmaycry": "иблесйөрергәмөмкин", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "плейстейшнуйнау", "samuraiwarriors": "самурайсугышчылар", "psvr2": "psvr2", "thelastguardian": "соңгысаклаучы", "soulblade": "җанкылычы", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "ирегетэзләү", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "соңгысаклаучы", "xboxone": "xboxone", "forza": "форза", "cd": "cd", "gamepass": "геймпасс", "armello": "armello", "partyanimal": "тусаучы", "warharmmer40k": "warhammer40k", "fightnightchampion": "fightnightchampion", "psychonauts": "психонавтлар", "mhw": "mhw", "princeofpersia": "персияшаһзадәсе", "theelderscrollsskyrim": "элдерскроллссәкайрим", "pantarhei": "пант<PERSON><PERSON><PERSON>й", "theelderscrolls": "килексвиткылар", "gxbox": "gxbox", "battlefront": "батт<PERSON><PERSON>ронт", "dontstarvetogether": "берликтәачекмәгез", "ori": "ори", "spelunky": "спелунки", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "йолдызларгатартыла", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "йортборгызучы", "americanmcgeesalice": "американмакгиалисасы", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "патшалыкларлигасы", "fable2": "әкият2", "xboxgamepass": "xboxгеймпасс", "undertale": "undertale", "trashtv": "чүптелевидение", "skycotl": "skycotl", "erica": "эрика", "ancestory": "нәселшәҗәрәсе", "cuphead": "cuphead", "littlemisfortune": "кечкенәханымбәхетсезлек", "sallyface": "саллифейс", "franbow": "franbow", "monsterprom": "монстрбал", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "мотоциклдар", "outerwilds": "галәмкыргыйлары", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "культягъны", "duckgame": "үрдәкуены", "thestanleyparable": "стэнлипарабель", "towerunite": "towerunite", "occulto": "яшерен", "longdrive": "озыншлыюл", "satisfactory": "канәгатьләндерерлек", "pluviophile": "яңгырсөюче", "underearth": "җиртүбәндә", "assettocorsa": "assettocorsa", "geometrydash": "геометриядаш", "kerbal": "кербал", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "кенши", "spiritfarer": "рухчы", "darkdome": "караңгыгөмбәз", "pizzatower": "пиццабашня", "indiegame": "индиуен", "itchio": "itchio", "golfit": "гольфойнайдыйк", "truthordare": "дөресмеяисәкүрсәт", "game": "гейм", "rockpaperscissors": "ташкәгазькайчы", "trampoline": "батут", "hulahoop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dare": "кыю", "scavengerhunt": "эзләнүуены", "yardgames": "ишегалдыуеннары", "pickanumber": "саннысайла", "trueorfalse": "дөресмеялган", "beerpong": "сырапонг", "dicegoblin": "дайсгоблин", "cosygames": "уюткауеннар", "datinggames": "таныш<PERSON><PERSON><PERSON>ннары", "freegame": "бу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "drinkinggames": "эчеш_уеннары", "sodoku": "судоку", "juegos": "уен<PERSON>р", "mahjong": "маджонг", "jeux": "уен<PERSON>р", "simulationgames": "симуляцияуеннары", "wordgames": "үзсүзлеуеннары", "jeuxdemots": "сүзуены", "juegosdepalabras": "сүзуеннары", "letsplayagame": "гелəадегезəле", "boredgames": "уйнардантуйдым", "oyun": "уен", "interactivegames": "интерактивуеннар", "amtgard": "amtgard", "staringcontests": "караш_ярышлары", "spiele": "уен<PERSON>р", "giochi": "уен<PERSON>р", "geoguessr": "геогессер", "iphonegames": "iphoneуеннары", "boogames": "boogames", "cranegame": "кранлыуен", "hideandseek": "кач<PERSON><PERSON><PERSON><PERSON>нау", "hopscotch": "секергеч", "arcadegames": "аркад<PERSON><PERSON><PERSON><PERSON><PERSON>р", "yakuzagames": "якузауеннары", "classicgame": "классикуен", "mindgames": "уенбашкару", "guessthelyric": "җырсүзләрентап", "galagames": "гала<PERSON>грал", "romancegame": "романсуены", "yanderegames": "яндерегеймс", "tonguetwisters": "телтөйнәтүләр", "4xgames": "4xу<PERSON>н<PERSON>р", "gamefi": "gamefi", "jeuxdarcades": "аркад<PERSON><PERSON><PERSON><PERSON><PERSON>р", "tabletopgames": "өстәлуеннары", "metroidvania": "метроидвания", "games90": "уеннар90", "idareyou": "миннәнкурыкма", "mozaa": "mozaa", "fumitouedagames": "fumitouedagamesуеннары", "racinggames": "узышуеннары", "ets2": "ets2", "realvsfake": "реальякедүгелме", "playgames": "уеннаруйна", "gameonline": "онлайнуйын", "onlinegames": "он<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>налар", "jogosonline": "он<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ннар", "writtenroleplay": "язмаролевойуен", "playaballgame": "уеныйбыз", "pictionary": "пикшенари", "coopgames": "уртак<PERSON><PERSON>ннар", "jenga": "jenga", "wiigames": "wiiуеннары", "highscore": "югарыбалл", "jeuxderôles": "рольуеннары", "burgergames": "бурге<PERSON><PERSON><PERSON>н<PERSON>р", "kidsgames": "бала<PERSON><PERSON><PERSON><PERSON><PERSON>ннары", "skeeball": "скибол", "nfsmwblackedition": "nfsmwкараверсия", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "juegodepreguntas", "gioco": "уен", "managementgame": "идарәуены", "hiddenobjectgame": "яшерелгәнәйберлеуен", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "формула1уены", "citybuilder": "шәһәртөзүче", "drdriving": "drdriving", "juegosarcade": "аркад<PERSON><PERSON>н<PERSON>р", "memorygames": "истәлекуеннары", "vulkan": "вулкан", "actiongames": "әрекәтлеуеннары", "blowgames": "өрүуеннары", "pinballmachines": "пинболл<PERSON>ашиналары", "oldgames": "искеуеннар", "couchcoop": "диванкооп", "perguntados": "сораулылар", "gameo": "уен", "lasergame": "лазертагы", "imessagegames": "imessageуеннары", "idlegames": "уенналарбуштавакыт", "fillintheblank": "буштыракларнытутыр", "jeuxpc": "уенкомпьютер", "rétrogaming": "ретроуен", "logicgames": "логикауеннары", "japangame": "япониягуены", "rizzupgame": "rizzойгыны", "subwaysurf": "метродатолкынсалу", "jeuxdecelebrite": "танылганнарныңуеннары", "exitgames": "чыгышуеннары", "5vs5": "5гә5", "rolgame": "рольуены", "dashiegames": "dashiegames", "gameandkill": "уйнапүтер", "traditionalgames": "традицио<PERSON><PERSON><PERSON>леннар", "kniffel": "книффель", "gamefps": "уенfps", "textbasedgames": "текстлыуеннар", "gryparagrafowe": "грыпараграфлар", "fantacalcio": "фантакальчо", "retrospel": "ретроспель", "thiefgame": "урлашучыуены", "lawngames": "гайлҗанашулары", "fliperama": "флиперама", "heroclix": "геройкликс", "tablesoccer": "өстәлфутболы", "tischfußball": "tischfußball", "spieleabende": "уенкичләре", "jeuxforum": "jeuxforum", "casualgames": "уен<PERSON>р", "fléchettes": "уклар", "escapegames": "качышу<PERSON>ннары", "thiefgameseries": "чурыешелуеннары", "cranegames": "кран<PERSON><PERSON>н<PERSON>ры", "játék": "уен", "bordfodbold": "өстәлфутболы", "jogosorte": "уенбәхете", "mage": "сихерче", "cargames": "маш<PERSON><PERSON><PERSON><PERSON>ннары", "onlineplay": "онл<PERSON><PERSON><PERSON><PERSON><PERSON>нау", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "уенкичләре", "pursebingos": "сумкабинголары", "randomizer": "очраклы", "msx": "msx", "anagrammi": "анаграмма", "gamespc": "уеннарpc", "socialdeductiongames": "социальдедукцияуеннары", "dominos": "домино", "domino": "домино", "isometricgames": "изометрикуеннар", "goodoldgames": "элеккечеуеннар", "truthanddare": "дөреслеккөчле", "mahjongriichi": "маһҗонгриичи", "scavengerhunts": "эзләнүуеннары", "jeuxvirtuel": "виртуальуенчык", "romhack": "ромхак", "f2pgamer": "f2pуйынчы", "free2play": "бу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>на", "fantasygame": "фэнтезиуен", "gryonline": "gryonline", "driftgame": "дрифтуены", "gamesotomes": "уеннарсөюлеләр", "halotvseriesandgames": "haloсериалларһәмуеннар", "mushroomoasis": "гөмбәоазисы", "anythingwithanengine": "моторлыберсәдә", "everywheregame": "һәрҗирдәуен", "swordandsorcery": "кылычһәмтылсым", "goodgamegiving": "яхшыуенбиру", "jugamos": "уйныйбыз", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "уенкомпьютерлары", "virgogami": "бикәгами", "gogame": "уйныйк", "jeuxderythmes": "ритмуеннары", "minaturegames": "мини<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "үзеңнеярату_гейминг", "gamemodding": "уенмоддинг", "crimegames": "җинаятьуеннары", "dobbelspellen": "добелэшсеннен", "spelletjes": "уен<PERSON>р", "spacenerf": "космоснерф", "charades": "пантомима", "singleplayer": "бердәнбер", "coopgame": "кооперативуен", "gamed": "уйнадым", "forzahorizon": "forzahorizon", "nexus": "нексус", "geforcenow": "geforcenow", "maingame": "төпуен", "kingdiscord": "кинг<PERSON>искорд", "scrabble": "скраббл", "schach": "шахмат", "shogi": "шоги", "dandd": "днд", "catan": "катан", "ludo": "лудо", "backgammon": "нарды", "onitama": "онитама", "pandemiclegacy": "пандемиямирасы", "camelup": "дөягә", "monopolygame": "монополияуены", "brettspiele": "бреттспиле", "bordspellen": "өстәлуеннары", "boardgame": "үстәлуены", "sällskapspel": "уеноюлдашлар", "planszowe": "planszowe", "risiko": "риск", "permainanpapan": "уентакталары", "zombicide": "зомбиүтерү", "tabletop": "өстәлөсте", "baduk": "бадук", "bloodbowl": "канлыкасә", "cluedo": "клуэдо", "xiangqi": "шахмат", "senet": "сенет", "goboardgame": "уентактасынауйна", "connectfour": "дүртнеуләштер", "heroquest": "геройквест", "giochidatavolo": "өстәлуеннары", "farkle": "фаркл", "carrom": "карром", "tablegames": "үстәлуеннары", "dicegames": "зар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "yatzy": "яцзы", "parchis": "парчис", "jogodetabuleiro": "өстәлуены", "jocuridesocietate": "җәмгыятьуеннары", "deskgames": "өстәлуеннары", "alpharius": "альфариус", "masaoyunları": "өстәлуеннары", "marvelcrisisprotocol": "marvelкризиспротоколы", "cosmicencounter": "галәмбуеныочрашу", "creationludique": "уенчыиҗат", "tabletoproleplay": "өстәлуеннары", "cardboardgames": "картонуенннары", "eldritchhorror": "элдричкуркыныч", "switchboardgames": "коммутаторуеннары", "infinitythegame": "infinityуены", "kingdomdeath": "патшалыкүлеме", "yahtzee": "ятзи", "chutesandladders": "баскычлардаҗыланнар", "társas": "түгәрәк", "juegodemesa": "өстәлуены", "planszówki": "планшетка", "rednecklife": "авылтормышы", "boardom": "ялыктыру", "applestoapples": "алмаларныалмаларга", "jeudesociété": "уеноякуенкоен", "gameboard": "уенеллер", "dominó": "домино", "kalah": "калах", "crokinole": "крокинол", "jeuxdesociétés": "уен<PERSON>р", "twilightimperium": "twilightimperium", "horseopoly": "атополия", "deckbuilding": "палубатөзү", "mansionsofmadness": "акылданязгантөяклэр", "gomoku": "гомоку", "giochidatavola": "өстәлуеннары", "shadowsofbrimstone": "бримстоункүләгәләре", "kingoftokyo": "токиокоролы", "warcaby": "шашка", "táblajátékok": "өстәлуеннары", "battleship": "сугышкорабы", "tickettoride": "поездгакиту", "deskovehry": "өстәлуеннары", "catán": "катан", "subbuteo": "subbuteo", "jeuxdeplateau": "өстәлуеннары", "stolníhry": "өстәлуеннары", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "уен<PERSON>р", "gesellschaftsspiele": "җәмгыятьуеннары", "starwarslegion": "starwarslegion", "gochess": "шах<PERSON>а<PERSON><PERSON><PERSON>на", "weiqi": "вэйци", "jeuxdesocietes": "уен<PERSON>р", "terraria": "terraria", "dsmp": "dsmp", "warzone": "сугышмәйданы", "arksurvivalevolved": "arksurvivalevolved", "dayz": "көннәр", "identityv": "identityv", "theisle": "утрау", "thelastofus": "соңгыбезнеңнәрдән", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "субнаутика", "tombraider": "томбрейдер", "callofcthulhu": "ктулхучакыруы", "bendyandtheinkmachine": "бендигәнечернилмашинасы", "conanexiles": "conanexiles", "eft": "eft", "amongus": "арабызда", "eco": "эко", "monkeyisland": "маймылутравы", "valheim": "valheim", "planetcrafter": "планетаясаучы", "daysgone": "күнкитте", "fobia": "фобия", "witchit": "күрәзәсең", "pathologic": "патологик", "zomboid": "зомбоид", "northgard": "northgard", "7dtd": "7көн7төн", "thelongdark": "озыкараңгы", "ark": "арк", "grounded": "җиргәтагалган", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "үгиата", "dontstarve": "ачыкмагыз", "eternalreturn": "мәңгелеккайту", "pathoftitans": "титаннарюлы", "frictionalgames": "уенчыраштыру", "hexen": "сихерчеләр", "theevilwithin": "явызлыкэчтә", "realrac": "чынрасизм", "thebackrooms": "арткабүлмәләр", "backrooms": "арткабүлмәләр", "empiressmp": "empiressmp", "blockstory": "блоксюжет", "thequarry": "кар<PERSON><PERSON>р", "tlou": "tlou", "dyinglight": "үлүеяктысы", "thewalkingdeadgame": "үлеләруены", "wehappyfew": "безазбыз", "riseofempires": "империяләрүсеше", "stateofsurvivalgame": "stateofsurvivalуены", "vintagestory": "борынгызаман", "arksurvival": "arkяшәү", "barotrauma": "баротравма", "breathedge": "сулышалыйгыз", "alisa": "алиса", "westlendsurvival": "westlendяшәү", "beastsofbermuda": "бермудахайваннары", "frostpunk": "фростпанк", "darkwood": "кара<PERSON><PERSON><PERSON>ан", "survivalhorror": "курылмаслыккоркыныч", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "бушпоезд", "lifeaftergame": "уеннансоңгытормыш", "survivalgames": "саклануеннары", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "буминемсугыш", "scpfoundation": "scpфонды", "greenproject": "яшелпроект", "kuon": "куон", "cryoffear": "куркудансалаулары", "raft": "сал", "rdo": "рсб", "greenhell": "яшелҗәһәннәм", "residentevil5": "резидентэвил5", "deadpoly": "үлепполи", "residentevil8": "residentevil8", "onironauta": "онейронавт", "granny": "әби", "littlenightmares2": "кечкенәтөнгекошмарлар2", "signalis": "сигна<PERSON><PERSON><PERSON>р", "amandatheadventurer": "аманда<PERSON>əяхəтче", "sonsoftheforest": "урманул<PERSON><PERSON><PERSON><PERSON>б<PERSON>з", "rustvideogame": "rust<PERSON><PERSON><PERSON>н", "outlasttrials": "сынаулардансоңгакалучы", "alienisolation": "читләрялгызлыгы", "undawn": "таңалдында", "7day2die": "7көнкалу2үлү", "sunlesssea": "кояшсызdiңгез", "sopravvivenza": "sopravvivenza", "propnight": "пропночь", "deadisland2": "үлегенутрау2", "ikemensengoku": "икемэнсенгоку", "ikemenvampire": "иркәксулукимвампир", "deathverse": "үлемгаләме", "cataclysmdarkdays": "афәтнеңкараңгыкөннәре", "soma": "сомасы", "fearandhunger": "курыкуһәмачлык", "stalkercieńczarnobyla": "сталкерчернобыльнеңкүләгәсе", "lifeafter": "яшәүдәвамы", "ageofdarkness": "караңгылыкчоры", "clocktower3": "сәгатьманарасы3", "aloneinthedark": "караңгыдаялгыз", "medievaldynasty": "урта咸асырдинастиясе", "projectnimbusgame": "projectnimbusgame", "eternights": "мәңгелектөннәр", "craftopia": "крафтопия", "theoutlasttrials": "outlastсынаулары", "bunker": "бу<PERSON><PERSON><PERSON><PERSON>", "worlddomination": "дөньяныяулаубыз", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officiоассассинорум", "necron": "некрон", "wfrp": "вфрп", "dwarfslayer": "cәрелеруткыргыч", "warhammer40kcrush": "warhammer40kгашыйк", "wh40": "wh40", "warhammer40klove": "warhammer40kгашыйк", "warhammer40klore": "warhammer40ктарихы", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "вархаммер40к", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "интикам", "ilovesororitas": "сорорителарнысөям", "ilovevindicare": "илаввиндикаре", "iloveassasinorum": "минассасиннарныяратам", "templovenenum": "вакытлыксөюсаны", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "үтерүчеләрһөнәре", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40мең", "tetris": "тетрис", "lioden": "lioden", "ageofempires": "империяләрзаманы", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "икеберәйбергә", "wingspan": "канатҗәю", "terraformingmars": "марсныяшелкайтыру", "heroesofmightandmagic": "геройларкөчһәмтылсым", "btd6": "btd6", "supremecommander": "югарыкомандир", "ageofmythology": "мифологиячоры", "args": "аргументлар", "rime": "ихтилаф", "planetzoo": "планетазоо", "outpost2": "постик2", "banished": "сөргенгә", "caesar3": "цезарь3", "redalert": "кызылтревога", "civilization6": "цивилизация6", "warcraft2": "warcraft2", "commandandconquer": "идарәитһәмяулап", "warcraft3": "warcraft3", "eternalwar": "мәңгелексугыш", "strategygames": "стратегияуеннары", "anno2070": "anno2070", "civilizationgame": "цивилизацияуены", "civilization4": "цивилизация4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "спора", "totalwar": "тулысугыш", "travian": "travian", "forts": "фортлар", "goodcompany": "яхшыкомпания", "civ": "цивилизация", "homeworld": "туганҗир", "heidentum": "хайдентум", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "җилданданчык", "forthekings": "батырларөчен", "realtimestrategy": "реальвакытстратегиясе", "starctaft": "starctaft", "sidmeierscivilization": "сидмейерцивилизациясе", "kingdomtwocrowns": "патшалыкикетаҗ", "eu4": "eu4", "vainglory": "тәкәббер", "ww40k": "ww40k", "godhood": "илаһлык", "anno": "елъязмасы", "battletech": "battletech", "malifaux": "малифо", "w40k": "w40k", "hattrick": "хеттрик", "davesfunalgebraclass": "дэйвныңкүңеллеалгебрадәресе", "plagueinc": "чу<PERSON><PERSON><PERSON><PERSON>к", "theorycraft": "теорияләштерү", "mesbg": "mesbg", "civilization3": "цивилизация3", "4inarow": "4рәттә", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "алга<PERSON>угышлар", "ageofempires2": "ageofempires2", "disciples2": "шәкертләр2", "plantsvszombies": "үсемлекләрзомбиларгакаршы", "giochidistrategia": "стратегияуеннары", "stratejioyunları": "стратегияуеннары", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "могҗизаларзаманы", "dinosaurking": "динозаврпатшасы", "worldconquest": "дөньяяулап", "heartsofiron4": "heartsofiron4", "companyofheroes": "геройларкомпаниясе", "battleforwesnoth": "уэснотһөчензугыш", "aoe3": "aoe3", "forgeofempires": "империяләручагы", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "казказүрдәк", "phobies": "фобияләр", "phobiesgame": "куркулароенгызатар", "gamingclashroyale": "уенклашрояль", "adeptusmechanicus": "адептусмеханикус", "outerplane": "тышкыяссылык", "turnbased": "чира<PERSON><PERSON><PERSON><PERSON>", "bomberman": "бомберман", "ageofempires4": "ageofempires4", "civilization5": "цивилизация5", "victoria2": "victoria2", "crusaderkings": "crusaderkings", "cultris2": "cultris2", "spellcraft": "сихертамалыгы", "starwarsempireatwar": "starwarsимперияугышта", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "стратегия", "popfulmail": "электронпочтагламур", "shiningforce": "ялтыравычлыкөч", "masterduel": "мастердуэль", "dysonsphereprogram": "дайсонсферасыпрограммасы", "transporttycoon": "транспортмагнаты", "unrailed": "тимергеҗүрми", "magicarena": "магикарена", "wolvesville": "wolvesville", "ooblets": "ублетлар", "planescapetorment": "planescapetorment", "uplandkingdoms": "югарыпатшалыклар", "galaxylife": "галактикатормышы", "wolvesvilleonline": "ширханнаравылыонлайн", "slaythespire": "slaythesp<PERSON><PERSON><PERSON><PERSON>у", "battlecats": "сугышмәчеләре", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "грантуризмо", "needforspeed": "тизлеккирәк", "needforspeedcarbon": "нидфорспидкарбон", "realracing3": "реалрейсинг3", "trackmania": "trackmania", "grandtourismo": "грандтуризмо", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "sims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "сыдыр", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "элисакотлыкайта", "darkhorseanthology": "караттайантология", "phasmophobia": "фасмофобия", "fivenightsatfreddys": "бишкичфреддида", "saiko": "сайко", "fatalframe": "үлемфрейм", "littlenightmares": "кечкенәтөнгикошмарлар", "deadrising": "үлеләртерелү", "ladydimitrescu": "ледидимитреску", "homebound": "өйдәгеләр", "deadisland": "үлеутау", "litlemissfortune": "litlemissfortune", "projectzero": "проектноль", "horory": "куркыныч", "jogosterror": "уенкурку", "helloneighbor": "саламкүрше", "helloneighbor2": "салампадыш2", "gamingdbd": "гейминдбд", "thecatlady": "мәчеәбисе", "jeuxhorreur": "куркынычуеннар", "horrorgaming": "куркынычуеннар", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "кешелеккәкаршыкарталар", "cribbage": "криббидж", "minnesotamtg": "миннесотаmtg", "edh": "edh", "monte": "монте", "pinochle": "пинокль", "codenames": "кодисемнәр", "dixit": "диксит", "bicyclecards": "велосипедкарталар", "lor": "лор", "euchre": "эв<PERSON>р", "thegwent": "gwent", "legendofrunetera": "legendofrun<PERSON>a", "solitaire": "солитер", "poker": "покер", "hearthstone": "hearthstone", "uno": "уно", "schafkopf": "шафкопф", "keyforge": "keyforge", "cardtricks": "картфокуслары", "playingcards": "уенкарталары", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "челтерчыгарган", "gwent": "гвинт", "metazoo": "метазоо", "tradingcards": "саудәкарточкалары", "pokemoncards": "покемонкарталары", "fleshandbloodtcg": "карта<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>у", "sportscards": "спорткарточкалар", "cardfightvanguard": "карточныйсугышвангард", "duellinks": "duellinks", "spades": "️", "warcry": "сугышавазы", "digimontcg": "digimontcg", "toukenranbu": "тоукенр<PERSON><PERSON><PERSON>у", "kingofhearts": "йөрәкләрпатшасы", "truco": "truco", "loteria": "лотерея", "hanafuda": "ханафуда", "theresistance": "каршылык", "transformerstcg": "transformerstcg", "doppelkopf": "доппелькопф", "yugiohcards": "yugioкарточкалары", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "дуэльдиск", "yugiohgame": "югио<PERSON>уены", "darkmagician": "караеһирче", "blueeyeswhitedragon": "зәңгәрсу_күзле_ак_аждаһа", "yugiohgoat": "югиохбатыр", "briscas": "бриска", "juegocartas": "карта<PERSON><PERSON><PERSON>ы", "burraco": "буррако", "rummy": "рамми", "grawkarty": "гравкар<PERSON><PERSON><PERSON><PERSON>р", "dobble": "доббле", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "карт<PERSON><PERSON><PERSON><PERSON>нары", "mtgjudge": "mtgjudge", "juegosdecartas": "карт<PERSON><PERSON><PERSON><PERSON>нары", "duelyst": "дуэлист", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgалдантикомандир", "kartenspiel": "kartenspiel", "carteado": "carteado", "sueca": "sueca", "beloteonline": "белото<PERSON><PERSON><PERSON><PERSON>н", "karcianki": "карточкалар", "battlespirits": "рухани<PERSON><PERSON>гышлар", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "карта<PERSON><PERSON><PERSON><PERSON><PERSON>ны", "žolíky": "жолик", "facecard": "йөзкарта", "cardfight": "картал<PERSON><PERSON>белəнсугыш", "biriba": "biriba", "deckbuilders": "палубаторгызучылар", "marvelchampions": "marvelчемпионнары", "magiccartas": "сихерлекарталар", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "скипбо", "unstableunicorns": "тотрыксызборҗайлар", "cyberse": "киберсе", "classicarcadegames": "классикаркадауеннары", "osu": "осу", "gitadora": "gitadora", "dancegames": "биюуен<PERSON>ры", "fridaynightfunkin": "җомгакичфанкин", "fnf": "fnf", "proseka": "просека", "projectmirai": "проектмирай", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "гита<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "клонгерой", "justdance": "биюгенә", "hatsunemiku": "хацунэмику", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "үлгәннәрнетырпайт", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "биюүзәге", "rhythmgamer": "ритмуйнаучысы", "stepmania": "stepmania", "highscorerythmgames": "югарыритмуеннары", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "онгеки", "soundvoltex": "soundvoltex", "rhythmheaven": "ритмҗәннәте", "hypmic": "hypmic", "adanceoffireandice": "утһәмбоздансбию", "auditiononline": "онлайнкастинг", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "ритмуеннары", "cryptofthenecrodancer": "криптонекроданскорольы", "rhythmdoctor": "ритм<PERSON><PERSON><PERSON><PERSON>б", "cubing": "кубрубик", "wordle": "вордл", "teniz": "өннәр", "puzzlegames": "башваткычуеннары", "spotit": "табыгыз", "rummikub": "раммикуб", "blockdoku": "блокдоку", "logicpuzzles": "логикауеннары", "sudoku": "судоку", "rubik": "rubik", "brainteasers": "башваткычлар", "rubikscube": "рубиккубы", "crossword": "кроссворд", "motscroisés": "сүзләртабышмагы", "krzyżówki": "кроссвордлар", "nonogram": "нонограмма", "bookworm": "китапбөҗәге", "jigsawpuzzles": "пазллар", "indovinello": "табышмак", "riddle": "жомак", "riddles": "җомаклар", "rompecabezas": "башваткыч", "tekateki": "серләретабыш", "inside": "ичендә", "angrybirds": "ачулыкошлар", "escapesimulator": "качышсимуляторы", "minesweeper": "минаэзләүче", "puzzleanddragons": "пазлһәмаждаһалар", "crosswordpuzzles": "крестсүзләр", "kurushi": "курыши", "gardenscapesgame": "gardenscapesуены", "puzzlesport": "табышмакспорт", "escaperoomgames": "качуыбүлмәсеуеннары", "escapegame": "качышуены", "3dpuzzle": "3дпазл", "homescapesgame": "homescapesуены", "wordsearch": "сүзләрэзләү", "enigmistica": "энигмистика", "kulaworld": "куладөнья", "myst": "серлелек", "riddletales": "табышмакхикәяләр", "fishdom": "fishdom", "theimpossiblequiz": "мөмкинбулмаганквиз", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "матч3уен", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "катамаридамаси", "kwirky": "квирки", "rubikcube": "рубиккубы", "cuborubik": "кубикрубик", "yapboz": "yapboz", "thetalosprinciple": "талоспринципы", "homescapes": "өйуеннары", "puttputt": "миниатюргольф", "qbert": "qbert", "riddleme": "табышмакәйт", "tycoongames": "магнат<PERSON><PERSON>ннары", "cubosderubik": "рубиккубиклары", "cruciverba": "кроссворд", "ciphers": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON>", "rätselwörter": "rätselsüzlär", "buscaminas": "минасэзлэү", "puzzlesolving": "башваткычлар", "turnipboy": "ша<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "табышмакshot", "nobodies": "һичкемнәр", "guessing": "фаразлау", "nonograms": "нонограммалар", "kostkirubika": "кубикрубиккостюмы", "crypticcrosswords": "криптиккроссвордлар", "syberia2": "syberia2", "puzzlehunt": "уенэзләү", "puzzlehunts": "табышмакэзләү", "catcrime": "мәчегенәят", "quebracabeça": "баштаватырык", "hlavolamy": "баштабанкатыру", "poptropica": "poptropica", "thelastcampfire": "соңгыучак", "autodefinidos": "автоүзаңатасвирлаучылар", "picopark": "пикопарк", "wandersong": "сәяхәтҗыры", "carto": "карто", "untitledgoosegame": "исемсезказойыны", "cassetête": "башватучы", "limbo": "лимбо", "rubiks": "рубик", "maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinykin": "тин<PERSON>ин", "rubikovakostka": "рубиккубы", "speedcube": "тизкубик", "pieces": "кисәкләр", "portalgame": "порталуены", "bilmece": "билмәче", "puzzelen": "пазллар", "picross": "пикросс", "rubixcube": "рубиккубы", "indovinelli": "табышмаклар", "cubomagico": "кубикрубик", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "twistedwonderland", "monopoly": "монополия", "futurefight": "киләчәкөчен", "mobilelegends": "мобайллегендс", "brawlstars": "brawlstars", "brawlstar": "бравлстар", "coc": "coc", "lonewolf": "ялгызбүре", "gacha": "гача", "wr": "wr", "fgo": "fgo", "bitlife": "бит<PERSON><PERSON><PERSON><PERSON>", "pikminbloom": "пикмин<PERSON>лум", "ff": "бу", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "алхимикйолдызлар", "stateofsurvival": "яшәүхәле", "mycity": "минемшәһәр", "arknights": "arknights", "colorfulstage": "төслееткән", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "доккансугышы", "fategrandorder": "fategrandorder", "hyperfront": "гиперфронт", "knightrun": "рыцарьйөгереше", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "футболсугышы", "a3": "a3", "phonegames": "телефонуеннары", "kingschoice": "патша<PERSON>айлавы", "guardiantales": "сакчылархикәяләре", "petrolhead": "машинаяратучы", "tacticool": "тактикуль", "cookierun": "cookierun", "pixeldungeon": "пиксельзиндан", "arcaea": "arcaea", "outoftheloop": "белмичәкалдым", "craftsman": "оста", "supersus": "бикшикле", "slowdrive": "әкренйөрү", "headsup": "игътибар", "wordfeud": "сүзуены", "bedwars": "бедварс", "freefire": "фрифаер", "mobilegaming": "мобиль<PERSON>ен", "lilysgarden": "лилияб<PERSON><PERSON>часы", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "командасугышытактикасы", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobileуены", "thearcana": "теаркана", "8ballpool": "8<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>д", "emergencyhq": "авариялекштаб", "enstars": "enstars", "randonautica": "рандонавтика", "maplestory": "maplestory", "albion": "альбион", "hayday": "көнбуе", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "калтырануһәмборгалану", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "вакытханбикәсе", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "фейерверк", "disneymirrorverse": "диснеймирроверс", "pocketlove": "кесәяшк", "androidgames": "and<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ы", "criminalcase": "крими<PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "ашпешүҗенлеге", "dokkan": "докан", "aov": "aov", "triviacrack": "викторинаярылышы", "leagueofangels": "фәрештәләрлигасы", "lordsmobile": "lordsmobile", "tinybirdgarden": "нәнимошбакчасы", "gachalife": "гача<PERSON><PERSON><PERSON><PERSON>", "neuralcloud": "нейральболыт", "mysingingmonsters": "минемҗырлаучыхайваннарым", "nekoatsume": "мәчеләржыю", "bluearchive": "көкархив", "raidshadowlegends": "raidshadowlegends", "warrobots": "warробослар", "mirrorverse": "көзгегаләм", "pou": "pou", "warwings": "сугышканатлары", "fifamobile": "фифамобайл", "mobalegendbangbang": "mobalegendbangbang", "evertale": "хикәя", "futime": "күңелле", "antiyoy": "антий<PERSON>й", "apexlegendmobile": "apexlegendмоб<PERSON>йл", "ingress": "керү", "slugitout": "дәгъвалашу", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "хайваннардуслар", "gameofsultans": "солтаннаруены", "arenabreakout": "аренаданчыгу", "wolfy": "вольфи", "runcitygame": "шәһеруены", "juegodemovil": "уенмобиль", "avakinlife": "аваkinгомер", "kogama": "kogama", "mimicry": "мимикрия", "blackdesertmobile": "карадәшер_мобайл", "rollercoastertycoon": "роллеркостертайкун", "grandchase": "grandchase", "bombmebrasil": "минеяралабразилия", "ldoe": "ldoe", "legendonline": "легенд<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "otomegame": "отомеуен", "mindustry": "mindustry", "callofdragons": "драконнарчакыруы", "shiningnikki": "ялтыравыкникки", "carxdriftracing2": "carxдрифтузышы2", "pathtonowhere": "юлюк", "sealm": "сыйлм", "shadowfight3": "көләгәләрсугышы3", "limbuscompany": "limbuscompany", "demolitionderby3": "яңгыратыпватабыз3", "wordswithfriends2": "дусларбеләнсүзләр2", "soulknight": "руханирыцарь", "purrfecttale": "мыраутәмгәзәп", "showbyrock": "рокныкүрсәт", "ladypopular": "популярханым", "lolmobile": "ярыйгына", "harvesttown": "урлыкшәһәре", "perfectworldmobile": "идеалдөньямобиль", "empiresandpuzzles": "империяләрһәмголоволомкалар", "empirespuzzles": "империяпазллары", "dragoncity": "аждаһашәһәре", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "күтәк", "littlenightmare": "кечкенәкошмартөш", "aethergazer": "aethergazer", "mudrunner": "балчыкйөгерүче", "tearsofthemis": "мисирныңкүзяшьләре", "eversoul": "мәңгелекҗан", "gunbound": "gunbound", "gamingmlbb": "гейминглегенды", "dbdmobile": "dbdmobile", "arknight": "арк<PERSON><PERSON>т", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "зомбикачаклар", "eveechoes": "eveechoes", "jogocelular": "уенмобиль", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "ашпешиәни", "cabalmobile": "cabalmobile", "streetfighterduel": "урамсугышчысыдуэль", "lesecretdhenri": "һанригасере", "gamingbgmi": "гейминбгми", "girlsfrontline": "кызларфронты", "jurassicworldalive": "юрапаркызындатере", "soulseeker": "җанэзләүче", "gettingoverit": "үтепбара", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "айчәйхикәясе", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "уеннармобиль", "legendofneverland": "легендаһичбулмаганил", "pubglite": "pubglite", "gamemobilelegends": "уенмобайльлегендалар", "timeraiders": "вакытбасыпалучылар", "gamingmobile": "мобильгеймер", "marvelstrikeforce": "marvelстрайкфорс", "thebattlecats": "thebattlecats", "dnd": "д<PERSON><PERSON>", "quest": "квест", "giochidiruolo": "рольуеннары", "dnd5e": "dnd5e", "rpgdemesa": "өстәлролевойуеннары", "worldofdarkness": "караңгылыкдөньясы", "travellerttrpg": "сәяхәтчеttrpg", "2300ad": "2300ел", "larp": "рольлеуенлар", "romanceclub": "романсклубы", "d20": "d20", "pokemongames": "покемонуеннары", "pokemonmysterydungeon": "pokemonserlegängöme", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "покемонго", "pokemonred": "pokemonкызыл", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON><PERSON><PERSON>нд<PERSON><PERSON>р", "lipeep": "lipeep", "porygon": "порыгон", "pokemonunite": "pokemonберләшә", "entai": "энтай", "hypno": "гипно", "empoleon": "эмполеон", "arceus": "арсеус", "mewtwo": "мьюту", "paldea": "палдеа", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "чатут", "pikachu": "пикачу", "roxie": "рокси", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonşәрәфә", "ashketchum": "әшкетчум", "gengar": "gengar", "natu": "табигый", "teamrocket": "тимракета", "furret": "фуррет", "magikarp": "магика<PERSON>п", "mimikyu": "мимикю", "snorlax": "снорлакс", "pocketmonsters": "cибалаымаҗаннар", "nuzlocke": "нузлок", "pokemonplush": "покемонйомшаклары", "teamystic": "командамистик", "pokeball": "покебол", "charmander": "ча<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "яктырткан", "shinypokemon": "ялтыравыкпокемон", "mesprit": "mesprit", "pokémoni": "покемоны", "ironhands": "тимердәйкуллар", "kabutops": "кабутопс", "psyduck": "псайдак", "umbreon": "умбреон", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "пиплуп", "pokemonsleep": "покемонйокы", "heyyoupikachu": "эйсинпикачу", "pokémonmaster": "покемонмастер", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "балаларһәмпокемон", "pokemonsnap": "покемонфото", "bulbasaur": "бульбазавр", "lucario": "lucario", "charizar": "харизма", "shinyhunter": "блестящийаучы", "ajedrez": "шахмат", "catur": "шахмат", "xadrez": "шахмат", "scacchi": "шахмат", "schaken": "шахмат", "skak": "скак", "ajedres": "aje<PERSON>s", "chessgirls": "шахматкызлары", "magnuscarlsen": "магнускарлсен", "worldblitz": "дөньяблиц", "jeudéchecs": "шахмат", "japanesechess": "японшахматы", "chinesechess": "кыта<PERSON><PERSON>ахматы", "chesscanada": "шахматканада", "fide": "фиде", "xadrezverbal": "xadrezverbal", "openings": "ачу<PERSON><PERSON>р", "rook": "ладья", "chesscom": "chesscom", "calabozosydragones": "зинданнарһәмаждаһалар", "dungeonsanddragon": "подземельеһәмаждаһа", "dungeonmaster": "зинданостасы", "tiamat": "тиамат", "donjonsetdragons": "донжонларһәмаждаһалар", "oxventure": "oxventure", "darksun": "караяңгыркояш", "thelegendofvoxmachina": "воксмачиналегендасы", "doungenoanddragons": "элекдәэлекһәмөйрәтү", "darkmoor": "кара<PERSON><PERSON><PERSON>ан", "minecraftchampionship": "minecraftчемпионаты", "minecrafthive": "майнкрафтоя", "minecraftbedrock": "майнкрафтбедрок", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "майнтест", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "майнкрафтмодлары", "mcc": "м<PERSON>ч", "candleflame": "шәмут", "fru": "фру", "addons": "өстәмәләр", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "скайблок", "minecraftpocket": "minecraftкесә", "minecraft360": "minecraft360", "moddedminecraft": "модда<PERSON><PERSON><PERSON><PERSON><PERSON>craft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "икеарада", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "майнкрафтшәһәре", "pcgamer": "pcуйнаучы", "jeuxvideo": "уен<PERSON>р", "gambit": "гамбит", "gamers": "геймер<PERSON><PERSON>р", "levelup": "үсешкә", "gamermobile": "геймермобайл", "gameover": "уенбетте", "gg": "gg", "pcgaming": "компьютеруеннары", "gamen": "гамен", "oyunoynamak": "уйнауйнау", "pcgames": "пкуеннары", "casualgaming": "гади<PERSON><PERSON><PERSON><PERSON><PERSON>ар", "gamingsetup": "геймин<PERSON><PERSON><PERSON><PERSON>у", "pcmasterrace": "pcустасы", "pcgame": "pcуен", "gamerboy": "геймер", "vrgaming": "врлебем", "drdisrespect": "дрдисреспект", "4kgaming": "4kуен", "gamerbr": "гамер", "gameplays": "уен<PERSON>р", "consoleplayer": "консольчы", "boxi": "boxi", "pro": "про", "epicgamers": "эпикгеймерлар", "onlinegaming": "онл<PERSON>йнуен", "semigamer": "уенчыярым", "gamergirls": "уенчыкызлар", "gamermoms": "геймермәмиләр", "gamerguy": "геймердегет", "gamewatcher": "уенкүзәтчесе", "gameur": "уенчы", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "гeймepчалар", "otoge": "отоге", "dedsafio": "dedsafio", "teamtryhard": "командатырышучы", "mallugaming": "маллуг<PERSON><PERSON>минг", "pawgers": "лапалылар", "quests": "квестлар", "alax": "алах", "avgn": "avgn", "oldgamer": "кар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>мер", "cozygaming": "уютгейминг", "gamelpay": "геймплей", "juegosdepc": "компьютеруеннары", "dsswitch": "dsалыштыру", "competitivegaming": "конкурентуенчы", "minecraftnewjersey": "minecraftньюджерси", "faker": "ялганчы", "pc4gamers": "компөчен4геймерлар", "gamingff": "геймин<PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "гетеросексуалгейминг", "gamepc": "уенпк", "girlsgamer": "кызларгеймер", "fnfmods": "fnfмодлар", "dailyquest": "көндәлекквест", "gamegirl": "геймеркыз", "chicasgamer": "гeймeруызлар", "gamesetup": "уеннарныкөйләү", "overpowered": "читерский", "socialgamer": "социальгеймер", "gamejam": "гейм_җәм", "proplayer": "профессионалигрок", "roleplayer": "рольуйнаучы", "myteam": "минемкоманда", "republicofgamers": "геймерларреспубликасы", "aorus": "aorus", "cougargaming": "кугар<PERSON><PERSON><PERSON><PERSON>инг", "triplelegend": "өчтапкырлегенда", "gamerbuddies": "геймердуслар", "butuhcewekgamers": "гәмергакызларкирәк", "christiangamer": "христиа<PERSON><PERSON><PERSON>научы", "gamernerd": "геймернерд", "nerdgamer": "нердгеймер", "afk": "afk", "andregamer": "andregamer", "casualgamer": "гадиг<PERSON><PERSON><PERSON><PERSON>р", "89squad": "89отряд", "inicaramainnyagimana": "ничекуйнарга", "insec": "килешмәгәнлек", "gemers": "гай<PERSON><PERSON><PERSON><PERSON><PERSON>р", "oyunizlemek": "уенкарау", "gamertag": "геймертег", "lanparty": "ланпарти", "videogamer": "геймер", "wspólnegranie": "бергәуйнау", "mortdog": "мортдог", "playstationgamer": "playstationуйнаучы", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "сәламәтгеймер", "gtracing": "gtracing", "notebookgamer": "дәфтәргеймер", "protogen": "протоген", "womangamer": "уенчыкыз", "obviouslyimagamer": "әлбәттәгеймер", "mario": "марио", "papermario": "папермарио", "mariogolf": "мариогольф", "samusaran": "самусаран", "forager": "чүпләүче", "humanfallflat": "humanfallflat", "supernintendo": "супернинтендо", "nintendo64": "nintendo64", "zeroescape": "нольчыгыш", "waluigi": "валуиджи", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomузыка", "sonicthehedgehog": "соникёж", "sonic": "соник", "fallguys": "фоллгайс", "switch": "алыштыру", "zelda": "зельда", "smashbros": "smashbros", "legendofzelda": "зелдалегендасы", "splatoon": "splatoon", "metroid": "метроид", "pikmin": "пикмин", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "мегаман", "majorasmask": "majorasmask", "mariokartmaster": "мариокартостазы", "wii": "wii", "aceattorney": "эйсаторни", "ssbm": "ssbm", "skychildrenofthelight": "күкбалалары", "tomodachilife": "томодачитормыш", "ahatintime": "вакытэле", "tearsofthekingdom": "патшалыкныңяшьләре", "walkingsimulators": "йөрүсимуляторлары", "nintendogames": "nintendoуеннары", "thelegendofzelda": "зельдалегендасы", "dragonquest": "dragonquest", "harvestmoon": "урактае", "mariobros": "мари<PERSON><PERSON><PERSON><PERSON><PERSON>", "runefactory": "runefactory", "banjokazooie": "банджоказуи", "celeste": "селесте", "breathofthewild": "кыргыйҗирныңсулышы", "myfriendpedro": "дустымпедро", "legendsofzelda": "зелдалегендалары", "donkeykong": "донкиконг", "mariokart": "мариокарт", "kirby": "кирби", "51games": "51уен", "earthbound": "җиргәбәйле", "tales": "хикәяләр", "raymanlegends": "raymanlegends", "luigismansion": "луиджиныңйорты", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "супермариобраслар", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashультимат", "nintendochile": "nintendoчили", "tloz": "tloz", "trianglestrategy": "өчпочмакстратегиясе", "supermariomaker": "супермариоясаучы", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "супермарио64", "conkersbadfurday": "конкерсначардума", "nintendos": "nintendolar", "new3ds": "яңа3ds", "donkeykongcountry2": "донкиконгиле2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "мариоһәмсоник", "banjotooie": "банджотуи", "nintendogs": "nintendogs", "thezelda": "зельда", "palia": "palia", "marioandluigi": "мариобелэнлуиджи", "mariorpg": "мариорпг", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "риклаган", "ahri": "ahri", "illaoi": "ила<PERSON>й", "aram": "арам", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendsлар", "urgot": "ургот", "zyra": "зыра", "redcanids": "кызылканидлар", "vanillalol": "ванильлол", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "лигалегенд", "tốcchiến": "тизюгереш", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "рекламата<PERSON>у", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsиспания", "aatrox": "aatrox", "euw": "эй", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "кайл", "samira": "самира", "akali": "акали", "lunari": "лунари", "fnatic": "fnatic", "lollcs": "лолкс", "akshan": "а<PERSON><PERSON><PERSON><PERSON>", "milio": "мильон", "shaco": "shaco", "ligadaslegendas": "легендаларлигасы", "gaminglol": "гейминглол", "nasus": "насус", "teemo": "тимо", "zedmain": "зедмейн", "hexgates": "гексишлыксалары", "hextech": "hextech", "fortnitegame": "fortni<PERSON>uen<PERSON>", "gamingfortnite": "гейминфор<PERSON>найт", "fortnitebr": "fortnitebr", "retrovideogames": "ретроу<PERSON>ннар", "scaryvideogames": "куркынычвидеоуеннар", "videogamemaker": "видеоуенясаучы", "megamanzero": "megamanzero", "videogame": "видеоуен", "videosgame": "видеоу<PERSON><PERSON><PERSON>р", "professorlayton": "профессорлэйтон", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "аркада<PERSON><PERSON>р", "acnh": "acnh", "puffpals": "пуффдуслар", "farmingsimulator": "фермасимуляторы", "robloxchile": "robloxçili", "roblox": "roblo<PERSON>", "robloxdeutschland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "robloxтатарча", "erlc": "erlc", "sanboxgames": "кумта<PERSON><PERSON><PERSON>ннары", "videogamelore": "уенэшләнүе", "rollerdrome": "роллердром", "parasiteeve": "паразитканун", "gamecube": "геймкьюб", "starcraft2": "starcraft2", "duskwood": "караңгыурман", "dreamscape": "төшпейзажы", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "грандчурлык", "deadspace": "үлегенурын", "amordoce": "татлыяр", "videogiochi": "видеоу<PERSON><PERSON><PERSON>р", "theoldrepublic": "искереспублика", "videospiele": "видеоу<PERSON><PERSON><PERSON>р", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "маҗаралыуеннар", "wolfenstein": "wolfenstein", "actionadventure": "боевикприключение", "storyofseasons": "елъязмалары", "retrogames": "ретроу<PERSON>ннар", "retroarcade": "ретроаркада", "vintagecomputing": "борынгыкомпьютерлар", "retrogaming": "ретроу<PERSON>ннар", "vintagegaming": "элеккечеуеннар", "playdate": "уйнаукөне", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "ба<PERSON><PERSON><PERSON><PERSON>с", "injustice2": "гаделсезлек2", "shadowthehedgehog": "көләгәеж", "rayman": "rayman", "skygame": "күкуены", "zenlife": "тынычтормыш", "beatmaniaiidx": "beatmaniaiidx", "steep": "тик", "mystgames": "серле<PERSON><PERSON><PERSON><PERSON>р", "blockchaingaming": "блокчейнуены", "medievil": "медивил", "consolegaming": "консольуйыны", "konsolen": "консольләр", "outrun": "узыпчыгу", "bloomingpanic": "чәчәкатупаника", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "уенкурку", "monstergirlquest": "monsterкызларквесты", "supergiant": "супергигант", "disneydreamlightvalle": "диснейдримлайтвелли", "farmingsims": "фермерсимнар", "juegosviejos": "искеуеннар", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "интерактивхикәя", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "сөюче_акыллар", "visualnovel": "визуальроман", "visualnovels": "визуальроманнар", "rgg": "rgg", "shadowolf": "карасылабүре", "tcrghost": "tcrghost", "payday": "айлыкалыныакшасы", "chatherine": "чэтрин", "twilightprincess": "соңгыкичханбикә", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "комлыкбакча", "aestheticgames": "эстетикуеннар", "novelavisual": "визуальроман", "thecrew2": "экипаж2", "alexkidd": "алекскидд", "retrogame": "ретроуен", "tonyhawkproskater": "тонихокпроскейтер", "smbz": "smbz", "lamento": "лаэменто", "godhand": "алласыныңкулы", "leafblowerrevolution": "яфракөрткечеволюция", "wiiu": "wiiu", "leveldesign": "дәрәҗәдизайны", "starrail": "йолдызлыюл", "keyblade": "ачкычкылыч", "aplaguetale": "үләтчума", "fnafsometimes": "fnafsкайчакта", "novelasvisuales": "визуальроманнар", "robloxbrasil": "robloxбразилия", "pacman": "пакман", "gameretro": "уенретро", "videojuejos": "видеоу<PERSON><PERSON><PERSON>р", "videogamedates": "видеоуеннардабелəнтанышу", "mycandylove": "минемшикәрлемсөйгәнем", "megaten": "megaten", "mortalkombat11": "морталкомбат11", "everskies": "everskies", "justcause3": "шулайгына3", "hulkgames": "халкуеннары", "batmangames": "бэтменуеннары", "returnofreckoning": "кайтупкилү", "gamstergaming": "гамстергейминг", "dayofthetantacle": "күнтентакль", "maniacmansion": "маньяктойы", "crashracing": "аварияүткәрү", "3dplatformers": "3dплатформерлар", "nfsmw": "nfsmw", "kimigashine": "кимига<PERSON>ине", "oldschoolgaming": "искемәктәпгейминг", "hellblade": "hellblade", "storygames": "хикәяуеннары", "bioware": "bioware", "residentevil6": "резидентэвил6", "soundodger": "тавыштанкачу", "beyondtwosouls": "икеҗандансоң", "gameuse": "уенкуллану", "offmortisghost": "гомерекишләгән", "tinybunny": "кечкенәкуян", "retroarch": "retroarch", "powerup": "көчәйт", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "эвентырәсграфикәс", "quickflash": "тизфлеш", "fzero": "fzero", "gachagaming": "гачаойыннары", "retroarcades": "ретроаркадалар", "f123": "f123", "wasteland": "чүллек", "powerwashsim": "электръювындырэмсим", "coralisland": "коралутравы", "syberia3": "syberia3", "grymmorpg": "грымморпг", "bloxfruit": "bloxfruit", "anotherworld": "башкадөнья", "metaquest": "metaquest", "animewarrios2": "анимесугышчылар2", "footballfusion": "футболберләшү", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "астронер", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "әйләнешлетимер", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "оятсызлыкөеме", "simulator": "симулятор", "symulatory": "симуляторлар", "speedrunner": "тизйөгерүче", "epicx": "эпикх", "superrobottaisen": "суперроботсугышы", "dcuo": "dcuo", "samandmax": "сэмбелэнмакс", "grywideo": "грывидео", "gaiaonline": "gaiaonline", "korkuoyunu": "куркыуены", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "егетдонжон", "toontownrewritten": "too<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "симрей<PERSON>инг", "simrace": "симрейс", "pvp": "pvp", "urbanchaos": "шәһәрбуталышы", "heavenlybodies": "күктәнтөшкәнгүзәлләр", "seum": "ачу", "partyvideogames": "партивиде<PERSON>уеннар", "graveyardkeeper": "каберстанкаравылчы", "spaceflightsimulator": "космикочышсимуляторы", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "кисүһәмсугу", "foodandvideogames": "ашказануенвидеоуеннар", "oyunvideoları": "уенвидеолары", "thewolfamongus": "куебездәгебүре", "truckingsimulator": "йөккашинасимуляторы", "horizonworlds": "horizonworlds", "handygame": "уенчыграк", "leyendasyvideojuegos": "легендаларһәмвидеоуеннар", "oldschoolvideogames": "элеккевидеоуеннар", "racingsimulator": "гонкасимуляторы", "beemov": "bee<PERSON>v", "agentsofmayhem": "майһемагентлары", "songpop": "җырпоп", "famitsu": "famitsu", "gatesofolympus": "олимпкапкалары", "monsterhunternow": "монстрауләүчехәзер", "rebelstar": "бунтарьйолдыз", "indievideogaming": "индиуен<PERSON>р", "indiegaming": "индиуен<PERSON>р", "indievideogames": "индивидеоуеннар", "indievideogame": "индиуен", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermanйокысыз", "bufffortress": "көчлекальга", "unbeatable": "җиңелмәс", "projectl": "проект", "futureclubgames": "киләчәкклубуеннары", "mugman": "кружкакеше", "insomniacgames": "инсомниакуеннар", "supergiantgames": "зурданзургеймнар", "henrystickman": "генристикман", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "арттакалганэшләр", "gamebacklog": "уенташлама", "gamingbacklog": "уенкалдыклары", "personnagejeuxvidéos": "видеоуенперсонажлары", "achievementhunter": "ачивментджыючы", "cityskylines": "шәһәрпанорамалары", "supermonkeyball": "супермаймылтуп", "deponia": "депония", "naughtydog": "усалэт", "beastlord": "явызхуҗа", "juegosretro": "ретроу<PERSON>ннар", "kentuckyroutezero": "кентуккиюлынуль", "oriandtheblindforest": "ориһәмсукырурман", "alanwake": "alanwake", "stanleyparable": "стэнлипарабель", "reservatoriodedopamin": "допаминсаклагычы", "staxel": "staxel", "videogameost": "видеоуеносты", "dragonsync": "аждаһасинхрон", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "minkofxvяратам", "arcanum": "серләр", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "берсерк", "baki": "баки", "sailormoon": "сейлормун", "saintseiya": "сентсейя", "inuyasha": "инуяша", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "башлангычd", "elhazard": "эльхазард", "dragonballz": "драго<PERSON>бо<PERSON>з", "sadanime": "моңсуаниме", "darkerthanblack": "караңгыданкарарак", "animescaling": "аниме<PERSON><PERSON><PERSON>штаблау", "animewithplot": "сюжетлыаниме", "pesci": "песчи", "retroanime": "ретроаниме", "animes": "аниме", "supersentai": "суперсентай", "samuraichamploo": "самурайчамплу", "madoka": "мадока", "higurashi": "һигураши", "80sanime": "80елларанимесы", "90sanime": "90еллар_аниме", "darklord": "караханым", "popeetheperformer": "попыныбашкаручы", "masterpogi": "үтәгözәл", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "верааниме", "2000sanime": "2000нчеанимэ", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "дрташ1сезон", "rapanime": "рэпаниме", "chargemanken": "батареяткәнчә", "animecover": "анимекавер", "thevisionofescaflowne": "эскафлауневидениесы", "slayers": "үтерүчеләр", "tokyomajin": "tokyomajin", "anime90s": "аниме90нчы", "animcharlotte": "anim<PERSON>арлотта", "gantz": "gantz", "shoujo": "сёдзё", "bananafish": "бананбалык", "jujutsukaisen": "джудзюцукайсен", "jjk": "жжк", "haikyu": "haikyu", "toiletboundhanakokun": "туалетбелəнбəйлəнгəнханакокун", "bnha": "bnha", "hellsing": "хел<PERSON><PERSON><PERSON><PERSON>г", "skipbeatmanga": "skipbeat<PERSON><PERSON>н<PERSON>а", "vanitas": "vanitas", "fireforce": "утгүчкөче", "moriartythepatriot": "мориартипатриот", "futurediary": "киләчәктәгекөндәлек", "fairytail": "әкият", "dorohedoro": "dorohedoro", "vinlandsaga": "винландсага", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "паразит", "punpun": "пунпун", "shingekinokyojin": "shingekinokyojin", "mushishi": "мушиши", "beastars": "бист<PERSON><PERSON><PERSON>", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "суперчыкканзавазы", "kamisamakiss": "иләһисүү", "blmanga": "блмлар", "horrormanga": "куркынычманга", "romancemangas": "романтикманга", "karneval": "кар<PERSON>вал", "dragonmaid": "аждаһахезмәтче", "blacklagoon": "караелга", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "мобпсихо100", "terraformars": "terraformars", "geniusinc": "гени<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shamanking": "шаманпатша", "kurokonobasket": "курокобаскеты", "jugo": "jugo", "bungostraydogs": "бангострэйдогс", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "дзюдзюцу", "yurionice": "юри<PERSON><PERSON><PERSON><PERSON>", "acertainmagicalindex": "бермөгҗизәлеиндекс", "sao": "sao", "blackclover": "караклевер", "tokyoghoul": "токиогуль", "onepunchman": "берсугышкешесе", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "ха<PERSON><PERSON>ю", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "сириу<PERSON>ягер", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "үлемфәрештәләре", "kakeguri": "какэгури", "dragonballsuper": "dragonballsuper", "hypnosismic": "гипнозмик", "goldenkamuy": "алтынкамуй", "monstermusume": "монстркыз", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "спортаниме", "sukasuka": "сукасука", "arwinsgame": "арвинуены", "angelbeats": "фәрештәләрйөрәге", "isekaianime": "исекайаниме", "sagaoftanyatheevil": "таняныңявызлыксагасы", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "таня", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "преттикюр", "theboyandthebeast": "егетһәмҗанвар", "fistofthenorthstar": "төньяктарафныңйодрыгы", "mazinger": "мазин<PERSON><PERSON>р", "blackbuttler": "кар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ч", "towerofgod": "тау<PERSON>р", "elfenlied": "эльфенлид", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "чиби", "servamp": "servamp", "howtokeepamummy": "эничемнисаклаучараллары", "fullmoonwosagashite": "тулайнурэзләү", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "токиомяумяу", "gugurekokkurisan": "гугурекоккурисан", "cuteandcreepy": "милашлыдаөркеткеч", "martialpeak": "сугышосталыгы", "bakihanma": "бакиханма", "hiscoregirl": "югарыбаллкыз", "orochimaru": "орочимару", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "да<PERSON>и", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "астольфо", "revanantfae": "revanant<PERSON>e", "shinji": "синдзи", "zerotwo": "нольике", "inosuke": "иносуке", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "монстркыз", "kanae": "kanae", "yone": "yone", "mitsuki": "мицуки", "kakashi": "какаши", "lenore": "ленор", "benimaru": "бенимару", "saitama": "сайтама", "sanji": "sanji", "bakugo": "bakugo", "griffith": "гриффит", "ririn": "rи<PERSON><PERSON>н", "korra": "корра", "vanny": "вэнни", "vegeta": "вегета", "goromi": "горомы", "luci": "люци", "reigen": "рейген", "scaramouche": "скарамуш", "amiti": "амити", "sailorsaturn": "матроссатурн", "dio": "дио", "sailorpluto": "сейлорплуто", "aloy": "aloy", "runa": "руна", "oldanime": "искеаниме", "chainsawman": "чәнсоумән", "bungoustraydogs": "бунгоурыбашлыэтләр", "jogo": "уен", "franziska": "franziska", "nekomimi": "некомими", "inumimi": "инумими", "isekai": "исекай", "tokyorevengers": "токиоревенджерс", "blackbutler": "карасмотритель", "ergoproxy": "ergoproxy", "claymore": "кылыч", "loli": "лоли", "horroranime": "куркынычаниме", "fruitsbasket": "җиләкҗимешсавыты", "devilmancrybaby": "җенкешесабыйелый", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "сейнен", "lovelive": "мәхәббәттормышы", "sakuracardcaptor": "сакуракарткаптор", "umibenoetranger": "читмемлекәтчит", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "вәгъдәителгәнберкайчандаҗир", "monstermanga": "монстрманга", "yourlieinapril": "апрельдәгеялганың", "buggytheclown": "баггиклоун", "bokunohero": "гыйбәренимәатлынуур", "seraphoftheend": "ахырзаманфәрештәсе", "trigun": "trigun", "cyborg009": "киборг009", "magi": "тылсымчы", "deepseaprisoner": "диңгезтөбеторкыны", "jojolion": "jojo<PERSON>", "deadmanwonderland": "үлекадәмеҗирәләре", "bannafish": "бананбалык", "sukuna": "сукуна", "darwinsgame": "дарвиннынуены", "husbu": "хасбу", "sugurugeto": "сугуругето", "leviackerman": "левиакерман", "sanzu": "санзу", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "пандорайөрәкләре", "yoimiya": "йоимия", "foodwars": "ашказанысугышы", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "столас", "devilsline": "шайтанюлы", "toyoureternity": "мәңгелеккәсиңа", "infpanime": "infpanime", "eleceed": "элисид", "akamegakill": "akamegakill", "blueperiod": "зәңгәрчор", "griffithberserk": "griffithberserk", "shinigami": "шинигами", "secretalliance": "яшеренберлек", "mirainikki": "киләчәкнегездәфтәр", "mahoutsukainoyome": "сихерчеңхатыны", "yuki": "юки", "erased": "юе<PERSON>г<PERSON>н", "bluelock": "bluelock", "goblinslayer": "гоблинүтерүче", "detectiveconan": "детективконан", "shiki": "шики", "deku": "деку", "akitoshinonome": "акитосинономе", "riasgremory": "riasgremory", "shojobeat": "шөҗөбит", "vampireknight": "вампирыцарь", "mugi": "муги", "blueexorcist": "күкзәңгәрэкзорцист", "slamdunk": "сләмданк", "zatchbell": "<PERSON><PERSON>bell", "mashle": "мэшл", "scryed": "скрай", "spyfamily": "шпионгаилә", "airgear": "airgear", "magicalgirl": "тылсымлыкыз", "thesevendeadlysins": "җидегүләтгөнаһ", "prisonschool": "төрмәмәктәбе", "thegodofhighschool": "лицейаллаһы", "kissxsis": "кисссеңел", "grandblue": "зургөк", "mydressupdarling": "минемкиендерүкурчагым", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "аниметәлем", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoкыскартылган", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "боччитеросок", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "мобпсихо100", "hajimenoippo": "hajimenoippo", "undeadunluck": "үлекбәхетсез", "romancemanga": "романтикманга", "blmanhwa": "блманхва", "kimetsunoyaba": "киметсуноябаны", "kohai": "кохай", "animeromance": "анимеромантика", "senpai": "сэнпай", "blmanhwas": "бл<PERSON>а<PERSON><PERSON>валар", "animeargentina": "анимеаргентина", "lolicon": "лоликон", "demonslayertothesword": "демонүтерүчегакылычына", "bloodlad": "bloodlad", "goodbyeeri": "гудбайэри", "firepunch": "утйодырыгы", "adioseri": "саубулэри", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "кинникуадәм", "mushokutensei": "мушокутенсей", "shoujoai": "кызларарасымәхәббәт", "starsalign": "йолдызларкилешә", "romanceanime": "романсаниме", "tsundere": "цундере", "yandere": "яндере", "mahoushoujomadoka": "махоушоужомадока", "kenganashura": "кенга<PERSON>шура", "saointegralfactor": "saoинтегральфактор", "cherrymagic": "черримагия", "housekinokuni": "өйкинокөн", "recordragnarok": "язмарагнарок", "oyasumipunpun": "oyasumipunpun", "meliodas": "мелиодас", "fudanshi": "фудан<PERSON>и", "retromanga": "ретроманга", "highschoolofthedead": "үлеләрмәктәбе", "germantechno": "немецтехно", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "анса<PERSON>укёсицу", "vindlandsaga": "виндландсага", "mangaka": "мангака", "dbsuper": "dbsuper", "princeoftennis": "теннисшаһзадәсе", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "докурачан", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "убийцасыныфы", "animemanga": "анимеманга", "bakuman": "бакуман", "deathparade": "үлемпарады", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "японаниме", "animespace": "анимедөнья", "girlsundpanzer": "кызларһәмтанклар", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON>", "animedub": "аниметавыш", "animanga": "анимәманга", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqтотучы", "indieanime": "индианиме", "bungoustray": "бунгоустрей", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "gundam0": "gundam0", "animescifi": "анимефантастика", "ratman": "тычканкеше", "haremanime": "гареманиме", "kochikame": "kochikame", "nekoboy": "некомалай", "gashbell": "гашбелл", "peachgirl": "персикбалаберсе", "cavalieridellozodiaco": "зодиакрыцарьлары", "mechamusume": "механикакызлар", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "ярыченсукасыклубы", "dragonquestdai": "dragonquestdai", "heartofmanga": "манганыңйөрәге", "deliciousindungeon": "тәмлеказематта", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "ragnarokъязмасы", "funamusea": "күңелле_музей", "hiranotokagiura": "хиранотокагиура", "mangaanime": "мангааниме", "bochitherock": "бочитарок", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "скиптолоафер", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialҗиңелтүрегез", "overgeared": "асып_киткән", "toriko": "<PERSON><PERSON>o", "ravemaster": "рейвостазы", "kkondae": "ккондэ", "chobits": "чобитс", "witchhatatelier": "сихерчеателье", "lansizhui": "лан<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>й", "sangatsunolion": "сангатсунолион", "kamen": "камен", "mangaislife": "мангатормыш", "dropsofgod": "алланыңтамчылары", "loscaballerosdelzodia": "зодиакныңрыцарьлары", "animeshojo": "анимешоджо", "reverseharem": "киреха<PERSON>ем", "saintsaeya": "әулиясеҗә", "greatteacheronizuka": "бөекукытучыонидзука", "gridman": "гридмен", "kokorone": "koko<PERSON>", "soldato": "солдат", "mybossdaddy": "минбоссәтием", "gear5": "gear5", "grandbluedreaming": "зурзәңгәрхыялланам", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusаниме", "bloodcanime": "bloodcанимэ", "bloodc": "канрәсеме", "talesofdemonsandgods": "җеннәрһәмилаһлархикәяләре", "goreanime": "гореаниме", "animegirls": "анимекызлар", "sharingan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crowsxworst": "crowsxяманнары", "splatteranime": "спләттераниме", "splatter": "чәчрәтү", "risingoftheshieldhero": "калканбатырыныңкүтәрелүе", "somalianime": "сомалианиме", "riodejaneiroanime": "риодежанейроаниме", "slimedattaken": "алынган", "animeyuri": "анимеюри", "animeespaña": "анимета<PERSON><PERSON><PERSON>стан", "animeciudadreal": "анимешәһәрреаль", "murim": "мурим", "netjuunosusume": "интернетсөйлəмен", "childrenofthewhales": "аккитбалалары", "liarliar": "ялганчыялганчы", "supercampeones": "суперчемпионнар", "animeidols": "анимекумирлары", "isekaiwasmartphone": "исекайсмартфонбелән", "midorinohibi": "яшелькөннәр", "magicalgirls": "сихерлекызлар", "callofthenight": "төнчакыруы", "bakuganbrawler": "бакуга<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "bakuganbrawlers": "бакуганбатырлары", "natsuki": "natsuki", "mahoushoujo": "магиккыз", "shadowgarden": "күләгәбакча", "tsubasachronicle": "tsubasachronicle", "findermanga": "манганытабучы", "princessjellyfish": "елакмедузасы", "kuragehime": "ку<PERSON><PERSON><PERSON><PERSON><PERSON>", "paradisekiss": "оҗмахпочмагы", "kurochan": "курочан", "revuestarlight": "йолдызлыяктылык", "animeverse": "анимеверс", "persocoms": "персокомнар", "omniscientreadersview": "барынбелүчеукучыныңкарашы", "animecat": "аниметомбык", "animerecommendations": "анимерекомендацияләр", "openinganime": "ачылышаниме", "shinichirowatanabe": "шиничироватанабэ", "uzumaki": "узумаки", "myteenromanticcomedy": "минемяшүсмеррomanтиккомедиям", "evangelion": "евангелион", "gundam": "гундам", "macross": "мак<PERSON><PERSON><PERSON>с", "gundams": "гунда<PERSON><PERSON>р", "voltesv": "вольтесв", "giantrobots": "гигантроботлар", "neongenesisevangelion": "неонгенезисевангелион", "codegeass": "кодгеасс", "mobilefighterggundam": "мобильфайтергандам", "neonevangelion": "неонэвангелион", "mobilesuitgundam": "мобильсьютгандам", "mech": "мех", "eurekaseven": "эврикаҗиде", "eureka7": "эврика7", "thebigoanime": "thebigoanime", "bleach": "аклау", "deathnote": "үлемдәфтәре", "cowboybebop": "каубойбибоп", "jjba": "jjba", "jojosbizarreadventure": "жожоныңсәерсәяхәте", "fullmetalalchemist": "тулыметаллалхимик", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "жожобизарсергүзәштләр", "kamuiyato": "камуияту", "militaryanime": "хәрбианиме", "greenranger": "грин<PERSON><PERSON><PERSON>нд<PERSON><PERSON>р", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "токиорев", "zorro": "зорро", "leonscottkennedy": "leonscottkennedy", "korosensei": "коросенсей", "starfox": "йолдызтөлке", "ultraman": "ультраман", "salondelmanga": "салондельманга", "lupinthe3rd": "люпен3нче", "animecity": "анимешәһәр", "animetamil": "анимэтамил", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "наруто", "narutoshippuden": "наруташиппуден", "onepiece": "onepiece", "animeonepiece": "анимеонепис", "dbz": "dbz", "dragonball": "драгонболл", "yugioh": "yugioh", "digimon": "дигимон", "digimonadventure": "digimonмаҗаралары", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "гоку", "broly": "broly", "shonenanime": "аниметуташлыклар", "bokunoheroacademia": "бокунохероакадемия", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "дрстоун", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "отака", "hunterxhunter": "hunterxhunter", "mha": "мга", "demonslayer": "демонсүтерүче", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "атакатитаннарга", "erenyeager": "эренйегер", "myheroacademia": "минемгеройакадемиясе", "boruto": "boruto", "rwby": "rwby", "dandadan": "да<PERSON><PERSON>а<PERSON><PERSON>н", "tomodachigame": "дусла<PERSON><PERSON><PERSON>ны", "akatsuki": "акацуки", "surveycorps": "разведкакорпусы", "onepieceanime": "onepieceанимесы", "attaquedestitans": "титаннарһөҗүме", "theonepieceisreal": "theonepieceреаль", "revengers": "үчалучылар", "mobpsycho": "мобпсихо", "aonoexorcist": "аоноэкзорцист", "joyboyeffect": "джойбойэффект", "digimonstory": "digimonstoryсы", "digimontamers": "дигимонта<PERSON><PERSON><PERSON><PERSON><PERSON>р", "superjail": "супертөрмә", "metalocalypse": "металокалипсис", "shinchan": "шин<PERSON><PERSON>н", "watamote": "watamote", "uramichioniisan": "ураминиагай", "uruseiyatsura": "урусейяцура", "gintama": "gintama", "ranma": "ранма", "doraemon": "doraemon", "gto": "гто", "ouranhostclub": "урансызныңкунакханәклубы", "flawlesswebtoon": "гаепсезвебтун", "kemonofriends": "кемонодуслар", "utanoprincesama": "utanoprincesama", "animecom": "анимеком", "bobobobobobobo": "бобобобобобобо", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "көндәлек", "yurucamp": "юр<PERSON><PERSON><PERSON>", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "очучыкарчык", "wotakoi": "вотакой", "konanime": "konanime", "clannad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justbecause": "ниәстәндер", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "барысыизгеләрурамы", "recuentosdelavida": "тормышхикәяләре"}