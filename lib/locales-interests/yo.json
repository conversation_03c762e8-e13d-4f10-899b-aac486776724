{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cognitivefunctions": "iṣẹọpọlọ", "psychology": "ẹkọ̀ìwàlááyè", "philosophy": "ìmòyeakọ́yé", "history": "<PERSON><PERSON><PERSON><PERSON>", "physics": "<PERSON><PERSON><PERSON>", "science": "<PERSON><PERSON><PERSON>", "culture": "àṣ<PERSON>", "languages": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "technology": "ìmọ̀ẹ̀rọ", "memes": "aw<PERSON><PERSON><PERSON><PERSON>", "mbtimemes": "m<PERSON><PERSON><PERSON><PERSON>", "astrologymemes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enneagrammemes": "awọnmemeenneagram", "showerthoughts": "er<PERSON><PERSON>baluwe", "funny": "apanilerin", "videos": "fidio", "gadgets": "awọnẹrọamuṣiṣẹ", "politics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "relationshipadvice": "imọranìbálòpọ̀", "lifeadvice": "imọranaye", "crypto": "crypto", "news": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldnews": "ìrò<PERSON>ìnà<PERSON>", "archaeology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "learning": "ìkẹ́kọ̀ọ́", "debates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "awọ<PERSON><PERSON>", "universe": "a<PERSON><PERSON>e", "meditation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mythology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "art": "ise_ona", "crafts": "ìṣẹ́ọnà", "dance": "<PERSON><PERSON><PERSON>", "design": "aṣẹda", "makeup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beauty": "ẹwà", "fashion": "ẹwà", "singing": "orin", "writing": "kíkọ", "photography": "fotoyiya", "cosplay": "cosplay", "painting": "<PERSON><PERSON><PERSON><PERSON>", "drawing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "books": "<PERSON><PERSON><PERSON>", "movies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "poetry": "ewì", "television": "tẹlifíṣọ̀n", "filmmaking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animation": "eremọgbẹsẹ", "anime": "anime", "scifi": "<PERSON><PERSON><PERSON><PERSON>", "fantasy": "ìràwọ̀ńtì", "documentaries": "awọndokumenti", "mystery": "ohun_a<PERSON>iri", "comedy": "awada", "crime": "ìwàìbàjẹ́", "drama": "ayika", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "ẹ̀rù", "romance": "ìfẹ́", "realitytv": "tẹlifisiọnọ̀tọ́", "action": "iṣe", "music": "orin", "blues": "ìrònú", "classical": "orinibile", "country": "orílẹ̀èdè", "desi": "desi", "edm": "edm", "electronic": "ẹ̀rọayárabíàṣá", "folk": "ibile", "funk": "funk", "hiphop": "hiphop", "house": "<PERSON><PERSON>", "indie": "<PERSON><PERSON><PERSON>", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "irin", "pop": "pop", "punk": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rnb": "rnb", "rap": "<PERSON><PERSON><PERSON><PERSON>", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "<PERSON><PERSON><PERSON>", "concerts": "erekose", "festivals": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "museums": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "standup": "dide", "theater": "tíátà", "outdoors": "ìtagbè", "gardening": "iṣẹ́ọgbà", "partying": "parírẹ́", "gaming": "ere", "boardgames": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "dungeonsanddragons", "chess": "ayo", "fortnite": "fortnite", "leagueoflegends": "eregbeawonakikanju", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "oúnjẹ", "baking": "ìdáná", "cooking": "<PERSON><PERSON><PERSON><PERSON>", "vegetarian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vegan": "ajewewe", "birds": "ẹyẹ", "cats": "ologbo", "dogs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fish": "eja", "animals": "àwọnẹranko", "blacklivesmatter": "èmíaláwòdúdú<PERSON>", "environmentalism": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "feminism": "tèn<PERSON><PERSON><PERSON><PERSON>", "humanrights": "ẹtọọmọnìyàn", "lgbtqally": "alaabolgbtq", "stopasianhate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "alátìlẹ́yìntrán", "volunteering": "itọrẹ", "sports": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "badminton": "bádí<PERSON><PERSON><PERSON>t<PERSON>", "baseball": "béèsíbọ́ọ̀lù", "basketball": "ìtàgbọ́ǹbọ́ọ̀lù", "boxing": "ẹṣẹ", "cricket": "krikẹti", "cycling": "kẹkẹgigun", "fitness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "football": "b<PERSON><PERSON><PERSON>", "golf": "gọ́ọ̀fù", "gym": "<PERSON><PERSON><PERSON><PERSON>", "gymnastics": "ereg<PERSON>a", "hockey": "ọkẹ", "martialarts": "ijaijagboro", "netball": "bọ<PERSON><PERSON><PERSON>ọwọ", "pilates": "pilates", "pingpong": "tẹnisi", "running": "<PERSON><PERSON><PERSON>", "skateboarding": "sọkẹtibọọdì", "skiing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "snowboarding": "síńbọ́ọ̀dì", "surfing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swimming": "ìlúwẹ̀ẹ́", "tennis": "tẹnisi", "volleyball": "bọ́ọ̀lùfólè", "weightlifting": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "yoga", "scubadiving": "ìlúwẹ̀ẹ́nínúomi", "hiking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "capricorn": "capricorn", "aquarius": "aquarius", "pisces": "pisces", "aries": "èbur<PERSON>", "taurus": "taurus", "gemini": "gemini", "cancer": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "leo": "leo", "virgo": "virgo", "libra": "ireti", "scorpio": "scorpio", "sagittarius": "sagittarius", "shortterm": "igbadied<PERSON>", "casual": "t<PERSON><PERSON><PERSON><PERSON>", "longtermrelationship": "ìfẹ́tíńpẹ́", "single": "<PERSON><PERSON><PERSON>", "polyamory": "ìfẹ́púpọ̀", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "onibinrin", "lesbian": "ob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bisexual": "onibaji<PERSON><PERSON>", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "a<PERSON>ga", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "ọkànrèrú", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "itanspyro", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "orunwosanaa", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yokaiwatch": "yokaiwatch", "rocksteady": "<PERSON><PERSON><PERSON><PERSON>", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "ogunẹgbẹ", "openworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "awọna<PERSON>ó", "cytus": "cytus", "soulslike": "ọkànbíi", "dungeoncrawling": "igbokikiogun", "jetsetradio": "jetsetradio", "tribesofmidgard": "awọnẹyatiasgard", "planescape": "awonfoforo", "lordsoftherealm2": "àwọnolúìjọba2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "aṣẹmúawọ̀", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON>", "witcher": "witcher", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "idaa<PERSON>", "fallout3": "fallout3", "fallout4": "itusilẹ4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "ṣiṣ<PERSON><PERSON>ṣe", "charactercreation": "ẹdaohunìtọ́kàn", "immersive": "afẹsẹgba", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "final<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ọ́", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "ìw<PERSON>riekú", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "er<PERSON><PERSON><PERSON><PERSON>ọ", "suckerforlove": "ọ̀dọ́mùkúnlé", "otomegames": "aw<PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarina<PERSON><PERSON><PERSON>", "yiikrpg": "yiikrpg", "vampirethemasquerade": "iwerekugbosoosuuru", "dimension20": "dimension20", "gaslands": "ilẹogesi", "pathfinder": "ol<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "ẹjẹloriagogo", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "fẹrannikki", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "ig<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "ìgbàkọ́ọ̀kan", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yourturntodie": "igbatire", "persona3": "persona3", "rpghorror": "eréìpayaẹ̀rùjẹ́jẹ́", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON>iokunkun", "mu": "mu", "falloutshelter": "ibùgbépaárọ̀rún", "gurps": "gurps", "darkestdungeon": "ibiokunkunjulo", "eclipsephase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "ayeode", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "ìdè<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloimmortal": "diabloimmortal", "dynastywarriors": "awọnogunagbalagba", "skullgirls": "skullgirls", "nightcity": "ilualẹ", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "òpópónà96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "ereawolelekookan", "gothamknights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "agbayeelege<PERSON><PERSON>", "dragonlance": "dragonlance", "arenaofvalor": "arenatiaw<PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "ilutoontown", "childoflight": "ọmọìmọ́lẹ̀", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "kokoro2", "xeno": "xeno", "vulcanverse": "agbayevulcan", "fracturedthrones": "ìtẹ́tìsápá", "horizonforbiddenwest": "agbegbeewọyoiwọorun", "twewy": "twewy", "shadowpunk": "ọmọojijipunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "gbálẹ̀", "lastepoch": "igbẹhinrere", "starfinder": "olùwáìràwọ̀", "goldensun": "<PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "idaaninuokunk<PERSON>", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkpupa", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "ilaṣẹtialagara", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "ilẹokunkun", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "el<PERSON>a", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "ayọkọdunìgbàńlá", "adventurequest": "ìr<PERSON><PERSON><PERSON><PERSON>òìw<PERSON>dìí", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "itansymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "ilutituka", "myfarog": "myfarog", "sacredunderworld": "ayeabẹokunkun", "chainedechoes": "<PERSON><PERSON><PERSON><PERSON>", "darksoul": "ẹmioókùnkùn", "soulslikes": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "othercide": "ẹgbẹkejì", "mountandblade": "okonataobeati", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pillarsofeternity": "ogiriayera<PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "aw<PERSON><PERSON><PERSON>", "tibia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thedivision": "i<PERSON>pin", "hellocharlotte": "kaabocharlotte", "legendofdragoon": "ìtàndragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "ẹlẹyinjúejè", "octopathtraveler": "erinajookanmejo", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "ikokulojo", "aveyond": "aveyond", "littlewood": "igikekere", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "ẹ́rọokàn", "fable3": "fitan3", "fablethelostchapter": "itanagbagbeagbeye", "hiveswap": "ìpààrọ̀oyin", "rollenspiel": "erep<PERSON><PERSON><PERSON><PERSON>e", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "ayéainípèkún", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "àlàfíàày<PERSON>", "oldschoolrevival": "igbaatijoti<PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "ayeawononijakija", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "ibiokunkun", "juegosrpg": "awọnererpg", "kingdomhearts": "ilẹobakobami", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "ẹbímàlkávíànì", "harvestella": "harvestella", "gloomhaven": "ig<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wildhearts": "à<PERSON>àokà<PERSON>", "bastion": "gbogon<PERSON><PERSON>", "drakarochdemoner": "drakarochawọnẹmi", "skiesofarcadia": "ojúọ̀runarcadia", "shadowhearts": "ọkàneléwù", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "ẹjẹkẹjẹ", "breathoffire4": "ẹ̀mí_iná_kẹrin", "mother3": "èyáketá", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "er<PERSON>e<PERSON>e", "fabulaultima": "fabulault<PERSON>", "witchsheart": "ọkàn_àjẹ́", "harrypottergame": "ereháríp<PERSON>́tà", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "akokọja", "cocttrpg": "cocttrpg", "huntroyale": "odesímorí", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "edeapanilayeajakaleke", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "forumurpg", "shadowheartscovenant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "awplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theworldendswithyou": "ayebalẹpẹlurẹ", "dragalialost": "dragalialósọ̀nù", "elderscroll": "akojọpọoniranlọwọagbalagba", "dyinglight2": "ikuimole2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "esinokunkun", "shoptitans": "àwọnalágbàratitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "asẹaraye", "blackbook": "i<PERSON><PERSON>du<PERSON>", "skychildrenoflight": "ọmọojumọ", "gryrpg": "gryrpg", "sacredgoldedition": "ohuntitọ́ọ́rìnwúràmímọ́", "castlecrashers": "awọnjagunjagunile", "gothicgame": "eregotig<PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "eregamingrpg", "prophunt": "ọdẹwòlé", "starrails": "awọnọkonailọfẹ", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "rpgindie", "pointandclick": "tọkasiklik", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "aláìpín", "freeside": "ẹgbẹalaisan", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "onaherekudekanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "odesiomu", "fireemblem": "<PERSON><PERSON>ja", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremancy", "persona5": "persona5", "ghostoftsushima": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "apanilẹrinrise", "nier": "nier", "dothack": "<PERSON><PERSON><PERSON><PERSON>", "ys": "ys", "souleater": "ẹmiokan", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahoyo": "mahoyo", "animegames": "ereanime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "onjẹọlọrun", "diluc": "diluc", "venti": "venti", "eternalsonata": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "princessconnect": "ìsopọ̀ọmọbabinrin", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "oloweonibupo", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindia", "dota": "dota", "madden": "<PERSON><PERSON><PERSON><PERSON>", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "ereidaraya", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ẹgbẹ̀aláàlà", "fifa14": "fifa14", "midlaner": "<PERSON><PERSON><PERSON><PERSON>", "efootball": "boolueleseafunyoruba", "dreamhack": "alahack", "gaimin": "gaimin", "overwatchleague": "ligioríǹwọ́overwatchi", "cybersport": "eresìpọ̀tù", "crazyraccoon": "erewere", "test1test": "test1test", "fc24": "fc24", "riotgames": "eregames<PERSON>", "eracing": "tituupa", "brasilgameshow": "erepereawonereoribrasil", "valorantcompetitive": "valorantcompetitive", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead": "okusiwaju4", "left4dead2": "okutiwonfisileemerin2", "valve": "valve", "portal": "màbọ̀ọ́kì", "teamfortress2": "teamfortress2", "everlastingsummer": "igbakigbeoju<PERSON>un", "goatsimulator": "<PERSON><PERSON><PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "aye<PERSON>n", "transformice": "transformice", "justshapesandbeats": "awọnirisiatiilu", "battlefield4": "ogungbogbode4", "nightinthewoods": "oruninuig<PERSON>", "halflife2": "halflife2", "hacknslash": "gebolebuje", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "awọnmetroidvania", "overcooked": "tabùjẹ", "interplanetary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helltaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "sẹ́ẹ̀lìòkú", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "ih<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stray": "ṣak<PERSON>", "battlefield": "o<PERSON><PERSON><PERSON><PERSON>", "battlefield1": "oguntoyá1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "oju", "blackdesert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "<PERSON><PERSON><PERSON><PERSON>", "partyhard": "jay<PERSON><PERSON>e", "hardspaceshipbreaker": "ònídáwọ́ọ̀kọ̀òfurufúlílágbára", "hades": "amuludun", "gunsmith": "agbẹ́dẹìbọn", "okami": "<PERSON>ami", "trappedwithjester": "tihamu̩pe̩luje̩ste̩ri", "dinkum": "ooto", "predecessor": "aṣ<PERSON><PERSON><PERSON><PERSON>", "rainworld": "ayeojo", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "eresimu", "noita": "noita", "dawnofwar": "o<PERSON><PERSON><PERSON><PERSON>", "minionmasters": "minionmasters", "grimdawn": "e<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "aṣẹkankara", "datingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "ọ̀nàkúrò_cube", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "awọnọk<PERSON><PERSON><PERSON>ilu", "defconheavy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsia", "virtualkenopsia": "virtualkenopsia", "snowrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenaidibujẹmu", "placidplasticduck": "ìtùtùpálasítíkìpẹ́pẹ́yẹ", "battlebit": "ogunbit", "ultimatechickenhorse": "ẹṣinàdìẹtítóbijù", "dialtown": "ilutabalọ", "smileforme": "rẹ́rìnfúnmi", "catnight": "oruologbo", "supermeatboy": "ọmọkùnrinẹransuper", "tinnybunny": "ehororokekere", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "<PERSON><PERSON><PERSON>", "callofduty": "ikpeerejaogun", "callofdutyww2": "codww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "ilẹ̀ààlà", "pubg": "pubg", "callofdutyzombies": "er<PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "oke", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "tanigun", "farcrygames": "erefarcry", "paladins": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earthdefenseforce": "agboogunilẹayé", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "ẹgbẹawọnẹmiọkú", "grandtheftauto5": "grandtheftauto5", "warz": "ogun", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ìpàdánúgbàágbàá", "joinsquad": "darapọmọẹgbẹ", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "b4b": "g4g", "codwarzone": "codwarzone", "callofdutywarzone": "erepaajagunwarzone", "codzombies": "codzombies", "mirrorsedge": "ojukanẹlẹgbẹ", "divisions2": "àwọnìpíndì2", "killzone": "adiẹkúpani", "helghan": "hel<PERSON>", "coldwarzombies": "<PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acecombat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crosscode": "kodiagbelebu", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "ọtaafinagbọ<PERSON>", "modernwarfare": "<PERSON><PERSON><PERSON><PERSON>", "neonabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planetside2": "planetside2", "mechwarrior": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boarderlands": "boarderlands", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_tarkov", "metalslug": "metalslug", "primalcarnage": "<PERSON><PERSON><PERSON><PERSON>", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "back4blood": "padàfúnẹ̀jẹ̀", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "apànìyàn", "masseffect": "masseffect", "systemshock": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "awonjagunjagunpata<PERSON>", "killingfloor2": "ilẹ̀kúpa2", "cavestory": "itanihokus<PERSON>", "doometernal": "ikuailopin", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mwo": "mwo", "division2": "ipin2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "irantibi", "enterthegungeon": "wolegbejaegungeon", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "ogunodeoni2", "blackops1": "blackops1", "sausageman": "ọk<PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "irọ̀raẹlẹ́gbẹ́", "warface": "o<PERSON><PERSON><PERSON><PERSON>", "crossfire": "ijafẹsẹgbara", "atomicheart": "okantitara", "blackops3": "blackops3", "vampiresurvivors": "awọnaṣọdi<PERSON>ò<PERSON>", "callofdutybatleroyale": "ipeàgbárayecallofduty", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "ominira", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "erepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "convertstrike": "iyipada_ikolu", "warzone2": "ogunnla2", "shatterline": "gbigbẹ́lánulẹ̀", "blackopszombies": "blackopszombies", "bloodymess": "ẹ̀jẹ̀gbàǹgbà", "republiccommando": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elitedangerous": "el<PERSON><PERSON><PERSON><PERSON>", "soldat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "groundbranch": "<PERSON><PERSON><PERSON>", "squad": "ẹgbẹ́", "destiny1": "àyànmọ́1", "gamingfps": "eregamingfps", "redfall": "redfall", "pubggirl": "ọmọbìnrìnpubg", "worldoftanksblitz": "ayeawontankiblitz", "callofdutyblackops": "epeijablackops", "enlisted": "titẹlọmọogun", "farlight": "ìmọ́lẹ̀jìnnà", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halo2": "halo2", "payday2": "owoeyan2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "gbajumọmania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostcod": "ghostcod", "csplay": "ere", "unrealtournament": "idijeasoteleolokiki", "callofdutydmz": "callofdutydmz", "gamingcodm": "erecodm", "borderlands2": "borderlands2", "counterstrike": "<PERSON>k<PERSON><PERSON>", "cs2": "cs2", "pistolwhip": "ìgbàdì<PERSON><PERSON>", "callofdutymw2": "ereoguncodmw2", "quakechampions": "asejuonija", "halo3": "halo3", "halo": "halo", "killingfloor": "ilẹkuiku", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "funfunelegidig<PERSON>", "remnant": "àṣẹ́kù", "azurelane": "azurelane", "worldofwar": "aye<PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "padàbọ̀wá", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "ọkùnrùnẹniyan", "quake2": "ìmìtìtì2", "microvolts": "microvolts", "reddead": "<PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "papaogun3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "okunjejija", "rust": "ẹ̀tù", "conqueronline": "ṣẹgunlori", "dauntless": "aláìbẹ̀rù", "warships": "ọkọ̀ojúomiogun", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "ogunàrà", "flightrising": "i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recroom": "recroom", "legendsofruneterra": "awọnakọọlẹruneterra", "pso2": "pso2", "myster": "ohun_a<PERSON>iri", "phantasystaronline2": "phantasystaronline2", "maidenless": "aláìnítọkọtàyà", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "ayeawọntanki", "crossout": "ṣayẹyẹ", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "superanimalroyale": "ìdíjeasáájúẹrankoagbára", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newworld": "ayeuntun", "blackdesertonline": "blackdesertonline", "multiplayer": "erepupo", "pirate101": "ajalelokun101", "honorofkings": "ọlátitóọba", "fivem": "fivem", "starwarsbattlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karmaland": "ilẹkama", "ssbu": "ssbu", "starwarsbattlefront2": "ogunirọrunlaye2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "<PERSON><PERSON><PERSON>", "3dchat": "iròyìn3d", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "eérúìṣẹ̀dá", "riotmmo": "riotmmo", "silkroad": "ọ̀nàábírísìmù", "spiralknights": "spiralknights", "mulegend": "<PERSON><PERSON><PERSON><PERSON>", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "as<PERSON><PERSON>lug<PERSON><PERSON><PERSON>", "grymmo": "grymmo", "warmane": "gbona", "multijugador": "ereolopupọ", "angelsonline": "awọnangẹlilori_aye<PERSON>jara", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworld": "a<PERSON><PERSON><PERSON>", "riseonline": "dìdemúlórí", "corepunk": "corepunk", "adventurequestworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "animaljam", "kingdomofloathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "iluel<PERSON>inadahunse", "mortalkombat": "igbijaokunologun", "streetfighter": "onijastrit", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "onijafoju", "streetsofrage": "awọ<PERSON>popona<PERSON><PERSON>", "mkdeadlyalliance": "aṣepọigbẹkurun", "nomoreheroes": "kòsíakíkanjúmọ́", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "ọbaawọnonija", "likeadragon": "bíidrágónì", "retrofightinggames": "awoneregameijaofe", "blasphemous": "<PERSON><PERSON><PERSON><PERSON>", "rivalsofaether": "er<PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "gbogbonisepamọ", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "awọn<PERSON>botiayelujar<PERSON>", "armoredwarriors": "awọ<PERSON>gunjagun<PERSON>ọ<PERSON>", "finalfight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "poweredgear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "awọnerefija", "killerinstinct": "ìfẹ́ṣàpàyàn", "kingoffigthers": "ọbaawọnonija", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "ogboju2", "demonssouls": "eemiokunokunkun", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightitẹle", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "kò<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "silksonggame": "eresílíìk<PERSON>", "silksongnews": "ir<PERSON><PERSON>ìnsil<PERSON><PERSON>", "silksong": "okùnabárasọ̀", "undernight": "ḷásáníkẹ́jì", "typelumina": "k<PERSON><PERSON><PERSON>", "evolutiontournament": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "evomoment": "<PERSON><PERSON><PERSON><PERSON>", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "<PERSON>an<PERSON><PERSON><PERSON><PERSON>", "bloodborne": "bloodborne", "horizon": "<PERSON><PERSON><PERSON>", "pathofexile": "<PERSON><PERSON><PERSON><PERSON>", "slimerancher": "erotitọ̀ọ̀rọ̀", "crashbandicoot": "crashbandicoot", "bloodbourne": "ẹ̀jẹ̀tíńgbàjáde", "uncharted": "àìkọsílẹ̀", "horizonzerodawn": "aṣálẹ̀lórùnòfin", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "igbẹhintónìkan", "infamous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstationbuddies": "awọnọrẹplaystation", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "abẹgún", "persona4": "persona4", "hellletloose": "jekisere", "gta4": "gta4", "gta": "gta", "roguecompany": "egbealaig<PERSON><PERSON>", "aisomniumfiles": "àwọnfáìlìòrúnsìnúaisomnium", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "awon", "detroitbecomehuman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "titodi", "touristtrophy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "ojijiolobilaru", "crashteamracing": "erekereija", "fivepd": "<PERSON><PERSON><PERSON><PERSON>", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "devilmaycry3", "devilmaycry5": "esulebaagun5", "ufc4": "ufc4", "playingstation": "erekere", "samuraiwarriors": "ologunsamurai", "psvr2": "psvr2", "thelastguardian": "ẹṣọkẹhin", "soulblade": "idaẹmiokan", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "wíwágbéfẹ́", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "ọkànòjìjìààdé<PERSON>", "pcsx2": "pcsx2", "lastguardian": "ẹniìkẹyìnolùṣọ́", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "onijàyéjàyé", "warharmmer40k": "warhammer40k", "fightnightchampion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "awọnak<PERSON>ọ", "mhw": "mhw", "princeofpersia": "ọmọọbapersia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "o<PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "májẹebipapo", "ori": "ori", "spelunky": "<PERSON><PERSON><PERSON><PERSON>", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "kọk<PERSON><PERSON><PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "siketi3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ìjọbaawọ<PERSON>", "fable2": "fìtàn2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "tẹlifisiọnalọ́mọ̀ráyè", "skycotl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "erica": "erica", "ancestory": "ebibi", "cuphead": "cuphead", "littlemisfortune": "<PERSON><PERSON><PERSON><PERSON>", "sallyface": "<PERSON>j<PERSON><PERSON>", "franbow": "franbow", "monsterprom": "eréìdáníṣẹ́ẹ̀jọ", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "ọkọ̀", "outerwilds": "agbayeode", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "eg<PERSON><PERSON><PERSON><PERSON>", "duckgame": "<PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "itans<PERSON>ley", "towerunite": "towerepapo", "occulto": "ìkọ̀kọ̀", "longdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "satisfactory": "itẹlọrun", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "labẹlẹ", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "alagborigbo", "darkdome": "okunkun", "pizzatower": "ilepisa", "indiegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itchio": "itchio", "golfit": "<PERSON><PERSON><PERSON><PERSON>", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rockpaperscissors": "okutawekaleke", "trampoline": "trampoline", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "g<PERSON>yan<PERSON>", "scavengerhunt": "<PERSON><PERSON><PERSON>", "yardgames": "erepapa", "pickanumber": "ya<PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "o<PERSON><PERSON><PERSON>", "beerpong": "ọtíbìlí", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "<PERSON><PERSON><PERSON><PERSON>", "datinggames": "<PERSON><PERSON><PERSON><PERSON>", "freegame": "erefree", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "awọnerewasikọsẹ", "mahjong": "mahjong", "jeux": "eré", "simulationgames": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "<PERSON><PERSON><PERSON>", "jeuxdemots": "jeuxdemots", "juegosdepalabras": "erepada", "letsplayagame": "jẹkiaṣeere", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "oyun", "interactivegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochi": "awọn_ere", "geoguessr": "geoguessr", "iphonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boogames": "boogames", "cranegame": "eregbongbon", "hideandseek": "sáàmúpamú", "hopscotch": "kẹkẹrẹ", "arcadegames": "er<PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON>", "classicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mindgames": "er<PERSON><PERSON><PERSON>", "guessthelyric": "sepeewek<PERSON>n", "galagames": "eregala", "romancegame": "<PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON>", "4xgames": "4<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamefi": "gamefi", "jeuxdarcades": "jeuxdarcades", "tabletopgames": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "ère90", "idareyou": "mokadani", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "ereg<PERSON><PERSON>", "ets2": "ets2", "realvsfake": "gidivsfeke", "playgames": "ṣ<PERSON><PERSON><PERSON>", "gameonline": "eréorílẹ̀ẹ́yìn", "onlinegames": "<PERSON><PERSON><PERSON><PERSON>", "jogosonline": "<PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playaballgame": "ṣ<PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "er<PERSON>ii", "highscore": "ọnkapataki", "jeuxderôles": "erepaa<PERSON>e", "burgergames": "eregames", "kidsgames": "<PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "gioco", "managementgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "eregameformula1", "citybuilder": "k<PERSON><PERSON><PERSON>", "drdriving": "wíwàkọ́kọ́", "juegosarcade": "<PERSON><PERSON><PERSON><PERSON>", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "er<PERSON>ṣ<PERSON><PERSON>e", "blowgames": "erefẹ", "pinballmachines": "àwọnẹ̀rọpinball", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "eregame", "lasergame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "awọnereimessage", "idlegames": "erealáìṣiṣẹ́", "fillintheblank": "fekusiile", "jeuxpc": "erep<PERSON>ebek<PERSON><PERSON><PERSON>", "rétrogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logicgames": "erealáròjinlẹ̀", "japangame": "<PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "gbéìfẹ́sókè", "subwaysurf": "ọkọelualẹsẹ", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "<PERSON><PERSON><PERSON><PERSON>", "5vs5": "5lodi5", "rolgame": "<PERSON><PERSON><PERSON><PERSON>", "dashiegames": "ereidaraya", "gameandkill": "er<PERSON><PERSON><PERSON>", "traditionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "eregí<PERSON><PERSON><PERSON><PERSON>", "textbasedgames": "aw<PERSON>nereg<PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "ìtàns<PERSON><PERSON>ìn<PERSON>", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tischfußball": "tischfußball", "spieleabende": "eréalẹ́", "jeuxforum": "jeuxforum", "casualgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "escapegames": "eregbigbọ", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "eregidanmole", "játék": "<PERSON><PERSON><PERSON><PERSON>", "bordfodbold": "bordfodbold", "jogosorte": "jogosorte", "mage": "mág<PERSON>", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "eréorílẹ̀ayélujára", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "awọneruìdúnǹ", "pursebingos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "randomizer": "randomizer", "msx": "msx", "anagrammi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamespc": "awọnereyọkọ̀ǹpútà", "socialdeductiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominos": "dominos", "domino": "domino", "isometricgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "otitọatiotitẹ", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "iṣẹigbọdẹ", "jeuxvirtuel": "er<PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "oṣereọ̀fẹ́", "free2play": "ọ̀fẹ́láti_ṣeré", "fantasygame": "ereg<PERSON><PERSON>", "gryonline": "gryloriayelujara", "driftgame": "<PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "awọneregíràmífẹ́", "halotvseriesandgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mushroomoasis": "a<PERSON><PERSON><PERSON><PERSON>", "anythingwithanengine": "ohunkohungbogbolatinienjini", "everywheregame": "gbogboibiayinaere", "swordandsorcery": "idàatiìdán", "goodgamegiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "j<PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "eregiriwewe", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "er<PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "<PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "eregileejeoborigitype4", "selflovegaming": "ifẹaraẹniere", "gamemodding": "atun<PERSON><PERSON><PERSON>", "crimegames": "awọneregídídarúpọ̀", "dobbelspellen": "er<PERSON><PERSON>e", "spelletjes": "<PERSON><PERSON><PERSON>ek<PERSON>", "spacenerf": "iféim<PERSON>", "charades": "ereeked<PERSON>", "singleplayer": "eréẹnìkan", "coopgame": "ereg<PERSON>j<PERSON>", "gamed": "erefun", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "erepapa", "kingdiscord": "obad<PERSON><PERSON>", "scrabble": "scrabble", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "l<PERSON><PERSON><PERSON>", "backgammon": "bàg<PERSON>mọ<PERSON>n<PERSON>", "onitama": "onitama", "pandemiclegacy": "aṣàfihànàjàkálẹ̀àrùn", "camelup": "ìbákasẹ́lẹ̀", "monopolygame": "eremonopoli", "brettspiele": "awọneregameboardilekun", "bordspellen": "<PERSON><PERSON><PERSON>", "boardgame": "erepapa", "sällskapspel": "<PERSON><PERSON><PERSON><PERSON>", "planszowe": "erepapa", "risiko": "ewu", "permainanpapan": "erepapan", "zombicide": "apaniokú", "tabletop": "tabletop", "baduk": "baduk", "bloodbowl": "bolubọọluẹjẹ", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "<PERSON><PERSON><PERSON><PERSON>", "connectfour": "koderinmerin", "heroquest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidatavolo": "a<PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tablegames": "ereidarapọ", "dicegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "creationludique": "ìṣẹ̀dáeréìdánílárayá", "tabletoproleplay": "erepereatapatọ", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "àwọnìròraẹ̀rújẹ́", "switchboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "infinitythegame": "er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "ikuijoba", "yahtzee": "yahtzee", "chutesandladders": "erepẹ̀pẹ̀àtièsùn", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "<PERSON><PERSON><PERSON>", "planszówki": "planszów<PERSON>", "rednecklife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardom": "àárẹ̀", "applestoapples": "apple_si_apple", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "papawọser<PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "twilightimperium", "horseopoly": "eṣinopoly", "deckbuilding": "kíkọ́dẹ̀ẹ́kì", "mansionsofmadness": "ilengbengbetialarun", "gomoku": "gomoku", "giochidatavola": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "ojijibrimstone", "kingoftokyo": "o<PERSON><PERSON><PERSON>", "warcaby": "okeawon", "táblajátékok": "<PERSON><PERSON><PERSON>", "battleship": "ọkọ̀ogun", "tickettoride": "ìwénibálọ", "deskovehry": "deskovehry", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "ereawokoribọọlu", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "<PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "ereawoṣe", "starwarslegion": "starwarslegion", "gochess": "ẹlọchess", "weiqi": "weiqi", "jeuxdesocietes": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "ogungbogburu", "arksurvivalevolved": "arksurvivalevolved", "dayz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "identityv": "idvn", "theisle": "<PERSON><PERSON><PERSON><PERSON>", "thelastofus": "awọnẹnitokẹhin", "nomanssky": "ayeawonokurinkan", "subnautica": "subnautica", "tombraider": "asawodeiledokunkun", "callofcthulhu": "<PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "láàárínwa", "eco": "eko", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "aṣẹdaaye", "daysgone": "ọjọtik<PERSON>", "fobia": "ìbẹ̀rù", "witchit": "witchit", "pathologic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7eọjọ", "thelongdark": "okunkuntiti", "ark": "ark", "grounded": "<PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "ipinleibaje2", "vrising": "vrising", "madfather": "baba<PERSON><PERSON><PERSON>", "dontstarve": "<PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "ipanatitani", "frictionalgames": "<PERSON><PERSON><PERSON>ag<PERSON><PERSON>", "hexen": "aje", "theevilwithin": "esubilẹninu", "realrac": "gidirac", "thebackrooms": "il<PERSON><PERSON><PERSON>", "backrooms": "ọ̀dọ̀ẹ̀yìn", "empiressmp": "empiressmp", "blockstory": "itanidena", "thequarry": "ebipa", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON>", "thewalkingdeadgame": "ereti<PERSON><PERSON><PERSON>", "wehappyfew": "àwọndíẹ̀tayọ̀", "riseofempires": "idarajiawonijoba", "stateofsurvivalgame": "ìpinlẹ̀ìwàláàyè", "vintagestory": "itanogbologbo", "arksurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "imísímí", "alisa": "alisa", "westlendsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastsofbermuda": "awọnẹrankoilubẹmuda", "frostpunk": "frostpunk", "darkwood": "igbodúdú", "survivalhorror": "eruti<PERSON><PERSON><PERSON>", "residentevil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil2": "eremoodogbaabele2", "residentevil4": "kokoerupiniberu4", "residentevil3": "residentevil3", "voidtrain": "òfuurufuelegbe", "lifeaftergame": "igbesehineere", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "ogunnáà<PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "iṣẹakoroayika", "kuon": "kuon", "cryoffear": "igbéẹ̀rùbojo", "raft": "òkun", "rdo": "rdo", "greenhell": "igbookunkun", "residentevil5": "residentevil5", "deadpoly": "poliooku", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "<PERSON><PERSON><PERSON><PERSON>", "littlenightmares2": "ęrubabaalẹkekere2", "signalis": "<PERSON><PERSON><PERSON>", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "ọmọkùn<PERSON>ó", "rustvideogame": "er<PERSON><PERSON>", "outlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "ifẹ́ìmọ́lẹ̀", "7day2die": "ojo<PERSON><PERSON><PERSON>u", "sunlesssea": "okusiwolalorun", "sopravvivenza": "<PERSON><PERSON><PERSON><PERSON>", "propnight": "alẹpariwo", "deadisland2": "òkúerekùṣù2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "ikuagbaye", "cataclysmdarkdays": "ipayaokunkun", "soma": "soma", "fearandhunger": "ìbẹ̀rùàtiebi", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "ibigbehinikuehin", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "agoibọnmẹta", "aloneinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternights": "gbogboalẹ", "craftopia": "ayẹyẹiṣẹọwọ", "theoutlasttrials": "idanwotheoutlast", "bunker": "<PERSON><PERSON><PERSON><PERSON>", "worlddomination": "agbayedasile", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "awonogbeniikualaṣ<PERSON>", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kifẹ", "wh40": "wh40", "warhammer40klove": "ifẹwarhammer40k", "warhammer40klore": "ìtànàròsọwarhammer40k", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "tẹmpuluokulẹsọsi", "vindicare": "gbẹ̀san", "ilovesororitas": "mofẹ́rànsororitas", "ilovevindicare": "mofẹ́ràngbẹ̀sẹ̀", "iloveassasinorum": "moferanẹmọbarawo", "templovenenum": "ifẹtẹmpilọrẹeniyan", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "awọnoniṣẹapaniyan", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ayeawonijoba", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "ẹnimejilopọndandan", "wingspan": "iyeapakan", "terraformingmars": "ìdásílẹ̀ayémars", "heroesofmightandmagic": "awọnakọniokikiatigbara", "btd6": "btd6", "supremecommander": "ọgáàgbà", "ageofmythology": "<PERSON><PERSON><PERSON>a", "args": "args", "rime": "rime", "planetzoo": "ekoerankoeluuju", "outpost2": "aago2", "banished": "ti_a_le_kuro", "caesar3": "caesar3", "redalert": "ipenijaepe", "civilization6": "ọlaju6", "warcraft2": "warcraft2", "commandandconquer": "aṣẹnijakosere", "warcraft3": "warcraft3", "eternalwar": "oguntitilaye", "strategygames": "ereo<PERSON>", "anno2070": "anno2070", "civilizationgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization4": "ìṣẹ̀dálẹ̀ìlú4", "factorio": "factorio", "dungeondraft": "dungeonkọọlu", "spore": "spore", "totalwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "travian": "travian", "forts": "àgbàlá", "goodcompany": "ẹgbẹrere", "civ": "civ", "homeworld": "il<PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "yárajùsànmọ̀nàmọ́ra", "forthekings": "awọnọba", "realtimestrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "kabiyesioniadeji", "eu4": "eu4", "vainglory": "ìkúnlógbéró", "ww40k": "ww40k", "godhood": "ẹnitiojẹolorun", "anno": "anno", "battletech": "ogunẹrọ", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "etametapin", "davesfunalgebraclass": "kilasialgebrafundave", "plagueinc": "aarun<PERSON><PERSON><PERSON>", "theorycraft": "ìgbéròẹ̀rọ", "mesbg": "mesbg", "civilization3": "kedere3", "4inarow": "<PERSON><PERSON><PERSON><PERSON>", "crusaderkings3": "ọbaajagun3", "heroes3": "heroes3", "advancewars": "<PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "ekunaṣojueledumare2", "disciples2": "ọmọẹ̀yìn2", "plantsvszombies": "ewekosiokunokunkun", "giochidistrategia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stratejioyunları": "awọnerejẹ", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "ọbaaláńgbá", "worldconquest": "àgbáyéjẹ́gbẹ́", "heartsofiron4": "àwọnọkànírin4", "companyofheroes": "awọnakíkanjúja<PERSON>jagun", "battleforwesnoth": "<PERSON><PERSON><PERSON>n<PERSON>not<PERSON>", "aoe3": "aoe3", "forgeofempires": "ileisegunawonalafia", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "ẹyẹẹyẹpẹpẹyẹ", "phobies": "ìbẹ̀rù", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "itaode", "turnbased": "igbakugba", "bomberman": "bomberman", "ageofempires4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization5": "itanolajuila5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "ìṣẹ́ògùn", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "ọdun1800", "estratégia": "ìlànàìṣèjẹ", "popfulmail": "popfulmail", "shiningforce": "agbaraidan", "masterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "etodysonsphere", "transporttycoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unrailed": "kòsíojúọ̀nà", "magicarena": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "galaxylife": "ayeafẹfẹ", "wolvesvilleonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slaythespire": "sẹgunilẹ̀gangan", "battlecats": "ologboo<PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "ilusimcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "<PERSON><PERSON><PERSON><PERSON>", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "er<PERSON><PERSON>o", "needforspeedcarbon": "iwọnereẹsẹkabọn", "realracing3": "eretokere3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "er<PERSON>unawonsimslo<PERSON>", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "pẹ́jù", "deadbydaylight": "ikupajọmọ", "alicemadnessreturns": "alicemad<PERSON>ret<PERSON>s", "darkhorseanthology": "akojọpọawọnalágbaràòkùnkùn", "phasmophobia": "ẹ̀rùabami", "fivenightsatfreddys": "orunmaruǹnibaba", "saiko": "saiko", "fatalframe": "eréìbẹ̀rùikú", "littlenightmares": "ẹ̀rùoruṣikẹkẹrẹ", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "ìlékún<PERSON>lé", "deadisland": "eretuoku", "litlemissfortune": "kekerebinrinalailoriire", "projectzero": "iseakosobodo", "horory": "ẹ̀rùjẹ́jẹ́", "jogosterror": "<PERSON><PERSON><PERSON><PERSON>", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "ẹkaaboonibugbe2", "gamingdbd": "erelerekannadeadorunkn", "thecatlady": "iyaologbo", "jeuxhorreur": "<PERSON><PERSON><PERSON>", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "orukoaṣiri", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "it<PERSON><PERSON><PERSON>a", "solitaire": "<PERSON><PERSON><PERSON><PERSON>", "poker": "pokà", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "àwọnkọ́kọ́rọ́ìdánimọ̀", "cardtricks": "<PERSON><PERSON><PERSON>", "playingcards": "ka<PERSON>ere<PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "ẹlẹsẹnẹtì", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "awọnkaaditasọọja", "pokemoncards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "esegidigidikadi", "duellinks": "duellinks", "spades": "ọkọ́", "warcry": "igbeogun", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "ọbaaláàyà", "truco": "truco", "loteria": "latari", "hanafuda": "hana<PERSON>da", "theresistance": "<PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "idijeyugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "ojualawoẹfunfun", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rọ́mì", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "erepẹ", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "awọnẹmiogun", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "er<PERSON><PERSON><PERSON><PERSON>", "žolíky": "àw<PERSON><PERSON>_à<PERSON><PERSON>_<PERSON>", "facecard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfight": "<PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "a<PERSON><PERSON><PERSON><PERSON>", "magiccartas": "awọnkaditomidan", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "er<PERSON><PERSON><PERSON><PERSON>", "skipbo": "skip<PERSON>", "unstableunicorns": "awọnẹṣinalaifiduro", "cyberse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classicarcadegames": "awọneregamẹsiakẹẹdẹatijọ", "osu": "osu", "gitadora": "gitadora", "dancegames": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "eréìtàlẹ́kú", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "iṣẹakanmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "akọrinagbáàwín", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pkxd": "pkxd", "sidem": "ẹgbẹmi", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "gbóoríkíoríintanẹ́ẹ̀tì", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spotit": "r<PERSON>i", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "buloku<PERSON>ku", "logicpuzzles": "a<PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikscube": "rubikscube", "crossword": "or<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motscroisés": "òrọ̀ìsọ̀tẹ́lẹ̀", "krzyżówki": "oroasokotan", "nonogram": "nonogram", "bookworm": "oníwèkíwè", "jigsawpuzzles": "papoaworan", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "àlọ́", "riddles": "aalo", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "ninu", "angrybirds": "àwọnẹyẹbínú", "escapesimulator": "atalọsimuleta", "minesweeper": "aṣàwárìbọ́ọ̀bù", "puzzleanddragons": "idibonatimufa", "crosswordpuzzles": "oroasoteleapejuwe", "kurushi": "k<PERSON>hi", "gardenscapesgame": "eregbagbogbosaaju", "puzzlesport": "ereeled<PERSON>", "escaperoomgames": "eregbegbonafib<PERSON><PERSON>de", "escapegame": "eregbigbọ", "3dpuzzle": "puzulu3d", "homescapesgame": "ereorileileorin", "wordsearch": "ìwáọ̀rọ̀", "enigmistica": "enigmistica", "kulaworld": "<PERSON><PERSON><PERSON><PERSON>", "myst": "asiri", "riddletales": "alotanes", "fishdom": "fishdom", "theimpossiblequiz": "idiwoìbéèrèk<PERSON>ṣ<PERSON>ṣe", "candycrush": "candycrush", "littlebigplanet": "ilẹayẹkekerealakọbẹrẹ", "match3puzzle": "ereṣẹtaalulopometa", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "aláìlẹ́sẹ̀ẹ́sẹ̀", "rubikcube": "rubikcube", "cuborubik": "cubor<PERSON>k", "yapboz": "<PERSON><PERSON><PERSON><PERSON>", "thetalosprinciple": "ipilẹsẹtalos", "homescapes": "ileelewailegbeje", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "talomi", "tycoongames": "<PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "cubosderubik", "cruciverba": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ciphers": "awọnakọsilẹaṣiri", "rätselwörter": "or<PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "tùrìnípùbọ́ọ̀yì", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON>", "nobodies": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "guessing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "oroamumafonka", "syberia2": "syberia2", "puzzlehunt": "wiwa<PERSON>", "puzzlehunts": "aw<PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "ẹṣẹologbo", "quebracabeça": "<PERSON><PERSON><PERSON><PERSON>", "hlavolamy": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "inaig<PERSON>ehintobasegun", "autodefinidos": "awọntiaraẹnisọ", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "eregeesetikoloruko", "cassetête": "wahala", "limbo": "<PERSON><PERSON><PERSON>", "rubiks": "rubiks", "maze": "<PERSON><PERSON><PERSON>", "tinykin": "kekerekere", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "àwọnẹ̀yà", "portalgame": "eréìlẹ̀kùnààyè", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "awọnaloapamo", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopoly": "monopoly", "futurefight": "<PERSON><PERSON><PERSON><PERSON>", "mobilelegends": "<PERSON><PERSON><PERSON><PERSON>", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "aláìníìbákẹ́gbẹ́", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "irawòàlúmọ́<PERSON>ì", "stateofsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON>aye", "mycity": "<PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "ogunfagure", "fategrandorder": "fategrandorder", "hyperfront": "iwajurekoja", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "awọnak<PERSON>agbara", "honkaiimpact": "honkaiimpact", "soccerbattle": "ijasoccerbọọlu", "a3": "a3", "phonegames": "erefoni<PERSON>", "kingschoice": "aṣàyàn<PERSON>ba", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "ẹlẹ́rìí_mọ́tò", "tacticool": "taki<PERSON><PERSON>olu", "cookierun": "<PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "kosilookan", "craftsman": "oníṣẹ́ọwọ́", "supersus": "gbajuọmọ", "slowdrive": "wakọsẹlẹ", "headsup": "gbeborun", "wordfeud": "oroija", "bedwars": "<PERSON><PERSON><PERSON>", "freefire": "ináọ̀fẹ́", "mobilegaming": "ereorisero", "lilysgarden": "<PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bgmi": "bgmi", "teamfighttactics": "ereo<PERSON><PERSON><PERSON>", "clashofclans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pjsekai": "pjsekai", "mysticmessenger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thearcana": "<PERSON><PERSON><PERSON>", "8ballpool": "8ballpool", "emergencyhq": "ibugbangbayo", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "gbigbonngbigbon", "ml": "ml", "bangdream": "al<PERSON><PERSON><PERSON>", "clashofclan": "ajaofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "ọmọbin<PERSON><PERSON>", "beatstar": "gbé<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "ifẹkisipo", "androidgames": "aw<PERSON><PERSON>eg<PERSON>", "criminalcase": "ẹjọodaranmọran", "summonerswar": "onígbóníjà", "cookingmadness": "iṣẹwálẹ̀dídùn", "dokkan": "dokkan", "aov": "aov", "triviacrack": "idanimeeleadug<PERSON>", "leagueofangels": "awonangẹlieledumare", "lordsmobile": "lordsmobile", "tinybirdgarden": "ọgbàkékeréẹyẹ", "gachalife": "gachalife", "neuralcloud": "akọjọpọagbara", "mysingingmonsters": "awọnẹranmiorin", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "a<PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "ok<PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "ìtànsọ̀rọ̀mádàá", "futime": "igbadun", "antiyoy": "lòd<PERSON><PERSON><PERSON><PERSON><PERSON>", "apexlegendmobile": "apexlegendmobile", "ingress": "<PERSON><PERSON><PERSON><PERSON>", "slugitout": "jagunjaadiiye", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petpals": "ẹranabẹrẹ", "gameofsultans": "eregiobaawonalafia", "arenabreakout": "igboboojade", "wolfy": "<PERSON><PERSON><PERSON><PERSON>", "runcitygame": "er<PERSON><PERSON><PERSON>", "juegodemovil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avakinlife": "<PERSON><PERSON><PERSON><PERSON>", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "adanikekereonirun", "grandchase": "grandchase", "bombmebrasil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ldoe": "ldoe", "legendonline": "alay<PERSON><PERSON>", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "ìpèẹjòòrìndò", "shiningnikki": "nik<PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "ọnasilaik<PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "ijakudojudu3", "limbuscompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "awonoroerekore2", "soulknight": "à<PERSON><PERSON><PERSON><PERSON>_ọkùnrin_ẹ̀mí", "purrfecttale": "it<PERSON><PERSON><PERSON><PERSON>", "showbyrock": "ifihanyansalẹ", "ladypopular": "obinrinolokiki", "lolmobile": "lolmobile", "harvesttown": "iluerèébìbì", "perfectworldmobile": "ayekanpipe", "empiresandpuzzles": "àwọnilẹ̀ọbaàtiàwọnàdítuù", "empirespuzzles": "awakooleerekuse", "dragoncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "papọjaọkunrinyorumiind", "fanny": "ìdí", "littlenightmare": "kékeréòrúbúrúkú", "aethergazer": "aethergazer", "mudrunner": "onirinẹrẹdupẹ", "tearsofthemis": "omijẹẹlẹorun", "eversoul": "ẹmíayérayé", "gunbound": "ìbọnmímú", "gamingmlbb": "eremlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiesgbéérú", "eveechoes": "eveechoes", "jogocelular": "eregbẹkẹ", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "iyatonase", "cabalmobile": "cabalmobile", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "eregamingbgmi", "girlsfrontline": "awọnọm<PERSON>binriniwajuila", "jurassicworldalive": "jurassicworldalive", "soulseeker": "ẹmiolukanokan", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "itan<PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeraiders": "awanonigbokegiga", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "awọnologboogun", "dnd": "dnd", "quest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidiruolo": "awọneregímé<PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofdarkness": "ayerere", "travellerttrpg": "arinrinkejitttrpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "ẹgbẹifẹ", "d20": "d20", "pokemongames": "erep<PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "enuete", "porygon": "porygon", "pokemonunite": "pokemonìsọ̀kan", "entai": "entai", "hypno": "irúlẹ́", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonelese", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "ẹgbẹrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "awọnẹdaapọ", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "ẹgbẹmystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "kékeréaláwòeléwà", "shinypokemon": "poke<PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "irúnpókímọ̀nì", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "ọgáàmúpokémon", "pokémonsleep": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "awonomodeokepojimu", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "ọdẹdidan", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "blitziagbaye", "jeudéchecs": "erechess", "japanesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chinesechess": "ìfigbáladà<PERSON>", "chesscanada": "ṣẹṣikanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "awọnayefasilẹ", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "calabozosydragones", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "òg<PERSON><PERSON><PERSON>jìn<PERSON>", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventure", "darksun": "or<PERSON><PERSON>u", "thelegendofvoxmachina": "itaniyinvoxmachina", "doungenoanddragons": "ṣenílẹ̀kùnlàtijà", "darkmoor": "ig<PERSON><PERSON><PERSON>", "minecraftchampionship": "asejuminicraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "idanwowa", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "mod<PERSON><PERSON><PERSON><PERSON>", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON><PERSON>ìná", "fru": "iyabaje", "addons": "àfikún", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftapo", "minecraft360": "minecraft360", "moddedminecraft": "minecrafttiawonfikun", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "alarinilẹ", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "iluminecraftcity", "pcgamer": "awakọnpuk<PERSON><PERSON>", "jeuxvideo": "jeuxvideo", "gambit": "ète", "gamers": "a<PERSON><PERSON><PERSON><PERSON>", "levelup": "ìgbéga", "gamermobile": "ereoriẹrọalagbeka", "gameover": "par<PERSON><PERSON>", "gg": "gg", "pcgaming": "eregamecomputeresi", "gamen": "gáàmù", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casualgaming": "erege<PERSON><PERSON>", "gamingsetup": "ètòọ̀rọ̀eré", "pcmasterrace": "pcgbajumoye", "pcgame": "eregba", "gamerboy": "ọmọkùnrìnalárédìgà", "vrgaming": "eregiridaraya", "drdisrespect": "drdisrespect", "4kgaming": "eregame4k", "gamerbr": "gamerbr", "gameplays": "aw<PERSON><PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "<PERSON><PERSON><PERSON><PERSON>", "semigamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamergirls": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermoms": "awọniyagamer", "gamerguy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamewatcher": "oniwoereọmọwe", "gameur": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "a<PERSON><PERSON><PERSON><PERSON>", "otoge": "otoge", "dedsafio": "ì<PERSON><PERSON><PERSON><PERSON>", "teamtryhard": "ẹgbẹawọntoyanjuù", "mallugaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pawgers": "awọnpọg<PERSON>", "quests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygaming": "ig<PERSON><PERSON><PERSON>", "gamelpay": "gamelpay", "juegosdepc": "aw<PERSON><PERSON><PERSON><PERSON>", "dsswitch": "dsswitch", "competitivegaming": "<PERSON><PERSON><PERSON><PERSON>", "minecraftnewjersey": "minecraftnewjersey", "faker": "eleke", "pc4gamers": "pc4awọnolereonijakadi", "gamingff": "eregamingi", "yatoro": "yatoro", "heterosexualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "eregamepc", "girlsgamer": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "iṣẹojoojumọ", "gamegirl": "ọmọb<PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesetup": "etogbowoleereaje", "overpowered": "talọgbara", "socialgamer": "<PERSON>eg<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "proplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayer": "aṣerejẹ", "myteam": "ẹgbẹmi", "republicofgamers": "orílẹ̀èdèawọnelere", "aorus": "aorus", "cougargaming": "obinrinagbalagamọ", "triplelegend": "ọlọlaoniled<PERSON>ji", "gamerbuddies": "awọnọrẹalaripada", "butuhcewekgamers": "ṣeolowongamergirl", "christiangamer": "oloribere", "gamernerd": "alaregbe", "nerdgamer": "ọmọwẹgamẹ", "afk": "kòsínibíyìí", "andregamer": "andregamer", "casualgamer": "aṣerébẹ̀rẹ̀", "89squad": "89squad", "inicaramainnyagimana": "bawonikansee<PERSON><PERSON><PERSON>", "insec": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gemers": "awọngẹmọ", "oyunizlemek": "oyunizlemek", "gamertag": "orukoeledare", "lanparty": "<PERSON><PERSON><PERSON><PERSON>", "videogamer": "ọ̀ṣèrébídiò", "wspólnegranie": "gbogbowapa<PERSON>", "mortdog": "mortdog", "playstationgamer": "oṣereplaystationmi", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "oniṣeretonialafia", "gtracing": "gtracing", "notebookgamer": "oníwéìdáríkòmẹ́rẹ́", "protogen": "protogen", "womangamer": "ob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "ajẹkoko", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "orinnintendo", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "awọnọkunniyanlailabẹ", "switch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "aláisanmimu", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "ọ̀gáàgbámariokart", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "awọnọmọoruninarẹwa", "tomodachilife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ahatintime": "aigbakannidiidan", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "awọneregbẹsẹibaramu", "nintendogames": "ereninitendo", "thelegendofzelda": "ìtànakọ́kọ́zelda", "dragonquest": "dragonquest", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "ọrẹmipedro", "legendsofzelda": "awọnakọọlẹzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "awọnere51", "earthbound": "ilẹayé", "tales": "àlọ́", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "ilanaoni<PERSON>met<PERSON>", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "ijaesobadifurday", "nintendos": "nintendos", "new3ds": "3dstituntun", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "awọnjagunjagunhyrule", "mariopartysuperstars": "ereeemarioparty", "marioandsonic": "marioandsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "<PERSON><PERSON><PERSON><PERSON>", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "ajákolẹbúrúkú", "vanillalol": "vanillalẹrin", "wildriftph": "wildriftph", "lolph": "ẹrinpipa", "leagueoflegend": "ereg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "ègbéàwọnàgbàáyéwild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "liigiawonakikanjuespeeni", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "awonalabaseyoruba", "gaminglol": "eregaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingfortnite": "ereidamufortnite", "fortnitebr": "fortnitebr", "retrovideogames": "awọnereyorio<PERSON>", "scaryvideogames": "awonerekoriidarayajagi<PERSON>gan", "videogamemaker": "alá<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "erefidio", "videosgame": "aw<PERSON><PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "ogunidijuawonereori", "arcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "awọnọrẹalapata", "farmingsimulator": "er<PERSON><PERSON><PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamelore": "itanleeregamufidio", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "igbookuro", "dreamscape": "àlápẹ̀rẹ̀", "starcitizen": "starcitizen", "yanderesimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadspace": "ayeoku", "amordoce": "ifẹaladun", "videogiochi": "aw<PERSON><PERSON><PERSON><PERSON><PERSON>", "theoldrepublic": "obaluayeijoun", "videospiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touhouproject": "<PERSON><PERSON><PERSON><PERSON>", "dreamcast": "<PERSON><PERSON><PERSON>", "adventuregames": "ereadenture", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "itanawonig<PERSON>", "retrogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "re<PERSON><PERSON><PERSON>", "vintagecomputing": "kompútàìgbàdéédéé", "retrogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagegaming": "eregamingijoun", "playdate": "<PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "injustice2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowthehedgehog": "ṣadosihẹjihọgu", "rayman": "rayman", "skygame": "erefisanma", "zenlife": "igbesialáfíà", "beatmaniaiidx": "beatmaniaiidx", "steep": "gaga", "mystgames": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "eregamingblockchain", "medievil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "consolegaming": "ereidaraya", "konsolen": "konsolen", "outrun": "s<PERSON><PERSON>_<PERSON>", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "eregamingẹru", "monstergirlquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nọmọbìnrin<PERSON>", "supergiant": "omooranorin", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "awọneregbegbe", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "aw<PERSON><PERSON><PERSON><PERSON>", "interactivefiction": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "ikẹhinawọnti2", "amantesamentes": "awọnolufẹolufẹ", "visualnovel": "ìtànà<PERSON><PERSON><PERSON><PERSON>", "visualnovels": "awọneweereapẹrẹ", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON>", "tcrghost": "tcrelee<PERSON>", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "ọmọbinrintitanic", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "ìpààrọ̀erékùṣù", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON>", "novelavisual": "itankomeji", "thecrew2": "ikoṣiṣẹmi2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "er<PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "ẹdùnrere", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "ìjàgbàdúnafẹ́fẹ́gbéeléwé", "wiiu": "wiiu", "leveldesign": "ìṣ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starrail": "<PERSON><PERSON><PERSON>", "keyblade": "keyblade", "aplaguetale": "<PERSON><PERSON><PERSON><PERSON>", "fnafsometimes": "fnafnigbakan", "novelasvisuales": "awọnereitanniworan", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "eregba", "videojuejos": "awọ<PERSON><PERSON><PERSON><PERSON>", "videogamedates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "ifẹsugbonmi", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hulkgames": "erewon", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "ẹkúnrẹ́rẹ́padà", "gamstergaming": "gamstergaming", "dayofthetantacle": "ojotentacle", "maniacmansion": "<PERSON><PERSON><PERSON><PERSON>", "crashracing": "er<PERSON><PERSON>o", "3dplatformers": "àwọnẹ̀rọìdárayíká3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "idàlúogun", "storygames": "er<PERSON>áwàd<PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "aṣábúùfọ̀hùn", "beyondtwosouls": "kojasemiokun", "gameuse": "lilo_ere", "offmortisghost": "ẹ̀mífànímò", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "agbara", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "awọn<PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "kíákíá", "fzero": "fzero", "gachagaming": "<PERSON><PERSON><PERSON>a", "retroarcades": "àwọnìléìdarayá<PERSON>gb<PERSON>j<PERSON>́", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON><PERSON>", "powerwashsim": "idotier<PERSON><PERSON><PERSON><PERSON>", "coralisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "ayeomiran", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "àpapọ̀bọ́ọ̀lù", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "irinwàhálà", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "ojúkòkòròìtìjú", "simulator": "oniranse", "symulatory": "simuletọ", "speedrunner": "eléréíkíák<PERSON>", "epicx": "epicx", "superrobottaisen": "robot<PERSON><PERSON>alag<PERSON>", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "ereg<PERSON><PERSON>", "wonderlandonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skylander": "skylander", "boyfrienddungeon": "ọrẹkunrindungeon", "toontownrewritten": "toontownkotunko", "simracing": "eréọkọ̀ayọ́kùnrin", "simrace": "<PERSON><PERSON><PERSON><PERSON>", "pvp": "idije", "urbanchaos": "ka<PERSON><PERSON><PERSON>lu", "heavenlybodies": "àwọnararẹwà", "seum": "<PERSON><PERSON><PERSON><PERSON>", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "olùṣọ́pọ̀kú", "spaceflightsimulator": "àṣesímiùlétọ̀ọ̀fọ̀sánmáyà", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "gépàkókò", "foodandvideogames": "oun<PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "aw<PERSON><PERSON><PERSON><PERSON>ere", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "i<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "ayeawonagbaye", "handygame": "<PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "awọnitanitumoatiaw<PERSON>ner<PERSON>i", "oldschoolvideogames": "awọnerelẹdẹmẹta", "racingsimulator": "erefafiweeresakoje", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "orinolokiki", "famitsu": "famitsu", "gatesofolympus": "ilẹkunolimpọsi", "monsterhunternow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rebelstar": "alátagbà", "indievideogaming": "eregamefindiike<PERSON>ì", "indiegaming": "<PERSON><PERSON><PERSON>a", "indievideogames": "awọneregameolominira", "indievideogame": "eréìdárayaonítọ̀wọ́", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bufffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "máṣẹ́dámẹ́hùn", "projectl": "iṣẹakanṣe", "futureclubgames": "er<PERSON><PERSON><PERSON>sik<PERSON>", "mugman": "mugman", "insomniacgames": "aw<PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "awọneregamugbogbo", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aperturescience": "imosa<PERSON><PERSON><PERSON>", "backlog": "<PERSON><PERSON><PERSON><PERSON>p<PERSON>", "gamebacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beastlord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosretro": "awọneregberetẹlẹ", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alanwake": "alanwake", "stanleyparable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reservatoriodedopamin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "staxel": "staxel", "videogameost": "orinereaw<PERSON>nerefiidio", "dragonsync": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "moferekofxv", "arcanum": "<PERSON><PERSON><PERSON>", "neoy2k": "neoy2k", "pcracing": "eréìjepákọ̀ǹpútà", "berserk": "j<PERSON><PERSON><PERSON><PERSON>", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animedaro", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "iwọnaníìmè", "animewithplot": "animepeluetuadawoyi", "pesci": "pesci", "retroanime": "retroanime", "animes": "aw<PERSON><PERSON><PERSON>", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "anime90s", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "ọgábẹ́gi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "animeti2000s", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneakọkọ", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thevisionofescaflowne": "iransíkanuescaflowne", "slayers": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "ereanime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "ejaọ̀gẹ̀dẹ̀", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "hanakokuntowadinibiilealagbara", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "mangaskipbeat", "vanitas": "vanitas", "fireforce": "agbárainá", "moriartythepatriot": "moriartyoniṣẹtẹlẹorílẹ̀èdè", "futurediary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fairytail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "ikọfẹabinitutumẹhin", "parasyte": "kokoro", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "ifẹolorun", "blmanga": "blmanga", "horrormanga": "erupamangaik<PERSON>lu", "romancemangas": "awọnmangaifẹ", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "iránṣẹ́bìnrindrágónì", "blacklagoon": "ẹriodo", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "awọnolugbeilẹ", "geniusinc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "ìtọ́kasíàṣírí", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "ọkànfíkùn", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "awọnangẹlikú", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "<PERSON><PERSON><PERSON><PERSON>", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "animeisekai", "sagaoftanyatheevil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shounenanime": "animekekere", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "ọmọkunrinatiẹranko", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "bímoṣetọ́jútánì<PERSON>á", "fullmoonwosagashite": "osupaamuujagboro", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "ẹlẹwàatìẹ̀rù", "martialpeak": "titenijaisuju", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "ọmọbìnrinhighscore", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "ọmọ<PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "animeatijọ", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "idaabon", "loli": "loli", "horroranime": "animeẹru", "fruitsbasket": "aponda<PERSON>n", "devilmancrybaby": "ọm<PERSON><PERSON><PERSON><PERSON><PERSON>n<PERSON>_èṣù_tó_ń_sunkún", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "ifẹlaaye", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "alẹjòmímìní", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "ilẹayétiwaseleri", "monstermanga": "<PERSON><PERSON><PERSON>", "yourlieinapril": "iróẹniosùàpríìlì", "buggytheclown": "buggyawadaapanilerin", "bokunohero": "bokunohero", "seraphoftheend": "serafia<PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "ẹlẹwọnokunkule", "jojolion": "jojo<PERSON>", "deadmanwonderland": "okúòkúilẹ̀àjèjì", "bannafish": "ejabanana", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "ọkọ", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "ọkànaláìlẹ́kọ̀ọ́", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "ilàòkùnẹ̀bù", "toyoureternity": "títílọ́tọnlá", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "igbaabulawe", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "ìmọ̀lẹ̀ẹ́bùn", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "parẹ", "bluelock": "bluelock", "goblinslayer": "apaniajamba", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "ẹlẹjẹmi", "mugi": "mugi", "blueexorcist": "blueexorcist", "slamdunk": "gbọlẹgbọlẹ", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "moju<PERSON>", "spyfamily": "ẹbispai", "airgear": "ẹ̀rọigbéafẹ́fẹ́", "magicalgirl": "ọmọbìnrintimọ́lẹ̀", "thesevendeadlysins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "iléẹkọ̀ẹ̀wọ̀n", "thegodofhighschool": "ọlọrunileekogiga", "kissxsis": "ifẹnkanifẹ", "grandblue": "grandblue", "mydressupdarling": "a<PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "ayeawọnanime", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "adej<PERSON><PERSON>in", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "ìfẹ́anime", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "kokuonibakunjepataki", "bloodlad": "ẹjẹfaaji", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "ẹṣinijàbó", "adioseri": "moda<PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "ifẹokobinrin", "starsalign": "a<PERSON>ọnirawoparapọ", "romanceanime": "animeerefẹ", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "aṣẹṣẹri", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "igbasilẹaragbara", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "<PERSON><PERSON><PERSON><PERSON>", "highschoolofthedead": "ileekoagbaojoku", "germantechno": "technojámánì", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "ọmọọbatén<PERSON>sì", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "kilasionikug<PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "ikuja<PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejapani", "animespace": "ayeaworan", "girlsundpanzer": "awọnọmọbinrin<PERSON>", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ratman": "okutẹmọlẹ", "haremanime": "animeharem", "kochikame": "kochikame", "nekoboy": "ọmọkúnrìnológbò", "gashbell": "gashbell", "peachgirl": "ọmọbìnrinpíìsì", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "ẹgbẹawọnasẹwọokobo", "dragonquestdai": "dragonquestdai", "heartofmanga": "okanmanga", "deliciousindungeon": "d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "àkọsílẹ̀ogunìparun", "funamusea": "igbadun", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "tẹrẹsíbàtàwọlé", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "ìd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "ọlọgbọnparty", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "aṣọẹlẹdẹaje", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangajẹayemi", "dropsofgod": "ẹ̀kọ́ọ́lọ́run", "loscaballerosdelzodia": "awonakonikunrinzodia", "animeshojo": "animeshojo", "reverseharem": "okunrinpupoidamewa", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "ẹkọniẹnlaonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mybossdaddy": "ogamibaami", "gear5": "gear5", "grandbluedreaming": "<PERSON><PERSON><PERSON><PERSON>", "bloodplus": "ejeati", "bloodplusanime": "animekejiekeje", "bloodcanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodc": "ẹ̀jẹ̀c", "talesofdemonsandgods": "itanogunegunatieniyan", "goreanime": "goreanime", "animegirls": "awọnọm<PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "ẹyẹkóroxbúburújùlọ", "splatteranime": "animesplatter", "splatter": "ìtúká", "risingoftheshieldhero": "igbegashieldhero", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animespain", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "ọmọdẹerinmi", "liarliar": "irọ́jẹ́irọ́jẹ́", "supercampeones": "akọnipataki", "animeidols": "animeidols", "isekaiwasmartphone": "isekaiyẹnjẹfoonualágbèéká", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "ipeakuoru", "bakuganbrawler": "onijabakugan", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "wakamanga", "princessjellyfish": "ọmọbinrineniẹja", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekiss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "ìtàns<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persocoms": "persocoms", "omniscientreadersview": "gbogbonkankaweoniwe", "animecat": "ologboanimu", "animerecommendations": "awọni<PERSON>", "openinganime": "animeibere", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "igbaeweeweremilọfẹ", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefightergundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mẹ́kì", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "fúnfún", "deathnote": "ikusilekole", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "ogungbe<PERSON><PERSON><PERSON>", "greenranger": "alawoewérangà", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "apaniẹmi", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "ebukolotitani", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON>do<PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onepieceanime": "animeonepiecé", "attaquedestitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theonepieceisreal": "theone<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "revengers": "awọngbẹsan", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "adunbooeffect", "digimonstory": "itand<PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "<PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "kòṣẹ́ẹ̀mí", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "egbeagbateruolowoosokekere", "flawlesswebtoon": "webtoonitonitọ", "kemonofriends": "ọ̀rẹ́ẹranko", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "ajẹibobonifẹ", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "gbogboerekusu", "recuentosdelavida": "recuentosdelavida"}