{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrolohiya", "cognitivefunctions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychology": "si<PERSON>lohiya", "philosophy": "pilosopiya", "history": "<PERSON><PERSON><PERSON><PERSON>", "physics": "pisika", "science": "syensya", "culture": "kultur", "languages": "mga_pinulongan", "technology": "teknolohiya", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "astrologymemes", "enneagrammemes": "enneagrammemes", "showerthoughts": "mgahunahunasamtangnaligo", "funny": "<PERSON><PERSON><PERSON><PERSON>", "videos": "bidyos", "gadgets": "gadyets", "politics": "politika", "relationshipadvice": "tambagsarelasyon", "lifeadvice": "pag<PERSON><PERSON><PERSON><PERSON>", "crypto": "crypto", "news": "balita", "worldnews": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "ark<PERSON>lo<PERSON>ya", "learning": "pag<PERSON>on", "debates": "debate", "conspiracytheories": "mgateoryanokonspirasya", "universe": "uniberso", "meditation": "pag<PERSON><PERSON><PERSON><PERSON>", "mythology": "<PERSON><PERSON><PERSON><PERSON>", "art": "sining", "crafts": "mgalikhangkamot", "dance": "sayaw", "design": "disenyo", "makeup": "makeup", "beauty": "<PERSON><PERSON><PERSON><PERSON>", "fashion": "fashion", "singing": "p<PERSON><PERSON><PERSON>", "writing": "pagsulat", "photography": "potograpiya", "cosplay": "cosplay", "painting": "p<PERSON><PERSON><PERSON>", "drawing": "drawing", "books": "mgalibro", "movies": "salida", "poetry": "balak", "television": "telebisyon", "filmmaking": "pag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animation": "animasyon", "anime": "anime", "scifi": "scifi", "fantasy": "pan<PERSON>ya", "documentaries": "dokyu", "mystery": "mistery", "comedy": "komedya", "crime": "krimen", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "kalisang", "romance": "gugma", "realitytv": "realitytv", "action": "aksyon", "music": "musika", "blues": "ka<PERSON><PERSON>", "classical": "k<PERSON><PERSON>", "country": "nasud", "desi": "desi", "edm": "edm", "electronic": "elektroniko", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "balay", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "bi<PERSON>e", "concerts": "mgakonsyerto", "festivals": "mgapista", "museums": "mgamuseo", "standup": "tindog", "theater": "teatro", "outdoors": "gawas", "gardening": "pagtanom", "partying": "partyparty", "gaming": "dula", "boardgames": "<PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "dungeonsanddragons", "chess": "chess", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "pag<PERSON><PERSON>", "baking": "pagpanader<PERSON>", "cooking": "pagluto", "vegetarian": "vegetarian", "vegan": "vegan", "birds": "langgam", "cats": "gatos", "dogs": "iro", "fish": "isda", "animals": "hayop", "blacklivesmatter": "angkinabuhingitomimportante", "environmentalism": "environmentalismu", "feminism": "feminismo", "humanrights": "katuligangsatawo", "lgbtqally": "kakuyoglgbtq", "stopasianhate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "kakuyogsamgatrans", "volunteering": "pag<PERSON><PERSON><PERSON>", "sports": "isports", "badminton": "badminton", "baseball": "baseball", "basketball": "basketball", "boxing": "boksing", "cricket": "kriket", "cycling": "pag<PERSON><PERSON><PERSON><PERSON>", "fitness": "fitness", "football": "putbol", "golf": "golf", "gym": "gym", "gymnastics": "gimnast<PERSON>", "hockey": "hockey", "martialarts": "artesmarsyal", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "pagdagan", "skateboarding": "skateboarding", "skiing": "pagski", "snowboarding": "snowboarding", "surfing": "surfing", "swimming": "p<PERSON><PERSON><PERSON>", "tennis": "tennis", "volleyball": "bolleyball", "weightlifting": "pagbuhat", "yoga": "yoga", "scubadiving": "scubadiving", "hiking": "hiking", "capricorn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aquarius": "aquarius", "pisces": "pisces", "aries": "aries", "taurus": "taurus", "gemini": "gemini", "cancer": "kanse<PERSON>", "leo": "leo", "virgo": "birgo", "libra": "libra", "scorpio": "scorpio", "sagittarius": "sagittarius", "shortterm": "mubo", "casual": "kaswal", "longtermrelationship": "matagalnarelasyon", "single": "single", "polyamory": "polyamory", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "bayot", "lesbian": "lesbian", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "mgabantay", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kingsquest", "soulreaver": "kaumogkalag", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "alamatnispyro", "rouguelikes": "rouguelikes", "syberia": "si<PERSON>a", "rdr2": "rdr2", "spyrothedragon": "spyroangdragon", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "kilidsaadlaw", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "ligdong", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "mgabayanisakagubat", "cytus": "cytus", "soulslike": "muragkalag", "dungeoncrawling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribesofmidgard", "planescape": "planescape", "lordsoftherealm2": "lordsoftherealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "hubadhubad", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersivesims", "okage": "okage", "juegoderol": "juegoderol", "witcher": "witcher", "dishonored": "<PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "pakyas", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "pagmod", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "immersive", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidmotivation", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "nabuangsakaluoy", "otomegames": "dulangbabayi", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocar<PERSON><PERSON>nah<PERSON>", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "gaslands", "pathfinder": "tiggiya", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "oneshot", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "agalon", "yourturntodie": "imongturnona<PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "mga_marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgteksto", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "da<PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "pinakangit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "panahonsaeklipse", "disgaea": "disgaea", "outerworlds": "lainngkalibutan", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastywarriors", "skullgirls": "skullgirls", "nightcity": "s<PERSON><PERSON>gabi<PERSON>", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "gubatkalihokan", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "bantogangkalimtan", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "anakngkahayag", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "nangagu<PERSON>trono", "horizonforbiddenwest": "horizongin<PERSON>lian<PERSON>dl<PERSON><PERSON>", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "samaran", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "mangitaogbitoon", "goldensun": "goldensunday", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "bladesinthedarkph", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "yutang<PERSON><PERSON>n", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "demonyo_nga_buhi", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "pag<PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "kalungkutansalumaangkalibutan", "adventurequest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "du<PERSON><PERSON>ng<PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "talesofsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "<PERSON><PERSON><PERSON><PERSON>", "myfarog": "akongfarog", "sacredunderworld": "balaangkalibotan", "chainedechoes": "chainedechoes", "darksoul": "kalagngitngit", "soulslikes": "mg<PERSON><PERSON><PERSON><PERSON><PERSON>", "othercide": "<PERSON>ing<PERSON><PERSON><PERSON>", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "mgahaligisakahangturan", "palladiumrpg": "palladiumrpg", "rifts": "panagbuwag", "tibia": "tibia", "thedivision": "angdibisyon", "hellocharlotte": "helocharlotte", "legendofdragoon": "legendofdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampiroangmaskara", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werewolfangapocalypse", "aveyond": "aveyond", "littlewood": "gam<PERSON><PERSON><PERSON><PERSON>", "childrenofmorta": "mgaanaksamorta", "engineheart": "makinakasingkasing", "fable3": "fable3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edenehang<PERSON>ds<PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "starfield", "oldschoolrevival": "oldschoolbalik", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "kali<PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "kangitngab<PERSON>ngeon", "juegosrpg": "juegosrpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "wildheartsph", "bastion": "kuta", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "langitngarcadia", "shadowhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "breathoffire4", "mother3": "mother3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "la<PERSON>den", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "roleplay<PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "ka<PERSON>sawitches", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON>sang<PERSON><PERSON><PERSON>", "dračák": "dragun", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "awplanet": "awplanet", "theworldendswithyou": "angkalibota<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "magicsayuta", "blackbook": "itomnga<PERSON>breng", "skychildrenoflight": "anaksakalangitansahayag", "gryrpg": "gryrpg", "sacredgoldedition": "sakredgoldedisyon", "castlecrashers": "castlecrashers", "gothicgame": "dulagnogothic", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "<PERSON><PERSON><PERSON><PERSON>", "prophunt": "pangtago", "starrails": "starrails", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "<PERSON><PERSON><PERSON><PERSON>", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "dilibuwagon", "freeside": "libreng<PERSON>lid", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "deathroadsacanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "monsterhunter", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacy", "persona5": "persona5", "ghostoftsushima": "multosakatsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "kaonkalag", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "dilabaynotgames", "tacticalrpg": "tacticalrpg", "mahoyo": "mahoyo", "animegames": "animegames", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "tig<PERSON><PERSON><PERSON>sdi<PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "princessconnect": "princessconnect", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "tigula<PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindian", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "dula", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligangmgadamguhan", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gaiming", "overwatchleague": "overwatchleague", "cybersport": "cybersport", "crazyraccoon": "buangnabaknit", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantkompetitibo", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "tung<PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead": "nabyangparamamatay", "left4dead2": "left4dead2", "valve": "balbo", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "waypagsulatangtinginit", "goatsimulator": "goatsimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planetangka<PERSON>san", "transformice": "transformice", "justshapesandbeats": "mgapormaraymgabeat", "battlefield4": "battlefield4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanias", "overcooked": "<PERSON><PERSON><PERSON>", "interplanetary": "intergalaktiko", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "lungag", "stray": "hig<PERSON><PERSON>", "battlefield": "gubatan", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "kilay", "blackdesert": "black<PERSON>ert", "tabletopsimulator": "tabletopsimulator", "partyhard": "partypakapin", "hardspaceshipbreaker": "lisodgubaspaceshipbreaker", "hades": "hades", "gunsmith": "gunsmith", "okami": "<PERSON>ami", "trappedwithjester": "nabitaysakomedyante", "dinkum": "<PERSON><PERSON><PERSON>", "predecessor": "predesesor", "rainworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON>", "colonysim": "colonysim", "noita": "noita", "dawnofwar": "banagbanag", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "ngitngitkaayo", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "trabahantengkalag", "datingsims": "dateningsims", "yaga": "yaga", "cubeescape": "cubeescape", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "mgasyudadskylines", "defconheavy": "<PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsia", "virtualkenopsia": "virtualkenopsia", "snowrunner": "snowrunner", "libraryofruina": "libraryaniruina", "l4d2": "l4d2", "thenonarygames": "ang<PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "tigpan<PERSON>aogdal<PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "kalmadongatikpato", "battlebit": "battlebit", "ultimatechickenhorse": "pinakausahangmanokngka<PERSON>o", "dialtown": "dialtown", "smileforme": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "gab<PERSON>airing", "supermeatboy": "supermeatboy", "tinnybunny": "tinnygagmaybabuy", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "kadaut", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "tuktok", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "paladins", "earthdefenseforce": "tig<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "huntshowdown", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakillkill", "joinsquad": "apilkomonasquad", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "gubatinsurgentesabagyo", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "ka<PERSON>yanngapan<PERSON>lakaw", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "tumbongsalamin", "divisions2": "divisions2", "killzone": "patayzone", "helghan": "hel<PERSON>", "coldwarzombies": "coldwarzombies", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "labanangsaeroplano", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "gubatmoderno", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "boarderlands", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "iky<PERSON>_sa_tarkov", "metalslug": "metalslug", "primalcarnage": "gubatsakarniboro", "worldofwarships": "worldofwarships", "back4blood": "balik4dugo", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "hitman", "masseffect": "masseffect", "systemshock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "<PERSON><PERSON><PERSON><PERSON>", "doometernal": "doometernal", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "henerasyonzero", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "lalakinghot<PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "angmultongsasakit", "warface": "da<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossfire": "crossfire", "atomicheart": "atomicheart", "blackops3": "blackops3", "vampiresurvivors": "vampiresurvivors", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "kagawasan", "battlegrounds": "guba<PERSON>n", "frag": "<PERSON><PERSON><PERSON><PERSON>", "tinytina": "gamays<PERSON>na", "gamepubg": "gamepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "juegosfps", "convertstrike": "t<PERSON><PERSON><PERSON>amaay<PERSON>", "warzone2": "warzone2", "shatterline": "linya_nga_nabuak", "blackopszombies": "blackopszombies", "bloodymess": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "republikanokomando", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "sangang_yuta", "squad": "barkada", "destiny1": "destinolang1", "gamingfps": "gamingfps", "redfall": "redfall", "pubggirl": "pubggirl", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "naen<PERSON>", "farlight": "<PERSON><PERSON>ag", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "sweldo2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "sabon_isda", "ghostcod": "ghostcod", "csplay": "csplay", "unrealtournament": "digywanglaroangdigay", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "panghampas", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "patayansalapag", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "<PERSON><PERSON><PERSON>", "remnant": "salin", "azurelane": "azurelane", "worldofwar": "kalibutansakgubat", "gunvolt": "gunvolt", "returnal": "returnal", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "tawoangidlom", "quake2": "quake2", "microvolts": "microvolts", "reddead": "<PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "rust", "conqueronline": "sapawsaonline", "dauntless": "walayykahadlok", "warships": "mgabarkodigira", "dayofdragons": "ad<PERSON><PERSON>gadra<PERSON>", "warthunder": "warthunder", "flightrising": "flightrising", "recroom": "kwartosaduwa", "legendsofruneterra": "legendsofruneterra", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "phantasystaronline2", "maidenless": "<PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "worldoftanks", "crossout": "<PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "dulaginternet", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "basura", "newworld": "bagongkalibutan", "blackdesertonline": "blackdesertonline", "multiplayer": "dulad<PERSON>", "pirate101": "pirata101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "silkroad", "spiralknights": "spiralknights", "mulegend": "mua<PERSON><PERSON><PERSON>", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "propetasadragon", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multijugador", "angelsonline": "mgaanghelsaonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "<PERSON><PERSON><PERSON><PERSON>", "blueprotocol": "blueprotocol", "perfectworld": "perpektongkalibutan", "riseonline": "bangononline", "corepunk": "corepunk", "adventurequestworlds": "ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "animaljam", "kingdomofloathing": "ginhariansapagkalagot", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "streetfighter", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mkdeadlyalliance", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "mura<PERSON><PERSON><PERSON>", "retrofightinggames": "mgadulagbarogawayretro", "blasphemous": "blasphemous", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersagol", "mugen": "mugen", "warofthemonsters": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "mgacyberbot", "armoredwarriors": "mangangawaynga_armado", "finalfight": "katapusanggu<PERSON>", "poweredgear": "gamitangmaypower", "beatemup": "k<PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "dulagko<PERSON><PERSON>", "killerinstinct": "killerinstinct", "kingoffigthers": "<PERSON><PERSON>mgamanggu<PERSON>bat", "ghostrunner": "multomodagan", "chivalry2": "kabayanihan2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "sunodngahollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "silksongnews", "silksong": "silksong", "undernight": "gabii", "typelumina": "typelum<PERSON>", "evolutiontournament": "ebolysyontorneyo", "evomoment": "evomoment", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "bloodborne", "horizon": "kapunawpunawan", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodbourne", "uncharted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infamous": "notorious", "playstationbuddies": "ka<PERSON>nsakps", "ps1": "ps1", "oddworld": "kalibotankalihokan", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "bahandi", "detroitbecomehuman": "detroitnahimongtalaw", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "touristang_tropheo", "lspdfr": "lspdfr", "shadowofthecolossus": "anggaangsadamgohigante", "crashteamracing": "crashteamracing", "fivepd": "limakapd", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "nagdulastation", "samuraiwarriors": "manggugubatsamurai", "psvr2": "psvr2", "thelastguardian": "angkataposangbantay", "soulblade": "espadadkalag", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "kataposangatigbantay", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "partyingon", "warharmmer40k": "warhammer40k", "fightnightchampion": "gab<PERSON>akampyon", "psychonauts": "psychonauts", "mhw": "mhw", "princeofpersia": "prinsi<PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "gubatan", "dontstarvetogether": "magka<PERSON><PERSON>m<PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "gawasnangbitoon", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "tigbalhinbalay", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligasamgagingharian", "fable2": "sugilanon2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "basurangtv", "skycotl": "skycotl", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterprom", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motorsiklo", "outerwilds": "<PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kultosain<PERSON>", "duckgame": "dulangitik", "thestanleyparable": "thestanleyparable", "towerunite": "towerunite", "occulto": "occulto", "longdrive": "dugaylayogbyahe", "satisfactory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "tigdala_sa_kalag", "darkdome": "ngitngitngakupola", "pizzatower": "pizzatower", "indiegame": "indiegame", "itchio": "itchio", "golfit": "golfit", "truthordare": "tiningodbakunhimo", "game": "dula", "rockpaperscissors": "batopapelyaguntingbato", "trampoline": "trampolin", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "panga<PERSON>", "scavengerhunt": "scavengerhunt", "yardgames": "dulasamnatad", "pickanumber": "piliogakot<PERSON>gi", "trueorfalse": "tinuodobibakakon", "beerpong": "beerpong", "dicegoblin": "dice_goblin", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "dulangpagpangu<PERSON>b", "freegame": "libren<PERSON><PERSON><PERSON>", "drinkinggames": "dulanginom", "sodoku": "sodoku", "juegos": "juegos", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "simu<PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "dulangpulong", "jeuxdemots": "jeuxdemots", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "dula<PERSON>", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "oyun", "interactivegames": "dulagainteraktibo", "amtgard": "amtgard", "staringcontests": "<PERSON><PERSON><PERSON><PERSON>", "spiele": "dula", "giochi": "giochi", "geoguessr": "geoguessr", "iphonegames": "dugangiphoneano", "boogames": "boogames", "cranegame": "cranegame", "hideandseek": "<PERSON><PERSON>see<PERSON>", "hopscotch": "piko", "arcadegames": "dulangarkada", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON>", "classicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mindgames": "<PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "hulaaangly<PERSON>s", "galagames": "duladula", "romancegame": "du<PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "libaklibak", "4xgames": "4xgames", "gamefi": "gamefi", "jeuxdarcades": "jeuxdarcades", "tabletopgames": "tabletopgames", "metroidvania": "metroidvania", "games90": "games90", "idareyou": "pangahasaka", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "<PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "tiunayvsepek", "playgames": "<PERSON><PERSON><PERSON><PERSON>", "gameonline": "dulaon<PERSON>", "onlinegames": "onlinegadula", "jogosonline": "dugaonline", "writtenroleplay": "<PERSON><PERSON><PERSON><PERSON>", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "dulangkooperasyon", "jenga": "jenga", "wiigames": "wiigames", "highscore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "dulangburger", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "juegodepreguntas", "gioco": "gioco", "managementgame": "dulangpagdumala", "hiddenobjectgame": "dulagpangitangbutang", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "dulaf1", "citybuilder": "tigtukogsyudad", "drdriving": "drdriving", "juegosarcade": "dulaarcade", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blowgames": "dulawatayhuyop", "pinballmachines": "mgapinballmachine", "oldgames": "<PERSON><PERSON><PERSON><PERSON>", "couchcoop": "salacoop", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "gameo", "lasergame": "lasergame", "imessagegames": "dugangimessage", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "saktoiangblangko", "jeuxpc": "jeuxpc", "rétrogaming": "rétrogaming", "logicgames": "duganglogika", "japangame": "<PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "duladulangsikat", "exitgames": "dulangpangikyas", "5vs5": "5batok5", "rolgame": "<PERSON><PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "gameugpatay", "traditionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "gamefps", "textbasedgames": "dulangbasadosateksto", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospel", "thiefgame": "dulangkawat", "lawngames": "<PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "larongfutbolsalamisa", "tischfußball": "tischfußball", "spieleabende": "g<PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "dula2x", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "escapegames", "thiefgameseries": "seryenidulangkawatan", "cranegames": "cranegames", "játék": "dula", "bordfodbold": "bordfodbold", "jogosorte": "jogosorte", "mage": "salamangkero", "cargames": "mgadulangawto", "onlineplay": "duloonline", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "pursebingos", "randomizer": "randomizer", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "dunga<PERSON><PERSON>", "socialdeductiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominos": "dominos", "domino": "domino", "isometricgames": "mgadulaisometric", "goodoldgames": "mgamaayngda<PERSON><PERSON><PERSON>", "truthanddare": "kamatudanogpahagit", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "pangitak<PERSON><PERSON>", "jeuxvirtuel": "du<PERSON><PERSON>t<PERSON>", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "libre2dula", "fantasygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "<PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotvseriesugdula", "mushroomoasis": "<PERSON><PERSON><PERSON>", "anythingwithanengine": "bisanunognayengine", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "espadaug<PERSON>lak", "goodgamegiving": "nindotngdulahatag", "jugamos": "j<PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "duwakompyuter", "virgogami": "virgogami", "gogame": "du<PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "gamemodding", "crimegames": "dulangkriminal", "dobbelspellen": "boardgames", "spelletjes": "dulaw<PERSON>law<PERSON>", "spacenerf": "spacenerf", "charades": "charades", "singleplayer": "nagiduwa", "coopgame": "<PERSON><PERSON><PERSON><PERSON>", "gamed": "gidula", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "<PERSON>jud", "kingdiscord": "harid<PERSON>rd", "scrabble": "scrabble", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "dama", "onitama": "onitama", "pandemiclegacy": "pandemyasa<PERSON>in", "camelup": "camelup", "monopolygame": "dulangmonopoly", "brettspiele": "brettspiele", "bordspellen": "<PERSON><PERSON><PERSON><PERSON>", "boardgame": "<PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "dulanglingaw", "planszowe": "boardgame", "risiko": "risiko", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombicide", "tabletop": "tabletop", "baduk": "baduk", "bloodbowl": "bloodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "duwaboardgame", "connectfour": "connectfour", "heroquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidatavolo": "giochidata<PERSON>lo", "farkle": "farkle", "carrom": "carrom", "tablegames": "<PERSON><PERSON><PERSON><PERSON>", "dicegames": "dulangdice", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "dulangpangkatilingban", "deskgames": "deskgames", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "panagkitasakalawakan", "creationludique": "pag<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "dulangdulang", "cardboardgames": "dugayboardgames", "eldritchhorror": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "dulangswitch", "infinitythegame": "infinitythegame", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "habinhugotpanaog", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "planszów<PERSON>", "rednecklife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardom": "kapoy", "applestoapples": "ayo<PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "dulangkatipon", "gameboard": "boardgame", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "mgadulangboard", "twilightimperium": "twilightimperium", "horseopoly": "kabalyopoly", "deckbuilding": "pag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "mgapalasyosabuang", "gomoku": "gomoku", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "mgalandongsakalayo", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcaby": "dama", "táblajátékok": "boardgame", "battleship": "battleship", "tickettoride": "tickettoride", "deskovehry": "deskovehry", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "dulangboardgame", "stolníhry": "<PERSON><PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "jeuxsociete", "gesellschaftsspiele": "gesellschaftsspiele", "starwarslegion": "starwarslegion", "gochess": "du<PERSON>chess", "weiqi": "weiqi", "jeuxdesocietes": "dula", "terraria": "terraria", "dsmp": "dsmp", "warzone": "g<PERSON><PERSON><PERSON>", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identityv", "theisle": "an<PERSON><PERSON>", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "callofcthulhu", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "taliwala", "eco": "eco", "monkeyisland": "islasaunggok", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "angm<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "witchit", "pathologic": "sakit", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "angtaglayomngangitngit", "ark": "ark", "grounded": "nakagrounded", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "amahangbuang", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "balikbalikhangtod", "pathoftitans": "pathoftitans", "frictionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexen": "hexen", "theevilwithin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thebackrooms": "angmgalikodngkwarto", "backrooms": "backrooms", "empiressmp": "empiressmp", "blockstory": "blockstorya", "thequarry": "an<PERSON><PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "nagkamatay", "thewalkingdeadgame": "angdagangpatay", "wehappyfew": "gamayralangkitangmalipayon", "riseofempires": "pag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "stateofsurvivalgame", "vintagestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvival": "arkpagpakabuhi", "barotrauma": "barotrauma", "breathedge": "ginhawaspace", "alisa": "alisa", "westlendsurvival": "pag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastsofbermuda": "mgahayopnibermuda", "frostpunk": "frostpunk", "darkwood": "kangitngit", "survivalhorror": "kalisudkahadlok", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "pag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "dulangpagpaka<PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "angakonggyera", "scpfoundation": "scpfoundation", "greenproject": "proyektoberde", "kuon": "kuon", "cryoffear": "<PERSON>lak<PERSON>ka<PERSON>lok", "raft": "balsa", "rdo": "rdo", "greenhell": "impyernongberde", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "lola", "littlenightmares2": "gamayraygadamgo2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "mgaanaksalasang", "rustvideogame": "rustvideogame", "outlasttrials": "outlasttrials", "alienisolation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "undawn", "7day2die": "7kawlangamatay", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "pagpabiling", "propnight": "g<PERSON><PERSON><PERSON>gasug<PERSON><PERSON>", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "ka<PERSON><PERSON>", "cataclysmdarkdays": "katal<PERSON><PERSON><PERSON><PERSON>law", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "panahonsangitom", "clocktower3": "clocktower3", "aloneinthedark": "nagiisasakangitngit", "medievaldynasty": "dinastiyasamedyebo", "projectnimbusgame": "dulangprojectnimbus", "eternights": "g<PERSON><PERSON><PERSON><PERSON>dsamagpa<PERSON>", "craftopia": "craftopia", "theoutlasttrials": "angoutlasttrials", "bunker": "bunker", "worlddomination": "sakopanangkalibotan", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "tigpamataysao<PERSON><PERSON>", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "tigpamatayugkano", "warhammer40kcrush": "warhammer40kcrush", "wh40": "wh40", "warhammer40klove": "gugmasa40kwarhammer", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "<PERSON><PERSON><PERSON>", "ilovesororitas": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "gihigugmakosivindicare", "iloveassasinorum": "ganahankosassasinorum", "templovenenum": "temporaryongpagmahalnanumero", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "templosabatasan", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "<PERSON><PERSON><PERSON>", "wingspan": "tumbokpako", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "mgabayanisakusugugmahika", "btd6": "btd6", "supremecommander": "pinakataasngakomandante", "ageofmythology": "ageofmythology", "args": "args", "rime": "rime", "planetzoo": "planetazoo", "outpost2": "outpost2", "banished": "gipalagpot", "caesar3": "caesar3", "redalert": "kodop<PERSON>", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "sugoagnakugbihag", "warcraft3": "warcraft3", "eternalwar": "gubatkahangturan", "strategygames": "dugaystrategiya", "anno2070": "anno2070", "civilizationgame": "dulangkacivilization", "civilization4": "civilization4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "gubat<PERSON><PERSON><PERSON>", "travian": "travian", "forts": "<PERSON><PERSON>", "goodcompany": "nindotngkauban", "civ": "civ", "homeworld": "kalibutan", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "labawsakahayag", "forthekings": "<PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "estratehiyas<PERSON>ktwalngpanahon", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eu4": "eu4", "vainglory": "garbo", "ww40k": "ww40k", "godhood": "pag<PERSON><PERSON><PERSON><PERSON>", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "funsaclassnialgebranidave", "plagueinc": "plagueinc", "theorycraft": "<PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "sibilisasyon3", "4inarow": "4sunodsunod", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "gubatngauswag", "ageofempires2": "ageofempires2", "disciples2": "disciples2", "plantsvszombies": "tanimbatokkongkatibal", "giochidistrategia": "giochidistrategia", "stratejioyunları": "stratejioyunları", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "haridugabuang", "worldconquest": "<PERSON>gs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "heartsofiron4", "companyofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "batobatopik", "phobies": "kamahádlokan", "phobiesgame": "phobiesgame", "gamingclashroyale": "dula<PERSON>lanwar<PERSON>roy<PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "turnbased", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "crusaderkings", "cultris2": "cultris2", "spellcraft": "salamangka", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estrate<PERSON>ya", "popfulmail": "popfulmail", "shiningforce": "kusogningpwersa", "masterduel": "masterduel", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "transportsyudonglalawigan", "unrailed": "walay_riles", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "kaginharannasabukid", "galaxylife": "<PERSON><PERSON><PERSON>inggalaks<PERSON>", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "patayangtore", "battlecats": "batugatohanongiring", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "needforspeedcarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "molungtad", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "darkhorseanthology", "phasmophobia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lo<PERSON>", "fivenightsatfreddys": "limanggabsangfreddys", "saiko": "saiko", "fatalframe": "ka<PERSON>lokannghulagway", "littlenightmares": "gagmayngabangungot", "deadrising": "deadrising", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "nagpabiling", "deadisland": "pataynga_isla", "litlemissfortune": "litlemissmalas", "projectzero": "proyektosero", "horory": "horory", "jogosterror": "duwangkalisang", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "hihimsayud2", "gamingdbd": "gamingdbd", "thecatlady": "thecatlady", "jeuxhorreur": "j<PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "dulangkataw", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "cardsbatokkatawo", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "codenames", "dixit": "dixit", "bicyclecards": "barahangbisikleta", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legendofrun<PERSON>a", "solitaire": "<PERSON>o", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "baraha", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "tradingcards", "pokemoncards": "pokemoncards", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "mgakole<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "️", "warcry": "singgit", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON>sing<PERSON><PERSON>", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "angpagsukol", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "<PERSON>lam<PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "blueeyeswhitedragon", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "dugangbarahacarddeck", "burraco": "bur<PERSON>o", "rummy": "rami", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "juegosdecar<PERSON>", "duelyst": "duelista", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "battlespirits", "battlespiritssaga": "sagasaespiritunggubatnon", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON>", "žolíky": "mga_joker", "facecard": "dagway", "cardfight": "<PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "tig<PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "marvelchampions", "magiccartas": "magiccartas", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "diliestablesagaunicorns", "cyberse": "<PERSON><PERSON>i", "classicarcadegames": "klasikongdulangarkada", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "fridaynightgiatay", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "proyektomirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "guitarhero", "clonehero": "clonehero", "justdance": "sayaw<PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "pasundayogangpata<PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "sayawsentral", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "highscorerhythmgames", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "rhythmheaven", "hypmic": "hypmic", "adanceoffireandice": "sayawsakalayougice", "auditiononline": "audisyononlayn", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "doktorsaritmo", "cubing": "cubing", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "dulangpuzzle", "spotit": "<PERSON><PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "mgapuzzlesapanglogika", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "brainteaser", "rubikscube": "rubikscube", "crossword": "kros<PERSON>", "motscroisés": "motscroisés", "krzyżówki": "kros<PERSON>", "nonogram": "nonogram", "bookworm": "batanglibrohon", "jigsawpuzzles": "jigsawpuzzles", "indovinello": "tigmo", "riddle": "tigmo", "riddles": "mga_tigmo", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "sulod", "angrybirds": "kasukongmgalanggam", "escapesimulator": "eskeypsimuleytor", "minesweeper": "minesweeper", "puzzleanddragons": "puzzleuggidragons", "crosswordpuzzles": "crosswordpuzzles", "kurushi": "k<PERSON>hi", "gardenscapesgame": "<PERSON><PERSON><PERSON><PERSON>", "puzzlesport": "puzzlesport", "escaperoomgames": "dulangtulagsulod", "escapegame": "dulanglikay", "3dpuzzle": "3dpuzzle", "homescapesgame": "dulangbalay", "wordsearch": "pangatapangapulong", "enigmistica": "enigmistica", "kulaworld": "kulaworld", "myst": "misteryo", "riddletales": "tigmo", "fishdom": "fishdom", "theimpossiblequiz": "angimposiblengakwis", "candycrush": "candycrush", "littlebigplanet": "gamaysadunaydako", "match3puzzle": "match3puzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikcube": "rubikcube", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "angtalosprinciple", "homescapes": "panimalay", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "tuk<PERSON><PERSON><PERSON><PERSON><PERSON>", "tycoongames": "dulangtycoón", "cubosderubik": "cubosderubik", "cruciverba": "cruciver<PERSON>", "ciphers": "mga_cipher", "rätselwörter": "mgapulongsapuzzle", "buscaminas": "buscaminas", "puzzlesolving": "pagsolbarpuzzle", "turnipboy": "turnipboy", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "walay<PERSON>o", "guessing": "pag<PERSON><PERSON>", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "misteryosongakrosword", "syberia2": "syberia2", "puzzlehunt": "pagpangitasapuzzle", "puzzlehunts": "puzzlehunts", "catcrime": "krimensairing", "quebracabeça": "quebracabeça", "hlavolamy": "mga_puzzle", "poptropica": "poptropica", "thelastcampfire": "angkataposangsiga", "autodefinidos": "autodefinidos", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON>", "carto": "karto", "untitledgoosegame": "waytimuloangbaktin", "cassetête": "<PERSON><PERSON>an", "limbo": "limbo", "rubiks": "rubiks", "maze": "labirintus", "tinykin": "g<PERSON><PERSON><PERSON><PERSON>", "rubikovakostka": "rubikcube", "speedcube": "speedcube", "pieces": "mgap<PERSON><PERSON>", "portalgame": "dulangportal", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "twistedwonderland", "monopoly": "monopolyo", "futurefight": "panggubatugma", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "na<PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemystars", "stateofsurvival": "estadongpagkaluwas", "mycity": "<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "entabladonghayag", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperatubangan", "knightrun": "knightrun", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "dulangcellphone", "kingschoice": "<PERSON><PERSON><PERSON><PERSON>", "guardiantales": "guardiantales", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "taktikal", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "craftsman": "panday", "supersus": "superdudoso", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "bedwars", "freefire": "freefire", "mobilegaming": "dulagsamobile", "lilysgarden": "hardinnilily", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "emergencyhq", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "yukayukaogkaligligo", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "timeprincess", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "gugmang<PERSON><PERSON>", "androidgames": "dulangangand<PERSON>", "criminalcase": "kasokriminal", "summonerswar": "summonerswar", "cookingmadness": "kalitisapangluto", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "leaguenimgamgaanghel", "lordsmobile": "lordsmobile", "tinybirdgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "akongmgakantahonmonstro", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "warrobots", "mirrorverse": "mirrorverse", "pou": "pou", "warwings": "pakpakgubat", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "l<PERSON><PERSON><PERSON><PERSON>", "antiyoy": "antiikaw", "apexlegendmobile": "apexlegendmobile", "ingress": "pag<PERSON><PERSON>", "slugitout": "pakig<PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "gameofsultans", "arenabreakout": "arenabreakout", "wolfy": "lobo", "runcitygame": "dugancitygame", "juegodemovil": "juegodemovil", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "mimicry", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ldoe": "ldoe", "legendonline": "legendsaonline", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "callofdragons", "shiningnikki": "<PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "<PERSON><PERSON><PERSON><PERSON><PERSON>lak<PERSON>", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "pulonanugkauban2", "soulknight": "soulknight", "purrfecttale": "purrfectngsugilanon", "showbyrock": "showbyrock", "ladypopular": "sikatngababaye", "lolmobile": "lolmobile", "harvesttown": "harvesttown", "perfectworldmobile": "perfectworldmobile", "empiresandpuzzles": "mgaimperyougmgapuzzle", "empirespuzzles": "empirespuzzles", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileindia", "fanny": "lubot", "littlenightmare": "gamaynanightmare", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiecastaways", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "nanaylutlut", "cabalmobile": "cabalmobile", "streetfighterduel": "streetfighterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "girlsfrontline", "jurassicworldalive": "jurassicworldalive", "soulseeker": "nangitaskalulag", "gettingoverit": "pagla<PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "moonchaistory", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "duwamobile", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "dugaymobilelegends", "timeraiders": "timeraiders", "gamingmobile": "dulasamobile", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "angm<PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "pangita", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgsalamesa", "worldofdarkness": "kalibotanngkangitngit", "travellerttrpg": "biyaherottrpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "klubsagugma", "d20": "d20", "pokemongames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "t<PERSON><PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "pocketmonsters", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "teamystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "<PERSON>ab<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "mgabataugpokemon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "manging<PERSON>g<PERSON><PERSON>", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "b<PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "ka<PERSON><PERSON>anb<PERSON>", "jeudéchecs": "jeudéchecs", "japanesechess": "shogijapan", "chinesechess": "<PERSON>ian<PERSON><PERSON>", "chesscanada": "chesscanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "p<PERSON><PERSON><PERSON>", "rook": "ruk", "chesscom": "chesscom", "calabozosydragones": "dungeonugdragon", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "dungeonmaster", "tiamat": "tiamat", "donjonsetdragons": "donjonugdragons", "oxventure": "oxventure", "darksun": "ngitngitngaadlaw", "thelegendofvoxmachina": "angleyendanivoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "ngitngitngakapatagan", "minecraftchampionship": "minecraftchampionship", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "sigang_kandila", "fru": "fru", "addons": "mg<PERSON><PERSON><PERSON>", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "moddedsaminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "talibutang", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "syudadminecraft", "pcgamer": "pcgamer", "jeuxvideo": "dulangkompyuter", "gambit": "gambit", "gamers": "dula", "levelup": "levelup", "gamermobile": "gamerasacellphone", "gameover": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "gg": "gg", "pcgaming": "<PERSON><PERSON><PERSON><PERSON>", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "duwangsapc", "casualgaming": "<PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "gamingsetup", "pcmasterrace": "pcmasterrace", "pcgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerboy": "gamerboyko", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "gameplay", "consoleplayer": "konsolplayer", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgamers", "onlinegaming": "<PERSON><PERSON><PERSON><PERSON>", "semigamer": "semigamer", "gamergirls": "gamergirls", "gamermoms": "gamermomsceb", "gamerguy": "gamerboyko", "gamewatcher": "tig<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "b<PERSON><PERSON><PERSON><PERSON>", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "teamgrabe", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "mga_quest", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON>", "cozygaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamelpay": "dulangbayad", "juegosdepc": "juegosdepc", "dsswitch": "dsswitch", "competitivegaming": "dulagkompetisyon", "minecraftnewjersey": "minecraftnewjersey", "faker": "peke", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heterosexualgaming", "gamepc": "gamepc", "girlsgamer": "b<PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamegirl": "gamergirl", "chicasgamer": "b<PERSON><PERSON><PERSON><PERSON>", "gamesetup": "gamesetup", "overpowered": "sobrakaligon", "socialgamer": "socialgamer", "gamejam": "gamejam", "proplayer": "proplayer", "roleplayer": "roleplay", "myteam": "akongteam", "republicofgamers": "republik<PERSON><PERSON><PERSON><PERSON>", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "<PERSON><PERSON><PERSON><PERSON>", "gamerbuddies": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "christiangamer", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "dilikaayogamer", "89squad": "89squad", "inicaramainnyagimana": "unsu<PERSON><PERSON><PERSON><PERSON>", "insec": "insekto", "gemers": "<PERSON><PERSON><PERSON>", "oyunizlemek": "oyunizlemek", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "gamerna", "wspólnegranie": "wspólnegranie", "mortdog": "mortdog", "playstationgamer": "duwaplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "tigdulagnotebok", "protogen": "protogen", "womangamer": "babayenggamer", "obviouslyimagamer": "syemparogamerko", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "mangolekta", "humanfallflat": "tawhongnahulogpataag", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zerowalaykaikyas", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusic", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "fallguys", "switch": "switch", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "skychildrenofthelight", "tomodachilife": "kinabuhinigbarkada", "ahatintime": "usarayugto", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "mg<PERSON><PERSON><PERSON>lakaw", "nintendogames": "mgadulasanintendo", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "b<PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "akonghigalapedro", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "<PERSON><PERSON><PERSON><PERSON>", "earthbound": "yuta", "tales": "mgaistorya", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "estratehiyatriyang<PERSON>lo", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "mario<PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "pulang<PERSON>", "vanillalol": "vanillalol", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendspilipinas", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligasalegenda", "gaminglol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "dulangfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrobidjogeyms", "scaryvideogames": "makahadlokngamgavideogames", "videogamemaker": "magbubuhatvidyogeyms", "megamanzero": "megamanzero", "videogame": "dulangkompyuter", "videosgame": "videogame", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "a<PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "puffpals", "farmingsimulator": "simulatordepagpanguma", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxalemania", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "<PERSON><PERSON><PERSON><PERSON>", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amordoce": "amordoce", "videogiochi": "videogiochi", "theoldrepublic": "angkaraangrepublika", "videospiele": "dulangkompyuter", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "dulangpangsugilon", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON>ks<PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "estory<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "retroarkeyds", "vintagecomputing": "vintagecomputing", "retrogaming": "retrogaming", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "<PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "injustisya2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "dulangkalangitan", "zenlife": "kinahangbu<PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "pangit", "mystgames": "dulangmisteryoso", "blockchaingaming": "blockchaingaming", "medievil": "medievil", "consolegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolen", "outrun": "ipamusot", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "gamingkalisang", "monstergirlquest": "monstergirlquest", "supergiant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "mg<PERSON><PERSON>ansapag<PERSON>", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "interaktibongpiksiyon", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantesamentes", "visualnovel": "visualnovel", "visualnovels": "mgavisualnovel", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON>", "tcrghost": "tcrmulto", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "prinsesasakilumkilom", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "mgadulangaesthetics", "novelavisual": "nobel<PERSON><PERSON><PERSON>", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "dulangkaraang", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "rebol<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "leveldesign", "starrail": "starrail", "keyblade": "keyblade", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafusahay", "novelasvisuales": "nobel<PERSON><PERSON><PERSON>", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "<PERSON><PERSON><PERSON><PERSON>", "videojuejos": "videojuegos", "videogamedates": "videogamedates", "mycandylove": "akonghinigugma", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "giustolang3", "hulkgames": "dulangkulgames", "batmangames": "<PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "adlawsagalamay", "maniacmansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashracing": "<PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "3dplatformers", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "oldschoolgaming", "hellblade": "espadadimpyerno", "storygames": "dugaystorya", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "lapasnag<PERSON><PERSON><PERSON>", "gameuse": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "offmortisghost", "tinybunny": "gamayrabbit", "retroarch": "retroarch", "powerup": "powerup", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "aventurasgraficas", "quickflash": "dali<PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retrogaditan", "f123": "f123", "wasteland": "kamingawan", "powerwashsim": "powerwashsim", "coralisland": "coralisland", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "laingkalibutan", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "footballfusion", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "liko<PERSON>o", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "tamboknguwaw", "simulator": "simulator", "symulatory": "simulador", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "akongkahadlok", "wonderlandonline": "wonderlandonlayn", "skylander": "skylander", "boyfrienddungeon": "u<PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "toontowngibalikpagsulat", "simracing": "<PERSON><PERSON><PERSON><PERSON>", "simrace": "<PERSON><PERSON><PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "langitnongalawas", "seum": "seum", "partyvideogames": "dulangvideogames", "graveyardkeeper": "magbalantaysakamenteryo", "spaceflightsimulator": "simulatorsakalawakan", "legacyofkain": "pamana_ni_kain", "hackandslash": "tuslokugpamutol", "foodandvideogames": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "oyunvideoları", "thewolfamongus": "anglobonatoatunga", "truckingsimulator": "truckingsimulator", "horizonworlds": "horizonworlds", "handygame": "<PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "legendsugvideogames", "oldschoolvideogames": "oldschoolvideogames", "racingsimulator": "simulatordagpangurera", "beemov": "bee<PERSON>v", "agentsofmayhem": "mgaahentenggubotgubot<PERSON>ayo", "songpop": "kantapop", "famitsu": "famitsu", "gatesofolympus": "ganghaaansakaolympus", "monsterhunternow": "monsterhunternow", "rebelstar": "rebelstar", "indievideogaming": "indievideogaming", "indiegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogames": "indievideogames", "indievideogame": "indievideogame", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermandimakatulog", "bufffortress": "kusgangkota", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "proyekto", "futureclubgames": "dulagangclubsaumaabot", "mugman": "mugman", "insomniacgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "backlog", "gamebacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "person<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "mangitangkalampusan", "cityskylines": "syudadngalangit", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "pilyo", "beastlord": "panginoonngamgahayop", "juegosretro": "j<PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "hatagngangkalipay", "staxel": "staxel", "videogameost": "videogameost", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkerthanblack": "itumkaayosaitom", "animescaling": "animescaling", "animewithplot": "animengmaydrama", "pesci": "pesci", "retroanime": "retroanime", "animes": "animeanime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseason1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "animecover", "thevisionofescaflowne": "thevisionofescaflowne", "slayers": "mgapataypatay", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "saging<PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "kawanangan", "fireforce": "sunog", "moriartythepatriot": "moriartyangpatri<PERSON>", "futurediary": "umaabotngadiary", "fairytail": "eng<PERSON><PERSON>ongsugilanon", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "gih<PERSON><PERSON><PERSON>lo<PERSON>", "parasyte": "parasayt", "punpun": "kulba", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "mgalabaykoreanong", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "dragonmaid", "blacklagoon": "blacklagoon", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "henyo", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "acertainmagicalindex", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8hangtudsamadayun", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "mgaanghelnokamatayon", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animesasports", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sagaoftanyatheevil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "un<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "fullmoongatugnaw", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "kinatumyangmartialarts", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "hiscoregirl", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "dagan", "oldanime": "<PERSON><PERSON>me", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "dula", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "fruitsbasket", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "g<PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "angisaadngadiligyudmahitabo", "monstermanga": "monstermanga", "yourlieinapril": "imongbakaksaabril", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "bilanggosadagatlawom", "jojolion": "jojo<PERSON>", "deadmanwonderland": "deadmanwonderland", "bannafish": "gid<PERSON><PERSON><PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON>", "husbu": "hasbu", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "giyerahangsapagkaon", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "<PERSON><PERSON><PERSON><PERSON><PERSON>nay", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "blueperiod", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "gipapas", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampireknight", "mugi": "mugi", "blueexorcist": "blueexorcist", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "gihilak", "spyfamily": "spyfamily", "airgear": "airgear", "magicalgirl": "b<PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "pitonggrabenggasala", "prisonschool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "angdiyosasahighschool", "kissxsis": "hag<PERSON><PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "akongsininavestida", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeyuniberso", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "dimamataydimaswerte", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animengromansa", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animepilipinas", "lolicon": "lo<PERSON>on", "demonslayertothesword": "angt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "suntoksikala<PERSON>", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "nagtakdosagbitoon", "romanceanime": "romansanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saofactorintegralnako", "cherrymagic": "cherrymagic", "housekinokuni": "baynik<PERSON><PERSON><PERSON>", "recordragnarok": "recordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "highschoolsalugara", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "assassinclassroom", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "pataypar<PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animenghapon", "animespace": "animespace", "girlsundpanzer": "girlsundpanzer", "akb0048": "akb0048", "hopeanuoli": "paglaumniadlaw", "animedub": "animedubbing", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "tawonglamrag", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "dalangapeach", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "kasingkasingsamanga", "deliciousindungeon": "la<PERSON>gs<PERSON>ungeon", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "recordofragnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "boc<PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "laktawngosat<PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "<PERSON>rak<PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "witchhatatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangaislife", "dropsofgod": "tululog<PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "harembaligbaliktad", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "dakongmagtutudwosionizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "akoongbossdaddy", "gear5": "gear5", "grandbluedreaming": "dako_nga_asul_nga_damgo", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "dugoanime", "bloodc": "bloodc", "talesofdemonsandgods": "mgasugidlanonsamgade<PERSON>s", "goreanime": "goreanime", "animegirls": "animegabaye", "sharingan": "<PERSON><PERSON>", "crowsxworst": "crowsxpinakan<PERSON><PERSON>", "splatteranime": "splatteranime", "splatter": "katag", "risingoftheshieldhero": "pagtungaw<PERSON>osa<PERSON><PERSON>", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimengkuhaonnabalay", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "bakakonnabakak", "supercampeones": "supercampeones", "animeidols": "animeidols", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "mgamagicalgirls", "callofthenight": "tawags<PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "pangitagmanga", "princessjellyfish": "prin<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "halikngparaiso", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeverse", "persocoms": "persocoms", "omniscientreadersview": "omniscientreadersview", "animecat": "animengming", "animerecommendations": "animengarekomendasyon", "openinganime": "<PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "akongteenromanticcomedy", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "higant<PERSON>mgar<PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "<PERSON><PERSON><PERSON>", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "military<PERSON><PERSON>", "greenranger": "greenranger", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinang3rd", "animecity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonslayer", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "surveycorps", "onepieceanime": "onepieceanime", "attaquedestitans": "attaquedestitans", "theonepieceisreal": "theonepieceisrealjud", "revengers": "<PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "epektonijoyboy", "digimonstory": "story<PERSON><PERSON><PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "superpreso", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "perfectongwebt<PERSON>", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "bisaglang", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "recuentosdelavida"}