{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "matariki", "cognitivefunctions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychology": "<PERSON><PERSON><PERSON><PERSON>", "philosophy": "whak<PERSON><PERSON>", "history": "<PERSON><PERSON><PERSON><PERSON>", "physics": "ahurei", "science": "<PERSON><PERSON><PERSON><PERSON>", "culture": "ahurea", "languages": "reo", "technology": "teknolohia", "memes": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mbtimemes": "mbtimemes", "astrologymemes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enneagrammemes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showerthoughts": "whakaar<PERSON>_kaukau", "funny": "katakata", "videos": "whakaata", "gadgets": "tap<PERSON><PERSON>", "politics": "t<PERSON><PERSON><PERSON><PERSON>", "relationshipadvice": "whakaaroaawangaitaanga", "lifeadvice": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "mon<PERSON><PERSON>ng<PERSON>", "news": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "worldnews": "panuitaiao", "archaeology": "kōpurapura", "learning": "whakaako", "debates": "<PERSON><PERSON><PERSON>", "conspiracytheories": "ngāwhakarangirua", "universe": "<PERSON><PERSON><PERSON><PERSON>", "meditation": "whakangā", "mythology": "p<PERSON><PERSON><PERSON>", "art": "<PERSON><PERSON><PERSON>", "crafts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dance": "kanikani", "design": "ho<PERSON><PERSON>", "makeup": "kanohi", "beauty": "<PERSON><PERSON><PERSON><PERSON>", "fashion": "k<PERSON><PERSON><PERSON>", "singing": "<PERSON><PERSON>ta", "writing": "tuhituhi", "photography": "whakaa<PERSON>", "cosplay": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting": "peita", "drawing": "w<PERSON><PERSON><PERSON><PERSON>", "books": "pu<PERSON><PERSON>ka", "movies": "kiriata", "poetry": "w<PERSON><PERSON><PERSON><PERSON>", "television": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filmmaking": "whakaa<PERSON><PERSON>", "animation": "hākoritanga", "anime": "anime", "scifi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasy": "moemoeā", "documentaries": "pūrongo", "mystery": "muna", "comedy": "whakakatakata", "crime": "hara", "drama": "whak<PERSON>i", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "w<PERSON><PERSON><PERSON><PERSON>", "romance": "w<PERSON><PERSON><PERSON>", "realitytv": "poukahotereiti", "action": "mahi", "music": "<PERSON><PERSON>ta", "blues": "<PERSON><PERSON><PERSON>", "classical": "puoroōnamata", "country": "whenua", "desi": "tehi", "edm": "edm", "electronic": "hiko", "folk": "<PERSON><PERSON><PERSON><PERSON>", "funk": "funky", "hiphop": "hiphop", "house": "whare", "indie": "indie", "jazz": "tiahe", "kpop": "kpop", "latin": "whakapākehā", "metal": "w<PERSON><PERSON><PERSON>", "pop": "pop", "punk": "pān<PERSON><PERSON>", "rnb": "rnb", "rap": "r<PERSON><PERSON>", "reggae": "reke", "rock": "toka", "techno": "tekeno", "travel": "ha<PERSON><PERSON><PERSON>", "concerts": "konohete", "festivals": "<PERSON><PERSON><PERSON><PERSON>", "museums": "ngāw<PERSON>tongat<PERSON>", "standup": "tū<PERSON><PERSON><PERSON>", "theater": "wharetapere", "outdoors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gardening": "m<PERSON>ra", "partying": "kanikani", "gaming": "kēmu", "boardgames": "pet<PERSON><PERSON>e", "dungeonsanddragons": "dungeonsanddragons", "chess": "whaturanga", "fortnite": "fortnite", "leagueoflegends": "riikitoa", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "kai", "baking": "tunu", "cooking": "kai", "vegetarian": "kaimangakore", "vegan": "kaihuawhenua", "birds": "manu", "cats": "ngā_pōtiki", "dogs": "ngā<PERSON><PERSON><PERSON>", "fish": "ika", "animals": "ka<PERSON><PERSON>e", "blacklivesmatter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "environmentalism": "taiao", "feminism": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "humanrights": "ngātikangatatangata", "lgbtqally": "takatapa<PERSON><PERSON><PERSON><PERSON>", "stopasianhate": "kātiaitetekonongaāhia", "transally": "tuakiritakatāpui", "volunteering": "t<PERSON><PERSON>", "sports": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "badminton": "p<PERSON><PERSON><PERSON><PERSON>", "baseball": "<PERSON><PERSON><PERSON><PERSON>", "basketball": "p<PERSON><PERSON><PERSON><PERSON>", "boxing": "mekemeke", "cricket": "kiri<PERSON><PERSON>", "cycling": "p<PERSON><PERSON>", "fitness": "whak<PERSON><PERSON>", "football": "whu<PERSON><PERSON>o", "golf": "korowha", "gym": "whare<PERSON>kari", "gymnastics": "p<PERSON><PERSON>", "hockey": "hō<PERSON><PERSON>", "martialarts": "ngākakangamauārā<PERSON>u", "netball": "poitūko<PERSON>", "pilates": "pilates", "pingpong": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "running": "oma", "skateboarding": "<PERSON><PERSON><PERSON><PERSON>", "skiing": "reti<PERSON><PERSON>", "snowboarding": "reti<PERSON><PERSON>", "surfing": "<PERSON><PERSON><PERSON>", "swimming": "<PERSON><PERSON><PERSON>", "tennis": "tē<PERSON><PERSON>", "volleyball": "p<PERSON><PERSON>", "weightlifting": "pakari", "yoga": "ioka", "scubadiving": "ka<PERSON><PERSON><PERSON>", "hiking": "h<PERSON><PERSON><PERSON>", "capricorn": "<PERSON><PERSON><PERSON><PERSON>", "aquarius": "a<PERSON><PERSON><PERSON>", "pisces": "paika", "aries": "a<PERSON>i", "taurus": "te_raiona", "gemini": "gemini", "cancer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leo": "leo", "virgo": "virgo", "libra": "<PERSON><PERSON><PERSON><PERSON>", "scorpio": "<PERSON><PERSON><PERSON>", "sagittarius": "te_kōpere", "shortterm": "<PERSON><PERSON><PERSON>", "casual": "māmā", "longtermrelationship": "w<PERSON><PERSON><PERSON>ang<PERSON><PERSON>", "single": "taka<PERSON>u", "polyamory": "maha_a<PERSON>ha", "enm": "enaman<PERSON><PERSON>", "lgbt": "takatāpu<PERSON>", "lgbtq": "takatāpuiwhānui", "gay": "takatāpu<PERSON>", "lesbian": "takataapuiwahine", "bisexual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pansexual": "taera", "asexual": "karekautanga", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "wairua", "suikoden": "su<PERSON><PERSON>", "subverse": "a<PERSON><PERSON><PERSON>", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "haipēria", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON>angarar<PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "<PERSON><PERSON><PERSON><PERSON>", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "ānga<PERSON><PERSON><PERSON>", "dungeoncrawling": "<PERSON>rata<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "iwiota<PERSON><PERSON>", "planescape": "<PERSON>ua<PERSON><PERSON><PERSON><PERSON>", "lordsoftherealm2": "ngārangatiratangaotewhenua2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "kaika<PERSON><PERSON>e", "medabots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lodsoftherealm2": "ngārangatiratangaotewhenua2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "karikoriwhakaurutanga", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON>uring<PERSON>", "witcher": "witcher", "dishonored": "whak<PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "paru", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "kōmurumuru", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "whak<PERSON><PERSON><PERSON>", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "r<PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "poauauāmotewhaiaipo", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "teputorewamatatau", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "ngāwhenuakapuni", "pathfinder": "ka<PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "omahae<PERSON>", "bloodontheclocktower": "tetotongatepuroko", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "rui<PERSON>a", "rpg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "kotahiko<PERSON>ĩ", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "rangatirama<PERSON><PERSON>", "yourturntodie": "tokoutakarotanga", "persona3": "persona3", "rpghorror": "rpgwhakamataku", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "<PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtuhi", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "whare<PERSON>a", "gurps": "gurps", "darkestdungeon": "ruakōpiripiri", "eclipsephase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "aotūmatawhāiti", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloimmortal": "diabloimmortal", "dynastywarriors": "pakangawhakaheke", "skullgirls": "kaupapatūpāpaku", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "kōreromōhogwarts", "madnesscombat": "paka<PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "road96", "vtmb": "vtmb", "chimeraland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "kē<PERSON>ate", "gothamknights": "gothamknights", "forgottenrealms": "takiwāngaro", "dragonlance": "dragonlance", "arenaofvalor": "ta<PERSON><PERSON><PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childoflight": "ta<PERSON>oh<PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "kai<PERSON><PERSON><PERSON><PERSON>aniwha", "ecopunk": "ecorūkahu", "vermintide2": "māramaramākutu2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "tepaetawhititerehia", "twewy": "twewy", "shadowpunk": "ah<PERSON><PERSON><PERSON>", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "poke<PERSON><PERSON><PERSON><PERSON>", "deltagreen": "kākārikiwhiti", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "<PERSON><PERSON><PERSON><PERSON>", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "whe<PERSON><PERSON><PERSON>i", "goldensun": "<PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "ahiahirua2000", "sandevistan": "sandevistan", "cyberpunk": "haipāpango", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkwhero", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "kauakataka", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON>", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "kaikoniwhakaoramate", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "atua", "pf2": "pf2", "farmrpg": "pākefarmrpg", "oldworldblues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "kēmutakiwehenga", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "ngākōreroosymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfarog": "tokufāwhitu", "sacredunderworld": "tea<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainedechoes": "ngākōrerowhakahono", "darksoul": "wairuakino", "soulslikes": "whai<PERSON><PERSON><PERSON>", "othercide": "whakamatetangata", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pillarsofeternity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "wehenga", "tibia": "tibia", "thedivision": "tewehenga", "hellocharlotte": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "pakiwaitarataniwha", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "kirikauwaihōkai", "aveyond": "aveyond", "littlewood": "tewhareiti", "childrenofmorta": "ngātamarikiamorta", "engineheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable3": "fable3", "fablethelostchapter": "fab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "<PERSON><PERSON><PERSON>ew<PERSON>", "rollenspiel": "<PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "kētiapōtua", "edeneternal": "edenmutungakore", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "whet<PERSON><PERSON>", "oldschoolrevival": "whakahou<PERSON>eaomatawhito", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "tangapourirangatira", "juegosrpg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "iwimalkavian", "harvestella": "koh<PERSON><PERSON>a", "gloomhaven": "pouritanga", "wildhearts": "ng<PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "pakari", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "rangiarcadia", "shadowhearts": "<PERSON>ā<PERSON><PERSON>ān<PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "totoiti", "breathoffire4": "hāngamamura4", "mother3": "māmā3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON>den<PERSON><PERSON>", "roleplaygames": "kēmutakiatu", "roleplaygame": "k<PERSON><PERSON><PERSON>kata<PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "ngatemanatauwhir<PERSON><PERSON>", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "tarakona", "spelljammer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "whakawhitikōwā", "cocttrpg": "cocttrpg", "huntroyale": "whakataetaeariki", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "kēmu<PERSON>rikiripatuterā", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "whak<PERSON><PERSON><PERSON>āngapūrāka<PERSON>", "shadowheartscovenant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "matatokitoki", "baldursgate3": "baldursgate3", "kingdomcome": "terangatirangamaikitemutunga", "awplanet": "aoteplanetawa", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "<PERSON><PERSON><PERSON><PERSON>", "dyinglight2": "matemate2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "heretikatiwha", "shoptitans": "<PERSON><PERSON><PERSON>ari<PERSON>", "forumrpg": "whakataetaekē<PERSON>", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackbook": "pukapango", "skychildrenoflight": "tamar<PERSON>rangiotera<PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "castlecrashers": "pakangapātūtūwhare", "gothicgame": "kēmukōtika", "scarletnexus": "teheretewhero", "ghostwiretokyo": "poroporowhaitokyo", "fallout2d20": "fallout2d20", "gamingrpg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prophunt": "k<PERSON>muparup<PERSON>", "starrails": "ngaararerehetū", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "kēmu<PERSON>uhitakihokohoko", "pointandclick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "kei<PERSON><PERSON><PERSON><PERSON>", "indivisible": "kotahitanga", "freeside": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisisake", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "imuriinanatanga", "deathroadtocanada": "<PERSON>ua<PERSON><PERSON><PERSON><PERSON><PERSON>", "palladium": "paret<PERSON><PERSON>a", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "kaikimikararehe", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "persona5": "persona5", "ghostoftsushima": "wairuatsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "<PERSON><PERSON><PERSON>", "ys": "ys", "souleater": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "kēmukorekoretānetahine", "tacticalrpg": "rpgmahiranga", "mahoyo": "mahoyo", "animegames": "kē<PERSON><PERSON><PERSON>ē", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "tangikorekore", "princessconnect": "k<PERSON><PERSON><PERSON><PERSON>", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON>apākor<PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valo<PERSON><PERSON><PERSON><PERSON>", "dota": "dota", "madden": "<PERSON><PERSON><PERSON>", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlg": "mlg", "leagueofdreamers": "rō<PERSON><PERSON>moemoea", "fifa14": "fifa14", "midlaner": "kaikangawaenga", "efootball": "whu<PERSON><PERSON><PERSON><PERSON>", "dreamhack": "moe<PERSON><PERSON><PERSON><PERSON>", "gaimin": "kēmu", "overwatchleague": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cybersport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crazyraccoon": "rakūnakūware", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "whak<PERSON><PERSON>", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "whakataetaevalorant", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON>", "left4dead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead2": "kua1mahue4mate2", "valve": "valve", "portal": "kuaha", "teamfortress2": "teamfortress2", "everlastingsummer": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "aotearoakatorohea", "transformice": "transformice", "justshapesandbeats": "ngāauahangākōrikorimeana", "battlefield4": "battlefield4", "nightinthewoods": "poiteirotongahere", "halflife2": "halflife2", "hacknslash": "pakuakapatu", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "uawhaiterangi2", "metroidvanias": "ngā<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overcooked": "kuaparahia", "interplanetary": "t<PERSON><PERSON><PERSON><PERSON>", "helltaker": "<PERSON><PERSON><PERSON><PERSON>", "inscryption": "inscryption", "7d2d": "7rā2rā", "deadcells": "pū<PERSON>ate", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "rua_pungarehu", "stray": "k<PERSON><PERSON>", "battlefield": "pakanga", "battlefield1": "pākan<PERSON>w<PERSON>uatuata<PERSON>", "swtor": "swtor", "fallout2": "fallout2", "uboat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eyeb": "kanohi", "blackdesert": "korumangu", "tabletopsimulator": "<PERSON><PERSON><PERSON>papamaheni", "partyhard": "kanik<PERSON><PERSON><PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "moka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinkum": "tū<PERSON><PERSON>", "predecessor": "tua<PERSON><PERSON><PERSON>", "rainworld": "a<PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "atapuowhawhai", "minionmasters": "minionmasters", "grimdawn": "<PERSON><PERSON><PERSON><PERSON>", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "pureihaupikowhetu", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "taonerangi", "defconheavy": "<PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsia", "virtualkenopsia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowrunner": "ka<PERSON><PERSON>_huka", "libraryofruina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "ngākē<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "ka<PERSON><PERSON>", "kenabridgeofspirits": "kena<PERSON><PERSON><PERSON><PERSON><PERSON>", "placidplasticduck": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlebit": "pakangakoka", "ultimatechickenhorse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dialtown": "tauw<PERSON><PERSON><PERSON><PERSON>o", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermeatboy", "tinnybunny": "riikipepeketūroro", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "mate<PERSON><PERSON>", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "roh<PERSON><PERSON>henga", "pubg": "pubg", "callofdutyzombies": "callofdutyzombiekēhua", "apex": "tauma<PERSON>", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "kēmufarcrygames", "paladins": "paladins", "earthdefenseforce": "<PERSON><PERSON><PERSON>whenua", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "whit<PERSON><PERSON><PERSON><PERSON>", "grandtheftauto5": "kēmukaratahangamotokā5", "warz": "pakanga", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "joinsquad": "<PERSON><PERSON><PERSON>ō<PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxpayne": "maxpayne", "hitman3": "kaikohuru3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "<PERSON><PERSON><PERSON><PERSON>", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "kēmukirimate", "mirrorsedge": "matakanoaangawhiti", "divisions2": "kaikēmuwehenga2", "killzone": "w<PERSON><PERSON><PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "zombiekongawhaw<PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosscode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare": "paka<PERSON><PERSON><PERSON><PERSON>", "neonabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planetside2": "planetside2", "mechwarrior": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boarderlands": "rohetaone", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofwarships": "aoteatauwakapakanga", "back4blood": "hokimotetoto", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "kaipatu", "masseffect": "masseffect", "systemshock": "ohorere", "valkyriachronicles": "valkyriachronicles", "specopstheline": "tohun<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killingfloor2": "papaturuawhitu2", "cavestory": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doometernal": "<PERSON><PERSON><PERSON><PERSON>", "centuryageofashes": "rautaukapongakohu", "farcry4": "farcry4", "gearsofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mwo": "mwo", "division2": "tuwehanga2", "tythetasmaniantiger": "tytetigeratamania", "generationzero": "tewhakareangakore", "enterthegungeon": "tomokaitepūkaea", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "pakangahouora2", "blackops1": "blackops1", "sausageman": "k<PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "<PERSON><PERSON><PERSON><PERSON>", "crossfire": "kur<PERSON><PERSON>", "atomicheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "kaikoniheketūpāpaku", "callofdutybatleroyale": "callofdutybattleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlegrounds": "papa<PERSON><PERSON><PERSON>", "frag": "patu", "tinytina": "itititina", "gamepubg": "kemup<PERSON><PERSON>", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "keemfps", "convertstrike": "taka<PERSON><PERSON><PERSON>", "warzone2": "warzonge2", "shatterline": "mat<PERSON><PERSON><PERSON>", "blackopszombies": "kirikiriapopozombie", "bloodymess": "<PERSON><PERSON><PERSON><PERSON>", "republiccommando": "ka<PERSON>whakah<PERSON><PERSON><PERSON><PERSON>", "elitedangerous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soldat": "ka<PERSON>ri", "groundbranch": "pekangawhenua", "squad": "r<PERSON><PERSON><PERSON>", "destiny1": "whakatau1", "gamingfps": "k<PERSON><PERSON><PERSON><PERSON>", "redfall": "ta<PERSON><PERSON><PERSON>", "pubggirl": "k<PERSON><PERSON>ropubg", "worldoftanksblitz": "aoteamatatang<PERSON>", "callofdutyblackops": "callofdutyblackops", "enlisted": "kuawhakau<PERSON>", "farlight": "pōmā<PERSON>ā", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "mekameka", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "papatupapati<PERSON>", "halo2": "halo2", "payday2": "utuāmoni2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostcod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "csplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unrealtournament": "k<PERSON><PERSON>karek<PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "kēmucodm", "borderlands2": "borderlands2", "counterstrike": "ka<PERSON><PERSON><PERSON><PERSON>", "cs2": "cs2", "pistolwhip": "whiup<PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "tōkangakēmupāpupuha", "halo3": "halo3", "halo": "kia<PERSON>", "killingfloor": "papamatemate", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "kiramamā", "remnant": "<PERSON>nga", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "hokinga", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "tan<PERSON><PERSON><PERSON><PERSON>", "quake2": "rū2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "matemate", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "pakehawhawhai3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "temoanaot<PERSON>", "rust": "tuku<PERSON><PERSON>", "conqueronline": "wikingaipurangi", "dauntless": "<PERSON>awhā<PERSON>", "warships": "ngākaipuke", "dayofdragons": "ratūtaniwha", "warthunder": "warthunder", "flightrising": "pakeketanga", "recroom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofruneterra": "ngāp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ra", "pso2": "pso2", "myster": "muna", "phantasystaronline2": "phantasystaronline2", "maidenless": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "whak<PERSON><PERSON>", "agario": "agario", "secondlife": "te<PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "pureiipurang<PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "kinikihiangakararehe", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "kororīamokopuna", "lotro": "lotro", "wakfu": "wakfu", "scum": "kūware", "newworld": "aotearangahou", "blackdesertonline": "blackdesertonline", "multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pirate101": "kōrere101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karmaland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ssbu": "ssbu", "starwarsbattlefront2": "pakangaiomuastarwars2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dchat": "kōrerotuatoru", "nostale": "ka<PERSON><PERSON><PERSON><PERSON>", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "silkroad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "spiralknights", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "matakiterangitaniwha", "grymmo": "grymmo", "warmane": "mahana", "multijugador": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelsonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "whenuataw<PERSON><PERSON><PERSON><PERSON><PERSON>", "grandfantasia": "tinoataa<PERSON>", "blueprotocol": "kawa<PERSON>upapaka<PERSON><PERSON>i", "perfectworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseonline": "whakatūāipurangi", "corepunk": "corepunk", "adventurequestworlds": "pākiwharawhararaotaiao", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "kēmuaninimalz", "kingdomofloathing": "terangatirangaotereretanga", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "streetfighter": "kaitauahuarahi", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "motemana", "tekken": "tekken", "guiltygear": "kē<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mkwhakamatenga", "nomoreheroes": "korehengatoaanō", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "tekingiongakaitawhaiwhai", "likeadragon": "heitaniwha", "retrofightinggames": "k<PERSON>muwhawhaatawhito", "blasphemous": "whakahāwea", "rivalsofaether": "hoarikitētawhirimatea", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mugen": "mugen", "warofthemonsters": "pakarangaotengātaniwha", "jogosdeluta": "pakanga<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>hai", "cyberbots": "<PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "whaw<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "poweredgear": "tap<PERSON><PERSON>", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "moumoeatanga", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "kaiomangeo", "chivalry2": "chivalry2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightwhakatukinei", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "<PERSON><PERSON>rerehuarang<PERSON>", "silksonggame": "k<PERSON><PERSON><PERSON><PERSON>nga", "silksongnews": "rongosilksong", "silksong": "silksong", "undernight": "p<PERSON><PERSON><PERSON>", "typelumina": "<PERSON><PERSON><PERSON><PERSON>", "evolutiontournament": "whakataetaepukenga", "evomoment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lollipopchainsaw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "kōreroberseria", "bloodborne": "tototaurua", "horizon": "<PERSON><PERSON><PERSON><PERSON>", "pathofexile": "pathofexile", "slimerancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashbandicoot": "crashbandicoot", "bloodbourne": "tototukutuku", "uncharted": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "whak<PERSON><PERSON><PERSON>a", "infamous": "r<PERSON><PERSON><PERSON><PERSON>o", "playstationbuddies": "hoaplaystation", "ps1": "ps1", "oddworld": "aohite<PERSON>ē", "playstation5": "pleisiteihana5", "slycooper": "kūparaka<PERSON>no", "psp": "psp", "rabbids": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splitgate": "kua<PERSON><PERSON>", "persona4": "persona4", "hellletloose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "ka<PERSON><PERSON><PERSON>ino", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "atuapakanga", "gris": "kēhua", "trove": "koh<PERSON>", "detroitbecomehuman": "detroittangataai", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "ta<PERSON><PERSON><PERSON>", "touristtrophy": "taongamanuh<PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "<PERSON><PERSON>ier<PERSON><PERSON>ī<PERSON><PERSON>", "fivepd": "rimapauna", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "devilmaycry3", "devilmaycry5": "tepaatupoumakawhio5", "ufc4": "ufc4", "playingstation": "taka<PERSON>i", "samuraiwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "tekaitiakiwhakamutunga", "soulblade": "mat<PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "ngākauatarangi2kawenata", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "kaha", "cd": "cd", "gamepass": "pukapukakēmu", "armello": "armello", "partyanimal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "taikangapowhiritoa", "psychonauts": "ngā<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhw": "mhw", "princeofpersia": "pirinahioperehia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "paka<PERSON><PERSON><PERSON>", "dontstarvetogether": "kauakaematekaihokimaikatoa", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "whetuhere", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "reta3", "houseflipper": "whakapaipaiwhare", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable2": "pakiwaitara2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skycotl": "skycotl", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "kotukunoaitua", "sallyface": "kanosally", "franbow": "franbow", "monsterprom": "<PERSON><PERSON><PERSON><PERSON>", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motopaika", "outerwilds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "karakiatereme", "duckgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "tekōrerotīparaoa", "towerunite": "whe<PERSON><PERSON><PERSON><PERSON>", "occulto": "huna", "longdrive": "hauroa", "satisfactory": "<PERSON>ga", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "rarohenua", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "kuruporuru", "pizzatower": "<PERSON>ō<PERSON><PERSON><PERSON>", "indiegame": "k<PERSON><PERSON><PERSON>", "itchio": "itchio", "golfit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "kēmu", "rockpaperscissors": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "tarapekepeke", "hulahoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dare": "kiam<PERSON>ia", "scavengerhunt": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegoblin": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "kēmekore", "drinkinggames": "k<PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "kēmu", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "kēmukeariwhakaari", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "oyun", "interactivegames": "kēmuhokohoko", "amtgard": "amtgard", "staringcontests": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "kēmu", "giochi": "kēmu", "geoguessr": "geoguessr", "iphonegames": "kēmu<PERSON><PERSON><PERSON>", "boogames": "k<PERSON><PERSON><PERSON>", "cranegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hideandseek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hopscotch": "p<PERSON><PERSON><PERSON><PERSON>", "arcadegames": "k<PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "kēmuyakuza", "classicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindgames": "whakap<PERSON><PERSON>etanga", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "k<PERSON><PERSON><PERSON>", "romancegame": "k<PERSON>mukaipō", "yanderegames": "kēmukaiwhakamataku", "tonguetwisters": "k<PERSON><PERSON><PERSON><PERSON>w<PERSON>a", "4xgames": "kēmuwhā", "gamefi": "k<PERSON><PERSON><PERSON><PERSON>", "jeuxdarcades": "kem<PERSON><PERSON><PERSON>", "tabletopgames": "k<PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "kēmu90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "pūkauavspūkeka", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "kēmuipurangi", "onlinegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosonline": "k<PERSON><PERSON>kaitoro", "writtenroleplay": "tuhingawhakatanga<PERSON>", "playaballgame": "<PERSON><PERSON><PERSON><PERSON>", "pictionary": "piki<PERSON><PERSON>", "coopgames": "k<PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "k<PERSON><PERSON><PERSON>i", "highscore": "tairewa", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "kēmupākā", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "pokiwhiti", "nfsmwblackedition": "nfsmwputipango", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "patapatai", "gioco": "gioco", "managementgame": "kēmuwhakahaere", "hiddenobjectgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roolipelit": "kēmuwhakatangitangi", "formula1game": "kēmuhuwakapaheketahi", "citybuilder": "kaituhukarar<PERSON>", "drdriving": "taraiwakataraiwa", "juegosarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldgames": "k<PERSON>muotawhito", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "patapatai", "gameo": "kēmu", "lasergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "kēmuwhakawhitiwhiti", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "whak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxpc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rétrogaming": "<PERSON><PERSON><PERSON>atawhit<PERSON>", "logicgames": "k<PERSON><PERSON>ra", "japangame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "whak<PERSON>ā<PERSON><PERSON><PERSON><PERSON>", "subwaysurf": "pekekeneterereina", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "k<PERSON><PERSON>putaa<PERSON>", "5vs5": "5ki5", "rolgame": "k<PERSON><PERSON>aran<PERSON>", "dashiegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "ng<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "kē<PERSON><PERSON>e", "textbasedgames": "k<PERSON><PERSON><PERSON><PERSON>uhi", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "whu<PERSON><PERSON><PERSON><PERSON>", "retrospel": "<PERSON><PERSON><PERSON><PERSON>", "thiefgame": "k<PERSON><PERSON>kaihere", "lawngames": "k<PERSON><PERSON>ngārae", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "poikīnikatēpu", "tischfußball": "<PERSON><PERSON><PERSON><PERSON>", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "kē<PERSON>ā<PERSON>ā", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "k<PERSON><PERSON><PERSON>a", "thiefgameseries": "kēmukaiaetanga", "cranegames": "k<PERSON><PERSON><PERSON>owhi<PERSON>", "játék": "kēmu", "bordfodbold": "poikōpere", "jogosorte": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mage": "mage", "cargames": "kēmumotukā", "onlineplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "randomizer": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "socialdeductiongames": "kēmutukutāpori", "dominos": "t<PERSON><PERSON><PERSON>", "domino": "t<PERSON><PERSON><PERSON>", "isometricgames": "kēmutaurite", "goodoldgames": "kēmurangatira", "truthanddare": "pākiwahapatapatai", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "kim<PERSON><PERSON>", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "ka<PERSON><PERSON><PERSON><PERSON>_utu<PERSON>e", "free2play": "k<PERSON><PERSON><PERSON>u", "fantasygame": "k<PERSON>muporokaki", "gryonline": "g<PERSON><PERSON><PERSON><PERSON>", "driftgame": "kēmunekeneke", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mushroomoasis": "ha<PERSON>raumāniania", "anythingwithanengine": "ngameakateputaputa", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodgamegiving": "kēmupaihēngia", "jugamos": "takaro", "lab8games": "lab8games", "labzerogames": "kēmuakorewhareputaiao", "grykomputerowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "k<PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "whakapaihēmukēmu", "crimegames": "<PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "kēmupetipeti", "spelletjes": "k<PERSON><PERSON><PERSON>", "spacenerf": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "whak<PERSON>i", "singleplayer": "taki<PERSON>i", "coopgame": "k<PERSON><PERSON><PERSON>i", "gamed": "k<PERSON><PERSON><PERSON><PERSON>", "forzahorizon": "forzahorizon", "nexus": "hononga", "geforcenow": "geforcenow", "maingame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scrabble": "poka<PERSON><PERSON>", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "pākē<PERSON>", "onitama": "onitama", "pandemiclegacy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "camelup": "ka<PERSON><PERSON><PERSON>", "monopolygame": "kēmumonopoly", "brettspiele": "k<PERSON><PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "k<PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planszowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "risiko": "tū<PERSON><PERSON>", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "kirimete", "tabletop": "papa<PERSON>i", "baduk": "p<PERSON><PERSON>e", "bloodbowl": "porototo", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectfour": "honongawhā", "heroquest": "pakanga<PERSON><PERSON><PERSON>", "giochidatavolo": "k<PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "k<PERSON><PERSON>", "tablegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "kēmutāwhiti", "yatzy": "yatzy", "parchis": "p<PERSON><PERSON>hi", "jogodetabuleiro": "k<PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "kēmupāpori", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "tū<PERSON><PERSON><PERSON><PERSON><PERSON>", "creationludique": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "takamauapurei", "cardboardgames": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "kaikino", "switchboardgames": "<PERSON><PERSON><PERSON><PERSON>ariwhakawhiti", "infinitythegame": "k<PERSON><PERSON>utunga", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "<PERSON>ket<PERSON><PERSON><PERSON>", "társas": "taki<PERSON>i", "juegodemesa": "k<PERSON><PERSON><PERSON><PERSON>", "planszówki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "mototūāoneone", "boardom": "<PERSON><PERSON><PERSON>", "applestoapples": "āporokiāporo", "jeudesociété": "takaro", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "t<PERSON><PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON>", "twilightimperium": "terangatiahiahi", "horseopoly": "hoihoonopoly", "deckbuilding": "<PERSON>ah<PERSON>", "mansionsofmadness": "wharengunengune", "gomoku": "gomoku", "giochidatavola": "k<PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "atarangaoprimstone", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcaby": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "táblajátékok": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "waka_pakanga", "tickettoride": "ekepanapana", "deskovehry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "k<PERSON><PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "jeuxsociete", "gesellschaftsspiele": "kēmupāpori", "starwarslegion": "starwarslegion", "gochess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "pakanga", "arksurvivalevolved": "arksurvivalevolved", "dayz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "identityv": "identityv", "theisle": "temotu", "thelastofus": "tewaahineowhakamutunga", "nomanssky": "tekahiparamatatangata", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON>aunga", "eco": "taiao", "monkeyisland": "moutuma<PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "kaihangaaorangi", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "w<PERSON><PERSON><PERSON><PERSON>", "witchit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathologic": "<PERSON><PERSON><PERSON><PERSON>", "zomboid": "kiri<PERSON><PERSON>", "northgard": "northgard", "7dtd": "7rtm", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "ake", "grounded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "matengakuware2", "vrising": "vrising", "madfather": "matua_ka<PERSON><PERSON><PERSON>ri", "dontstarve": "kauakaitekai", "eternalreturn": "hokingamutunga", "pathoftitans": "a<PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexen": "<PERSON><PERSON><PERSON>", "theevilwithin": "keirotokino", "realrac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thebackrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backrooms": "ngārumatuarongo", "empiressmp": "empiressmp", "blockstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thequarry": "<PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "kēmuwhakatangatangamate", "wehappyfew": "temataitioahoa", "riseofempires": "tepuawatan<PERSON>", "stateofsurvivalgame": "<PERSON><PERSON><PERSON><PERSON>gamau<PERSON><PERSON>", "vintagestory": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "breathedge": "whakangāngā", "alisa": "alisa", "westlendsurvival": "<PERSON>w<PERSON><PERSON>moor<PERSON>", "beastsofbermuda": "ngākararehebermuda", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "<PERSON><PERSON><PERSON><PERSON>", "residentevil": "mat<PERSON>nga<PERSON><PERSON>", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "re<PERSON><PERSON>e", "lifeaftergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "tē<PERSON>ipakangarāwaaku", "scpfoundation": "scpfoundation", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "tangitangiwhakamataku", "raft": "raft", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "<PERSON><PERSON><PERSON><PERSON>", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "koroua", "littlenightmares2": "pōmakunukuliti2", "signalis": "tohuiranga", "amandatheadventurer": "amandangakaikō<PERSON>ko<PERSON><PERSON>", "sonsoftheforest": "ngātamatamaraotengutukunuku", "rustvideogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "atapō", "7day2die": "7rangiwhakamatemate", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "oranga", "propnight": "poprowera", "deadisland2": "matepurangamu2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampaia", "deathverse": "aomatenga", "cataclysmdarkdays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soma": "soma", "fearandhunger": "matakuahiakai", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "a<PERSON><PERSON><PERSON>", "ageofdarkness": "wa<PERSON><PERSON><PERSON>", "clocktower3": "takapurangawaatoru", "aloneinthedark": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "kēmuprojectnimbus", "eternights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "craftopia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theoutlasttrials": "ngawhakamataungaoutlast", "bunker": "<PERSON><PERSON><PERSON>", "worlddomination": "aotūroatahitanga", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "teariwhakamatemate", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "kaipatu", "warhammer40kcrush": "warhammer40kwhaiāipo", "wh40": "wh40", "warhammer40klove": "arohawarhammer40k", "warhammer40klore": "kōreroaparangawhariwharitokotoru", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kpōuri", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "<PERSON><PERSON><PERSON>uid<PERSON>awa<PERSON>", "ilovevindicare": "keitefiaroaukindicare", "iloveassasinorum": "keiteneinikitenekaikohuru", "templovenenum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "p<PERSON><PERSON>o", "terraformingmars": "w<PERSON><PERSON><PERSON><PERSON>", "heroesofmightandmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "btd6": "btd6", "supremecommander": "rangati<PERSON>uiarawa", "ageofmythology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "args": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planetzoo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outpost2": "outpost2", "banished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "ohorerehea", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "tohungawhakahau", "warcraft3": "warcraft3", "eternalwar": "p<PERSON><PERSON><PERSON><PERSON>ga", "strategygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "kēmekōiwi", "civilization4": "civilization4", "factorio": "factorio", "dungeondraft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spore": "pua", "totalwar": "paka<PERSON><PERSON>", "travian": "travian", "forts": "ngāpā", "goodcompany": "heiwhakahoahoa", "civ": "civ", "homeworld": "k<PERSON><PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "teretereakeatuiteraiti", "forthekings": "moangakingi", "realtimestrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "rangatiratangaeruaraukarauna", "eu4": "eu4", "vainglory": "whakah<PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "atuatanga", "anno": "tau", "battletech": "pūrorotekniko", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "davesfunalgebraclass": "tekarahupāngarauataakongaadave", "plagueinc": "mateumareketekete", "theorycraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "ahurea3", "4inarow": "4iteturanga", "crusaderkings3": "crusaderkings3", "heroes3": "ngātoa3", "advancewars": "paka<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "ageofempires2", "disciples2": "whakapono2", "plantsvszombies": "tiputipuvskahupokeroke", "giochidistrategia": "k<PERSON><PERSON><PERSON><PERSON>", "stratejioyunları": "k<PERSON><PERSON><PERSON>kan<PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "kiingamokopuna", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "ngākōmanamārō4", "companyofheroes": "teropuotoa", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammerkillteam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goosegooseduck": "kere<PERSON><PERSON><PERSON>", "phobies": "mataku", "phobiesgame": "kēmutakutaku", "gamingclashroyale": "kēmukakangakingi", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "w<PERSON><PERSON><PERSON><PERSON>", "turnbased": "takaturumāmā", "bomberman": "bomberman", "ageofempires4": "tewaongaemepaea4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "pakangawhakangārotorangahōkura", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "<PERSON><PERSON><PERSON>", "popfulmail": "m<PERSON>rama<PERSON>k<PERSON>", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "transporttycoon": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unrailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uplandkingdoms": "ngārangatiratangaotirikitanga", "galaxylife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "kerematangaaipurei", "slaythespire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "t<PERSON><PERSON>kē<PERSON>", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "k<PERSON><PERSON><PERSON><PERSON>e", "needforspeed": "matenuitētere", "needforspeedcarbon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realracing3": "petipetimotuka3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "mate<PERSON>āramatan<PERSON>", "alicemadnessreturns": "alicemad<PERSON>ret<PERSON>s", "darkhorseanthology": "kōihoarārangi", "phasmophobia": "w<PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "porimarama<PERSON><PERSON><PERSON><PERSON><PERSON>", "saiko": "<PERSON><PERSON><PERSON>", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "mate<PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadisland": "motumate", "litlemissfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzero": "kaihangarore", "horory": "w<PERSON><PERSON><PERSON><PERSON>", "jogosterror": "kē<PERSON>ataku", "helloneighbor": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "kiaoratehoamata2", "gamingdbd": "kēmudbd", "thecatlady": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "k<PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "kē<PERSON>ataku", "magicthegathering": "tepatohit<PERSON><PERSON>ngaatua", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kāriwhakatenetekaiwhakangongo", "cribbage": "ka<PERSON><PERSON>", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "<PERSON><PERSON><PERSON>", "codenames": "ing<PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "ka<PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "i<PERSON><PERSON>", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "kirimahara", "poker": "pōkā", "hearthstone": "ngākohatu", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "kiitorino", "cardtricks": "mahi_kāri", "playingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ram<PERSON><PERSON><PERSON>a", "netrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kaariwhitiwhi", "pokemoncards": "k<PERSON><PERSON>okemona", "fleshandbloodtcg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sportscards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "whaw<PERSON><PERSON><PERSON><PERSON>", "duellinks": "w<PERSON><PERSON><PERSON><PERSON><PERSON>nga", "spades": "p<PERSON><PERSON>", "warcry": "pakanga", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "kiingateomanawa", "truco": "truco", "loteria": "rōteria", "hanafuda": "hana<PERSON>da", "theresistance": "tepakatanga", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "whak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "mat<PERSON><PERSON><PERSON>", "yugiohgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "tohunga_makutu", "blueeyeswhitedragon": "matakahurangiwhitotarakona", "yugiohgoat": "yugiohkerehauora", "briscas": "paraikete", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "<PERSON><PERSON><PERSON><PERSON>", "rummy": "rami", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "<PERSON><PERSON><PERSON><PERSON>", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "whak<PERSON><PERSON><PERSON>", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carteado": "kāriwhakatoi", "sueca": "hueka", "beloteonline": "petipakauamāmā", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "žolíky": "ngā_kāri_kaitoa", "facecard": "kano<PERSON><PERSON><PERSON>", "cardfight": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "piripa", "deckbuilders": "ngākaihang<PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magiccartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "iōkitoiōkore", "cyberse": "ipurangi", "classicarcadegames": "k<PERSON>mumaraetawhito", "osu": "osu", "gitadora": "gitadora", "dancegames": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "porangihaupo", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "kaupapamīrai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "<PERSON><PERSON><PERSON><PERSON>", "justdance": "kanik<PERSON><PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "whakapuakitengamate", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "kanik<PERSON><PERSON><PERSON>", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "pkxd": "pkxd", "sidem": "taha", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptotarateimamate", "rhythmdoctor": "taktak<PERSON>", "cubing": "mataono", "wordle": "wordle", "teniz": "tenei", "puzzlegames": "k<PERSON><PERSON><PERSON><PERSON>", "spotit": "kitea", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "logicpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "ngāpākekeingāroro", "rubikscube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossword": "kup<PERSON><PERSON>", "motscroisés": "kup<PERSON><PERSON><PERSON>i", "krzyżówki": "panga_kupu", "nonogram": "nonogram", "bookworm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinello": "panga", "riddle": "panga", "riddles": "panga", "rompecabezas": "panga", "tekateki": "tekateki", "inside": "waho", "angrybirds": "man<PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "pakiwhakaahuawairua", "minesweeper": "<PERSON><PERSON>ahupa<PERSON><PERSON><PERSON><PERSON>", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "pan<PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ngarak<PERSON>", "puzzlesport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapegame": "k<PERSON><PERSON><PERSON>a", "3dpuzzle": "taangakiritatutoru", "homescapesgame": "kēmupākaingā", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "pūpa<PERSON>patanga", "kulaworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myst": "<PERSON><PERSON><PERSON><PERSON>", "riddletales": "k<PERSON>rerota<PERSON>", "fishdom": "<PERSON><PERSON><PERSON>", "theimpossiblequiz": "tepatapataioteekorekorekataea", "candycrush": "candycrush", "littlebigplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "match3puzzle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON>wer<PERSON>", "rubikcube": "rubikcube", "cuborubik": "kiupapoporokiroki", "yapboz": "yap<PERSON>", "thetalosprinciple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homescapes": "kāingatirot<PERSON>", "puttputt": "<PERSON><PERSON><PERSON>", "qbert": "qbert", "riddleme": "whakamāramatia", "tycoongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "cubosderubik", "cruciverba": "patapatai", "ciphers": "ngātūāhuatanga", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "whakaotinga_panga", "turnipboy": "ta<PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "<PERSON><PERSON><PERSON><PERSON>", "guessing": "<PERSON><PERSON><PERSON>", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "kuputakakorekore", "syberia2": "syberia2", "puzzlehunt": "kimikimirapu", "puzzlehunts": "rapupapatūtū", "catcrime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "panga", "hlavolamy": "whir<PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "teahitewhak<PERSON>utunga", "autodefinidos": "autodefinidos", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "kemukukakoreingoa", "cassetête": "casset<PERSON>te", "limbo": "t<PERSON><PERSON><PERSON>", "rubiks": "rubiks", "maze": "rau<PERSON><PERSON>", "tinykin": "potunga", "rubikovakostka": "rubikovakostka", "speedcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pieces": "ngāwāhanga", "portalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "kōrero_muna", "cubomagico": "rubikscube", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monopoly": "k<PERSON><PERSON><PERSON>", "futurefight": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "ka<PERSON>hi", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "<PERSON><PERSON><PERSON><PERSON>", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "wh<PERSON><PERSON><PERSON><PERSON><PERSON>", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvival": "teaoraanga", "mycity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "ataahuamatarae", "bloonstowerdefense": "parekuratūporow<PERSON><PERSON><PERSON><PERSON>", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "<PERSON><PERSON>mu<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "tekowhirikingitanga", "guardiantales": "k<PERSON>rerotūpua", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "r<PERSON><PERSON><PERSON>o", "cookierun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ao", "craftsman": "<PERSON><PERSON><PERSON>", "supersus": "whakatupatotupato", "slowdrive": "taraiātā", "headsup": "kiatūpa<PERSON>", "wordfeud": "<PERSON>up<PERSON><PERSON>", "bedwars": "paka<PERSON><PERSON><PERSON>", "freefire": "ma<PERSON><PERSON>", "mobilegaming": "k<PERSON>mukorerorero", "lilysgarden": "ma<PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bgmi": "bgmi", "teamfighttactics": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clashofclans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pjsekai": "pjsekai", "mysticmessenger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymobile": "callofdutymobilewhaea", "thearcana": "<PERSON><PERSON><PERSON>a", "8ballpool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emergencyhq": "whaiw<PERSON><PERSON><PERSON><PERSON>", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "arap<PERSON>", "hayday": "he<PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "rūrūmekō<PERSON>", "ml": "ml", "bangdream": "bangdream", "clashofclan": "pakangaiwi", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "<PERSON><PERSON><PERSON><PERSON>", "beatstar": "whet<PERSON><PERSON><PERSON>", "dragonmanialegend": "taniwhamanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "arohapoket", "androidgames": "k<PERSON><PERSON><PERSON><PERSON>", "criminalcase": "kēhikōtae", "summonerswar": "summonerswar", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "patapatai", "leagueofangels": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "kapuapōkōrangi", "mysingingmonsters": "my<PERSON>ingmons<PERSON>", "nekoatsume": "ngeru", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "k<PERSON>rerotūtur<PERSON>", "futime": "<PERSON><PERSON><PERSON><PERSON>", "antiyoy": "whakahētanga", "apexlegendmobile": "apexlegendmobile", "ingress": "kuh<PERSON>i", "slugitout": "whak<PERSON><PERSON>hetohe", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "pēwheatangakirikēkorekore", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "kēmuosultans", "arenabreakout": "teputahipurei", "wolfy": "wuruhi", "runcitygame": "omaket<PERSON><PERSON>", "juegodemovil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "kirimanaponikeke", "grandchase": "grandchase", "bombmebrasil": "w<PERSON><PERSON><PERSON>_ahau_parahi", "ldoe": "ldoe", "legendonline": "ki<PERSON><PERSON>to<PERSON>", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "karangaotangar<PERSON>", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "whakatereketemokonei2", "pathtonowhere": "arahekoreanga", "sealm": "sealm", "shadowfight3": "whawhaiatawhenua3", "limbuscompany": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "demolitionderby3": "tatakirangawaka3", "wordswithfriends2": "kūpumoahoa2", "soulknight": "<PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "ngātākōrerorawe", "showbyrock": "whak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladypopular": "wahinemōhiotia", "lolmobile": "katakatakorere", "harvesttown": "<PERSON><PERSON><PERSON>", "perfectworldmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiresandpuzzles": "emperatangameraupapa", "empirespuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlegroundmobileind": "pakangamōpukengaīnia", "fanny": "kumu", "littlenightmare": "pouririkikiiti", "aethergazer": "aethergazer", "mudrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "roimataotehemiti", "eversoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gunbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmlbb": "kemumlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombienohoatu", "eveechoes": "eveechoes", "jogocelular": "kēmuroriwaea", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "ma<PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "k<PERSON><PERSON>bg<PERSON>", "girlsfrontline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jurassicworldalive": "teaokanoaorauriwhenua", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "katekapohia", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "moonchaistory", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "kēmupūkoro", "legendofneverland": "pūrākauneverland", "pubglite": "pubglite", "gamemobilelegends": "k<PERSON><PERSON><PERSON><PERSON>rerorongonui", "timeraiders": "kairaideriwā", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON>ukor<PERSON>", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "ngapoakakehe", "dnd": "dnd", "quest": "kuera", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgpapa", "worldofdarkness": "teaotūhae", "travellerttrpg": "travellerttrpg", "2300ad": "2300ad", "larp": "whakatangatawhaikōrero", "romanceclub": "ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "d20": "d20", "pokemongames": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonwhero", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "<PERSON><PERSON><PERSON><PERSON>", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "whak<PERSON>e", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonwhero", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "taiao", "teamrocket": "kaparōketi", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "taka<PERSON>up<PERSON><PERSON>", "teamystic": "<PERSON><PERSON><PERSON><PERSON><PERSON>ramatanga", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "k<PERSON><PERSON><PERSON><PERSON>", "shinypokemon": "poke<PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>e", "heyyoupikachu": "kia<PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "matanga<PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "tamarikimepoukemone", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "parapara", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "kai<PERSON><PERSON><PERSON><PERSON>", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "kōtiromate", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "a<PERSON><PERSON><PERSON>", "jeudéchecs": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "japanesechess": "kēmuōiwapani", "chinesechess": "<PERSON>ian<PERSON><PERSON>", "chesscanada": "poiripātacan<PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "whakatuw<PERSON>atanga", "rook": "ruke", "chesscom": "chesscom", "calabozosydragones": "ringaringaatangaru", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "kaiwhakahaeredungeons", "tiamat": "tiamat", "donjonsetdragons": "taiharataniwha", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "tepurakauovoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "repopouri", "minecraftchampionship": "w<PERSON><PERSON><PERSON>engamainek<PERSON>raw<PERSON>", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftkaitāpiri", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON>", "fru": "kaikore", "addons": "t<PERSON><PERSON><PERSON><PERSON>", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpūtea", "minecraft360": "minecraft360", "moddedminecraft": "minecraftwhakarerekē", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "<PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "paminecraftcity", "pcgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvideo": "k<PERSON><PERSON><PERSON><PERSON>", "gambit": "<PERSON><PERSON><PERSON><PERSON>", "gamers": "kēm<PERSON>", "levelup": "whak<PERSON><PERSON><PERSON>", "gamermobile": "k<PERSON><PERSON><PERSON><PERSON>reroit<PERSON>", "gameover": "kaumutu", "gg": "gg", "pcgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamen": "kēmu", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casualgaming": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "tū<PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vrgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drdisrespect": "drdisrespect", "4kgaming": "kēmuriwhā", "gamerbr": "kiniki<PERSON><PERSON><PERSON>", "gameplays": "pureikēmu", "consoleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "semigamer": "pokikē<PERSON><PERSON>o", "gamergirls": "kōtiroreimakēmu", "gamermoms": "māmākē<PERSON><PERSON><PERSON><PERSON>i", "gamerguy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamewatcher": "ka<PERSON><PERSON>i", "gameur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otoge": "otoge", "dedsafio": "<PERSON><PERSON><PERSON>", "teamtryhard": "kapa_pukumahi", "mallugaming": "k<PERSON><PERSON><PERSON>u", "pawgers": "ngāpōkīataahua", "quests": "ngā_wero", "alax": "alax", "avgn": "avgn", "oldgamer": "k<PERSON><PERSON><PERSON><PERSON>āw<PERSON><PERSON>", "cozygaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamelpay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsswitch": "dsswitch", "competitivegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftnewjersey": "minecraftnūtīhi", "faker": "tinihanga", "pc4gamers": "kirikēmumōngākaitākaro", "gamingff": "kee<PERSON>i", "yatoro": "yatoro", "heterosexualgaming": "takakuratānetāne", "gamepc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsgamer": "kotirokai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "kotirokeemu", "gamesetup": "whakaritengakēmu", "overpowered": "kaikaha", "socialgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "proplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayer": "<PERSON><PERSON><PERSON><PERSON>", "myteam": "<PERSON><PERSON><PERSON><PERSON>", "republicofgamers": "repū<PERSON>rika<PERSON><PERSON><PERSON>", "aorus": "aorus", "cougargaming": "kui<PERSON><PERSON><PERSON>", "triplelegend": "tohuaruatoa", "gamerbuddies": "ho<PERSON><PERSON><PERSON>", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "k<PERSON>makaim<PERSON><PERSON><PERSON>", "nerdgamer": "kuwarepōkēmu", "afk": "kknm", "andregamer": "andregamer", "casualgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "89kapa", "inicaramainnyagimana": "peheatekatakirama", "insec": "pōh<PERSON>hē", "gemers": "<PERSON><PERSON><PERSON>", "oyunizlemek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamertag": "ingoapawe", "lanparty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "pukapukapūrei", "protogen": "protogen", "womangamer": "wahinekēmā", "obviouslyimagamer": "hetinotangakēmtangata", "mario": "mario", "papermario": "papermario", "mariogolf": "karif<PERSON>korowa<PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "kairapu", "humanfallflat": "tan<PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendo64": "nintendo64", "zeroescape": "zerotorere", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "puoronintendo", "sonicthehedgehog": "sonicthehedgehog", "sonic": "honika", "fallguys": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "whak<PERSON><PERSON><PERSON>", "zelda": "zelda", "smashbros": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofzelda": "tekor<PERSON>ozeld<PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "tamar<PERSON>rangiotera<PERSON>", "tomodachilife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ahatintime": "kia<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "ngāroimataotekīngitanga", "walkingsimulators": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogames": "k<PERSON><PERSON><PERSON><PERSON>o", "thelegendofzelda": "tekōreroamatahuanaozelda", "dragonquest": "dragonquest", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "tokuhoapedro", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51kēmu", "earthbound": "papat<PERSON><PERSON><PERSON>", "tales": "k<PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermariomaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendos": "nintendos", "new3ds": "3dshou", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartyngawhetu", "marioandsonic": "marioandsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "kua_wehea", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "urgot": "urgot", "zyra": "zyra", "redcanids": "kur<PERSON><PERSON><PERSON>o", "vanillalol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wildriftph": "wildriftph", "lolph": "katam<PERSON><PERSON><PERSON><PERSON>", "leagueoflegend": "leagueoflegend", "tốcchiến": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "panuitaunaki", "lolzinho": "<PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendspāniora", "aatrox": "aatrox", "euw": "ākene", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "<PERSON><PERSON><PERSON><PERSON><PERSON>ren<PERSON>", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ngātohuingoa", "gaminglol": "kēmukatakata", "nasus": "nasus", "teemo": "teemo", "zedmain": "z<PERSON>mat<PERSON>", "hexgates": "kuahamatawhe", "hextech": "hextech", "fortnitegame": "kē<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingfortnite": "kēmufortnite", "fortnitebr": "fortnitebr", "retrovideogames": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scaryvideogames": "kēmuatakapōmataku", "videogamemaker": "ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "k<PERSON><PERSON><PERSON><PERSON>", "videosgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arcades": "wharerehia", "acnh": "acnh", "puffpals": "<PERSON><PERSON><PERSON>", "farmingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "k<PERSON><PERSON><PERSON><PERSON>", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "papa<PERSON><PERSON><PERSON>", "parasiteeve": "parasiteeve", "gamecube": "kiamutūkēmu", "starcraft2": "starcraft2", "duskwood": "tewaopouri", "dreamscape": "moemoea", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadspace": "w<PERSON><PERSON><PERSON>", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theoldrepublic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videospiele": "k<PERSON><PERSON><PERSON><PERSON>", "touhouproject": "touhouproject", "dreamcast": "porohangamoemoea", "adventuregames": "whakatāngatatanga", "wolfenstein": "wolfenstein", "actionadventure": "pakangamākeke", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "takiwhakatangitangi", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "injustice2": "kaikino2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "kēmurangi", "zenlife": "<PERSON><PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "teitei", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "k<PERSON><PERSON><PERSON><PERSON><PERSON>hitunga", "medievil": "mateataahuatanga", "consolegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolen", "outrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "mat<PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "k<PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiant": "kaitoa", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "kēmukēkē", "bethesda": "bethesda", "jackboxgames": "kēmujackbox", "interactivefiction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "w<PERSON>aipotangatahinengākoro", "visualnovel": "pukapeak<PERSON>rero", "visualnovels": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "kuri<PERSON>o", "tcrghost": "tcraruakore", "payday": "<PERSON><PERSON><PERSON><PERSON>", "chatherine": "kōrerocatherine", "twilightprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "pu<PERSON><PERSON><PERSON><PERSON><PERSON>", "thecrew2": "teroputuaru<PERSON>", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "ringareatua", "leafblowerrevolution": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starrail": "a<PERSON><PERSON><PERSON><PERSON>", "keyblade": "<PERSON><PERSON><PERSON><PERSON>", "aplaguetale": "tepupuhimate", "fnafsometimes": "fna<PERSON>ē<PERSON>i", "novelasvisuales": "pukauaatapakiwaitara", "robloxbrasil": "robloxaotearoa", "pacman": "pacman", "gameretro": "<PERSON><PERSON><PERSON>atawhit<PERSON>", "videojuejos": "k<PERSON><PERSON><PERSON><PERSON>", "videogamedates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "<PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "matewhawhaihanga11", "everskies": "rangirangi", "justcause3": "natekoanoahe", "hulkgames": "kēmuhuriki", "batmangames": "k<PERSON>mupātimana", "returnofreckoning": "tehokingaraikore", "gamstergaming": "k<PERSON><PERSON><PERSON><PERSON><PERSON>a", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "wharepōrangi", "crashracing": "ekeratua", "3dplatformers": "kēmupapawhā3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "k<PERSON><PERSON>koroheke", "hellblade": "<PERSON><PERSON><PERSON>", "storygames": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "kaikarotangi", "beyondtwosouls": "tuwhar<PERSON>atu", "gameuse": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "ketimatepōkēkē", "tinybunny": "rapetimōiti", "retroarch": "retroarch", "powerup": "whetowheto", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "aventurasgraficas", "quickflash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tat<PERSON>", "fzero": "fzero", "gachagaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcades": "m<PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON><PERSON>", "powerwashsim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coralisland": "moutumango", "syberia3": "syberia3", "grymmorpg": "rpgmātaotao", "bloxfruit": "bloxfruit", "anotherworld": "he<PERSON>oaoke", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "<PERSON>hak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "maitakawhēwhē", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "putuputu<PERSON><PERSON><PERSON><PERSON>", "simulator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symulatory": "p<PERSON>mana<PERSON>_whakatau", "speedrunner": "ka<PERSON><PERSON><PERSON><PERSON>", "epicx": "epikx", "superrobottaisen": "robotaitūmatawhāiti", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "kēmurīpeo", "gaiaonline": "gaiaonline", "korkuoyunu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "aotearoaipurangi", "skylander": "skylander", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "toontownituhinoaano", "simracing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simrace": "<PERSON><PERSON><PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "<PERSON><PERSON><PERSON>", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "kaitiakiurupā", "spaceflightsimulator": "whakatauiraatuwhak<PERSON>renga", "legacyofkain": "whakahekeokurikainā", "hackandslash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "ngāatakirīpene", "thewolfamongus": "tekireheiwaengatangata", "truckingsimulator": "<PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handygame": "kē<PERSON>ā<PERSON>ā", "leyendasyvideojuegos": "ngāpakiwaituaraameangātākaro", "oldschoolvideogames": "kēmuatawhitotawhito", "racingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beemov": "pikihauā", "agentsofmayhem": "ngākaimahipōkēkē", "songpop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "famitsu": "famitsu", "gatesofolympus": "kuwahaoorimpia", "monsterhunternow": "kaikimikararehe", "rebelstar": "whet<PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogaming": "kēmuataraingamotuhake", "indiegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogames": "kēmuataraingamotuhake", "indievideogame": "kēmuatakirik<PERSON>tūhak<PERSON>", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "pūngāweromoenoa", "bufffortress": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "kāoretekōrerotūkino", "projectl": "kaupapam", "futureclubgames": "kēmukaraputāp<PERSON><PERSON><PERSON><PERSON>", "mugman": "kanohekapu", "insomniacgames": "kēmukaipōmoe", "supergiantgames": "kēmuapiapiarangatira", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "kēmuceleste", "aperturescience": "pūtaiao<PERSON><PERSON>ā<PERSON>", "backlog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamebacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "ngawharetiketike", "supermonkeyball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deponia": "deponia", "naughtydog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastlord": "kaitohukararehe", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "pūtahidopamine", "staxel": "staxel", "videogameost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsync": "tarakona", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "arohanuitangakofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "berserk": "p<PERSON><PERSON><PERSON>", "baki": "baki", "sailormoon": "<PERSON><PERSON><PERSON><PERSON>", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "pōurianimation", "darkerthanblack": "pōuriakemaitēpango", "animescaling": "animescaling", "animewithplot": "kiriatamaiwhakaaro", "pesci": "pesci", "retroanime": "<PERSON><PERSON><PERSON>", "animes": "kiriata", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "<PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "rau<PERSON><PERSON><PERSON>", "2000sanime": "anime2000", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drst<PERSON><PERSON>uranga<PERSON>", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "uhi_anime", "thevisionofescaflowne": "tepuawarioescaflowne", "slayers": "ngākaiwhakamate", "tokyomajin": "tokyomajin", "anime90s": "animengatautekau", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "hanakokunteherepaku", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "whakah<PERSON><PERSON><PERSON>", "fireforce": "<PERSON><PERSON><PERSON>", "moriartythepatriot": "moriart<PERSON><PERSON><PERSON>ot", "futurediary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fairytail": "p<PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "kitepukenga", "parasyte": "parataito", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "atua_kihi", "blmanga": "blmanga", "horrormanga": "mangawhak<PERSON><PERSON><PERSON>", "romancemangas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karneval": "<PERSON><PERSON><PERSON><PERSON>", "dragonmaid": "kot<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "kunaiwai", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "<PERSON><PERSON><PERSON><PERSON>", "shamanking": "<PERSON><PERSON><PERSON><PERSON>", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "tetahitohumaakutu", "sao": "sao", "blackclover": "pokongakōtukutuku", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "anahera<PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "te<PERSON>hip<PERSON><PERSON><PERSON>", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "<PERSON><PERSON>wara<PERSON><PERSON><PERSON><PERSON><PERSON>", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "atenipatu", "isekaianime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sagaoftanyatheevil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shounenanime": "kiriatamariki", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "tetamaarikimetekararehe", "fistofthenorthstar": "te<PERSON>at<PERSON>rak<PERSON>", "mazinger": "mazinger", "blackbuttler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "kaipeheakite<PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "teputangaatua", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "<PERSON><PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>aorang<PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zeroru<PERSON>", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dio": "dio", "sailorpluto": "<PERSON><PERSON><PERSON><PERSON>", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "kēmu", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "kaitiakipango", "ergoproxy": "ergoproxy", "claymore": "k<PERSON><PERSON>e", "loli": "loli", "horroranime": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "ketehua", "devilmancrybaby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "a<PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "tewhenuakiterautaki", "monstermanga": "mangakaikino", "yourlieinapril": "k<PERSON><PERSON><PERSON>_teka_<PERSON><PERSON>", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "<PERSON><PERSON><PERSON><PERSON>", "deepseaprisoner": "<PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "a<PERSON><PERSON><PERSON>", "bannafish": "parānap<PERSON><PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "ngākōnohinopandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "kit<PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "w<PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "kaiw<PERSON><PERSON><PERSON>", "secretalliance": "<PERSON>non<PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "mukua", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "tekaiwhakakorekino", "slamdunk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "matakite", "spyfamily": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "airgear": "a<PERSON><PERSON><PERSON>", "magicalgirl": "kotiromaakutu", "thesevendeadlysins": "ngāharatū<PERSON>not<PERSON>reow<PERSON><PERSON>", "prisonschool": "kurawhareherehere", "thegodofhighschool": "teatuaokuraatuaroa", "kissxsis": "kih<PERSON><PERSON><PERSON><PERSON>", "grandblue": "<PERSON><PERSON><PERSON>", "mydressupdarling": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "aomangaao", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saowhakāpoto", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "matekorekōmāhak<PERSON>", "romancemanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "whakaahuatangat<PERSON><PERSON>ora", "senpai": "<PERSON><PERSON><PERSON><PERSON>", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayerkitepātū", "bloodlad": "<PERSON><PERSON><PERSON>", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "k<PERSON>a", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "whetūkiteahi", "romanceanime": "mekemeketākaro", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "w<PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "<PERSON>uh<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "pukapikitūtur<PERSON>", "highschoolofthedead": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "tecnomatene", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "piri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON><PERSON>arawa", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "akorngakaipatu", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "matekapeka", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japanese<PERSON><PERSON>", "animespace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsundpanzer": "kot<PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "kaikupuuq", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "kiriataanipō", "ratman": "kioretanga<PERSON>", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "k<PERSON>tukumāwhero", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "ng<PERSON><PERSON><PERSON>", "deliciousindungeon": "tekaingākataahi", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "rekoatawhakangaro", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "pekekingatītaha", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "awhiā<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "<PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "wharew<PERSON><PERSON><PERSON><PERSON><PERSON>ahua", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "ka<PERSON>ateao", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "ngakaihautuoteaowhiti", "animeshojo": "animeshojo", "reverseharem": "tanetinimooku", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "kaiakomatangataaonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "h<PERSON>ia", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "kear5", "grandbluedreaming": "moemoeataahuatoa", "bloodplus": "<PERSON><PERSON><PERSON><PERSON>", "bloodplusanime": "bloodplusanime", "bloodcanime": "totoaniime", "bloodc": "toto", "talesofdemonsandgods": "ngakorerooataniwhaataatua", "goreanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animegirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatteranime": "anime_pirau", "splatter": "paka<PERSON><PERSON><PERSON><PERSON>", "risingoftheshieldhero": "tepurangateotetūākite<PERSON><PERSON><PERSON><PERSON>", "somalianime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "tamarikiothewere", "liarliar": "re<PERSON><PERSON>e", "supercampeones": "tū<PERSON>hurangatira", "animeidols": "animeidols", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "karangaotepō", "bakuganbrawler": "kaiwhakatetetebakauka<PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "ma<PERSON><PERSON>a", "tsubasachronicle": "tsubasachronicle", "findermanga": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "princessjellyfish": "pirihiniterep<PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "kih<PERSON><PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "<PERSON><PERSON>aringa", "animeverse": "aomanga", "persocoms": "ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omniscientreadersview": "mat<PERSON><PERSON><PERSON>kai<PERSON>ī<PERSON><PERSON>", "animecat": "<PERSON><PERSON><PERSON><PERSON>", "animerecommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openinganime": "ahuatangaw<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "taku<PERSON><PERSON><PERSON><PERSON><PERSON>pa<PERSON>", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathnote": "<PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jo<PERSON><PERSON><PERSON><PERSON><PERSON>ekere<PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "paka<PERSON><PERSON><PERSON>", "greenranger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "pāpopiwikato", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON>n", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "tauakawehaweha", "onepieceanime": "onepieceanime", "attaquedestitans": "attaquedestitans", "theonepieceisreal": "koi<PERSON>piece<PERSON><PERSON><PERSON><PERSON>", "revengers": "kiaitanga", "mobpsycho": "mobpsycho", "aonoexorcist": "tewhakak<PERSON><PERSON><PERSON><PERSON><PERSON>", "joyboyeffect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimonstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "mateaiwhenua", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "rawepapatuhi<PERSON><PERSON><PERSON>", "kemonofriends": "<PERSON><PERSON><PERSON><PERSON>", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "p<PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}