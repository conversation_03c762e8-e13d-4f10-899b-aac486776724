{"2048": "2048", "mbti": "mbti", "enneagram": "eneagramma", "astrology": "astroloģija", "cognitivefunctions": "kognitīvāsfunkcijas", "psychology": "psiholoģija", "philosophy": "filozofija", "history": "vēsture", "physics": "fizika", "science": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "culture": "<PERSON><PERSON><PERSON><PERSON>", "languages": "valodas", "technology": "tehnoloģija", "memes": "mēmes", "mbtimemes": "mbt<PERSON><PERSON><PERSON>", "astrologymemes": "astroloģijasmēmes", "enneagrammemes": "eneagrammasmēmes", "showerthoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "smieklīgi", "videos": "video", "gadgets": "gadžeti", "politics": "politika", "relationshipadvice": "attie<PERSON><PERSON><PERSON>pad<PERSON>", "lifeadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "kripto", "news": "<PERSON><PERSON><PERSON><PERSON>", "worldnews": "p<PERSON><PERSON><PERSON>ņ<PERSON>", "archaeology": "arheoloģija", "learning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "debates": "debates", "conspiracytheories": "sazvērestībasteorijas", "universe": "universs", "meditation": "meditā<PERSON>ja", "mythology": "mitologija", "art": "m<PERSON><PERSON><PERSON>", "crafts": "amati", "dance": "deja", "design": "dizains", "makeup": "grims", "beauty": "skaistums", "fashion": "mode", "singing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "photography": "fotografija", "cosplay": "kos<PERSON><PERSON>šana", "painting": "glez<PERSON>šana", "drawing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "books": "gr<PERSON><PERSON><PERSON>", "movies": "filmas", "poetry": "dzeja", "television": "televīzija", "filmmaking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anime": "anime", "scifi": "zinātniskāfantastika", "fantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentaries": "do<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mystery": "noslēpums", "comedy": "komēdija", "crime": "noziegums", "drama": "dr<PERSON><PERSON>", "bollywood": "bolivuds", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "<PERSON><PERSON><PERSON>", "romance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realitytv": "realitātestv", "action": "darbība", "music": "<PERSON><PERSON><PERSON><PERSON>", "blues": "blū<PERSON>s", "classical": "klasika", "country": "valsts", "desi": "desi", "edm": "edm", "electronic": "elektronika", "folk": "folk", "funk": "fanks", "hiphop": "hiphops", "house": "m<PERSON><PERSON>", "indie": "indie", "jazz": "<PERSON><PERSON><PERSON><PERSON>", "kpop": "kpops", "latin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metal": "<PERSON><PERSON><PERSON>", "pop": "pop", "punk": "panks", "rnb": "rnb", "rap": "reps", "reggae": "rege<PERSON><PERSON>", "rock": "roks", "techno": "tehno", "travel": "<PERSON><PERSON>ļ<PERSON><PERSON><PERSON>", "concerts": "<PERSON><PERSON><PERSON><PERSON>", "festivals": "festiv<PERSON><PERSON>", "museums": "muzeji", "standup": "stendaps", "theater": "<PERSON><PERSON><PERSON><PERSON>", "outdoors": "ā<PERSON><PERSON><PERSON><PERSON><PERSON>", "gardening": "d<PERSON>rz<PERSON>pī<PERSON>", "partying": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "dungeonsanddragons", "chess": "<PERSON><PERSON>s", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "ēdiens", "baking": "<PERSON><PERSON><PERSON><PERSON>", "cooking": "gatavošana", "vegetarian": "veģetārietis", "vegan": "vegāns", "birds": "putni", "cats": "kaķi", "dogs": "<PERSON><PERSON><PERSON>", "fish": "zivis", "animals": "dzīvnieki", "blacklivesmatter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "environmentalism": "vide", "feminism": "feminisms", "humanrights": "cilvēktiesības", "lgbtqally": "lgbtqsa<PERSON><PERSON><PERSON><PERSON>", "stopasianhate": "stopāzijunaidslv", "transally": "transpersonu_at<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "volunteering": "brīvprāt<PERSON>gais_darbs", "sports": "sportiņš", "badminton": "badmintons", "baseball": "beisbols", "basketball": "basketbols", "boxing": "bokss", "cricket": "krikets", "cycling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fitness": "fitness", "football": "futbols", "golf": "golfs", "gym": "sportzā<PERSON>", "gymnastics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hockey": "hokejs", "martialarts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "netbols", "pilates": "pilates", "pingpong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "running": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skateboarding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snowboarding": "snov<PERSON>šana", "surfing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swimming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tennis": "teniss", "volleyball": "volejbols", "weightlifting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "joga", "scubadiving": "<PERSON><PERSON><PERSON><PERSON>", "hiking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "capricorn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aquarius": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pisces": "zivis", "aries": "auns", "taurus": "vē<PERSON><PERSON>", "gemini": "dv<PERSON><PERSON><PERSON>", "cancer": "v<PERSON><PERSON><PERSON>", "leo": "lauva", "virgo": "jauna<PERSON>", "libra": "svari", "scorpio": "skorpions", "sagittarius": "strēlnieks", "shortterm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "ikdienišķs", "longtermrelationship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "single": "viens", "polyamory": "poliamorija", "enm": "nav", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "geju", "lesbian": "les<PERSON><PERSON>", "bisexual": "bise<PERSON><PERSON><PERSON><PERSON>", "pansexual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "d<PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "zemteksts", "legendofspyro": "leģendaparspīro", "rouguelikes": "rouguelikes", "syberia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rdr2": "rdr2", "spyrothedragon": "spa<PERSON><PERSON><PERSON>s", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "die<PERSON><PERSON><PERSON>", "fireemblemfates": "fireemblemfates", "yokaiwatch": "jokajuskate", "rocksteady": "rokstabilais", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "<PERSON><PERSON><PERSON><PERSON>", "openworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "dv<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsoftherealm2": "valstulordai2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medabots": "medaboti", "lodsoftherealm2": "valdniekutīgas2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "lo<PERSON><PERSON><PERSON><PERSON>", "witcher": "ragans", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "tumšā<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "apokalipse", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modā", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "falloutnewvegas": "fallout<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyvecāskola", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoja", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbīdāmotivācija", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "mīlasmuļķis", "otomegames": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "okariinaslaiks", "yiikrpg": "yiikrpg", "vampirethemasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dimension20": "dimension20", "gaslands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2edīcija", "shadowrun": "ēnuskr<PERSON><PERSON><PERSON>", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravitātestrieciens", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "vienareize", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "pav<PERSON>lnieks", "yourturntodie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpg<PERSON><PERSON><PERSON>", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "<PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgteksts", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "atompatvertne", "gurps": "rolloņuklubs", "darkestdungeon": "tum<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "apt<PERSON><PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "ārpasaules", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skullgirls": "skullgirls", "nightcity": "naktsvilsēta", "hogwartslegacy": "hogvartsasmantojums", "madnesscombat": "trak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neka<PERSON><PERSON><PERSON><PERSON>", "road96": "ceļš96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelike", "gothamknights": "gothamknights", "forgottenrealms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonlance": "dragonu_šķēps", "arenaofvalor": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "multenesgradina", "childoflight": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonpasaule", "monsterrancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ekopanks", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "ē<PERSON><PERSON><PERSON>", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartmystery": "hogvartsanoslēpums", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smaids", "lastepoch": "pē<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "zvaigžņumeklētājs", "goldensun": "zeltssaule", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "asmen<PERSON><PERSON>ā", "twilight2000": "krēsla2000", "sandevistan": "sandevistans", "cyberpunk": "kiberpanks", "cyberpunk2077": "kiberpanks2077", "cyberpunkred": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "veln<PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "dievišķība", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "vec<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "piedzīvojumameklējumi", "dagorhir": "<PERSON>gorkara", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "tales<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "honkaistarrail": "honkaistarrail", "wolong": "volongs", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "pl<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfarog": "mansfarogs", "sacredunderworld": "svē<PERSON>ā<PERSON>aul<PERSON>", "chainedechoes": "ķēžuatbalsis", "darksoul": "tum<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "othercide": "citāpuse", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "hronotrigeris", "pillarsofeternity": "mūžībasstabi", "palladiumrpg": "palladiumrpg", "rifts": "plaisas", "tibia": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "thedivision": "thedivision", "hellocharlotte": "sveik<PERSON>rlote", "legendofdragoon": "legendofdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "vilkačuapokalipse", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "mort<PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable3": "fable3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollenspiel": "lo<PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "vecāsskolasatdzimšana", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "nežēlīgāspasaules", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "karaliskāsirds1", "ff9": "ff9", "kingdomheart2": "kingdomhearts2", "darknessdungeon": "tumsascietums", "juegosrpg": "rpgsp<PERSON>les", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomhearts3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harvestella": "rudensdeja", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "bastions", "drakarochdemoner": "pūķiundemoni", "skiesofarcadia": "skiesofarcadia", "shadowhearts": "<PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosija", "pennyblood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "breathoffire4", "mother3": "māte3", "cyberpunk2020": "kiberpanks2020", "falloutbos": "falloutbos", "anothereden": "citaēdene", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "lo<PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "raganassirds", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "burvestī<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "hronoškē<PERSON><PERSON>šana", "cocttrpg": "kokteiļuttrpg", "huntroyale": "medībukaraliskais", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "ksenošūpoles", "temtem": "temtem", "rpgforum": "rpgforums", "shadowheartscovenant": "šēnhārtaspakts", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "nākskaraliste", "awplanet": "awplanēta", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "tumšāherēzija", "shoptitans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forumrpg": "forumrpg", "golarion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earthmagic": "zemesmaģija", "blackbook": "melnag<PERSON><PERSON><PERSON>", "skychildrenoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "svētaiszeltaizdevums", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "spokuvaditokyja", "fallout2d20": "fallout2d20", "gamingrpg": "spēlurpg", "prophunt": "prop_me<PERSON><PERSON><PERSON>", "starrails": "zvaigžņusliedes", "cityofmist": "pilsētasmigla", "indierpg": "indīrpg", "pointandclick": "klikšķiniunorādi", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "nedal<PERSON><PERSON>", "freeside": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postkiberpanks", "deathroadtocanada": "nāvesceļšuz<PERSON>ādu", "palladium": "pala<PERSON><PERSON><PERSON>", "knightjdr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "monstrahunter", "fireemblem": "ugunsemb<PERSON>ē<PERSON>", "genshinimpact": "genshinimpact", "geosupremancy": "ģeosupremācija", "persona5": "persona5", "ghostoftsushima": "spoksnocušimas", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "taktiskarpg", "mahoyo": "mahoyo", "animegames": "animespēles", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "princessconnect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexenzirkel": "<PERSON><PERSON><PERSON><PERSON>", "cristales": "<PERSON><PERSON><PERSON><PERSON>", "vcs": "vks", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorant<PERSON><PERSON><PERSON><PERSON>", "dota": "dota", "madden": "dusmoties", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "esp<PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esports", "mlg": "mlg", "leagueofdreamers": "sapņ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa14": "fifa14", "midlaner": "<PERSON><PERSON><PERSON>", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "spēlējam", "overwatchleague": "overwatchlīga", "cybersport": "kibersports", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eautosa<PERSON><PERSON><PERSON><PERSON>", "brasilgameshow": "<PERSON>z<PERSON><PERSON>jas<PERSON><PERSON>", "valorantcompetitive": "valorantcompetitive", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portāls2", "halflife": "pusdzīve", "left4dead": "atstātsmir<PERSON>", "left4dead2": "left4dead2", "valve": "v<PERSON><PERSON><PERSON>", "portal": "<PERSON><PERSON><PERSON>", "teamfortress2": "teamfortress2", "everlastingsummer": "mūžīgāvasara", "goatsimulator": "kazusimulatorslv", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "brīvībasplanēta", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "battlefield4", "nightinthewoods": "naktsmeža", "halflife2": "halflife2", "hacknslash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanijas", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "starpplanētu", "helltaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inscryption": "inskriptācija", "7d2d": "7d2d", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "punduru<PERSON><PERSON><PERSON><PERSON>", "foxhole": "sl<PERSON><PERSON><PERSON>", "stray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "zemūdene", "eyeb": "<PERSON><PERSON><PERSON>", "blackdesert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "galdaspeļusimulators", "partyhard": "ballēciež", "hardspaceshipbreaker": "cietākuģulauztuve", "hades": "heids", "gunsmith": "šaujammeistars", "okami": "<PERSON>ami", "trappedwithjester": "nokļ<PERSON>vissla<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinkum": "<PERSON><PERSON><PERSON>", "predecessor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rainworld": "lietuspassaule", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolo<PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "<PERSON><PERSON><PERSON><PERSON>", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "tum<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "melnā<PERSON><PERSON>", "soulworker": "dvēselesstrādnieks", "datingsims": "iepazīšanāssimulatori", "yaga": "jaga", "cubeescape": "kubaizbēgšana", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "def<PERSON><PERSON><PERSON>", "kenopsia": "kenopsija", "virtualkenopsia": "virtuālākenopsija", "snowrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "ruīnubibliotēka", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "<PERSON>ena<PERSON><PERSON><PERSON>", "placidplasticduck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlebit": "battlebit", "ultimatechickenhorse": "<PERSON><PERSON><PERSON><PERSON>", "dialtown": "sarun<PERSON><PERSON><PERSON><PERSON>", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "kaķunakts", "supermeatboy": "supergaļaspuika", "tinnybunny": "maziņzaķītis", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "glums", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdu<PERSON><PERSON><PERSON><PERSON>", "apex": "topā", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earthdefenseforce": "zemesaizsardzībasspēki", "huntshowdown": "huntshowdown", "ghostrecon": "spoku<PERSON><PERSON><PERSON><PERSON>", "grandtheftauto5": "grandtheftauto5", "warz": "kari", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultranogalināt", "joinsquad": "pievienojieskomandai", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "nemiernieķusmilšuvētra", "farcry3": "farcry3", "hotlinemiami": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxpayne": "ma<PERSON><PERSON><PERSON>", "hitman3": "slepkava3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "nā<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombiji", "mirrorsedge": "spo<PERSON><PERSON><PERSON><PERSON>", "divisions2": "dalījumi2", "killzone": "<PERSON>galinā<PERSON>a", "helghan": "hel<PERSON>", "coldwarzombies": "aukstākarazomb<PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "krosukods", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "snaiperaelite", "modernwarfare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neonabyss": "neon<PERSON><PERSON><PERSON><PERSON><PERSON>", "planetside2": "planetside2", "mechwarrior": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boarderlands": "<PERSON><PERSON><PERSON><PERSON>", "owerwatch": "ower<PERSON>", "rtype": "rtips", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofwarships": "pasauleskuģukari", "back4blood": "atpakaļ4asinīm", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "īpašooperāciju<PERSON>ža", "killingfloor2": "killingfloor2", "cavestory": "alukāsts", "doometernal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "centuryageofashes": "gadsimtapelnuvecums", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "nulles<PERSON><PERSON><PERSON>", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "karamaskotā", "crossfire": "krustuguns", "atomicheart": "atomiskāsirds", "blackops3": "blackops3", "vampiresurvivors": "vamp<PERSON><PERSON>iz<PERSON>zī<PERSON>tā<PERSON>", "callofdutybatleroyale": "callofdutybattleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "brī<PERSON><PERSON><PERSON>", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "frag", "tinytina": "<PERSON><PERSON><PERSON><PERSON>", "gamepubg": "spēlepubg", "necromunda": "nekromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "f<PERSON><PERSON><PERSON><PERSON>", "convertstrike": "sarun<PERSON>_uzb<PERSON><PERSON>", "warzone2": "warzone2", "shatterline": "šķembulīnija", "blackopszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodymess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "republikaskomandieris", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "groundbranch", "squad": "<PERSON><PERSON><PERSON><PERSON>", "destiny1": "liktenis1", "gamingfps": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "redfall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubggirl": "pubgmeitene", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "iesaistīts", "farlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "<PERSON>īkā<PERSON><PERSON>nasbrīnum<PERSON><PERSON>", "halo2": "halo2", "payday2": "algasdiena2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgczech": "pubgčehija", "titanfall2": "titanfall2", "soapcod": "ziepeszivs", "ghostcod": "spokukods", "csplay": "csplay", "unrealtournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "plēvepā<PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "quakeč<PERSON><PERSON><PERSON>", "halo3": "halo3", "halo": "s<PERSON><PERSON>s", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonsbalts", "remnant": "pali<PERSON>s", "azurelane": "azurelane", "worldofwar": "karapasaule", "gunvolt": "gunvolt", "returnal": "atgriezes", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "ēnuvīrs", "quake2": "quake2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "sarkans<PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "<PERSON><PERSON><PERSON>", "conqueronline": "conqueronline", "dauntless": "nebaidīgs", "warships": "karakuģi", "dayofdragons": "pūķudiena", "warthunder": "warthunder", "flightrising": "lidojošāspūķi", "recroom": "atpūtasistaba", "legendsofruneterra": "legendsofruneterra", "pso2": "pso2", "myster": "noslēpums", "phantasystaronline2": "phantasystaronline2", "maidenless": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "svītrot", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubiņšpingvīns", "lotro": "lotro", "wakfu": "wakfu", "scum": "atbrāķis", "newworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pirate101": "pirāts101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dchat": "3dčats", "nostale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tauriwow": "taurawow", "wowclassic": "wowklasika", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mulegend": "manaleģenda", "startrekonline": "startrektiešsaistē", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "pūķapravieša", "grymmo": "grymmo", "warmane": "varmane", "multijugador": "multijugador", "angelsonline": "eņģeļitionlainā", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarssenārepublika", "grandfantasia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blueprotocol": "blueprotocol", "perfectworld": "id<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "corepunk": "kodolpanks", "adventurequestworlds": "piedzīvojumupasaule", "flyforfun": "lidopriekaprieka", "animaljam": "animaljam", "kingdomofloathing": "loathing<PERSON><PERSON><PERSON>", "cityofheroes": "varoņ<PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streetfighter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "pargodu", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "ielas<PERSON>ī<PERSON>ī<PERSON><PERSON>js6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "dvēseleskaliburs", "brawlhalla": "brawlhalla", "virtuafighter": "virt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mkbīstamāsavienība", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "thekingoffighters", "likeadragon": "kāpūķis", "retrofightinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blasphemous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelpretcapcom", "supersmash": "superdauzīšana", "mugen": "mugen", "warofthemonsters": "monstruslavs", "jogosdeluta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "k<PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poweredgear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "<PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "slepkavasinstinkts", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "spokub<PERSON><PERSON><PERSON><PERSON>s", "chivalry2": "bruņnieciskums2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightturpinājums", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonglap<PERSON><PERSON><PERSON>", "silksonggame": "silksonggame", "silksongnews": "silksongjaunumi", "silksong": "silksong", "undernight": "pazemēnakts", "typelumina": "r<PERSON><PERSON><PERSON><PERSON>", "evolutiontournament": "evolūcijasturn<PERSON>", "evomoment": "evomoments", "lollipopchainsaw": "mīlulītismotorzāģis", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "asinsdzi<PERSON>", "horizon": "horizonta", "pathofexile": "pathofexile", "slimerancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashbandicoot": "crashbandicoot", "bloodbourne": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uncharted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstationbuddies": "playstationdraugi", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "slaidisvāģis", "psp": "psp", "rabbids": "rabbids", "splitgate": "šķeltnevārti", "persona4": "persona4", "hellletloose": "<PERSON><PERSON><PERSON>ļ<PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "kardi<PERSON><PERSON>", "gris": "meitenes", "trove": "krā<PERSON><PERSON>", "detroitbecomehuman": "detroitbecomehuman", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "tūristutrofeja", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "crashteamracing", "fivepd": "piecipd", "tekken7": "tekken7", "devilmaycry": "ve<PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "spēlējustaciju", "samuraiwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "dv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "spēļ<PERSON><PERSON>", "armello": "armello", "partyanimal": "ballītēszvērs", "warharmmer40k": "warharmmer40k", "fightnightchampion": "cīņasnaktščempions", "psychonauts": "psihonauti", "mhw": "mhw", "princeofpersia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "elderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "neizbadieskopā", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "zvaigžņumežģis", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "am<PERSON><PERSON><PERSON><PERSON>", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "karaļval<PERSON><PERSON><PERSON><PERSON>", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "mēs<PERSON>v", "skycotl": "debesuk<PERSON>", "erica": "erica", "ancestory": "sen<PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "ma<PERSON>āneveik<PERSON><PERSON><PERSON>", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterballe", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motoci<PERSON><PERSON>", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duckgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "stenlī<PERSON><PERSON><PERSON>", "towerunite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "occulto": "slepens", "longdrive": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "satisfactory": "<PERSON><PERSON><PERSON><PERSON>š<PERSON>", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "zemzemes", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "garu<PERSON><PERSON><PERSON>", "darkdome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pizzatower": "pica<PERSON><PERSON>", "indiegame": "indī<PERSON><PERSON><PERSON>", "itchio": "itchio", "golfit": "golfo", "truthordare": "patiesī<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "sp<PERSON>le", "rockpaperscissors": "akmensšķērespapīrs", "trampoline": "batuts", "hulahoop": "hulahops", "dare": "uzdrīksties", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>spēle", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "izvēliesnumuru", "trueorfalse": "tiešāmvainepatiesība", "beerpong": "aluspongs", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "iepazīša<PERSON>ā<PERSON><PERSON><PERSON><PERSON>", "freegame": "bezmaksasspē<PERSON>", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>ē<PERSON>", "sodoku": "sudoku", "juegos": "sp<PERSON><PERSON>", "mahjong": "madžongs", "jeux": "sp<PERSON><PERSON>", "simulationgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "oyun", "interactivegames": "interaktīvāsspēles", "amtgard": "amtgard", "staringcontests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "sp<PERSON><PERSON>", "giochi": "sp<PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boogames": "boos<PERSON><PERSON><PERSON>", "cranegame": "c<PERSON>tņ<PERSON><PERSON><PERSON><PERSON>", "hideandseek": "paslēpesunmeklē", "hopscotch": "<PERSON><PERSON><PERSON><PERSON>", "arcadegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "klasiskā<PERSON><PERSON><PERSON>", "mindgames": "prāta_spēles", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "galasp<PERSON><PERSON>", "romancegame": "romantikasspēle", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4xgames": "4x<PERSON><PERSON><PERSON>", "gamefi": "gamefi", "jeuxdarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "speles90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "<PERSON>umi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "sac<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "īstsviltots", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosonline": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playaballgame": "spēlējamsportu", "pictionary": "piktogrammas", "coopgames": "kooperatīvā<PERSON><PERSON><PERSON><PERSON>", "jenga": "<PERSON><PERSON><PERSON><PERSON>", "wiigames": "wii<PERSON><PERSON><PERSON>", "highscore": "augstā<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "burger<PERSON><PERSON><PERSON><PERSON>", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeebols", "nfsmwblackedition": "nfsmwmelnāizdevuma", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "sp<PERSON>le", "managementgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "pas<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roolipelit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "formula1game": "formula1spēle", "citybuilder": "pilsē<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "drdriving": "drdriving", "juegosarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "memorygames": "<PERSON>miņass<PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "sp<PERSON><PERSON>", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "fliperi", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameo": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "lasergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "imess<PERSON><PERSON><PERSON><PERSON>", "idlegames": "bezdarbības<PERSON><PERSON><PERSON>", "fillintheblank": "aizpildit<PERSON><PERSON><PERSON>", "jeuxpc": "jeuxpc", "rétrogaming": "retrogaming", "logicgames": "loģikasspēles", "japangame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "<PERSON><PERSON><PERSON>bos<PERSON><PERSON><PERSON>", "subwaysurf": "metroserfo", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5pret5", "rolgame": "lo<PERSON><PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "tradici<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kauli<PERSON><PERSON>_spēle", "gamefps": "spēlesfps", "textbasedgames": "<PERSON>kstaspē<PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospēle", "thiefgame": "zag<PERSON><PERSON>gas<PERSON>ē<PERSON>", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "galdfutbols", "tischfußball": "galdfutbols", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "ikdienasspēles", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "zagļuspē<PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "játék": "sp<PERSON>le", "bordfodbold": "galdfutbols", "jogosorte": "jogosorte", "mage": "burvis", "cargames": "autospēles", "onlineplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "somubingo", "randomizer": "randomaizers", "msx": "msx", "anagrammi": "anagrammas", "gamespc": "spēlesdatoram", "socialdeductiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominos": "domino", "domino": "domino", "isometricgames": "<PERSON>zometris<PERSON>ā<PERSON><PERSON><PERSON><PERSON>", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "patiesībaivaiuzdrī<PERSON>ties", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ē<PERSON>", "jeuxvirtuel": "virtu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "f2pspēlētājs", "free2play": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "sp<PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "helotvseriesunspeles", "mushroomoasis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anythingwithanengine": "viskaskamirmotors", "everywheregame": "visirgame", "swordandsorcery": "mečiunmaģija", "goodgamegiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "spēlējam", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "spēlējam", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "miniatū<PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "galdaspelesrullē", "spelletjes": "<PERSON><PERSON><PERSON><PERSON>", "spacenerf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "<PERSON><PERSON><PERSON><PERSON>", "singleplayer": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgame": "koop<PERSON><PERSON><PERSON>", "gamed": "sp<PERSON><PERSON><PERSON><PERSON>", "forzahorizon": "forzahorizon", "nexus": "savienojums", "geforcenow": "geforcenow", "maingame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "karalisdiscord", "scrabble": "scrabble", "schach": "<PERSON><PERSON>s", "shogi": "<PERSON><PERSON>i", "dandd": "dnd", "catan": "katans", "ludo": "ludo", "backgammon": "bekgemons", "onitama": "onitama", "pandemiclegacy": "pandē<PERSON>jasmantoju<PERSON>", "camelup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopolygame": "monopolsspēle", "brettspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planszowe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "risiko": "risks", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "<PERSON><PERSON><PERSON><PERSON>", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ņ<PERSON><PERSON>", "connectfour": "četririndā", "heroquest": "varoņupiedzīvojums", "giochidatavolo": "galdaspeleslatvis<PERSON>", "farkle": "farkle", "carrom": "karambols", "tablegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "galdas<PERSON><PERSON><PERSON><PERSON>", "yatzy": "jat<PERSON>", "parchis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "kosmiskssatikšanās", "creationludique": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "telefonistespēles", "infinitythegame": "<PERSON>zgalīgas<PERSON>ē<PERSON>", "kingdomdeath": "ka<PERSON><PERSON>stsn<PERSON><PERSON>", "yahtzee": "jac<PERSON><PERSON><PERSON>", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "sa<PERSON><PERSON><PERSON><PERSON>", "juegodemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "<PERSON><PERSON><PERSON><PERSON>", "rednecklife": "<PERSON>uki<PERSON><PERSON>", "boardom": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "applestoapples": "ābolipretsāboliem", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "krokinols", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "krēslasimpērija", "horseopoly": "zirgopols", "deckbuilding": "klājaslikšana", "mansionsofmadness": "trakumanmēdija", "gomoku": "gomoku", "giochidatavola": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "brims<PERSON><PERSON><PERSON><PERSON>", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcaby": "dam<PERSON><PERSON>", "táblajátékok": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "kaujakuģis", "tickettoride": "biļeteuzbraucienu", "deskovehry": "deskovehry", "catán": "catán", "subbuteo": "galdasfutbols", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "galdaspeleslatvija", "gesellschaftsspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "starwarslegion", "gochess": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weiqi": "<PERSON><PERSON><PERSON><PERSON>", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identityv", "theisle": "sala", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "<PERSON><PERSON><PERSON><PERSON>", "callofcthulhu": "cthulhuaicinājums", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "stripmums", "eco": "eko", "monkeyisland": "pērtiķusala", "valheim": "valheim", "planetcrafter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "fobija", "witchit": "burvestība", "pathologic": "patoloģisks", "zomboid": "zomb<PERSON>īds", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "ilgaist<PERSON>s", "ark": "ark", "grounded": "ierobežots", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "trak<PERSON><PERSON>č", "dontstarve": "neizbadiņies", "eternalreturn": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "tit<PERSON><PERSON><PERSON>", "frictionalgames": "frictionalgames", "hexen": "raganas", "theevilwithin": "ļaunumsiekšā", "realrac": "<PERSON><PERSON><PERSON><PERSON>", "thebackrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiressmp": "empiressmp", "blockstory": "b<PERSON>kus<PERSON>ā<PERSON>", "thequarry": "<PERSON><PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "mirst<PERSON><PERSON><PERSON>", "thewalkingdeadgame": "thewalkingdeadspēle", "wehappyfew": "mēsnedaudziebetlaimīgie", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "stateofsurvivalspēle", "vintagestory": "vintagestāsts", "arksurvival": "arkizdzīvošana", "barotrauma": "barotrauma", "breathedge": "elpasgars", "alisa": "alisa", "westlendsurvival": "vestendasizdzīvošana", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "tumšaism<PERSON>ž<PERSON>", "survivalhorror": "šausmuizdzīvošana", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "tukšumavilciens", "lifeaftergame": "<PERSON>zī<PERSON><PERSON><PERSON><PERSON>pē<PERSON>", "survivalgames": "izdzīvošanasspēles", "sillenthill": "klusiniekalni", "thiswarofmine": "šismanskarsthegame", "scpfoundation": "scpf<PERSON>s", "greenproject": "zaļaisprojekts", "kuon": "kuon", "cryoffear": "raud<PERSON><PERSON><PERSON><PERSON>", "raft": "plosts", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauts", "granny": "vecenīte", "littlenightmares2": "maziemurgi2", "signalis": "<PERSON><PERSON><PERSON>", "amandatheadventurer": "amandapiedzīvojumuvadītā<PERSON>", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "rust<PERSON><PERSON><PERSON><PERSON><PERSON>", "outlasttrials": "<PERSON>z<PERSON>ēt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "sveš<PERSON><PERSON>sol<PERSON>", "undawn": "saullēkts", "7day2die": "7dienas2nāve", "sunlesssea": "bezasaulessea", "sopravvivenza": "izdzīvošana", "propnight": "propnakts", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampī<PERSON>", "deathverse": "nāvesvisums", "cataclysmdarkdays": "kataklizmatumšā<PERSON>dienas", "soma": "soma", "fearandhunger": "bailesunbads", "stalkercieńczarnobyla": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeafter": "dzīvepē<PERSON>", "ageofdarkness": "tums<PERSON><PERSON><PERSON>", "clocktower3": "pulkstentornis3", "aloneinthedark": "vienspašstumšā", "medievaldynasty": "viduslaikusdinastija", "projectnimbusgame": "projectnimbusspēle", "eternights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftopia": "amatniecība", "theoutlasttrials": "theoutlasttrials", "bunker": "bunkurs", "worlddomination": "pasauleskundzība", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "necron": "nek<PERSON>", "wfrp": "wfrp", "dwarfslayer": "pundur<PERSON><PERSON><PERSON>s", "warhammer40kcrush": "warhammer40kiemīlē<PERSON><PERSON>", "wh40": "wh40", "warhammer40klove": "warhammer40kmīlestība", "warhammer40klore": "warhammer<PERSON><PERSON><PERSON><PERSON><PERSON>i", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindicare": "vindicare", "ilovesororitas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "esmiluvindicare", "iloveassasinorum": "esmiluassasinorum", "templovenenum": "tempsmīlestībaindēm", "templocallidus": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templomaerorus": "tempļaero<PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lauva", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "spārnuplat<PERSON>", "terraformingmars": "terraformingmarss", "heroesofmightandmagic": "varoņinovarasunmaģijas", "btd6": "btd6", "supremecommander": "galvenaispavēlnieks", "ageofmythology": "mītuloģijaslaikmets", "args": "args", "rime": "rime", "planetzoo": "planētaszoo", "outpost2": "outpost2", "banished": "<PERSON><PERSON><PERSON><PERSON>ī<PERSON>", "caesar3": "caesar3", "redalert": "sarka<PERSON><PERSON><PERSON><PERSON><PERSON>e", "civilization6": "civilizācija6", "warcraft2": "warcraft2", "commandandconquer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strategygames": "stratēģijaspēles", "anno2070": "anno2070", "civilizationgame": "civilizācijasspēle", "civilization4": "civilizācija4", "factorio": "factorio", "dungeondraft": "<PERSON><PERSON><PERSON>šņ<PERSON><PERSON>ān<PERSON>", "spore": "sporas", "totalwar": "pil<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travian": "travian", "forts": "forti", "goodcompany": "labākompānija", "civ": "civilizācija", "homeworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heidentum": "pagānisms", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "reāllaikastratēģija", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eu4": "es4", "vainglory": "iedomība", "ww40k": "ww40k", "godhood": "dievišķums", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hetriks", "davesfunalgebraclass": "deivasjautrāalgebrasklase", "plagueinc": "plagueinclv", "theorycraft": "teorijkalde", "mesbg": "mesbg", "civilization3": "civilizācija3", "4inarow": "4p<PERSON><PERSON><PERSON><PERSON><PERSON>", "crusaderkings3": "krustnešukaraļi3", "heroes3": "heroes3", "advancewars": "kaujukaŗi", "ageofempires2": "ageofempires2", "disciples2": "disciples2", "plantsvszombies": "augipret<PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "stratēģijaspēles", "stratejioyunları": "stratēģijasspēles", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "heartsofiron4", "companyofheroes": "varoņukompānija", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "valdībukalve", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phobies": "fob<PERSON>s", "phobiesgame": "fob<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "pagrie<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "burvestības", "starwarsempireatwar": "zvaigžņukaruimpērijakarā", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "stratēģija", "popfulmail": "populārspasts", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "daison<PERSON><PERSON><PERSON><PERSON>gram<PERSON>", "transporttycoon": "transportamagnāts", "unrailed": "<PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "vil<PERSON><PERSON><PERSON>ē<PERSON>", "ooblets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planescapetorment": "pleinskeipartorments", "uplandkingdoms": "augstienuvalstiibas", "galaxylife": "galaktikasdzīve", "wolvesvilleonline": "vilkubāzeonline", "slaythespire": "slaythespire", "battlecats": "kaķukauja", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "vajadzī<PERSON>ātruma<PERSON>", "needforspeedcarbon": "vajadzībaātrumamu<PERSON>lis", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "zaudējumsims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "alicesnepratsmeatgriežas", "darkhorseanthology": "tumšāzirgantoloģija", "phasmophobia": "fazmofobija", "fivenightsatfreddys": "piecasnaktispiefredija", "saiko": "saiko", "fatalframe": "liktenīgaiska<PERSON><PERSON>", "littlenightmares": "mazienakšumurgi", "deadrising": "deadrising", "ladydimitrescu": "leidijadimitresku", "homebound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "ma<PERSON>āneveik<PERSON><PERSON><PERSON>", "projectzero": "projektsnulle", "horory": "<PERSON><PERSON><PERSON>", "jogosterror": "skriešanasterors", "helloneighbor": "sveikskaimiņ", "helloneighbor2": "halloukaimiņ2", "gamingdbd": "gamingdbd", "thecatlady": "kaķutante", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "kribidžs", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "<PERSON><PERSON><PERSON>", "codenames": "seg<PERSON><PERSON><PERSON>", "dixit": "<PERSON><PERSON>", "bicyclecards": "velos<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "eikers", "thegwent": "gwent", "legendofrunetera": "runetera_leģenda", "solitaire": "p<PERSON><PERSON><PERSON>", "poker": "pokeris", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "šafkopfs", "keyforge": "keyforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netrunner": "datorhaķeris", "gwent": "gvents", "metazoo": "<PERSON>d<PERSON><PERSON><PERSON>", "tradingcards": "t<PERSON>d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "poke<PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sportscards": "sporta_kartinas", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "️", "warcry": "kaujassauciens", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "triks", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "pretestība", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON>ug<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "jugioduels", "yugiohocg": "yugiohocg", "dueldisk": "dueldisks", "yugiohgame": "yug<PERSON>hspē<PERSON>", "darkmagician": "tumšaismaģis", "blueeyeswhitedragon": "zilasacisbaltaispūķis", "yugiohgoat": "yugiohlegenda", "briscas": "briska", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "burako", "rummy": "<PERSON><PERSON><PERSON><PERSON>", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgcommander": "mtgkommanders", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "duelists", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carteado": "izkārtots", "sueca": "sueca", "beloteonline": "be<PERSON><PERSON><PERSON><PERSON>", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "kaujasgariepika", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "žolíky": "žolīks", "facecard": "<PERSON><PERSON><PERSON><PERSON><PERSON>ā<PERSON>", "cardfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "špēļukomplektuveidotāji", "marvelchampions": "<PERSON><PERSON><PERSON><PERSON>", "magiccartas": "maģiskāskārtis", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unstableunicorns": "nest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberse": "kiberse", "classicarcadegames": "klasiskāsarkādesspēles", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "piektdienesvakarakaifojam", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projektsnā<PERSON><PERSON>", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "ģitārvaronis", "clonehero": "clonehero", "justdance": "<PERSON><PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "roksmits", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "dejucentrs", "rhythmgamer": "rit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "augstirekordiritmasspē<PERSON>", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "r<PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "ugunsledusdeja", "auditiononline": "konkursstiešsaistē", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "kriptanomirisiemdejotajs", "rhythmdoctor": "ritmadakt<PERSON>s", "cubing": "kubikošana", "wordle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teniz": "teniz", "puzzlegames": "sp<PERSON><PERSON><PERSON><PERSON>s", "spotit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rummikub": "<PERSON><PERSON><PERSON><PERSON>", "blockdoku": "blokdoku", "logicpuzzles": "loģikasmīklas", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikscube": "<PERSON><PERSON><PERSON><PERSON>", "crossword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motscroisés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "krzyżówki": "krustv<PERSON>rd<PERSON>_mīklas", "nonogram": "nenogramma", "bookworm": "gr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "puzles", "indovinello": "<PERSON><PERSON><PERSON>", "riddle": "<PERSON><PERSON><PERSON>", "riddles": "<PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "puzle", "tekateki": "<PERSON><PERSON><PERSON>", "inside": "i<PERSON><PERSON><PERSON>", "angrybirds": "dusmīgieputni", "escapesimulator": "bēgšanassimulators", "minesweeper": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzleanddragons": "puzleunpūķi", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesspēle", "puzzlesport": "puzlessports", "escaperoomgames": "eva<PERSON>ā<PERSON>jasistabaspēles", "escapegame": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "3dpuzle", "homescapesgame": "homescapesspēle", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "<PERSON><PERSON><PERSON><PERSON>", "kulaworld": "kulapasaule", "myst": "noslēpums", "riddletales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "fishdom", "theimpossiblequiz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "match3puzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikcube": "<PERSON><PERSON><PERSON><PERSON>", "cuborubik": "<PERSON><PERSON><PERSON><PERSON>", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "putputts", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON>", "tycoongames": "magn<PERSON><PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON>", "cruciverba": "cruciver<PERSON>", "ciphers": "<PERSON><PERSON><PERSON>", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "nevieni", "guessing": "<PERSON><PERSON><PERSON><PERSON>", "nonograms": "nonogrammas", "kostkirubika": "kostkirubika", "crypticcrosswords": "mīklaini<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia2": "sibīrija2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "kaķunoziegums", "quebracabeça": "puzle", "hlavolamy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poptropica": "<PERSON><PERSON>pi<PERSON>", "thelastcampfire": "pē<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "picopark": "picoparks", "wandersong": "ceļ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "netitulētaspēle", "cassetête": "gal<PERSON><PERSON>ā<PERSON>", "limbo": "limbs", "rubiks": "rubiks", "maze": "labirints", "tinykin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikovakostka": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "speedcube": "sp<PERSON><PERSON><PERSON><PERSON>", "pieces": "gabali", "portalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bilmece": "<PERSON><PERSON><PERSON>", "puzzelen": "puzzelen", "picross": "pik<PERSON>", "rubixcube": "<PERSON><PERSON><PERSON><PERSON>", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "kubsrubiks", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopoly": "monopols", "futurefight": "nākotnesbattle", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "vientuļaisvilks", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ansambļazvaigznes", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cepumuskrēju<PERSON>", "alchemystars": "alchemystars", "stateofsurvival": "izdzīvošanasstāvoklis", "mycity": "manapils<PERSON>ta", "arknights": "arknights", "colorfulstage": "krāsainā_skatuve", "bloonstowerdefense": "bloonscietokšņaaizsardzība", "btd": "ddb", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "<PERSON><PERSON><PERSON>e", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "futbolakaujas", "a3": "a3", "phonegames": "telefonaspēles", "kingschoice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "degvielas_galva", "tacticool": "taktiskrutāks", "cookierun": "cookierun", "pixeldungeon": "pikseļucietums", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "craftsman": "amatnieks", "supersus": "megaaizdomīgs", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "<PERSON><PERSON><PERSON><PERSON>", "wordfeud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bedwars": "gultukaŗi", "freefire": "freefire", "mobilegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "lilijasdarrzs", "farmville2": "farmville2", "animalcrossing": "dzīvnieku<PERSON><PERSON><PERSON><PERSON>", "bgmi": "bgmi", "teamfighttactics": "komandascīņastaktika", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mistiķaziņneši", "callofdutymobile": "callofdutymobile", "thearcana": "<PERSON><PERSON><PERSON><PERSON>", "8ballpool": "8ballpool", "emergencyhq": "ārkārtassituācijašķ", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albions", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "kratīšanasuņķerīšanās", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclanspēle", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "laika_princese", "beatstar": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disneja_spoguļpasaule", "pocketlove": "kabatasmīlestī<PERSON>", "androidgames": "and<PERSON><PERSON><PERSON><PERSON>", "criminalcase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "gatavošanastrakums", "dokkan": "dokkan", "aov": "aov", "triviacrack": "prā<PERSON><PERSON><PERSON><PERSON>", "leagueofangels": "eņģeļulīga", "lordsmobile": "lordsmobile", "tinybirdgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "spoguļverse", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "antiyoy": "pretīgums", "apexlegendmobile": "apexlegendmobile", "ingress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slugitout": "cīnieslīdzgalam", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON><PERSON><PERSON>", "petpals": "dzīvniekudraugi", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfy": "vil<PERSON><PERSON><PERSON>", "runcitygame": "skriešanaspilsētasspēle", "juegodemovil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "amerikāņukalniadījukonstruktors", "grandchase": "grandchase", "bombmebrasil": "uzspridzinimanibrazilijā", "ldoe": "ldoe", "legendonline": "leģendatiešsaistē", "otomegame": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "pūķuaicinājums", "shiningnikki": "spīdošānikija", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "ceļšuznekurieni", "sealm": "sealm", "shadowfight3": "ēnucīņa3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolīcijaskauja3", "wordswithfriends2": "vārdiariddraugiem2", "soulknight": "d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "purrfektāpasak<PERSON>", "showbyrock": "showbyrock", "ladypopular": "pop<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "lolmob<PERSON>", "harvesttown": "ražaspilsēta", "perfectworldmobile": "perfektāpasaulemobilā", "empiresandpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "empirespuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "dragonucity", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "pupa", "littlenightmare": "mazaismurgs", "aethergazer": "aethergazer", "mudrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "m<PERSON>žīgād<PERSON><PERSON><PERSON><PERSON>", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombijubēgļi", "eveechoes": "eveechoes", "jogocelular": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "anrīnoslē<PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "meitenešupriekšlīnija", "jurassicworldalive": "jurassicworldalive", "soulseeker": "dv<PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "mēness<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "carxdriftracing2", "jogosmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofneverland": "leģendaparnekadzemē", "pubglite": "pubglite", "gamemobilelegends": "spēlesmobilāsleģendas", "timeraiders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thebattlecats": "kaķukauja", "dnd": "nt", "quest": "kvests", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofdarkness": "tumsaspasaule", "travellerttrpg": "ceļotājsttrpg", "2300ad": "2300mg", "larp": "larp", "romanceclub": "romantikasklubs", "d20": "d20", "pokemongames": "poke<PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonred", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "pokemonmednie<PERSON>", "lipeep": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hip<PERSON><PERSON>", "empoleon": "empoleons", "arceus": "arceus", "mewtwo": "mj<PERSON><PERSON><PERSON>", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON><PERSON>", "chatot": "čatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpurs", "ashketchum": "ešskečums", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furets", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlaks", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonp<PERSON><PERSON><PERSON>u", "teamystic": "komandamistika", "pokeball": "pokébumba", "charmander": "č<PERSON><PERSON>", "pokemonromhack": "pokemon<PERSON><PERSON>s", "pubgmobile": "pubgmobile", "litten": "i<PERSON><PERSON><PERSON><PERSON>s", "shinypokemon": "spī<PERSON><PERSON>gipokemoni", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "umbreon": "umbreons", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "pokemonmiegs", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokemonameistars", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaurs", "lucario": "lucario", "charizar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinyhunter": "sp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "<PERSON><PERSON>s", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON>s", "scacchi": "<PERSON><PERSON>s", "schaken": "<PERSON><PERSON>s", "skak": "skak", "ajedres": "<PERSON><PERSON>s", "chessgirls": "šahameitas", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "pasa<PERSON><PERSON><PERSON>s", "jeudéchecs": "<PERSON><PERSON>s", "japanesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chinesechess": "ķīnašahs", "chesscanada": "šahskanāda", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "vakances", "rook": "tornis", "chesscom": "chesscom", "calabozosydragones": "cietumiundūķi", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "pažzemjumeistars", "tiamat": "tiamat", "donjonsetdragons": "pazemiešiunpūķi", "oxventure": "oxventure", "darksun": "tum<PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "voksmašīnasleģenda", "doungenoanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmoor": "t<PERSON><PERSON><PERSON>purv<PERSON>", "minecraftchampionship": "minecraftčempionāts", "minecrafthive": "minecraftstrops", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodi", "mcc": "mcc", "candleflame": "s<PERSON><PERSON>lies<PERSON>", "fru": "fru", "addons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "modētaisminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftdators", "betweenlands": "starpzemēm", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftpilsēta", "pcgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvideo": "<PERSON><PERSON><PERSON><PERSON>", "gambit": "gam<PERSON><PERSON><PERSON>", "gamers": "g<PERSON><PERSON>", "levelup": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermobile": "spēlētājumobilais", "gameover": "spēlebei<PERSON><PERSON>", "gg": "gg", "pcgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamen": "spēlējam", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "casualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "sp<PERSON>ļ<PERSON>ekā<PERSON>", "pcmasterrace": "pcmeistargaita", "pcgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerboy": "gamerboi", "vrgaming": "v<PERSON><PERSON><PERSON><PERSON>", "drdisrespect": "drdisrespect", "4kgaming": "4kgeimingi", "gamerbr": "geimerubr", "gameplays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "konsolesp<PERSON><PERSON><PERSON><PERSON>ā<PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "episkiegejmeri", "onlinegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "semigamer": "puspadsmitnieks", "gamergirls": "geimerimeitenes", "gamermoms": "geim<PERSON><PERSON><PERSON><PERSON>", "gamerguy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamewatcher": "sp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "spe<PERSON><PERSON><PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "geimeritesmeitenes", "otoge": "otome", "dedsafio": "dedsafio", "teamtryhard": "koman<PERSON><PERSON><PERSON>ša<PERSON>", "mallugaming": "mallugaming", "pawgers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quests": "<PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "veca<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamelpay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dsswitch": "dv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "competitivegaming": "konkurētspējīgā<PERSON>ēle", "minecraftnewjersey": "minecraftnewjersey", "faker": "vilt<PERSON><PERSON><PERSON>s", "pc4gamers": "pc4geimeriem", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "hetero<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "spēļudators", "girlsgamer": "meitenugeimerēm", "fnfmods": "fnfmodi", "dailyquest": "ikdienasuzdevums", "gamegirl": "spēļumeitene", "chicasgamer": "geimerumeitenes", "gamesetup": "sp<PERSON><PERSON>iest<PERSON><PERSON><PERSON><PERSON>", "overpowered": "pārspeķ", "socialgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "sp<PERSON><PERSON><PERSON><PERSON><PERSON>", "proplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "manakoman<PERSON>", "republicofgamers": "geimeruvaldstība", "aorus": "aorus", "cougargaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "triplelegend": "trīskāršaisleģenda", "gamerbuddies": "gamerbiedri", "butuhcewekgamers": "vajaggeimerimeitenes", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "afk": "nlb", "andregamer": "andregamer", "casualgamer": "gad<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gemers": "<PERSON><PERSON><PERSON><PERSON>", "oyunizlemek": "oyunizlemek", "gamertag": "spelejatagsme", "lanparty": "lant<PERSON><PERSON><PERSON><PERSON>", "videogamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "ve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "protogen": "protogēns", "womangamer": "gamersieviete", "obviouslyimagamer": "protam<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "mario": "mario", "papermario": "papermario", "mariogolf": "mario<PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "humanfallflat": "cilvēkskrī<PERSON>plakans", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "null<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomūzika", "sonicthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sonic": "soniks", "fallguys": "fallguys", "switch": "<PERSON><PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "zeldaleģenda", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amiibo": "amiibo", "megaman": "megamens", "majorasmask": "<PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "mariokartsmeistars", "wii": "wii", "aceattorney": "ace_attorney", "ssbm": "ssbm", "skychildrenofthelight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "draugadzīve", "ahatintime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "karalistesvaldstībasasaras", "walkingsimulators": "s<PERSON><PERSON><PERSON><PERSON>nassi<PERSON><PERSON><PERSON>", "nintendogames": "ninte<PERSON><PERSON><PERSON><PERSON>", "thelegendofzelda": "zeldaleģenda", "dragonquest": "dragonquest", "harvestmoon": "rudensmēness", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "bandžokazūijs", "celeste": "celeste", "breathofthewild": "mežaelpa", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "zeldaleģendas", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "51games": "51<PERSON><PERSON><PERSON>", "earthbound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tales": "st<PERSON><PERSON>i", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "ninte<PERSON><PERSON><PERSON><PERSON>", "tloz": "tloz", "trianglestrategy": "trijstūrastratēģija", "supermariomaker": "supermariomei<PERSON>s", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "čaulīšusliktāspalvupiektdiena", "nintendos": "nintendos", "new3ds": "jaunais3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "zelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "sarkaniesplē<PERSON><PERSON><PERSON>", "vanillalol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspānija", "aatrox": "aatrox", "euw": "jū", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "keila", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "mēnessmeitene", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "mil<PERSON><PERSON>", "shaco": "shaco", "ligadaslegendas": "līgasleģendas", "gaminglol": "geimingrž", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hextech": "hekstehnoloģija", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideospēles", "scaryvideogames": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "videospēļ<PERSON>ido<PERSON>ā<PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "<PERSON><PERSON><PERSON><PERSON>", "videosgame": "<PERSON><PERSON><PERSON><PERSON>", "professorlayton": "profesorsleitons", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "pūkain<PERSON>gie_draugi", "farmingsimulator": "lauksaimniecībassimulators", "robloxchile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxvācija", "robloxdeutsch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "sanboxspēles", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "skrituļdrome", "parasiteeve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "tumšmežs", "dreamscape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulators", "grandtheftauto": "grandtheftauto", "deadspace": "mirus<PERSON><PERSON><PERSON>", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "<PERSON><PERSON><PERSON><PERSON>", "theoldrepublic": "vecārepublika", "videospiele": "<PERSON><PERSON><PERSON><PERSON>", "touhouproject": "touhouproject", "dreamcast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventuregames": "piedzīvojumus<PERSON><PERSON>", "wolfenstein": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "actionadventure": "darbībaspiedzīvojums", "storyofseasons": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "retro<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "retroarkāde", "vintagecomputing": "vintāžadatori", "retrogaming": "retrogameošana", "vintagegaming": "retrogā<PERSON>s", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "netaisnība2", "shadowthehedgehog": "<PERSON><PERSON><PERSON><PERSON>", "rayman": "reimans", "skygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "dzenmiers", "beatmaniaiidx": "beatmaniaiidx", "steep": "stāvs", "mystgames": "noslē<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "blok<PERSON><PERSON><PERSON>_sp<PERSON>les", "medievil": "vid<PERSON><PERSON><PERSON>", "consolegaming": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolen", "outrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "paniskaziedēšana", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "monstrmeitenesmeklējumi", "supergiant": "supermilzis", "disneydreamlightvalle": "disnijassapņugaismasieleja", "farmingsims": "lauksaimnie<PERSON>ībasspē<PERSON>", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxspēles", "interactivefiction": "interaktīvāfikcija", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "mīļākaismīļākiem", "visualnovel": "vizuālānovele", "visualnovels": "viz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "ēnuvilks", "tcrghost": "tcrghost", "payday": "algasdiena", "chatherine": "<PERSON><PERSON><PERSON><PERSON>", "twilightprincess": "krēslasprincesei", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "viz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thecrew2": "komanda2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retro_spēle", "tonyhawkproskater": "tonyhawksp<PERSON><PERSON><PERSON><PERSON><PERSON>", "smbz": "smbz", "lamento": "nožēloju", "godhand": "dievišķāroka", "leafblowerrevolution": "lapupūtējrevolūcija", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON>", "starrail": "zvaigžņusliedes", "keyblade": "atslēgašķēps", "aplaguetale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnafsometimes": "fnafdažreiz", "novelasvisuales": "viz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "robloxbrazīlija", "pacman": "pacman", "gameretro": "retro<PERSON><PERSON><PERSON>", "videojuejos": "<PERSON><PERSON><PERSON><PERSON>", "videogamedates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "tāpātvien3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "atgriežasaprēķins", "gamstergaming": "geimerugeimerība", "dayofthetantacle": "tentakļudiena", "maniacmansion": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "crashracing": "sacī<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "3dplatformeri", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "skaņas<PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "pāridivāmdvēselēm", "gameuse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "jaud<PERSON><PERSON><PERSON><PERSON><PERSON>", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "grafikaspiedzīvojumi", "quickflash": "ātruzplaiksnījums", "fzero": "fzero", "gachagaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcades": "retroarkādes", "f123": "f123", "wasteland": "noma<PERSON><PERSON><PERSON>", "powerwashsim": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coralisland": "k<PERSON><PERSON>ļ<PERSON><PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "citapasaule", "metaquest": "metaquest", "animewarrios2": "animecīnītāji2", "footballfusion": "futbolasintēze", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simulators", "symulatory": "simulatori", "speedrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "epicx": "epicx", "superrobottaisen": "superrobotišķi", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skylander": "skylander", "boyfrienddungeon": "puišadraugscietoksnis", "toontownrewritten": "toontownpārrakstīts", "simracing": "sim<PERSON><PERSON>", "simrace": "simreiss", "pvp": "pvp", "urbanchaos": "pils<PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "debesķermeņi", "seum": "seum", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "kapugargs", "spaceflightsimulator": "kosmosalidojumasimulators", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "<PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "ēdiensunvideospē<PERSON>", "oyunvideoları": "oyunvideoları", "thewolfamongus": "vilksmusu<PERSON><PERSON>", "truckingsimulator": "kravasimulators", "horizonworlds": "horizontupasaules", "handygame": "sp<PERSON><PERSON>ītekabatiņā", "leyendasyvideojuegos": "leģendasunvideospēles", "oldschoolvideogames": "vec<PERSON><PERSON><PERSON><PERSON>", "racingsimulator": "sacīkšusimulators", "beemov": "bee<PERSON>v", "agentsofmayhem": "maihemaaģenti", "songpop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "famitsu": "famitsu", "gatesofolympus": "olimpav<PERSON><PERSON><PERSON>", "monsterhunternow": "monstermedniekitagad", "rebelstar": "nemiernieka_zvaigzne", "indievideogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indiegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogame": "indī<PERSON><PERSON><PERSON>", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spaidermensbezmiegs", "bufffortress": "musk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "nepārspēja<PERSON>", "projectl": "projekts", "futureclubgames": "nākotnesklubaspēles", "mugman": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "insomniacgames": "bezmiegalakaspē<PERSON>", "supergiantgames": "supergiantgames", "henrystickman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celeste<PERSON><PERSON><PERSON>", "aperturescience": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backlog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamebacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "sasniegumumernieks", "cityskylines": "pilsē<PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "superpērtiķubumba", "deponia": "deponia", "naughtydog": "blēdīgaissuns", "beastlord": "zvērukungs", "juegosretro": "retrovideospēles", "kentuckyroutezero": "kentakitakaisviensapnis", "oriandtheblindforest": "oriundaklāmežs", "alanwake": "alanwake", "stanleyparable": "stenlīj<PERSON><PERSON>ī<PERSON><PERSON><PERSON><PERSON>", "reservatoriodedopamin": "dopamīnakrātuve", "staxel": "staxel", "videogameost": "videospēļ<PERSON><PERSON><PERSON><PERSON>", "dragonsync": "pūķsinhro", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "mīlukofxv", "arcanum": "<PERSON><PERSON><PERSON><PERSON>", "neoy2k": "jaung2k", "pcracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "berserk": "trakais", "baki": "baki", "sailormoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "skumjaisanime", "darkerthanblack": "melnāksparnakti", "animescaling": "anime<PERSON>ē<PERSON><PERSON><PERSON><PERSON>", "animewithplot": "animearsiuzetu", "pesci": "pesci", "retroanime": "retroanime", "animes": "anime", "supersentai": "superstāsts", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "80gadu_anime", "90sanime": "90gadu_anime", "darklord": "tumšaiskungs", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "v<PERSON><PERSON><PERSON><PERSON>", "2000sanime": "2000anime", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesezona1", "rapanime": "repanime", "chargemanken": "lādētājsaizķēries", "animecover": "animesegums", "thevisionofescaflowne": "escaflownesvīzija", "slayers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tokyomajin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anime90s": "anime90tie", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haik<PERSON><PERSON>", "toiletboundhanakokun": "tualetesaistītaishanak<PERSON>ns", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "<PERSON>gun<PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "moriartijspatriots", "futurediary": "nākotnesdienasgrāmata", "fairytail": "pasaka", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "<PERSON><PERSON><PERSON><PERSON>", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "zvērzvaigznes", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "dievietesmūpiens", "blmanga": "blmanga", "horrormanga": "šausmumangas", "romancemangas": "romantikasmanga", "karneval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonmaid": "pūķameita", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "teraform<PERSON><PERSON><PERSON>", "geniusinc": "ģēnijssia", "shamanking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "d<PERSON><PERSON><PERSON><PERSON><PERSON>", "yurionice": "yurionice", "acertainmagicalindex": "noteiktamaģisksindekss", "sao": "sao", "blackclover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ņš", "tokyoghoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onepunchman": "onesitiens<PERSON><PERSON><PERSON>", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "brīnumolasprioritāte", "angelsofdeath": "nāveseņģeļi", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosismikrofons", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusmeitene", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportsanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "eņģeļusitienu", "isekaianime": "isekaianime", "sagaoftanyatheevil": "<PERSON><PERSON><PERSON>", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "puikaunzvērs", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "<PERSON><PERSON><PERSON><PERSON>", "blackbuttler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "<PERSON><PERSON><PERSON><PERSON>", "elfenlied": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "<PERSON><PERSON>", "servamp": "servamp", "howtokeepamummy": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "pilnmēnessmeklējumi", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "cīņasmāksluvir<PERSON>ne", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "hiscoreveiden<PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "š<PERSON>ž<PERSON>", "zerotwo": "nullesdivi", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstrmeitene", "kanae": "kanae", "yone": "visi<PERSON><PERSON><PERSON>", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "<PERSON><PERSON><PERSON>", "vegeta": "vegeta", "goromi": "goromi", "luci": "lu<PERSON><PERSON>", "reigen": "reigen", "scaramouche": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amiti": "amiti", "sailorsaturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dio": "dio", "sailorpluto": "jūrnieksplū<PERSON>", "aloy": "aloy", "runa": "runa", "oldanime": "ve<PERSON><PERSON><PERSON><PERSON>", "chainsawman": "čeinsoumens", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "kaķausis", "inumimi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokijoatriebē<PERSON>", "blackbutler": "meln<PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "claymore": "klaivs", "loli": "loli", "horroranime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "velnaviracenesbernins", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "mīlestībaužīvo", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "solītānekadiedzeme", "monstermanga": "briesmumanga", "yourlieinapril": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buggytheclown": "kļūdainaisklauns", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "kiborgs009", "magi": "magi", "deepseaprisoner": "dziļjūrascietumnieks", "jojolion": "jojo<PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "banān<PERSON><PERSON><PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "velnalinija", "toyoureternity": "tevīmū<PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "zilaisperiods", "griffithberserk": "grifitsbersērks", "shinigami": "<PERSON><PERSON><PERSON>", "secretalliance": "sle<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ī<PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "izdzēsts", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "dete<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiki": "šiki", "deku": "de<PERSON><PERSON>", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "zilaiseksorcists", "slamdunk": "triecienmetiens", "zatchbell": "zakuberulv", "mashle": "mashle", "scryed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyfamily": "spieguģimene", "airgear": "gaisa_rati", "magicalgirl": "maģiskāmeitene", "thesevendeadlysins": "septiņināvniecīgiegrēki", "prisonschool": "cietumskoola", "thegodofhighschool": "augstsk<PERSON><PERSON><PERSON><PERSON>", "kissxsis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandblue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animepasaule", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridžēts", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpsiho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "nedzīvojošieneveiksminieki", "romancemanga": "romantikasmanga", "blmanhwa": "b<PERSON><PERSON><PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animemīlestība", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animearģentīna", "lolicon": "lolicons", "demonslayertothesword": "dēmonumednieksuzzoben", "bloodlad": "asinskungs", "goodbyeeri": "at<PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adioseri": "atvadier<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "zvaigzness<PERSON><PERSON><PERSON><PERSON>", "romanceanime": "romantiskaisanime", "tsundere": "cundere", "yandere": "jandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactors", "cherrymagic": "ķiršumaģija", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "ierakstapokalipse", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vind<PERSON>sāga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prin<PERSON><PERSON><PERSON><PERSON>", "tonikawa": "rupajksmiekligi", "esdeath": "esdeath", "dokurachan": "dokuračans", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "slepkavasklase", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japāņuanime", "animespace": "animutel<PERSON>", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "c<PERSON><PERSON><PERSON><PERSON>", "animedub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "našķi", "gundam0": "gundam0", "animescifi": "animezinātnesfantastika", "ratman": "žurkacilvēks", "haremanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kochikame": "kochikame", "nekoboy": "kaķupuika", "gashbell": "ga<PERSON><PERSON><PERSON>", "peachgirl": "persiķmeitene", "cavalieridellozodiaco": "zodiakabruninieki", "mechamusume": "<PERSON><PERSON><PERSON><PERSON>", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "sirdsmanga", "deliciousindungeon": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "vīriešuvīrieš<PERSON>jaoi", "recordofragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funamusea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "pārlēktuzīkšķeri", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "pam<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "reivmeistars", "kkondae": "ve<PERSON><PERSON><PERSON><PERSON>", "chobits": "chobits", "witchhatatelier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamen": "kamen", "mangaislife": "mangairsdzīve", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "zodiakabruninieki", "animeshojo": "animemeitene", "reverseharem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "izcilsskoloājsonizuka", "gridman": "gridmans", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "piektaispārslēgs", "grandbluedreaming": "grandbluedreaming", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodc": "asinsgrupa", "talesofdemonsandgods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "goreanime", "animegirls": "animemeitenes", "sharingan": "šaringans", "crowsxworst": "vā<PERSON><PERSON>bri<PERSON><PERSON>īg<PERSON><PERSON>", "splatteranime": "šķīdumānime", "splatter": "<PERSON><PERSON><PERSON><PERSON>", "risingoftheshieldhero": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "so<PERSON><PERSON><PERSON><PERSON><PERSON>", "riodejaneiroanime": "riodežaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "<PERSON><PERSON><PERSON>", "animeespaña": "animespānija", "animeciudadreal": "animepilsētar<PERSON>āla", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "v<PERSON>š<PERSON><PERSON><PERSON><PERSON>", "liarliar": "<PERSON><PERSON><PERSON><PERSON>", "supercampeones": "superčempioni", "animeidols": "<PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiarbiedurrakstu", "midorinohibi": "zaļāsdienas", "magicalgirls": "maģiskāsmeitenes", "callofthenight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "mangameklētājs", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradīzessk<PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON><PERSON>", "revuestarlight": "zvaigžņugaisma", "animeverse": "animepasaule", "persocoms": "<PERSON><PERSON><PERSON>_datori", "omniscientreadersview": "viszinošālasītājaskatījums", "animecat": "animekakis", "animerecommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openinganime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "manspusaudžuromantiskākomēdija", "evangelion": "evangelion", "gundam": "gundam", "macross": "makross", "gundams": "gundami", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "kōdsgeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "<PERSON><PERSON><PERSON><PERSON>", "eurekaseven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathnote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "kaubojs_bebops", "jjba": "jjba", "jojosbizarreadventure": "džodžobizaraispiedzīvojums", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "džodžobizariepiedzīvojumi", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "milit<PERSON><PERSON><PERSON><PERSON><PERSON>", "greenranger": "zaļaisreindžers", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "zvaigžņulapsa", "ultraman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "salondelmanga": "salondelmanga", "lupinthe3rd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecity": "animepilsēta", "animetamil": "animetamilu", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "viensgabals", "animeonepiece": "animeviensgabals", "dbz": "dbz", "dragonball": "<PERSON><PERSON><PERSON><PERSON>", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonapiedzīvojums", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broulis", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "dēmonumednieks", "shonenjump": "shonenjump", "otaka": "otaku", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "dēmonumednieks", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "uzbrukumstitaniem", "erenyeager": "erenyeager", "myheroacademia": "manavaroniakadēmija", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onepieceanime": "onepieceanime", "attaquedestitans": "uzbrukumstītaniem", "theonepieceisreal": "theonepieceisreal", "revengers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "mobpsiho", "aonoexorcist": "zilāeksorcists", "joyboyeffect": "priekazeenefekts", "digimonstory": "digimonst<PERSON>sts", "digimontamers": "dig<PERSON><PERSON><PERSON><PERSON>", "superjail": "supercietums", "metalocalypse": "metaloapokalipse", "shinchan": "<PERSON><PERSON><PERSON><PERSON>", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "nevainojamskomikss", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "lidojošāragana", "wotakoi": "animufans", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}