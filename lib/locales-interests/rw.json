{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "inyenyeri", "cognitivefunctions": "imikororeyubwenge", "psychology": "psychologiya", "philosophy": "filozofiya", "history": "am<PERSON><PERSON>", "physics": "ubugenge", "science": "siyanse", "culture": "umuco", "languages": "indimi", "technology": "ikoranabuhanga", "memes": "meme", "mbtimemes": "mbtimemes", "astrologymemes": "memezastrolojiya", "enneagrammemes": "memezaenneagram", "showerthoughts": "ibitekerezobyoimvura", "funny": "usetsa", "videos": "<PERSON><PERSON><PERSON><PERSON>", "gadgets": "<PERSON><PERSON><PERSON>", "politics": "politiki", "relationshipadvice": "umugambi_w_urukundo", "lifeadvice": "inamaguru_yubuzima", "crypto": "crypto", "news": "amakuru", "worldnews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "learning": "kwiga", "debates": "impaka", "conspiracytheories": "inyangamugayozibihumbi", "universe": "igitenge", "meditation": "<PERSON><PERSON>_umuti<PERSON>", "mythology": "<PERSON><PERSON>i", "art": "<PERSON><PERSON><PERSON><PERSON>", "crafts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dance": "byina", "design": "design", "makeup": "isura", "beauty": "ub<PERSON>za", "fashion": "imideli", "singing": "um<PERSON><PERSON>", "writing": "kwandika", "photography": "ifoto", "cosplay": "cosplay", "painting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drawing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "books": "ibitabo", "movies": "<PERSON><PERSON><PERSON><PERSON>", "poetry": "<PERSON><PERSON><PERSON><PERSON>", "television": "televiziyo", "filmmaking": "gukora_filimi", "animation": "animasiyo", "anime": "anime", "scifi": "ubugenge", "fantasy": "fantasy", "documentaries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mystery": "amayobera", "comedy": "<PERSON><PERSON><PERSON><PERSON>", "crime": "<PERSON><PERSON><PERSON><PERSON>", "drama": "idrama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "umutekano", "romance": "urukundo", "realitytv": "terevizionyukuri", "action": "ibikorwa", "music": "muzika", "blues": "<PERSON><PERSON><PERSON><PERSON>", "classical": "klasika", "country": "<PERSON><PERSON><PERSON><PERSON>", "desi": "desi", "edm": "edm", "electronic": "eleg<PERSON><PERSON><PERSON>", "folk": "abaturage", "funk": "funk", "hiphop": "hiphop", "house": "<PERSON><PERSON><PERSON>", "indie": "indie", "jazz": "jazi", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "rege", "rock": "<PERSON><PERSON><PERSON><PERSON>", "techno": "techno", "travel": "urugendo", "concerts": "ibitaramo", "festivals": "<PERSON><PERSON><PERSON>", "museums": "am<PERSON><PERSON>", "standup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theater": "<PERSON><PERSON><PERSON>", "outdoors": "hanze", "gardening": "guh<PERSON>", "partying": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaming": "imikino", "boardgames": "imikino_y_akarari", "dungeonsanddragons": "dungeonsanddragons", "chess": "umukino_wecheki", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "<PERSON><PERSON><PERSON>", "baking": "guteka", "cooking": "gateka", "vegetarian": "indyamunyanya", "vegan": "imfunguro_yibimera", "birds": "in<PERSON>i", "cats": "umwana_w_i<PERSON>i", "dogs": "imbwa", "fish": "ifi", "animals": "inyamaswa", "blacklivesmatter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "environmentalism": "kubungabungaibidukikije", "feminism": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "humanrights": "uburenganzirebwamuntu", "lgbtqally": "inshutilgbtq", "stopasianhate": "<PERSON><PERSON><PERSON>urwanyeabanyaaziya", "transally": "inshutitransz", "volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sports": "imikino", "badminton": "badminton", "baseball": "baseball", "basketball": "umup<PERSON>_wagatebo", "boxing": "gukina_amanota", "cricket": "cricket", "cycling": "gusiganwa", "fitness": "<PERSON><PERSON><PERSON><PERSON>", "football": "<PERSON><PERSON><PERSON>", "golf": "golf", "gym": "siporo", "gymnastics": "imikino_yo_kuzunguruka", "hockey": "umukino_wurubura", "martialarts": "imikino_y_intambara", "netball": "<PERSON><PERSON><PERSON>_wintoki", "pilates": "pilates", "pingpong": "pingpong", "running": "<PERSON><PERSON><PERSON><PERSON>", "skateboarding": "gusiganwa_skateboard", "skiing": "gusiganwa", "snowboarding": "snowboarding", "surfing": "gusur<PERSON>", "swimming": "igwi<PERSON><PERSON>", "tennis": "umukino_wa_tennis", "volleyball": "volleyball", "weightlifting": "kurambarirauburemere", "yoga": "yoga", "scubadiving": "kwogarumuvuye", "hiking": "amate<PERSON><PERSON>", "capricorn": "isimba", "aquarius": "aquarius", "pisces": "pisces", "aries": "in<PERSON>i", "taurus": "taurus", "gemini": "gemini", "cancer": "ka<PERSON><PERSON>", "leo": "leo", "virgo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "libra": "<PERSON><PERSON><PERSON><PERSON>", "scorpio": "iskorpiyo", "sagittarius": "nyamuziga", "shortterm": "i<PERSON>ih<PERSON>", "casual": "bisanzwe", "longtermrelationship": "urukundo_rwigihe_kirekire", "single": "umuntuuta<PERSON>tse", "polyamory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "<PERSON><PERSON><PERSON><PERSON>", "lesbian": "abakobwakunda", "bisexual": "bi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pansexual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "ubutumwabutagaragara", "legendofspyro": "umugandiwas<PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "siberiya", "rdr2": "rdr2", "spyrothedragon": "spyroikiyoka", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON>rutsindagira", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "umusare", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "intambarayubushake", "openworld": "is<PERSON>ba<PERSON><PERSON>", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "nkimyoroboro", "dungeoncrawling": "dungeoncrawling", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "indege", "lordsoftherealm2": "abategetsibyisi2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "<PERSON><PERSON><PERSON>", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealmrw2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "imisi<PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "juegoderol", "witcher": "witcher", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "umutimangavu", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "g<PERSON><PERSON>byangiritse", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modif<PERSON><PERSON>", "charactercreation": "gukora_umuntu", "immersive": "kwinjiramo", "falloutnewvegas": "igiterurayenewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyishaje", "ffvii": "ffvii", "ff6": "abakurikira6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "imbarambaragufakubeshyakwihangana", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "ishywanabauburenganzira", "otomegames": "imikino_y_urukundo", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinayyigihe", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "umwanyarw20", "gaslands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder": "inzirarangiza", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "gusiganwa", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "ukundarwanda", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpgmaker": "gukora_rpg", "osrs": "osrs", "overlord": "umugaba", "yourturntodie": "nibaweiwawe", "persona3": "persona3", "rpghorror": "rpgit<PERSON>mbere", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "umukinorpg", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "inzirazizirabura", "eclipsephase": "igiheicyumwijikiswe", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "guhumanizaizaka", "diabloimmortal": "diabloimmortal", "dynastywarriors": "a<PERSON><PERSON><PERSON>ing<PERSON>", "skullgirls": "skullgirls", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "intambarayuburakare", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "inzira96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "imikino_ya_roguelike", "gothamknights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "ibangaby<PERSON><PERSON><PERSON><PERSON>", "dragonlance": "dragonlance", "arenaofvalor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "isimikubwenge", "monsterrancher": "imbagaizimisimbi", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "a<PERSON><PERSON><PERSON><PERSON>", "vulcanverse": "vulcanverse", "fracturedthrones": "ing<PERSON>_zime<PERSON>e", "horizonforbiddenwest": "imihangireyakuwenyabwo", "twewy": "twewy", "shadowpunk": "umwijimapunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "deltaicyatsi", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "g<PERSON><PERSON><PERSON>", "lastepoch": "i<PERSON><PERSON><PERSON><PERSON>", "starfinder": "inyenyeri_zishakisha", "goldensun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "igicuku2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "ubuyanjabyubugome", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "umudimonyasigaye", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "imana", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "ubutembereribusesenguzi", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "imiki<PERSON>_yo_gukina_uruhare", "roleplayinggames": "imiki<PERSON>_yo_gukina_uruhare", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "inkurusymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfarog": "myfarog", "sacredunderworld": "isiimperturbateuse", "chainedechoes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darksoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "nkizaby<PERSON><PERSON><PERSON>", "othercide": "ubu<PERSON><PERSON><PERSON><PERSON>", "mountandblade": "i<PERSON><PERSON><PERSON><PERSON>cyamafar<PERSON>", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pillarsofeternity": "inkingizyuburambe", "palladiumrpg": "palladiumrpg", "rifts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tibia": "igufa_ryakaguru", "thedivision": "umugabane", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "umugankowadragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirewigisigarwahishwa", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werewolfapocalypse", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "a<PERSON><PERSON><PERSON><PERSON>", "engineheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable3": "fable3", "fablethelostchapter": "igitabogizimye", "hiveswap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "inyenyeri", "oldschoolrevival": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "isikupfunyeteye", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "imikino_ya_rpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "umutimawubwami3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harvestella": "<PERSON><PERSON><PERSON><PERSON>", "gloomhaven": "intangamvugo", "wildhearts": "imiti<PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "ikin<PERSON>_giko<PERSON>ye", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "ikirerecyarcadia", "shadowhearts": "imitimayadukije", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "umukaingoye4", "mother3": "amabyeyi3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "ikindiikindi", "roleplaygames": "imikino_yo_kwikinisha", "roleplaygame": "igikino_cy_uruhare", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON>uti<PERSON>wu<PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirizabwihishwa", "dračák": "<PERSON><PERSON><PERSON>", "spelljammer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "huntroyale": "guhigairoyale", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>s<PERSON>", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "imbugarpg", "shadowheartscovenant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "awplanet": "<PERSON><PERSON><PERSON>", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragaliabyatakaye", "elderscroll": "elderscroll", "dyinglight2": "ubumunyidyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "urugambor<PERSON>bi", "shoptitans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "ubuhangazenyabutaka", "blackbook": "igitabonkuruye", "skychildrenoflight": "umukaziikirere", "gryrpg": "<PERSON><PERSON><PERSON>", "sacredgoldedition": "igitabocyumwihariko", "castlecrashers": "abateramubihome", "gothicgame": "umukinnogothic", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "imikino_rpg", "prophunt": "guh<PERSON>_abahanuzi", "starrails": "inziramagereza", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "gandafatakura", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON>ahandinawe", "emilyisaway": "emily<PERSON><PERSON><PERSON><PERSON>", "indivisible": "nti<PERSON><PERSON><PERSON><PERSON>_gutand<PERSON>na", "freeside": "kuru<PERSON>e", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "nyumacyberpunk", "deathroadtocanada": "inziramfuzejeyancanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON>", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "ubutegetsibuhantu", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nier": "nier", "dothack": "korahack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "imikino_idafite_igitsina", "tacticalrpg": "tacticalrpg", "mahoyo": "mahoyo", "animegames": "animegames", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "um<PERSON><PERSON>_imana", "diluc": "diluc", "venti": "venti", "eternalsonata": "indirimboiteka", "princessconnect": "amahorabahanyaneza", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valo<PERSON><PERSON><PERSON><PERSON>", "dota": "dota", "madden": "gusakaza", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "imikino_ya_elegitoronike", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "imikino_ya_elegitoronike", "mlg": "mlg", "leagueofdreamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa14": "fifa14", "midlaner": "umukinnyi_wo_hagati", "efootball": "umup<PERSON>_wama<PERSON>ru_wa_elegitoronike", "dreamhack": "inzozindoto", "gaimin": "gukina", "overwatchleague": "shampiyonaligendeoverwatch", "cybersport": "umuki<PERSON>_kuri_mudasobwa", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "umukinnoriotgames", "eracing": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brasilgameshow": "umukinnoyimikino_brezile", "valorantcompetitive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "ubuzi<PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead": "left4dead", "left4dead2": "gahomborumusozi2", "valve": "umuyoboro", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "umubangourwageza", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "battlefield4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "gukubitanokwica", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "imvuramvura2", "metroidvanias": "metroidvanias", "overcooked": "yatetse_cyane", "interplanetary": "hagar<PERSON><PERSON><PERSON>wenge", "helltaker": "umusatanikwaka", "inscryption": "guhisha", "7d2d": "7munsi2", "deadcells": "insetserupfu", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "umwobo_wimbwebwe", "stray": "<PERSON><PERSON><PERSON>", "battlefield": "intambara", "battlefield1": "intambarayambere1", "swtor": "swtor", "fallout2": "umukino2", "uboat": "ub<PERSON><PERSON>", "eyeb": "amaso", "blackdesert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "imikinyaku<PERSON>zantoya", "partyhard": "twi<PERSON><PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "umenyiimbangukiramajyambere", "hades": "hades", "gunsmith": "umubaji_w_imbunda", "okami": "<PERSON>ami", "trappedwithjester": "gufunzwehamwenajester", "dinkum": "<PERSON><PERSON><PERSON><PERSON>", "predecessor": "uwab<PERSON><PERSON><PERSON>", "rainworld": "isiyisibutaka", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "urugociyakopi", "noita": "noita", "dawnofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minionmasters": "abanyabwenge<PERSON><PERSON>", "grimdawn": "umwijimaurubanza", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "imiki<PERSON>_yo_gukundana", "yaga": "yaga", "cubeescape": "guhumacube", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "imijyindege", "defconheavy": "defconbiremetse", "kenopsia": "kenopsia", "virtualkenopsia": "virtualkenopsia", "snowrunner": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "isomerolyuburiganya", "l4d2": "l4d2", "thenonarygames": "amasibanoanyambere", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placidplasticduck": "igitoyecyiplastikigituje", "battlebit": "intambaragito", "ultimatechickenhorse": "inkokokwan<PERSON>man<PERSON><PERSON>", "dialtown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "nyumvirire", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON>", "supermeatboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinnybunny": "<PERSON><PERSON><PERSON><PERSON>", "cozygrove": "urumananyabukonje", "doom": "agahomamungo", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "igihembwe6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "imip<PERSON>", "pubg": "pubg", "callofdutyzombies": "callofdutyzombiesurugamba", "apex": "urugamba", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farcrygames": "imikino_farcry", "paladins": "paladins", "earthdefenseforce": "abarinziisibahererezo", "huntshowdown": "guhigasohoka", "ghostrecon": "intwarayizimu", "grandtheftauto5": "grandtheftauto5", "warz": "intambara", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "guh<PERSON><PERSON>_byose", "joinsquad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "umwicanyi3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "urup<PERSON><PERSON><PERSON><PERSON>", "b4b": "b4b", "codwarzone": "intambarayakode", "callofdutywarzone": "intambarayacallof<PERSON><PERSON>", "codzombies": "codzombies", "mirrorsedge": "ins<PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "umujyanama2", "killzone": "ahazicanye", "helghan": "hel<PERSON>", "coldwarzombies": "intambarazakizirwa", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "urugamburwimpanvu", "crosscode": "kodeambuka<PERSON><PERSON>", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare": "intambarayikigihe", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boarderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "back4blood": "garuka4maraso", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "<PERSON><PERSON><PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "ihungabana", "valkyriachronicles": "valkyriachronicles", "specopstheline": "umurongo_wa_specops", "killingfloor2": "killingfloor2", "cavestory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doometernal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "intambaragakomeye", "mwo": "mwo", "division2": "icyonzi2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "injiramumisamb<PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "intambarayubukoranjeigihugu2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "uburibaurimbitse", "warface": "intambaragahunda", "crossfire": "<PERSON><PERSON><PERSON><PERSON>", "atomicheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "abacikavampire", "callofdutybatleroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "moorhuhn": "inkokoyumunyegenyege", "freedoom": "ubwi<PERSON><PERSON>", "battlegrounds": "intararugamba", "frag": "frag", "tinytina": "tinatitonitoni", "gamepubg": "umukino_pubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "imikino_ya_fps", "convertstrike": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "warzone2": "warzone2", "shatterline": "umurongo_ucitse", "blackopszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodymess": "akavumo", "republiccommando": "republiccommando", "elitedangerous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soldat": "soldat", "groundbranch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "umutwe", "destiny1": "igitegerejwe1", "gamingfps": "imikino_ya_fps", "redfall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubggirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "yin<PERSON><PERSON>", "farlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "intambaragitangaje", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "umunsiwumushahara2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pub<PERSON>ruk<PERSON>", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostcod": "ghostcod", "csplay": "csplay", "unrealtournament": "imikino_idafite_akabag<PERSON>ro", "callofdutydmz": "callofdutydmz", "gamingcodm": "imikino_codm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "agafindo", "callofdutymw2": "callofdutymw2", "quakechampions": "umukinnyi_wa_quake", "halo3": "halo3", "halo": "halo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "umwerum<PERSON><PERSON>", "remnant": "abasigaye", "azurelane": "azurelane", "worldofwar": "isi_y_intambara", "gunvolt": "gunvolt", "returnal": "gus<PERSON>ra", "halo4": "halo4", "haloreach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "umutingito2", "microvolts": "microvolt", "reddead": "reddead", "standoff2": "imirwano2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "inyanjayabusamb<PERSON>", "rust": "<PERSON><PERSON><PERSON>", "conqueronline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dauntless": "<PERSON><PERSON><PERSON><PERSON>", "warships": "ubwat<PERSON>_b<PERSON><PERSON><PERSON>", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recroom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofruneterra": "imiganibiherabyaruneterra", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantaziyanyamburugastayorinewokuri2", "maidenless": "ntagufite", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "worldoftanks", "crossout": "k<PERSON><PERSON>", "agario": "agario", "secondlife": "ubuzimabwakabiri", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reddeadonline": "urugambaruvyitserufu", "superanimalroyale": "inyamanswakaraje", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gw2": "gw2", "tboi": "tigishyeumutindigushakangwibereho", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newworld": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer": "igi<PERSON><PERSON>", "pirate101": "umuhigi101", "honorofkings": "<PERSON><PERSON><PERSON><PERSON>t<PERSON>", "fivem": "fivem", "starwarsbattlefront": "intambarayestarwars", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "intambarayi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dchat": "ikiganiro3d", "nostale": "ntabishya", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "torakumurongo", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "inzi<PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "spiralknights", "mulegend": "umulegendi", "startrekonline": "startrekkunterambere", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "umupangaweubugingo", "evony": "evony", "dragonsprophet": "umuhanuziwikiyoka", "grymmo": "grymmo", "warmane": "umubindiurugendo", "multijugador": "umuki<PERSON><PERSON>_benshi", "angelsonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseinterineti", "growtopia": "gukura", "starwarsoldrepublic": "starwarsrepublikayakera", "grandfantasia": "ubuzimaburyoshye", "blueprotocol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworld": "isiibaangikwihagaze", "riseonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "corepunk": "corepunk", "adventurequestworlds": "ibihebyurugendogasiiisi", "flyforfun": "guraforamihindire", "animaljam": "<PERSON>git<PERSON><PERSON><PERSON>s<PERSON>", "kingdomofloathing": "igihu<PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "urug<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streetfighter": "umuki<PERSON><PERSON>_wo_mumuhanda", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "urugambomimuhanda6", "multiversus": "multiversus", "smashbrosultimate": "igihangangumukinoesmashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streetsofrage": "imih<PERSON>_y_u<PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mku<PERSON><PERSON>ng<PERSON><PERSON><PERSON>", "nomoreheroes": "ntakindibatwenge", "mhr": "mhr", "mortalkombat12": "imikino12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "imikino_ya_gush<PERSON><PERSON><PERSON>_zaba<PERSON>a", "blasphemous": "kirazira", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "intambararwibisigo", "jogosdeluta": "imikino_ya_luta", "cyberbots": "rob<PERSON><PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "intwarizirindishije", "finalfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poweredgear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "imikino_yo_kurwana", "killerinstinct": "ububasibikeeka", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "ubunyangamugayo2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "umukindiwahollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "silksonggame": "umukino_silksong", "silksongnews": "amakurusilks<PERSON>", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "and<PERSON><PERSON><PERSON>", "evolutiontournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evomoment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "inkuruzyaberse<PERSON>", "bloodborne": "amarasoterwa", "horizon": "urubibe", "pathofexile": "inzirayayihangayihanga", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uncharted": "itazwi", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infamous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playstationbuddies": "abakunzibaplaystationhamwe", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "r<PERSON>zef<PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "umutweroguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detroitbecomehuman": "detroitihindukamunt<PERSON>", "beatsaber": "umucyoumurwanyabitero", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "kugezaubucya", "touristtrophy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "igihangantewakibambasi", "crashteamracing": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "fivepd": "<PERSON><PERSON><PERSON><PERSON>", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "gukina_station", "samuraiwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "umuh<PERSON>_w_um<PERSON>ri", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "gush<PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "umutimawumwijimabiri2isezerano", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "umukinyamutwe", "warharmmer40k": "warhammer40k", "fightnightchampion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhw": "mhw", "princeofpersia": "igikomangango_cya_persia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "<PERSON><PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "intambara", "dontstarvetogether": "ntimu<PERSON>rwehamwe", "ori": "ori", "spelunky": "ubu<PERSON><PERSON>", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "inyenyerizatumye", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable2": "umugani2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "televiz<PERSON><PERSON><PERSON><PERSON><PERSON>", "skycotl": "skycotl", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "agakobwakengana", "sallyface": "<PERSON><PERSON><PERSON><PERSON>", "franbow": "franbow", "monsterprom": "prom_y_igisigo", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "moto", "outerwilds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "igitambcyintama", "duckgame": "umukino_w_imbata", "thestanleyparable": "igitekocastanley", "towerunite": "guhuzaigisenge", "occulto": "<PERSON><PERSON>he", "longdrive": "urugendorwireire", "satisfactory": "kunyura", "pluviophile": "ukundaimvura", "underearth": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "umukinoyageometry", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogramu", "kenshi": "kenshi", "spiritfarer": "umucungacyubwenge", "darkdome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pizzatower": "pizzatower", "indiegame": "umukino_gito", "itchio": "itchio", "golfit": "guf<PERSON><PERSON><PERSON><PERSON>", "truthordare": "ukurindibiharya", "game": "<PERSON><PERSON><PERSON>", "rockpaperscissors": "ubu<PERSON>tasekaratambara", "trampoline": "trampoline", "hulahoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dare": "<PERSON><PERSON><PERSON><PERSON>", "scavengerhunt": "gushakashanganitse", "yardgames": "imikino_yikibuga", "pickanumber": "toraranomero", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "umukino_w_inzoga", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "imikino_ituza", "datinggames": "imikino_yo_kuron<PERSON>a", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "imikinyamukenyezi", "sodoku": "sodoku", "juegos": "juegos", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "imikino_ya_kwi<PERSON>ira", "wordgames": "imikino_y_amagambo", "jeuxdemots": "jeuxdemots", "juegosdepalabras": "amaga<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "tukinnamicyezwa", "boredgames": "imikino_irambagije", "oyun": "oyun", "interactivegames": "imikino_ihinduraduterana", "amtgard": "amtgard", "staringcontests": "kure<PERSON>_uda<PERSON>e", "spiele": "imikino", "giochi": "giochi", "geoguessr": "geoguessr", "iphonegames": "imikino_ya_iphone", "boogames": "boogames", "cranegame": "umukinocrane", "hideandseek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hopscotch": "gusimbag<PERSON><PERSON>", "arcadegames": "imikino_ya_arcade", "yakuzagames": "imikino_ya_yakuza", "classicgame": "umukino_wa_kera", "mindgames": "imikino_y_ubwenge", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "imikino_gala", "romancegame": "urukundo_umukino", "yanderegames": "imikino_ya_yandere", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON>", "4xgames": "imikino4x", "gamefi": "imikino_yamafaranga", "jeuxdarcades": "imikino_ya_arcade", "tabletopgames": "imikino_kumeza", "metroidvania": "metroidvania", "games90": "imikino90", "idareyou": "n<PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "imikino_ya_fumito<PERSON>a", "racinggames": "imikino_y_imodoka", "ets2": "ets2", "realvsfake": "ubukobyanku<PERSON><PERSON>", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "imikino_kumurongo", "onlinegames": "imikino_kurubuga", "jogosonline": "imikino_kumurongo", "writtenroleplay": "umukino_wanditse", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgames": "umukinnyi_hamwe", "jenga": "jenga", "wiigames": "imikinnwayawii", "highscore": "amanota_menshi", "jeuxderôles": "imikino_yimyanya", "burgergames": "imikino_ya_burger", "kidsgames": "imikino_y_abana", "skeeball": "skeeball", "nfsmwblackedition": "nfsmweditionyirabura", "jeuconcour": "imikino", "tcgplayer": "tcgplayer", "juegodepreguntas": "ikibazocyimikino", "gioco": "gioco", "managementgame": "umuki<PERSON>_w_ubu<PERSON><PERSON><PERSON>", "hiddenobjectgame": "<PERSON>uki<PERSON><PERSON><PERSON>a", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "umukino_wa_formula1", "citybuilder": "um<PERSON><PERSON>_w_umujyi", "drdriving": "<PERSON><PERSON>", "juegosarcade": "imikino_ya_arcade", "memorygames": "imikino_yubutarebwenge", "vulkan": "vulkan", "actiongames": "umukino_w_ibikorwa", "blowgames": "imikino_yo_kuvuza", "pinballmachines": "imashinizimupiragumwe", "oldgames": "imikino_ya_kera", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "ibibazo", "gameo": "<PERSON><PERSON><PERSON>", "lasergame": "umukino_wa_laser", "imessagegames": "imibibazoyaimessage", "idlegames": "idlegameszihariye", "fillintheblank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxpc": "imikino_yamudasobwa", "rétrogaming": "imikino_ya_kera", "logicgames": "imikino_yubwenge", "japangame": "umukino_w_ubuyapani", "rizzupgame": "kunozaamakoste", "subwaysurf": "gusimbukamurome<PERSON>", "jeuxdecelebrite": "imikino_y_ibyamamare", "exitgames": "imikino_yo_gus<PERSON>oka", "5vs5": "5ku5", "rolgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "gameandkill", "traditionalgames": "imikino_gakondo", "kniffel": "kniffel", "gamefps": "imikino_fps", "textbasedgames": "imikino_yandikwa", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgame": "<PERSON><PERSON>nn<PERSON><PERSON><PERSON><PERSON>", "lawngames": "imikino_y_ahanze", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "umupiragitwewe", "tischfußball": "tischfußball", "spieleabende": "amajoro<PERSON>_gukina", "jeuxforum": "jeuxforum", "casualgames": "imikino_yoroheje", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "imikino_yo_gutoroka", "thiefgameseries": "igic<PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "imiki<PERSON>_yo_guku<PERSON>_ibihembo", "játék": "<PERSON><PERSON><PERSON>", "bordfodbold": "imipiraykubirab<PERSON>", "jogosorte": "jogosorte", "mage": "mage", "cargames": "imikino_y_imodoka", "onlineplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "imikino_nijoro", "pursebingos": "agasekeibingos", "randomizer": "guh<PERSON><PERSON>_zose", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "imikino_kuri_mudasobwa", "socialdeductiongames": "imikino_yo_gutanduka<PERSON>_abantu", "dominos": "domino", "domino": "domino", "isometricgames": "imikino_isometrike", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "uk<PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "gusaka", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f2pgamer": "f2pgamerrwanda", "free2play": "k<PERSON><PERSON>_amafaranga", "fantasygame": "umukino_wubwenge", "gryonline": "gukinganaiint<PERSON><PERSON><PERSON>", "driftgame": "umukinnyadrift", "gamesotomes": "imikino_y_urukundo", "halotvseriesandgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mushroomoasis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anythingwithanengine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodgamegiving": "umukinnmwi<PERSON>el", "jugamos": "j<PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "imbikinozitagira", "grykomputerowe": "grykomputerowe", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "imikino_ntoya", "ridgeracertype4": "umukinnoyambukaurubuga4", "selflovegaming": "ubwikundigame", "gamemodding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crimegames": "imikino_y_ubugi<PERSON>_bw_<PERSON>aha", "dobbelspellen": "imikino_y_umuhango", "spelletjes": "imikino", "spacenerf": "spacenerf", "charades": "uruk<PERSON><PERSON><PERSON>", "singleplayer": "umuki<PERSON><PERSON>_wenyine", "coopgame": "imikino_twihuriye", "gamed": "n<PERSON><PERSON>", "forzahorizon": "forzahorizon", "nexus": "guh<PERSON>", "geforcenow": "geforcenow", "maingame": "umukinnwi<PERSON><PERSON>", "kingdiscord": "umwamidiscord", "scrabble": "scrabble", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "<PERSON><PERSON><PERSON>", "onitama": "onitama", "pandemiclegacy": "umuragepandemike", "camelup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopolygame": "umukino_wa_monopoly", "brettspiele": "imikino_y_urupapuro", "bordspellen": "imikino_y_urupapuro", "boardgame": "umukino_w_ikibaho", "sällskapspel": "imikino_yabaturanyi", "planszowe": "imikino_yameza", "risiko": "risiko", "permainanpapan": "imikino_y_urupapuro", "zombicide": "zombisid", "tabletop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "um<PERSON><PERSON>_wamaraso", "cluedo": "ubutekamutwe", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectfour": "urusibirane", "heroquest": "urugendorwintwari", "giochidatavolo": "imikino_yameza", "farkle": "farkle", "carrom": "umuzingo", "tablegames": "imikino_yameza", "dicegames": "imikino_ya_dice", "yatzy": "yatzy", "parchis": "parcheesi", "jogodetabuleiro": "umukino_wimpapuro", "jocuridesocietate": "imikino_yabaturage", "deskgames": "imikino_inyandiko", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "guhuranamwubwenge", "creationludique": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "umukinnoyaimezawanditse", "cardboardgames": "imikino_y_ikadiri", "eldritchhorror": "iterabayeubwoba", "switchboardgames": "imikino_ya_switchboard", "infinitythegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "urupfukingoma", "yahtzee": "yahtzee", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegodemesa": "umuki<PERSON>_wo_ku_meza", "planszówki": "imikino_y_urupapuro", "rednecklife": "ubuzimabwicyaro", "boardom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "imikino_yo_kumeza", "gameboard": "imbugakemisoro", "dominó": "domino", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "imikino_yumeza", "twilightimperium": "ubutegetsibumugoroba", "horseopoly": "ifarashipoly", "deckbuilding": "kubakaisange", "mansionsofmadness": "amazu_y_<PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "imikino_yo_ku_meza", "shadowsofbrimstone": "ibihumbibyabrimstone", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcaby": "<PERSON><PERSON><PERSON>", "táblajátékok": "imikino_yameza", "battleship": "ubwat<PERSON>_b<PERSON><PERSON><PERSON>", "tickettoride": "urugendorwimbonerahamwe", "deskovehry": "imikino_ya_kuri_desktop", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "j<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "imikino_yameza", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "imikino", "gesellschaftsspiele": "gesellschaftsspiele", "starwarslegion": "starwarslegion", "gochess": "tuki<PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "imikino_yabaturage", "terraria": "terraria", "dsmp": "dsmp", "warzone": "intambara", "arksurvivalevolved": "arksurvivalevolved", "dayz": "im<PERSON>i", "identityv": "identityv", "theisle": "<PERSON><PERSON><PERSON><PERSON>", "thelastofus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nomanssky": "umukinnomanssky", "subnautica": "subnautica", "tombraider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofcthulhu": "muh<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "muri<PERSON><PERSON>", "eco": "eko", "monkeyisland": "isirwayinguge", "valheim": "valheim", "planetcrafter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "fobiya", "witchit": "<PERSON><PERSON>", "pathologic": "indwara", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "<PERSON><PERSON><PERSON><PERSON>", "thelongdark": "umwijimawurekurekure", "ark": "ark", "grounded": "ntabwo_nsohoka", "stateofdecay2": "umugabowabuze2", "vrising": "vrising", "madfather": "papamuganga", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "inzirangabo", "frictionalgames": "imikino_ituje", "hexen": "<PERSON><PERSON><PERSON><PERSON>", "theevilwithin": "ikibiinkimbere", "realrac": "realrac", "thebackrooms": "inyumaziherekana", "backrooms": "inyumazibianza", "empiressmp": "empiressmp", "blockstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thequarry": "akavomero", "tlou": "tlou", "dyinglight": "<PERSON>uc<PERSON><PERSON><PERSON>", "thewalkingdeadgame": "umukinnoyimfubyigenze", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "kwi<PERSON><PERSON>bwing<PERSON>", "stateofsurvivalgame": "imiter<PERSON><PERSON>uza<PERSON>gam<PERSON>", "vintagestory": "<PERSON><PERSON><PERSON>", "arksurvival": "gukizaamajyambere", "barotrauma": "umuhatiwumuco", "breathedge": "<PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "kubahomuwestlend", "beastsofbermuda": "inyamasikabizezabermuda", "frostpunk": "imbehobyubukonje", "darkwood": "i<PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "ubuzimaburangiyeumukino", "survivalgames": "imikino_yo_kubaho", "sillenthill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "intambarayanjye", "scpfoundation": "scpfoundation", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "kuriraubwoba", "raft": "ub<PERSON><PERSON>", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil8": "residentevil8", "onironauta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "granny": "<PERSON><PERSON><PERSON><PERSON>", "littlenightmares2": "ubuhumyebukebutotwahoro2", "signalis": "ibimenyetso", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "abanabyishyamba", "rustvideogame": "umukinnorustw", "outlasttrials": "muke<PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "guhungabanyamahanga", "undawn": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "7day2die": "7<PERSON><PERSON><PERSON>pfa", "sunlesssea": "inyanjayitaizuba", "sopravvivenza": "k<PERSON><PERSON>", "propnight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "urupfu", "cataclysmdarkdays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soma": "amasoma", "fearandhunger": "ubwobaninzar<PERSON>", "stalkercieńczarnobyla": "stalkeriwumwijimawiyacyernobyl", "lifeafter": "ibu<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "igi<PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "isahu3", "aloneinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftopia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theoutlasttrials": "ibigezeweryoimbonyabyose", "bunker": "bunker", "worlddomination": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "um<PERSON><PERSON>_wa_moteri_zih<PERSON><PERSON>a", "tft": "lol", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "um<PERSON><PERSON><PERSON>_wa<PERSON>aza_bagufi", "warhammer40kcrush": "warhammer40kurukundo", "wh40": "wh40", "warhammer40klove": "urukundo_warhammer40k", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "ubutembobwubwenge", "vindicare": "vindicare", "ilovesororitas": "nkundasororita", "ilovevindicare": "ndakundavindicare", "iloveassasinorum": "nkundasasiniwe", "templovenenum": "urukundo_gato_rwagati", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "intambwe", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "ittakestwo", "wingspan": "uburebure_bw_amababa", "terraformingmars": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofmightandmagic": "intwarizimazeub<PERSON><PERSON>oz<PERSON>", "btd6": "btd6", "supremecommander": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofmythology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args": "args", "rime": "rime", "planetzoo": "<PERSON><PERSON><PERSON><PERSON>", "outpost2": "ubutumwa2", "banished": "b<PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "umuco6", "warcraft2": "warcraft2", "commandandconquer": "tegekaerutsinde", "warcraft3": "warcraft3", "eternalwar": "intambarazitagira", "strategygames": "imikino_ya_strategiya", "anno2070": "anno2070", "civilizationgame": "umukino_w_umuco", "civilization4": "civilization4", "factorio": "factorio", "dungeondraft": "kwiremburamihigo", "spore": "isporwe", "totalwar": "intambarazose", "travian": "travian", "forts": "ibihome", "goodcompany": "<PERSON><PERSON><PERSON>hamwer<PERSON><PERSON>", "civ": "umuco", "homeworld": "kwabo", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "kuba<PERSON>", "realtimestrategy": "ingambazituranga", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "ubwamiibwikamba2", "eu4": "eu4", "vainglory": "k<PERSON><PERSON>", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON>", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "plagueinc": "<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON>", "theorycraft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "umuco3", "4inarow": "ane_z<PERSON><PERSON><PERSON><PERSON>", "crusaderkings3": "umuhamicyurwagihugu3", "heroes3": "intwari3", "advancewars": "intambaraziterambere", "ageofempires2": "imyakaingaheya2", "disciples2": "abigishwa2", "plantsvszombies": "ibimerabirim<PERSON><PERSON>bi", "giochidistrategia": "imikino_yo_guhanga_ingamba", "stratejioyunları": "imikino_ya_strategie", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "imyakayimpinduka", "dinosaurking": "umwamiwanyamanswa", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "imitimayumunyuma4", "companyofheroes": "itsindabaintwari", "battleforwesnoth": "intambarayawesnoth", "aoe3": "aoe3", "forgeofempires": "ubucorongaubushya", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phobies": "ubwoba", "phobiesgame": "phobiegame", "gamingclashroyale": "imikino_clashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "imyakayingahumve4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "<PERSON>tam<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estratejiya", "popfulmail": "am<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningforce": "ing<PERSON><PERSON><PERSON><PERSON>", "masterduel": "<PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "porogaramuyadysonsphere", "transporttycoon": "umukoziwubwikorez<PERSON>", "unrailed": "ntabikurikiranwa", "magicarena": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "umugamboindegatorment", "uplandkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galaxylife": "ubuzimab<PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "wolvesvillekuruburimbere", "slaythespire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlecats": "intambaragazamatsinda", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "gusiganwa", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "umwakanyebyihuse", "realracing3": "um<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "imikino_ya_sims_kubuntu", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "kurambagiza", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "antholog<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>gerejwe", "phasmophobia": "<PERSON><PERSON><PERSON>", "fivenightsatfreddys": "amagoroniyatanumugoroba", "saiko": "saiko", "fatalframe": "umukogotwiteye", "littlenightmares": "inzozizitoyankuru", "deadrising": "bazutsebaka<PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "murugo", "deadisland": "umunyagasab<PERSON>", "litlemissfortune": "agakeburyubuto", "projectzero": "umushorongamajyambere", "horory": "ubwoba", "jogosterror": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor": "mura<PERSON>", "helloneighbor2": "murandumushawacu2", "gamingdbd": "imikino_dbd", "thecatlady": "umugorewindingu", "jeuxhorreur": "imikino_yubwoba", "horrorgaming": "imikino_iterabwoba", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "amazina<PERSON><PERSON><PERSON><PERSON>", "dixit": "<PERSON><PERSON><PERSON>", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "umugandiwarune<PERSON>", "solitaire": "soliteri", "poker": "poker", "hearthstone": "umutima_w_ibuye", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "amakaritayikin<PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sportscards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "urugambaukubishanaamakaaritavanguard", "duellinks": "duellinks", "spades": "pikipiki", "warcry": "uruuru", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "kurwanyabishingizi", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "imikino_ya_yugioh", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "amas<PERSON><PERSON><PERSON><PERSON>_i<PERSON>ab<PERSON>", "yugiohgoat": "yugiohikinyarwagoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rumi", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "kabiri", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "imikino_y_amakarita", "mtgjudge": "mtgjudge", "juegosdecartas": "imikino_y_amakarita", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "carteado", "sueca": "sueca", "beloteonline": "urusimbionline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "urugam<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "umuki<PERSON>_w_amakarita", "žolíky": "žolíky", "facecard": "isura", "cardfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "abubakadege", "marvelchampions": "<PERSON>twari<PERSON><PERSON>", "magiccartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "<PERSON><PERSON><PERSON>_ya_<PERSON><PERSON><PERSON><PERSON>", "skipbo": "simbagabo", "unstableunicorns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberse": "cyberse", "classicarcadegames": "imikino_ya_arcade_ya_kera", "osu": "osu", "gitadora": "gitadora", "dancegames": "imikino_yimbyino", "fridaynightfunkin": "iju<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "umushingamirai", "projectdiva": "umushingawadivaproject", "djmax": "djmax", "guitarhero": "intwari<PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "tura<PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "bwambabakizibe", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "gusakura<PERSON><PERSON><PERSON><PERSON>", "rhythmgamer": "umukinnyi_w_injyana", "stepmania": "imyidendamitereboze", "highscorerythmgames": "amanota_menshi_imikino_yinjyana", "pkxd": "pkxd", "sidem": "<PERSON><PERSON><PERSON><PERSON>", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "imikino_yo_kubyina", "cryptofthenecrodancer": "cryptoyurupfu", "rhythmdoctor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubing": "gukina_rubik", "wordle": "wordle", "teniz": "tenisi", "puzzlegames": "imikino_y_ibis<PERSON>zo", "spotit": "<PERSON><PERSON><PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "amasuzumabyubwenge", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "ibibazo_bikangura_ubwenge", "rubikscube": "rubikscube", "crossword": "amagambo_agufi", "motscroisés": "amagamboemeze", "krzyżówki": "amagambo_agufi", "nonogram": "nonogram", "bookworm": "<PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "<PERSON><PERSON><PERSON><PERSON>", "riddles": "ibisakuzo", "rompecabezas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "munda", "angrybirds": "inyonigihangayikaye", "escapesimulator": "guhungasimulator", "minesweeper": "g<PERSON><PERSON><PERSON>_i<PERSON><PERSON>u", "puzzleanddragons": "imikino_igendanwa", "crosswordpuzzles": "amagambomatanga", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesgame", "puzzlesport": "umukinnwu<PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "imikino_yo_guhunga", "escapegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "igikoresho3d", "homescapesgame": "umukino_homescapes", "wordsearch": "amajamboarashakwa", "enigmistica": "enigmistica", "kulaworld": "<PERSON><PERSON><PERSON><PERSON>", "myst": "agasiri", "riddletales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "shidomo", "theimpossiblequiz": "ikibazogitizemvya", "candycrush": "candycrush", "littlebigplanet": "gitoyantoyagikomeye", "match3puzzle": "ihuroprovamatchi3", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "kidasanzwe", "rubikcube": "rubikcube", "cuborubik": "rubikicyacye", "yapboz": "yapboz", "thetalosprinciple": "igitekerezokinyatalos", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "qbert": "qbert", "riddleme": "mbazansonga", "tycoongames": "imikino_abakoreshejwe", "cubosderubik": "cubosderubik", "cruciverba": "amagambo_yambukiranya", "ciphers": "inyu<PERSON><PERSON>_zihishe", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "turnipboy", "adivinanzashot": "igitekerezo_g<PERSON>ye", "nobodies": "abantagacyo", "guessing": "gukomboza", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "amasomeroagatambanye", "syberia2": "syberia2", "puzzlehunt": "gushanzyamakebanya", "puzzlehunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "um<PERSON><PERSON>_gukemura", "hlavolamy": "ibibazo_byubwenge", "poptropica": "poptropica", "thelastcampfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "twi<PERSON><PERSON><PERSON><PERSON><PERSON>", "picopark": "picopark", "wandersong": "urugendoruririmba", "carto": "carto", "untitledgoosegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "umutwe_urambya", "limbo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rubiks": "rubiks", "maze": "ubutam<PERSON><PERSON>", "tinykin": "agakingirwagato", "rubikovakostka": "rubikovakostka", "speedcube": "rub<PERSON><PERSON><PERSON><PERSON>", "pieces": "ibice", "portalgame": "umukinnuwaportali", "bilmece": "bilmece", "puzzelen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "ibibazo", "cubomagico": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "<PERSON><PERSON>", "monopoly": "monopoli", "futurefight": "intambara<PERSON>jo", "mobilelegends": "imikino_ya_mobile", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "ubuzimabwikibuga", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "inyenyerizihuriye", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alchemystars": "inyenyerinyubatsemburo", "stateofsurvival": "ubuzimabukomeza", "mycity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "imbere_cyane", "knightrun": "urugend<PERSON><PERSON><PERSON>wari", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "umukino_kurugamba", "a3": "a3", "phonegames": "imikino_ya_telefoni", "kingschoice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guardiantales": "inkurugambacyabatwi<PERSON>", "petrolhead": "umushof<PERSON>_ukunda_imodoka", "tacticool": "ta<PERSON><PERSON><PERSON><PERSON>", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftsman": "umuhanga", "supersus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slowdrive": "gutindagahoro", "headsup": "y<PERSON><PERSON><PERSON>", "wordfeud": "amagamboyimikino", "bedwars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freefire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilegaming": "imikino_kuri_mobile", "lilysgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "umuhanourugamba", "pjsekai": "pjsekai", "mysticmessenger": "u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "umukino_wa_8", "emergencyhq": "ibyihutirwehq", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "kunyeganyeganogutetera", "ml": "ml", "bangdream": "inzozizamakubitoririmbyino", "clashofclan": "urugambaurwakirenga", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "umwamikazi_wigihe", "beatstar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "urukundo_mu_mufuka", "androidgames": "imikino_ya_android", "criminalcase": "uruba<PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "ubusambandihabikurya", "dokkan": "dokkan", "aov": "aov", "triviacrack": "imikino_yamabazwa", "leagueofangels": "<PERSON><PERSON>angowamalai<PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "igicuuburundi", "mysingingmonsters": "ibigirangomwimbyino", "nekoatsume": "ipusizitwegeranya", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "imisimbiyintambara", "mirrorverse": "isimbukazuba", "pou": "pou", "warwings": "intambararindege", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "<PERSON><PERSON><PERSON><PERSON>", "slugitout": "toraniranadu<PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "umukinnowasulitani", "arenabreakout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfy": "u<PERSON><PERSON><PERSON>", "runcitygame": "gusiganaurujyongeramuco", "juegodemovil": "juegodemovil", "avakinlife": "ubuzimab<PERSON>ava<PERSON>", "kogama": "kogama", "mimicry": "gukigana", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "umuki<PERSON><PERSON>_w_umuy<PERSON><PERSON>", "grandchase": "grandchase", "bombmebrasil": "nterabyihangane", "ldoe": "ldoe", "legendonline": "umurengeinteri<PERSON><PERSON>", "otomegame": "umuki<PERSON>_w_aba<PERSON>riya", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "<PERSON><PERSON><PERSON>", "carxdriftracing2": "umudugaragariwamodoka2", "pathtonowhere": "inziraidahandi", "sealm": "umutuzo", "shadowfight3": "intambarayigicucu3", "limbuscompany": "kompanyiyalimbus", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "amagambonyinshuti2", "soulknight": "umuhezikanimwe", "purrfecttale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showbyrock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladypopular": "umudamupopulaire", "lolmobile": "urusekouwazimurugendo", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "isiiperezoramugiteganyabwije", "empiresandpuzzles": "ingororoimizimuzo", "empirespuzzles": "imikino_ya_empire", "dragoncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "garitikifone", "battlegroundmobileind": "intambararwizungurukaryurwahindustan", "fanny": "fanny", "littlenightmare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "umukinyambuga", "tearsofthemis": "amadembayubutabera", "eversoul": "ubugingobwubuzi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gunbound": "gunbound", "gamingmlbb": "imikino_mlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiebahung<PERSON>", "eveechoes": "eveechoes", "jogocelular": "umukino_kuri_mobile", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "ma<PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalumukigendanwa", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "imikino_bgmi", "girlsfrontline": "abasorebaimbere", "jurassicworldalive": "jurassicworldalive", "soulseeker": "mush<PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "kureka", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "<PERSON><PERSON><PERSON>_moonchai", "carxdriftracingonline": "<PERSON><PERSON><PERSON>wogu<PERSON>ikumurandawakumurongo", "jogosmobile": "imikino_ya_mobile", "legendofneverland": "igitekerezocyigihugucyitarigeze", "pubglite": "pubglite", "gamemobilelegends": "umukinnomobilelegends", "timeraiders": "timeraiders", "gamingmobile": "imikino_kuri_mobile", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "intambararw", "dnd": "ntakung<PERSON><PERSON>", "quest": "gush<PERSON><PERSON>", "giochidiruolo": "imiki<PERSON>_yo_gukina_uruhare", "dnd5e": "dnd5e", "rpgdemesa": "rpgyimeza", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "umukinnnimukeragu<PERSON>a", "2300ad": "2300ad", "larp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceclub": "urukundo", "d20": "d20", "pokemongames": "imikino_ya_pokemon", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "reba", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "itsindayirakete", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "itsindaamayobera", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "urabizi", "shinypokemon": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "gahunda_ikomye", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "<PERSON><PERSON><PERSON><PERSON>k<PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "abananapokemon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "gukina_umukino", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "abakobwaumukinyi", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "isiblitzryisi", "jeudéchecs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "japanesechess": "umuki<PERSON>_wa_shogi", "chinesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "umunanucanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON>", "rook": "intare", "chesscom": "chesscom", "calabozosydragones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "umuyoboziwamakinamico", "tiamat": "tiamat", "donjonsetdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "umuganiwavoxmachina", "doungenoanddragons": "dungeonanddragons", "darkmoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "sham<PERSON><PERSON><PERSON>", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "imikino_ya_minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modiziminecraft", "mcc": "mcc", "candleflame": "u<PERSON><PERSON>_rw_akabuye", "fru": "gukundanabasore", "addons": "inyongera", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "igice_cyikirere", "minecraftpocket": "minecraftmuurikono", "minecraft360": "minecraft360", "moddedminecraft": "minecraftihindutse", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgamer": "umukinyi_wa_pc", "jeuxvideo": "jeuxvideo", "gambit": "umugamb<PERSON>", "gamers": "<PERSON><PERSON><PERSON><PERSON>", "levelup": "<PERSON><PERSON><PERSON><PERSON>", "gamermobile": "imikino_kuri_mobile", "gameover": "iyiguhezayirangiye", "gg": "gg", "pcgaming": "imikino_ya_mudas<PERSON>wa", "gamen": "gukina", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "abi<PERSON><PERSON>_bya_mudas<PERSON>wa", "casualgaming": "imikino_yoroheje", "gamingsetup": "imikino_yubatse", "pcmasterrace": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgame": "umukino_wa_pc", "gamerboy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vrgaming": "imikino_vr", "drdisrespect": "drdisrespect", "4kgaming": "imikino4k", "gamerbr": "<PERSON><PERSON><PERSON><PERSON>", "gameplays": "imikino", "consoleplayer": "umukiniyikon<PERSON>li", "boxi": "boxi", "pro": "<PERSON><PERSON><PERSON><PERSON>", "epicgamers": "abacurungabadasigana", "onlinegaming": "imikino_kuri_interineti", "semigamer": "umukinyi_gato", "gamergirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermoms": "<PERSON><PERSON><PERSON><PERSON>", "gamerguy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamewatcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "<PERSON><PERSON><PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "abakinnyi_abakobwa", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "itsindatwikunganira", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "umukinyi_mukuru", "cozygaming": "imikino_ituje", "gamelpay": "<PERSON><PERSON><PERSON><PERSON>", "juegosdepc": "imikino_ya_mudas<PERSON>wa", "dsswitch": "kubandukaryands", "competitivegaming": "imikino_igahangana", "minecraftnewjersey": "minecraftnewjersey", "faker": "<PERSON><PERSON><PERSON><PERSON>", "pc4gamers": "pc4gamers", "gamingff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "gukinanaabagenzibawe", "gamepc": "umukinnwakurik<PERSON>", "girlsgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "<PERSON><PERSON><PERSON><PERSON>", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesetup": "gutegeko<PERSON><PERSON><PERSON><PERSON>", "overpowered": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "socialgamer": "umukiniyirwi<PERSON>", "gamejam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "proplayer": "umukinny<PERSON>_wa_kige<PERSON><PERSON>ho", "roleplayer": "<PERSON><PERSON><PERSON><PERSON>", "myteam": "itsindimwe", "republicofgamers": "republikayabakinnyi", "aorus": "aorus", "cougargaming": "igiceb<PERSON><PERSON><PERSON><PERSON>", "triplelegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerbuddies": "abakunzigutekanya", "butuhcewekgamers": "nkenezeumukob<PERSON><PERSON><PERSON>", "christiangamer": "um<PERSON><PERSON>_ukina", "gamernerd": "umukinyi_nerd", "nerdgamer": "umunyabumengeinkino", "afk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "andregamer": "andregamer", "casualgamer": "umukinnyi_gato", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "umutekano", "gemers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunizlemek": "kurebaimikino", "gamertag": "<PERSON><PERSON>augu<PERSON><PERSON>", "lanparty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamer": "<PERSON><PERSON><PERSON>", "wspólnegranie": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "umukinnyi_wa_playstation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "umukindinkungiziindwara", "gtracing": "gusiganwa", "notebookgamer": "notebookgamer", "protogen": "protogen", "womangamer": "umukinnyi_w_umugore", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON>a", "humanfallflat": "aban<PERSON>aribaguy<PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "guhungahata<PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomuzika", "sonicthehedgehog": "sonicigihimburabyihuta", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "hagura", "zelda": "zelda", "smashbros": "umukinowabagabo", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "mario<PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "avukatiyingab<PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "abanaabi<PERSON>rere", "tomodachilife": "<PERSON>bu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "amari<PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "imikino_yo_gutembera", "nintendogames": "imikino_ya_nintendo", "thelegendofzelda": "urugendorwazelda", "dragonquest": "dragonquest", "harvestmoon": "ukwezikwizuba", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "umwuka<PERSON><PERSON><PERSON>", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "umubura<PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "imikino51", "earthbound": "<PERSON><PERSON><PERSON>", "tales": "<PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "inyubakoyaluigi", "animalcrosssing": "inyamanswa_zambukiranya", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "<PERSON><PERSON><PERSON><PERSON>", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "ing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "igihugurwakongonyuma2", "hyrulewarriors": "intambararwa<PERSON><PERSON><PERSON>", "mariopartysuperstars": "umuki<PERSON><PERSON><PERSON><PERSON><PERSON>", "marioandsonic": "marioanasonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "imbwanintendo", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "marion<PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "imbwakibumbye", "vanillalol": "vanilla", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "<PERSON><PERSON><PERSON><PERSON>", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsespanya", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gukinaryandetfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "imikino_ya_videwo_ya_kera", "scaryvideogames": "imikino_iterabwoba", "videogamemaker": "umukoravidewo", "megamanzero": "megamanzero", "videogame": "<PERSON><PERSON><PERSON>", "videosgame": "umukino_wa_videwo", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "imikino_battleblocktheater", "arcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "ubuhin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxrwanda", "robloxdeutsch": "rob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "sanboxgames", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "rollerdrome", "parasiteeve": "umugoroberai<PERSON><PERSON>", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "a<PERSON><PERSON><PERSON><PERSON>", "dreamscape": "inzozizubwenge", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amordoce": "ubukundiburyoshye", "videogiochi": "imikino_ya_videwo", "theoldrepublic": "repubur<PERSON><PERSON><PERSON><PERSON>", "videospiele": "videogame", "touhouproject": "touhouproject", "dreamcast": "inzozizakozwe", "adventuregames": "imikino_y_urugendo", "wolfenstein": "wolfenstein", "actionadventure": "ibikoreroshyaka", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "imikino_ya_kera", "retroarcade": "imikino_ya_kera", "vintagecomputing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogaming": "imikino_ya_kera", "vintagegaming": "imikino_ya_kera", "playdate": "gukinnagirango", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "akataraboneka2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "ubuzimabwihangane", "beatmaniaiidx": "beatmaniaiidx", "steep": "gahoro", "mystgames": "mystgames", "blockchaingaming": "imikino_ya_blockchain", "medievil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "consolegaming": "imikino_ya_console", "konsolen": "konsolen", "outrun": "gusumba", "bloomingpanic": "u<PERSON><PERSON><PERSON>uter<PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "imikino_yubwoba", "monstergirlquest": "igitangazaukubakobwa", "supergiant": "ikigezwezo", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "imikino_y<PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "imikino_ishaje", "bethesda": "bethesda", "jackboxgames": "imikino_ya_jackbox", "interactivefiction": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantesamentes", "visualnovel": "igiteka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovels": "amatekamashushoyandikirwe", "rgg": "rgg", "shadowolf": "umugobobant<PERSON>", "tcrghost": "tcrbazima", "payday": "ubushyinguro", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "umuganwabwindirimbo", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "a<PERSON><PERSON><PERSON>", "aestheticgames": "imikino_nziza", "novelavisual": "igitekend<PERSON>oma", "thecrew2": "itsindamuntu2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "umukino_wa_kera", "tonyhawkproskater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smbz": "smbz", "lamento": "<PERSON><PERSON><PERSON>", "godhand": "ikiganzacyimana", "leafblowerrevolution": "impindukayi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "i<PERSON><PERSON><PERSON><PERSON>_cy_urwego", "starrail": "starrail", "keyblade": "urufunguzo_rw_inkoni", "aplaguetale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnafsometimes": "fnafirimwenary<PERSON>", "novelasvisuales": "novelasvisuales", "robloxbrasil": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pacman": "pacman", "gameretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videojuejos": "videoju<PERSON><PERSON>", "videogamedates": "imikino_kurugereza", "mycandylove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "gusaimpamvu3", "hulkgames": "imikino_ya_hulk", "batmangames": "imikino_ya_batman", "returnofreckoning": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "umunsiwikiganza", "maniacmansion": "igisigamansion", "crashracing": "gus<PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "imikino3diyubuhagarike", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "imikino_ya_kera", "hellblade": "urugamban<PERSON><PERSON>", "storygames": "imikino_y_inkuru", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "k<PERSON>rishaimbyinaebyiri", "gameuse": "imikino", "offmortisghost": "offmortisghost", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "ongerapawa", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "ibisobanurobyeretse", "quickflash": "imas<PERSON><PERSON>", "fzero": "fzero", "gachagaming": "imikino_ya_gacha", "retroarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "u<PERSON><PERSON><PERSON>", "powerwashsim": "gukozamashiniyisukura", "coralisland": "ikirwacoralline", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "izindiyindi", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "umuh<PERSON>_w_i<PERSON><PERSON>e", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "ibyumabyahindutse", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simulateri", "symulatory": "symulatory", "speedrunner": "<PERSON><PERSON><PERSON><PERSON>", "epicx": "epicxpic", "superrobottaisen": "superrobotkurwanya", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "imikino_ya_videwo", "gaiaonline": "gaiaonline", "korkuoyunu": "umukinnowubwoba", "wonderlandonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skylander": "skylander", "boyfrienddungeon": "umusore<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "toontownryanditswe", "simracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "inzika", "partyvideogames": "imikino_ya_videwo_y_i<PERSON>ori", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "imbuganyuzimuyajya", "legacyofkain": "umuragowakain", "hackandslash": "gucanokwica", "foodandvideogames": "ibyokulya_n_imikino_ya_videwo", "oyunvideoları": "oyunvideoları", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "<PERSON><PERSON>_ka<PERSON><PERSON><PERSON><PERSON>i", "horizonworlds": "isizungukirere", "handygame": "umukino_woroheje", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolvideogames": "imikino_ya_kera", "racingsimulator": "simulator<PERSON><PERSON><PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "ibitsindwimimenyekane", "songpop": "indirimbozikintu", "famitsu": "famitsu", "gatesofolympus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunternow": "monsterhunternow", "rebelstar": "umunyamasewer<PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogaming": "imikino_ya_videwo_itandukanye", "indiegaming": "imikino_ntoya", "indievideogames": "imikino_gito", "indievideogame": "umuki<PERSON>_<PERSON><PERSON>_witezubere", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermanudatsinzira", "bufffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "ntakibishob<PERSON>", "projectl": "umushorongol", "futureclubgames": "imikino_ya_club_izaza", "mugman": "<PERSON>gi<PERSON><PERSON>", "insomniacgames": "insomniacgames", "supergiantgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "umukinnoceleste", "aperturescience": "aperturescience", "backlog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamebacklog": "imikino_ntarangiye", "gamingbacklog": "imikino_ntigeze_nyikina", "personnagejeuxvidéos": "imikinyabi<PERSON><PERSON><PERSON>o", "achievementhunter": "intsinzikuzamura", "cityskylines": "imijyikubengasurakirere", "supermonkeyball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deponia": "deponia", "naughtydog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastlord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosretro": "imikino_ya_kera", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "urugaragazadopamine", "staxel": "staxel", "videogameost": "imiz<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsync": "umuzingosinkuronize", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "nkundakofxv", "arcanum": "amabanga", "neoy2k": "neoy2k", "pcracing": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "berserk": "<PERSON><PERSON><PERSON>", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animeyababay<PERSON>", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animewithplot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pesci": "pesci", "retroanime": "animeyakera", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80", "90sanime": "anime90", "darklord": "umutware_w_umwijima", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "d<PERSON><PERSON><PERSON><PERSON><PERSON>mber<PERSON>", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "igitabo_cya_anime", "thevisionofescaflowne": "ivisionyaescaflowne", "slayers": "abica", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bananafish": "umufiinya", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "ubusa", "fireforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "kazindukundi", "fairytail": "umuganowahagije", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "yakozerwemwomuzi", "parasyte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "uburirimb<PERSON>bunyanja", "kamisamakiss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "mangayubwoba", "romancemangas": "mangayurukundo", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "um<PERSON><PERSON><PERSON>_i<PERSON><PERSON>ka", "blacklagoon": "umwo<PERSON>_wirabura", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "ubuhengeziinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "indeksizitagatirimwe", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>vy<PERSON>m<PERSON>", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8ubuz<PERSON><PERSON>zo", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "igisimbamukobwa", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animeyaimikino", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "urugendorwatanyatheevil", "shounenanime": "urwegemovieanimezabah<PERSON>u", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "inkotoyamajyaruguru", "mazinger": "mazinger", "blackbuttler": "umuk<PERSON>i_wirabura", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "ukongumihembe", "fullmoonwosagashite": "ukwezikwuzuye", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "cyizanicyangabigirak<PERSON>e", "martialpeak": "intambweyubukombe", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "hiscoregirl", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "<PERSON><PERSON><PERSON><PERSON>", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON>ishaje", "chainsawman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "<PERSON><PERSON><PERSON>", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "icumu", "loli": "loli", "horroranime": "anime_ziteranya", "fruitsbasket": "imbogaurimbuto", "devilmancrybaby": "umwanawumuzimuwu<PERSON><PERSON>", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "ubuzimaburyoshye", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "igihuguyacyasezeraniwe", "monstermanga": "urug<PERSON><PERSON>ng<PERSON>", "yourlieinapril": "ububeshor<PERSON>", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "inzirazyumalaika", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "<PERSON><PERSON><PERSON><PERSON>", "deepseaprisoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "umuki<PERSON>_wa_darwin", "husbu": "<PERSON><PERSON>bowanj<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "umutimawapandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "intambarayibiryo", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "umuron<PERSON>_wa_satani", "toyoureternity": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON>sib<PERSON>", "bluelock": "bluelock", "goblinslayer": "umwicaingoma", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "igitsikabakobwa", "vampireknight": "vampireknight", "mugi": "mugi", "blueexorcist": "urukundo_rwubururu", "slamdunk": "i<PERSON><PERSON><PERSON>uyikeye", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "<PERSON><PERSON><PERSON>", "spyfamily": "spyfamily", "airgear": "imyotsiikiziga", "magicalgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "ibyahanyugupfundimusanze", "prisonschool": "agerihocyivungane", "thegodofhighschool": "<PERSON><PERSON><PERSON><PERSON>", "kissxsis": "gusomana_mushiki", "grandblue": "grandblue", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "igitonyangaanimation", "swordartonlineabridge": "swordartonlineinkubibumwe", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "imfuihomvu", "romancemanga": "imbugomanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "urukundo_rw_anime", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeyubuwari<PERSON><PERSON>e", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayerkuinkota", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "u<PERSON><PERSON><PERSON><PERSON><PERSON>", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "inyenyerizihuriye", "romanceanime": "urukundo_anime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "ubumagicdeikiragi", "housekinokuni": "urugomukinokuni", "recordragnarok": "guf<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "tekinoyo_ubudage", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "umuganizawatenisi", "tonikawa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "ishur<PERSON>bica", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animeyubuyapani", "animespace": "animespace", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "animeyindependa", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "umugabo_w_imbeba", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funamusea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "boch<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "kunyagukugasimburwa", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "cyaneb<PERSON>mere<PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "umukinnyi_wa_rave", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "ubukor<PERSON><PERSON><PERSON>aba<PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "abag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeshojo": "animeshojo", "reverseharem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mybossdaddy": "<PERSON>un<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "umutwe5", "grandbluedreaming": "grandbluedreaming", "bloodplus": "amarasoplus", "bloodplusanime": "animebloodplus", "bloodcanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofdemonsandgods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "reba_anime", "animegirls": "abakobanimeqq", "sharingan": "<PERSON><PERSON>", "crowsxworst": "inkokonankobwa", "splatteranime": "animezisakara", "splatter": "<PERSON><PERSON><PERSON><PERSON>", "risingoftheshieldhero": "kwihangiraingabonyantege", "somalianime": "animeyasomaliya", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animeespanya", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "ubugengebugenge", "supercampeones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeidols": "animeidols", "isekaiwasmartphone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "midorinohibi": "iminsiy<PERSON>ats<PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "ubus<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "shak<PERSON><PERSON><PERSON><PERSON><PERSON>", "princessjellyfish": "umugandasigarwi", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeverse": "animeverse", "persocoms": "persocoms", "omniscientreadersview": "abasomabizibyose", "animecat": "animekitty", "animerecommendations": "animezisabwe", "openinganime": "ifunguramanimwe", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefightergundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "<PERSON><PERSON><PERSON>", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "javel", "deathnote": "igitabourwapfu", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "urugendorwijojorwudasanzwe", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "anime_ya_gisirikare", "greenranger": "greenranger", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shoneni<PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonslayer", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "akaademiyibatwangebange", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onepieceanime": "animeyaonpiece", "attaquedestitans": "igiterocyibihangange", "theonepieceisreal": "theonepiece<PERSON><PERSON>", "revengers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "igitsinamobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimonstory": "ink<PERSON>yadigi<PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "igerihenzimuto", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonzidafite", "kemonofriends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "um<PERSON><PERSON>_w_a<PERSON><PERSON><PERSON><PERSON>_bose", "recuentosdelavida": "inkozibyubuzima"}