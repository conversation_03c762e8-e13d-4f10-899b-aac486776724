{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "izinkanyezi", "cognitivefunctions": "imisebenziyengqondo", "psychology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "philosophy": "<PERSON><PERSON><PERSON><PERSON>", "history": "umlando", "physics": "ifiziksi", "science": "<PERSON><PERSON><PERSON><PERSON>", "culture": "isiko", "languages": "<PERSON><PERSON><PERSON><PERSON>", "technology": "ubu<PERSON><PERSON><PERSON><PERSON>", "memes": "amameme", "mbtimemes": "mbtimemes", "astrologymemes": "amamimize<PERSON><PERSON><PERSON><PERSON>", "enneagrammemes": "amamemesenneagram", "showerthoughts": "imica<PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "hle<PERSON>a", "videos": "<PERSON><PERSON><PERSON><PERSON>", "gadgets": "<PERSON><PERSON><PERSON><PERSON>", "politics": "ezombusazwe", "relationshipadvice": "izeluluthozothando", "lifeadvice": "izelulekozokuphila", "crypto": "crypto", "news": "izindaba", "worldnews": "izindabazomhlaba", "archaeology": "umlando", "learning": "ukufunda", "debates": "izingxoxo", "conspiracytheories": "imibonowemfihl<PERSON>lo", "universe": "indawo_yonke", "meditation": "ukuzindla", "mythology": "<PERSON>zinganekwan<PERSON>", "art": "<PERSON><PERSON><PERSON><PERSON>", "crafts": "imisebenzobukhono", "dance": "dansa", "design": "<PERSON><PERSON><PERSON><PERSON>", "makeup": "isimo", "beauty": "u<PERSON><PERSON>e", "fashion": "<PERSON><PERSON><PERSON><PERSON>", "singing": "<PERSON><PERSON>ula", "writing": "ukubhala", "photography": "ukuthwebulaumfanek<PERSON>o", "cosplay": "cosplay", "painting": "ukudweba", "drawing": "ukudweba", "books": "izincwadi", "movies": "amamu<PERSON>", "poetry": "<PERSON><PERSON><PERSON>", "television": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filmmaking": "ukwen<PERSON><PERSON>lim<PERSON>", "animation": "<PERSON><PERSON><PERSON><PERSON>", "anime": "anime", "scifi": "<PERSON><PERSON><PERSON><PERSON>", "fantasy": "<PERSON><PERSON><PERSON><PERSON>", "documentaries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mystery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "comedy": "<PERSON><PERSON><PERSON>", "crime": "ubugebengu", "drama": "indaba", "bollywood": "ibollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "es<PERSON><PERSON><PERSON>", "romance": "uthando", "realitytv": "umdlaloweqiniso", "action": "<PERSON><PERSON><PERSON>", "music": "umculo", "blues": "amab<PERSON>z", "classical": "iklasiki", "country": "izwe", "desi": "desi", "edm": "edm", "electronic": "ielekthronikhi", "folk": "a<PERSON><PERSON>", "funk": "funk", "hiphop": "hiphop", "house": "indlu", "indie": "indie", "jazz": "<PERSON><PERSON><PERSON>", "kpop": "kpop", "latin": "latin", "metal": "insimbi", "pop": "p<PERSON><PERSON>", "punk": "<PERSON><PERSON><PERSON><PERSON>", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "dwala", "techno": "ithekno", "travel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "concerts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "festivals": "imicimbi", "museums": "am<PERSON><PERSON><PERSON><PERSON><PERSON>", "standup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theater": "iti<PERSON><PERSON>", "outdoors": "phandle", "gardening": "ukulima", "partying": "ukuzitika", "gaming": "<PERSON><PERSON><PERSON><PERSON>", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chess": "ishesi", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "istarcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "ukudla", "baking": "ukupheka", "cooking": "ukupheka", "vegetarian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vegan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "birds": "<PERSON><PERSON><PERSON><PERSON>", "cats": "<PERSON><PERSON><PERSON>", "dogs": "<PERSON><PERSON><PERSON>", "fish": "inhlanzi", "animals": "izilwan<PERSON>", "blacklivesmatter": "abantsundumabaphile", "environmentalism": "imvelo", "feminism": "<PERSON><PERSON><PERSON><PERSON>", "humanrights": "<PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stopasianhate": "yekelaukuzondaamaeshiya", "transally": "umnganewab<PERSON><PERSON>_a<PERSON><PERSON><PERSON><PERSON>_ubulili", "volunteering": "ukuvolontiya", "sports": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "badminton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baseball": "ibhola_likanobhutshuzwayo", "basketball": "ibhola_likanobhutshuzwayo", "boxing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cricket": "bhola", "cycling": "ukugibela", "fitness": "uk<PERSON><PERSON><PERSON>_kahle", "football": "ib<PERSON><PERSON>", "golf": "<PERSON><PERSON><PERSON><PERSON>", "gym": "gym", "gymnastics": "jimnastiki", "hockey": "i<PERSON><PERSON>i", "martialarts": "impilokelwa", "netball": "umdlalo_webhola", "pilates": "ipilates", "pingpong": "ipingpong", "running": "<PERSON><PERSON><PERSON><PERSON>", "skateboarding": "ukushibilikanzimba", "skiing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "snowboarding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "surfing": "ukusefa", "swimming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tennis": "<PERSON><PERSON><PERSON>", "volleyball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weightlifting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "iyoga", "scubadiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiking": "ukuqwalaizintaba", "capricorn": "mgede", "aquarius": "<PERSON><PERSON><PERSON><PERSON>", "pisces": "inhlanzi", "aries": "ink<PERSON><PERSON><PERSON>", "taurus": "<PERSON><PERSON><PERSON>", "gemini": "<PERSON><PERSON><PERSON>", "cancer": "<PERSON><PERSON><PERSON><PERSON>", "leo": "leo", "virgo": "<PERSON>mbi", "libra": "und<PERSON>_we<PERSON>i", "scorpio": "ufudu", "sagittarius": "<PERSON><PERSON><PERSON><PERSON>", "shortterm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "<PERSON><PERSON><PERSON><PERSON>", "longtermrelationship": "ubudlelwaneobudephileyo", "single": "nginesingle", "polyamory": "ubu<PERSON><PERSON><PERSON><PERSON><PERSON>", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "<PERSON><PERSON><PERSON>", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "izinja_zokugada", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "ink<PERSON><PERSON>yenkosi", "soulreaver": "um<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofspyro": "inganekwanyekaspyro", "rouguelikes": "amaroguelike", "syberia": "isibhiliya", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "ilanga<PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openworld": "umhlababalulekile", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "umphefu<PERSON><PERSON><PERSON>a", "dungeoncrawling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "indi<PERSON><PERSON><PERSON>", "lordsoftherealm2": "amakhosiwomhlaba2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "umbelethombala", "medabots": "amadabots", "lodsoftherealm2": "amakhosiomhlaba2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "imishiniyokuzifakisa", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "witcher": "witcher", "dishonored": "ngahl<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "mdo<PERSON>", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "ukuguqula", "charactercreation": "ukwakhaizihlangumbe", "immersive": "<PERSON><PERSON><PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "ukugqugquzelak<PERSON>o", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegames": "imid<PERSON>oye_otome", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ioc<PERSON><PERSON><PERSON><PERSON><PERSON>", "yiikrpg": "yiikrpg", "vampirethemasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dimension20": "dimension20", "gaslands": "amazwegesi", "pathfinder": "umvuli", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "iga<PERSON>_ligc<PERSON>le_emgwaqeni", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "<PERSON><PERSON><PERSON><PERSON><PERSON>_k<PERSON><PERSON><PERSON>_adons<PERSON>_phansi", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "ngokukodwa", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "<PERSON><PERSON><PERSON>", "yourturntodie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpgwesabekayo", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "imibaloyokudlala", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "indawoyokuvikela", "gurps": "gurps", "darkestdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "isigabaesifihliwe", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "amaqhawelomndeniomkhulu", "skullgirls": "amantombazanezogeb<PERSON>zi", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "impiemhlola", "jaggedalliance2": "jaggedalliance2", "neverwinter": "akusekhonaubusika", "road96": "umgwaqo96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "imidlaloengaphindaphi<PERSON>", "gothamknights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "izindawozasekhohliwe", "dragonlance": "dragonlance", "arenaofvalor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childoflight": "umntwanokukhanya", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "idigimoni", "monsterrancher": "umlimiwezilwane", "ecopunk": "ecopunk", "vermintide2": "inqwaba2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "isithunziepunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "imfihl<PERSON>lokahog<PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "shaya", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "umfundiinkanyezi", "goldensun": "ilangalegolide", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "izinkombaezimnyameni", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkbomvu", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "ukuwaehlulekile", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "izwelibubi", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "ubunkulunkulu", "pf2": "pf2", "farmrpg": "umdlaloweplasirpg", "oldworldblues": "ubuhlungubedolobha<PERSON>a", "adventurequest": "uhambolokufuna", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "imidlaloyokuzenzisa", "finalfantasy9": "finalfantasy9", "sunhaven": "ilanga", "talesofsymphonia": "izindabazesymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "idol<PERSON><PERSON><PERSON>ziweyo", "myfarog": "myfarog", "sacredunderworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chainedechoes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darksoul": "umphe<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "imid<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "othercide": "<PERSON><PERSON><PERSON><PERSON>", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pillarsofeternity": "izinsikazaphakade", "palladiumrpg": "palladiumrpg", "rifts": "izingxabano", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON>", "hellocharlotte": "sanema<PERSON><PERSON>otte", "legendofdragoon": "inganekwanedragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirelamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "impisi<PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "<PERSON><PERSON><PERSON><PERSON>", "childrenofmorta": "abantwana<PERSON><PERSON><PERSON><PERSON>", "engineheart": "injininhliziyo", "fable3": "inganekwane3", "fablethelostchapter": "insuku<PERSON>lahlekile", "hiveswap": "hiveswap", "rollenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "imvuselakelayisikole", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "izwelase<PERSON>hl<PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "umgodid<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "imidlaloyerpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "<PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "izinhliziywoyasendle", "bastion": "inqaba", "drakarochdemoner": "amadi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "esibhakabhakasasearcadia", "shadowhearts": "izinhlizi<PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "igazi_elincane", "breathoffire4": "umlilowephefumulo4", "mother3": "umama3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "en<PERSON><PERSON><PERSON>", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "umdlalowezicangozimibi", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "<PERSON>dl<PERSON>ka<PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirilamasquerade", "dračák": "umdlwane", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cocttrpg": "cocttrpg", "huntroyale": "inqabayokuzingela", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "isivumelwanosikashadowheart", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "umbusowaweza", "awplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theworldendswithyou": "umhlabawuphelanawe", "dragalialost": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elderscroll": "umquluscrollomdala", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "iforamuyamdlaloweqhawe", "golarion": "golarion", "earthmagic": "umlingo_womhlaba", "blackbook": "incwadinswana", "skychildrenoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "okungcwelekahlegolide", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "umdlalowegothic", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "imidlaloye_rpg", "prophunt": "ukuzingela_abaphrofethi", "starrails": "izinja<PERSON><PERSON>", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "khombauchofoza", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freeside": "<PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "emvakwecyberpunk", "deathroadtocanada": "indlelayokufaecanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "umzingeliwesilwan<PERSON>", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "ukubusailizwe", "persona5": "persona5", "ghostoftsushima": "umoyawetsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "umzingeliwezilwane", "nier": "nier", "dothack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ys": "<PERSON><PERSON><PERSON><PERSON>", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "umdlaloweqhingalempi", "mahoyo": "mahoyo", "animegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "iphimboeliphakade", "princessconnect": "ukuxhumanak<PERSON><PERSON><PERSON><PERSON>", "hexenzirkel": "umthakathiwesifazane", "cristales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vcs": "vcs", "pes": "pes", "pocketsage": "isalulekisephaketheni", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorant<PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "<PERSON>dlalo<PERSON>kh<PERSON><PERSON><PERSON><PERSON>", "mlg": "mlg", "leagueofdreamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa14": "fifa14", "midlaner": "umid<PERSON><PERSON>", "efootball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamhack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaimin": "dlala", "overwatchleague": "overwatchleague", "cybersport": "umdlalounyawo", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorant<PERSON><PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "iportal2", "halflife": "impiloyesigamu", "left4dead": "ngishiyekwafa", "left4dead2": "kushiyweabafile2", "valve": "<PERSON><PERSON><PERSON>", "portal": "isango", "teamfortress2": "iqembulenqabaeyesibili", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "imvuzisimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "inkululekoyomhlaba", "transformice": "transformice", "justshapesandbeats": "izimookubhayiza", "battlefield4": "impizempi4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "gcobhel<PERSON><PERSON>le", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "ingoziyemvula2", "metroidvanias": "amamethroidvania", "overcooked": "kushiwe_kakhulu", "interplanetary": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helltaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "amaselimfile", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "umgodi", "stray": "imidaka", "battlefield": "impi", "battlefield1": "impizempi1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "isubmarine", "eyeb": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesert": "black<PERSON>ert", "tabletopsimulator": "umdlaloetafuleni", "partyhard": "hlabang<PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "inqwelomgqunyaelunzima", "hades": "hades", "gunsmith": "umkhandi_wezibhamu", "okami": "<PERSON>ami", "trappedwithjester": "valelekenejester", "dinkum": "iqiniso", "predecessor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rainworld": "<PERSON>z<PERSON>lomvula", "cavesofqud": "imigeded<PERSON>ud", "colonysim": "umdlalowekoloni", "noita": "noita", "dawnofwar": "kusukokwempi", "minionmasters": "abaphathiabancane", "grimdawn": "grimdawn", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "umsebenzikomphefumulo", "datingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "ukubalekakwecube", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "defconkanzima", "kenopsia": "kenopsia", "virtualkenopsia": "virtualkenopsia", "snowrunner": "<PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "umtapowelucingo", "l4d2": "l4d2", "thenonarygames": "imid<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "umhlolowemimoya", "placidplasticduck": "ithunyangwelikahlevelihlwanyane", "battlebit": "impibit", "ultimatechickenhorse": "inkukhuenkunzikamalanga", "dialtown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "ubusukubamakati", "supermeatboy": "supermeatboy", "tinnybunny": "inyonganencane", "cozygrove": "indawoyekuphola", "doom": "inkathazo", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "imingcele", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "i<PERSON><PERSON><PERSON>", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "umdlalowefarcry", "paladins": "<PERSON><PERSON><PERSON><PERSON>", "earthdefenseforce": "umbuthowezokulondolozakomhlaba", "huntshowdown": "ukuzingelaokukhomba", "ghostrecon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto5": "gta5", "warz": "impi", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "b<PERSON>lang<PERSON><PERSON><PERSON><PERSON>", "joinsquad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "impiuhlabathiyesivunguvungu", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "b4b": "b4b", "codwarzone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "umphethekwesibuko", "divisions2": "izigaba2", "killzone": "indawoyokubulala", "helghan": "hel<PERSON>", "coldwarzombies": "imfazambieyompikamandiband", "metro2033": "metro2033", "metalgear": "imetalgear", "acecombat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crosscode": "ukuphambana_kwekhodi", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON><PERSON><PERSON><PERSON>weq<PERSON><PERSON>", "modernwarfare": "impize<PERSON><PERSON>", "neonabyss": "umbhinyaneonabyss", "planetside2": "planetside2", "mechwarrior": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boarderlands": "boarderlands", "owerwatch": "ower<PERSON>", "rtype": "uhlobo", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "ukunqobanezilwane", "worldofwarships": "worldofwarships", "back4blood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "inkabi", "masseffect": "masseffect", "systemshock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopsumug<PERSON>", "killingfloor2": "bulalaugesi2", "cavestory": "indabayomgede", "doometernal": "inhlanganaphakade", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "umqulu2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "impizemanje2", "blackops1": "blackops1", "sausageman": "<PERSON>do<PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "ubuhlungubungekho", "warface": "ubusojempi", "crossfire": "crossfire", "atomicheart": "inhliziyweathomu", "blackops3": "blackops3", "vampiresurvivors": "abaphangiabampaya", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "<PERSON><PERSON><PERSON><PERSON>", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "umdlalowepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "imetalgearsonsoflibert", "juegosfps": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "convertstrike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warzone2": "impirangezimbili", "shatterline": "<PERSON><PERSON><PERSON><PERSON>zintingo", "blackopszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodymess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "republikamakhomando", "elitedangerous": "elitedangerous", "soldat": "<PERSON><PERSON><PERSON><PERSON>", "groundbranch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "<PERSON><PERSON><PERSON><PERSON>", "destiny1": "isiphetheloyedwa", "gamingfps": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "redfall": "ukuwaredfall", "pubggirl": "intombeyepubg", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "ikh<PERSON>iliqi<PERSON>le", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "usukuhlawula2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "isoapcod", "ghostcod": "iph<PERSON><PERSON>_lekhodi", "csplay": "csplay", "unrealtournament": "imdumekamdlalo", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "<PERSON><PERSON><PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halo3": "halo3", "halo": "<PERSON><PERSON>o", "killingfloor": "inkundlalaphelaphezulu", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "okuneonokumhlophe", "remnant": "insalela", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "<PERSON><PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "ukunyakaza2", "microvolts": "<PERSON>ami<PERSON><PERSON><PERSON><PERSON>", "reddead": "ufilemhlophe", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "impizempi3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "ukugqwala", "conqueronline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dauntless": "ingesabi", "warships": "imik<PERSON><PERSON>_yempi", "dayofdragons": "usukudragons", "warthunder": "warthunder", "flightrising": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recroom": "igumbilokudlalela", "legendsofruneterra": "amaqhawelweruneterra", "pso2": "pso2", "myster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phantasystaronline2": "phantasystaronline2", "maidenless": "ungumfanawent<PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "susa", "agario": "agario", "secondlife": "impiloyesibili", "aion": "aion", "toweroffantasy": "itoweroffantasy", "netplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "iklabhukapenguini", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON>", "newworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pirate101": "umgquba101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "<PERSON><PERSON><PERSON><PERSON>", "3dchat": "3dchat", "nostale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tauriwow": "tauri<PERSON>o", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "im<PERSON><PERSON><PERSON><PERSON><PERSON>", "silkroad": "umgwaqowalokhwebu", "spiralknights": "amasosha_es<PERSON><PERSON><PERSON><PERSON>a", "mulegend": "umlando", "startrekonline": "startrekkubuyekeza", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "isiphrofethisodrago", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelsonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "igrandfant<PERSON>a", "blueprotocol": "blueprotocol", "perfectworld": "umhlabawaphelelayo", "riseonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "corepunk": "corepunk", "adventurequestworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flyforfun": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "animaljam", "kingdomofloathing": "umbusowokuzonda", "cityofheroes": "am<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "impizemgwaqo", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON>o", "tekken": "tekken", "guiltygear": "umdlaloongcolile", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streetsofrage": "imigwaqoyolaka", "mkdeadlyalliance": "mkunyewenzokubambisananokufa", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "njengenhlamba", "retrofightinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blasphemous": "ihlazayo", "rivalsofaether": "izithainirival<PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvcapcom", "supersmash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugen": "mugen", "warofthemonsters": "impiyezilwane", "jogosdeluta": "imidlaloyokulwa", "cyberbots": "amacyberbots", "armoredwarriors": "amaqhaweivikelekile", "finalfight": "ukulwal<PERSON>g<PERSON><PERSON>", "poweredgear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "imidlalolokulwa", "killerinstinct": "umoyow<PERSON><PERSON>la", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON>bal<PERSON>", "ghostrunner": "umgijimiwes<PERSON><PERSON>", "chivalry2": "inhlonipho2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "umdlalowolandelelahollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "silksonggame": "umdlalowesilksong", "silksongnews": "izindabazesilikisongo", "silksong": "silksong", "undernight": "ebus<PERSON>", "typelumina": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "evolutiontournament": "<PERSON><PERSON><PERSON><PERSON><PERSON>owokuziphend<PERSON>la", "evomoment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lollipopchainsaw": "ushu<PERSON><PERSON>_<PERSON><PERSON><PERSON>b<PERSON><PERSON>i", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "izindabazoberseria", "bloodborne": "igazi_eligcwele", "horizon": "umkhathizwe", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "igazi_elithwele", "uncharted": "kungajwayelekile", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "infamous": "<PERSON><PERSON><PERSON>", "playstationbuddies": "abanganibeplaystation", "ps1": "ps1", "oddworld": "umhl<PERSON>wesimanga", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "<PERSON><PERSON><PERSON><PERSON>", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "mfethumfethu", "gta4": "gta4", "gta": "gta", "roguecompany": "inkampaniyengavumelekile", "aisomniumfiles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "unkulunkuluwempi", "gris": "gris", "trove": "umcebo", "detroitbecomehuman": "detroityibaumuntu", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "umk<PERSON><PERSON><PERSON>_we<PERSON><PERSON><PERSON>hi", "lspdfr": "lspdfr", "shadowofthecolossus": "isithunzisomdondoshiya<PERSON>", "crashteamracing": "crashteamracing", "fivepd": "5pd", "tekken7": "tekken7", "devilmaycry": "abantubangazakhala", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraiwarriors": "amasamuraiempi", "psvr2": "psvr2", "thelastguardian": "umqaphielin<PERSON><PERSON>a", "soulblade": "umoyawenkemba", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "ukuzingela_amadoda", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "izinhliziywezithunzi2isivumelwano", "pcsx2": "pcsx2", "lastguardian": "umqaphielin<PERSON>cina", "xboxone": "xboxone", "forza": "ngamandla", "cd": "cd", "gamepass": "igamepass", "armello": "armello", "partyanimal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "impiyangomlwanewebho<PERSON>i", "psychonauts": "izingelosi_zengqondo", "mhw": "mhw", "princeofpersia": "inkhosanayepersia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "impi", "dontstarvetogether": "singalambisane", "ori": "ori", "spelunky": "umgodimgodi", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "izinkanyezizibophile", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable2": "inganekwane2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skycotl": "isibhakabhakacotl", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "isichebelittle", "sallyface": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "franbow": "franbow", "monsterprom": "ipromyezigundane", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "amamotho", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "inkonyamayensindiso", "duckgame": "umdlalowedada", "thestanleyparable": "istanleyparable", "towerunite": "towerunite", "occulto": "okufihliwe", "longdrive": "uhambolude", "satisfactory": "kug<PERSON><PERSON>ayo", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "ngaphansikomhlaba", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pizzatower": "pizzatower", "indiegame": "umdlaloozimele", "itchio": "itchio", "golfit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truthordare": "iqinisono<PERSON>", "game": "umdlalo", "rockpaperscissors": "itshedweniintshepapepa", "trampoline": "ukugxumagxuma", "hulahoop": "<PERSON><PERSON><PERSON><PERSON>", "dare": "ngwazisa", "scavengerhunt": "ukuzingela", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "yiqinisoneqambanga", "beerpong": "ibeerpong", "dicegoblin": "<PERSON><PERSON><PERSON>_ya<PERSON><PERSON><PERSON>", "cosygames": "imidlaloepholile", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "umdlalowamahhala", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "<PERSON><PERSON><PERSON><PERSON>", "mahjong": "mahjong", "jeux": "<PERSON><PERSON><PERSON><PERSON>", "simulationgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "imidlaloengidiniwe", "oyun": "oyun", "interactivegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "midlalo", "giochi": "<PERSON><PERSON><PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "imidlaloyeiphone", "boogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegame": "umdlalowek<PERSON><PERSON>", "hideandseek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hopscotch": "ukugxumagxuma", "arcadegames": "imidlaloyearcade", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "um<PERSON><PERSON><PERSON><PERSON>a", "mindgames": "imidlaloengqondo", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "galagames", "romancegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "amaqhugwana", "4xgames": "imidlalo4x", "gamefi": "gamefi", "jeuxdarcades": "imidlaloyearcade", "tabletopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "imidlalo90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "okungokoqobovsokungamanga", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "gamelinesidlalo", "onlinegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "<PERSON><PERSON><PERSON><PERSON>dlalaindima", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "umdlalopikitsha", "coopgames": "imidlaloedlalwa_n<PERSON>ye", "jenga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wiigames": "<PERSON><PERSON><PERSON><PERSON>", "highscore": "irekhodi_eliphezulu", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON>", "burgergames": "im<PERSON><PERSON><PERSON>ebhe<PERSON>", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "iskeeball", "nfsmwblackedition": "nfsmwuhlobol<PERSON><PERSON>ma", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "umdlalowemibuzo", "gioco": "umdlalo", "managementgame": "umdlalowo<PERSON><PERSON><PERSON>", "hiddenobjectgame": "umdlalowezintowezifihliwe", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "umdlalowefomula1", "citybuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drdriving": "<PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "imidlaloyearcade", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blowgames": "dlalaimoyogames", "pinballmachines": "imishiniyepinball", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "abuzwayo", "gameo": "umdlalo", "lasergame": "umdlalowelaser", "imessagegames": "imidlaloyeimessage", "idlegames": "imidlaloengenzilutho", "fillintheblank": "gc<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxpc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rétrogaming": "ukudlala_kwakudala", "logicgames": "imidlaloengqondo", "japangame": "umdlalowasejapan", "rizzupgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subwaysurf": "<PERSON>ush<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>i", "jeuxdecelebrite": "imidlalolosaziwayo", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5ku5", "rolgame": "umdlalowokuzenza", "dashiegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "<PERSON><PERSON><PERSON>", "gamefps": "umdlalowefps", "textbasedgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "ib<PERSON><PERSON>_la<PERSON><PERSON><PERSON><PERSON>", "retrospel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgame": "umdlalowesela", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "ifliperama", "heroclix": "heroclix", "tablesoccer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tischfußball": "tischfußball", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "imidlaloforamu", "casualgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "umdlalowamas<PERSON>", "cranegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "játék": "umdlalo", "bordfodbold": "i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosorte": "jogosorte", "mage": "umlingo", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "dlalaonline", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "randomizer": "randomizer", "msx": "msx", "anagrammi": "amagrama", "gamespc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "socialdeductiongames": "imidlaloyokucabangela", "dominos": "amadomino", "domino": "idomino", "isometricgames": "imidlaloyeisometric", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "iqinisonesinselelo", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "ukuzingela", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "free2play": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "griyaonline", "driftgame": "umdlalowokusheleleza", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "haloochungechungekanochungechumidlalo", "mushroomoasis": "<PERSON><PERSON><PERSON>amakhowe", "anythingwithanengine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "umdlalowendawozonke", "swordandsorcery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodgamegiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "sidlala", "lab8games": "lab8games", "labzerogames": "imidlalokalabzero", "grykomputerowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "imidlaloemincanyana", "ridgeracertype4": "ridgeraceruhlobo4", "selflovegaming": "ukuzithandaselfdlala", "gamemodding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crimegames": "imidlalobugeben<PERSON>", "dobbelspellen": "dobbelspellen", "spelletjes": "<PERSON><PERSON><PERSON><PERSON>", "spacenerf": "spacenerf", "charades": "imidlaloy<PERSON>ling<PERSON>", "singleplayer": "<PERSON><PERSON><PERSON><PERSON>", "coopgame": "umdlalowokubambisana", "gamed": "dlalile", "forzahorizon": "forzahorizon", "nexus": "ukuhlangana", "geforcenow": "geforcenow", "maingame": "umdlaloomkhulu", "kingdiscord": "inkosikadiscord", "scrabble": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "iludo", "backgammon": "ibhek<PERSON><PERSON>", "onitama": "onitama", "pandemiclegacy": "umfuza<PERSON><PERSON><PERSON><PERSON>", "camelup": "ib<PERSON>bes<PERSON>kangqongqoshe", "monopolygame": "umdlalowemonopoly", "brettspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "umdlalowebhodi", "sällskapspel": "imidlaloyokudlalanabanye", "planszowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "risiko": "ingozi", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombicide", "tabletop": "itafulelokudlalela", "baduk": "baduk", "bloodbowl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cluedo": "icluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectfour": "connectfour", "heroquest": "ukuphephukakweqhaw<PERSON>", "giochidatavolo": "im<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "icarrom", "tablegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "imid<PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "ukuhlanganokukhulu", "creationludique": "ukudalalwenjabulo", "tabletoproleplay": "umdlaloweqhaza<PERSON>", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "intokozelwesabekayo", "switchboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infinitythegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "ukufakwesizwe", "yahtzee": "yahtzee", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "ukuhlangana", "juegodemesa": "umdlalowebhodi", "planszówki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "impilokamlungu", "boardom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "umdlaloweqembu", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "idominó", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "twilightimperium", "horseopoly": "ihhashiopoly", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "izigodlozokuhlanya", "gomoku": "gomoku", "giochidatavola": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "ithunzielasebrimstone", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcaby": "amadama", "táblajátékok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "umkhumbiwempi", "tickettoride": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "starwarslegion", "gochess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "impiinjani", "arksurvivalevolved": "arksurvivalevolved", "dayz": "<PERSON><PERSON><PERSON><PERSON>", "identityv": "identityv", "theisle": "<PERSON><PERSON><PERSON><PERSON>", "thelastofus": "okugcinakwethu", "nomanssky": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "ubizowekacthul<PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eco": "eco", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "daysgone": "izinsukuzidlulile", "fobia": "fobiya", "witchit": "yenza", "pathologic": "isifo", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7daytodie", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "<PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "isimozombiesgame2", "vrising": "vrising", "madfather": "ubabaohlanyayo", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "buyelelaphakade", "pathoftitans": "umkhondoweziqhawe", "frictionalgames": "imid<PERSON><PERSON>yingqa<PERSON>", "hexen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theevilwithin": "ububimng<PERSON><PERSON>le", "realrac": "umjahowangempela", "thebackrooms": "amaka<PERSON><PERSON>_an<PERSON><PERSON><PERSON>", "backrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiressmp": "empiressmp", "blockstory": "indabayokuvimba", "thequarry": "umgodi", "tlou": "tlou", "dyinglight": "ukufakwashaokukhanya", "thewalkingdeadgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "ukuvukakwemibuso", "stateofsurvivalgame": "umdlalowokusinda", "vintagestory": "indabayakudala", "arksurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "barotrauma": "ukucindezeleka", "breathedge": "phe<PERSON><PERSON>la", "alisa": "alisa", "westlendsurvival": "ukusindawestlend", "beastsofbermuda": "izilwanezasebermuda", "frostpunk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "ukusindaekwesabekayo", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "voidtrain", "lifeaftergame": "impiloemuvakomdlalo", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "iphroje<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "khalanyokwesaba", "raft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "polyefileyo", "residentevil8": "residentevil8", "onironauta": "umham<PERSON>_wama<PERSON><PERSON><PERSON>", "granny": "ugogo", "littlenightmares2": "amaphuphoamancaneebusuku2", "signalis": "<PERSON>hawu", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "umdlalowevidiyoirust", "outlasttrials": "ukuhlolwakokuphilaisikhathi", "alienisolation": "ukwahlukaniswakomaliens", "undawn": "ukusanhlamvu", "7day2die": "usuku7ufa2", "sunlesssea": "ulwandleolungenalanga", "sopravvivenza": "ukusinda", "propnight": "ubusukubokushayisana", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "amadodawu<PERSON><PERSON>be", "deathverse": "ukufakomhlaba", "cataclysmdarkdays": "usukuolumnyamakakhulu", "soma": "soma", "fearandhunger": "ukwesabanokulamba", "stalkercieńczarnobyla": "umlandelihlathilasetjernobyl", "lifeafter": "impiloemvakomboo", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "isikhathiumthangala3", "aloneinthedark": "ngedwayedwa", "medievaldynasty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "umdlalonimbus", "eternights": "ubusukubuphakade", "craftopia": "craftopia", "theoutlasttrials": "izivivinyozeoutlast", "bunker": "<PERSON><PERSON><PERSON>", "worlddomination": "ukubusaumhlabawonke", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "oficioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "umbula<PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kushaywa", "wh40": "wh40", "warhammer40klove": "uthando_lwewarhammer40k", "warhammer40klore": "warhammer40klore", "warhammer": "impizempi", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "itemploculex<PERSON>", "vindicare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovesororitas": "ng<PERSON><PERSON><PERSON><PERSON>_o<PERSON>i", "ilovevindicare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "iloveassasinorum": "ngith<PERSON><PERSON><PERSON>", "templovenenum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templocallidus": "lokubandayo", "templomaerorus": "itemplomaerorus", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "kubayiababili", "wingspan": "<PERSON><PERSON><PERSON>", "terraformingmars": "ukwakhaihlanganakazwelonke", "heroesofmightandmagic": "<PERSON><PERSON>haweasemandlanomlingo", "btd6": "btd6", "supremecommander": "umkhokheli_omkhulu", "ageofmythology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args": "amaph<PERSON>swan<PERSON>", "rime": "rhyme", "planetzoo": "iplanetiyezilwane", "outpost2": "isigodlo2", "banished": "x<PERSON>we", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON>", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "phat<PERSON><PERSON><PERSON>e", "warcraft3": "warcraft3", "eternalwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strategygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anno2070": "nyaka2070", "civilizationgame": "umdialowentuthuko", "civilization4": "ubugebengu4", "factorio": "factorio", "dungeondraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spore": "inhlwathi", "totalwar": "impito<PERSON>na", "travian": "travian", "forts": "amafothi", "goodcompany": "inkamp<PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "i<PERSON><PERSON>", "heidentum": "ubuhedeni", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "ngokushesha_kunokukhanya", "forthekings": "<PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "isuqhingomqondongesikha<PERSON>a", "starctaft": "starcraft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "umbusowezigqokoez<PERSON>i", "eu4": "eu4", "vainglory": "ukuziqhenya", "ww40k": "ww40k", "godhood": "ubunkulunkulu", "anno": "<PERSON><PERSON><PERSON>", "battletech": "izimpizobuchwepheshe", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "iklasikadaveyokufundaisalgebralemnandi", "plagueinc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theorycraft": "ukucabangaisimo", "mesbg": "mesbg", "civilization3": "umphakathi3", "4inarow": "4kumugqa", "crusaderkings3": "crusaderkings3", "heroes3": "amaqhawe3", "advancewars": "izimpindabempi", "ageofempires2": "iminyakayombusowamagama2", "disciples2": "abafundi2", "plantsvszombies": "<PERSON><PERSON><PERSON>haloenezombi", "giochidistrategia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stratejioyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "inhliziywensimbi4", "companyofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>qhaw<PERSON>", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gansagansaidada", "phobies": "amaphobia", "phobiesgame": "umdlalowokwesaba", "gamingclashroyale": "umdlalocrashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "i<PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "ngokulandelana", "bomberman": "imbomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "amakhosiwezimpi", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "impiyases<PERSON><PERSON>s", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "isu", "popfulmail": "postayelayeka", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "umdlalowobuchwepheshe", "dysonsphereprogram": "<PERSON><PERSON><PERSON>_l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_sika<PERSON>son", "transporttycoon": "umphathitransport", "unrailed": "ngasukanga", "magicarena": "<PERSON><PERSON>na", "wolvesville": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ooblets": "ooblets", "planescapetorment": "uhluphekolwasezind<PERSON>ni", "uplandkingdoms": "izigodizasezintabeni", "galaxylife": "imp<PERSON>yas<PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "wolvesvillekuinthanethi", "slaythespire": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlecats": "amakatiemp<PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "isims", "simcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "ngidingaisukazinyecarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreedlala", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "ukulahlekelwasims4", "fnaf": "fnaf", "outlast": "ng<PERSON><PERSON>u", "deadbydaylight": "ngif<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "alice<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "inkhabakaziyavelanga", "phasmophobia": "ukwesabaimoya", "fivenightsatfreddys": "<PERSON><PERSON><PERSON><PERSON><PERSON>hl<PERSON>kafreddy<PERSON>", "saiko": "saiko", "fatalframe": "u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "amaphuphoaman<PERSON><PERSON>", "deadrising": "uk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "ng<PERSON><PERSON><PERSON>", "deadisland": "deadisland", "litlemissfortune": "litlemissfortune", "projectzero": "iphrojekthi_zero", "horory": "horory", "jogosterror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "sawubonaumakhelwani2", "gamingdbd": "ukudlaladbd", "thecatlady": "umamawekati", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "codenames": "amagamafihliwe", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "ieuchre", "thegwent": "ig<PERSON>t", "legendofrunetera": "ing<PERSON><PERSON>", "solitaire": "isolitaire", "poker": "<PERSON><PERSON><PERSON><PERSON>", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON>", "playingcards": "<PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "<PERSON><PERSON><PERSON><PERSON>", "netrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "am<PERSON><PERSON><PERSON>_epokemon", "fleshandbloodtcg": "inyamayegazi", "sportscards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duellinks": "amaduellinks", "spades": "ispedi", "warcry": "uk<PERSON><PERSON>zel<PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "ilotho", "hanafuda": "hana<PERSON>da", "theresistance": "ukumelana", "transformerstcg": "itransformerstcg", "doppelkopf": "idoppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "impi_disk", "yugiohgame": "umdialokayugioh", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "amehlobluwelidragonelihloph", "yugiohgoat": "yugiohmdlalowegolide", "briscas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegocartas": "umdl<PERSON><PERSON>makhadi", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgum<PERSON><PERSON>", "cotorro": "ixoxo", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "umdl<PERSON><PERSON>makhadi", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "<PERSON><PERSON><PERSON><PERSON>", "battlespirits": "imoyayokulwa", "battlespiritssaga": "umlandomlolozishwebhu", "jogodecartas": "umdl<PERSON><PERSON>makhadi", "žolíky": "<PERSON><PERSON><PERSON><PERSON>", "facecard": "ubuso", "cardfight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magiccartas": "imilandoyo<PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "amah<PERSON>ng<PERSON><PERSON><PERSON><PERSON>", "cyberse": "icyberse", "classicarcadegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "ul<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>di", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "iphrojekthi_mirai", "projectdiva": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "djmax": "djmax", "guitarhero": "iqhawelomculo", "clonehero": "clonehero", "justdance": "g<PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "dancecentral", "rhythmgamer": "umdlaliweziculo", "stepmania": "stepmania", "highscorerythmgames": "amaphumuscoreamaphezuluemdlalweniwezirhythm", "pkxd": "pkxd", "sidem": "hlangothi", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "umsindovoltex", "rhythmheaven": "iparadisiyomculo", "hypmic": "hypmic", "adanceoffireandice": "umdansowomliloweqhwa", "auditiononline": "hlolwangabuonline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptoyabafileyo", "rhythmdoctor": "udokt<PERSON><PERSON><PERSON><PERSON>lo", "cubing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wordle": "i<PERSON>le", "teniz": "<PERSON><PERSON><PERSON>", "puzzlegames": "imid<PERSON><PERSON><PERSON><PERSON><PERSON>", "spotit": "ubone", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "imicabangokuxa<PERSON><PERSON>la", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "imicabangomqondo", "rubikscube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motscroisés": "<PERSON>az<PERSON><PERSON><PERSON><PERSON>", "krzyżówki": "amagama", "nonogram": "nonogram", "bookworm": "umfundisincwadi", "jigsawpuzzles": "amaphazulijigsaw", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "<PERSON><PERSON><PERSON><PERSON>", "riddles": "amaq<PERSON>a", "rompecabezas": "<PERSON><PERSON><PERSON><PERSON>", "tekateki": "<PERSON><PERSON><PERSON><PERSON>", "inside": "<PERSON><PERSON><PERSON><PERSON>", "angrybirds": "izinyonie<PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minesweeper": "umgqibandlela", "puzzleanddragons": "i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "umdlalowegardenscapes", "puzzlesport": "umdlalopuzzle", "escaperoomgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapegame": "umdlalowokubaleka", "3dpuzzle": "iphazili3d", "homescapesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordsearch": "ukucingafamagama", "enigmistica": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kulaworld": "kulaworld", "myst": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riddletales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "fishdom", "theimpossiblequiz": "umbuzowokwehluleka", "candycrush": "candycrush", "littlebigplanet": "umdlaloweplanethencaneyinkulu", "match3puzzle": "umdlalowokufanisaoku3", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON>ahl<PERSON><PERSON>", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yapboz": "<PERSON><PERSON><PERSON><PERSON>", "thetalosprinciple": "umgomowetalos", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "qbert": "qbert", "riddleme": "ngibuzabuza", "tycoongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cruciverba": "amagamaaphancazelayo", "ciphers": "<PERSON><PERSON><PERSON><PERSON>", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "ukuxazululaizinkinga", "turnipboy": "ujanawetenisi", "adivinanzashot": "ukuqagelelaunxalenxale", "nobodies": "abangewona", "guessing": "qagela", "nonograms": "amanonogram", "kostkirubika": "kostkirubika", "crypticcrosswords": "<PERSON>aga<PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia2": "syberia2", "puzzlehunt": "ukuzingelaumphicabango", "puzzlehunts": "amapuzzlehunt", "catcrime": "ubugelekezibekati", "quebracabeça": "inqhaqhamba", "hlavolamy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "autodefinidos": "ngizizik<PERSON><PERSON><PERSON>", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "<PERSON><PERSON><PERSON><PERSON>", "untitledgoosegame": "id<PERSON>jun<PERSON><PERSON>nji<PERSON>", "cassetête": "ngicindezelekile", "limbo": "<PERSON><PERSON><PERSON>i", "rubiks": "irabiks", "maze": "<PERSON><PERSON><PERSON><PERSON>", "tinykin": "okuncane", "rubikovakostka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "speedcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pieces": "<PERSON><PERSON><PERSON><PERSON>", "portalgame": "umdlaloweportali", "bilmece": "bilmece", "puzzelen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "picross": "ipicross", "rubixcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinelli": "imicabango", "cubomagico": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "indawoyokumangala", "monopoly": "imonopholi", "futurefight": "impiexakusasa", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "impisi_yodwa", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "izinkanyezizeqembu", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "izinkanyezizealchemy", "stateofsurvival": "isimoboku<PERSON><PERSON>", "mycity": "<PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "isiteji<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefensezu", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "phambilipham<PERSON><PERSON>", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guardiantales": "guardiantales", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "itacticool", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON>", "craftsman": "<PERSON><PERSON><PERSON><PERSON>", "supersus": "<PERSON><PERSON><PERSON><PERSON>", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "q<PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "impiyombhede", "freefire": "freefire", "mobilegaming": "imidumelokudlalela", "lilysgarden": "ingadikalilily", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "it<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clashofclans": "impinamaqembu", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callof<PERSON><PERSON><PERSON><PERSON><PERSON>", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "indawoyezimo", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON>e", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "nyakazisafudumele", "ml": "ml", "bangdream": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clashofclan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatstar": "umculi", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "androidgames": "imidlaloyeandroid", "criminalcase": "icalazobugebengu", "summonerswar": "summonerswar", "cookingmadness": "ukupheka<PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "ukuqhekekaum<PERSON>zo", "leagueofangels": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "ifuneural", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "warrobots", "mirrorverse": "isibukongalo", "pou": "pou", "warwings": "izimpikozimpi", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "indabaonke", "futime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "antiyoy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "apexlegendmobile": "apexlegendmobile", "ingress": "ngena", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "umdlalowezimoneyeni", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "izilwanezik<PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "<PERSON><PERSON><PERSON><PERSON>", "wolfy": "impisi", "runcitygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegodemovil": "umdlaloweselfow<PERSON>", "avakinlife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kogama": "kogama", "mimicry": "ukulingisa", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "is<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_ng<PERSON><PERSON>a", "grandchase": "grandchase", "bombmebrasil": "bhombamisabrazil", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "ubizol<PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "nikkinokubenyezela", "carxdriftracing2": "imotoedriftherebhayise2", "pathtonowhere": "indlelayokungaphi", "sealm": "sealm", "shadowfight3": "ukulwaizithunzi3", "limbuscompany": "inkampaniyelimbus", "demolitionderby3": "ukushayanakwezimotozokubhidliza3", "wordswithfriends2": "amagamangabangani2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showbyrock": "k<PERSON><PERSON>angerocki", "ladypopular": "intombazanethandwa", "lolmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiresandpuzzles": "imibusona<PERSON><PERSON>", "empirespuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "idolobheki<PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "impizemidlalozelulaindia", "fanny": "inunu", "littlenightmare": "iphuphoelincane", "aethergazer": "aethergazer", "mudrunner": "umgi<PERSON><PERSON>_o<PERSON>eni", "tearsofthemis": "izinyembezizokwahlulela", "eversoul": "umphefu<PERSON><PERSON><PERSON><PERSON>", "gunbound": "<PERSON><PERSON><PERSON><PERSON>", "gamingmlbb": "ukudlalamlbb", "dbdmobile": "dbdmobile", "arknight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "izidumbuezihambahamba", "eveechoes": "eveechoes", "jogocelular": "umdial<PERSON><PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "<PERSON><PERSON><PERSON>aib<PERSON><PERSON>", "girlsfrontline": "amantombazanephambilikomgqa", "jurassicworldalive": "jurassicworldalive", "soulseeker": "umfunisisiphefumulo", "gettingoverit": "ngiyakhohlwa", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "indabayasenyangeni", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofneverland": "inganekwaseyakwaneverland", "pubglite": "pubglite", "gamemobilelegends": "umdlalowamaselulamobile", "timeraiders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "amakatiemp<PERSON>", "dnd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quest": "<PERSON><PERSON><PERSON><PERSON>", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "umdlalowetrpgtraveller", "2300ad": "2300ad", "larp": "umdlalowendawo", "romanceclub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "d20": "d20", "pokemongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonbomvu", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "umlebe", "porygon": "iporygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "phapha<PERSON>a", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "amaph<PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokeball": "ipokeball", "charmander": "<PERSON><PERSON><PERSON><PERSON>", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "ipokemonecwebezelayo", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "izandlazensimbi", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>a", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "izinganekanye_ipokemon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "umzingelishinys<PERSON>", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "amant<PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "worldblitz", "jeudéchecs": "jeudéchecs", "japanesechess": "ishogijapani", "chinesechess": "is<PERSON><PERSON><PERSON>", "chesscanada": "chesscanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON><PERSON>", "rook": "iphini", "chesscom": "chesscom", "calabozosydragones": "imigo<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "umphathi<PERSON>nge<PERSON>", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "inganekamayevoxma<PERSON>", "doungenoanddragons": "dungeonanddragons", "darkmoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "umqhudelwanowabadlalibaseminecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "<PERSON>cimb<PERSON><PERSON><PERSON><PERSON><PERSON>", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "amamodiminecraft", "mcc": "mcc", "candleflame": "ilangabikha<PERSON>le<PERSON>", "fru": "fru", "addons": "izangezelelwezo", "mcpeaddons": "mcpeizenge<PERSON>", "skyblock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftoshintshiwe", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "phak<PERSON><PERSON>z<PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgamer": "<PERSON>dl<PERSON><PERSON>kh<PERSON><PERSON><PERSON><PERSON>", "jeuxvideo": "<PERSON><PERSON><PERSON><PERSON>", "gambit": "i<PERSON><PERSON><PERSON>", "gamers": "<PERSON><PERSON><PERSON><PERSON>", "levelup": "ngenyukelaphezulu", "gamermobile": "umdlalowesel<PERSON>", "gameover": "phelile", "gg": "gg", "pcgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamen": "dlala", "oyunoynamak": "dlala", "pcgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "igamingsetup", "pcmasterrace": "pcubabazulu", "pcgame": "<PERSON>dlalo<PERSON>kh<PERSON><PERSON><PERSON><PERSON>", "gamerboy": "umfanawem<PERSON><PERSON><PERSON>", "vrgaming": "umdlalowevr", "drdisrespect": "drdisrespect", "4kgaming": "imidlalo4k", "gamerbr": "umfanawem<PERSON><PERSON><PERSON>", "gameplays": "ukudlalwagembani", "consoleplayer": "umdlaliconsole", "boxi": "boxi", "pro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "epicgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "ukudlalaonline", "semigamer": "umgeyimakancane", "gamergirls": "amantombazanedlala", "gamermoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerguy": "umdlaliwemdlalo", "gamewatcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "um<PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "amantombazanedlala", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mallugaming": "imidlaloasemallu", "pawgers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quests": "imisebenzi", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygaming": "ukudlalaokupholile", "gamelpay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsswitch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "competitivegaming": "umdlalowomncintiswano", "minecraftnewjersey": "minecraftnewjersey", "faker": "umzenzi<PERSON>", "pc4gamers": "pc4gamers", "gamingff": "imidlalo_ff", "yatoro": "yatoro", "heterosexualgaming": "imidlaloengqondoqo", "gamepc": "<PERSON>dlalo<PERSON>kh<PERSON><PERSON><PERSON><PERSON>", "girlsgamer": "amantombazanadlala", "fnfmods": "fnfmods", "dailyquest": "umsebenziwansukuzonke", "gamegirl": "umdlaliwemdlalo", "chicasgamer": "amantombazanedlala", "gamesetup": "ukusethagemu", "overpowered": "ngamandlakakhulu", "socialgamer": "umdlalisocial", "gamejam": "gamejam", "proplayer": "umdlaliomkhulu", "roleplayer": "umdlaliwendawo", "myteam": "<PERSON><PERSON><PERSON><PERSON>", "republicofgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aorus": "aorus", "cougargaming": "ugogodlala", "triplelegend": "inqop<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerbuddies": "abanganebamageymu", "butuhcewekgamers": "butuhintombigamers", "christiangamer": "<PERSON>dl<PERSON>wekh<PERSON><PERSON>", "gamernerd": "umdl<PERSON><PERSON><PERSON>dala", "nerdgamer": "ubugebengu", "afk": "angi<PERSON>o", "andregamer": "andregamer", "casualgamer": "umdlalizithandayo", "89squad": "89squad", "inicaramainnyagimana": "ngingayifakakanjaniinceku", "insec": "ukung<PERSON><PERSON><PERSON><PERSON>", "gemers": "amage<PERSON>a", "oyunizlemek": "<PERSON><PERSON><PERSON><PERSON>", "gamertag": "igama_lomdlali", "lanparty": "lanparty", "videogamer": "umdlaliamavidiyo", "wspólnegranie": "d<PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "umdlaliweplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "umdlalowes<PERSON>", "gtracing": "ukujaha_amagt", "notebookgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "protogen": "protogen", "womangamer": "umdlalindikosazane", "obviouslyimagamer": "ngingumgadl<PERSON>mp<PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON>", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zerobalek<PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "umculowenintendo", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "shintsha", "zelda": "zelda", "smashbros": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofzelda": "ing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ukufit<PERSON><PERSON><PERSON><PERSON>", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "skychildrenofthelight", "tomodachilife": "impiloyobungane", "ahatintime": "isigqokoesifinyelelayo", "tearsofthekingdom": "izinyembezizombuso", "walkingsimulators": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "inyangayokuvuna", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "umoyaweganga", "myfriendpedro": "umnganimpedro", "legendsofzelda": "ubuqhawezeldazelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "imidlalo51", "earthbound": "<PERSON><PERSON><PERSON><PERSON>", "tales": "izindaba", "raymanlegends": "raymanlegends", "luigismansion": "indlukaluigi", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "isuhlongqondezintathu", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendos": "onintendo", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "umariouhlangothiyezizenkhanyezi", "marioandsonic": "umarioenosonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "<PERSON>zinjay<PERSON><PERSON><PERSON>", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "marion<PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON>lamabomvu", "vanillalol": "vanillalol", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspain", "aatrox": "aatrox", "euw": "yhu", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "inyanga", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "g<PERSON><PERSON><PERSON>", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "amasangohexu", "hextech": "hextech", "fortnitegame": "igemuyelwefortnitegame", "gamingfortnite": "igemingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "imid<PERSON><PERSON><PERSON><PERSON>ya<PERSON><PERSON>", "scaryvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "umenziwamageymu", "megamanzero": "megamanzero", "videogame": "umdlalo", "videosgame": "<PERSON><PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arcades": "<PERSON><PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "umdlalowo<PERSON>lima", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "robloxisizulu", "erlc": "erlc", "sanboxgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "irollerdrome", "parasiteeve": "parasiteeve", "gamecube": "igamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "iphuphoscape", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "ugrandtheftauto", "deadspace": "indawoefileyo", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "imid<PERSON><PERSON>_yevidiyo", "theoldrepublic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "videospiele": "imid<PERSON><PERSON>_yevidiyo", "touhouproject": "touhouproject", "dreamcast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventuregames": "imidlaloyokuzijabulisa", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "inda<PERSON><PERSON>ink<PERSON><PERSON>", "retrogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "ikh<PERSON><PERSON><PERSON><PERSON>a", "retrogaming": "imid<PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagegaming": "im<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "<PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "izimbunguluzidla", "injustice2": "ukungewabulungelwayo2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "impilokahle", "beatmaniaiidx": "beatmaniaiidx", "steep": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mystgames": "imidlaloengalungile", "blockchaingaming": "imidlaloyeblockchain", "medievil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "consolegaming": "umdlalowa<PERSON>kh<PERSON><PERSON>", "konsolen": "khon<PERSON>i", "outrun": "bale<PERSON>a", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "ukudlalaukwesaba", "monstergirlquest": "umbuthowamantokazanewezilwane", "supergiant": "iqhawelandoda", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "<PERSON>dl<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "imidlalojackbox", "interactivefiction": "indabaehlangeneyo", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "inovelihyokubuka", "visualnovels": "izinganekwanezibonakalayo", "rgg": "rgg", "shadowolf": "<PERSON>ts<PERSON><PERSON><PERSON>", "tcrghost": "tcrghost", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "isigcino_sokudlala", "aestheticgames": "imid<PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "inovelihlukile", "thecrew2": "abangane2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "umdlalowasendle", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "uklamamaleveli", "starrail": "umsikau<PERSON><PERSON><PERSON><PERSON>", "keyblade": "<PERSON><PERSON><PERSON>_yokuvula", "aplaguetale": "is<PERSON>oese<PERSON><PERSON>", "fnafsometimes": "fnafsometimes", "novelasvisuales": "amanovelihlawumbonob<PERSON>o", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videojuejos": "<PERSON><PERSON><PERSON><PERSON>", "videogamedates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "nje3", "hulkgames": "imidlaloyehulk", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "indluehlanya", "crashracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "ama3dplatformers", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "imid<PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "kweqa<PERSON>msakazweni", "beyondtwosouls": "ngaléthalamaphefumulo", "gameuse": "umdlalo", "offmortisghost": "offmortisghost", "tinybunny": "unogwajandwana", "retroarch": "retroarch", "powerup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "izenzoyesithombeezinhle", "quickflash": "<PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "ug<PERSON><PERSON><PERSON>", "powerwashsim": "ukugezaisimulator", "coralisland": "coralisland", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "i<PERSON><PERSON><PERSON><PERSON><PERSON>anisi<PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "um<PERSON><PERSON>_we<PERSON><PERSON><PERSON>", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "insimbiyang<PERSON>ke", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simu<PERSON><PERSON>a", "symulatory": "symulatory", "speedrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "epicx": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "superrobottaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "umdlalowesabekayo", "wonderlandonline": "wonderlandonline", "skylander": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "toontownibhalwe<PERSON>", "simracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simrace": "umdlalowokulinga", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "inhliziyo", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "umg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "uk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "ukunikelanakwekain", "hackandslash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "ukud<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "isilinganisoselokulayish<PERSON>loli", "horizonworlds": "imimomhlaba", "handygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "izinganekwan<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolvideogames": "imidlaloyama<PERSON><PERSON><PERSON>", "racingsimulator": "umdlalowokulinga_ukushayela", "beemov": "bee<PERSON>v", "agentsofmayhem": "amaajentiokubhuntshwa", "songpop": "umculowedlalwayo", "famitsu": "famitsu", "gatesofolympus": "amasangolwaseolympus", "monsterhunternow": "monsterhunternow", "rebelstar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogaming": "imidlaloekumpyuter", "indiegaming": "imidlaloezimele", "indievideogames": "imidlaloevidiyo_ezizimele", "indievideogame": "umdialoweqoqoelin<PERSON>", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermanongafikilibudongo", "bufffortress": "inqabayamandla", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "iprojekthi", "futureclubgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugman": "inkomishimugshot", "insomniacgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "imidlaloenkhulukunene", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "umdlaloweceleste", "aperturescience": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backlog": "umthwalowez<PERSON><PERSON><PERSON><PERSON>", "gamebacklog": "imidlaloengakaqedwa", "gamingbacklog": "imidlaloengakaqedwa", "personnagejeuxvidéos": "umdlalowevidiyoomlingiswa", "achievementhunter": "umzingeliwemp<PERSON><PERSON><PERSON>", "cityskylines": "amado<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deponia": "deponia", "naughtydog": "injayang<PERSON>lile", "beastlord": "inkosiyezilwane", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "isip<PERSON><PERSON><PERSON><PERSON>", "staxel": "staxel", "videogameost": "<PERSON><PERSON>dowevidiyo", "dragonsync": "idragonihlangana", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ngiyakuthandakofxv", "arcanum": "<PERSON><PERSON><PERSON><PERSON>", "neoy2k": "neoy2k", "pcracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "berserk": "h<PERSON>ya", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animeyokubuhlungu", "darkerthanblack": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_anime", "animewithplot": "animengenhlelo", "pesci": "pesci", "retroanime": "retroanime", "animes": "amaan<PERSON>", "supersentai": "amase<PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80s", "90sanime": "ianimeyama90s", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000s", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneisizhinsokuqala", "rapanime": "rapanime", "chargemanken": "<PERSON><PERSON><PERSON><PERSON>gx<PERSON><PERSON>", "animecover": "animecover", "thevisionofescaflowne": "umbonowescaflowne", "slayers": "aba<PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "inhlanzilikabhanana", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "<PERSON><PERSON><PERSON><PERSON>", "fireforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "idayeriyekusasa", "fairytail": "inganekwane", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "kwenziweeku<PERSON>len<PERSON>", "parasyte": "inambuzane", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "manga_yesabe<PERSON>yo", "romancemangas": "amanovelihothando", "karneval": "<PERSON><PERSON><PERSON><PERSON>", "dragonmaid": "intombiyodrago", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "inkombaethizeyomlingo", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "izingelosizokufa", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "intombazanezisilwane", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "upopojiwezemidlalo", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "indabakatanyaomubi", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "umfananesilwan<PERSON>", "fistofthenorthstar": "inqindi_yenka<PERSON><PERSON><PERSON>_ya<PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "indlelayokugcinaumummy", "fullmoonwosagashite": "inyangayigcweleuyifuna", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "isiqongophezulu", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "intombayakhewamascore", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "izimpundulu", "shinji": "shinji", "zerotwo": "zerotubili", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "intombazaneisilokazane", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "iveni", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "umatilozisaturni", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "gijima", "oldanime": "anime_yakudala", "chainsawman": "umuntowesaha", "bungoustraydogs": "bungoustraydogs", "jogo": "umdlalo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "ink<PERSON><PERSON>", "loli": "intombazanyana", "horroranime": "i<PERSON><PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "isasivothecaneverland", "monstermanga": "imangayezilwane", "yourlieinapril": "inangayak<PERSON><PERSON>reli", "buggytheclown": "ubuggyclown", "bokunohero": "bokunohero", "seraphoftheend": "ingelo<PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "umlingo", "deepseaprisoner": "imbonjweyolwan<PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "indawoyabafilelimangazayo", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "umdlalokadarwin", "husbu": "myeni", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "izinhliziyo_zikapandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "izimpifwokudla", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "kuzephakade", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yuki": "yuki", "erased": "cisusiwe", "bluelock": "bluelock", "goblinslayer": "umbulaliwamagoblin", "detectiveconan": "udetectiveconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "blueexorcist", "slamdunk": "ngqophaigoli", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "ngakhala", "spyfamily": "umndeniwabahloli", "airgear": "airgear", "magicalgirl": "intombazanemlingo", "thesevendeadlysins": "izonoeziyisikhombulezingabulala", "prisonschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "unkulunkuluwesikoleesiphakeme", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "igrandblue", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "indawozonkezekhathuni", "swordartonlineabridge": "sword<PERSON><PERSON>lineib<PERSON><PERSON>o", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "ungafileungathembekile", "romancemanga": "<PERSON><PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "senpai": "usenpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayerkupheleinkemba", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "is<PERSON><PERSON><PERSON>_somlilo", "adioseri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "izinkanyeziziyahlangana", "romanceanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON>othulwa", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "highschoolofthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "iteknoyasejalimane", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "ngoku<PERSON><PERSON><PERSON><PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "ibangafababuli", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "umgidiwokufa", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "upopayi_wasejapan", "animespace": "indawoyeanimu", "girlsundpanzer": "am<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "umdlalowezodumezulu", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "ng<PERSON><PERSON><PERSON><PERSON>", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "igundane", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "umfananekati", "gashbell": "gashbell", "peachgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "inhliziywemanga", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "irekhodi_yer<PERSON><PERSON>ok", "funamusea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "imanganeama_anime", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialinzima", "overgeared": "ngigqoke_kakhulu", "toriko": "<PERSON><PERSON>o", "ravemaster": "umgidi<PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "umlayowezikhundla", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "amaconsolwokunkulunkulu", "loscaballerosdelzodia": "amadodanezodiac", "animeshojo": "animeshojo", "reverseharem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "uthishaomkhuluonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "isoldato", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodplus": "<PERSON><PERSON><PERSON>", "bloodplusanime": "iga<PERSON>_kanye_ne_anime", "bloodcanime": "igazi_le_anime", "bloodc": "<PERSON><PERSON><PERSON>", "talesofdemonsandgods": "izindabazamadimoninonkulunkulu", "goreanime": "goreianime", "animegirls": "amant<PERSON><PERSON><PERSON><PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatteranime": "inkembalwezokupopakazwe", "splatter": "chitha", "risingoftheshieldhero": "ukuvukaweqhawelokuvikela", "somalianime": "ianimenasesomalia", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "ubulalaozothathiwe", "animeyuri": "animeyuri", "animeespaña": "animespain", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "izinganezemimkhomo", "liarliar": "amangaamanga", "supercampeones": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animeidols": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "izinsukuzal<PERSON>laza", "magicalgirls": "amantombazaneamkhulu", "callofthenight": "ubizolwasebus<PERSON>", "bakuganbrawler": "umdlalibakugan", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "intombazanezikamagivu", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "thola<PERSON>ga", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "inkundlayasothando", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeverse", "persocoms": "<PERSON><PERSON><PERSON><PERSON>", "omniscientreadersview": "umbukozifundayowazozonke", "animecat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animerecommendations": "izincomothelwelaanime", "openinganime": "vulumanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "<PERSON>da<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "amagundams", "voltesv": "ngivotela", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "ikhodi_geass", "mobilefighterggundam": "umshiniwokulwaweselula", "neonevangelion": "neonevangelion", "mobilesuitgundam": "it<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mech": "<PERSON><PERSON><PERSON>", "eurekaseven": "eurekaseven", "eureka7": "ngitholile7", "thebigoanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bleach": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deathnote": "incwadiyokufa", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "greenranger": "greenranger", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drstone": "<PERSON>he<PERSON><PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "umdlalowabangane", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "abacwaningi", "onepieceanime": "onepieceanime", "attaquedestitans": "ukuhlaselakwamatitani", "theonepieceisreal": "itheyaisrealonepiece", "revengers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "umthelelajabula", "digimonstory": "indabayedigimon", "digimontamers": "digimontamers", "superjail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "imetalaloku<PERSON>la", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kemonofriends": "izihlobozilwane", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "ngoba", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "sitalolonkeizinkulu", "recuentosdelavida": "izindabazokuphila"}