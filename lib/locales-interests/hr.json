{"2048": "2048", "mbti": "mbti", "enneagram": "eneagram", "astrology": "astrologija", "cognitivefunctions": "kognitivnefunkcije", "psychology": "psihologija", "philosophy": "filozofija", "history": "povije<PERSON>", "physics": "fizika", "science": "znanost", "culture": "kultura", "languages": "jez<PERSON>", "technology": "tehnologija", "memes": "memeovi", "mbtimemes": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "astrologymemes": "astrologijskimemeovi", "enneagrammemes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showerthoughts": "<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "smiješno", "videos": "<PERSON><PERSON><PERSON><PERSON>", "gadgets": "gadgeti", "politics": "politika", "relationshipadvice": "savjetzaveze", "lifeadvice": "životnisavjeti", "crypto": "kripto", "news": "vijesti", "worldnews": "vijesti", "archaeology": "arheologija", "learning": "učenje", "debates": "rasprave", "conspiracytheories": "teorijezavjere", "universe": "svemir", "meditation": "meditacija", "mythology": "mitologija", "art": "umjetnost", "crafts": "rukotvorine", "dance": "ples", "design": "<PERSON><PERSON><PERSON>", "makeup": "šminka", "beauty": "l<PERSON><PERSON>a", "fashion": "moda", "singing": "<PERSON><PERSON><PERSON><PERSON>", "writing": "pisanje", "photography": "fotografija", "cosplay": "cosplay", "painting": "slikanje", "drawing": "crtanje", "books": "knjige", "movies": "filmovi", "poetry": "poezija", "television": "televizija", "filmmaking": "<PERSON><PERSON><PERSON><PERSON>", "animation": "animacija", "anime": "anime", "scifi": "znanstvenafantastikaboo", "fantasy": "fantazija", "documentaries": "dokumentarci", "mystery": "<PERSON><PERSON><PERSON>", "comedy": "komedija", "crime": "<PERSON><PERSON><PERSON><PERSON>", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horor", "romance": "romansa", "realitytv": "realityshow", "action": "ak<PERSON><PERSON>", "music": "glazba", "blues": "blues", "classical": "klasika", "country": "država", "desi": "desi", "edm": "edm", "electronic": "elektronika", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "k<PERSON><PERSON>a", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "tehno", "travel": "<PERSON><PERSON><PERSON>", "concerts": "<PERSON><PERSON><PERSON><PERSON>", "festivals": "festivali", "museums": "muzeji", "standup": "standup", "theater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outdoors": "pri<PERSON>a", "gardening": "vrtlarenje", "partying": "<PERSON><PERSON><PERSON>", "gaming": "gaming", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "tamniceidraguni", "chess": "<PERSON>ah", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "hrana", "baking": "pečenje", "cooking": "kuh<PERSON><PERSON>", "vegetarian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegan": "vegan", "birds": "ptice", "cats": "<PERSON><PERSON><PERSON>", "dogs": "psi", "fish": "riba", "animals": "životinje", "blacklivesmatter": "crnizivotivrijede", "environmentalism": "ekologija", "feminism": "feminizam", "humanrights": "<PERSON>juds<PERSON><PERSON><PERSON>", "lgbtqally": "lgbtqsaveznik", "stopasianhate": "stopazijskojmržnji", "transally": "transprijatelj", "volunteering": "volontiranje", "sports": "sport", "badminton": "badminton", "baseball": "bejzbol", "basketball": "<PERSON><PERSON><PERSON><PERSON>", "boxing": "boks", "cricket": "kriket", "cycling": "bicikliranje", "fitness": "fitness", "football": "nogomet", "golf": "golf", "gym": "teretana", "gymnastics": "gimnast<PERSON>", "hockey": "hokej", "martialarts": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "netbal", "pilates": "pilates", "pingpong": "pingpong", "running": "trčanje", "skateboarding": "skejt", "skiing": "<PERSON><PERSON><PERSON>", "snowboarding": "snowboarding", "surfing": "<PERSON><PERSON><PERSON>", "swimming": "plivanje", "tennis": "tenis", "volleyball": "od<PERSON><PERSON><PERSON>", "weightlifting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "joga", "scubadiving": "ronjenje", "hiking": "planinarenje", "capricorn": "jarac", "aquarius": "vodenjak", "pisces": "ribe", "aries": "ovan", "taurus": "bik", "gemini": "gemini", "cancer": "rak", "leo": "lav", "virgo": "<PERSON><PERSON><PERSON><PERSON>", "libra": "vaga", "scorpio": "škorpion", "sagittarius": "sagittarius", "shortterm": "kratkoročno", "casual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "longtermrelationship": "dugavezesubuducnost", "single": "samac", "polyamory": "poliamor", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gej", "lesbian": "lezbijka", "bisexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pansexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "asexual": "aseksualan", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kingsquest", "soulreaver": "dušokradica", "suikoden": "su<PERSON><PERSON>", "subverse": "subverzija", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelike", "syberia": "sibirija", "rdr2": "rdr2", "spyrothedragon": "spy<PERSON>z<PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "zalazaksunca", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "otvorenisvjet", "heroesofthestorm": "herojestioljuje", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "tamnicepuzanje", "jetsetradio": "jetsetradio", "tribesofmidgard": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "planescape", "lordsoftherealm2": "gospodaricarstava2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "medabots": "meda<PERSON><PERSON>i", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "imerzivnesimulacije", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON>", "witcher": "čarobnjak", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modiranje", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "imerzivno", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldskul", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidnamotivacija", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "ludiodzaljubljen", "otomegames": "otomeg<PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirthemasquerade", "dimension20": "dimension20", "gaslands": "benzinskazemlja", "pathfinder": "tragač", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "shadowrun", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "ljubavnikki", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "jurišgravitacije", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "jedanpo<PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "overlord", "yourturntodie": "tvojrednadaumrijet", "persona3": "persona3", "rpghorror": "rpghoror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "sklonište", "gurps": "gurps", "darkestdungeon": "najc<PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "eklipsafaza", "disgaea": "disgaea", "outerworlds": "vanjskisvjetovi", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dinastijskiratnici", "skullgirls": "skullgirls", "nightcity": "noćnigrad", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "ludaborba", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelike", "gothamknights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "zaboravljenakraljevstva", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "trtačkazemlja", "childoflight": "dijetesvjetlosti", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "uzgajateljudovišta", "ecopunk": "ekopank", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "slomljenapijestolja", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "svakaoaza", "hogwartmystery": "hogwartsm<PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "pogodi", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "zvjezdotražitelj", "goldensun": "zlatnosunce", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "oštriceu<PERSON><PERSON>", "twilight2000": "sumrak2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkcrveni", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "zlozemlje", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "vragjiprezivio", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "božanstvenost", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "tugezastarimsvijetom", "adventurequest": "avanturistickiquest", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "igre_uloga", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofsymphonia": "pričeizumfonija", "honkaistarrail": "honkaistarrail", "wolong": "voloong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "rascijepigrad", "myfarog": "myfarog", "sacredunderworld": "svetipodzemnisvjet", "chainedechoes": "vezan<PERSON>dl<PERSON>si", "darksoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "<PERSON><PERSON><PERSON>", "othercide": "drugastrana", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "kronotrigger", "pillarsofeternity": "stupoviviječnosti", "palladiumrpg": "palladiumrpg", "rifts": "pukotine", "tibia": "tibia", "thedivision": "thedivision", "hellocharlotte": "bokšarlota", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirskamaskerada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "srcemotor", "fable3": "fable3", "fablethelostchapter": "fablebiznesenikapitol", "hiveswap": "hiveswap", "rollenspiel": "<PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "<PERSON><PERSON><PERSON>", "edeneternal": "edene<PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "starfield", "oldschoolrevival": "oldschoolpovratak", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "divljisvijetovi", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kraljevstvosrca1", "ff9": "ff9", "kingdomheart2": "kingdomhearts2", "darknessdungeon": "tamničarskitamnica", "juegosrpg": "rpgigre", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomhearts3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klanmalkavian", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "divljasrca", "bastion": "bastion", "drakarochdemoner": "zmajekamenedemonima", "skiesofarcadia": "nebesaarcadie", "shadowhearts": "sjenovitasrca", "nierreplicant": "nierreplicant", "gnosia": "gnozija", "pennyblood": "g<PERSON>šićzakrv", "breathoffire4": "breathoffire4", "mother3": "majka3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "igre_uloga", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "srceviještice", "harrypottergame": "harrypottergame", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirskimaskenbal", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "kronokriž", "cocttrpg": "cocttrpg", "huntroyale": "lovkraljevski", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "shadowheartszavjet", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "kraljevst<PERSON><PERSON><PERSON><PERSON>", "awplanet": "awplanet", "theworldendswithyou": "svijetzavršavastobom", "dragalialost": "dragaliapropala", "elderscroll": "starizvitak", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "mra<PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "zemaljskamagija", "blackbook": "crnaknjiga", "skychildrenoflight": "skychildrenoflight", "gryrpg": "gryrpg", "sacredgoldedition": "svetozlatnaizdanja", "castlecrashers": "razbijačidvoraca", "gothicgame": "gotičkaigra", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "igranjerpg", "prophunt": "lov<PERSON>ap<PERSON>roke", "starrails": "zvjezdanetračnice", "cityofmist": "gradma<PERSON>", "indierpg": "indierpg", "pointandclick": "pokaziiklikni", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "nedjeljivotjedan", "freeside": "slobodnastrana", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postkiberpank", "deathroadtocanada": "cesta_smrti_za_kanadu", "palladium": "pala<PERSON><PERSON>", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "lovacnamonstrume", "fireemblem": "vatreniorao", "genshinimpact": "genshinimpact", "geosupremancy": "<PERSON>eo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nenarativneigre", "tacticalrpg": "taktičkirpg", "mahoyo": "mahoyo", "animegames": "animeigrе", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "b<PERSON>žićnezderačine", "diluc": "diluc", "venti": "venti", "eternalsonata": "vječnasonata", "princessconnect": "princessconnect", "hexenzirkel": "vještičjikrug", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindija<PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "eigre", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligasanjara", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gejming", "overwatchleague": "overwatchliga", "cybersport": "kibersport", "crazyraccoon": "ludi<PERSON><PERSON>n", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "virtualneutrke", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantkompetitivno", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "poluživot", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "ventil", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "vječnoljeto", "goatsimulator": "goatsimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planetaslobode", "transformice": "transformice", "justshapesandbeats": "samoblikovibitovi", "battlefield4": "battlefield4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanije", "overcooked": "prepečeno", "interplanetary": "međ<PERSON><PERSON><PERSON><PERSON>", "helltaker": "paklenjak", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "patuljastatvrđava", "foxhole": "skrivanje", "stray": "z<PERSON><PERSON><PERSON>", "battlefield": "bojnopolje", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "oko", "blackdesert": "crnapustinja", "tabletopsimulator": "stonoigresimulator", "partyhard": "žurajdokraja", "hardspaceshipbreaker": "hardspaceshipbreaker", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "zarobljensa<PERSON>lom", "dinkum": "dinkum", "predecessor": "prethodnik", "rainworld": "kišnisvjet", "cavesofqud": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolo<PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "zorarata", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "mračnoitamnije", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "dušeradnik", "datingsims": "sims<PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "bijegizbox", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "novigrad", "citiesskylines": "gradovihorizonta", "defconheavy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsija", "virtualkenopsia": "virtualkenopsija", "snowrunner": "snjegotrkač", "libraryofruina": "knjižnicaruševina", "l4d2": "l4d2", "thenonarygames": "nebinarneigrе", "omegastrikers": "omegastrikers", "wayfinder": "putokaz", "kenabridgeofspirits": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "placidplasticduck": "mirnaplastičnapatka", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatepilekonjcafetak", "dialtown": "nazivigrad", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermesnidecko", "tinnybunny": "<PERSON><PERSON><PERSON><PERSON>", "cozygrove": "cozygrove", "doom": "<PERSON><PERSON><PERSON><PERSON>", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "kod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdu<PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "pala<PERSON><PERSON>", "earthdefenseforce": "obranbenesnagazemlje", "huntshowdown": "huntshowdown", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "prid<PERSON>žisesquadu", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "pobunjeničkaoluja", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "z4z", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombiji", "mirrorsedge": "mirrorsedge", "divisions2": "divizije2", "killzone": "killzone", "helghan": "hel<PERSON>", "coldwarzombies": "zomb<PERSON>hladnogra<PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "asborilac", "crosscode": "crosscode", "goldeneye007": "zlatnooko007", "blackops2": "blackops2", "sniperelite": "snaj<PERSON><PERSON>_elita", "modernwarfare": "modernirat", "neonabyss": "neonbezdan", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "borderlands", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "divl<PERSON><PERSON><PERSON>", "worldofwarships": "worldofwarships", "back4blood": "nazad4krv", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "plaćeniubojica", "masseffect": "masseffect", "systemshock": "sistemskišok", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "<PERSON><PERSON><PERSON><PERSON>", "doometernal": "doom<PERSON><PERSON><PERSON><PERSON>", "centuryageofashes": "sto<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mpc", "division2": "division2", "tythetasmaniantiger": "tytasmanski<PERSON><PERSON>", "generationzero": "<PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "uđiutamnicuoružja", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "fantomskabolašaštalijegdje", "warface": "ratnolice", "crossfire": "unakrsnavatara", "atomicheart": "atomskosrce", "blackops3": "blackops3", "vampiresurvivors": "vampirskipreživjeli", "callofdutybatleroyale": "callofdutybattleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "sloboda", "battlegrounds": "b<PERSON><PERSON><PERSON><PERSON>", "frag": "frag", "tinytina": "<PERSON><PERSON><PERSON><PERSON>", "gamepubg": "gamepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "fpsigrе", "convertstrike": "<PERSON>t<PERSON><PERSON>uspjeh", "warzone2": "warzone2", "shatterline": "prelomnica", "blackopszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodymess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "republikanskapostrojba", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "groundbranch", "squad": "ekipa", "destiny1": "sudbina1", "gamingfps": "gamingfps", "redfall": "crvenipad", "pubggirl": "pubgcura", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "upisan", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "oklopnajezgra", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "placa2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "empyrion": "empyrion", "pubgczech": "pubghrvatska", "titanfall2": "titanfall2", "soapcod": "sapunskabakalarska", "ghostcod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "csplay": "csigra", "unrealtournament": "nerealniturnir", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "razvalitpod", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonbijela", "remnant": "ostatak", "azurelane": "azurelane", "worldofwar": "svijetrata", "gunvolt": "gunvolt", "returnal": "povratak", "halo4": "halo4", "haloreach": "doseghaloa", "shadowman": "čovjekizsjene", "quake2": "quake2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "crvenimrtvac", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "moreplovaizlodijeva", "rust": "hrđa", "conqueronline": "conqueronline", "dauntless": "neustrašiv", "warships": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON>va", "warthunder": "warthunder", "flightrising": "<PERSON><PERSON><PERSON><PERSON>", "recroom": "družaonica", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "<PERSON><PERSON><PERSON>", "phantasystaronline2": "phantasystaronline2", "maidenless": "bezdjeve", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "worldoftanks", "crossout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "drug<PERSON>ž<PERSON><PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "netplay", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "šljam", "newworld": "novisvjet", "blackdesertonline": "blackdesertonline", "multiplayer": "multiplayer", "pirate101": "gusari101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "bikwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "svileniput", "spiralknights": "spiralknights", "mulegend": "mulegenda", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "prorokzmajeva", "grymmo": "grymmo", "warmane": "toplina", "multijugador": "multiigrač", "angelsonline": "anđelion<PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starojarepublikasw", "grandfantasia": "velikafantazija", "blueprotocol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworld": "savršensvjet", "riseonline": "uspjehonline", "corepunk": "corepunk", "adventurequestworlds": "adventurequestworlds", "flyforfun": "letizazabave", "animaljam": "animaljam", "kingdomofloathing": "kraljevstvoužasa", "cityofheroes": "grad<PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "ulič<PERSON>borac", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "začast", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtualnaborac", "streetsofrage": "ulicebijesa", "mkdeadlyalliance": "mksmtnonosavez", "nomoreheroes": "nemavišeheroja", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "kraljboraca", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retroborbenegame", "blasphemous": "bogohulno", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "superudarac", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "bor<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "oklopniratnici", "finalfight": "finalniokršaj", "poweredgear": "opremljena", "beatemup": "mlatimise", "blazblue": "blazblue", "mortalkombat9": "smrtonosnaborba9", "fightgames": "borbeigre", "killerinstinct": "ubojitinstinkt", "kingoffigthers": "kraljboraca", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "viteštvo2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightnast<PERSON><PERSON>", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksongstršljen", "silksonggame": "silksonggame", "silksongnews": "vijestiosilksongu", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "tipkalumina", "evolutiontournament": "evolucijskiturnej", "evomoment": "evomomentzaboo", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "bloodborne", "horizon": "horizont", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodborne", "uncharted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "ozloglašen", "playstationbuddies": "playstation_frendovi", "ps1": "ps1", "oddworld": "oddworld", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbi<PERSON>", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "pustise<PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "bograta", "gris": "gris", "trove": "skrivenoblago", "detroitbecomehuman": "detroitpostajeljudski", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "turističkitrofej", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "crashteamracing", "fivepd": "petpd", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "igranjestanice", "samuraiwarriors": "samuraji", "psvr2": "psvr2", "thelastguardian": "<PERSON>ad<PERSON><PERSON><PERSON><PERSON>", "soulblade": "soulblade", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "<PERSON>ad<PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "zabavl<PERSON>č", "warharmmer40k": "warharmmer40k", "fightnightchampion": "nocboracasampion", "psychonauts": "psihonavigatori", "mhw": "mhw", "princeofpersia": "princeofpersia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "b<PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "nezagladnjujmozajedno", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "zvjezdanputnik", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "kucopreuredjivac", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxserija", "r6xbox": "r6xbox", "leagueofkingdoms": "ligakraljevstava", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "traštv", "skycotl": "nebeskiotl", "erica": "erica", "ancestory": "predci", "cuphead": "cuphead", "littlemisfortune": "malanesretnica", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monstermaturalac", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duckgame": "<PERSON><PERSON><PERSON>", "thestanleyparable": "thestanleyparable", "towerunite": "towerunite", "occulto": "skriveno", "longdrive": "dugavožnja", "satisfactory": "zadovoljavajuće", "pluviophile": "kišoljubac", "underearth": "ispodzemlje", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "mrač<PERSON><PERSON><PERSON><PERSON>", "pizzatower": "pizzatoranj", "indiegame": "indieigra", "itchio": "itchio", "golfit": "<PERSON><PERSON><PERSON>", "truthordare": "istinailiizvršiizazov", "game": "igra", "rockpaperscissors": "kamenskarepapirzeillegalcommitted", "trampoline": "trampolina", "hulahoop": "hula<PERSON>", "dare": "usudise", "scavengerhunt": "lovnablago", "yardgames": "igrenad<PERSON><PERSON><PERSON>", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "kockomanijak", "cosygames": "ugodnegame", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "be<PERSON><PERSON><PERSON><PERSON>gra", "drinkinggames": "igrezapijenje", "sodoku": "sudoku", "juegos": "juegos", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "simulacijskeig<PERSON>", "wordgames": "igrerječima", "jeuxdemots": "igrasriječima", "juegosdepalabras": "igrerječima", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "dosadneigre", "oyun": "oyun", "interactivegames": "interaktivneigre", "amtgard": "amtgard", "staringcontests": "natjecanjeubuljenj", "spiele": "spiele", "giochi": "igre", "geoguessr": "geoguessr", "iphonegames": "iphoneigre", "boogames": "boogames", "cranegame": "igrasklještima", "hideandseek": "skrivača", "hopscotch": "školica", "arcadegames": "arkadneigre", "yakuzagames": "yakuzaigre", "classicgame": "klasičnaigra", "mindgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "pogo<PERSON><PERSON><PERSON>", "galagames": "galagames", "romancegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "yander<PERSON><PERSON>", "tonguetwisters": "brzalice", "4xgames": "4xigre", "gamefi": "gamefi", "jeuxdarcades": "arkadneigre", "tabletopgames": "društvenjeigre", "metroidvania": "metroidvania", "games90": "igrice90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "igrezautrk<PERSON>", "ets2": "ets2", "realvsfake": "stvarnoprotivlažno", "playgames": "igrajigre", "gameonline": "igrajuonline", "onlinegames": "onlineigrе", "jogosonline": "onlineigre", "writtenroleplay": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "playaballgame": "igrajloptaškuigru", "pictionary": "pictionary", "coopgames": "coopigre", "jenga": "jenga", "wiigames": "wiiigre", "highscore": "rekord", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "burgerigre", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "igranapitanja", "gioco": "gioco", "managementgame": "igrazaupravljanje", "hiddenobjectgame": "igrapronalaženjapredmeta", "roolipelit": "igre_uloga", "formula1game": "formula1igra", "citybuilder": "gradograditelj", "drdriving": "d<PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "arkadneigre", "memorygames": "igrepamćenja", "vulkan": "vulkan", "actiongames": "<PERSON>k<PERSON><PERSON><PERSON><PERSON>", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "fliperi", "oldgames": "<PERSON><PERSON><PERSON><PERSON>", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "<PERSON><PERSON><PERSON>", "gameo": "igrica", "lasergame": "lasergame", "imessagegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "idlegames": "idlegame", "fillintheblank": "popuniprazno", "jeuxpc": "pcigre", "rétrogaming": "retrogaming", "logicgames": "logičkeigre", "japangame": "<PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "podigniigrusheđenja", "subwaysurf": "metrosurfanje", "jeuxdecelebrite": "igreslavnih", "exitgames": "izlazneigre", "5vs5": "5na5", "rolgame": "<PERSON><PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "igraiubij", "traditionalgames": "tradicionalneigre", "kniffel": "jamb", "gamefps": "fpsigranje", "textbasedgames": "igretekstom", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospelj", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "igrenativnjaku", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "stoninogomet", "tischfußball": "stoln<PERSON>udbal", "spieleabende": "igračkevečeri", "jeuxforum": "jeuxforum", "casualgames": "casualigre", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "igrezabježanja", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "igrea<PERSON>mat", "játék": "igra", "bordfodbold": "stoninogomet", "jogosorte": "igresreće", "mage": "mag", "cargames": "<PERSON><PERSON><PERSON><PERSON>", "onlineplay": "igranjenapunke", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "bingozatorbice", "randomizer": "randomizator", "msx": "msx", "anagrammi": "anagrami", "gamespc": "<PERSON>grezap<PERSON>", "socialdeductiongames": "društveneigredokumaca", "dominos": "dominos", "domino": "domino", "isometricgames": "izometrijskeigre", "goodoldgames": "dobrestarigre", "truthanddare": "is<PERSON><PERSON>za<PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "potragezablagom", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "f2pi<PERSON><PERSON>", "free2play": "besplatnoigranje", "fantasygame": "igrafantazije", "gryonline": "gryonline", "driftgame": "driftgame", "gamesotomes": "igrezadjevojke", "halotvseriesandgames": "halotvserijeiigre", "mushroomoasis": "gljivljaoaza", "anythingwithanengine": "sveštoimamotor", "everywheregame": "igrasposvuda", "swordandsorcery": "mačibajke", "goodgamegiving": "dobraigradavan<PERSON>", "jugamos": "j<PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "računalneigrе", "virgogami": "virgorigami", "gogame": "idemoigra", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "minijaturneigre", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "ljubavpremasebiigranje", "gamemodding": "mod<PERSON><PERSON><PERSON><PERSON><PERSON>", "crimegames": "igrek<PERSON>inala", "dobbelspellen": "društveneigrehrv", "spelletjes": "igrice", "spacenerf": "spacenerf", "charades": "šarade", "singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgame": "coop<PERSON>ra", "gamed": "igrao", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "glavnaigra", "kingdiscord": "kraljdiscorda", "scrabble": "scrabble", "schach": "<PERSON>ah", "shogi": "<PERSON><PERSON>i", "dandd": "didnd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "pandemijskonasljeđe", "camelup": "camelgore", "monopolygame": "monopolyigra", "brettspiele": "brettspiele", "bordspellen": "društvenkeigre", "boardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "društveneigrе", "planszowe": "drustveneigre", "risiko": "risiko", "permainanpapan": "društvenéigre", "zombicide": "zombicid", "tabletop": "stolne", "baduk": "baduk", "bloodbowl": "bloodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "idigračjedr<PERSON>štveneigrače", "connectfour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroquest": "heroquest", "giochidatavolo": "društveneigreboo", "farkle": "farkle", "carrom": "karambola", "tablegames": "stolneigre", "dicegames": "kockiceigranje", "yatzy": "jamb", "parchis": "parchis", "jogodetabuleiro": "društvenagra", "jocuridesocietate": "d<PERSON>štveneigrez<PERSON><PERSON><PERSON>", "deskgames": "stolneigre", "alpharius": "<PERSON><PERSON><PERSON><PERSON>", "masaoyunları": "masaigre", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "kozmičkisusret", "creationludique": "kreativnaigra", "tabletoproleplay": "sto<PERSON>igre", "cardboardgames": "kartonskeigrainice", "eldritchhorror": "lovecraftovskiužas", "switchboardgames": "igrenaploci", "infinitythegame": "infinitythegame", "kingdomdeath": "kraljsmrtikraljevstvo", "yahtzee": "yahtzee", "chutesandladders": "ljestviceizmije", "társas": "d<PERSON>štvena", "juegodemesa": "društvenjaigra", "planszówki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "seljačkiživot", "boardom": "dosada", "applestoapples": "usporedbajabukasjabukama", "jeudesociété": "društvenagra", "gameboard": "ploča_za_igru", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "krokinol", "jeuxdesociétés": "društveneigrehr", "twilightimperium": "sumraknogcarstva", "horseopoly": "horseopoly", "deckbuilding": "gradnjašpila", "mansionsofmadness": "vile<PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "društveneigrenamobilnim", "shadowsofbrimstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoftokyo": "kraljtokija", "warcaby": "dame", "táblajátékok": "drustveneigre", "battleship": "b<PERSON><PERSON><PERSON><PERSON>", "tickettoride": "kartazavožnju", "deskovehry": "stolneigre", "catán": "catán", "subbuteo": "s<PERSON><PERSON><PERSON><PERSON>", "jeuxdeplateau": "društvenačigra", "stolníhry": "stolneigre", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "društveneigre", "gesellschaftsspiele": "gesellschaftsspiele", "starwarslegion": "starwarslegion", "gochess": "<PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "društvenéigre", "terraria": "terraria", "dsmp": "dsmp", "warzone": "ratnazona", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dani", "identityv": "identityv", "theisle": "otok", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "pozivchul<PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "med<PERSON><PERSON>", "eco": "eko", "monkeyisland": "majmunskaotok", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "<PERSON><PERSON><PERSON><PERSON>", "pathologic": "<PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7ddp", "thelongdark": "<PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "uzemljen", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "ludistarac", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "vječnipovratak", "pathoftitans": "sta<PERSON><PERSON><PERSON>", "frictionalgames": "frictionalgames", "hexen": "vještice", "theevilwithin": "zlounama", "realrac": "st<PERSON><PERSON><PERSON>", "thebackrooms": "stražnjesobe", "backrooms": "stražnjesobe", "empiressmp": "empiressmp", "blockstory": "blokp<PERSON>ča", "thequarry": "kamenolom", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "thewalkingdeadgame", "wehappyfew": "nasmaloshrecnika", "riseofempires": "usponcarstava", "stateofsurvivalgame": "stateofsurvivalgame", "vintagestory": "vintage<PERSON><PERSON><PERSON><PERSON>", "arksurvival": "arkprezivljavanje", "barotrauma": "barotrauma", "breathedge": "breathedge", "alisa": "alisa", "westlendsurvival": "westlendpreživljavanje", "beastsofbermuda": "zvijeribermude", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "hororpreživljavanja", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "životnakonigre", "survivalgames": "igrezapreživljavanje", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "ovamojarataigra", "scpfoundation": "scp<PERSON><PERSON><PERSON>", "greenproject": "zeleniprojekat", "kuon": "kuon", "cryoffear": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "splav", "rdo": "rdo", "greenhell": "zelenapakao", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "baka", "littlenightmares2": "malenovnemorebl2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "sinoviašume", "rustvideogame": "rustvideogame", "outlasttrials": "outlasttrials", "alienisolation": "tuđinskaizolacija", "undawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "7day2die": "7danazivjeti", "sunlesssea": "morebezsunca", "sopravvivenza": "preživljavanje", "propnight": "<PERSON><PERSON><PERSON>", "deadisland2": "mrtviotok2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "smrtosvemirje", "cataclysmdarkdays": "katak<PERSON><PERSON>mata<PERSON>nidani", "soma": "soma", "fearandhunger": "strahistraženj", "stalkercieńczarnobyla": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeafter": "životposlije", "ageofdarkness": "do<PERSON><PERSON>", "clocktower3": "clocktower3", "aloneinthedark": "sa<PERSON>ac<PERSON>", "medievaldynasty": "srednjovjekovnadinastija", "projectnimbusgame": "projectnimbusgame", "eternights": "<PERSON>čne<PERSON><PERSON><PERSON>", "craftopia": "craftopia", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "svijetskadominacija", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officiozaatentatore", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "<PERSON><PERSON>jk<PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kzaljubljenost", "wh40": "wh40", "warhammer40klove": "warhammer40kljubav", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "hramculexus", "vindicare": "vindicare", "ilovesororitas": "volimsestrinstava", "ilovevindicare": "volimosvetiti", "iloveassasinorum": "volimassasinorum", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "trebamdvoje", "wingspan": "rasponk<PERSON>a", "terraformingmars": "terraformingmarsa", "heroesofmightandmagic": "herojimočiimagije", "btd6": "btd6", "supremecommander": "vrhnovnizapovjednik", "ageofmythology": "dobamitologije", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "prognan", "caesar3": "caesar3", "redalert": "crvenialarm", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "zapovijedajipokoravaj", "warcraft3": "warcraft3", "eternalwar": "vječnirat", "strategygames": "igre_strategije", "anno2070": "anno2070", "civilizationgame": "igracivlizacije", "civilization4": "civilizacija4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spora", "totalwar": "totalnirat", "travian": "travian", "forts": "tvr<PERSON><PERSON>", "goodcompany": "dobrotimdruštvo", "civ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homeworld": "svijetdoma", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "zakraljeve", "realtimestrategy": "strategijauživo", "starctaft": "starcraft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "kingdomtwocrowns", "eu4": "eu4", "vainglory": "ispraznost", "ww40k": "ww40k", "godhood": "božanstvo", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hetriktrik", "davesfunalgebraclass": "daveovazabavnisatmatematike", "plagueinc": "plagueinc", "theorycraft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "civilizacija3", "4inarow": "4zaredom", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "ageofempires2", "disciples2": "učenici2", "plantsvszombies": "biljeprotivzombija", "giochidistrategia": "strate<PERSON><PERSON><PERSON><PERSON>", "stratejioyunları": "strateškigejming", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "dobačudesa", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "osvajanjesvijeta", "heartsofiron4": "heartsofiron4", "companyofheroes": "d<PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "bitkazawesnoth", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "guskapok<PERSON>n", "phobies": "fobije", "phobiesgame": "igrafobija", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "napotezu", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "kraljevikrizari", "cultris2": "cultris2", "spellcraft": "čarobnjaštvovanje", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategija", "popfulmail": "popovapunapošte", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "masterduel", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "transportmogul", "unrailed": "skinutsatračnica", "magicarena": "<PERSON><PERSON>na", "wolvesville": "vukodlakovo", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "gornjakraljevstva", "galaxylife": "životgalaksije", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlecats": "mačkeratnice", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "potrebazabrzinom", "needforspeedcarbon": "needforspeedcarbon", "realracing3": "praveutrke3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "preživjeti", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "alicemad<PERSON>ret<PERSON>s", "darkhorseanthology": "antologijatamni<PERSON><PERSON>", "phasmophobia": "fazmofobija", "fivenightsatfreddys": "fivenightsatfreddys", "saiko": "saiko", "fatalframe": "fatalframe", "littlenightmares": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "mrtvacisepodiže", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "zatonuliku<PERSON>i", "deadisland": "mrtviotok", "litlemissfortune": "malasrgospođica", "projectzero": "projekt<PERSON><PERSON>", "horory": "horori", "jogosterror": "trčanjeteror", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "pozdravsusjed2", "gamingdbd": "gamingdbd", "thecatlady": "mačkašica", "jeuxhorreur": "horrorigre", "horrorgaming": "horrorigre", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "protivčovječnosti", "cribbage": "c<PERSON><PERSON><PERSON>", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinokl", "codenames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "kartezabic<PERSON><PERSON>", "lor": "lor", "euchre": "euker", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "pasi<PERSON>s", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "šafkopf", "keyforge": "keyforge", "cardtricks": "trikoviskartama", "playingcards": "karte", "marvelsnap": "marvelsnap", "ginrummy": "remi", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "trgovačkekarte", "pokemoncards": "poke<PERSON><PERSON>te", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "sportskarte", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "️", "warcry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "kraljsrca", "truco": "trik", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "otpor", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yug<PERSON>hkar<PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "duelskidisk", "yugiohgame": "yugiohigra", "darkmagician": "crnimag", "blueeyeswhitedragon": "plaveokebijelizmaj", "yugiohgoat": "yugiohkoza", "briscas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegocartas": "kartaškeigrе", "burraco": "bur<PERSON>o", "rummy": "remi", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "spika", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "kartaškeigrе", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "kartanje", "sueca": "sueca", "beloteonline": "belanainternetie", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "sagaobojevnimduhovpma", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "<PERSON>ž<PERSON><PERSON>", "facecard": "licekaokarta", "cardfight": "kartaškabitka", "biriba": "biriba", "deckbuilders": "<PERSON>rad<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "marvelšampioni", "magiccartas": "čarobnicarta", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "preskočibo", "unstableunicorns": "nestabilnijednorozisuhljudi", "cyberse": "cyberse", "classicarcadegames": "klasičnearkadneigre", "osu": "osu", "gitadora": "gitadora", "dancegames": "plesneigre", "fridaynightfunkin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "gitarskiheroj", "clonehero": "clonehero", "justdance": "samoplesati", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockajtmrtve", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "plesnicentar", "rhythmgamer": "ritmašigre", "stepmania": "stepmania", "highscorerythmgames": "najboljiritmigejmaši", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "rit<PERSON><PERSON><PERSON><PERSON>", "hypmic": "hip<PERSON>", "adanceoffireandice": "plesvatreiledasimfonija", "auditiononline": "audicijeonline", "itgmania": "itgmanija", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "kriptaplesačamrtvih", "rhythmdoctor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubing": "kubiranje", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "igreslagalica", "spotit": "<PERSON><PERSON><PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "logičkezagonetke", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "zagonetke", "rubikscube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossword": "k<PERSON>ž<PERSON>jka", "motscroisés": "ukrštenerijeci", "krzyżówki": "krzyżówki", "nonogram": "nonogram", "bookworm": "knjiškacrv", "jigsawpuzzles": "puzzleslagalice", "indovinello": "zagonetka", "riddle": "zagonetka", "riddles": "zagonetke", "rompecabezas": "slagalica", "tekateki": "tekateki", "inside": "unutra", "angrybirds": "ljuticeptice", "escapesimulator": "escapesimulator", "minesweeper": "minolovac", "puzzleanddragons": "puzzleandragons", "crosswordpuzzles": "križaljke", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesigra", "puzzlesport": "puzzlesport", "escaperoomgames": "igresabegsobe", "escapegame": "<PERSON><PERSON><PERSON>", "3dpuzzle": "3dslagalica", "homescapesgame": "homescapesigra", "wordsearch": "traženjerijeci", "enigmistica": "enigmatika", "kulaworld": "s<PERSON><PERSON><PERSON><PERSON>", "myst": "<PERSON><PERSON><PERSON><PERSON>", "riddletales": "zagonetnepriče", "fishdom": "fishdom", "theimpossiblequiz": "nemogućik<PERSON>z", "candycrush": "candycrush", "littlebigplanet": "malivelikiplanet", "match3puzzle": "slagalica3", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON><PERSON>", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yapboz": "pričalica", "thetalosprinciple": "thetalosprinciple", "homescapes": "domzavjestine", "puttputt": "minigolf", "qbert": "qbert", "riddleme": "zagonetkame", "tycoongames": "tycoonigrice", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cruciverba": "k<PERSON>ž<PERSON>jka", "ciphers": "<PERSON><PERSON><PERSON>", "rätselwörter": "zagonetkeriječi", "buscaminas": "minopolje", "puzzlesolving": "rješavanjezagonetki", "turnipboy": "turnipboy", "adivinanzashot": "vrućezagonetke", "nobodies": "nitkoniti", "guessing": "pogađanje", "nonograms": "nonogrami", "kostkirubika": "kostkirubika", "crypticcrosswords": "kriptičnekrižaljke", "syberia2": "syberia2", "puzzlehunt": "lovnazagonetke", "puzzlehunts": "lovnazagonetke", "catcrime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "slagalica", "hlavolamy": "g<PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "zadnjilogorskioganj", "autodefinidos": "autodefiniranje", "picopark": "picopark", "wandersong": "putosong", "carto": "carto", "untitledgoosegame": "untitledgoosegame", "cassetête": "glavolomka", "limbo": "limbo", "rubiks": "rubiks", "maze": "labirint", "tinykin": "tinykin", "rubikovakostka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "speedcube": "speedcube", "pieces": "<PERSON><PERSON><PERSON><PERSON>", "portalgame": "portalgame", "bilmece": "bilmece", "puzzelen": "puzzliranje", "picross": "picross", "rubixcube": "rub<PERSON><PERSON>_kocka", "indovinelli": "zagonetke", "cubomagico": "čarobnikocka", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "twistedwonderland", "monopoly": "monopoli", "futurefight": "bor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "kuk", "lonewolf": "vukosamotnjak", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemystars", "stateofsurvival": "stanjepreživljavanja", "mycity": "mojgrad", "arknights": "arknights", "colorfulstage": "šarenascena", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "viteškatrka", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "nogometnabitka", "a3": "a3", "phonegames": "igrenatelefonu", "kingschoice": "kraljevskiizbor", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "benzinac", "tacticool": "taktikul", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "izvanpetlje", "craftsman": "zanatlija", "supersus": "prejelsumnjivo", "slowdrive": "laganavožnja", "headsup": "pazi", "wordfeud": "wordfeud", "bedwars": "bedwars", "freefire": "slobodnavatra", "mobilegaming": "mobilnoigranje", "lilysgarden": "lilyjinvrt", "farmville2": "farmville2", "animalcrossing": "životinjskiprijelaz", "bgmi": "bgmi", "teamfighttactics": "borbenataktičkomtimskom", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "hitnapomoc", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "tresenjeidžitanje", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "ljuba<PERSON>dz<PERSON><PERSON>", "androidgames": "androidigre", "criminalcase": "kriminalistič<PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "kvizomanija", "leagueofangels": "ligaanđela", "lordsmobile": "lordsmobile", "tinybirdgarden": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "neuralniooblak", "mysingingmonsters": "mojepjevaju<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "vječnapriča", "futime": "<PERSON><PERSON><PERSON>", "antiyoy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "apexlegendmobile": "apexlegendmobile", "ingress": "ulaz<PERSON>", "slugitout": "izboritisedonaja", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "ljubimciburazeri", "gameofsultans": "igrasultana", "arenabreakout": "arenabreakout", "wolfy": "vuč<PERSON>", "runcitygame": "igrautrčigrad", "juegodemovil": "mobilnaigra", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "oponašanje", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "magnatroler<PERSON>astera", "grandchase": "grandchase", "bombmebrasil": "bombardirajmebrazil", "ldoe": "ldoe", "legendonline": "legendaonline", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "pozivzmajeva", "shiningnikki": "<PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "putniotkud", "sealm": "foke", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderbi3", "wordswithfriends2": "riječisprijateljima2", "soulknight": "vitezodduse", "purrfecttale": "purrfektnapriča", "showbyrock": "showbyrock", "ladypopular": "ladypopular", "lolmobile": "ha<PERSON>eljubitelj", "harvesttown": "harvesttown", "perfectworldmobile": "savršenisvjetmobile", "empiresandpuzzles": "cаrst<PERSON><PERSON><PERSON>uzzle", "empirespuzzles": "carstvazagonetki", "dragoncity": "zmajskigrad", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobilehr", "fanny": "<PERSON><PERSON><PERSON>", "littlenightmare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "b<PERSON><PERSON>tr<PERSON><PERSON>", "tearsofthemis": "suzetemide", "eversoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "z<PERSON><PERSON><PERSON><PERSON><PERSON>", "eveechoes": "eveechoes", "jogocelular": "igricamobitel", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "ma<PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "streetfighterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "girlsfrontline", "jurassicworldalive": "jurassicworldalive", "soulseeker": "tra<PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "preboljet", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "pričaomoonchai", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "jogosmobile", "legendofneverland": "legendaoneversije", "pubglite": "pubglite", "gamemobilelegends": "igremobilelegends", "timeraiders": "timeraiders", "gamingmobile": "gamingmobilni", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "mačjeborbecro", "dnd": "dnd", "quest": "kvest", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgzastolom", "worldofdarkness": "svijettame", "travellerttrpg": "travellerttrpg", "2300ad": "2300ng", "larp": "larp", "romanceclub": "klubromantike", "d20": "d20", "pokemongames": "pokemonigre", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON>na", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "<PERSON><PERSON><PERSON>", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "timracket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "timmystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "željezneruke", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmajstor", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "d<PERSON><PERSON><PERSON>okemoni", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "šarmirati", "shinyhunter": "lovacnasjajne", "ajedrez": "<PERSON>ah", "catur": "<PERSON>ah", "xadrez": "<PERSON>ah", "scacchi": "scacchi", "schaken": "<PERSON>ah", "skak": "skak", "ajedres": "<PERSON>ah", "chessgirls": "šahistice", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "svjetskiblitz", "jeudéchecs": "<PERSON>ah", "japanesechess": "japanski_šah", "chinesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "šah<PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rook": "top", "chesscom": "chesscom", "calabozosydragones": "tamniceizmajevi", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "dungeonmaster", "tiamat": "tiamat", "donjonsetdragons": "tamniceizmajevi", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "legendaovox<PERSON><PERSON><PERSON>", "doungenoanddragons": "doungenoanddragons", "darkmoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "minecraftprvenstvohr", "minecrafthive": "minecraftkošnica", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hipixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodovi", "mcc": "mcc", "candleflame": "plamenplamensvijeće", "fru": "fru", "addons": "<PERSON><PERSON><PERSON>", "mcpeaddons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "modiraniminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "izmeđuzemlja", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftgrad", "pcgamer": "pcgamer", "jeuxvideo": "videoigre", "gambit": "gambit", "gamers": "g<PERSON><PERSON><PERSON>", "levelup": "levelup", "gamermobile": "gamermobitel", "gameover": "krajigre", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gejman", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "pcigre", "casualgaming": "opuštenigejming", "gamingsetup": "gamingpostavka", "pcmasterrace": "pcmasterrace", "pcgame": "pcigra", "gamerboy": "gamerb<PERSON>j", "vrgaming": "vrigre", "drdisrespect": "drdisrespect", "4kgaming": "4kgejming", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON>", "consoleplayer": "konsolas", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgejmeri", "onlinegaming": "onlinegaming", "semigamer": "polugejmer", "gamergirls": "gamerice", "gamermoms": "gamermame", "gamerguy": "gamer", "gamewatcher": "gamerwatcher", "gameur": "gejmer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerice", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "šapice", "quests": "<PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON>", "cozygaming": "ugodnogejming", "gamelpay": "gamelpay", "juegosdepc": "juegosdepc", "dsswitch": "dsswitch", "competitivegaming": "kompetitivnogejming", "minecraftnewjersey": "minecraftnovjersey", "faker": "lažnjak", "pc4gamers": "pc4<PERSON><PERSON>", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heteroseksualnogejmanja", "gamepc": "igranjenakompu", "girlsgamer": "curegejmerice", "fnfmods": "fnfmodovi", "dailyquest": "dnevnizadatak", "gamegirl": "gamerica", "chicasgamer": "gejmerice", "gamesetup": "postavljanjeigrе", "overpowered": "<PERSON><PERSON><PERSON>", "socialgamer": "društvenigejmer", "gamejam": "gamejam", "proplayer": "<PERSON><PERSON><PERSON><PERSON>", "roleplayer": "roleplayeri", "myteam": "mojatim", "republicofgamers": "republikagamera", "aorus": "aorus", "cougargaming": "pumegaming", "triplelegend": "tros<PERSON><PERSON><PERSON><PERSON>a", "gamerbuddies": "gamerskifrendi", "butuhcewekgamers": "trebamgamerice", "christiangamer": "kršćanskigejmer", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "štrebergejmer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "casualgamer", "89squad": "89ek<PERSON><PERSON>", "inicaramainnyagimana": "kakobiloprvoputodigrajemse", "insec": "nesig", "gemers": "gemeri", "oyunizlemek": "gledanjeigranja", "gamertag": "gamertag", "lanparty": "lanžurka", "videogamer": "gejmer", "wspólnegranie": "zajedničkoigranje", "mortdog": "mortdog", "playstationgamer": "playstationgejmer", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "zdravigejmer", "gtracing": "gtutrke", "notebookgamer": "gamers<PERSON><PERSON><PERSON>", "protogen": "protogen", "womangamer": "gamerica", "obviouslyimagamer": "naravnodajamgamer", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "humanfallflat": "humanfallflat", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nultiizlaz", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusic", "sonicthehedgehog": "son<PERSON><PERSON><PERSON><PERSON>", "sonic": "sonic", "fallguys": "fallguys", "switch": "prebaci", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "sudn<PERSON>jedrama", "ssbm": "ssbm", "skychildrenofthelight": "skychildrenofthelight", "tomodachilife": "tomodachilife", "ahatintime": "šeširuvremenu", "tearsofthekingdom": "suzenaljevstva", "walkingsimulators": "simulator<PERSON><PERSON><PERSON>", "nintendogames": "nintendoigre", "thelegendofzelda": "thelegendofzelda", "dragonquest": "dragonquest", "harvestmoon": "žetvaljunskimjesec", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "diszeldivljine", "myfriendpedro": "mojprijateljpedro", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51igara", "earthbound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tales": "prič<PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendočile", "tloz": "tloz", "trianglestrategy": "trokutastastrategija", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersgroznipetak", "nintendos": "nintendosi", "new3ds": "novi3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyr<PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioisonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "ma<PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vanillalol": "van<PERSON>lol", "wildriftph": "wildriftph", "lolph": "smrtods<PERSON>jeha", "leagueoflegend": "leagueoflegends", "tốcchiến": "brzinskabitka", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "<PERSON><PERSON><PERSON>", "lolzinho": "<PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendshrvatska", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "<PERSON><PERSON><PERSON><PERSON>", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligalegendi", "gaminglol": "g<PERSON><PERSON><PERSON>l", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexvrata", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideogame", "scaryvideogames": "strasnevideoigre", "videogamemaker": "stvarateljvideoigara", "megamanzero": "megamanzero", "videogame": "videoigra", "videosgame": "videoigre", "professorlayton": "profe<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "arkade", "acnh": "acnh", "puffpals": "puff<PERSON><PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "simulatorpol<PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxnjemačka", "robloxdeutsch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "igrespijeskom", "videogamelore": "gejmerščina", "rollerdrome": "rollerdrom", "parasiteeve": "parazitskaeva", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "<PERSON><PERSON><PERSON><PERSON>", "dreamscape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "mrtavprostor", "amordoce": "slad<PERSON><PERSON><PERSON><PERSON>", "videogiochi": "videoigre", "theoldrepublic": "stararepublika", "videospiele": "videoigre", "touhouproject": "touhouproject", "dreamcast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventuregames": "avanturisti<PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "akcij<PERSON>pustolovš<PERSON>", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "retrogej<PERSON><PERSON>", "retroarcade": "retroarkada", "vintagecomputing": "vintage<PERSON><PERSON><PERSON><PERSON>", "retrogaming": "retrogaming", "vintagegaming": "retrogejming", "playdate": "igrica", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "nepravda2", "shadowthehedgehog": "<PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "igraneba", "zenlife": "zenzivot", "beatmaniaiidx": "beatmaniaiidx", "steep": "strmina", "mystgames": "mistične_igre", "blockchaingaming": "blockchainigranje", "medievil": "medievil", "consolegaming": "igranjekon<PERSON>la", "konsolen": "konzole", "outrun": "<PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "panikapunaprocvala", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "gaminghororac", "monstergirlquest": "questdjevojk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiant": "superdiv", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "interaktivnafikcija", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "ljubavniciljubavnice", "visualnovel": "vizual<PERSON><PERSON>", "visualnovels": "vizualnenovele", "rgg": "rgg", "shadowolf": "vuksjene", "tcrghost": "tcrduh", "payday": "isplata", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "sumrakprincezezelda", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "pjeskovnik", "aestheticgames": "estetskigejmovi", "novelavisual": "vizualniromani", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogejm", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "revolucijapuha<PERSON>", "wiiu": "wiiu", "leveldesign": "dizajnrazina", "starrail": "zvjezdanapruga", "keyblade": "keyblade", "aplaguetale": "pričaokugi", "fnafsometimes": "fnafsometimes", "novelasvisuales": "vizualneromane", "robloxbrasil": "robloxbrazil", "pacman": "pacman", "gameretro": "retrogejming", "videojuejos": "videoju<PERSON><PERSON>", "videogamedates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "smrtonosnaborba11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkigre", "batmangames": "batmanigre", "returnofreckoning": "povratakosvetnika", "gamstergaming": "gamstergejming", "dayofthetantacle": "dan<PERSON><PERSON><PERSON>", "maniacmansion": "luda<PERSON>", "crashracing": "utrkesudaranja", "3dplatformers": "3dplatformeri", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "oldschoolgemanje", "hellblade": "paklenaoštica", "storygames": "priče_igre", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "zvukobježac", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "igrice", "offmortisghost": "offmortisghost", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "nadogradi", "katanazero": "katana<PERSON><PERSON>", "famicom": "famicom", "aventurasgraficas": "grafi<PERSON><PERSON><PERSON><PERSON>", "quickflash": "brziblic", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarkade", "f123": "f123", "wasteland": "pustara", "powerwashsim": "simulatorpranjapritiskom", "coralisland": "koralniotok", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "drugisvjet", "metaquest": "metaquest", "animewarrios2": "animeratnici2", "footballfusion": "nogometnaspoj", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "uv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "gomilaisk<PERSON><PERSON><PERSON><PERSON>", "simulator": "simulator", "symulatory": "symulatori", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "videoigre", "gaiaonline": "gaiaonline", "korkuoyunu": "igrazastrašivanja", "wonderlandonline": "čudesnozemljaonline", "skylander": "skylander", "boyfrienddungeon": "dečkotamnica", "toontownrewritten": "toontownprepisano", "simracing": "simracing", "simrace": "simvožnja", "pvp": "pvp", "urbanchaos": "gradskikaos", "heavenlybodies": "božanskatijelići", "seum": "sikiracija", "partyvideogames": "partyvideigre", "graveyardkeeper": "grobar", "spaceflightsimulator": "simulator<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "hranaivideoigre", "oyunvideoları": "oyunvideoları", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "simulator<PERSON><PERSON><PERSON>", "horizonworlds": "horizonworlds", "handygame": "igricanamobitelu", "leyendasyvideojuegos": "legendeivideoigre", "oldschoolvideogames": "oldschoolvideoigre", "racingsimulator": "simulator<PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentimetražnje", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "ka<PERSON><PERSON>oli<PERSON>", "monsterhunternow": "monsterhunternow", "rebelstar": "bunttovnazvijezda", "indievideogaming": "indievideoigre", "indiegaming": "indiegaming", "indievideogames": "indievideoigre", "indievideogame": "indievideoigra", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermannesanica", "bufffortress": "utvr<PERSON><PERSON><PERSON><PERSON>i", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "projekt", "futureclubgames": "igrezabuduciklub", "mugman": "šalica", "insomniacgames": "igrezanesanice", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "zaostatak", "gamebacklog": "hrpaigara", "gamingbacklog": "gamingzalihegara", "personnagejeuxvidéos": "likizvideoigara", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "gradskihorizonti", "supermonkeyball": "supermajmunskaloptica", "deponia": "deponia", "naughtydog": "<PERSON><PERSON>š<PERSON><PERSON>", "beastlord": "zvjerogospodar", "juegosretro": "retrogej<PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "rezervoardopamina", "staxel": "staxel", "videogameost": "glazbaizvideoigara", "dragonsync": "sinkronizacijazmajeva", "vivapiñata": "živjelapiñata", "ilovekofxv": "volimkofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "inicijalnitd", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "tužnianime", "darkerthanblack": "crnjeodbrnoga", "animescaling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animewithplot": "animesradnjom", "pesci": "pesci", "retroanime": "retroanime", "animes": "animei", "supersentai": "supertajno", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80ih", "90sanime": "90<PERSON>me", "darklord": "mračnigospodar", "popeetheperformer": "popapaiz<PERSON>đ<PERSON>č", "masterpogi": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "2000anime", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesesona1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "animecover", "thevisionofescaflowne": "vizijaescaflownea", "slayers": "ubojice", "tokyomajin": "tokyomajin", "anime90s": "anime90ih", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "<PERSON><PERSON><PERSON><PERSON>", "bananafish": "<PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "zahodskivezanihanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "vat<PERSON><PERSON><PERSON>", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "bud<PERSON><PERSON>idnev<PERSON>", "fairytail": "bajka", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "<PERSON><PERSON><PERSON>", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "sirename<PERSON><PERSON><PERSON>", "kamisamakiss": "kamisamapoljubac", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "ljubavnimangići", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "zmajskaslužavka", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformari", "geniusinc": "geni<PERSON><PERSON><PERSON>", "shamanking": "šamankralj", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "<PERSON><PERSON><PERSON><PERSON><PERSON>ind<PERSON><PERSON>", "sao": "sao", "blackclover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnomikrofon", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstrumcure", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportskianime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "saga<PERSON><PERSON>j<PERSON><PERSON>", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "pesnicasjevera", "mazinger": "mazinger", "blackbuttler": "crnicvijetlar", "towerofgod": "towerofgod", "elfenlied": "vilinskalag", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "kakoočuvatimamicu", "fullmoonwosagashite": "punmje<PERSON><PERSON><PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "simpatičnoistranoživo", "martialpeak": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "n<PERSON><PERSON><PERSON>", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monstercura", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON><PERSON>", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "košaravoća", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "ljubavuživo", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "tuđinecusvojojzemlji", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "obećanazemljanijedne", "monstermanga": "monstermanga", "yourlieinapril": "tvajalaživtravnju", "buggytheclown": "buggy<PERSON><PERSON><PERSON>", "bokunohero": "bokunohero", "seraphoftheend": "serafinkra<PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "dubokomorskizatvorenik", "jojolion": "jojo<PERSON>", "deadmanwonderland": "mrtavčevozemlječuda", "bannafish": "<PERSON><PERSON><PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "<PERSON><PERSON><PERSON><PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "pandor<PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "hranaratnebojevište", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "vražjalinja", "toyoureternity": "dozauvijek", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "plavorazdoblje", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "izbrisano", "bluelock": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goblinslayer": "ubojicagoblina", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vampireknight": "vampirskivitez", "mugi": "mugi", "blueexorcist": "plaviegzor<PERSON>", "slamdunk": "zakucavanje", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "plakao", "spyfamily": "špijunskafamilija", "airgear": "airgear", "magicalgirl": "čarobnadjevojka", "thesevendeadlysins": "sedamsmrtnihg<PERSON><PERSON><PERSON><PERSON>", "prisonschool": "školazatvor", "thegodofhighschool": "bogvisokeškolie", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "velikoplavo", "mydressupdarling": "mojaljepoticanazazodijevanje", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeuniverzum", "swordartonlineabridge": "swordartonlinemost", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON>", "romancemanga": "ljubavnimanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromansa", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON>we", "animeargentina": "animehrvaska", "lolicon": "lolikon", "demonslayertothesword": "demonslayer<PERSON><PERSON><PERSON><PERSON>", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "vatreniudarac", "adioseri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "zvijezdeseporavnavaju", "romanceanime": "romantičnianime", "tsundere": "tsundere", "yandere": "jandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralnikfaktor", "cherrymagic": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "recordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "highschoolofthedead", "germantechno": "germantechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prinšteniskog", "tonikawa": "prepredobro", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "razredubojica", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "smrt_parada", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japanskaanime", "animespace": "animeprostor", "girlsundpanzer": "cureipanzeri", "akb0048": "akb0048", "hopeanuoli": "novagodina", "animedub": "animesinkronizacija", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanimacija", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "štakorčovjek", "haremanime": "harem<PERSON><PERSON>", "kochikame": "kochikame", "nekoboy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gashbell": "gashbell", "peachgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchklub", "dragonquestdai": "dragonquestdai", "heartofmanga": "srcemange", "deliciousindungeon": "ukusnoukatakombi", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "zapisragnaroka", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "preskočinamokas<PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "<PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "rejvmajstor", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "čarobnjaštvoatelje", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangaježivot", "dropsofgod": "kapibožje", "loscaballerosdelzodia": "vitezovizodijaka", "animeshojo": "animeshojo", "reverseharem": "obrnutiharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "velikučiteljizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "mojšeftatica", "gear5": "gear5", "grandbluedreaming": "velikoplavosanjanje", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "bloodcanime", "bloodc": "krvnagrupa", "talesofdemonsandgods": "pričeodemonimaibogovima", "goreanime": "goreanime", "animegirls": "animecure", "sharingan": "<PERSON><PERSON>", "crowsxworst": "vranenajgore", "splatteranime": "splatteranime", "splatter": "prskanje", "risingoftheshieldhero": "usponštitaheroja", "somalianime": "somal<PERSON>j<PERSON><PERSON><PERSON>", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "zauzetsljuzom", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "d<PERSON><PERSON><PERSON>rnog", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "superprvaci", "animeidols": "<PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaisamobijem", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "čarobnedjeve", "callofthenight": "poz<PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "vrtsijena", "tsubasachronicle": "kroniketsube", "findermanga": "pronađ<PERSON><PERSON>", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "rajskipoljubac", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animesvemir", "persocoms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "omniscientreadersview": "sveznajučičitateljskipogled", "animecat": "animeačka", "animerecommendations": "preporukeanimea", "openinganime": "<PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "mojatinejdžerskaljubavnakomedija", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundami", "voltesv": "voltesv", "giantrobots": "divovskihroboti", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "meh", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "izbjeljivač", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarnaavantura", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "vojn<PERSON><PERSON>", "greenranger": "zele<PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "<PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonavantura", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "drkamen", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "ubojicademona", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "napadnatitane", "erenyeager": "erenyeager", "myheroacademia": "mojaherojskaakademija", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "istražneknjige", "onepieceanime": "onepieceanime", "attaquedestitans": "nap<PERSON><PERSON><PERSON>", "theonepieceisreal": "one<PERSON><PERSON><PERSON><PERSON><PERSON>", "revengers": "osvetnici", "mobpsycho": "mobpsycho", "aonoexorcist": "aoexorcist", "joyboyeffect": "joyboyefekt", "digimonstory": "pričaodigimonima", "digimontamers": "dig<PERSON><PERSON><PERSON><PERSON>", "superjail": "superzatvor", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "savrseniwebtoon", "kemonofriends": "kemonoprijatelji", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "vješticakojaleti", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "jereto", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "pričeizživota"}