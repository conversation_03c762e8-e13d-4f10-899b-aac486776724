{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "lithu<PERSON>_t<PERSON>_l<PERSON><PERSON>", "cognitivefunctions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "philosophy": "filos<PERSON><PERSON>", "history": "histori", "physics": "fi<PERSON>s", "science": "saense", "culture": "setso", "languages": "lipuo", "technology": "theknoloji", "memes": "limeme", "mbtimemes": "limemembti", "astrologymemes": "lithethoneatsetselopele", "enneagrammemes": "dimemetsaenneagram", "showerthoughts": "mehopolelatlhapeng", "funny": "qoso", "videos": "livideo", "gadgets": "lisebel<PERSON><PERSON>", "politics": "<PERSON><PERSON><PERSON><PERSON>", "relationshipadvice": "keletsotsalilerato", "lifeadvice": "keletsotsabophelo", "crypto": "crypto", "news": "litaba", "worldnews": "litsebatsalefatshe", "archaeology": "archaeology", "learning": "<PERSON><PERSON><PERSON>", "debates": "<PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "universe": "ka<PERSON><PERSON>", "meditation": "ho_thui<PERSON>_kelello", "mythology": "nanetemoea", "art": "bonono", "crafts": "me<PERSON><PERSON><PERSON>_ea_mat<PERSON><PERSON>", "dance": "<PERSON><PERSON><PERSON>", "design": "moralo", "makeup": "botle", "beauty": "botle", "fashion": "fesh<PERSON>", "singing": "bina", "writing": "mongolo", "photography": "lifoto", "cosplay": "boqa<PERSON>", "painting": "ho_penta", "drawing": "hotaka", "books": "libuka", "movies": "lifilimi", "poetry": "thothokiso", "television": "thelevishene", "filmmaking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animation": "popae", "anime": "anime", "scifi": "saensefiction", "fantasy": "toro", "documentaries": "lidokumentari", "mystery": "<PERSON><PERSON><PERSON><PERSON>", "comedy": "<PERSON><PERSON>e", "crime": "botlokotsebe", "drama": "<PERSON><PERSON><PERSON><PERSON>", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "b<PERSON><PERSON><PERSON><PERSON>", "romance": "lerato", "realitytv": "tvy<PERSON><PERSON>", "action": "ketso", "music": "mmino", "blues": "blues", "classical": "classical", "country": "naha", "desi": "desi", "edm": "edm", "electronic": "eletroniki", "folk": "setso", "funk": "funk", "hiphop": "hiphop", "house": "ntlo", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "selatin", "metal": "<PERSON><PERSON><PERSON><PERSON>", "pop": "pop", "punk": "<PERSON><PERSON><PERSON>i", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "lejoe", "techno": "techno", "travel": "ma<PERSON>", "concerts": "likonsarete", "festivals": "mekete", "museums": "dimusiamo", "standup": "<PERSON><PERSON><PERSON><PERSON>", "theater": "theatara", "outdoors": "kantle", "gardening": "bojala", "partying": "mokete", "gaming": "<PERSON><PERSON><PERSON>", "boardgames": "<PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "dungeonsanddragons", "chess": "chess", "fortnite": "fortnite", "leagueoflegends": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "lijo", "baking": "ho_baka", "cooking": "ho_pheha", "vegetarian": "vejet<PERSON><PERSON><PERSON>", "vegan": "vegan", "birds": "linonyana", "cats": "dikatse", "dogs": "libese", "fish": "tlhapi", "animals": "liphoofolo", "blacklivesmatter": "batšomehlophaphetseng", "environmentalism": "tikoloho", "feminism": "bohlale_ba_basali", "humanrights": "<PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "motsoealletsalgbtq", "stopasianhate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "motšehetsiwabalgbtqia", "volunteering": "b<PERSON><PERSON><PERSON>", "sports": "<PERSON><PERSON><PERSON>", "badminton": "fethereball", "baseball": "besebolo", "basketball": "basketball", "boxing": "bokeseng", "cricket": "krikete", "cycling": "boi<PERSON><PERSON><PERSON><PERSON><PERSON>ala<PERSON>", "fitness": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "football": "bolo", "golf": "golf", "gym": "gym", "gymnastics": "khimnastiki", "hockey": "hokei", "martialarts": "b<PERSON><PERSON><PERSON><PERSON>", "netball": "neteball", "pilates": "pilates", "pingpong": "phing<PERSON>g", "running": "matha", "skateboarding": "skateboarding", "skiing": "go_ya_lehlwa", "snowboarding": "ho_the<PERSON><PERSON>_lehlweng", "surfing": "ho_surfa", "swimming": "<PERSON>sa", "tennis": "tenese", "volleyball": "volleyball", "weightlifting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "yoga", "scubadiving": "hoq<PERSON><PERSON><PERSON><PERSON>", "hiking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "capricorn": "kgudu", "aquarius": "aquarius", "pisces": "lipaisese", "aries": "aries", "taurus": "thoro", "gemini": "gemini", "cancer": "kankere", "leo": "leo", "virgo": "<PERSON><PERSON><PERSON><PERSON>", "libra": "tekano", "scorpio": "sekorepio", "sagittarius": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shortterm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "tloaelo", "longtermrelationship": "leratole<PERSON>lele", "single": "mong", "polyamory": "lerato_la_batho_ba_bangata", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "mosali_ratang_basali", "bisexual": "biseksuele", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "moh<PERSON>baniwama<PERSON>reste", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "moro<PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "<PERSON><PERSON><PERSON><PERSON>", "legendofspyro": "tšōmosealepiri", "rouguelikes": "lipapalitsejoekalirouguelike", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "letsatsilefa<PERSON>dimo", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "t<PERSON><PERSON><PERSON><PERSON>", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "ntoamasekaseka", "openworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "moeaseotsoanang", "dungeoncrawling": "hoqoll<PERSON>ading<PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "sefofane", "lordsoftherealm2": "marenametušeng2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "medabots": "medabots", "lodsoftherealm2": "mahlanyahoalichaba2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "lipapalimotsasengqondo", "okage": "okage", "juegoderol": "papalieatšoanang", "witcher": "witcher", "dishonored": "nye<PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "palo", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "phetola", "charactercreation": "bop<PERSON><PERSON><PERSON><PERSON>", "immersive": "ken<PERSON><PERSON><PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasykhale", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "leqhe<PERSON>_la_lerato", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "ma<PERSON><PERSON>", "pathfinder": "moetapele", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "morethethoeathoase", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "morena_e_moholo", "yourturntodie": "kenakoeahoushoa", "persona3": "persona3", "rpghorror": "papadi_ea_let<PERSON><PERSON>o", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "papadi_ya_mongolo", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "meoaeoabademone", "mu": "mu", "falloutshelter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "nthomoselefeifi", "eclipsephase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "libolalelikantle", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "tlam<PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloimmortal": "diaboloesalefeleng", "dynastywarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skullgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "nalefayahogwarts", "madnesscombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "road96": "tsela96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "lipapalikarogu", "gothamknights": "gothamknights", "forgottenrealms": "liboparelitheilweng", "dragonlance": "dragonlance", "arenaofvalor": "arenaeabohlale", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childoflight": "ngoanaakhanya", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "papallothebeli2", "lineage2": "lineage2", "digimonworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "makwerekwere", "vulcanverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fracturedthrones": "literonethauohobehileng", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "se<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltabohlooho", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "otla", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON>lo", "starfinder": "moq<PERSON><PERSON><PERSON><PERSON><PERSON>", "goldensun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "b<PERSON><PERSON>bibolikheth<PERSON>", "bladesinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "bosiu2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkkhubelu", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "taeloyaweleng", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "ma<PERSON><PERSON>tl<PERSON><PERSON>", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "bomodimo", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "maswabialelekale", "adventurequest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON>eb<PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofsymphonia": "lipalelatsamphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "motsecity", "myfarog": "farogoeaka", "sacredunderworld": "lefifiling_le_halalelang", "chainedechoes": "molumo_o_phethang", "darksoul": "moe<PERSON><PERSON>", "soulslikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "othercide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mountandblade": "nto<PERSON><PERSON><PERSON>e", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "nakoeketsang", "pillarsofeternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "lik<PERSON><PERSON>", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "dumelach<PERSON><PERSON><PERSON>", "legendofdragoon": "legendofdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "leholoananyane", "childrenofmorta": "banabomorta", "engineheart": "pelong", "fable3": "pale3", "fablethelostchapter": "paleleabolahlehileng", "hiveswap": "phetolahive", "rollenspiel": "papadi_ya_likarolo", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "paradiselekanyolehore", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "lebolapanale<PERSON>", "oldschoolrevival": "k<PERSON><PERSON><PERSON>tsomoraotsakgale", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "peloeatebele1", "ff9": "ff9", "kingdomheart2": "peloeatebohale2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "lipapalisarpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "peloeatebohare3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "lelokomalkavian", "harvestella": "harvestella", "gloomhaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "qhobosheane", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "meholesothoaearcadia", "shadowhearts": "lip<PERSON>thi<PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "moea_oa_mollo4", "mother3": "mme3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "edenengoe", "roleplaygames": "lipapalihobapalakhale", "roleplaygame": "papa<PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "pelong_ya<PERSON><PERSON>i", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirilengkhalefateng", "dračák": "tloholo", "spelljammer": "morallong", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "<PERSON><PERSON><PERSON><PERSON>", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "lefatshelemabutakgolo", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "foramesarpg", "shadowheartscovenant": "selekanosamoyaomothomo", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "puso<PERSON><PERSON><PERSON>", "awplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theworldendswithyou": "lefatšelefelakaleona", "dragalialost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elderscroll": "<PERSON><PERSON><PERSON>akhol<PERSON>", "dyinglight2": "lefu_le_khanyang2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "thuto<PERSON><PERSON>lo", "shoptitans": "barekisititane", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "bole<PERSON><PERSON><PERSON><PERSON>", "blackbook": "b<PERSON><PERSON><PERSON>", "skychildrenoflight": "letsatsabaleholimo", "gryrpg": "gryrpg", "sacredgoldedition": "kgatisoyakolaneyakgauta", "castlecrashers": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "papalieasegothic", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwiretokyosgame", "fallout2d20": "fallout2d20", "gamingrpg": "<PERSON>lirpg", "prophunt": "prophunt", "starrails": "litelaats<PERSON><PERSON><PERSON>", "cityofmist": "motsefumo", "indierpg": "papalieatiketheoetseng", "pointandclick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "sakekengoeng", "freeside": "kantle", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "kamoraocyberpunk", "deathroadtocanada": "tselalalelefucanada", "palladium": "palladiamo", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona5": "persona5", "ghostoftsushima": "moeanajapane", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nier": "nier", "dothack": "etsahack", "ys": "ys", "souleater": "m<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "papading_ya_maano", "mahoyo": "mahoyo", "animegames": "lipapalisaanime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "pinaleyakomeletseng", "princessconnect": "khokahanoe<PERSON><PERSON><PERSON><PERSON>", "hexenzirkel": "hexenzirkel", "cristales": "dikristale", "vcs": "<PERSON><PERSON><PERSON><PERSON>", "pes": "pes", "pocketsage": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantleindia", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "lipapalisaehleketroniking", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "papadi_ea_bohlale", "mlg": "mlg", "leagueofdreamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "bolo<PERSON><PERSON>", "dreamhack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gaimin": "papa<PERSON>", "overwatchleague": "<PERSON>ing<PERSON><PERSON><PERSON><PERSON><PERSON>", "cybersport": "<PERSON><PERSON>_tsa_dikh<PERSON><PERSON>a", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "ephumula", "brasilgameshow": "<PERSON>ile<PERSON>š<PERSON><PERSON>si<PERSON>", "valorantcompetitive": "valo<PERSON><PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "bophahobokahalofo", "left4dead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead2": "sietselang2", "valve": "valve", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "lehloelakaselefeleng", "goatsimulator": "phoofololeboli", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transformice": "transformice", "justshapesandbeats": "libopehofeletsebamino", "battlefield4": "ntoamarung4", "nightinthewoods": "borokomanaheng", "halflife2": "halflife2", "hacknslash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "kotsiolapula2", "metroidvanias": "metroidvanias", "overcooked": "fetisisitse", "interplanetary": "lipolanete", "helltaker": "mola<PERSON><PERSON><PERSON><PERSON><PERSON>", "inscryption": "inscryption", "7d2d": "matsatsi7hokae2", "deadcells": "lise<PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "<PERSON><PERSON><PERSON>", "stray": "makgowa", "battlefield": "ntoa", "battlefield1": "nthabelepele1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "mahlo", "blackdesert": "le<PERSON>ata<PERSON>š<PERSON><PERSON>", "tabletopsimulator": "papalieatafole", "partyhard": "moketethololo", "hardspaceshipbreaker": "morobispaceshipbreaker", "hades": "hades", "gunsmith": "sets<PERSON><PERSON>_sa_lithunya", "okami": "<PERSON>ami", "trappedwithjester": "koaletsoelekamohlankana", "dinkum": "dinkum", "predecessor": "moetapele", "rainworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "ntoamolemo", "minionmasters": "minionmasters", "grimdawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkanddarker": "le<PERSON><PERSON><PERSON>behang", "motox": "motox", "blackmesa": "<PERSON><PERSON><PERSON><PERSON>", "soulworker": "mosebet<PERSON>emoea", "datingsims": "<PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "def<PERSON><PERSON><PERSON>", "kenopsia": "kenopsia", "virtualkenopsia": "kenops<PERSON><PERSON>la", "snowrunner": "khoro_lehloa", "libraryofruina": "laeborariearuina", "l4d2": "l4d2", "thenonarygames": "lipapalinonarygames", "omegastrikers": "omegastrikers", "wayfinder": "moetapele", "kenabridgeofspirits": "kena<PERSON><PERSON><PERSON><PERSON><PERSON>", "placidplasticduck": "ntataplastikiekhutšitsoeng", "battlebit": "ntwantšo", "ultimatechickenhorse": "nokoaseolipalamoutloa", "dialtown": "toropoyamohala", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "bosoabalikatse", "supermeatboy": "mots<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinnybunny": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "t<PERSON><PERSON><PERSON>", "callofduty": "callofduty", "callofdutyww2": "mohoeladintoantsaelefatshelabobelilaworld", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "meeli", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "ntlhafatso", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "papadi_farcrygames", "paladins": "dipaladins", "earthdefenseforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto5": "grandtheftauto5", "warz": "ntoa", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "bolaeaholo", "joinsquad": "kenyeletsehosehlop<PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "ntoantlhaloanelemohlatsamoea", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "lepolaea3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "lefu_le_tsa<PERSON><PERSON>g", "b4b": "b4b", "codwarzone": "ntoatsemotho", "callofdutywarzone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "codzombies": "codzombies", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "likarolo2", "killzone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "ntwazombietsahatse", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "ntoatsefoloha", "crosscode": "k<PERSON><PERSON><PERSON>š<PERSON>", "goldeneye007": "goldeneye007", "blackops2": "ntoapele2", "sniperelite": "moq<PERSON><PERSON>_ea_h<PERSON><PERSON><PERSON><PERSON>", "modernwarfare": "nto<PERSON><PERSON><PERSON><PERSON>", "neonabyss": "le<PERSON><PERSON><PERSON>", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "owerwatch": "ower<PERSON>", "rtype": "mofalirtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "ntoateboqhoma", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tsamepe", "back4blood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "moh<PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "<PERSON>š<PERSON><PERSON>yoeamokgwa", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "palekgolo", "doometernal": "timilothalesafeleng", "centuryageofashes": "lekhololadilemotsaemolora", "farcry4": "farcry4", "gearsofwar": "ntoaeatšepe", "mwo": "mwo", "division2": "karohano2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "molokolo<PERSON><PERSON>a", "enterthegungeon": "kenaholemonglapotlakanyane", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "ntoaeatsamajoale2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "bohlokobantlo", "warface": "fahlehoease<PERSON><PERSON><PERSON>lo", "crossfire": "moh<PERSON><PERSON><PERSON><PERSON>", "atomicheart": "pelotha<PERSON>eat<PERSON>", "blackops3": "blackops3", "vampiresurvivors": "bampaerepholosang", "callofdutybatleroyale": "callof<PERSON>tyn<PERSON>ap<PERSON><PERSON>", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "tokoloho", "battlegrounds": "<PERSON><PERSON><PERSON>", "frag": "bolaea", "tinytina": "tiki<PERSON>", "gamepubg": "papalieatsa", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "<PERSON><PERSON><PERSON><PERSON>", "convertstrike": "fetolak<PERSON><PERSON><PERSON>", "warzone2": "ntoahamabili2", "shatterline": "ntloafatso", "blackopszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodymess": "moferefere", "republiccommando": "molaodimpolitiki", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "<PERSON><PERSON><PERSON><PERSON>", "destiny1": "papiso1", "gamingfps": "papalisefps", "redfall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubggirl": "pubgn<PERSON><PERSON><PERSON>", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "kentse", "farlight": "<PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "lihlolloanyaneatsatina", "halo2": "halo2", "payday2": "moholo2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "soapcod", "ghostcod": "se<PERSON>kosamolao", "csplay": "papali_ya_cs", "unrealtournament": "papaling_e_sa_nnetefalang", "callofdutydmz": "callofdutydmz", "gamingcodm": "papalisethobane", "borderlands2": "borderlands2", "counterstrike": "<PERSON><PERSON><PERSON>etsoeamokotaba", "cs2": "cs2", "pistolwhip": "sabole", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "mokatokamekete", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonešweu", "remnant": "masalela", "azurelane": "azurelane", "worldofwar": "ntoakholo", "gunvolt": "gunvolt", "returnal": "<PERSON><PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "mon<PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "morodumo2", "microvolts": "lim<PERSON><PERSON><PERSON>s", "reddead": "lefu_le_lefubelu", "standoff2": "ntoapapedi2", "harekat": "harekat", "battlefield3": "ntoa3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "leoatlalaterona", "rust": "mofero", "conqueronline": "conqueronline", "dauntless": "sebete", "warships": "likepe_tsa_ntoa", "dayofdragons": "letsakamakgwakgwane", "warthunder": "ntoatoloana", "flightrising": "sefofanese<PERSON><PERSON>hamisang", "recroom": "kamoro_ya_boi<PERSON><PERSON><PERSON>o", "legendsofruneterra": "lipaletalikheichene", "pso2": "pso2", "myster": "<PERSON><PERSON><PERSON><PERSON>", "phantasystaronline2": "papadi_ya_nalane_ya_inthaneteng2", "maidenless": "haunyetsoe", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON>let<PERSON><PERSON>", "crossout": "phumola", "agario": "agario", "secondlife": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "papadi_ya_ma<PERSON>rang", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "pheletsenginthaneteng", "superanimalroyale": "ntoana<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "papalieatseleng", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "makererere", "newworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "papali_ya_batho_ba_bangata", "pirate101": "pirate101", "honorofkings": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "ntwasefabatswana2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dchat": "puisano3d", "nostale": "haelesale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "papali_ya_moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "silkroad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "spiralknights", "mulegend": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "startrekonline": "startrekkainthaneteng", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "lehananalemoraea", "evony": "evony", "dragonsprophet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grymmo": "grymmo", "warmane": "mofuthu", "multijugador": "papadi_ya_batho_ba_bangata", "angelsonline": "mangelatainthaneteng", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseinthaneteng", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "<PERSON><PERSON>k<PERSON><PERSON>", "blueprotocol": "blueprotocol", "perfectworld": "lefatšelakgabane", "riseonline": "phahama_inthaneteng", "corepunk": "corepunk", "adventurequestworlds": "le<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyforfun": "fofallamonate", "animaljam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "borengehabokgalayo", "cityofheroes": "toropoeatjhes<PERSON>", "mortalkombat": "ntoanatš<PERSON>leng", "streetfighter": "n<PERSON><PERSON>kh<PERSON><PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "b<PERSON>ng<PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "papaling_e_nang_le_molato", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "papalieatšoanakamoka", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streetsofrage": "literatamotsephara", "mkdeadlyalliance": "mkbolaomekamanosefahlehong", "nomoreheroes": "<PERSON><PERSON>abahale", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON>", "likeadragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "lipapali<PERSON><PERSON><PERSON>l<PERSON>", "blasphemous": "nye<PERSON>lo", "rivalsofaether": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "superpetla", "mugen": "mugen", "warofthemonsters": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "lipapalisengntoa", "cyberbots": "dicyberbots", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "ntoafinal<PERSON><PERSON><PERSON><PERSON>", "poweredgear": "matlapowerete", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "papalilidintwa", "killerinstinct": "b<PERSON><PERSON><PERSON><PERSON>", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON>", "chivalry2": "bohale2", "demonssouls": "moea_ya_diabolose", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknightsequel": "papadi_e_latelang_ya_hollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "bokhopa_ba_silksong", "silksonggame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "silksongnews": "litsebotsasilksong", "silksong": "silksong", "undernight": "bosiubongata", "typelumina": "ngola<PERSON><PERSON>", "evolutiontournament": "<PERSON><PERSON>k<PERSON><PERSON>", "evomoment": "m<PERSON><PERSON><PERSON><PERSON>", "lollipopchainsaw": "lollipopketane", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodborne": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizon": "<PERSON><PERSON><PERSON><PERSON>", "pathofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slimerancher": "molemimafura", "crashbandicoot": "crashbandicoot", "bloodbourne": "madi_t<PERSON><PERSON><PERSON><PERSON><PERSON>", "uncharted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "hoqetelahoarona", "infamous": "tummeng", "playstationbuddies": "metsoallepapad<PERSON>", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "ka<PERSON>hanyasehlekehleke", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "modimontwa", "gris": "gris", "trove": "<PERSON><PERSON><PERSON><PERSON>", "detroitbecomehuman": "detroiteket<PERSON>mot<PERSON>", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "moputs<PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "mori<PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "crashteamracing", "fivepd": "ntlhano", "tekken7": "tekken7", "devilmaycry": "deabolokaseke", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "bapalasetatjhene", "samuraiwarriors": "bahlabanibasamurai", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "tsoma_banna", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "peletsamoea2selekane", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "chelete", "cd": "cd", "gamepass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "armello": "armello", "partyanimal": "m<PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "<PERSON><PERSON><PERSON>", "mhw": "mhw", "princeofpersia": "morenaoaperesia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "ntwantšo", "dontstarvetogether": "resengoala", "ori": "ori", "spelunky": "lehodimong", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "na<PERSON><PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "moq<PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable2": "tšōmo2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "mananeo_a_sekgala", "skycotl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erica": "erica", "ancestory": "botate", "cuphead": "cuphead", "littlemisfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sallyface": "sefahl<PERSON><PERSON>ly", "franbow": "franbow", "monsterprom": "kolebekgoaba", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "lebalenglebohol<PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "thap<PERSON><PERSON><PERSON><PERSON>", "duckgame": "papet<PERSON>", "thestanleyparable": "papadi_ya_stanley", "towerunite": "kop<PERSON><PERSON><PERSON><PERSON>a", "occulto": "<PERSON><PERSON><PERSON>", "longdrive": "leeto_le_lelelele", "satisfactory": "kgotsofalo", "pluviophile": "moratian<PERSON><PERSON>", "underearth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON>", "darkdome": "ntl<PERSON><PERSON><PERSON>", "pizzatower": "thoto<PERSON><PERSON><PERSON>", "indiegame": "papadi_e_ikemetseng", "itchio": "itchio", "golfit": "igolefa", "truthordare": "nneteokan<PERSON><PERSON><PERSON><PERSON>", "game": "papali", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampoline", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "sebete", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "lip<PERSON><PERSON>erapeng", "pickanumber": "k<PERSON><PERSON><PERSON>o", "trueorfalse": "nnetekapa", "beerpong": "papaling", "dicegoblin": "taise<PERSON><PERSON>u", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "pap<PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "<PERSON><PERSON><PERSON>", "mahjong": "mahjong", "jeux": "<PERSON><PERSON><PERSON>", "simulationgames": "lipapalit<PERSON><PERSON><PERSON><PERSON>", "wordgames": "papadi_tsa_mantswe", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "lip<PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON>a", "boredgames": "lip<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "papa<PERSON>", "interactivegames": "lipapalinabalang", "amtgard": "amtgard", "staringcontests": "tlhodisanokashebana", "spiele": "papa<PERSON>", "giochi": "<PERSON><PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "lipapalietsaiphone", "boogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegame": "papaliakrane", "hideandseek": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hopscotch": "khak<PERSON>", "arcadegames": "lipapalisaarcade", "yakuzagames": "lip<PERSON><PERSON><PERSON>aya<PERSON><PERSON>", "classicgame": "<PERSON><PERSON>ekhale", "mindgames": "<PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "galagames", "romancegame": "<PERSON><PERSON><PERSON><PERSON>", "yanderegames": "lipapaliyanderegame<PERSON>", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4xgames": "lipapali4x", "gamefi": "<PERSON><PERSON><PERSON><PERSON>", "jeuxdarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "lipapalitatafole", "metroidvania": "metroidvania", "games90": "lipapali90", "idareyou": "kelo<PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "dip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "lip<PERSON>ling_tsa_lebelo", "ets2": "ets2", "realvsfake": "nnetevskgakgakgo", "playgames": "bapalekemang", "gameonline": "papaliinthaneteng", "onlinegames": "lipapaliinthaneteng", "jogosonline": "lip<PERSON><PERSON><PERSON>", "writtenroleplay": "papadi_ya_karolo_e_ngotsweng", "playaballgame": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "papadi_ya_ho_taka", "coopgames": "lipapalishoakhelebano", "jenga": "jenga", "wiigames": "dipapading_tsa_wii", "highscore": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "papa<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "lipapalisengburger", "kidsgames": "lip<PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "bapalitcg", "juegodepreguntas": "papadi_ya_dipotso", "gioco": "papa<PERSON>", "managementgame": "papa<PERSON><PERSON><PERSON>", "hiddenobjectgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roolipelit": "<PERSON><PERSON><PERSON>ak<PERSON><PERSON>", "formula1game": "papaling1", "citybuilder": "moa<PERSON><PERSON><PERSON>", "drdriving": "khorokhorokhanna", "juegosarcade": "lipapalisaarcade", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "papa<PERSON><PERSON><PERSON>", "blowgames": "<PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "metjenapinball", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "ntlontoana_sofeng", "perguntados": "lipotso", "gameo": "papali", "lasergame": "<PERSON><PERSON><PERSON><PERSON>", "imessagegames": "lipapalinetsamessage", "idlegames": "lipapalimorao", "fillintheblank": "tlatsamoselacoroane", "jeuxpc": "lipapaling_pc", "rétrogaming": "papali_tsa_kgale", "logicgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "japangame": "papalisajapane", "rizzupgame": "p<PERSON><PERSON><PERSON>_papali", "subwaysurf": "<PERSON><PERSON><PERSON>lemethap<PERSON>", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5vs5", "rolgame": "papadi_ya_likarolo", "dashiegames": "<PERSON><PERSON><PERSON><PERSON>", "gameandkill": "papalilempola<PERSON>", "traditionalgames": "lipapalitstsakgale", "kniffel": "kniffel", "gamefps": "<PERSON><PERSON><PERSON><PERSON>", "textbasedgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "moraopapadi", "thiefgame": "papaling", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "bololapatla", "tischfußball": "tischfußball", "spieleabende": "papalibosiung", "jeuxforum": "jeuxforum", "casualgames": "lipapalisatloaelo", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "lipapalietsahaba", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "lipapalilikrane", "játék": "papali", "bordfodbold": "bordfodbold", "jogosorte": "jogosorte", "mage": "mage", "cargames": "lipapalisadi<PERSON>loi", "onlineplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "lipapalibosiung", "pursebingos": "purseselisebingo", "randomizer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "dipapading_pc", "socialdeductiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominos": "<PERSON><PERSON><PERSON>", "domino": "domino", "isometricgames": "lipapali_tsa_isometric", "goodoldgames": "lip<PERSON>lisakhale", "truthanddare": "nneteekalefano", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "lipaletsompe", "jeuxvirtuel": "papadi_ea_di<PERSON>hale", "romhack": "papano_ea_lipapali", "f2pgamer": "motshamekingeaselefelengale", "free2play": "bapa<PERSON><PERSON><PERSON>", "fantasygame": "papalingenyalo", "gryonline": "grybainthaneteng", "driftgame": "papalinguea", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "halolipatlaetelevishinelemethaba", "mushroomoasis": "set<PERSON><PERSON><PERSON>_sa_likhomo_tsa_mahapu", "anythingwithanengine": "nthoefeealeenenjene", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "sabole_le_boloi", "goodgamegiving": "papalientsofetse", "jugamos": "realeb<PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "lip<PERSON><PERSON>engthebelitseng", "minaturegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "leratolarona", "gamemodding": "papalimodding", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spelletjes": "papa<PERSON>", "spacenerf": "sekgotlasebaka", "charades": "<PERSON><PERSON><PERSON>_tsa_ho_bontsha", "singleplayer": "motsham<PERSON><PERSON>", "coopgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamed": "pap<PERSON>e", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "papali_e_kholo", "kingdiscord": "morenawadiscord", "scrabble": "sekrabole", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "beke", "onitama": "onitama", "pandemiclegacy": "sephethosakhoronapandemiki", "camelup": "maka<PERSON>e", "monopolygame": "papalimonopoly", "brettspiele": "papadi_tsa_boto", "bordspellen": "dipapadi_tsa_boto", "boardgame": "papali_ya_boto", "sällskapspel": "<PERSON><PERSON><PERSON>_tsa_sechaba", "planszowe": "papadi_tsa_boto", "risiko": "kotsi", "permainanpapan": "p<PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombicide", "tabletop": "ta<PERSON>leepapali", "baduk": "baduk", "bloodbowl": "papaliea_mali", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "eatsetshabatlatafole", "connectfour": "hokahananeng", "heroquest": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidatavolo": "lipapalitatafole", "farkle": "farkle", "carrom": "khar<PERSON>", "tablegames": "lip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "papalieatebula", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "lipapalishadeskeng", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "kopanoleekholo", "creationludique": "boqapiqammonate", "tabletoproleplay": "papalieatepele", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "lipapaliswitchboard", "infinitythegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "lefumolotš<PERSON>", "yahtzee": "yahtzee", "chutesandladders": "<PERSON><PERSON><PERSON>lem<PERSON><PERSON>o", "társas": "<PERSON><PERSON><PERSON>", "juegodemesa": "papalieatepele", "planszówki": "liba<PERSON><PERSON>", "rednecklife": "bophelomafene", "boardom": "<PERSON><PERSON><PERSON>", "applestoapples": "apolekaapole", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "papaliboramapolanka", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "krokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "twilightimperium", "horseopoly": "<PERSON><PERSON><PERSON><PERSON>", "deckbuilding": "hoahakolitshelete", "mansionsofmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "lipapaliboleng", "shadowsofbrimstone": "meritibrimstone", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON>", "warcaby": "n<PERSON><PERSON><PERSON><PERSON>", "táblajátékok": "<PERSON><PERSON><PERSON>_tsa_boto", "battleship": "sekepe_sa_ntoa", "tickettoride": "lekeletsamoeteose", "deskovehry": "lipapalisefing", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "papalieatafole", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "papal<PERSON><PERSON>", "gesellschaftsspiele": "papadi_tsa_se<PERSON>ba", "starwarslegion": "starwarslegion", "gochess": "eachess", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "n<PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "arksurvivalevolved", "dayz": "<PERSON><PERSON><PERSON>", "identityv": "identityv", "theisle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelastofus": "batlafeletseng", "nomanssky": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "moq<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofcthulhu": "piletsolekgaufi", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "ha<PERSON><PERSON>", "eco": "eco", "monkeyisland": "setlhekehletsemane", "valheim": "valheim", "planetcrafter": "morapiwapolanete", "daysgone": "<PERSON><PERSON><PERSON><PERSON>", "fobia": "<PERSON><PERSON><PERSON>", "witchit": "ebolaitse", "pathologic": "lefu_la_tshoaetso", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "matsatsi7hofihlela", "thelongdark": "bosiunemabobothata", "ark": "areka", "grounded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "bofalabobolalaneng2", "vrising": "vrising", "madfather": "ntataehlanya", "dontstarve": "<PERSON><PERSON>aq<PERSON><PERSON>", "eternalreturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "tselaeatitane", "frictionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexen": "boloi", "theevilwithin": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "realrac", "thebackrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiressmp": "empiressmp", "blockstory": "palekanokhetho", "thequarry": "sebaka", "tlou": "tlou", "dyinglight": "lesolofeelang", "thewalkingdeadgame": "papalingalobalefung", "wehappyfew": "remorat<PERSON><PERSON><PERSON><PERSON>", "riseofempires": "ntlafatsommuso", "stateofsurvivalgame": "papalieasetjhabathoephelang", "vintagestory": "palesekgale", "arksurvival": "pholohelosearkeng", "barotrauma": "barotrauma", "breathedge": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "pholohelomophebelong", "beastsofbermuda": "libatlalatšabermu<PERSON>", "frostpunk": "leq<PERSON>a", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "moro<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "seretse_se_se_nang_letho", "lifeaftergame": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "more<PERSON>ane", "kuon": "kuon", "cryoffear": "lloelatshabeng", "raft": "sekepe", "rdo": "rdo", "greenhell": "thabomotala", "residentevil5": "residentevil5", "deadpoly": "lefu_la_poly", "residentevil8": "residentevil8", "onironauta": "moh<PERSON><PERSON><PERSON><PERSON>", "granny": "nkhono", "littlenightmares2": "toropelenyenyaneeatšabehang2", "signalis": "mapontsa", "amandatheadventurer": "am<PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "barabamoroka", "rustvideogame": "papalieavideoearust", "outlasttrials": "melekoeatshoareng", "alienisolation": "tloaelotsoauoa", "undawn": "mafube", "7day2die": "matsatsi7hokaofela", "sunlesssea": "leoatlenglenaletsatsi", "sopravvivenza": "pholoho", "propnight": "bosiunyathelano", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "moh<PERSON><PERSON>evamp<PERSON>", "deathverse": "le<PERSON><PERSON><PERSON>", "cataclysmdarkdays": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkermoritichernobyl", "lifeafter": "bophellokamor<PERSON>", "ageofdarkness": "nako_ea_lefifi", "clocktower3": "torongtsoatharo", "aloneinthedark": "keno<PERSON>", "medievaldynasty": "lebolopelal<PERSON><PERSON>bala", "projectnimbusgame": "papa<PERSON><PERSON><PERSON><PERSON>", "eternights": "bosiubosakheleng", "craftopia": "bokhellochab<PERSON><PERSON>", "theoutlasttrials": "litekono<PERSON><PERSON><PERSON><PERSON><PERSON>", "bunker": "bunkara", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioasassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kcrush", "wh40": "wh40", "warhammer40klove": "warhammer40kra<PERSON>ello", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "ntoanalebahlanka3", "temploculexus": "temploculexus", "vindicare": "phe<PERSON><PERSON>o", "ilovesororitas": "keratasororitas", "ilovevindicare": "keratamolato", "iloveassasinorum": "kerataassasinorum", "templovenenum": "le<PERSON>lajo<PERSON><PERSON><PERSON>", "templocallidus": "lehodi<PERSON><PERSON><PERSON><PERSON>", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "tau", "ageofempires": "<PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "bohob<PERSON><PERSON><PERSON>", "wingspan": "mapheo", "terraformingmars": "<PERSON><PERSON><PERSON><PERSON><PERSON>ts<PERSON>", "heroesofmightandmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "btd6": "btd6", "supremecommander": "molaodikgolo", "ageofmythology": "nako_ea_dingoloi", "args": "l<PERSON><PERSON><PERSON>o", "rime": "leq<PERSON>a", "planetzoo": "planetazoo", "outpost2": "outpost2", "banished": "<PERSON><PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "kotsiefubelu", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "n<PERSON>ae<PERSON><PERSON><PERSON>ng", "strategygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization4": "peakanyo4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "n<PERSON><PERSON><PERSON><PERSON>", "travian": "travian", "forts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodcompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heidentum": "boh<PERSON><PERSON>", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "lebelohofetaletsona", "forthekings": "bakenabakgosi", "realtimestrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eu4": "eu4", "vainglory": "boi<PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "bolimo", "anno": "<PERSON><PERSON><PERSON>", "battletech": "ntoanatechniki", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "sekelasakenamorosadave", "plagueinc": "lefu_la_sechaba", "theorycraft": "theorycraftsesotho", "mesbg": "mesbg", "civilization3": "moetlo3", "4inarow": "4kaditekanyane", "crusaderkings3": "morenaekhalefo3", "heroes3": "bahlabani3", "advancewars": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "ntsoadimmusosetharo2", "disciples2": "barutuwa2", "plantsvszombies": "lime<PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stratejioyunları": "papadi_tsa_leano", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "morenaedinosa<PERSON>", "worldconquest": "horap<PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "lipelomeatsitso4", "companyofheroes": "kopanoeatebatho", "battleforwesnoth": "ntwatlantona", "aoe3": "aoe3", "forgeofempires": "ntoanatsachaba", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phobies": "<PERSON><PERSON><PERSON><PERSON>", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "kantle", "turnbased": "phetohoka<PERSON>", "bomberman": "bomberman", "ageofempires4": "ntoaeareinotsebongsa4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "morenaebakreste", "cultris2": "cultris2", "spellcraft": "boloi", "starwarsempireatwar": "ntoaselaletsoenoaemmuso", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "maano", "popfulmail": "popfulmail", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "mokhoebatsamaisanolikerete", "unrailed": "lelelokafofane", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "toromenteamoqhobosheane", "uplandkingdoms": "mare<PERSON><PERSON>_holimo", "galaxylife": "bophelo_ba_leholimo", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "phehellaph<PERSON>", "battlecats": "lipitsabyoadintoa", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "mots<PERSON><PERSON><PERSON>", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "lebelo_ke_tsona", "needforspeedcarbon": "tlhokoealebelocarbon", "realracing3": "lipapalitsamabili3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "sims_e_bapalang_mahala", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "phel<PERSON><PERSON>e", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phasmophobia": "tshabo_ya_meya", "fivenightsatfreddys": "ma<PERSON>diahlanoakwafred<PERSON>s", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "tsoho", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "lapeng", "deadisland": "sehlekehlekesefeleng", "litlemissfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzero": "moreroapele", "horory": "<PERSON><PERSON><PERSON><PERSON>", "jogosterror": "papadi_tse_tshosang", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "dumelandumelwane2", "gamingdbd": "papaliea_dbd", "thecatlady": "mmedika<PERSON><PERSON>", "jeuxhorreur": "<PERSON><PERSON><PERSON>_tse_t<PERSON>osang", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtg": "mtg", "tcg": "<PERSON><PERSON><PERSON>_tsa_likarete", "cardsagainsthumanity": "likaretek<PERSON>hl<PERSON>nglebat<PERSON>", "cribbage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "mabit<PERSON>_a_se<PERSON>ri", "dixit": "dixit", "bicyclecards": "lika<PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legendofrun<PERSON>a", "solitaire": "solitaire", "poker": "pokere", "hearthstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "maqheka_a_likarete", "playingcards": "lika<PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "lika<PERSON><PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sportscards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "ntoapakareteyavanguard", "duellinks": "duellinks", "spades": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcry": "nthonyana", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "morenaealipelo", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "<PERSON>han<PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "likhareteyugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "<PERSON>ise<PERSON>gio<PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "papali_ya_yugioh", "darkmagician": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "mahloampshetshoporokokhoeu", "yugiohgoat": "yug<PERSON><PERSON>kh<PERSON>o", "briscas": "theletseke", "juegocartas": "<PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "ketekete", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON>lik<PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON>lik<PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "belotekainthanete", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "meoyaentoa", "battlespiritssaga": "ntoapamoea", "jogodecartas": "<PERSON><PERSON><PERSON>", "žolíky": "li<PERSON><PERSON><PERSON>", "facecard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfight": "n<PERSON><PERSON><PERSON>e", "biriba": "biriba", "deckbuilders": "baa<PERSON><PERSON><PERSON>", "marvelchampions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magiccartas": "lithokotsemolao", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "papadi_ya_moriti", "skipbo": "tlolabo", "unstableunicorns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberse": "saeb<PERSON>", "classicarcadegames": "lipapalilikhaletsaclassic", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "bosiumarapamonate", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "<PERSON><PERSON><PERSON><PERSON>", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "tan<PERSON><PERSON><PERSON><PERSON>hareng", "rhythmgamer": "seba<PERSON><PERSON><PERSON><PERSON>", "stepmania": "mohato", "highscorerythmgames": "lipapalotseliphahamengtsarhythm", "pkxd": "pkxd", "sidem": "saidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "molumooa_molumo", "rhythmheaven": "rhythmheaven", "hypmic": "hypmic", "adanceoffireandice": "tantšoamolloleseleqhooa", "auditiononline": "nkaholainethenete", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "lipapalisaritimo", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "ngakayamoretlo", "cubing": "ho_bapala_khiubu", "wordle": "wordle", "teniz": "tenizi", "puzzlegames": "lip<PERSON><PERSON><PERSON>", "spotit": "ebona", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "me<PERSON><PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "meqaqantsoane", "rubikscube": "kubusearubiks", "crossword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "motscroisés": "motscroisés", "krzyżówki": "litharotha", "nonogram": "nonogram", "bookworm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "<PERSON><PERSON><PERSON>", "indovinello": "thoth<PERSON><PERSON>", "riddle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riddles": "<PERSON><PERSON><PERSON>", "rompecabezas": "phapano", "tekateki": "tekateki", "inside": "kahare", "angrybirds": "linonyanatsehalefile", "escapesimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minesweeper": "moqoqo", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "mantsoe_a_t<PERSON><PERSON>na", "kurushi": "k<PERSON>hi", "gardenscapesgame": "papaliea_gardenscapes", "puzzlesport": "papalisekgopolo", "escaperoomgames": "lipapalina<PERSON><PERSON><PERSON><PERSON>", "escapegame": "papalelocheche", "3dpuzzle": "phuzele3d", "homescapesgame": "papaliealapeng", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "enigmistica", "kulaworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myst": "<PERSON><PERSON><PERSON><PERSON>", "riddletales": "lipalelikhutlo", "fishdom": "fishdom", "theimpossiblequiz": "lipotsobotekenyelitsoeng", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "papali3tšoanang", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "kgwirky", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "cubor<PERSON>k", "yapboz": "<PERSON><PERSON><PERSON><PERSON>", "thetalosprinciple": "molaothetalosprinciple", "homescapes": "ma<PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "nkubutsemeqatjhwa", "tycoongames": "papa<PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "cubosderubik", "cruciverba": "mantswease<PERSON><PERSON>", "ciphers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rätselwörter": "mants<PERSON>_a_<PERSON><PERSON><PERSON><PERSON>o", "buscaminas": "buscaminas", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "moshanyanaerepete", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "<PERSON><PERSON><PERSON><PERSON>", "guessing": "ho<PERSON><PERSON>tsa", "nonograms": "dinonograma", "kostkirubika": "kostkirubika", "crypticcrosswords": "mantsoeapolaenongtsepakang", "syberia2": "syberia2", "puzzlehunt": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "bok<PERSON><PERSON><PERSON><PERSON>akatse", "quebracabeça": "quebracabeça", "hlavolamy": "lipuzulu", "poptropica": "poptropica", "thelastcampfire": "moll<PERSON><PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "ikemetseng", "picopark": "picopark", "wandersong": "leetosefela", "carto": "carto", "untitledgoosegame": "papetlankedigoose", "cassetête": "qaqatso", "limbo": "limbo", "rubiks": "rubiks", "maze": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "liko<PERSON><PERSON>", "portalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bilmece": "mohlolo", "puzzelen": "ho_r<PERSON><PERSON><PERSON>", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "<PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "lefalalefehlileng", "monopoly": "monopoli", "futurefight": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "mphephutlholo", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bophelo_ba_bits", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "<PERSON><PERSON><PERSON><PERSON>", "stateofsurvival": "bop<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "<PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "likapalehlooeng", "honkaiimpact": "honkaiimpact", "soccerbattle": "ntoasebolo", "a3": "a3", "phonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guardiantales": "lipalelatebahale", "petrolhead": "mora<PERSON><PERSON><PERSON><PERSON>", "tacticool": "tacticool", "cookierun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "kentsengtlalekanyo", "craftsman": "m<PERSON><PERSON><PERSON>", "supersus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "hlokomelamollahako", "wordfeud": "wordfeud", "bedwars": "ntoamorao", "freefire": "mollo_o_loko<PERSON><PERSON>eng", "mobilegaming": "papal<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "se<PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "ntoatoloana", "pjsekai": "pjsekai", "mysticmessenger": "molaetsaboseleng", "callofdutymobile": "callofdutymobile", "thearcana": "moro<PERSON>", "8ballpool": "8ballpool", "emergencyhq": "ntlotlolo", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON><PERSON><PERSON><PERSON>dge<PERSON>", "ml": "ml", "bangdream": "to<PERSON><PERSON>yaphetoho", "clashofclan": "ntwamolefatshe", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "ngoanangoenyanya", "beatstar": "sefeletsamino", "dragonmanialegend": "na<PERSON><PERSON><PERSON><PERSON>", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "le<PERSON><PERSON><PERSON><PERSON>", "androidgames": "lipapalidit<PERSON><PERSON>ndroid", "criminalcase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cookingmadness": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "lipotsopakgehosetsa", "leagueofangels": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "se<PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "marun<PERSON><PERSON>", "mysingingmonsters": "liphoofetsatsalebino", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "lirobot<PERSON><PERSON><PERSON>", "mirrorverse": "le<PERSON>la", "pou": "pou", "warwings": "ntoapheho", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "palekgolo", "futime": "nakokamonate", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "ken<PERSON><PERSON><PERSON>", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "metsoalleeat<PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "papalieasultans", "arenabreakout": "t<PERSON><PERSON><PERSON><PERSON>", "wolfy": "phiri", "runcitygame": "papallammotoropong", "juegodemovil": "papaliselefouneng", "avakinlife": "bophelo_ba_avakin", "kogama": "kogama", "mimicry": "papiso", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "tlhahisanangbrazil", "ldoe": "ldoe", "legendonline": "mohlalefionline", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "koloi_x_drift_mabelo2", "pathtonowhere": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "ntwashadow3", "limbuscompany": "limbuscompany", "demolitionderby3": "ntoanatshupisano3", "wordswithfriends2": "mantsuoalemetsoalle2", "soulknight": "molokolephefumolong", "purrfecttale": "kganoekgatse", "showbyrock": "pontshakaletsoana", "ladypopular": "moroetsanaeatumelang", "lolmobile": "tsehomobile", "harvesttown": "mots<PERSON><PERSON><PERSON>", "perfectworldmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiresandpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empirespuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "toropongtlhaku", "garticphone": "garticphone", "battlegroundmobileind": "ntoanatselefonotseleketeng", "fanny": "marago", "littlenightmare": "toropelenyenyane", "aethergazer": "aethergazer", "mudrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "moe<PERSON><PERSON><PERSON><PERSON>a", "gunbound": "gunbound", "gamingmlbb": "papalimlbb", "dbdmobile": "dbdmobile", "arknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "babale<PERSON><PERSON><PERSON>", "eveechoes": "eveechoes", "jogocelular": "<PERSON><PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "mmaeapheha", "cabalmobile": "cabalmobile", "streetfighterduel": "ntwatšaseterateng", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsfrontline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jurassicworldalive": "jurassicworldalive", "soulseeker": "batlikopheho", "gettingoverit": "keatlohlatumelo", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "palekamoonchai", "carxdriftracingonline": "kolointsamisatsoelapeleng", "jogosmobile": "lipapaling_tsa_mohala", "legendofneverland": "tš<PERSON><PERSON>yabohlokoanafatše", "pubglite": "pubglite", "gamemobilelegends": "<PERSON><PERSON><PERSON><PERSON>", "timeraiders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "lidikatsetlhoane", "dnd": "lss", "quest": "moqoqo", "giochidiruolo": "papalieatšehelano", "dnd5e": "dnd5e", "rpgdemesa": "papalieatafole", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "papallittrtpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "lebakalamalerato", "d20": "d20", "pokemongames": "papaling_pokemongo", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "ntoat<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipipi", "porygon": "porygon", "pokemonunite": "pokemonekopane", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "bua", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpepuru", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "sehlophaserocket", "furret": "furret", "magikarp": "<PERSON><PERSON><PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "lipokemone", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "sehlophasamystiki", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "t<PERSON><PERSON><PERSON>", "shinypokemon": "pokemonekganyang", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "moetapelipokémon", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "mots<PERSON><PERSON><PERSON>a", "ajedrez": "chess", "catur": "<PERSON>ur", "xadrez": "chess", "scacchi": "chess", "schaken": "ho_bapala_chess", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "litoetseaches", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "<PERSON><PERSON><PERSON>", "japanesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chinesechess": "chesesechina", "chesscanada": "chessecanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "bulelo", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragon": "dungeonsledragon", "dungeonmaster": "mola<PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "ntlhaeadrakone", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "paletsoeavoxmachina", "doungenoanddragons": "ntlhamelwanalemeketse", "darkmoor": "le<PERSON><PERSON>", "minecraftchampionship": "papalieatselengea_minecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "dimodaminecraft", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON>", "fru": "fru", "addons": "lintlhapheto<PERSON>", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecrafteofetotsoe", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "lipakengtsalinaha", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "toropaeyaminecraft", "pcgamer": "pcgamer", "jeuxvideo": "lipapalilivideo", "gambit": "<PERSON><PERSON><PERSON><PERSON>", "gamers": "<PERSON><PERSON><PERSON><PERSON>", "levelup": "nyo<PERSON><PERSON>a", "gamermobile": "papaliselefouneng", "gameover": "<PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "papalieatsebepc", "gamen": "raloka", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "lipapalisaeakhomph<PERSON>", "casualgaming": "<PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmasterrace", "pcgame": "pcgame", "gamerboy": "mosh<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vrgaming": "<PERSON><PERSON><PERSON><PERSON>", "drdisrespect": "drdisrespect", "4kgaming": "papali4k", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON><PERSON>", "consoleplayer": "sebapalisesotho", "boxi": "boxi", "pro": "propro", "epicgamers": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "papading_inthaneteng", "semigamer": "g<PERSON><PERSON><PERSON>", "gamergirls": "liba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermoms": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerguy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamewatcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "se<PERSON><PERSON>i", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "libapaditsachicks", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "se<PERSON>op<PERSON>ebatlelang", "mallugaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pawgers": "<PERSON><PERSON><PERSON><PERSON>", "quests": "lipatlisiso", "alax": "alax", "avgn": "avgn", "oldgamer": "moballikhoale", "cozygaming": "papaliemonate", "gamelpay": "bapallopefa", "juegosdepc": "lipapalisatsaeakomporo", "dsswitch": "fetoladsea", "competitivegaming": "papaling_ya_tlhol<PERSON>o", "minecraftnewjersey": "minecraftnewjersey", "faker": "kgitla", "pc4gamers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingff": "papaling_ff", "yatoro": "yatoro", "heterosexualgaming": "papalisesothohodibokaneng", "gamepc": "papalip<PERSON>", "girlsgamer": "bananabasebapali", "fnfmods": "fnfmods", "dailyquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamegirl": "<PERSON><PERSON><PERSON>", "chicasgamer": "basa<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesetup": "papiso_ea_papali", "overpowered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "socialgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "papadi_ea_lipapali", "proplayer": "sebapal<PERSON>_sa_bohlale", "roleplayer": "seba<PERSON>i", "myteam": "sehlophasaka", "republicofgamers": "<PERSON><PERSON><PERSON><PERSON>", "aorus": "aorus", "cougargaming": "papalieatšehaliya", "triplelegend": "le<PERSON><PERSON><PERSON><PERSON>", "gamerbuddies": "metswalledibapadisane", "butuhcewekgamers": "keantsabananagamers", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "nerdgamer": "selefodipapad<PERSON>", "afk": "nkas<PERSON>", "andregamer": "andregamer", "casualgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "sehlopha89", "inicaramainnyagimana": "inizyinindaba", "insec": "tieho", "gemers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunizlemek": "oyunizlemek", "gamertag": "le<PERSON>lapapadi", "lanparty": "kopanolipapali", "videogamer": "videogamer", "wspólnegranie": "papadi_mmoho", "mortdog": "mortdog", "playstationgamer": "sebapalisesothopleise<PERSON>š<PERSON>", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "sengolimokemera", "protogen": "protogen", "womangamer": "bash<PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "kekgametlhaapapatahleemoleng", "mario": "mario", "papermario": "papermario", "mariogolf": "kolofolamario", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON>jaki", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "phol<PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "mminooanintendo", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "bapa<PERSON>bao<PERSON>", "switch": "fetola", "zelda": "zelda", "smashbros": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofzelda": "tshe<PERSON>bohalebangoasethak<PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "selikalikoe", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "moetapelewamarik<PERSON>khate", "wii": "wii", "aceattorney": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "maronaalehodimo", "tomodachilife": "bophelo_ba_metsoalle", "ahatintime": "nakoefetileng", "tearsofthekingdom": "meokoanaseborengseng", "walkingsimulators": "lipapalinahosephetsephetse", "nintendogames": "lipapalisanintendo", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "khoeliyakgwedi", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "seleste", "breathofthewild": "moyaoamofifi", "myfriendpedro": "mots<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "legendsofzelda", "donkeykong": "ton<PERSON>khonkh<PERSON>", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "lipapali51", "earthbound": "<PERSON><PERSON><PERSON><PERSON>", "tales": "lipale", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "morerotriangle", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "linintendo", "new3ds": "3dsentj<PERSON>", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "ntoanayahyrule", "mariopartysuperstars": "papalieasephirimaeanalinalinkopano", "marioandsonic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "dintjapelona", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "lihlabanytl<PERSON><PERSON><PERSON>", "urgot": "ugot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vanillalol": "vanillalol", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "ntwat<PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "papaliealikemphengwild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspain", "aatrox": "aatrox", "euw": "aeuw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "lenari", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaminglol": "papadi_lol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "lihelekete", "hextech": "hextech", "fortnitegame": "papalifortnite", "gamingfortnite": "papalifortnite", "fortnitebr": "fortnitebr", "retrovideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scaryvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "papadi_ya_video", "videosgame": "lipapalilivideo", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "ntoanaleba<PERSON><PERSON><PERSON>", "arcades": "liakeite", "acnh": "acnh", "puffpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxlesotho", "robloxdeutsch": "robloxsesotho", "erlc": "erlc", "sanboxgames": "lipapalisandbox", "videogamelore": "tsebovideogame", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "<PERSON><PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "ntšakolotoinona", "deadspace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amordoce": "leratolelemonate", "videogiochi": "lipapalilivideo", "theoldrepublic": "rephabolokhale", "videospiele": "lipapalilivideo", "touhouproject": "touhouproject", "dreamcast": "toropelehellong", "adventuregames": "lip<PERSON><PERSON>ab<PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "ketsolesebapala", "storyofseasons": "palekanakgakolo", "retrogames": "lipapali_tsa_kgale", "retroarcade": "<PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "khomphutha_ea_khale", "retrogaming": "<PERSON><PERSON><PERSON>", "vintagegaming": "papalieatsamaeakgale", "playdate": "papa<PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "tloloboloharong2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "<PERSON>i_ya_le<PERSON>imo", "zenlife": "bophelo_bo_k<PERSON><PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "thata", "mystgames": "lipapaliletsamystical", "blockchaingaming": "papaliea_blockchain", "medievil": "mekoaqale", "consolegaming": "papaling_konsole", "konsolen": "konsolen", "outrun": "fetisapele", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiant": "sekolo", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "lipapalikhale", "bethesda": "bethesda", "jackboxgames": "lipapalisajackbox", "interactivefiction": "palekanokopano", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "ma<PERSON><PERSON><PERSON><PERSON>", "visualnovel": "palekanyeotsebonoa", "visualnovels": "dipalesaokuteng", "rgg": "rgg", "shadowolf": "p<PERSON><PERSON><PERSON>e", "tcrghost": "tcrmoya", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "morenaetšanyanayamantsiboya", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "<PERSON><PERSON><PERSON><PERSON>", "thecrew2": "sehlopha2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "papallakgale", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "bohloko", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "phetohoyal<PERSON><PERSON>blower", "wiiu": "wiiu", "leveldesign": "moraoamapapali", "starrail": "starrail", "keyblade": "lehare_la_senotlolo", "aplaguetale": "bolo<PERSON><PERSON>lim<PERSON>", "fnafsometimes": "fnafsometimes", "novelasvisuales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "<PERSON><PERSON><PERSON>", "videojuejos": "papadi_tsa_video", "videogamedates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "leratolakalapompong", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "justcause3": "hobane3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "kgutlelo<PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "papali_ya_gangster", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "ntlomaniacmansion", "crashracing": "maqhubutseleketse", "3dplatformers": "lipapali3dtsasethopo", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>ne", "storygames": "lipapalisalikane", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "p<PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "papaliusoea", "offmortisghost": "tholo<PERSON><PERSON><PERSON>", "tinybunny": "konyanenyane", "retroarch": "retroarch", "powerup": "<PERSON>laakakgol<PERSON>", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "liketsahapatsetšoan<PERSON>šo", "quickflash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "papaliea_gacha", "retroarcades": "liarcadesakhale", "f123": "f123", "wasteland": "lehoatateng", "powerwashsim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coralisland": "<PERSON><PERSON><PERSON><PERSON>lekesakorali", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "kopanoyakolo", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "moe<PERSON><PERSON><PERSON><PERSON><PERSON>", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "tšepeekobileng", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "mokotelatshepe", "simulator": "simulator", "symulatory": "symulatory", "speedrunner": "<PERSON><PERSON><PERSON>", "epicx": "epicx", "superrobottaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "papadi_tsa_vidiyo", "gaiaonline": "gaiaonline", "korkuoyunu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "wonderlandinthaneteng", "skylander": "skylander", "boyfrienddungeon": "moratuoa<PERSON><PERSON><PERSON>", "toontownrewritten": "toontownengotsoehape", "simracing": "paporaising", "simrace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "hloya", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "<PERSON><PERSON><PERSON>_wa_ma<PERSON>la", "spaceflightsimulator": "papadi_ya_sefofane_sa_sebaka", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "seka<PERSON>mp<PERSON><PERSON>", "foodandvideogames": "li<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewolfamongus": "phoofool<PERSON><PERSON><PERSON>", "truckingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handygame": "<PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "lipaletsatsilelipapalitsavideo", "oldschoolvideogames": "<PERSON><PERSON><PERSON><PERSON>", "racingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "lipinasesotho", "famitsu": "famitsu", "gatesofolympus": "majak<PERSON>laoli<PERSON>", "monsterhunternow": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rebelstar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogaming": "papaling_ea_divideo_e_ikemetseng", "indiegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogames": "lipapalitstivideo", "indievideogame": "papadi_ea_komporo_e_ikemetseng", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "qhobokatšoepe", "unbeatable": "esalengke", "projectl": "<PERSON><PERSON><PERSON>", "futureclubgames": "lipapalisehopotseng", "mugman": "<PERSON><PERSON><PERSON>š<PERSON>", "insomniacgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "papalisupergiant", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "papalisaceleste", "aperturescience": "saenseyasekheo", "backlog": "moraorao", "gamebacklog": "papalitseokesaqetang", "gamingbacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "papadi_tsa_video", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "le<PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "ntjaesenyang", "beastlord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosretro": "lip<PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reservatoriodedopamin": "reservatorioyadorophamine", "staxel": "staxel", "videogameost": "lipinapapaling", "dragonsync": "kgokanyadikgwanyane", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "keratakofxv", "arcanum": "<PERSON><PERSON><PERSON><PERSON>", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "h<PERSON>ya", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "<PERSON><PERSON><PERSON><PERSON>", "darkerthanblack": "ntšohofetaletsoto", "animescaling": "sekalaanime", "animewithplot": "animekaleqhubu", "pesci": "pesci", "retroanime": "animeyakgale", "animes": "lian<PERSON>", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80s", "90sanime": "anime90s", "darklord": "morenae<PERSON><PERSON>beho", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "mohlankanawa<PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000s", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesehlatsa1", "rapanime": "rapanime", "chargemanken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecover": "khav<PERSON><PERSON><PERSON>", "thevisionofescaflowne": "ponomolonoeatseaescaflowne", "slayers": "di<PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "basali_ba_banyenyane", "bananafish": "hlapi_ea_banana", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "hanakokuneathoalengengt<PERSON>", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "lefeela", "fireforce": "mollo_o_matla", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "palet<PERSON>an<PERSON><PERSON><PERSON>", "fairytail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "leetselengbojula", "parasyte": "parasaete", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "<PERSON><PERSON><PERSON>", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "molinometsing", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "<PERSON><PERSON><PERSON><PERSON>", "horrormanga": "mangaeatshabehang", "romancemangas": "lipalepalitseratotsemanga", "karneval": "karnivale", "dragonmaid": "morwetsanadragon", "blacklagoon": "lechibanene", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "libokoanetsatsoepheleto", "geniusinc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shamanking": "moro<PERSON><PERSON>", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurihokeng", "acertainmagicalindex": "indexyamoh<PERSON>lo", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "b<PERSON><PERSON><PERSON>bal<PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "sethotsanamamonate", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animeeaseporo", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "lint<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isekaianime": "animetseisekai", "sagaoftanyatheevil": "palesa<PERSON><PERSON><PERSON>bemo<PERSON>", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "moshana<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "moqhotsoaleboya", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "thalaphomolimo", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "mokhoanakeobolokantate", "fullmoonwosagashite": "kgwediyakgoloyabatlafumaneng", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "bonolo_le_botshabehang", "martialpeak": "ntlhantlhapeak", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "morwalin<PERSON>ber", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "lefela", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vejeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "lekholoale", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "moahlolipluto", "aloy": "aloy", "runa": "matha", "oldanime": "animeyakgale", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "papali", "franziska": "franziska", "nekomimi": "lika<PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "thipa", "loli": "loli", "horroranime": "animeeatšhabisang", "fruitsbasket": "sebaketesalitholoana", "devilmancrybaby": "deabolomangoananakang", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "keengmoeti", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "naha_eo_ba_neng_ba_re_tla_e_fumana", "monstermanga": "<PERSON><PERSON><PERSON><PERSON>", "yourlieinapril": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buggytheclown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "moloi", "deepseaprisoner": "mogolegwaebaleoukeng", "jojolion": "jojo<PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "<PERSON><PERSON><PERSON><PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON>", "husbu": "monna", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "dipelotsapandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "ntoatsale", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "molaetsa_oa_diabolo", "toyoureternity": "ho<PERSON><PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "nako_ya_bohlasoa", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "phumulu<PERSON>", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampireknight", "mugi": "mugi", "blueexorcist": "mof<PERSON><PERSON><PERSON><PERSON><PERSON>", "slamdunk": "papali_e_pholileng", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "bonneng", "spyfamily": "lelapaspia<PERSON>", "airgear": "airgear", "magicalgirl": "ng<PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "ma<PERSON><PERSON><PERSON>_as<PERSON><PERSON>ang", "prisonschool": "sekolochangana", "thegodofhighschool": "molimoosekolengephahamengtse", "kissxsis": "k<PERSON><PERSON><PERSON>a", "grandblue": "grandblue", "mydressupdarling": "lengata<PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoekhutsufaditsoeng", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "lefu_le_hlo<PERSON>hile", "romancemanga": "lithokotsalerato", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "leratoap<PERSON><PERSON>", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animearjentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "mobolaisethalin<PERSON><PERSON>", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "mollo_wa_fist", "adioseri": "salalehantle", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "linalangatseakopana", "romanceanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "ntlokinokuni", "recordragnarok": "hatisaletsetso", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "<PERSON><PERSON><PERSON><PERSON>", "highschoolofthedead": "<PERSON><PERSON><PERSON>kh<PERSON><PERSON>fu", "germantechno": "technosajeremane", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "morenaetenese", "tonikawa": "hantle", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "sekololesababolai", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animesamajapane", "animespace": "sebopeosaanimation", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "animeyaboitsekang", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "<PERSON><PERSON><PERSON><PERSON>", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gashbell": "gashbell", "peachgirl": "ngoananababelegae", "cavalieridellozodiaco": "bakempitsaeketane", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "monate_kotoneleng", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "tlalehamonyaneblase", "funamusea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "bochimajwe", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "tlolaroofo", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "hlo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "moetapelothebe", "kkondae": "kgohlopele", "chobits": "chobits", "witchhatatelier": "moro<PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangakebophelo", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "mora<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "morutsweomohlehavese", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "sesole", "mybossdaddy": "ntata<PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "toropakgolommabluu", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "madica<PERSON><PERSON>", "bloodc": "madic", "talesofdemonsandgods": "lipalelimaholimolemolinoisele", "goreanime": "goreanime", "animegirls": "litokots<PERSON><PERSON>manga", "sharingan": "<PERSON><PERSON>", "crowsxworst": "mak<PERSON><PERSON><PERSON><PERSON>", "splatteranime": "<PERSON>yama<PERSON><PERSON>", "splatter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "risingoftheshieldhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "senyehilesetšerwe", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "banabamaora", "liarliar": "<PERSON><PERSON><PERSON>", "supercampeones": "bahl<PERSON><PERSON><PERSON><PERSON><PERSON>", "animeidols": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaikelefounang", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "pitsoeanakongaboroko", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "princessjellyfish": "ngoanaprincessejellyfish", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekiss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persocoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omniscientreadersview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animerecommendations": "likeletsotsaan<PERSON>", "openinganime": "bulaopenaming<PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "papalieatsamalemongyanaka", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "ligundams", "voltesv": "voltesv", "giantrobots": "lirobotetseholokgolo", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "se<PERSON>pa", "deathnote": "buka<PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "<PERSON><PERSON><PERSON>tsamakatsang", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "anime_ya_sesole", "greenranger": "lengerekgahlama", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "<PERSON><PERSON><PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "h<PERSON><PERSON><PERSON><PERSON>", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onepieceanime": "onepieceanime", "attaquedestitans": "tlamolatitane", "theonepieceisreal": "theonepiecekeenetefela", "revengers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimonstory": "paletsokoadijimone", "digimontamers": "digimontamers", "superjail": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "tshenyo_ea_lit<PERSON><PERSON>e", "shinchan": "shinchan", "watamote": "keh<PERSON>tsaeng", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "sehlophasarona<PERSON>", "flawlesswebtoon": "webtoonephetse", "kemonofriends": "diphoofanatsala", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "bophelo_ba_letsatsi_le_letsatsi", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "moloits<PERSON>ang", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "seratalasebaboloi", "recuentosdelavida": "lipale<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}