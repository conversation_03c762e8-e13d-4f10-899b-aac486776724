{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "nyenyezi", "cognitivefunctions": "ntchitozamubongo", "psychology": "say<PERSON><PERSON>_yamaganizo", "philosophy": "filos<PERSON><PERSON>", "history": "mbiri", "physics": "<PERSON><PERSON><PERSON>i", "science": "<PERSON><PERSON><PERSON>", "culture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "languages": "zilankhulo", "technology": "teknoloji", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enneagrammemes": "enneagrammemes", "showerthoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "zoseket<PERSON>", "videos": "<PERSON><PERSON><PERSON>", "gadgets": "zidagadget", "politics": "ndale", "relationshipadvice": "uphun<PERSON><PERSON>_chib<PERSON><PERSON>", "lifeadvice": "moyo_malangizo", "crypto": "crypto", "news": "nkhani", "worldnews": "nkhanizapad<PERSON>ko", "archaeology": "zint<PERSON>_zakale", "learning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "debates": "zokambirana", "conspiracytheories": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ez<PERSON>", "universe": "chilengedwe", "meditation": "kukhazikika", "mythology": "nthano", "art": "chojamb<PERSON>", "crafts": "ntchito_zamanja", "dance": "vina", "design": "mapangidwe", "makeup": "zodzola", "beauty": "kukongola", "fashion": "<PERSON><PERSON><PERSON><PERSON>", "singing": "kuimba", "writing": "kulemba", "photography": "kujambula", "cosplay": "cosplay", "painting": "kujambula", "drawing": "kujambula", "books": "buku", "movies": "<PERSON><PERSON><PERSON>", "poetry": "n<PERSON><PERSON><PERSON>", "television": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filmmaking": "kup<PERSON><PERSON><PERSON><PERSON><PERSON>", "animation": "animation", "anime": "anime", "scifi": "say<PERSON><PERSON>_ya_nthano", "fantasy": "pangano", "documentaries": "madokumentale", "mystery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "comedy": "nthabwala", "crime": "umbanda", "drama": "<PERSON><PERSON><PERSON>", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "mantha", "romance": "chikondi", "realitytv": "tvy<PERSON><PERSON><PERSON>", "action": "zochitika", "music": "nyimbo", "blues": "chisoni", "classical": "achikale", "country": "<PERSON><PERSON><PERSON>", "desi": "desi", "edm": "edm", "electronic": "<PERSON><PERSON><PERSON><PERSON>", "folk": "anthu", "funk": "funk", "hiphop": "hiphop", "house": "nyumba", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "panka", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "ulendo", "concerts": "ma<PERSON><PERSON><PERSON>", "festivals": "zikondwerero", "museums": "nyumbazakale", "standup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theater": "<PERSON><PERSON><PERSON><PERSON>", "outdoors": "kunja", "gardening": "kulima", "partying": "<PERSON><PERSON><PERSON>", "gaming": "masewera", "boardgames": "masewera_a<PERSON><PERSON>di", "dungeonsanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chess": "chess", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "chakudya", "baking": "k<PERSON><PERSON><PERSON>", "cooking": "k<PERSON><PERSON><PERSON>", "vegetarian": "zamasamba", "vegan": "mbewu<PERSON><PERSON><PERSON><PERSON>", "birds": "mbalame", "cats": "mphaka", "dogs": "mbwa", "fish": "nsomba", "animals": "n<PERSON>", "blacklivesmatter": "moy<PERSON><PERSON><PERSON><PERSON>", "environmentalism": "chilengedwe", "feminism": "ufeministi", "humanrights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "wakuchirikizalgbtq", "stopasianhate": "lekanizosakondaanthuakuasia", "transally": "mzake_wa_transgender", "volunteering": "kudzipereka", "sports": "masewera", "badminton": "badminton", "baseball": "mpira_wa_baseball", "basketball": "basketball", "boxing": "nkhonya", "cricket": "kriketi", "cycling": "kup<PERSON><PERSON>", "fitness": "thupi_labwino", "football": "mpira", "golf": "gofu", "gym": "gym", "gymnastics": "masewer<PERSON>_<PERSON><PERSON>ha", "hockey": "hoki", "martialarts": "nkhondo", "netball": "mpira_wamanja", "pilates": "pilates", "pingpong": "pingpong", "running": "kuthamanga", "skateboarding": "skateboarding", "skiing": "kukwera_ski", "snowboarding": "snowboarding", "surfing": "kusefa", "swimming": "k<PERSON>mb<PERSON>", "tennis": "tennis", "volleyball": "volleyball", "weightlifting": "kun<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "yoga", "scubadiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiking": "kuyenda_mphiri", "capricorn": "capricorn", "aquarius": "aquarius", "pisces": "pisces", "aries": "aries", "taurus": "ngombe", "gemini": "gemini", "cancer": "<PERSON>han<PERSON>", "leo": "leo", "virgo": "virgo", "libra": "libra", "scorpio": "sikopiye", "sagittarius": "sagittarius", "shortterm": "k<PERSON><PERSON><PERSON><PERSON>", "casual": "wamba", "longtermrelationship": "ubanjawakal<PERSON><PERSON>", "single": "single", "polyamory": "chikondi_chambiri", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lesbian": "azimayi_okonda_azimayi", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "agal<PERSON>o", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "osakwiyafyamzimu", "suikoden": "su<PERSON><PERSON>", "subverse": "zotsutsa", "legendofspyro": "nthanoyadambozakumwamba", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openworld": "dzi<PERSON><PERSON>lotseguka", "heroesofthestorm": "ng<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "ngatimoyo", "dungeoncrawling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "mafukoakumidgard", "planescape": "ndegemtunda", "lordsoftherealm2": "ambuye_achigawo2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "medabots": "malo<PERSON>i_amankhondo", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "seweramamasewera", "witcher": "witcher", "dishonored": "kunyozedwa", "eldenring": "elden<PERSON>", "darksouls": "masewera_ovuta", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "kugwa", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "kukonza", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "chomangira", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyakale", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "zolimbikitsazaimaliseche", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "wopusapachikondi", "otomegames": "masewera_a<PERSON>kondi", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "gawo20", "gaslands": "<PERSON><PERSON>_amafuta", "pathfinder": "njiran<PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "mwazipawochiwotsero", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "ch<PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "kamodzi<PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "wa<PERSON><PERSON><PERSON>", "yourturntodie": "nthawiyako", "persona3": "persona3", "rpghorror": "rpgmantha", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "achifwamba", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "malo<PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "mdimandiwawa", "eclipsephase": "mphindoyachithunzichakudzuwa", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "kulumikizakwai<PERSON>", "diabloimmortal": "diabloimmortal", "dynastywarriors": "nkhondozaufumu", "skullgirls": "skullgirls", "nightcity": "mzindawausiku", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "nkhondozamisala", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "masewer<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "gothamknights": "gothamknights", "forgottenrealms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonlance": "dragonlance", "arenaofvalor": "banjampikisano", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "fumula<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "menya", "lastepoch": "matembelezero", "starfinder": "nyenyeziopeza", "goldensun": "d<PERSON>walagolide", "divinityoriginalsin": "mulu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesinthedark": "mipenimumdima", "twilight2000": "mdima2000", "sandevistan": "sandevistan", "cyberpunk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "malamulowagwa", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "mdyomwampiriwo", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "umulungu", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "zisonidzadziko_lakale", "adventurequest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "kupangamaseweraachi<PERSON>i", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "dzu<PERSON>_labwino", "talesofsymphonia": "nkhanizasymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "myfarog", "sacredunderworld": "ma<PERSON><PERSON><PERSON>woyer<PERSON>", "chainedechoes": "ma<PERSON><PERSON>a_olumikizana", "darksoul": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "ngatimoyo", "othercide": "<PERSON><PERSON><PERSON>", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "nyengoitsikiritse", "pillarsofeternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "<PERSON><PERSON><PERSON>", "tibia": "tibia", "thedivision": "gawodivision", "hellocharlotte": "mulibuanjicha<PERSON>otte", "legendofdragoon": "nganoyadragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampayalachemazimba", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werewolftheapocalypse", "aveyond": "aveyond", "littlewood": "kamtengo", "childrenofmorta": "an<PERSON><PERSON><PERSON>", "engineheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable3": "nthano3", "fablethelostchapter": "n<PERSON><PERSON><PERSON>_yo<PERSON><PERSON>ka", "hiveswap": "hiveswap", "rollenspiel": "sewero", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "ed<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "starfield", "oldschoolrevival": "kubweretsazakale", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "mdi<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "maseweraakusewera", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "kukolola", "gloomhaven": "m<PERSON><PERSON><PERSON>", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "linga", "drakarochdemoner": "<PERSON><PERSON><PERSON>ka<PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "miyambayakumwamba", "shadowhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "mwaziwamitengo", "breathoffire4": "mpweyawamoto4", "mother3": "mayi3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "eden<PERSON><PERSON><PERSON>", "roleplaygames": "maseweraakusewera", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "mtimawamfiti", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirilamasquerade", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "nthawi<PERSON><PERSON>i", "cocttrpg": "cocttrpg", "huntroyale": "sakafuna", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "awplanet": "planitiyowawa", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "zitfezindiwindeyi2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "malondaakuluakulu", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "mphambuzachilengedwe", "blackbook": "b<PERSON><PERSON><PERSON>", "skychildrenoflight": "anawakumwa<PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "edishoniyagolidewopatulika", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "maseweraachigothic", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gameningrpg", "prophunt": "kusaka", "starrails": "njanjiazanyenyezi", "cityofmist": "mzindawachinjenje", "indierpg": "rpmyod<PERSON>yi<PERSON>la", "pointandclick": "dina_ndi_kudina", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "em<PERSON><PERSON><PERSON>", "indivisible": "sangathe", "freeside": "kunja", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "pambuyo_pa_cyberpunk", "deathroadtocanada": "njiraimfayakucanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "kasakazilidza", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "k<PERSON><PERSON><PERSON>rakomwe<PERSON>", "persona5": "persona5", "ghostoftsushima": "mziwawatsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "kusaka_zilombo", "nier": "nier", "dothack": "chita<PERSON>ta", "ys": "ys", "souleater": "mphwevundyandamzimu", "fatestaynight": "tsikuusikudziwike", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "rpgy<PERSON><PERSON><PERSON><PERSON>", "mahoyo": "mahoyo", "animegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "nyi<PERSON>osa<PERSON>", "princessconnect": "princessconnect", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "wanzeru_mmatumba", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindian", "dota": "dota", "madden": "kukwiya", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "maseweraapakompyuta", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "gululapazoloto", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamhack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaimin": "<PERSON><PERSON><PERSON><PERSON>", "overwatchleague": "overwatchleague", "cybersport": "mase<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "crazyraccoon": "<PERSON>yamasok<PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "kuchotsa", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantcompetitive", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "thekatenga", "left4dead": "tidatsala4akufa", "left4dead2": "mwatsala4akufa2", "valve": "valavu", "portal": "<PERSON><PERSON><PERSON>", "teamfortress2": "teamfortress2", "everlastingsummer": "chiri<PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "maseweraambuzi", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "<PERSON><PERSON><PERSON><PERSON>lidaulere", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "battlefield4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "ngozindimenyani", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "mvulayadetsa2", "metroidvanias": "metroidvanias", "overcooked": "kuphika_mwambili", "interplanetary": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "helltaker": "wotenga_kugehena", "inscryption": "inscryption", "7d2d": "7m2m", "deadcells": "maseloyakufa", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "ngomezazikhungu", "foxhole": "dzenje_la_nkhandwe", "stray": "o<PERSON><PERSON><PERSON>a", "battlefield": "nkhondo", "battlefield1": "nkhondoyankhani1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "ngalawa", "eyeb": "eyeb", "blackdesert": "<PERSON><PERSON><PERSON>", "tabletopsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partyhard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "kuth<PERSON>la<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "okangidwandije<PERSON>", "dinkum": "zoona", "predecessor": "choyamba", "rainworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "fanizoyendetsankhalango", "noita": "noita", "dawnofwar": "nthawiyankhondo", "minionmasters": "minionmasters", "grimdawn": "mdimanyo<PERSON><PERSON>", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "wog<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "kuthawacube", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON>zindawatsopano", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "chibwezimkulu", "kenopsia": "kenopsia", "virtualkenopsia": "kenopsiaviritual", "snowrunner": "katunduwasnow", "libraryofruina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "timangemulatiwamzimu", "placidplasticduck": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlebit": "nkhondoyabit", "ultimatechickenhorse": "nkhukunsewerapamwamba", "dialtown": "townyadialilo", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "usikuwamphaka", "supermeatboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinnybunny": "kamwana_wawungono", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "tsoka", "callofduty": "callofduty", "callofdutyww2": "nkhondoyachiwi<PERSON><PERSON><PERSON>", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "malire", "pubg": "pubg", "callofdutyzombies": "callofdutyzomb<PERSON><PERSON>", "apex": "pamwamba", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paladins": "<PERSON><PERSON><PERSON>", "earthdefenseforce": "gululank<PERSON><PERSON>i", "huntshowdown": "kusakamasewera", "ghostrecon": "msilikaliwazimu", "grandtheftauto5": "grandtheftauto5", "warz": "war<PERSON>", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "joinsquad": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "nkhondoyamchenga", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "nkhond<PERSON><PERSON><PERSON><PERSON>", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "mirrorsedge", "divisions2": "zakugawika2", "killzone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "nkhondoyachizuluyankhanza", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "ndegerazankhondo", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "sniperelitechichewa", "modernwarfare": "nkhondoyamas<PERSON><PERSON>_ano", "neonabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "malire", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "k<PERSON>hanakokalamba", "worldofwarships": "worldofwarships", "back4blood": "ndabwerera<PERSON><PERSON><PERSON>", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "waku<PERSON>", "masseffect": "masseffect", "systemshock": "mantha_asistemu", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "doometernal": "tsikuosathalayense", "centuryageofashes": "zakazakamakamaka", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "magawanidwa2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "chizunguzero", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "nkhondoyamakono2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "ululu_womveka", "warface": "nkhond<PERSON><PERSON><PERSON><PERSON>", "crossfire": "moto_wambiri", "atomicheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "nkhuku", "freedoom": "ufulu", "battlegrounds": "mabwal<PERSON>_ankh<PERSON>o", "frag": "<PERSON><PERSON><PERSON><PERSON>", "tinytina": "titititina", "gamepubg": "gameya_pubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "maseweraachifwambwefps", "convertstrike": "kusinthastrike", "warzone2": "warzone2", "shatterline": "linenyenyeka", "blackopszombies": "blackopszombies", "bloodymess": "ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "republiccommando", "elitedangerous": "osewerapamwamba", "soldat": "soldat", "groundbranch": "ntham<PERSON>yan<PERSON><PERSON><PERSON>", "squad": "gulu", "destiny1": "ulemu1", "gamingfps": "maseweraafps", "redfall": "redfall", "pubggirl": "mtsikanawapubg", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "ntchitoyankhondoyakuda", "enlisted": "kulembetsa", "farlight": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "chitetezochangalimba", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "payday2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "nsombayankhungu", "ghostcod": "mzukwacod", "csplay": "sewero", "unrealtournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamecodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "chikwa<PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "<PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonwoyera", "remnant": "otsala", "azurelane": "azurelane", "worldofwar": "nkhondozapadzikalonse", "gunvolt": "gunvolt", "returnal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "mphepo2", "microvolts": "mavoliti_angonoangono", "reddead": "wakufa", "standoff2": "chigamanyano2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "nyanjayachifwamba", "rust": "dzimbiri", "conqueronline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>galeng<PERSON>", "dauntless": "wa<PERSON><PERSON><PERSON>", "warships": "zombomanga", "dayofdragons": "tsikudragons", "warthunder": "warthunder", "flightrising": "ndegezaulendo", "recroom": "chipindachosangalalira", "legendsofruneterra": "nthanoandel<PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "phantasystaronline2", "maidenless": "opandachikondi", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON>lit<PERSON><PERSON>", "crossout": "chotsani", "agario": "agario", "secondlife": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "seweranikompyuta", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "chitsiru", "newworld": "dzikopatsopano", "blackdesertonline": "blackdesertonline", "multiplayer": "oseweraambiri", "pirate101": "pirate101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "nkhondozanyenyezi", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "nkhondoyanyenyezimkati2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ta<PERSON>lamahatchi", "3dchat": "3dchat", "nostale": "nostal<PERSON><PERSON><PERSON>", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramopaintaneti", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "spiralknights", "mulegend": "ndiwamphamvu", "startrekonline": "startrekpaintaneti", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "mneneriwazidzinga", "grymmo": "grymmo", "warmane": "wotentha", "multijugador": "oseweraambiri", "angelsonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "kukula", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "blueprotocol", "perfectworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseonline": "dzukampaintaneti", "corepunk": "corepunk", "adventurequestworlds": "maseweraauchiwembirimbiri", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "nyama<PERSON>ons<PERSON>", "kingdomofloathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "mzindawangwazi", "mortalkombat": "nkhondoyaimfa", "streetfighter": "n<PERSON>wan<PERSON><PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "nkh<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mgwirizanowakupha", "nomoreheroes": "palibengambanatena", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "ngatidragon", "retrofightinggames": "maseweatsekeraokalekale", "blasphemous": "mwano", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "kuphulitsamphamvu", "mugen": "mugen", "warofthemonsters": "nkhondoyazilombo", "jogosdeluta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "malo<PERSON><PERSON>_a<PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "nkhondoyomaliza", "poweredgear": "zidazogwiritsid<PERSON><PERSON><PERSON>", "beatemup": "<PERSON>ganaw<PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "otenga_pamwamba", "chivalry2": "chivalry2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "chidindochodikirahollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "maseweraasilksonge", "silksongnews": "nkhanizasilksong", "silksong": "silksong", "undernight": "usiku", "typelumina": "lembakuwala", "evolutiontournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evomoment": "nthawiyaevona", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "nthanozaberseria", "bloodborne": "matendaop<PERSON><PERSON>", "horizon": "mphepete", "pathofexile": "pathofexile", "slimerancher": "woweta_ma_slime", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodbourne", "uncharted": "osadziwikabwino", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infamous": "zodziwika", "playstationbuddies": "anzaathuaplaystation", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "kampaniyachinyengo", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "mulunguankhondo", "gris": "gris", "trove": "katundu", "detroitbecomehuman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "mpaka<PERSON>", "touristtrophy": "mphototourist", "lspdfr": "lspdfr", "shadowofthecolossus": "mthunziwa<PERSON>mphon<PERSON>", "crashteamracing": "mpikisanowagalimotowothamanga", "fivepd": "sikono", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "ndikusewera", "samuraiwarriors": "asamurai", "psvr2": "psvr2", "thelastguardian": "wotetezayo", "soulblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "kusakafuna", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "mtimawamdima2pangano", "pcsx2": "pcsx2", "lastguardian": "wotetezammaliza", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "nkhondousikuwakatswi<PERSON>", "psychonauts": "am<PERSON><PERSON>_a<PERSON><PERSON>", "mhw": "mhw", "princeofpersia": "<PERSON>longawaajem<PERSON>", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "chinthuchilichonsechikusintha", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "nkhondokunthanda", "dontstarvetogether": "tisama<PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "yemwerezandinyenyezi", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "fable2": "nthano2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skycotl": "skycotl", "erica": "erica", "ancestory": "makolo", "cuphead": "cuphead", "littlemisfortune": "tsokal<PERSON><PERSON>", "sallyface": "nkhopezasally", "franbow": "franbow", "monsterprom": "pro<PERSON><PERSON><PERSON><PERSON>", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "ma<PERSON><PERSON><PERSON>", "outerwilds": "zakumwamba", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duckgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "thestanleyparable", "towerunite": "mgwirizanowanja", "occulto": "zobisika", "longdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "satisfactory": "<PERSON><PERSON><PERSON><PERSON>", "pluviophile": "wokondavula", "underearth": "<PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "mtsogolerimzimu", "darkdome": "m<PERSON><PERSON><PERSON>", "pizzatower": "pizzatower", "indiegame": "maseweraodzipangira", "itchio": "itchio", "golfit": "golfit", "truthordare": "choonadzikapenangokhala", "game": "masewera", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampoline", "hulahoop": "mphinjizozungulira", "dare": "<PERSON><PERSON><PERSON><PERSON>", "scavengerhunt": "k<PERSON><PERSON><PERSON>", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "<PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "zonadikapenazabodza", "beerpong": "beerpong", "dicegoblin": "dicegoblin", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "masewera", "mahjong": "mahjong", "jeux": "masewera", "simulationgames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "jeuxdemots", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "ti<PERSON><PERSON><PERSON><PERSON>", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "oyun", "interactivegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "mipikisanoyangʼanizana", "spiele": "masewera", "giochi": "giochi", "geoguessr": "geoguessr", "iphonegames": "masewera_aiphone", "boogames": "mase<PERSON><PERSON><PERSON>", "cranegame": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hideandseek": "kubisalanakundifuna", "hopscotch": "masewera_agwedede", "arcadegames": "maseweraarcade", "yakuzagames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "maseweraakale", "mindgames": "z<PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancegame": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "mitengoyadilankhulidwe", "4xgames": "masewera4x", "gamefi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdarcades": "maseweraakale", "tabletopgames": "masewerapatebulo", "metroidvania": "metroidvania", "games90": "gamesimakhumi9", "idareyou": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "maseweraothamanga", "ets2": "ets2", "realvsfake": "zenichazoona", "playgames": "sewerapamasewera", "gameonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegames": "masewerapa_intaneti", "jogosonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "sewerolamawu", "playaballgame": "sewerapampira", "pictionary": "pictionary", "coopgames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "masewerawiigames", "highscore": "chiwerengero_chachikulu", "jeuxderôles": "masewera_a<PERSON>ye<PERSON>", "burgergames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsgames": "ma<PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "sewero", "managementgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "maseweraaformula1", "citybuilder": "<PERSON><PERSON><PERSON><PERSON>", "drdriving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "maseweraarcade", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "gameazochita", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "makinapinball", "oldgames": "maseweraakale", "couchcoop": "couchcoop", "perguntados": "<PERSON><PERSON><PERSON>", "gameo": "maseweraaaa", "lasergame": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "maseweraimessage", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "lemberapob<PERSON><PERSON>", "jeuxpc": "jeuxpc", "rétrogaming": "masewer<PERSON>_akale", "logicgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "japangame": "<PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "bwezetsampwevu", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "masoweoletseka", "5vs5": "5vs5", "rolgame": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dashiegames": "maseweraaliwonse", "gameandkill": "seweranimpha", "traditionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "masewer<PERSON><PERSON>", "textbasedgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "m<PERSON><PERSON><PERSON><PERSON>", "tischfußball": "tischfußball", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "madati", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "játék": "masewera", "bordfodbold": "m<PERSON><PERSON>pakat<PERSON>", "jogosorte": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mage": "mage", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "randomizer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "masewerapa_kompyuta", "socialdeductiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominos": "<PERSON><PERSON><PERSON><PERSON>", "domino": "domino", "isometricgames": "masewer<PERSON>_osat<PERSON><PERSON>_mapu", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "zowonayofunsa", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "kusaka", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "wose<PERSON><PERSON><PERSON><PERSON>", "free2play": "kawonjezere", "fantasygame": "<PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "masewer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "gamesotomes", "halotvseriesandgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mushroomoasis": "bulungutiwabowa", "anythingwithanengine": "chili<PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "masewerepaliponse", "swordandsorcery": "lupangandomatsenga", "goodgamegiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "timasewerera", "lab8games": "masoweralab8", "labzerogames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>kerapauz<PERSON>", "grykomputerowe": "maseweraapakompyuta", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "kudzikondandimasewera", "gamemodding": "kukonzandimasewera", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "masewera_a_dice", "spelletjes": "masewero", "spacenerf": "spacenerf", "charades": "masewer<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "singleplayer": "osewera_yekha", "coopgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamed": "ndisewera", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "nyambazoneni", "kingdiscord": "mfumuyadiscord", "scrabble": "scrabble", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "bawo", "onitama": "onitama", "pandemiclegacy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "camelup": "ngamboyi", "monopolygame": "maseweramon<PERSON><PERSON>", "brettspiele": "brettspiele", "bordspellen": "masewera_a<PERSON><PERSON>di", "boardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planszowe": "masewera_a<PERSON><PERSON>di", "risiko": "chiopse<PERSON>", "permainanpapan": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombicide", "tabletop": "pagome", "baduk": "baduk", "bloodbowl": "mpira_wa<PERSON><PERSON><PERSON>", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "sewerapabodi", "connectfour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidatavolo": "masewerapatebulo", "farkle": "farkle", "carrom": "kalomu", "tablegames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "maseweraapaboard", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "kukumanakwawapacalo", "creationludique": "<PERSON><PERSON><PERSON><PERSON>ngapan<PERSON>", "tabletoproleplay": "sewerolapatebulo", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "manthangowopezeka", "switchboardgames": "ma<PERSON>wer<PERSON><PERSON><PERSON><PERSON>", "infinitythegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "imfayakufa", "yahtzee": "yahtzee", "chutesandladders": "ma<PERSON><PERSON>amakwerero", "társas": "<PERSON><PERSON>", "juegodemesa": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "planszów<PERSON>", "rednecklife": "moyo_wa<PERSON><PERSON><PERSON><PERSON>", "boardom": "kunyongonyeka", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "sewero", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "ma<PERSON>weraa<PERSON><PERSON><PERSON>", "twilightimperium": "twilightimperium", "horseopoly": "aka<PERSON><PERSON><PERSON>", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "masewéapatebulo", "shadowsofbrimstone": "mthunziwabrimstone", "kingoftokyo": "mfumuakutokyo", "warcaby": "warcaby", "táblajátékok": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "ngalawa", "tickettoride": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "starwarslegion", "gochess": "pitachess", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "warzone", "arksurvivalevolved": "arksurvivalevolved", "dayz": "masiku", "identityv": "identityv", "theisle": "chil<PERSON><PERSON>", "thelastofus": "omaliza", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "muitanitsawamkulu", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "paka<PERSON><PERSON>u", "eco": "eco", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "wop<PERSON><PERSON><PERSON><PERSON>", "daysgone": "masikuapita", "fobia": "fobia", "witchit": "witchit", "pathologic": "matenda", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7masiku", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "areka", "grounded": "ndilikundende", "stateofdecay2": "chiwirizochamasiku2", "vrising": "vrising", "madfather": "b<PERSON><PERSON><PERSON><PERSON>", "dontstarve": "m<PERSON><PERSON><PERSON>", "eternalreturn": "kubwererabwerera", "pathoftitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexen": "mfiti", "theevilwithin": "zoyipamkati", "realrac": "<PERSON><PERSON><PERSON><PERSON>", "thebackrooms": "zipindazakumbuyo", "backrooms": "zipindazakumbuyo", "empiressmp": "empiressmp", "blockstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thequarry": "thequarry", "tlou": "tlou", "dyinglight": "kufakomaliza", "thewalkingdeadgame": "maseweraaakufandambiwakufa", "wehappyfew": "<PERSON>eto<PERSON>ched<PERSON>", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagestory": "nkhaniakale", "arksurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "kup<PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastsofbermuda": "nyamazikuluzikulu_zabermuda", "frostpunk": "frostpunk", "darkwood": "nkhalangoakuda", "survivalhorror": "man<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "pambu<PERSON>_pama<PERSON><PERSON>a", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "nkhondoyangayi", "scpfoundation": "scpfoundation", "greenproject": "chilengedwe", "kuon": "kuon", "cryoffear": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "sitima", "rdo": "rdo", "greenhell": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil8": "residentevil8", "onironauta": "mlotowakudziwi<PERSON>itsa", "granny": "agogo", "littlenightmares2": "zoopsazoungʼonozausiku2", "signalis": "chiz<PERSON><PERSON><PERSON>", "amandatheadventurer": "amanda<PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "maseweraakukanema_rust", "outlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "chipembedzechodera", "undawn": "<PERSON><PERSON><PERSON>", "7day2die": "7masiku2kufa", "sunlesssea": "nyan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "<PERSON><PERSON><PERSON><PERSON>", "propnight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadisland2": "mzindawakufa2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "amunakhutitsedipilachilu", "deathverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "tsikuzoopsama<PERSON><PERSON>", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkerwamthunziwakuchernobyl", "lifeafter": "chikondi_pambuyo_pache", "ageofdarkness": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "wachisitima3", "aloneinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "ufumumwakale", "projectnimbusgame": "projectnimbusgame", "eternights": "<PERSON><PERSON><PERSON><PERSON>", "craftopia": "uluso", "theoutlasttrials": "may<PERSON><PERSON><PERSON>_osatha", "bunker": "mngando", "worlddomination": "kulamuliradzikalonse", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "chikondimkachiwarhammer40k", "wh40": "wh40", "warhammer40klove": "chifunirochawarhammer40k", "warhammer40klore": "mbiri_ya_warhammer40k", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "ndimakondakulipsira", "iloveassasinorum": "ndimakondaassasinorum", "templovenenum": "chikonditengatemporary", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "ntchitosakupha", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "zanda<PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "amatopaawiri", "wingspan": "map<PERSON>", "terraformingmars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofmightandmagic": "ngwazizamphamvundimatsenga", "btd6": "btd6", "supremecommander": "mkuluwamkulu", "ageofmythology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args": "args", "rime": "rime", "planetzoo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outpost2": "malo2", "banished": "tachotsedwa", "caesar3": "caesar3", "redalert": "wops<PERSON><PERSON>", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "lamu<PERSON>rankug<PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "nkhond<PERSON><PERSON><PERSON>", "strategygames": "mase<PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization4": "chitukuko4", "factorio": "factorio", "dungeondraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spore": "spore", "totalwar": "nkhondoyonse", "travian": "travian", "forts": "malinga", "goodcompany": "gulu_labwino", "civ": "civ", "homeworld": "d<PERSON><PERSON><PERSON>lapachabe", "heidentum": "<PERSON><PERSON><PERSON><PERSON>", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "<PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "ndondomeko_yapompopompo", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "ufumuwakoroniziŵiri", "eu4": "eu4", "vainglory": "kudzikuza", "ww40k": "ww40k", "godhood": "umulungu", "anno": "anno", "battletech": "nkhondozamakono", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "kalasiyadaveyamaseweroaalgebra", "plagueinc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theorycraft": "kuyerekezeraplan", "mesbg": "mesbg", "civilization3": "chitukuko3", "4inarow": "4motsogolo", "crusaderkings3": "crusaderkings3", "heroes3": "ngwazi3", "advancewars": "nkhondozapatsogolo", "ageofempires2": "ageofempires2", "disciples2": "ophunzira2", "plantsvszombies": "zomeravszombi", "giochidistrategia": "maseweraamaganizo", "stratejioyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "zakanyanenedwe", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "mitimayachitsulo4", "companyofheroes": "gulu_la_ngwazi", "battleforwesnoth": "ndewe<PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "phobies": "mantha", "phobiesgame": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "kunja", "turnbased": "patsogolo", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "matsenga", "starwarsempireatwar": "nkhondoyanyenyezikwamphamvu", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popfulmail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "mtsogoleripamipi<PERSON>ano", "dysonsphereprogram": "pulogalamuya<PERSON>sonspheya", "transporttycoon": "katswiriwamatengatedwe", "unrailed": "sinapangidwe", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "<PERSON><PERSON>_a<PERSON><PERSON><PERSON><PERSON>", "galaxylife": "moyowanyenyezi", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "tumenyangazipata", "battlecats": "nyan<PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "kuthaman<PERSON>_yaipanso", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "fun<PERSON>raliwi<PERSON>", "needforspeedcarbon": "k<PERSON><PERSON><PERSON>hamangam<PERSON>", "realracing3": "kuthawakwenifeni3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "kayikalosims4", "fnaf": "fnaf", "outlast": "khalankhale", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "al<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "nthanozongodziwikazopambali", "phasmophobia": "mantha_amizimu", "fivenightsatfreddys": "masikuasanupafreddys", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "malonjezaausik<PERSON>", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "kunyumba", "deadisland": "chil<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzero": "projectzero", "horory": "mantha", "jogosterror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "hellomnansi2", "gamingdbd": "maseweraamdbd", "thecatlady": "mkaziwaaphaka", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "g<PERSON><PERSON><PERSON>", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "solitaire", "poker": "pokala", "hearthstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>_am<PERSON><PERSON>i", "playingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "makhadi_amalonda", "pokemoncards": "makhadi_a_pokemon", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "maka<PERSON>_amase<PERSON>a", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "ma<PERSON><PERSON><PERSON>", "warcry": "mfuuuuu", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "kukana", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "makadi_ayu<PERSON>h", "yugiohtcg": "yugiohtcg", "yugiohduel": "nkhondoyugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohwam<PERSON>lu", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "carteado", "sueca": "sueca", "beloteonline": "belotepaintan<PERSON>", "karcianki": "makadi", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "nkhondozamzimu", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "žolíky": "ma_joker", "facecard": "nkhopeyabwino", "cardfight": "nde<PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "oman<PERSON><PERSON><PERSON>", "marvelchampions": "ngwazizachimanga", "magiccartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "nyamasosag<PERSON><PERSON><PERSON><PERSON>", "cyberse": "cyberse", "classicarcadegames": "maseweraakaleakale", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "chis<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "<PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "kuvinachapakati", "rhythmgamer": "<PERSON>se<PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>bomwamasikozo<PERSON>weza", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "nyimbazakumwamba", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "bweranimuonline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "masewer<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "dokotalamanyimbo", "cubing": "kuchita_cubing", "wordle": "wordle", "teniz": "tennis", "puzzlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spotit": "onatu", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "misozo_yaubongo", "rubikscube": "rubikscube", "crossword": "<PERSON><PERSON><PERSON><PERSON>", "motscroisés": "motscroisés", "krzyżówki": "ma<PERSON>_op<PERSON><PERSON><PERSON>a", "nonogram": "nonogram", "bookworm": "wowerengamabuku", "jigsawpuzzles": "majigsawpuzzle", "indovinello": "<PERSON><PERSON><PERSON>", "riddle": "chingalande", "riddles": "zoko<PERSON>", "rompecabezas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "mkati", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "<PERSON>aw<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minesweeper": "chisweeper", "puzzleanddragons": "maseweroamapuzzlenadragon", "crosswordpuzzles": "zob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "maseweraagadenscapes", "puzzlesport": "masewerapuzzle", "escaperoomgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "3dpuzzle", "homescapesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordsearch": "sa<PERSON><PERSON><PERSON><PERSON>", "enigmistica": "<PERSON><PERSON><PERSON>", "kulaworld": "dzikokulaworld", "myst": "myst", "riddletales": "nthano_zamiyambi", "fishdom": "fishdom", "theimpossiblequiz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "sewerolamatchin3", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON><PERSON>", "rubikcube": "rubikcube", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "njuganjuga", "qbert": "qbert", "riddleme": "ndimbuze", "tycoongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "cubosderubik", "cruciverba": "cruciver<PERSON>", "ciphers": "zolemba_zachi<PERSON>i", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "turnipboy", "adivinanzashot": "yankho_la_mwamsanga", "nobodies": "palibe", "guessing": "kuyerekezera", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON>", "puzzlehunts": "kusaka_mapuzulo", "catcrime": "mlandu_wa_amphaka", "quebracabeça": "quebracabeça", "hlavolamy": "<PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "motowomaliza", "autodefinidos": "odziyika", "picopark": "picopark", "wandersong": "maloos<PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "mut<PERSON><PERSON><PERSON><PERSON>", "limbo": "ch<PERSON><PERSON><PERSON><PERSON>", "rubiks": "rubiks", "maze": "chis<PERSON><PERSON><PERSON>", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "zidutswa", "portalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bilmece": "<PERSON><PERSON><PERSON>", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "dzi<PERSON><PERSON>losokongono", "monopoly": "monopoly", "futurefight": "nkhondoyamtsogolo", "mobilelegends": "maseweraafoninyumba", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "m<PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "nyenyez<PERSON><PERSON><PERSON><PERSON><PERSON>", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "nyenyeziwamatsenga", "stateofsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "gamulamitengitengi", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "kutsogolerapambene", "knightrun": "kuthamandzangauwongo", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "nkhondoyampira", "a3": "a3", "phonegames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "chisankh<PERSON><PERSON><PERSON><PERSON>", "guardiantales": "nthanozizotetezedwa", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "tacticool", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "sindikudziwa", "craftsman": "m<PERSON><PERSON>o", "supersus": "z<PERSON><PERSON><PERSON><PERSON><PERSON>", "slowdrive": "kuyendapangʼonopangʼono", "headsup": "chen<PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "nkhondozabedi", "freefire": "freefire", "mobilegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "munda<PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "nyamazazikulumphana", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "nkhondoyamakolo", "pjsekai": "pjsekai", "mysticmessenger": "u<PERSON><PERSON>_wa<PERSON><PERSON><PERSON>", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "mpirawamagalimoto8", "emergencyhq": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "kuthingathingandzunguza", "ml": "ml", "bangdream": "ndotazachisangalalo", "clashofclan": "nkhondoyamtundu", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatstar": "<PERSON><PERSON><PERSON>zon<PERSON>dira", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "chikondicha_mthumba", "androidgames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "criminalcase": "mlanduwachifwamba", "summonerswar": "nkhondozamilungu", "cookingmadness": "kuphikakwambiri", "dokkan": "dokkan", "aov": "aov", "triviacrack": "masomu", "leagueofangels": "ligayaangelo", "lordsmobile": "lordsmobile", "tinybirdgarden": "kamundawangonokambalame", "gachalife": "gachalife", "neuralcloud": "ubongolwamtambo", "mysingingmonsters": "nyimboangamalitsiro", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "malobotiankhondo", "mirrorverse": "dzi<PERSON>lomchenicheni", "pou": "pou", "warwings": "nkhondozamapiko", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "nthanoiliyonse", "futime": "nthawiyandinawe", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "kulowa", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "chilangocha<PERSON><PERSON>da", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "kutulukamubwalo", "wolfy": "mbweya", "runcitygame": "thamangacitygame", "juegodemovil": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "kunamizira", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandchase": "grandchase", "bombmebrasil": "ndibombeni_brazil", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "nikkiwonyezimira", "carxdriftracing2": "galimotoxdriftracing2", "pathtonowhere": "njiraimeneisiyitengapamalo", "sealm": "sealm", "shadowfight3": "ndewezomenya3", "limbuscompany": "kampaniyakugwira", "demolitionderby3": "mipikisanoyowonongamotokha3", "wordswithfriends2": "mawundianzathu2", "soulknight": "wotetezampweya", "purrfecttale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showbyrock": "sewera_rock", "ladypopular": "mkaziwodziŵika", "lolmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvesttown": "mudzi_wa_ulimi", "perfectworldmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiresandpuzzles": "empiresandpuzzles", "empirespuzzles": "masewera_aufumu", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "nkhondozapafoninyamu", "fanny": "matako", "littlenightmare": "ngoziyausikuwochepa", "aethergazer": "aethergazer", "mudrunner": "galimotokunkhulupirira", "tearsofthemis": "miso<PERSON><PERSON><PERSON><PERSON>", "eversoul": "moyo_wosatha", "gunbound": "gunbound", "gamingmlbb": "maseweraamlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "<PERSON><PERSON><PERSON><PERSON>", "eveechoes": "eveechoes", "jogocelular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "ndewezakumenyanamumsewu", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsfrontline": "atsikanakutsogolo", "jurassicworldalive": "jurassicworldamoyo", "soulseeker": "<PERSON>sakas<PERSON>ung<PERSON>", "gettingoverit": "ndikuiwala", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "galimotoxdriftracingpaintaneti", "jogosmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofneverland": "nthanoyaneverland", "pubglite": "pubglite", "gamemobilelegends": "masewer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeraiders": "nthawiyachifwamba", "gamingmobile": "masewerapaphone", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "nkhondozaamphaka", "dnd": "dnd", "quest": "<PERSON><PERSON><PERSON>", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "maulendomasewera", "2300ad": "2300ad", "larp": "seweramaseweraachikulire", "romanceclub": "chikondiclub", "d20": "d20", "pokemongames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonred", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "k<PERSON><PERSON><PERSON><PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "gulimistiri", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "manja_achi<PERSON>lo", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "an<PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "shinyhunter", "ajedrez": "chess", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "atsikanaamasewera", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "d<PERSON><PERSON>blitz", "jeudéchecs": "jeudéchecs", "japanesechess": "chesswachijapani", "chinesechess": "<PERSON><PERSON><PERSON><PERSON>", "chesscanada": "chesscanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "mipata", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "njovundizilombo", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "mkuruwamasewera", "tiamat": "tiamat", "donjonsetdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "nthanoikukuluyavoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "m<PERSON><PERSON><PERSON>", "minecraftchampionship": "mpikisanowachampionminecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "zowonjezera", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftwosintha", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "mudziwaminecraft", "pcgamer": "oseweramasewerapakompyuta", "jeuxvideo": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gambit": "gambit", "gamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "levelup": "kukwerapamwamba", "gamermobile": "gameramafoni", "gameover": "<PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "maseweraapakompyuta", "gamen": "masewera", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "maseweraapakompyuta", "casualgaming": "sewera_zosangalatsa", "gamingsetup": "chipangizowose<PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmasterrace", "pcgame": "maseweraapakompyuta", "gamerboy": "m<PERSON>mata<PERSON>masew<PERSON>", "vrgaming": "masewer<PERSON><PERSON><PERSON><PERSON>", "drdisrespect": "drdisrespect", "4kgaming": "masewera4k", "gamerbr": "gamebrny", "gameplays": "masewera", "consoleplayer": "ose<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "<PERSON><PERSON><PERSON><PERSON>", "epicgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "masewerapa_intaneti", "semigamer": "w<PERSON>werapango<PERSON>", "gamergirls": "atsekanaamasewera", "gamermoms": "amayi_amase<PERSON>a", "gamerguy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamewatcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "osewera_atsikana", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "g<PERSON><PERSON><PERSON><PERSON>", "mallugaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pawgers": "zigolo", "quests": "maulendo", "alax": "alax", "avgn": "avgn", "oldgamer": "wosewer<PERSON><PERSON><PERSON><PERSON>_wakale", "cozygaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamelpay": "gameipay", "juegosdepc": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsswitch": "k<PERSON>int<PERSON><PERSON>int<PERSON>", "competitivegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftnewjersey": "minecraftnewjersey", "faker": "<PERSON><PERSON><PERSON><PERSON>", "pc4gamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingff": "gamesingff", "yatoro": "yatoro", "heterosexualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "maseweraapakompyuta", "girlsgamer": "atsikanawoseweramasewera", "fnfmods": "fnfmods", "dailyquest": "t<PERSON><PERSON><PERSON>lons<PERSON>", "gamegirl": "mtsikanawaseweramas<PERSON>", "chicasgamer": "atsikanaamasewera", "gamesetup": "masewera_kukonzekera", "overpowered": "ndiwamphamvukupita", "socialgamer": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "gamemuvinifale", "proplayer": "<PERSON><PERSON><PERSON><PERSON>", "roleplayer": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "timuyanga", "republicofgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "mtsogolerikatatu", "gamerbuddies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "butuhcewekgamers": "komandimamaseweramasewera", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "gamerokuchenjera", "nerdgamer": "katswiriwamasewera", "afk": "sindilipacomputer", "andregamer": "andregamer", "casualgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "89squad", "inicaramainnyagimana": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "insec": "umbulimbuli", "gemers": "masewera", "oyunizlemek": "oyunizlemek", "gamertag": "dzon<PERSON>sewer<PERSON><PERSON><PERSON>", "lanparty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamer": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "woseweraplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "protogen": "protogen", "womangamer": "mtsikanawosewera", "obviouslyimagamer": "zachiwonekerandinemasewera", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "wofunafuna", "humanfallflat": "anthuamagwa", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nyimbonintendomusic", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "sinthani", "zelda": "zelda", "smashbros": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "chigobachachikulu", "mariokartmaster": "wodziwa_mariokart", "wii": "wii", "aceattorney": "ace_attorney", "ssbm": "ssbm", "skychildrenofthelight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "moyo_wanzathu", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogames": "maseweranintendo", "thelegendofzelda": "nthanoyadabwiyazelda", "dragonquest": "dragonquest", "harvestmoon": "mweziwokolola", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "mpweya<PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "mzangawangapedro", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "masewera51", "earthbound": "padzikopansi", "tales": "nthano", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "<PERSON><PERSON>ray<PERSON><PERSON><PERSON>", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioandsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "<PERSON><PERSON><PERSON><PERSON>", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vanillalol": "vanillakkkk", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "nkhond<PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "z<PERSON>ats<PERSON>ling<PERSON>", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsespaña", "aatrox": "aatrox", "euw": "ee", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolics", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligayaakale", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "maseweraafortnite", "gamingfortnite": "maseweraafortnite", "fortnitebr": "fortnitebr", "retrovideogames": "masewerachikale", "scaryvideogames": "ma<PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "hashtag_opanga_masewera_apakanema", "megamanzero": "megamanzero", "videogame": "maseweraapakompyuta", "videosgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "nkhondozamasewera", "arcades": "ma_arcade", "acnh": "acnh", "puffpals": "anzathuachuchu", "farmingsimulator": "kulimirama<PERSON>wer<PERSON><PERSON><PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxdeutschland", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "maseweraasanbox", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "maloto", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "ma<PERSON><PERSON><PERSON>a", "amordoce": "ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theoldrepublic": "dzikolomvekale", "videospiele": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touhouproject": "touhouproject", "dreamcast": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventuregames": "mase<PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "zochitachitikamasewera", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "masewachakale", "vintagecomputing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogaming": "maseweraakale", "vintagegaming": "maseweraakale", "playdate": "<PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "chilungamochiwiri2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "sewerolamlengalenga", "zenlife": "moy<PERSON><PERSON>a", "beatmaniaiidx": "beatmaniaiidx", "steep": "zokwerakwambiri", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "masewerablockchain", "medievil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "consolegaming": "ma<PERSON>wer<PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolen", "outrun": "thamangapitilira", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "funsondendeyaatsikana<PERSON>ú", "supergiant": "chimphamvuzakulu", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "maseweraakale", "bethesda": "bethesda", "jackboxgames": "maseweraajackbox", "interactivefiction": "<PERSON>than<PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantesamentes", "visualnovel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovels": "nthan<PERSON><PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tcrghost": "tcrghost", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sewero", "aestheticgames": "maseweraokongolaokongola", "novelavisual": "nthetowelemba", "thecrew2": "gulu2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "maseweraakale", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "godhand": "dzan<PERSON>lamulungu", "leafblowerrevolution": "ch<PERSON>mberamasewemasewera", "wiiu": "wiiu", "leveldesign": "mapangidweamagemu", "starrail": "starrail", "keyblade": "keyblade", "aplaguetale": "n<PERSON><PERSON><PERSON><PERSON>", "fnafsometimes": "fnafnt<PERSON>i_zina", "novelasvisuales": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videojuejos": "masewera_apa_wailesi", "videogamedates": "maseweraapakompyuta", "mycandylove": "chikondichang<PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "malembalemba", "justcause3": "justcause3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamerangaming", "dayofthetantacle": "tsikularachisokonezo", "maniacmansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashracing": "mpikisanowagalimotoozimenya", "3dplatformers": "masewera3daothamanga", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "maseweraakale", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "masewera_an<PERSON>ni", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "kuthaweramanjamasaundi", "beyondtwosouls": "kupitamoyo2", "gameuse": "gameuse", "offmortisghost": "offmortisghost", "tinybunny": "kalulukakalulu", "retroarch": "retroarch", "powerup": "mphamvuzowonjezera", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "m<PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "koseweramagacha", "retroarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON>", "powerwashsim": "<PERSON><PERSON><PERSON><PERSON>ama<PERSON><PERSON>", "coralisland": "chilambacha_coral", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON>zina", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "oyeserera", "symulatory": "symulatory", "speedrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "epicx": "zabwino", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaiaonline": "gaiaonline", "korkuoyunu": "sewerowazoopsa", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "chibwenzichangamanga", "toontownrewritten": "toonto<PERSON><PERSON><PERSON>tenyo", "simracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simrace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "chis<PERSON><PERSON><PERSON>", "heavenlybodies": "matupiokongola", "seum": "kukwiya", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "w<PERSON><PERSON><PERSON>mand<PERSON>", "spaceflightsimulator": "kayesedwekazandegekumlengalenga", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "menyamenya", "foodandvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewolfamongus": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "truckingsimulator": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "<PERSON><PERSON><PERSON><PERSON>lengaleng<PERSON>", "handygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "nthanozinamasewera", "oldschoolvideogames": "maseweraakaleakale", "racingsimulator": "masewer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "akalon<PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "nyimbomasewera", "famitsu": "famitsu", "gatesofolympus": "zipataazaolympus", "monsterhunternow": "kusokamonsternow", "rebelstar": "nyenyeziyaupanduko", "indievideogaming": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indiegaming": "maseweraang<PERSON>", "indievideogames": "ma<PERSON><PERSON><PERSON><PERSON>_o<PERSON><PERSON><PERSON><PERSON>_ma<PERSON><PERSON><PERSON>_akulu", "indievideogame": "maseweraapakompyuta", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermanosagona", "bufffortress": "mpandamwamphamvu", "unbeatable": "osagonjetseka", "projectl": "pulojekiti", "futureclubgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugman": "mugman", "insomniacgames": "osewer<PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "sewelaceleste", "aperturescience": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backlog": "zotsala", "gamebacklog": "maseweraosewerekedwa", "gamingbacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "masewer<PERSON>_akane<PERSON>", "achievementhunter": "chof<PERSON><PERSON><PERSON><PERSON>", "cityskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "galu_woipa", "beastlord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "staxel", "videogameost": "nyimbovideogame", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ndimakondakofxv", "arcanum": "<PERSON><PERSON><PERSON>", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "okwiya", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "oyambad", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animeyachisoni", "darkerthanblack": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "animescaling", "animewithplot": "animendikatanthauzo", "pesci": "pesci", "retroanime": "retroanime", "animes": "makan<PERSON>_a<PERSON>i", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "popewoyimbawoyimba", "masterpogi": "wa<PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000s", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseason1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "animecover", "thevisionofescaflowne": "masomphenyaamtunduwaescaflowne", "slayers": "<PERSON><PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "nsombazankhaka", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "<PERSON>na<PERSON><PERSON><PERSON>mat<PERSON><PERSON>t", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "motoamphamvu", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fairytail": "nthano", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "yapangidwakuphompho", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "nyimboyansambad<PERSON><PERSON>yi", "kamisamakiss": "mpswa<PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "mangayazo<PERSON><PERSON>", "romancemangas": "mabuku<PERSON><PERSON><PERSON>za<PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "katumikiwadragon", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "nzeru_za_kampani", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "chinthuchinachodab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON>", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "nyamazimphongo", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "animeyaisekai", "sagaoftanyatheevil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "mpindidzawakumpoto", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "momba_kusunga_mummy", "fullmoonwosagashite": "mweziwathuntaakufunafuna", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "zokongolakomansowoopsa", "martialpeak": "msilikaliwamasewera", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "mtsikanawamphamvu", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "mkaziwachirombo", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "anime_yakale", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "ineundiiwe", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "nkhondoyaikulu", "loli": "loli", "horroranime": "animeyamantha", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>o", "devilmancrybaby": "mwana<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "moyowachonde", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "dzikololonjekedwa", "monstermanga": "monstermanga", "yourlieinapril": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buggytheclown": "buggytheclown", "bokunohero": "boku<PERSON><PERSON>her<PERSON><PERSON><PERSON>", "seraphoftheend": "ngelowotsiriza", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "matsenga", "deepseaprisoner": "kaidi_wa<PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "nsombazoopsya", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "m<PERSON><PERSON><PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "nkhondozakudya", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "mzerew<PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "kwampakalikalinyinuyo", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "zachotsedwa", "bluelock": "bluelock", "goblinslayer": "wa<PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampireknight", "mugi": "mugi", "blueexorcist": "blueexorcist", "slamdunk": "chi<PERSON><PERSON><PERSON>", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "n<PERSON><PERSON><PERSON>", "spyfamily": "banjamwakazisusa", "airgear": "airgear", "magicalgirl": "mtsikanawozizwitso", "thesevendeadlysins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "ndendesukuluyo", "thegodofhighschool": "mulu<PERSON><PERSON><PERSON><PERSON><PERSON>", "kissxsis": "kuk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "kavalidwe_kanga", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "d<PERSON><PERSON><PERSON>lathu_la_anime", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoosafupikitsidwa", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "nsonizochikondi", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "chikondicha_anime", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animechinyanja", "lolicon": "lo<PERSON>on", "demonslayertothesword": "wophyampphy<PERSON>", "bloodlad": "bloodlad", "goodbyeeri": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "nkhofikabwino", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "nyenyezizikugwirizana", "romanceanime": "anyimatachikondi", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "matsenga_acheri", "housekinokuni": "nyumbakujapan", "recordragnarok": "lembaragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "sukuluyayakufa", "germantechno": "teknoyachijeremani", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "ka<PERSON><PERSON><PERSON><PERSON>", "tonikawa": "nyamangeundendene", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "assassinclassroom", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animeyakujapan", "animespace": "maloanime", "girlsundpanzer": "atsikanandimfuti", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>dit<PERSON>di", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "khoswe", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "mnyamatawachikondi", "gashbell": "gashbell", "peachgirl": "mtsikanawapichi", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "mti<PERSON><PERSON>man<PERSON>", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "nkhondoyakumapeto", "funamusea": "zoseket<PERSON>", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "zachuluka", "toriko": "<PERSON><PERSON>o", "ravemaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "mph<PERSON><PERSON><PERSON>indamatsenga", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "akavaliyeakuzodiac", "animeshojo": "animeshojo", "reverseharem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "<PERSON><PERSON><PERSON><PERSON>", "mybossdaddy": "bambo<PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "ma<PERSON><PERSON><PERSON><PERSON>", "bloodc": "magazi", "talesofdemonsandgods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "goreanime", "animegirls": "atsikanaakuchitaanimation", "sharingan": "<PERSON><PERSON>", "crowsxworst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatteranime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ya", "splatter": "<PERSON><PERSON><PERSON><PERSON>", "risingoftheshieldhero": "kukwerapamphindowamtetezi", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "ndatenga", "animeyuri": "animeyuri", "animeespaña": "<PERSON><PERSON><PERSON>", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "bodza_lokha_lokha", "supercampeones": "osewera_amphamvu", "animeidols": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "masikuobiriwira", "magicalgirls": "atsikanamwamatsenga", "callofthenight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "pemphezamanga", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekiss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "nyaninyeziyankulu", "animeverse": "d<PERSON><PERSON>lithunthu_la_anime", "persocoms": "makompyu<PERSON>_amunthu", "omniscientreadersview": "owelengandimaonaamunthualiense", "animecat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animerecommendations": "animezomwemukhozakuwona", "openinganime": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "nthanoyangazachisangalalozanga", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "<PERSON><PERSON><PERSON><PERSON>", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "ndimenyetandigandamupatelefonikeyamanja", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "makina", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "blichi", "deathnote": "b<PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "greenranger": "greenranger", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "mphowademon", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "onepieceanime": "onepieceanime", "attaquedestitans": "attaquedestitans", "theonepieceisreal": "theonepiecendizoonadi", "revengers": "obwe<PERSON>a", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "chisangalalodzetsakupangitsa", "digimonstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "ndendezikuluzikulu", "metalocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinchan": "shinchan", "watamote": "sindinatch<PERSON>", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flawlesswebtoon": "webtoonyosalakwika", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "mfitiyowuluka", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "ch<PERSON>uk<PERSON><PERSON>be", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}