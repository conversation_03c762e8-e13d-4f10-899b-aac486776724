{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "taurari", "cognitivefunctions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "philosophy": "falsafa", "history": "tarihi", "physics": "physics", "science": "kimi<PERSON>", "culture": "alada", "languages": "harsuna", "technology": "fasaha", "memes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mbtimemes": "mbtimemes", "astrologymemes": "astrologymemes", "enneagrammemes": "enneagrammemes", "showerthoughts": "tunaninshawa", "funny": "bandarriya", "videos": "bidiyo", "gadgets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "politics": "<PERSON>yasa", "relationshipadvice": "shaw<PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "crypto", "news": "labarai", "worldnews": "labarin_duniya", "archaeology": "ka<PERSON><PERSON><PERSON><PERSON>", "learning": "koyo", "debates": "muh<PERSON><PERSON>", "conspiracytheories": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "universe": "<PERSON>aga<PERSON>", "meditation": "tunani", "mythology": "<PERSON><PERSON><PERSON>", "art": "zanezane", "crafts": "sanaoi", "dance": "rawa", "design": "ƙira", "makeup": "makeup", "beauty": "kyau", "fashion": "salo", "singing": "waƙa", "writing": "rubutu", "photography": "hoton_hotuna", "cosplay": "cosplay", "painting": "zanen", "drawing": "zanezane", "books": "littattafai", "movies": "finafinai", "poetry": "wakoki", "television": "talabijin", "filmmaking": "yinfilm", "animation": "animation", "anime": "anime", "scifi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasy": "fantasy", "documentaries": "shirin_gaskiya", "mystery": "sirri", "comedy": "<PERSON><PERSON><PERSON>", "crime": "laifi", "drama": "<PERSON><PERSON>", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "tsoro", "romance": "soyayya", "realitytv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "aikin", "music": "kiɗa", "blues": "blues", "classical": "nak<PERSON><PERSON>", "country": "ƙasa", "desi": "desi", "edm": "edm", "electronic": "la<PERSON><PERSON>i", "folk": "mawaka", "funk": "funk", "hiphop": "hiphop", "house": "gida", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "karfe", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "dutse", "techno": "techno", "travel": "ta<PERSON>ya", "concerts": "wasanninkiɗa", "festivals": "was<PERSON><PERSON>_aladu", "museums": "gidajen_tarihi", "standup": "mi<PERSON><PERSON><PERSON><PERSON>", "theater": "wasankwaikwayo", "outdoors": "wajen_gida", "gardening": "aikinlambu", "partying": "sha<PERSON>i", "gaming": "wasa", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "dungeonsanddragons", "chess": "satar_dara", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "a<PERSON><PERSON>", "baking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cooking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegetarian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegan": "<PERSON><PERSON><PERSON>", "birds": "t<PERSON><PERSON><PERSON>e", "cats": "k<PERSON><PERSON><PERSON>", "dogs": "karnuka", "fish": "kifi", "animals": "<PERSON><PERSON><PERSON><PERSON>", "blacklivesmatter": "bakimutumine", "environmentalism": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "feminism": "mata_masu_kishin_kai", "humanrights": "hakkokinadamakai", "lgbtqally": "maikawancenlgbtq", "stopasianhate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "volunteering": "taimakonhankali", "sports": "<PERSON><PERSON><PERSON>", "badminton": "badminton", "baseball": "wasanƙwallonkwando", "basketball": "kwaskwallon_kafa", "boxing": "dambe", "cricket": "kiri<PERSON><PERSON>", "cycling": "keke", "fitness": "motsa_jiki", "football": "ƙwallon_ƙafa", "golf": "golf", "gym": "gymdaaj<PERSON>motsa", "gymnastics": "gymnastics", "hockey": "ƙwallonƙarfe", "martialarts": "labaru_kwallon_kafa", "netball": "ƙwallonraga", "pilates": "pilates", "pingpong": "wasan_tebur", "running": "gudu", "skateboarding": "wasan_allo", "skiing": "tallankankara", "snowboarding": "hawandusarƙanƙara", "surfing": "hawan_i<PERSON><PERSON>_ruwa", "swimming": "iyo", "tennis": "wasan_tennis", "volleyball": "waliyar", "weightlifting": "ɗagaganakiba", "yoga": "yoga", "scubadiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiking": "yawocikindutse", "capricorn": "capricorn", "aquarius": "aquarius", "pisces": "pisces", "aries": "aries", "taurus": "taurus", "gemini": "gemini", "cancer": "ciwonkansa", "leo": "leo", "virgo": "kigo", "libra": "libra", "scorpio": "scorpio", "sagittarius": "sagittarius", "shortterm": "da<PERSON><PERSON><PERSON>", "casual": "kyauta", "longtermrelationship": "dangantakadaɗewa", "single": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "polyamory": "da<PERSON><PERSON><PERSON><PERSON><PERSON>", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "madigo", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingsquest": "tamba<PERSON><PERSON><PERSON>", "soulreaver": "ma<PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofspyro": "tatsuninyarspyro", "rouguelikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "faɗuwargwandama", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "yaƙingungiyoyi", "openworld": "duniyanbuɗe", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "ta<PERSON><PERSON><PERSON><PERSON>", "lordsoftherealm2": "sarautagoman2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "medabots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "wasanninkwaikwayogaske", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON>", "witcher": "witcher", "dishonored": "<PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "datti", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "<PERSON><PERSON><PERSON><PERSON>", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "gyara", "charactercreation": "<PERSON><PERSON><PERSON><PERSON>", "immersive": "<PERSON>ewa", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motsinzuciyatakomamutuwa", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "<PERSON><PERSON><PERSON><PERSON>", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "ƙasarguzuri", "pathfinder": "ma<PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "soyayyarn<PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "<PERSON><PERSON><PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "kaiɗaya", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "s<PERSON><PERSON><PERSON><PERSON>", "yourturntodie": "lokacinkamutuwa", "persona3": "persona3", "rpghorror": "rpgtsoro", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "masu_hari", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "<PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "duniyoyi<PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "dauri<PERSON><PERSON>u", "diabloimmortal": "diabloimmortal", "dynastywarriors": "yaƙinhanedoki", "skullgirls": "skullgirls", "nightcity": "bi<PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "fadang<PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "hanya96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "wasanninkwallokallin", "gothamknights": "gothamknights", "forgottenrealms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonlance": "dragonlance", "arenaofvalor": "<PERSON><PERSON><PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "g<PERSON><PERSON><PERSON><PERSON>", "childoflight": "<PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "duni<PERSON><PERSON><PERSON>", "monsterrancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "karacintama2", "xeno": "baƙo", "vulcanverse": "duniyarvulcan", "fracturedthrones": "gadaje<PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "kowanesarkakiya", "hogwartmystery": "<PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "doke", "lastepoch": "zamaninƙarshe", "starfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goldensun": "<PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "dareduhu2000", "sandevistan": "sandevistan", "cyberpunk": "saibafonk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "shai<PERSON>_mai_tsira", "oldschoolrunescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "allahntaka", "pf2": "pf2", "farmrpg": "wasan_noma", "oldworldblues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "balaguron<PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "<PERSON><PERSON><PERSON><PERSON>", "talesofsymphonia": "labarunsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myfarog": "myfarog", "sacredunderworld": "<PERSON>ni<PERSON><PERSON>u", "chainedechoes": "sauraran_ƙararrawa", "darksoul": "ruh<PERSON><PERSON><PERSON>", "soulslikes": "wasankanzankamarrai", "othercide": "othercide", "mountandblade": "du<PERSON>end<PERSON><PERSON>", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pillarsofeternity": "ginshikandawwadawa", "palladiumrpg": "palladiumrpg", "rifts": "rarrabuwa", "tibia": "ƙashinƙafa", "thedivision": "<PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "sannu_charlotte", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "danhuntumin", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON>", "engineheart": "zuciyarinjin", "fable3": "tatsuniya3", "fablethelostchapter": "tatsuni<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "m<PERSON><PERSON><PERSON><PERSON>", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "zamancanfarwalawa", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "wasanninrpg", "kingdomhearts": "sarautandazuciya", "kingdomheart3": "mulukinzuciya3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "girbi", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "kagara", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "samawanarcadia", "shadowhearts": "zukatansumila", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "shaƙarwuta4", "mother3": "uwa3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "wani<PERSON>dan", "roleplaygames": "wasanninkwaikwayo", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "kyakkyawar<PERSON><PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirilamasquerade", "dračák": "ɗan_tsoro", "spelljammer": "<PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "lokacigicewa", "cocttrpg": "cocttrpg", "huntroyale": "farauta_na_sarauta", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "dandalinrpg", "shadowheartscovenant": "al<PERSON><PERSON>_shadowheart", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "mul<PERSON><PERSON><PERSON>", "awplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragaliaẓatacce", "elderscroll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dyinglight2": "wasandmutuwa2", "finalfantasytactics": "finalfantasytactics", "grandia": "babba", "darkheresy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "dilalantitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "sihirinƙasa", "blackbook": "littafiba<PERSON>", "skychildrenoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "castlecrashers": "<PERSON>mash<PERSON><PERSON><PERSON><PERSON>", "gothicgame": "<PERSON><PERSON><PERSON><PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "fatalwayatokyo", "fallout2d20": "fallout2d20", "gamingrpg": "wasanrpg", "prophunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starrails": "<PERSON><PERSON><PERSON>", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "taftada<PERSON><PERSON>na", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "emilybabu", "indivisible": "bazakiyaraba", "freeside": "gefebabu", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathroadtocanada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "ikonmakwabta", "persona5": "persona5", "ghostoftsushima": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "wasanrpgnadabara", "mahoyo": "mahoyo", "animegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "maicingod", "diluc": "diluc", "venti": "venti", "eternalsonata": "waƙarkadadauwa", "princessconnect": "girkankaddarwa", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "da<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindiya", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "wasanninelektironik", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "<PERSON><PERSON><PERSON><PERSON>", "mlg": "mlg", "leagueofdreamers": "ƙungiyarmasu_ma<PERSON>ki", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "ƙwallon_ƙafa_ta_lantarki", "dreamhack": "mafarkinhaɗa", "gaimin": "gemin", "overwatchleague": "ganin<PERSON><PERSON>verwatch", "cybersport": "<PERSON><PERSON><PERSON>k<PERSON><PERSON><PERSON><PERSON>", "crazyraccoon": "damuƙarbazikinzomo", "test1test": "test1test", "fc24": "fc24", "riotgames": "wasanninkofifi", "eracing": "sharamin", "brasilgameshow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorantcompetitive": "valorant<PERSON>a", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead": "anbarsu4matattu", "left4dead2": "anbarsu4matattu2", "valve": "bawul", "portal": "<PERSON><PERSON><PERSON>", "teamfortress2": "teamfortress2", "everlastingsummer": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transformice": "transformice", "justshapesandbeats": "siff<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "yakinbattlefield4", "nightinthewoods": "<PERSON><PERSON>jin<PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "ya<PERSON><PERSON><PERSON>", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "kasadarkashi2", "metroidvanias": "metroidvanias", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "tsakaninplanets", "helltaker": "j<PERSON><PERSON><PERSON>", "inscryption": "rum<PERSON>inin<PERSON>e", "7d2d": "7k2k", "deadcells": "kwayoyinmatattu", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "<PERSON><PERSON><PERSON><PERSON>", "stray": "maras<PERSON>da", "battlefield": "dandali", "battlefield1": "yakinsakata1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "jirginkarkashinruwa", "eyeb": "gira", "blackdesert": "hamadanbaƙi", "tabletopsimulator": "wasantabletop", "partyhard": "<PERSON>n<PERSON><PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hades": "hades", "gunsmith": "maƙerinbindiga", "okami": "<PERSON>ami", "trappedwithjester": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinkum": "gaske", "predecessor": "ma<PERSON><PERSON><PERSON>", "rainworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "<PERSON><PERSON><PERSON><PERSON>", "minionmasters": "minionmasters", "grimdawn": "<PERSON><PERSON><PERSON><PERSON>", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "was<PERSON><PERSON><PERSON><PERSON>ak<PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "kuɓantamewa", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "birninisabon", "citiesskylines": "fagenbiranebirane", "defconheavy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenopsia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virtualkenopsia": "digitalinbacintakawar", "snowrunner": "dusar_ƙanƙara_mai_gudu", "libraryofruina": "ɗakinlittafinruina", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "jagora", "kenabridgeofspirits": "gadadagagg<PERSON><PERSON><PERSON><PERSON>", "placidplasticduck": "yakamutmulasteduck", "battlebit": "yaƙinyaƙi", "ultimatechickenhorse": "dokokazurcinkatakara", "dialtown": "gari<PERSON><PERSON><PERSON>", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermeatboy", "tinnybunny": "ƙaraminkishiya", "cozygrove": "dajincalmness", "doom": "karshe", "callofduty": "callofduty", "callofdutyww2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "iyakokiniyaka", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "kashinbaka", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "wasanninfarcrygames", "paladins": "paladins", "earthdefenseforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto5": "grandtheftauto5", "warz": "yaƙe", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "<PERSON><PERSON><PERSON><PERSON>", "joinsquad": "shi<PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "mutu<PERSON><PERSON><PERSON><PERSON>", "b4b": "b4b", "codwarzone": "yaƙinyafimafiƙo", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "gefenka<PERSON><PERSON>", "divisions2": "sasai2", "killzone": "yank<PERSON><PERSON><PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "yaƙinyasamaimai", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "harbin_maharbi", "modernwarfare": "<PERSON><PERSON><PERSON><PERSON>", "neonabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "boarderlands", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "farautarfaɗuwa", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON>ragenru<PERSON>", "back4blood": "dawowar4jini", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "ma<PERSON>sangilla", "masseffect": "masseffect", "systemshock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "<PERSON><PERSON><PERSON><PERSON>", "doometernal": "mutu<PERSON><PERSON><PERSON><PERSON><PERSON>", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "zan_ji_dadi", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "yakinzamani2", "blackops1": "blackops1", "sausageman": "da<PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "tambayarchex", "thephantompain": "zafindoru<PERSON>", "warface": "fuskary<PERSON>", "crossfire": "luguden_wuta", "atomicheart": "zuciyaratomic", "blackops3": "blackops3", "vampiresurvivors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "yaƙindutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "yanci", "battlegrounds": "fagagen_yaki", "frag": "frag", "tinytina": "karama<PERSON>ina", "gamepubg": "wasanpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "wasanninfps", "convertstrike": "juyin_addini", "warzone2": "warzone2", "shatterline": "<PERSON><PERSON><PERSON>ye<PERSON>", "blackopszombies": "zombiesnabaƙinops", "bloodymess": "halinruɗuɗi", "republiccommando": "jarumianjamhur<PERSON>", "elitedangerous": "elitedangerous", "soldat": "soja", "groundbranch": "restarshen", "squad": "tawagar", "destiny1": "kadarci1", "gamingfps": "<PERSON><PERSON><PERSON><PERSON>", "redfall": "redfall", "pubggirl": "yarinyarpubg", "worldoftanksblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyblackops": "callofdutyblackops", "enlisted": "sakatare", "farlight": "<PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "ranaralbashi2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "mahayanpubg", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostcod": "ghostcod", "csplay": "cosplay", "unrealtournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "bulalar_bindiga", "callofdutymw2": "callofdutymw2", "quakechampions": "zaka<PERSON>_girgiza", "halo3": "halo3", "halo": "halo", "killingfloor": "<PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "farin<PERSON>", "remnant": "ragowar", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "<PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "mut<PERSON><PERSON><PERSON><PERSON>", "quake2": "girgiza2", "microvolts": "microvolts", "reddead": "mut<PERSON><PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "yakinmaidaki3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "tekunkwazon", "rust": "tsatsa", "conqueronline": "nasaraonline", "dauntless": "<PERSON><PERSON>", "warships": "<PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON>", "warthunder": "yaƙintsawa", "flightrising": "<PERSON><PERSON><PERSON><PERSON>", "recroom": "<PERSON><PERSON><PERSON><PERSON>", "legendsofruneterra": "danbazanruneterra", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "phantasystaronline2", "maidenless": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "tsa<PERSON><PERSON>", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netplay": "<PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "ma<PERSON><PERSON><PERSON><PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "yanwasanninƙi", "pirate101": "danfashi101", "honorofkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "yaƙinyaƙintaurari", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "yakinstarwars2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "gari<PERSON><PERSON>", "3dchat": "3<PERSON><PERSON>", "nostale": "b<PERSON><PERSON><PERSON><PERSON>", "tauriwow": "tauri<PERSON><PERSON>w", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "yaƙinaƙasashe", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "wasan_ƙungiya", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "tokar<PERSON><PERSON>tuwa", "riotmmo": "riotmmo", "silkroad": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "yaƙinmasu", "mulegend": "mufifike", "startrekonline": "startrekacikinlayi", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grymmo": "grymmo", "warmane": "dumi", "multijugador": "multijugador", "angelsonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "zagayezagaye", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "blueprotocol", "perfectworld": "duniyarcikakke", "riseonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "corepunk": "babbanpunk", "adventurequestworlds": "kashin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "danfada", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "laifingiyal", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "t<PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mutuwarhaɗinkai", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blasphemous": "shirka", "rivalsofaether": "abokan<PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "bab<PERSON><PERSON><PERSON>", "mugen": "mugen", "warofthemonsters": "yaƙindodonkuna", "jogosdeluta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "bot<PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "yaƙintaƙarshe", "poweredgear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "<PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "kishinhalaka", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "yarinyarduddube2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "wasakegudunhollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silikinsonghornet", "silksonggame": "wasansilksong", "silksongnews": "sigilansilksong", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON>", "typelumina": "rubut<PERSON><PERSON>", "evolutiontournament": "gasar_cigaba", "evomoment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lollipopchainsaw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "lab<PERSON><PERSON><PERSON><PERSON>", "bloodborne": "bloodborne", "horizon": "ta<PERSON><PERSON>", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "ji<PERSON><PERSON><PERSON><PERSON>", "uncharted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "naƙarshe", "infamous": "m_sha<PERSON><PERSON>", "playstationbuddies": "abokanwasanplaystation", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "sanjabarewa", "gta4": "gta4", "gta": "gta", "roguecompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "k<PERSON><PERSON><PERSON>_taska", "detroitbecomehuman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "har<PERSON><PERSON>i", "touristtrophy": "bud<PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "inuwarkagidan", "crashteamracing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fivepd": "shekaru5dapolis", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "wasanstation", "samuraiwarriors": "jarumansамurai", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "zukatainiyoyi2alkawali", "pcsx2": "pcsx2", "lastguardian": "maciginƙarshe", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "dan<PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "masu_tafiya_cikin_hankali", "mhw": "mhw", "princeofpersia": "yarimalfaris", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "yaƙinfarko", "dontstarvetogether": "kadakuciyarciharekanku", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fable2": "tatsuniya2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "kuɗinkama", "skycotl": "samacotl", "erica": "erica", "ancestory": "asali", "cuphead": "cuphead", "littlemisfortune": "ƙaramarmasifa", "sallyface": "fuskarsally", "franbow": "franbow", "monsterprom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "duniyoyi<PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "addinichaɗin", "duckgame": "wasandambaza", "thestanleyparable": "littafinstanley", "towerunite": "haɗutasak", "occulto": "boye", "longdrive": "dogontafiya", "satisfactory": "daidai", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "ƙarƙashinƙasa", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenshi": "kenshi", "spiritfarer": "jago<PERSON>_ruhohi", "darkdome": "duhu<PERSON><PERSON>", "pizzatower": "pizzatower", "indiegame": "<PERSON><PERSON><PERSON><PERSON>", "itchio": "itchio", "golfit": "y<PERSON>lf", "truthordare": "gaskiyakotsallama", "game": "wasa", "rockpaperscissors": "duts<PERSON><PERSON>urtara", "trampoline": "was<PERSON>_t<PERSON>le", "hulahoop": "zobe_kafkafi", "dare": "<PERSON><PERSON><PERSON><PERSON>", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "was<PERSON>in<PERSON>lin", "pickanumber": "zauɓilamba", "trueorfalse": "gaskiyakokarya", "beerpong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegoblin": "maisonkadanya", "cosygames": "was<PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "juegos", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "wasanninkwaikwayo", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "wasancikinbakinzuciya", "oyun": "oyun", "interactivegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "spiele", "giochi": "<PERSON><PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "wasanninaiphone", "boogames": "wasanniboo", "cranegame": "<PERSON><PERSON><PERSON><PERSON>", "hideandseek": "ɓoyedanema", "hopscotch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arcadegames": "was<PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "wasanninkyakuza", "classicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mindgames": "wasaninkankanta", "guessthelyric": "kwazolaya", "galagames": "<PERSON><PERSON><PERSON><PERSON>", "romancegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "ɓaɗaɗɗenharshe", "4xgames": "4x<PERSON><PERSON><PERSON>", "gamefi": "was<PERSON>_kudi", "jeuxdarcades": "was<PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "wasannintebur", "metroidvania": "metroidvania", "games90": "wasa90", "idareyou": "nagaskeka", "mozaa": "mozaa", "fumitouedagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "wasannintsere", "ets2": "ets2", "realvsfake": "<PERSON>gi<PERSON><PERSON><PERSON>", "playgames": "<PERSON><PERSON><PERSON><PERSON>", "gameonline": "wasaonline", "onlinegames": "wasanninintanet", "jogosonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>_hali", "playaballgame": "wasanƙwalallo", "pictionary": "zanen_tambaya", "coopgames": "wasanninhaɗinkai", "jenga": "jenga", "wiigames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "highscore": "maka<PERSON><PERSON>", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON>", "burgergames": "wasankibaga", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwbaƙinedition", "jeuconcour": "<PERSON><PERSON><PERSON><PERSON>", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON>", "gioco": "gioco", "managementgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "wasanformula1", "citybuilder": "mangajin_birni", "drdriving": "drdireba", "juegosarcade": "was<PERSON><PERSON><PERSON><PERSON>", "memorygames": "wasannin_ƙwaƙwalwa", "vulkan": "vulkan", "actiongames": "wasanninaiki", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "kwallonkarfe", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "zamantuberazama", "perguntados": "tamba<PERSON>yi", "gameo": "gameo", "lasergame": "<PERSON><PERSON><PERSON><PERSON>", "imessagegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "cikagibin", "jeuxpc": "jeuxpc", "rétrogaming": "wasankomfutankwarai", "logicgames": "<PERSON><PERSON><PERSON>_<PERSON>_tunani", "japangame": "<PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "<PERSON><PERSON><PERSON><PERSON>", "subwaysurf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON>", "exitgames": "was<PERSON><PERSON><PERSON>", "5vs5": "5vs5", "rolgame": "<PERSON><PERSON><PERSON><PERSON>", "dashiegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameandkill": "<PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "wasanfps", "textbasedgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "wasanƙwallon_fantasy", "retrospel": "<PERSON><PERSON><PERSON><PERSON>", "thiefgame": "<PERSON><PERSON><PERSON><PERSON>", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "ƙwallanteburin", "tischfußball": "ƙwallontebur", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "wasanninkada", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "wasanɓarawo", "cranegames": "wasanninkurege", "játék": "<PERSON><PERSON><PERSON><PERSON>", "bordfodbold": "bordfodbold", "jogosorte": "jogosorte", "mage": "<PERSON><PERSON>i", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "<PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "daren_wasa", "pursebingos": "jakar_bingo", "randomizer": "baz<PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "socialdeductiongames": "wasann<PERSON>newagudu", "dominos": "dominos", "domino": "domino", "isometricgames": "wasanninisometric", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "gaskiyakodaƙalubale", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "wasanninintanet", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "kyauta2wasa", "fantasygame": "<PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "<PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mushroomoasis": "<PERSON><PERSON><PERSON>", "anythingwithanengine": "komedayanainjin", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "takobidamatsiya", "goodgamegiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "munayin_wasa", "lab8games": "lab8games", "labzerogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grykomputerowe": "wasanninkwamfuta", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "tsereninmotatuddu4", "selflovegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "wasannincaca", "spelletjes": "<PERSON><PERSON>in", "spacenerf": "spacenerf", "charades": "<PERSON><PERSON><PERSON><PERSON>", "singleplayer": "ɗanyaɗankwaikwayo", "coopgame": "wasancoop", "gamed": "wasa", "forzahorizon": "forzahorizon", "nexus": "haɗin_kai", "geforcenow": "geforcenow", "maingame": "<PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "<PERSON><PERSON><PERSON><PERSON>", "scrabble": "<PERSON><PERSON><PERSON>", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "dara", "onitama": "onitama", "pandemiclegacy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "camelup": "rak<PERSON><PERSON>tuwa", "monopolygame": "wasanmonopoly", "brettspiele": "brettspiele", "bordspellen": "<PERSON><PERSON><PERSON>", "boardgame": "wasank<PERSON><PERSON>", "sällskapspel": "wasaninkungiyya", "planszowe": "planszowe", "risiko": "kasada", "permainanpapan": "<PERSON><PERSON><PERSON>", "zombicide": "kisan_aljanu", "tabletop": "teburintasa", "baduk": "baduk", "bloodbowl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "zajekasank<PERSON><PERSON>", "connectfour": "haɗahuɗu", "heroquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidatavolo": "wasannintebur", "farkle": "farkle", "carrom": "carram", "tablegames": "<PERSON><PERSON><PERSON><PERSON>", "dicegames": "wasannindice", "yatzy": "yatzy", "parchis": "farcis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "wasannitebur", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "gam<PERSON><PERSON><PERSON><PERSON><PERSON>", "creationludique": "kirkirkawance", "tabletoproleplay": "wasankwaikway<PERSON>kan<PERSON>bur", "cardboardgames": "wasanninkatink<PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "<PERSON><PERSON><PERSON><PERSON>", "infinitythegame": "wasaninfinity", "kingdomdeath": "mutu<PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "tub<PERSON><PERSON>atakala", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "<PERSON><PERSON><PERSON>", "planszówki": "planszów<PERSON>", "rednecklife": "rayuwarbauƙa", "boardom": "gajiya", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "<PERSON><PERSON><PERSON><PERSON>a", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "tauronsultan", "horseopoly": "dawakiopoli", "deckbuilding": "ginadauka", "mansionsofmadness": "gidajenmahaukata", "gomoku": "gomoku", "giochidatavola": "wasanninteburin", "shadowsofbrimstone": "inukanbrimstone", "kingoftokyo": "sa<PERSON><PERSON><PERSON>", "warcaby": "damau", "táblajátékok": "táblajátékok", "battleship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tickettoride": "tikitin_tafiya", "deskovehry": "wasannitebur", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "wasanninatebur", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "<PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "starwarslegion", "gochess": "<PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "yaƙinyaƙi", "arksurvivalevolved": "arksurvivalevolved", "dayz": "k<PERSON><PERSON>", "identityv": "identityv", "theisle": "<PERSON><PERSON><PERSON><PERSON>", "thelastofus": "nak<PERSON>he", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "kirancthulhu", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "a<PERSON><PERSON><PERSON>", "eco": "muhalli", "monkeyisland": "tsi<PERSON><PERSON>_biri", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "<PERSON><PERSON><PERSON>", "pathologic": "cutacuta", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grounded": "<PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "yakinruɓewa2", "vrising": "vrising", "madfather": "<PERSON><PERSON><PERSON><PERSON>", "dontstarve": "ka<PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "hanyarkafirai", "frictionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexen": "hexen", "theevilwithin": "<PERSON><PERSON><PERSON><PERSON>", "realrac": "gaskiyarrac", "thebackrooms": "<PERSON><PERSON><PERSON><PERSON>", "backrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "empiressmp": "empiressmp", "blockstory": "labarinkatanga", "thequarry": "<PERSON><PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "haske_yana_mutuwa", "thewalkingdeadgame": "<PERSON><PERSON><PERSON><PERSON>", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "tashindaular", "stateofsurvivalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagestory": "<PERSON><PERSON><PERSON><PERSON>", "arksurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "barotrauma": "rau<PERSON><PERSON><PERSON><PERSON>", "breathedge": "numfashi", "alisa": "alisa", "westlendsurvival": "rayuwarwestlend", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "tsoro_rayuwa", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "j<PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "wannanyaƙinadawa", "scpfoundation": "scpfoundation", "greenproject": "aikinkorearhuwa", "kuon": "<PERSON><PERSON><PERSON><PERSON>", "cryoffear": "kukantsoro", "raft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "mataccenpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares2": "damainkasadakundarare2", "signalis": "<PERSON><PERSON><PERSON><PERSON>", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "yayandarjin", "rustvideogame": "wasanbidonrust", "outlasttrials": "gwajintsayawa", "alienisolation": "bazamubaƙonaware", "undawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "7day2die": "7kwanakamutu", "sunlesssea": "tekuntekubahaskene", "sopravvivenza": "sopravvivenza", "propnight": "<PERSON><PERSON><PERSON><PERSON>", "deadisland2": "tsibirin_matattu2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "kyakyawanvampire", "deathverse": "mut<PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON>yun<PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "<PERSON><PERSON><PERSON>_bayan", "ageofdarkness": "<PERSON>aman<PERSON><PERSON><PERSON>", "clocktower3": "hasumiyaagogo3", "aloneinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "da<PERSON><PERSON><PERSON><PERSON>_da", "projectnimbusgame": "wasanprojectnimbus", "eternights": "<PERSON><PERSON><PERSON><PERSON>", "craftopia": "sana<PERSON><PERSON><PERSON>", "theoutlasttrials": "gwajingwallatonjiki", "bunker": "bunka", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "karfangunguwa", "warhammer40kcrush": "warhammer40kyaƙwaƙwa", "wh40": "wh40", "warhammer40klove": "soyayyarwarhammer40k", "warhammer40klore": "labarin_warhammer40k", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "inasonsororitas", "ilovevindicare": "inasonyinvindicare", "iloveassasinorum": "iloveassasinorum", "templovenenum": "sabondangimu", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "yancindubiyuneake", "wingspan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terraformingmars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofmightandmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "btd6": "btd6", "supremecommander": "jagoranmukiya", "ageofmythology": "zamanintsumma", "args": "args", "rime": "rime", "planetzoo": "duwatsardabirai", "outpost2": "wajeninmu2", "banished": "hamɓe", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "yaƙindabaiyaƙarewa", "strategygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization4": "wayewa4", "factorio": "factorio", "dungeondraft": "zane<PERSON><PERSON><PERSON>", "spore": "spore", "totalwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travian": "travian", "forts": "ganuwa", "goodcompany": "kyawawan_tarbiyya", "civ": "civ", "homeworld": "labaring<PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON>", "forthekings": "don<PERSON><PERSON><PERSON>", "realtimestrategy": "dabarunlokacingaskiya", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eu4": "eu4", "vainglory": "girman_kai", "ww40k": "ww40k", "godhood": "allahntaka", "anno": "anno", "battletech": "yaƙinteknoloji", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "kwallonkafa3", "davesfunalgebraclass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plagueinc": "annobaincorporated", "theorycraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "wayardunya3", "4inarow": "4cikidaciki", "crusaderkings3": "crusaderkings3", "heroes3": "matasan3", "advancewars": "yaƙintarawa", "ageofempires2": "zamaninmasarautu2", "disciples2": "muridai2", "plantsvszombies": "tsirraiwadacutar", "giochidistrategia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stratejioyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "sarkindinosaur", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "zukatankarfe4", "companyofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "yaƙindawesnoth", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "kazakazaduck", "phobies": "tsoro", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "<PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "zamanindauloli4", "civilization5": "wayyadunci5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON>i", "starwarsempireatwar": "ya<PERSON><PERSON>atstar<PERSON>s", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "<PERSON><PERSON>", "popfulmail": "wasiƙunsamai", "shiningforce": "karka<PERSON><PERSON>", "masterduel": "masterduel", "dysonsphereprogram": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transporttycoon": "jago<PERSON>_su<PERSON>ri", "unrailed": "ba_a_kan_titin_jirgi", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "gudunarnajirginsamatorment", "uplandkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galaxylife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "kyautalonlayi", "slaythespire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlecats": "yaƙinkuliyoyi", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "mots<PERSON><PERSON>a", "needforspeed": "b<PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "asarathesims4", "fnaf": "fnaf", "outlast": "fi_karfi", "deadbydaylight": "mut<PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "littafintatsuniyoyinbakinbanza", "phasmophobia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "dareninbiyadafreddy", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "ƙanananmafarkandare", "deadrising": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "zaunegida", "deadisland": "tsi<PERSON><PERSON>_matattu", "litlemissfortune": "karamarsassara", "projectzero": "projektusifili", "horory": "firg<PERSON>", "jogosterror": "was<PERSON><PERSON>", "helloneighbor": "sannu_mak<PERSON><PERSON>", "helloneighbor2": "sanunumakwabci2", "gamingdbd": "wasangamingdbd", "thecatlady": "maimacecat", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "gwent<PERSON>", "legendofrunetera": "almararrunetera", "solitaire": "katin_kaddarwa", "poker": "katin_caca", "hearthstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "mabudinƙarfe", "cardtricks": "goridintare", "playingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "<PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "ka<PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "was<PERSON>_kati_nama_da_jini", "sportscards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "yaƙinkatunanvanguard", "duellinks": "duellinks", "spades": "feri", "warcry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "sarkin<PERSON><PERSON>ya", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "<PERSON><PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "wasanyug<PERSON><PERSON>", "darkmagician": "maiduhunsihiri", "blueeyeswhitedragon": "idanushudi<PERSON>rfafaruwa", "yugiohgoat": "yugiohzakara", "briscas": "briscas", "juegocartas": "j<PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "wasanninkatunan", "mtgjudge": "mtgjudge", "juegosdecartas": "wasan<PERSON>una<PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "carteado", "sueca": "sueca", "beloteonline": "be<PERSON><PERSON><PERSON><PERSON>", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "yaƙinruhohi", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "wasan<PERSON>una<PERSON>", "žolíky": "katin_am<PERSON>i", "facecard": "fuskark<PERSON><PERSON>", "cardfight": "yaƙinkatunan", "biriba": "biriba", "deckbuilders": "masu_gina_buga", "marvelchampions": "<PERSON><PERSON><PERSON><PERSON>", "magiccartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "<PERSON><PERSON><PERSON><PERSON>", "unstableunicorns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberse": "fasa<PERSON><PERSON><PERSON>", "classicarcadegames": "wasanninarcadenada", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fndnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "jar<PERSON><PERSON>a", "clonehero": "clonehero", "justdance": "<PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "girgizamatattu", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rhythmgamer": "danwasankaɗa", "stepmania": "wakarstepmania", "highscorerythmgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pkxd": "pkxd", "sidem": "<PERSON><PERSON>i", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "wasanƙafarkaɗa", "hypmic": "hypmic", "adanceoffireandice": "rawan_wuta_da_kankara", "auditiononline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "wasanninkaɗa", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "likitankaɗa", "cubing": "cubing", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "wasanninkwakwalwa", "spotit": "ganeshi", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "wasaninkwakwalwa", "rubikscube": "rubikscube", "crossword": "ka<PERSON><PERSON><PERSON><PERSON>", "motscroisés": "motscroisés", "krzyżówki": "kalimatinwasiƙa", "nonogram": "nonogram", "bookworm": "ɗankaranta", "jigsawpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinello": "tambaya", "riddle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riddles": "tambayoyin_wasa", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "ciki", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "kubuttarcesimulator", "minesweeper": "nak<PERSON><PERSON>", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "wasangardenshafa", "puzzlesport": "wasanwarware", "escaperoomgames": "wasanbugongido", "escapegame": "<PERSON><PERSON><PERSON>", "3dpuzzle": "cantsiudu3d", "homescapesgame": "wasanhomescapes", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "enigmistica", "kulaworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myst": "myst", "riddletales": "tatsuni<PERSON><PERSON><PERSON><PERSON>", "fishdom": "kif<PERSON><PERSON>", "theimpossiblequiz": "ta<PERSON><PERSON><PERSON><PERSON><PERSON>wuwa", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "wasanyincikiyargaba3", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "kubbaunrubik", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "g<PERSON><PERSON><PERSON>e", "puttputt": "dan_golf", "qbert": "qbert", "riddleme": "tamba<PERSON><PERSON><PERSON>", "tycoongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "cubosderubik", "cruciverba": "cruciver<PERSON>", "ciphers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "warwaremagudanar", "turnipboy": "yaronturnefi", "adivinanzashot": "tambayarshot", "nobodies": "ma<PERSON><PERSON><PERSON><PERSON>", "guessing": "kusantar_zato", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "waƙoƙintsinkaye", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "<PERSON><PERSON>a_was<PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "quebracabeça", "hlavolamy": "hlavolamy", "poptropica": "poptropica", "thelastcampfire": "wuta<PERSON><PERSON><PERSON>", "autodefinidos": "autodefinidos", "picopark": "picopark", "wandersong": "wakar_yawo", "carto": "carto", "untitledgoosegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "<PERSON><PERSON><PERSON><PERSON>", "limbo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rubiks": "rubiks", "maze": "caca", "tinykin": "<PERSON><PERSON>an", "rubikovakostka": "rubikovakostka", "speedcube": "garzagarcube", "pieces": "guntu", "portalgame": "wasankofa", "bilmece": "bilmece", "puzzelen": "zuzzulen", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "tamba<PERSON>yi", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopoly": "monopoly", "futurefight": "yaƙindagaba", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "ɗankadaɗe", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "rayuwa_ta_bit", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "taura<PERSON><PERSON><PERSON><PERSON>", "stateofsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "gari<PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "yaƙindokkan", "fategrandorder": "fategrandorder", "hyperfront": "gabangaba", "knightrun": "gujendaka", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "yaƙinƙwallon", "a3": "a3", "phonegames": "wasanninwaya", "kingschoice": "zabensarki", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "maisonmota", "tacticool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON>", "craftsman": "maisanaa", "supersus": "shak<PERSON><PERSON>", "slowdrive": "t<PERSON>maita<PERSON>", "headsup": "hanka<PERSON>", "wordfeud": "kalmomincimusaya", "bedwars": "gado<PERSON>wo", "freefire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "lambun<PERSON>ly", "farmville2": "farmville2", "animalcrossing": "<PERSON><PERSON>_da<PERSON><PERSON><PERSON>", "bgmi": "bgmi", "teamfighttactics": "yaƙingungiyoyi", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "wasancallofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "hedikwatangaggawa", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "zamanangimbiya", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "androidgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "criminalcase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "ya<PERSON><PERSON><PERSON>", "cookingmadness": "gir<PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "tamba<PERSON>yi<PERSON><PERSON><PERSON>", "leagueofangels": "gamayyankimalaikansport", "lordsmobile": "lordsmobile", "tinybirdgarden": "kankanindatsugaru", "gachalife": "gachalife", "neuralcloud": "gajimargizarneural", "mysingingmonsters": "waƙoƙinhalittuna", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "yaƙinroboci", "mirrorverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "dambenfikafikai", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "lokacinjin", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "shiga", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "fashedaga", "wolfy": "wolfy", "runcitygame": "<PERSON><PERSON><PERSON>", "juegodemovil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avakinlife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kogama": "kogama", "mimicry": "takama", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ldoe": "ldoe", "legendonline": "s<PERSON>gabaayakai", "otomegame": "wasanotome", "mindustry": "mindustry", "callofdragons": "kirankurki", "shiningnikki": "nikki_mai_walƙiya", "carxdriftracing2": "motardrifttsere2", "pathtonowhere": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "yaƙinduwawu3", "limbuscompany": "kambanilimbus", "demolitionderby3": "tsubumotocintsaro3", "wordswithfriends2": "kalimoicidaabbokai2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "labarimaicikakkenkyau", "showbyrock": "showbyrock", "ladypopular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "kamilduniyamobile", "empiresandpuzzles": "dauloliddapuzzles", "empirespuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "yakinmobile", "fanny": "buri", "littlenightmare": "ɗanmafarkinbandare", "aethergazer": "aethergazer", "mudrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "<PERSON><PERSON><PERSON>", "gunbound": "gunbound", "gamingmlbb": "wasanmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "almajaraimasukeɓe", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "mamana<PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "fadandaretitin", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "wasanbgmi", "girlsfrontline": "yakin<PERSON>", "jurassicworldalive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulseeker": "maineman_ruhi", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "motsarkadireboanakanintanet", "jogosmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofneverland": "almararalmakasudiyya", "pubglite": "pubglite", "gamemobilelegends": "wasanmobilelegends", "timeraiders": "lokacinhariyanmata", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "yaƙinkuloli", "dnd": "<PERSON><PERSON><PERSON><PERSON>", "quest": "nema", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "dankuwarcwatorpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "k<PERSON>bur<PERSON>ns<PERSON>", "d20": "d20", "pokemongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonred", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "ƙungiyarroket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "tawagarmystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "ƙananankyanwa", "shinypokemon": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "bar<PERSON><PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "dara", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "yan<PERSON><PERSON>ess", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "wasanchess", "japanesechess": "dam<PERSON><PERSON><PERSON><PERSON>", "chinesechess": "daran_sin", "chesscanada": "chesscanada", "fide": "tsoro", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON>", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "calabozosydragones", "dungeonsanddragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonmaster": "ma<PERSON>dan<PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "zindannadodoraguwa", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "tatsuniyarvoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "<PERSON><PERSON><PERSON><PERSON><PERSON>_moor", "minecraftchampionship": "gasar_jagoran_minecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "gwajingufargaba", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "karshenkyandir", "fru": "<PERSON><PERSON><PERSON>", "addons": "abu<PERSON><PERSON>_ƙari", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftdaakacanza", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "tsaikaninƙasashe", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "birnin_minecraft", "pcgamer": "pcgamer", "jeuxvideo": "jeuxvideo", "gambit": "gambit", "gamers": "yan<PERSON>a", "levelup": "ƙaramatarwa", "gamermobile": "wasan_wayar_hannu", "gameover": "<PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "wasanpcna", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "wasanninpc", "casualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgame": "wasanpc", "gamerboy": "<PERSON><PERSON><PERSON><PERSON>", "vrgaming": "wasanvr", "drdisrespect": "drdisrespect", "4kgaming": "wasan4k", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON>in", "consoleplayer": "da<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "<PERSON><PERSON><PERSON><PERSON>", "semigamer": "dan<PERSON><PERSON>", "gamergirls": "ya<PERSON><PERSON><PERSON>", "gamermoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerguy": "mai_wasan_gemu", "gamewatcher": "ma<PERSON><PERSON>_wasa", "gameur": "dan<PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "teammaitukin", "mallugaming": "<PERSON><PERSON><PERSON><PERSON>", "pawgers": "masu_pawg", "quests": "<PERSON><PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygaming": "wasankwaikwayoncikinnatsuwa", "gamelpay": "wasangamelpay", "juegosdepc": "wasanninpc", "dsswitch": "dsswitch", "competitivegaming": "wasantakinakwaikwayo", "minecraftnewjersey": "minecraftnewjersey", "faker": "karya", "pc4gamers": "pc4masu_wasa", "gamingff": "<PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "wasanheterosexual", "gamepc": "wasanpc", "girlsgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "bullata<PERSON><PERSON>", "gamegirl": "<PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "ya<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "socialgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "proplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayer": "<PERSON><PERSON><PERSON><PERSON>", "myteam": "tawagatanmu", "republicofgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aorus": "aorus", "cougargaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "triplelegend": "jagora3", "gamerbuddies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "butuhcewekgamers": "buttercewekgamers", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "da<PERSON><PERSON><PERSON><PERSON>", "nerdgamer": "maimainanerdgamer", "afk": "banzan", "andregamer": "andregamer", "casualgamer": "da<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "89squad", "inicaramainnyagimana": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "insec": "insec", "gemers": "gemers", "oyunizlemek": "oyunizlemek", "gamertag": "<PERSON><PERSON><PERSON><PERSON>", "lanparty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wspólnegranie": "ta<PERSON>war<PERSON>", "mortdog": "mortdog", "playstationgamer": "danyaronplaystationha", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtsinadi", "notebookgamer": "<PERSON>ib<PERSON><PERSON><PERSON>", "protogen": "protogen", "womangamer": "macewarƙwaƙwalwa", "obviouslyimagamer": "tabbas_ɗan_wasa_ne", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "humanfallflat": "muta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "waƙarnintenɗo", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "canja", "zelda": "zelda", "smashbros": "<PERSON><PERSON><PERSON><PERSON>", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "z<PERSON><PERSON><PERSON><PERSON>", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "yaransamaskychildrenofthelight", "tomodachilife": "rayuwaabokantaka", "ahatintime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogames": "wasanninnintendo", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "abokina_pedro", "legendsofzelda": "almajirintarzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51games", "earthbound": "ɗanƙasa", "tales": "labaru", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "dabb<PERSON><PERSON>_masu_t<PERSON><PERSON>a_wuri", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkers<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendos": "nintendos", "new3ds": "sabon3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "yaƙinhyrule", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "ma<PERSON>das<PERSON>i", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "ma<PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON>", "vanillalol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wildriftph": "wildriftph", "lolph": "dariya", "leagueoflegend": "leagueoflegend", "tốcchiến": "yaƙin_gashi", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "tallacarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspain", "aatrox": "aatrox", "euw": "yau", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "wasanfortnite", "gamingfortnite": "wasanfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "wasanninkwamfutar<PERSON><PERSON><PERSON>", "scaryvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_masu_ban<PERSON>oro", "videogamemaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videosgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "yaƙinshingentiyeta", "arcades": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "abokan<PERSON>", "farmingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "wasanninsandbox", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "filinrollar", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "wurin<PERSON><PERSON>u", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "wasanninkwamfuta", "theoldrepublic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videospiele": "videogames", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "wasannintafiya", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "was<PERSON><PERSON><PERSON><PERSON>", "retroarcade": "wasantsofaffigago", "vintagecomputing": "kwamfutocintikirai", "retrogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "rashinadalicci2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON>", "zenlife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "<PERSON><PERSON><PERSON>", "mystgames": "mystgames", "blockchaingaming": "wasanblockchain", "medievil": "zamanengijin", "consolegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolen", "outrun": "fi_gaba", "bloomingpanic": "tsoron<PERSON>re", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "was<PERSON><PERSON>", "monstergirlquest": "tamba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiant": "katonkai", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "<PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "wasanninjackbox", "interactivefiction": "lab<PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "masu_soyayya_masu_hankali", "visualnovel": "wazan_labari", "visualnovels": "littattafanganinga<PERSON>", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON>", "tcrghost": "tcrghost", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "gimbiyarmagariba", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON>", "novelavisual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "naji_bakin_ciki", "godhand": "<PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "juyin_uwa_na_busa_ganye", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starrail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyblade": "takobinmabuɗi", "aplaguetale": "<PERSON><PERSON><PERSON><PERSON>", "fnafsometimes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "novelasvisuales": "littattafanbicewa", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "<PERSON><PERSON><PERSON><PERSON>", "videojuejos": "videoju<PERSON><PERSON>", "videogamedates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_kwanan_wata", "mycandylove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "kawaisaboda3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "gidan<PERSON><PERSON><PERSON><PERSON>", "crashracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "wasannin3dmaistsalle", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "takobin_jahannama", "storygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "fitata<PERSON>", "gameuse": "wasankwaikwayo", "offmortisghost": "kuskuremutuwarfatalwa", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "ƙarfafa", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "aventurasgraficas", "quickflash": "saurinwalƙiya", "fzero": "fzero", "gachagaming": "<PERSON><PERSON><PERSON>", "retroarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "jeji", "powerwashsim": "wasulishimulator", "coralisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "duniyardabam", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "kwallogargajiya", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "dan_sama", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "karafanmurɗe", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "tarin_kunya", "simulator": "fasahar_kwaikwayo", "symulatory": "symulatory", "speedrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "epicx": "epicx", "superrobottaisen": "superzakinmutummutumaisen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaiaonline": "gaiaonline", "korkuoyunu": "wasankunya", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "sauraiyargicciye", "toontownrewritten": "too<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "tseren_motoci_kwamfuta", "simrace": "tseren_motoci_na_kwamfuta", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON>", "seum": "seum", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_na_liyafa", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "gadonkain", "hackandslash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "oyunvideoları", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "direbanmotarbabahausa", "horizonworlds": "<PERSON>nga<PERSON><PERSON><PERSON>", "handygame": "<PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "tatsuni<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolvideogames": "wasan_bidiyo_na_zamanin_da", "racingsimulator": "wasan_tseren_mota", "beemov": "bee<PERSON>v", "agentsofmayhem": "masu_wakiltar_rikici", "songpop": "wa<PERSON><PERSON>i", "famitsu": "famitsu", "gatesofolympus": "ƙofofintaolympus", "monsterhunternow": "monsterhunternow", "rebelstar": "taurarontawaye", "indievideogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_masu_zaman_kansu", "indiegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bufffortress": "jagabanƙarfi", "unbeatable": "sauɗayabaaiyaka", "projectl": "projectl", "futureclubgames": "wasanninkungiyartasagaba", "mugman": "mugman", "insomniacgames": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "wasanceleste", "aperturescience": "aperturescience", "backlog": "ajizanwaya", "gamebacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "maidansakamuncinasara", "cityskylines": "<PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "kwallonbiri", "deponia": "deponia", "naughtydog": "<PERSON><PERSON><PERSON><PERSON>", "beastlord": "<PERSON><PERSON><PERSON><PERSON>", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "wasan_stanley", "reservatoriodedopamin": "ajiyardopamine", "staxel": "staxel", "videogameost": "ka<PERSON><PERSON><PERSON>a", "dragonsync": "<PERSON><PERSON><PERSON><PERSON>", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "inasokofxv", "arcanum": "sirri", "neoy2k": "neoy2k", "pcracing": "tseren_pc", "berserk": "dukkanin", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animemaibacinkaiyi", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "k<PERSON><PERSON><PERSON>", "animewithplot": "animedinjikadiri", "pesci": "pesci", "retroanime": "t<PERSON><PERSON><PERSON><PERSON>", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80s", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "<PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterpogi": "babbankyakyawa", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonekakareken1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "anime_labule", "thevisionofescaflowne": "hangen_escaflowne", "slayers": "<PERSON><PERSON><PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "kifin_a<PERSON>ba", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "armayanabanza", "fireforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "littafindubani", "fairytail": "tatsuniya", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "ankirmec<PERSON><PERSON><PERSON><PERSON>", "parasyte": "k<PERSON><PERSON><PERSON>_cututtuka", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "wakar_mermaid", "kamisamakiss": "<PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "littafinbantsororinmanga", "romancemangas": "littattafansoyyarromance", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "blacklagoon", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "<PERSON><PERSON><PERSON><PERSON>", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "<PERSON><PERSON><PERSON>", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "animendunyarsabo", "sagaoftanyatheevil": "labarincillacintanyamug<PERSON>", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tambaya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "yarondasaboda", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "<PERSON><PERSON><PERSON>", "blackbuttler": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "cikakkenwatanakenemawa", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "kololunyaƙi", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "yarinhiscore", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "ya<PERSON><PERSON><PERSON>o", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amici", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "t<PERSON><PERSON><PERSON>nan<PERSON>", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "finaf<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "s<PERSON><PERSON>_yaro_mai_kuka", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "baƙonbakinteku", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "tsar<PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermanga": "mangadodo", "yourlieinapril": "<PERSON><PERSON><PERSON>awatanafril<PERSON>", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "was<PERSON>_darwin", "husbu": "miji", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "zukatapandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "yaƙingwasuabinci", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "zamanbulu", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "ƙawancensirri", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "an<PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "blueexorcist", "slamdunk": "<PERSON><PERSON><PERSON><PERSON>", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "yinaduba", "spyfamily": "iyalinƴanboshi", "airgear": "airgear", "magicalgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "zunubibakarfaitakwas", "prisonschool": "makarantarkurkuku", "thegodofhighschool": "allahn<PERSON><PERSON><PERSON><PERSON><PERSON>", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "mutu<PERSON><PERSON><PERSON>_saa", "romancemanga": "littafinzuciyarjapan", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nkwaikway<PERSON>", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodlad": "bloodlad", "goodbyeeri": "barcieeri", "firepunch": "naushi", "adioseri": "kubarkingadaddaitaccokocin", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "kaya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceanime": "kwaikwayonsoyyarmangas", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "gidankino<PERSON>ni", "recordragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "makarantarmatattunkadai", "germantechno": "technondanjamus", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON><PERSON><PERSON><PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "mutu<PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animedinjapan", "animespace": "sarar<PERSON>nime", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "begenanuoli", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "wasan_gizo_kimiyya", "ratman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "haremanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "<PERSON><PERSON><PERSON>peach", "cavalieridellozodiaco": "masu_hawan_da<PERSON><PERSON>_zodiaci", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquestdai": "dragonquestdai", "heartofmanga": "<PERSON><PERSON>yarman<PERSON>", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "da<PERSON><PERSON>_yana_da_wuya_sosai", "overgeared": "cikakken_kayan_aiki", "toriko": "<PERSON><PERSON>o", "ravemaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kkondae": "tsohondangi", "chobits": "chobits", "witchhatatelier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangashinene", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "ji<PERSON><PERSON>", "bloodc": "<PERSON><PERSON><PERSON><PERSON>", "talesofdemonsandgods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "goreanime", "animegirls": "yan<PERSON><PERSON><PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatteranime": "animemaisoyaywa", "splatter": "<PERSON><PERSON><PERSON>", "risingoftheshieldhero": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "animesomalia", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animespain", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON>", "liarliar": "maƙaryaci", "supercampeones": "<PERSON><PERSON><PERSON><PERSON>", "animeidols": "animeidols", "isekaiwasmartphone": "nawajedunk<PERSON><PERSON><PERSON><PERSON>", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "kirankadare", "bakuganbrawler": "maidanbakugan", "bakuganbrawlers": "mabuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "ne<PERSON><PERSON><PERSON>", "princessjellyfish": "gimbiyarjellyfish", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekiss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "tauraronbita", "animeverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "persocoms": "kwamfutocindace", "omniscientreadersview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecat": "kyangege", "animerecommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openinganime": "b<PERSON><PERSON>me", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "kyaukyawanmasoyatadajarirai", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "ƙatattunmutummutumendʼanka", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "yaƙingundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "<PERSON><PERSON>a", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "tsabtace", "deathnote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "sojanimation", "greenranger": "greenranger", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "g<PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "maiyaƙarkuruka", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "harinkanatitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "kungiyardubawa", "onepieceanime": "onepieceanime", "attaquedestitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theonepieceisreal": "theonepieceyanagaskiya", "revengers": "<PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aonoexorcist": "aonoexorcist", "joyboyeffect": "tasirinjoyboyeffect", "digimonstory": "labarindijiman", "digimontamers": "digimontamers", "superjail": "babbangiɗanfursuna", "metalocalypse": "ƙarshenƙarfe", "shinchan": "shinchan", "watamote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flawlesswebtoon": "kusurshinwebtoon", "kemonofriends": "abo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "sabodak<PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "titi<PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "labarin_rayuwa"}