{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologi", "cognitivefunctions": "fungsikognitif", "psychology": "psikologi", "philosophy": "filos<PERSON><PERSON>", "history": "<PERSON><PERSON><PERSON>", "physics": "<PERSON><PERSON>a", "science": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "culture": "budaya", "languages": "basa", "technology": "teknologi", "memes": "meme", "mbtimemes": "mbtimemes", "astrologymemes": "memeastrologi", "enneagrammemes": "enneagrammemes", "showerthoughts": "mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "lucu", "videos": "video", "gadgets": "gadget", "politics": "politik", "relationshipadvice": "saransihubungan", "lifeadvice": "saran_urip", "crypto": "kripto", "news": "pawarta", "worldnews": "wartadunya", "archaeology": "arkeologi", "learning": "sinau", "debates": "debat", "conspiracytheories": "teorikonspirasi", "universe": "semesta", "meditation": "meditasi", "mythology": "mitologi", "art": "seni", "crafts": "kera<PERSON>an", "dance": "joget", "design": "desain", "makeup": "dandan", "beauty": "cantik", "fashion": "fashion", "singing": "nyanyi", "writing": "nulis", "photography": "potret", "cosplay": "cosplay", "painting": "lukisan", "drawing": "gambar", "books": "buku", "movies": "pilem", "poetry": "puisi", "television": "televisi", "filmmaking": "gawefilm", "animation": "animasi", "anime": "anime", "scifi": "scifi", "fantasy": "fantasi", "documentaries": "doku<PERSON><PERSON>", "mystery": "misteri", "comedy": "lucu", "crime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horor", "romance": "asmara", "realitytv": "tipitirealiti", "action": "aksi", "music": "musik", "blues": "sendu", "classical": "klasik", "country": "negara", "desi": "desi", "edm": "edm", "electronic": "elektronik", "folk": "rakyat", "funk": "funk", "hiphop": "hiphop", "house": "omah", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "tekno", "travel": "<PERSON><PERSON><PERSON>", "concerts": "konser", "festivals": "festival", "museums": "museum", "standup": "ngadeg", "theater": "tiater", "outdoors": "alampinggir", "gardening": "kebon", "partying": "pesta", "gaming": "dolanan", "boardgames": "papanpermainan", "dungeonsanddragons": "dungeonsanddragons", "chess": "<PERSON>ur", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "pangan", "baking": "masak_roti", "cooking": "masak", "vegetarian": "vegetarian", "vegan": "vegan", "birds": "manuk", "cats": "kucing", "dogs": "asu", "fish": "iwak", "animals": "kewan", "blacklivesmatter": "blacklivesmatter", "environmentalism": "lingkunganhidup", "feminism": "feminisme", "humanrights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "lgbtqally", "stopasianhate": "stopkebencinasiaane", "transally": "sekututrans", "volunteering": "<PERSON><PERSON><PERSON><PERSON>", "sports": "olahraga", "badminton": "bulutangkis", "baseball": "baseball", "basketball": "basket", "boxing": "tinju", "cricket": "kriket", "cycling": "se<PERSON><PERSON>", "fitness": "fitness", "football": "bal<PERSON>an", "golf": "golf", "gym": "gym", "gymnastics": "senam", "hockey": "hoki", "martialarts": "belad<PERSON>", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "m<PERSON><PERSON><PERSON><PERSON>", "skateboarding": "skateboarding", "skiing": "<PERSON><PERSON><PERSON><PERSON>", "snowboarding": "snowboarding", "surfing": "<PERSON><PERSON><PERSON>", "swimming": "renang", "tennis": "tenis", "volleyball": "voli", "weightlifting": "angkatbeban", "yoga": "yoga", "scubadiving": "nyelem", "hiking": "n<PERSON>i", "capricorn": "capricorn", "aquarius": "aquarius", "pisces": "pisces", "aries": "aries", "taurus": "taurus", "gemini": "gemini", "cancer": "kanker", "leo": "leo", "virgo": "virgo", "libra": "libra", "scorpio": "skor<PERSON>", "sagittarius": "sagittarius", "shortterm": "sakwentara", "casual": "santai", "longtermrelationship": "hubunganlawas", "single": "j<PERSON><PERSON>", "polyamory": "polia<PERSON><PERSON>", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "lesbian", "bisexual": "biseksual", "pansexual": "<PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kingsquest", "soulreaver": "jiwamaling", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "legend<PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "siberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON>an", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "halotanpawates", "guildwars": "guildwars", "openworld": "du<PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "kayaksouls", "dungeoncrawling": "njelajahdungeon", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "pemandanganpesawat", "lordsoftherealm2": "lordsoftherealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "warnabhore", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "simimmersive", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON>", "witcher": "witcher", "dishonored": "<PERSON><PERSON><PERSON>e", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "kreasikarakter", "immersive": "immersif", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyjadul", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivasiedan", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegames": "gametresnowadon", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "gaslands", "pathfinder": "njejak", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "shadowrun", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "sekalianjepret", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "juragan", "yourturntodie": "giliranmumatine", "persona3": "persona3", "rpghorror": "rpghoror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "penyerang", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "teksrpg", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "kebanjenganpeteng", "eclipsephase": "fase<PERSON><PERSON>s", "disgaea": "disgaea", "outerworlds": "duny<PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastywarriors", "skullgirls": "skullgirls", "nightcity": "kotawengi", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "dalan96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "jaga<PERSON><PERSON>", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "<PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "takhtaretak", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "deltaijo", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "nggebug", "lastepoch": "<PERSON><PERSON><PERSON>", "starfinder": "golekbintang", "goldensun": "srengengekuenceng", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "ped<PERSON>ingpeteng", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "fallenorder", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "bumijahat", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "<PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "ceritasymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "myfarog", "sacredunderworld": "jaga<PERSON><PERSON><PERSON><PERSON>", "chainedechoes": "rantaikumandang", "darksoul": "<PERSON><PERSON><PERSON><PERSON>", "soulslikes": "gamekayadarksouls", "othercide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "pila<PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "pecahan", "tibia": "tibia", "thedivision": "divisine", "hellocharlotte": "halocharlotte", "legendofdragoon": "legendofdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirpakaitopeng", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werewolfapocalypse", "aveyond": "aveyond", "littlewood": "alaskecil", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON>", "engineheart": "atimesin", "fable3": "fable3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edeneternal", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "starfield", "oldschoolrevival": "oldschool<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "dolananrpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "atiliar", "bastion": "<PERSON><PERSON>", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "langitarcadia", "shadowhearts": "<PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "napasgatragenipatang", "mother3": "ibu3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "atinemodong", "harrypottergame": "gem<PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "man<PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "buruanroyale", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "kratonwistiwi", "awplanet": "awplanet", "theworldendswithyou": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "bidahireng", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "gawejagad", "blackbook": "buku<PERSON>ng", "skychildrenoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "edisiemask<PERSON>inge", "castlecrashers": "bentenggerudug", "gothicgame": "gamegothic", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gamerpg", "prophunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starrails": "bintangrail", "cityofmist": "kotakabut", "indierpg": "rpgindie", "pointandclick": "titiklanklik", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "oratetegere", "freeside": "be<PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "pascasiberpunk", "deathroadtocanada": "dalansedocanada", "palladium": "paladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "monsterhunter", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremasi", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "souleaterjw", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "gameoranoroporo", "tacticalrpg": "rp<PERSON><PERSON><PERSON>", "mahoyo": "mahoyo", "animegames": "gamanime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "tukangmangangdewa", "diluc": "diluc", "venti": "venti", "eternalsonata": "sonatakekal", "princessconnect": "princessconnect", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "waskithakantong", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valo<PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligadreamers", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gaimin", "overwatchleague": "overwatchleague", "cybersport": "olahragasiber", "crazyraccoon": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "erasing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantkompetitif", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "setengumuri<PERSON>n", "left4dead": "matid<PERSON><PERSON>", "left4dead2": "left4dead2", "valve": "klep", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "musimpanaslanggeng", "goatsimulator": "simulatorkambing", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planetkebebasan", "transformice": "transformice", "justshapesandbeats": "cумаformalanirама", "battlefield4": "battlefield4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvania", "overcooked": "matengkelang<PERSON><PERSON>", "interplanetary": "antarplanet", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7h2h", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "ngumpetwewelu", "stray": "m<PERSON><PERSON><PERSON><PERSON>", "battlefield": "medanperang", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "matae", "blackdesert": "black<PERSON>ert", "tabletopsimulator": "simulatormejagame", "partyhard": "pesthagede", "hardspaceshipbreaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hades": "hades", "gunsmith": "tukang<PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinkum": "temenanbos", "predecessor": "<PERSON><PERSON><PERSON>", "rainworld": "jaga<PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "subuhperang", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "petengndekpeteng", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "b<PERSON><PERSON><PERSON><PERSON>", "datingsims": "sim<PERSON><PERSON>", "yaga": "yaga", "cubeescape": "cubelolos", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "kota<PERSON><PERSON>", "citiesskylines": "kuthakuthawolangit", "defconheavy": "defconheavy", "kenopsia": "kenopsia", "virtualkenopsia": "virtualkenopsia", "snowrunner": "snowrunner", "libraryofruina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenajemba<PERSON>roh", "placidplasticduck": "bebeksulungtenang", "battlebit": "perangbit", "ultimatechickenhorse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dialtown": "dialtown", "smileforme": "nggumusake", "catnight": "we<PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermeatboy", "tinnybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygrove": "cozygrove", "doom": "kiamat", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "apeks", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "paladins", "earthdefenseforce": "pasuka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "huntshowdown", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "joinsquad": "gabungsquad", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "pem<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "mirrorsedge", "divisions2": "divisidivisi2", "killzone": "zonanipateni", "helghan": "hel<PERSON>", "coldwarzombies": "zombie<PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "perangmodern", "neonabyss": "jurangneon", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "boarderlands", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "kekejamenprimitif", "worldofwarships": "worldofwarships", "back4blood": "b<PERSON><PERSON><PERSON>", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "tukang<PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "sistemshock", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "ceri<PERSON><PERSON><PERSON>", "doometernal": "doometernal", "centuryageofashes": "abadkang_dadi_awu", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "generasinol", "enterthegungeon": "entermlebugungeon", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "wongsosis", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "warface", "crossfire": "crossfire", "atomicheart": "atomicheart", "blackops3": "blackops3", "vampiresurvivors": "<PERSON>irs<PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "mardika", "battlegrounds": "medanperang", "frag": "frag", "tinytina": "tinake<PERSON>l", "gamepubg": "gamepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "juegosfps", "convertstrike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warzone2": "warzone2", "shatterline": "garisretak", "blackopszombies": "zombiblackops", "bloodymess": "b<PERSON>rah<PERSON>be<PERSON>", "republiccommando": "republiccommando", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "cabangtanah", "squad": "skuad", "destiny1": "takdir1", "gamingfps": "gamingfps", "redfall": "redfall", "pubggirl": "pubggirl", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "mlebu_tentara", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "gajian2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON>", "ghostcod": "ghostcod", "csplay": "csplay", "unrealtournament": "unrealtournament", "callofdutydmz": "callofdutydmz", "gamingcodm": "dolanancodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "<PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "<PERSON><PERSON><PERSON><PERSON>", "remnant": "remnant", "azurelane": "azurelane", "worldofwar": "donyaperang", "gunvolt": "gunvolt", "returnal": "balimaning", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "wongbayangan", "quake2": "gempa2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "mat<PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "seaofthieves", "rust": "karat", "conqueronline": "takluknaonline", "dauntless": "or<PERSON>i", "warships": "kapalperang", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "flightrising", "recroom": "r<PERSON><PERSON><PERSON>", "legendsofruneterra": "legendsofruneterra", "pso2": "pso2", "myster": "misteri", "phantasystaronline2": "phantasystaronline2", "maidenless": "r<PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "jagadtank", "crossout": "coretkeluar", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON>dh<PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "<PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "sampah", "newworld": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "ma<PERSON><PERSON><PERSON>", "pirate101": "pirate101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "obrolan3d", "nostale": "nostale", "tauriwow": "tauri<PERSON><PERSON>w", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "silkroad", "spiralknights": "spiralknights", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "nabilaga", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multijugador", "angelsonline": "malaekatolonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsrepublikjamanbiyen", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "blueprotocol", "perfectworld": "dunyanampurna", "riseonline": "bangkitonline", "corepunk": "corepunk", "adventurequestworlds": "adventurequestworlds", "flyforfun": "mburkel<PERSON>", "animaljam": "animaljam", "kingdomofloathing": "kerajaangantung", "cityofheroes": "kotapahlawan", "mortalkombat": "mortalkombat", "streetfighter": "streetfighter", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "kangkehormatan", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mkdeadlyalliance", "nomoreheroes": "or<PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "koyonogo", "retrofightinggames": "gamelaw<PERSON><PERSON><PERSON>l", "blasphemous": "n<PERSON><PERSON><PERSON><PERSON>", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "robotsiber", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poweredgear": "perlengkapanbertenaga", "beatemup": "kalahna", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "gemfighting", "killerinstinct": "na<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "mlayu_setan", "chivalry2": "chivalry2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "sekuelholowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "gamesilksong", "silksongnews": "kabarsilksong", "silksong": "silksong", "undernight": "weng<PERSON>reng", "typelumina": "ketik<PERSON>ina", "evolutiontournament": "evolusiturnamen", "evomoment": "evomonemene", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "bloodborne", "horizon": "cakrawala", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodbourne", "uncharted": "durungdijelajah", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "kondhang", "playstationbuddies": "kancaplaystation", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "dewa<PERSON><PERSON>", "gris": "gris", "trove": "<PERSON><PERSON><PERSON>", "detroitbecomehuman": "detroitdadimungsa", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "ngantitekesuk", "touristtrophy": "pialaturis", "lspdfr": "lspdfr", "shadowofthecolossus": "bayanga<PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "crashteamracing", "fivepd": "limapd", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "maenplaystation", "samuraiwarriors": "p<PERSON><PERSON><PERSON><PERSON>urai", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "jiwapdang", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "burulanang", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "senengdugem", "warharmmer40k": "warhammer40k", "fightnightchampion": "juaramalem<PERSON><PERSON>", "psychonauts": "psikonauts", "mhw": "mhw", "princeofpersia": "pangeranpersia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "medan_perang", "dontstarvetogether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "starbound", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "liganegara", "fable2": "dongeng2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "tvsampahe", "skycotl": "langitcotl", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "cilakawadulcilik", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "<PERSON>m<PERSON><PERSON>", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "poto", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kultuswedhuscilik", "duckgame": "gameduck", "thestanleyparable": "thestanleyparable", "towerunite": "towerbersatu", "occulto": "occulto", "longdrive": "numpakadoh", "satisfactory": "cukup", "pluviophile": "tres<PERSON><PERSON><PERSON>", "underearth": "n<PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "kubahireng", "pizzatower": "pizzatower", "indiegame": "gameindependen", "itchio": "itchio", "golfit": "golfit", "truthordare": "keju<PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "dolanan", "rockpaperscissors": "suitjepang", "trampoline": "trampolin", "hulahoop": "<PERSON><PERSON><PERSON><PERSON>", "dare": "wani", "scavengerhunt": "goletgoletan", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "pilihangka", "trueorfalse": "benerapamosok", "beerpong": "beerpong", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON>", "cosygames": "gamekalem", "datinggames": "<PERSON><PERSON>n<PERSON><PERSON><PERSON>", "freegame": "gratisan", "drinkinggames": "dolanann<PERSON>mbe", "sodoku": "sodoku", "juegos": "dolanan", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "gamedolanan", "wordgames": "tembungdolanan", "jeuxdemots": "jeuxdemots", "juegosdepalabras": "dolanantegesantembung", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "dolana<PERSON><PERSON><PERSON><PERSON>", "oyun": "oyun", "interactivegames": "dolananinteraktif", "amtgard": "amtgard", "staringcontests": "lombamloto", "spiele": "spiele", "giochi": "giochi", "geoguessr": "geoguessr", "iphonegames": "gameponeiphone", "boogames": "boogames", "cranegame": "dholen", "hideandseek": "umbulumbulanan", "hopscotch": "hopscotch", "arcadegames": "gemarcade", "yakuzagames": "gameyakuza", "classicgame": "gametradisional", "mindgames": "dolana<PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "tugeltembang", "galagames": "galagames", "romancegame": "gamelove", "yanderegames": "gameyandere", "tonguetwisters": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "4xgames": "4xgames", "gamefi": "gamefi", "jeuxdarcades": "jeuxdarcades", "tabletopgames": "dolananmeja", "metroidvania": "metroidvania", "games90": "game90", "idareyou": "akantantang", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "balaplomba", "ets2": "ets2", "realvsfake": "asliv<PERSON><PERSON>une", "playgames": "<PERSON><PERSON><PERSON><PERSON>", "gameonline": "gameonline", "onlinegames": "gameonline", "jogosonline": "jogosonline", "writtenroleplay": "roleplay<PERSON><PERSON>", "playaballgame": "<PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "game<PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "<PERSON><PERSON><PERSON><PERSON>", "highscore": "skor<PERSON><PERSON>", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "burgergames", "kidsgames": "dolanan<PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "gioco", "managementgame": "gamedolanan", "hiddenobjectgame": "gamecaribarangkesumput", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "gamedugf1", "citybuilder": "kotabuilder", "drdriving": "drdriving", "juegosarcade": "dolananarkade", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "g<PERSON><PERSON>i", "blowgames": "dolangames", "pinballmachines": "mesinpinball", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "dolanan", "lasergame": "lasergame", "imessagegames": "gameimessage", "idlegames": "gamenengneng", "fillintheblank": "isilowonge", "jeuxpc": "jeuxpc", "rétrogaming": "rétrogaming", "logicgames": "gamelogika", "japangame": "dolanjepang", "rizzupgame": "rizzupgame", "subwaysurf": "<PERSON><PERSON><PERSON><PERSON>", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5vs5", "rolgame": "<PERSON><PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "dolanantradisional", "kniffel": "kniffel", "gamefps": "gamefps", "textbasedgames": "gamedolanteks", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospel", "thiefgame": "gametukumaling", "lawngames": "dolananlapangan", "fliperama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heroclix": "heroclix", "tablesoccer": "bola<PERSON><PERSON><PERSON>a", "tischfußball": "balangsepak", "spieleabende": "spie<PERSON><PERSON>de", "jeuxforum": "jeuxforum", "casualgames": "gamesenteng", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "<PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "serigamemaling", "cranegames": "dolancapit", "játék": "dolanan", "bordfodbold": "bordfodbold", "jogosorte": "jogosorte", "mage": "mage", "cargames": "gamedolananmobil", "onlineplay": "dolanoning", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "malemgame", "pursebingos": "<PERSON><PERSON><PERSON><PERSON>", "randomizer": "acakacakan", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "gamekomputer", "socialdeductiongames": "gamedugadugaansosial", "dominos": "dominos", "domino": "domino", "isometricgames": "gameisometrik", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "golekgolekan", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "gamerf2p", "free2play": "gratismain", "fantasygame": "<PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "gamedrift", "gamesotomes": "gameotome", "halotvseriesandgames": "halotvserieslangames", "mushroomoasis": "oasisjamur", "anythingwithanengine": "apawesingduwe<PERSON>in", "everywheregame": "dolananngendibae", "swordandsorcery": "pedang<PERSON><PERSON><PERSON>", "goodgamegiving": "apikbangetma<PERSON>", "jugamos": "j<PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "grykomputerowe", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "gamecilik", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "tresnoawakdhewegolek", "gamemodding": "modgame", "crimegames": "<PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "dolankartu", "spelletjes": "dolanan", "spacenerf": "spacenerf", "charades": "<PERSON>g<PERSON><PERSON>n", "singleplayer": "singleplayer", "coopgame": "gambareng", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "sambungan", "geforcenow": "geforcenow", "maingame": "<PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "rajadiscord", "scrabble": "scrabble", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "warisanpandemi", "camelup": "untakmunggah", "monopolygame": "dolananmonop<PERSON>", "brettspiele": "brettspiele", "bordspellen": "b<PERSON><PERSON><PERSON><PERSON>", "boardgame": "dolanbordgame", "sällskapspel": "<PERSON><PERSON><PERSON><PERSON>", "planszowe": "planszowe", "risiko": "risiko", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombicide", "tabletop": "mejane", "baduk": "baduk", "bloodbowl": "bloodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "dolgobordgem", "connectfour": "sambungpapat", "heroquest": "heroquest", "giochidatavolo": "dolanbordgame", "farkle": "farkle", "carrom": "karambol", "tablegames": "dolananmeja", "dicegames": "dadukobong", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "dolananmeja", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "temukosmik", "creationludique": "<PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "roleplaymejatop", "cardboardgames": "gameskardus", "eldritchhorror": "horo<PERSON><PERSON><PERSON>", "switchboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "infinitythegame": "dolana<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "<PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON>", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "juegodemesa", "planszówki": "planszów<PERSON>", "rednecklife": "<PERSON><PERSON><PERSON><PERSON>", "boardom": "bosen", "applestoapples": "apelkaaroapel", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "papandolan", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "jeuxdesociétés", "twilightimperium": "twilightimperium", "horseopoly": "kudapoli", "deckbuilding": "mbangunsetdeck", "mansionsofmadness": "omahgedhengsinting", "gomoku": "gomoku", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "bayanganbrimstone", "kingoftokyo": "ratutokyo", "warcaby": "warcaby", "táblajátékok": "dolanbordgame", "battleship": "kapalperang", "tickettoride": "tiketmlebu", "deskovehry": "deskovehry", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "j<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "dolanbordgame", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "<PERSON>lan<PERSON><PERSON>eng<PERSON>", "gesellschaftsspiele": "gesellschaftsspiele", "starwarslegion": "starwarslegion", "gochess": "ayo<PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identityv", "theisle": "theisle", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "panggilancthulhu", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "amongus", "eco": "eko", "monkeyisland": "pulokethek", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "cekrek", "pathologic": "patologis", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "weng<PERSON>etengsuwe", "ark": "ark", "grounded": "<PERSON><PERSON><PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "bapakgilasinting", "dontstarve": "orate<PERSON><PERSON><PERSON>", "eternalreturn": "balikmaning", "pathoftitans": "jalanebareksasa", "frictionalgames": "frictionalgames", "hexen": "<PERSON><PERSON><PERSON>", "theevilwithin": "kejahataningdalem", "realrac": "realrac", "thebackrooms": "kamarmburi", "backrooms": "ruangmburi", "empiressmp": "empiressmp", "blockstory": "ceritablok", "thequarry": "tambang", "tlou": "tlou", "dyinglight": "matilampune", "thewalkingdeadgame": "gamedhedhemlatine", "wehappyfew": "kitaikisithok", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "gamesurvivalnegara", "vintagestory": "ceri<PERSON><PERSON><PERSON><PERSON>", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "ambegandege", "alisa": "alisa", "westlendsurvival": "<PERSON><PERSON><PERSON><PERSON>", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "alasireng", "survivalhorror": "horo<PERSON><PERSON><PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "sepur_kosong", "lifeaftergame": "uripsakbuwargame", "survivalgames": "<PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "perangkuiki", "scpfoundation": "yayasanscp", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "nangisketakutan", "raft": "rakit", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "mat<PERSON><PERSON>", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "simbah", "littlenightmares2": "mimpiburukcilikloro2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "rustvideogame", "outlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "<PERSON><PERSON><PERSON><PERSON>", "undawn": "<PERSON><PERSON><PERSON><PERSON>", "7day2die": "7harimpatirasane", "sunlesssea": "segor<PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "sopravvivenza", "propnight": "malemprop", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "vampirgantengjv", "deathverse": "<PERSON><PERSON><PERSON>", "cataclysmdarkdays": "bancibencanagelap<PERSON>eng", "soma": "soma", "fearandhunger": "wedilanluwe", "stalkercieńczarnobyla": "stalkerbayangenechernobyl", "lifeafter": "uripsakwise", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON>", "clocktower3": "menaragedong3", "aloneinthedark": "deweaning<PERSON><PERSON>", "medievaldynasty": "dinastiabadpertengahan", "projectnimbusgame": "projectnimbusgame", "eternights": "<PERSON><PERSON><PERSON><PERSON>", "craftopia": "craftopia", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "dominasijagad", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "tukangpatènicebol", "warhammer40kcrush": "warhammer40kcrush", "wh40": "wh40", "warhammer40klove": "warhammer40kseneng", "warhammer40klore": "lorekampeni40k", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "akutresnasororitas", "ilovevindicare": "akutresnovindicare", "iloveassasinorum": "akutresnoassasinorum", "templovenenum": "katresnanmusuhsementara", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "lebarsayap", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "pahl<PERSON><PERSON><PERSON><PERSON>lanma<PERSON>", "btd6": "btd6", "supremecommander": "panglimatinggi", "ageofmythology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "di<PERSON><PERSON>", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "peradaban6", "warcraft2": "warcraft2", "commandandconquer": "mrentahlanmenaklukake", "warcraft3": "warcraft3", "eternalwar": "peranglanggeng", "strategygames": "strategigame", "anno2070": "anno2070", "civilizationgame": "gamengaradaban", "civilization4": "peradaban4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spora", "totalwar": "perangtotal", "travian": "travian", "forts": "b<PERSON><PERSON><PERSON><PERSON>", "goodcompany": "kancaapik", "civ": "civ", "homeworld": "jaga<PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "lu<PERSON><PERSON><PERSON>ettinimbangcahya", "forthekings": "kangpararaja", "realtimestrategy": "strategitempurnyanik<PERSON>", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eu4": "eu4", "vainglory": "umuk", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON><PERSON>", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "topitelu", "davesfunalgebraclass": "kelasmatematikaasikenyedave", "plagueinc": "wabahpenyakit", "theorycraft": "teorijuragan", "mesbg": "mesbg", "civilization3": "sradab3", "4inarow": "4urutan", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "<PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "ageofempires2", "disciples2": "murid2", "plantsvszombies": "tanduranvszombie", "giochidistrategia": "dolananstrategi", "stratejioyunları": "stratejioyunları", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON>", "worldconquest": "penaklukanjagat", "heartsofiron4": "heartsofiron4", "companyofheroes": "kancanepahlawan", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "bebekbebekangsa", "phobies": "<PERSON><PERSON><PERSON><PERSON>", "phobiesgame": "gamefobi", "gamingclashroyale": "gaminclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "g<PERSON><PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "crusaderkings", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "perangkekaisaranstarwars", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategi", "popfulmail": "popfulmail", "shiningforce": "balatentaragebyar", "masterduel": "masterduel", "dysonsphereprogram": "programspheredyson", "transporttycoon": "rajatransport", "unrailed": "rake<PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "kratondhuwur", "galaxylife": "uripgalaxy", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "kalahkanmenara", "battlecats": "kucingperang", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "ngebutka<PERSON>petan", "needforspeedcarbon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>kar<PERSON>", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "matinengawan", "alicemadnessreturns": "alicemad<PERSON>ret<PERSON>s", "darkhorseanthology": "antologi<PERSON><PERSON><PERSON><PERSON>", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "limanginatfreddys", "saiko": "saiko", "fatalframe": "fatalframe", "littlenightmares": "mimpikakangseramcilik", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "omahe", "deadisland": "pu<PERSON><PERSON>i", "litlemissfortune": "c<PERSON><PERSON><PERSON>", "projectzero": "proyekzero", "horory": "horor", "jogosterror": "jogosterror", "helloneighbor": "halokonco", "helloneighbor2": "halotonggoku2", "gamingdbd": "gamingdbd", "thecatlady": "bud<PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "j<PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "horrorgaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kertumelawanmanungsa", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "jenengkode", "dixit": "dixit", "bicyclecards": "kertubal", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legendofrun<PERSON>a", "solitaire": "solitaire", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON>", "playingcards": "kartumainan", "marvelsnap": "marvelsnap", "ginrummy": "gin<PERSON>i", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "ketutrading", "pokemoncards": "kertupokemon", "fleshandbloodtcg": "tcgdaginglangetih", "sportscards": "kertusport", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "pik", "warcry": "unjukanperang", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "ratungatiku", "truco": "truco", "loteria": "lotre", "hanafuda": "hana<PERSON>da", "theresistance": "<PERSON><PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohcards", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohgame", "darkmagician": "<PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "matobirunagaruntukputih", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "remi", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobel", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "semangatjuang", "battlespiritssaga": "semangatperangsaga", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "žolíky": "kartujoker", "facecard": "rupaayu", "cardfight": "<PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "tukangdeck", "marvelchampions": "juaramarvel", "magiccartas": "karta<PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "unicornracakracik", "cyberse": "cyberse", "classicarcadegames": "gamearcadejadul", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "malemkemisfunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "gita<PERSON><PERSON>o", "clonehero": "clonehero", "justdance": "jog<PERSON>e", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "goyangmayit", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "joget<PERSON><PERSON>", "rhythmgamer": "gameritme", "stepmania": "stepmania", "highscorerythmgames": "gameiramatinggi", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "tarijogenigadinge", "auditiononline": "audisionline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "dokterirama", "cubing": "kubiking", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "gamepuzzle", "spotit": "nemu", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "tekatekinalarpikir", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "tekateki", "rubikscube": "rubikscube", "crossword": "tekateka<PERSON><PERSON>", "motscroisés": "motscroisés", "krzyżówki": "tekatekan", "nonogram": "nonogram", "bookworm": "kut<PERSON><PERSON>", "jigsawpuzzles": "puzzlejigsaw", "indovinello": "tekateki", "riddle": "tekateki", "riddles": "tekateki", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "njero", "angrybirds": "<PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "escapesimulator", "minesweeper": "dhedhor", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "tekatekatekisilang", "kurushi": "k<PERSON>hi", "gardenscapesgame": "<PERSON><PERSON><PERSON><PERSON>", "puzzlesport": "olahragapuzzle", "escaperoomgames": "escaperoomgames", "escapegame": "dolanan<PERSON><PERSON><PERSON>", "3dpuzzle": "puzzel3d", "homescapesgame": "gamehomescapes", "wordsearch": "golettembung", "enigmistica": "enigmistica", "kulaworld": "jaga<PERSON><PERSON><PERSON>", "myst": "myst", "riddletales": "ceritatekatekan", "fishdom": "fishdom", "theimpossiblequiz": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "cocokentelu", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "unik", "rubikcube": "rubikcube", "cuborubik": "kubustrubik", "yapboz": "yapboz", "thetalosprinciple": "prinsi<PERSON><PERSON><PERSON>", "homescapes": "<PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tycoongames": "gamekonglomerat", "cubosderubik": "cubosderubik", "cruciverba": "cruciver<PERSON>", "ciphers": "kode_rahasia", "rätselwörter": "tembungtekatekan", "buscaminas": "buscaminas", "puzzlesolving": "mecahkantekatekan", "turnipboy": "bocahwortel", "adivinanzashot": "tebaktebakanshot", "nobodies": "dudusingopo", "guessing": "tebakan", "nonograms": "nonogram", "kostkirubika": "kostkirubika", "crypticcrosswords": "tekatek<PERSON><PERSON><PERSON><PERSON>", "syberia2": "syberia2", "puzzlehunt": "golekgolekpuzzle", "puzzlehunts": "golekpuzzle", "catcrime": "kejahatank<PERSON>ing", "quebracabeça": "tekateki", "hlavolamy": "tekateki", "poptropica": "poptropica", "thelastcampfire": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "autodefinidos", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "gameangsamoratitel", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubiks", "maze": "mbingungi", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "<PERSON><PERSON><PERSON>", "portalgame": "portalgame", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "twistedwonderland", "monopoly": "monopoli", "futurefight": "gelutmasadepan", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "<PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemystars", "stateofsurvival": "stateofsurvival", "mycity": "kotaku", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "<PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "balbalanbola", "a3": "a3", "phonegames": "dolananhp", "kingschoice": "p<PERSON><PERSON><PERSON><PERSON>", "guardiantales": "cerito<PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "tukangmotor", "tacticool": "taktikuler", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "craftsman": "tukang", "supersus": "supersus", "slowdrive": "nyliralanapik", "headsup": "waspodo", "wordfeud": "wordfeud", "bedwars": "bedwars", "freefire": "freefire", "mobilegaming": "dolananmobile", "lilysgarden": "lilysgarden", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "kantordarurat", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "wektuniprincess", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "ka<PERSON><PERSON><PERSON><PERSON>", "androidgames": "gameandroid", "criminalcase": "kasuskriminal", "summonerswar": "summonerswar", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "trivianjebret", "leagueofangels": "liganepararekso", "lordsmobile": "lordsmobile", "tinybirdgarden": "kebunsikcilik", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "monstermonsterpadang", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "warrobots", "mirrorverse": "jagadbayangan", "pou": "pou", "warwings": "warwings", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "senengseneng", "antiyoy": "antiarek", "apexlegendmobile": "apexlegendmobile", "ingress": "mlebu", "slugitout": "<PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "kanc<PERSON><PERSON>", "gameofsultans": "gameofsultans", "arenabreakout": "arenabreakout", "wolfy": "wolfy", "runcitygame": "runcitygame", "juegodemovil": "juegodemovil", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "niru", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ldoe": "ldoe", "legendonline": "legendonline", "otomegame": "gameotome", "mindustry": "mindustry", "callofdragons": "panggilanlaga", "shiningnikki": "nikkingeglitere", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "tembungkarokancak2", "soulknight": "soulknight", "purrfecttale": "c<PERSON><PERSON><PERSON><PERSON>", "showbyrock": "showbyrock", "ladypopular": "cewekpopuler", "lolmobile": "ngguyu_mobile", "harvesttown": "desanenpan<PERSON>", "perfectworldmobile": "perfectworldmobile", "empiresandpuzzles": "kerajaanlankekathik", "empirespuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileindonesia", "fanny": "bokong", "littlenightmare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "airmatanemis", "eversoul": "<PERSON><PERSON><PERSON><PERSON>", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiekesasar", "eveechoes": "eveechoes", "jogocelular": "dolananhp", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "streetfighterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "girlsfrontline", "jurassicworldalive": "jurassicworldalive", "soulseeker": "go<PERSON><PERSON>doh", "gettingoverit": "uwismoveon", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "c<PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "dolananmobile", "legendofneverland": "legendneverland", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "penyerbuwektu", "gamingmobile": "gamingponsel", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "kucingperang", "dnd": "jkm", "quest": "kuest", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgmeja", "worldofdarkness": "jagad<PERSON><PERSON><PERSON>", "travellerttrpg": "travellerttrpg", "2300ad": "2300m", "larp": "larp", "romanceclub": "klubromansa", "d20": "d20", "pokemongames": "gamepokemon", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonungu", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "tim<PERSON>t", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "monsterkanthong", "nuzlocke": "nuzlocke", "pokemonplush": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "<PERSON><PERSON><PERSON><PERSON>", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "pokemonturu", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "pemburu_mengilap", "ajedrez": "<PERSON>ur", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "sekak", "ajedres": "aje<PERSON>s", "chessgirls": "cewekcatur", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "blitzdunyo", "jeudéchecs": "jeudéchecs", "japanesechess": "cat<PERSON>jepang", "chinesechess": "<PERSON><PERSON><PERSON>a", "chesscanada": "cat<PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "bukaan", "rook": "ruk", "chesscom": "chesscom", "calabozosydragones": "calabozosydragones", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "dungeonmaster", "tiamat": "tiamat", "donjonsetdragons": "donjonlandragun", "oxventure": "oxventure", "darksun": "srengengekelam", "thelegendofvoxmachina": "legendanevoxmachina", "doungenoanddragons": "dungeonanddragons", "darkmoor": "alunalunan", "minecraftchampionship": "kejuaraanminecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modminecraft", "mcc": "mcc", "candleflame": "geniblarak", "fru": "<PERSON><PERSON><PERSON><PERSON>", "addons": "tambahan", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmodifikasi", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "antaranegara", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "kotaminecraft", "pcgamer": "gamerpeceh", "jeuxvideo": "dolananvideo", "gambit": "gambit", "gamers": "gamer", "levelup": "munggahlevel", "gamermobile": "gamerhp", "gameover": "tamat", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "gamekomputer", "casualgaming": "gamingsantai", "gamingsetup": "setupgaming", "pcmasterrace": "pcmasterrace", "pcgame": "gamekomputer", "gamerboy": "gamercowok", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "gaming4k", "gamerbr": "gamerbr", "gameplays": "dolanan", "consoleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "gamerepik", "onlinegaming": "dolan<PERSON>н<PERSON><PERSON><PERSON>н", "semigamer": "gamermung", "gamergirls": "gamercewek", "gamermoms": "ibugamer", "gamerguy": "cokgamer", "gamewatcher": "nontonngame", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerschicas", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "tim<PERSON><PERSON><PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "quest", "alax": "alax", "avgn": "avgn", "oldgamer": "gamerlawas", "cozygaming": "gamingsantuy", "gamelpay": "gamelpay", "juegosdepc": "juegosdepc", "dsswitch": "gantids", "competitivegaming": "main<PERSON><PERSON><PERSON>", "minecraftnewjersey": "minecraftnewjersey", "faker": "palsu", "pc4gamers": "pc4gamer", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "gaming<PERSON><PERSON>se<PERSON><PERSON>", "gamepc": "gamepc", "girlsgamer": "cewekgamer", "fnfmods": "modfnf", "dailyquest": "pitakonpadinten", "gamegirl": "gamecewek", "chicasgamer": "cewekgamer", "gamesetup": "<PERSON><PERSON><PERSON><PERSON>", "overpowered": "kesuwen", "socialgamer": "gamersosial", "gamejam": "gamejam", "proplayer": "propemain", "roleplayer": "roleplayer", "myteam": "timku", "republicofgamers": "republikgamer", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "legendate<PERSON>", "gamerbuddies": "ka<PERSON><PERSON><PERSON>", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "gamerkristenl", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "gamersantai", "89squad": "89squad", "inicaramainnyagimana": "cakngonogimanacaramaine", "insec": "insek", "gemers": "gemers", "oyunizlemek": "oyunizlemek", "gamertag": "gamertag", "lanparty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamer": "gamervideo", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "gamertakmainplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gamersehati", "gtracing": "gtracing", "notebookgamer": "gamercatetan", "protogen": "protogen", "womangamer": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "jlasgamertemenanan", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "mb<PERSON><PERSON>", "humanfallflat": "manungsatibajembles", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "lolosbuktinggal", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusic", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "fallguys", "switch": "ganti", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "anakangkasaterangpedhang", "tomodachilife": "uripkancane", "ahatintime": "wektusiji", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "simulator<PERSON><PERSON><PERSON>", "nintendogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "panenrembulan", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "ambegansaka", "myfriendpedro": "kancak<PERSON>dr<PERSON>", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "<PERSON><PERSON><PERSON><PERSON>", "earthbound": "donya", "tales": "crita", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "strategisegitelu", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "3<PERSON><PERSON><PERSON>", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "mario<PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "canidabang", "vanillalol": "vanillang<PERSON>yu", "wildriftph": "wildriftph", "lolph": "ngguyu", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspanyol", "aatrox": "aatrox", "euw": "iiih", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "yuta", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "maingamelol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "game<PERSON><PERSON><PERSON>", "scaryvideogames": "videogamenyeramkan", "videogamemaker": "jvtukangbikinvideogame", "megamanzero": "megamanzero", "videogame": "<PERSON><PERSON><PERSON><PERSON>", "videosgame": "videogame", "professorlayton": "profe<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "dolanan", "acnh": "acnh", "puffpals": "kancapompom", "farmingsimulator": "simulatortetanen", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "gamepasir", "videogamelore": "lorévideogame", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "impenankabut", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "<PERSON><PERSON><PERSON><PERSON>", "amordoce": "t<PERSON><PERSON><PERSON><PERSON>", "videogiochi": "videogame", "theoldrepublic": "republikakuno", "videospiele": "dolananvideo", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "gamepetualangan", "wolfenstein": "wolfenstein", "actionadventure": "aks<PERSON>etualangan", "storyofseasons": "ceritanemus<PERSON>", "retrogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "arkadejamanbiyen", "vintagecomputing": "komputerjadul", "retrogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagegaming": "gamingjadul", "playdate": "dolanan", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "ketidakadilan2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "do<PERSON><PERSON>wang", "zenlife": "uriptenang", "beatmaniaiidx": "beatmaniaiidx", "steep": "miring", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "blockchaingaming", "medievil": "medievil", "consolegaming": "dolankonsolgame", "konsolen": "konsolen", "outrun": "ngungkuli", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "horrorgaming", "monstergirlquest": "takonwedokmemedi", "supergiant": "gedhebangit", "disneydreamlightvalle": "disneydre<PERSON><PERSON><PERSON><PERSON>", "farmingsims": "simulas<PERSON>ni", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "fiksinteraktif", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantesamentes", "visualnovel": "novelgambar", "visualnovels": "novelvisual", "rgg": "rgg", "shadowolf": "serigalabayang", "tcrghost": "tcrghost", "payday": "g<PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "putrise<PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandboxan", "aestheticgames": "aestheticgames", "novelavisual": "novelvisual", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "gemejamanbiyen", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "tan<PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "revolus<PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "leveldesign", "starrail": "starrail", "keyblade": "keyblade", "aplaguetale": "aplaguetale", "fnafsometimes": "fnafsometimes", "novelasvisuales": "novelvisual", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "game<PERSON><PERSON><PERSON>", "videojuejos": "videoju<PERSON><PERSON>", "videogamedates": "dolananvideogame", "mycandylove": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "meksagelem3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "dinojagungk<PERSON>wi<PERSON>", "maniacmansion": "omahgilak", "crashracing": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "platformer3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "ped<PERSON><PERSON><PERSON>", "storygames": "do<PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "ngegame", "offmortisghost": "offmortisghost", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "powerup", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "lerengaring", "powerwashsim": "simu<PERSON><PERSON><PERSON><PERSON>", "coralisland": "pulokoral", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "jagadliya", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "fusibal", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "logammlintir", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "tumpukanwirang", "simulator": "simulator", "symulatory": "simulatoran", "speedrunner": "speedrunner", "epicx": "epikx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "p<PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simrace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "awa<PERSON><PERSON>", "seum": "seum", "partyvideogames": "videogamepesta", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "simulatorpenerbanganlantariksa", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "tekeslantekes", "foodandvideogames": "pangananlanvideogame", "oyunvideoları": "oyunvideoları", "thewolfamongus": "serigalananengahtengahawakdhewe", "truckingsimulator": "simulatortrek", "horizonworlds": "horizonworlds", "handygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "legendalanvideogame", "oldschoolvideogames": "videogamejamandulu", "racingsimulator": "simulatorracing", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "gerbangolympus", "monsterhunternow": "monsterhunternow", "rebelstar": "lint<PERSON><PERSON><PERSON><PERSON>", "indievideogaming": "gameindiepopuler", "indiegaming": "maingameindie", "indievideogames": "gameindiejowo", "indievideogame": "gameindiejawa", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "bent<PERSON>rak<PERSON><PERSON>", "unbeatable": "oraterlawan", "projectl": "projectl", "futureclubgames": "dolananklubmbesuk", "mugman": "mugman", "insomniacgames": "gamertanggakmarem", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "t<PERSON><PERSON>n", "gamebacklog": "tunggakangame", "gamingbacklog": "t<PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "karaktergamevideo", "achievementhunter": "prestasihunter", "cityskylines": "lang<PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "<PERSON><PERSON><PERSON><PERSON>", "beastlord": "ratukebon", "juegosretro": "dolananlaw<PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "cadehdopamin", "staxel": "staxel", "videogameost": "musikgameonline", "dragonsync": "sinkronaga", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "akutresnokofxv", "arcanum": "misteri", "neoy2k": "neoy2k", "pcracing": "balappcracing", "berserk": "edan", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animesedih", "darkerthanblack": "lu<PERSON><PERSON><PERSON>", "animescaling": "skalaingan<PERSON>", "animewithplot": "animesingseru", "pesci": "pesci", "retroanime": "animelawas", "animes": "anime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80an", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "<PERSON><PERSON><PERSON>", "popeetheperformer": "popesing<PERSON>performer", "masterpogi": "master<PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000an", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonemusim1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "coveranime", "thevisionofescaflowne": "thevisionofescaflowne", "slayers": "slayers", "tokyomajin": "tokyomajin", "anime90s": "anime90an", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "iwakpisang", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "hanakokunjagawc", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "fireforce", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "diarymasadepan", "fairytail": "<PERSON>eng<PERSON>na", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "parasit", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "melodipuyik", "kamisamakiss": "kamisamangambung", "blmanga": "blmanga", "horrormanga": "<PERSON><PERSON><PERSON>", "romancemangas": "mangaas<PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "blacklagoon", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "pinterpoll", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "indexsihirtartentu", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosismik", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animeolahraga", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagaoftanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "takon", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "carangerawatmum<PERSON>", "fullmoonwosagashite": "bulanngarepwedokmadol", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "lucunanserem", "martialpeak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "cewehhiscore", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "cewekantu", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "animelawas", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "animehorroor", "fruitsbasket": "keranjangjambe", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "thepromisedneverland", "monstermanga": "monstermanga", "yourlieinapril": "apusomulanbulanap<PERSON>", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "serap<PERSON>ungkas<PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "perangpangan", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "garis<PERSON><PERSON>", "toyoureternity": "tekonomuselamane", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "<PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "aliansirahas<PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON><PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "goblinslayer", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "ksatriavampir", "mugi": "mugi", "blueexorcist": "blueexorcist", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "nangis", "spyfamily": "spyfamily", "airgear": "airgear", "magicalgirl": "putrica<PERSON>", "thesevendeadlysins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "sekolahpenjara", "thegodofhighschool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "j<PERSON><PERSON><PERSON>", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saosingkat", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "untungpasmalinglarang", "romancemanga": "mangaas<PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animéroma<PERSON><PERSON>", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayernyangsedel", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON>", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceanime": "animeromantis", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "cherrymagic", "housekinokuni": "<PERSON>mah<PERSON><PERSON><PERSON>", "recordragnarok": "rekamragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "smamatitotal", "germantechno": "<PERSON>k<PERSON><PERSON><PERSON>", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "paradepatine", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejepang", "animespace": "animespace", "girlsundpanzer": "girlsundpanzer", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "tikusk<PERSON><PERSON>", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "wedikcantikpersik", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "klubkoncowadonakal", "dragonquestdai": "dragonquestdai", "heartofmanga": "<PERSON><PERSON><PERSON>", "deliciousindungeon": "enakbangetningpenjara", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "recordofragnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialekiangelangsung", "overgeared": "keluwihengear", "toriko": "<PERSON><PERSON>o", "ravemaster": "masterrave", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "witchhatatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON>rip<PERSON>man<PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "haremwalik", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "guru<PERSON><PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "bosku", "gear5": "gear5", "grandbluedreaming": "ngimpigranblue", "bloodplus": "bloodplus", "bloodplusanime": "animebloodzplus", "bloodcanime": "bloodcanime", "bloodc": "golongandarah", "talesofdemonsandgods": "ceritasatanlandemit", "goreanime": "goreanime", "animegirls": "animecewek", "sharingan": "<PERSON><PERSON>", "crowsxworst": "crowsxpalingparah", "splatteranime": "animesplatter", "splatter": "cipratan", "risingoftheshieldhero": "risingoftheshieldhero", "somalianime": "animesomalia", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedattaken", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "bocah<PERSON><PERSON>", "liarliar": "apus<PERSON><PERSON>", "supercampeones": "supercampeones", "animeidols": "animeidol", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "bocahwedokgaib", "callofthenight": "undanganmalem", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "kebonbayang", "tsubasachronicle": "tsubasachronicle", "findermanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "princessjellyfish": "put<PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekiss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animéverse", "persocoms": "kompumanungsa", "omniscientreadersview": "wecangarepkabeh", "animecat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animerecommendations": "rekomendasianime", "openinganime": "buka<PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "komediromantiskupasakremaja", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "robotraksa<PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mekanik", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "kelantang", "deathnote": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animebalatentara", "greenranger": "<PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animecity", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiecejowo", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonslayer", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "kor<PERSON><PERSON><PERSON>i", "onepieceanime": "animeonepieceku", "attaquedestitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theonepieceisreal": "theonepieceikibeneran", "revengers": "balasdendam", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "efekjoboy", "digimonstory": "ceritad<PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonmpuspus", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "uripbendinane", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "penyihirterbang", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "m<PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "jalanskabehsanto", "recuentosdelavida": "ceri<PERSON><PERSON><PERSON><PERSON><PERSON>"}