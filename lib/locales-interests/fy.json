{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologysk", "cognitivefunctions": "kognitivefunksjes", "psychology": "psychologysk", "philosophy": "filosofy", "history": "skiednis", "physics": "natuerkunde", "science": "wittenskip", "culture": "<PERSON><PERSON><PERSON>", "languages": "talen", "technology": "technologysk", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "astrologymemes", "enneagrammemes": "enneagrammemes", "showerthoughts": "dûsjeprakkesearen", "funny": "grap<PERSON><PERSON>", "videos": "fideos", "gadgets": "gadgets", "politics": "polityk", "relationshipadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "libbensfisy", "crypto": "krypto", "news": "nijs", "worldnews": "wr<PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "archeology", "learning": "learje", "debates": "debatten", "conspiracytheories": "konspirasjeteorien", "universe": "universum", "meditation": "meditaa<PERSON><PERSON>", "mythology": "mytology", "art": "keunst", "crafts": "knutselje", "dance": "dûnsje", "design": "ûntwerp", "makeup": "makeup", "beauty": "skientme", "fashion": "moade", "singing": "sjonge", "writing": "skriuwe", "photography": "fotografie", "cosplay": "cosplay", "painting": "skilderjen", "drawing": "tekenje", "books": "boeken", "movies": "films", "poetry": "poëzij", "television": "televyzje", "filmmaking": "filmmeitsje", "animation": "animaasje", "anime": "anime", "scifi": "scifi", "fantasy": "fantasy", "documentaries": "do<PERSON><PERSON><PERSON><PERSON><PERSON>", "mystery": "mystearje", "comedy": "komeedzje", "crime": "kriminaliteit", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horror", "romance": "romantyk", "realitytv": "realitytv", "action": "a<PERSON><PERSON>", "music": "muzyk", "blues": "blues", "classical": "klassyk", "country": "lân", "desi": "desi", "edm": "edm", "electronic": "elektronysk", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "hûs", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latyn", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "reis", "concerts": "konserten", "festivals": "festivals", "museums": "musea", "standup": "standup", "theater": "teater", "outdoors": "b<PERSON><PERSON><PERSON>", "gardening": "tuin<PERSON><PERSON>", "partying": "<PERSON><PERSON><PERSON>", "gaming": "gaming", "boardgames": "b<PERSON>rdspu<PERSON>", "dungeonsanddragons": "donjonsendrakken", "chess": "skaak", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "iten", "baking": "bakke", "cooking": "koken", "vegetarian": "fegetarysk", "vegan": "vegan", "birds": "f<PERSON><PERSON>s", "cats": "katten", "dogs": "<PERSON><PERSON><PERSON>", "fish": "fisk", "animals": "dieren", "blacklivesmatter": "blacklivesmatter", "environmentalism": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "feminism": "feminisme", "humanrights": "minsker<PERSON><PERSON><PERSON>", "lgbtqally": "lgbtqbondgenoat", "stopasianhate": "stopaziatyskehaat", "transally": "transally", "volunteering": "frijwilligerswurk", "sports": "sport", "badminton": "badminton", "baseball": "honkbal", "basketball": "basketbal", "boxing": "bokse", "cricket": "kriket", "cycling": "<PERSON><PERSON><PERSON>", "fitness": "fitness", "football": "fuotbal", "golf": "golf", "gym": "gym", "gymnastics": "gymnast<PERSON>", "hockey": "hockey", "martialarts": "fjochtsporten", "netball": "netbal", "pilates": "pilates", "pingpong": "pingpong", "running": "rinne", "skateboarding": "skateboarden", "skiing": "skiën", "snowboarding": "snowboarden", "surfing": "surfen", "swimming": "swimme", "tennis": "tennis", "volleyball": "follybal", "weightlifting": "gewichtheffen", "yoga": "yoga", "scubadiving": "d<PERSON>k<PERSON><PERSON>", "hiking": "kui<PERSON><PERSON>", "capricorn": "steenbok", "aquarius": "wetterman", "pisces": "fisken", "aries": "aries", "taurus": "stier", "gemini": "twilling", "cancer": "kanker", "leo": "leo", "virgo": "maagd", "libra": "weeg<PERSON><PERSON>", "scorpio": "skor<PERSON><PERSON>", "sagittarius": "sagittarius", "shortterm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "relaxed", "longtermrelationship": "langetermynrel<PERSON><PERSON><PERSON>", "single": "single", "polyamory": "polyamory", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "lesbysk", "bisexual": "bi<PERSON><PERSON><PERSON><PERSON>", "pansexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "wa<PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kingsquest", "soulreaver": "<PERSON>elr<PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "legendefanspyro", "rouguelikes": "rouguelikes", "syberia": "siberië", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "sinneûndergongoverdrive", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "heroesofthestorm", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "dungeoncrawlen", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribesofmidgard", "planescape": "flean<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsoftherealm2": "hearrenvanhetrykje2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medabots": "medabots", "lodsoftherealm2": "hearskenfaritryk2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "immersivesims", "okage": "okage", "juegoderol": "rolspul", "witcher": "witcher", "dishonored": "<PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "ynslûpend", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyâldeskool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidemotyvaasje", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "sukkerfoarleafde", "otomegames": "otom<PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimensje20", "gaslands": "<PERSON><PERSON><PERSON><PERSON>", "pathfinder": "paads<PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2eediysje", "shadowrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "swiertekrêftrush", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "oneshot", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "oerhear", "yourturntodie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "atomskûlplak", "gurps": "gurps", "darkestdungeon": "tsjusterstekerker", "eclipsephase": "eklipsfaze", "disgaea": "disgaea", "outerworlds": "bûtenw<PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastywarriors", "skullgirls": "skullgirls", "nightcity": "nachtsstêd", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "gekkengefecht", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeralân", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "fergettenfantasywrâlden", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonwrâ<PERSON>", "monsterrancher": "monsterfokker", "ecopunk": "ekopunk", "vermintide2": "fermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "brokkentroannen", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "skaadpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "altiidoaze", "hogwartmystery": "hogwartsmystearje", "deltagreen": "deltagriene", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "<PERSON><PERSON><PERSON>", "starfinder": "stjêrrefiner", "goldensun": "goudensun", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "meskynittsjuster", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "fallenoarder", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "godlikens", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "aventoertocht", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "rolferspullen", "roleplayinggames": "r<PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "sinnehaven", "talesofsymphonia": "talesofsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "<PERSON><PERSON><PERSON><PERSON>", "sacredunderworld": "hillichûndergrûn", "chainedechoes": "kettinglûden", "darksoul": "tsjusteregeest", "soulslikes": "soulslike<PERSON><PERSON>en", "othercide": "oarekant", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "rifts", "tibia": "tibia", "thedivision": "dedivyzje", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "legendofdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "fampyrlamamaskerada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "wearwolfdeapokalyps", "aveyond": "aveyond", "littlewood": "lytsbosk", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "motorhert", "fable3": "fable3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "hiveruil", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "iew<PERSON>eden", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "stjer<PERSON>f<PERSON><PERSON>", "oldschoolrevival": "âldeskoallerevival", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "wyldekriten", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "tsjustertfinzen", "juegosrpg": "rpgspullen", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "wyl<PERSON><PERSON><PERSON>", "bastion": "bastioan", "drakarochdemoner": "draakkenendemoanen", "skiesofarcadia": "skiesofarcadia", "shadowhearts": "s<PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "sintebloed", "breathoffire4": "breathoffire4", "mother3": "mem3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "weranothereden", "roleplaygames": "rolspultsjes", "roleplaygame": "rolspulspul", "fabulaultima": "fabeleftigeultima", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "harrypot<PERSON><PERSON>l", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirylmasquerade", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronokrús", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "shadowheartsynferbûn", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "keninkrykkomme", "awplanet": "awplaneet", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON>ingetmeidy", "dragalialost": "dragalialost", "elderscroll": "âldereskriftrolle", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytaktyk", "grandia": "grandia", "darkheresy": "d<PERSON>sterek<PERSON><PERSON><PERSON>", "shoptitans": "winkelgiganten", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "ierdemagy", "blackbook": "swartboek", "skychildrenoflight": "skybernenvanljocht", "gryrpg": "gryrpg", "sacredgoldedition": "sakraalegoudedysje", "castlecrashers": "kasteelkrakers", "gothicgame": "gothicgame", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gamingrpg", "prophunt": "prophunt", "starrails": "st<PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofmist": "st<PERSON>dsfan<PERSON>", "indierpg": "indierpg", "pointandclick": "oanwizeenklikke", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "ûnskiedbear", "freeside": "b<PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postsyberpunk", "deathroadtocanada": "deadegeiweinaarkanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "monsterjager", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremasy", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "sieliter", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarygames", "tacticalrpg": "taktyskrpg", "mahoyo": "mahoyo", "animegames": "animespultsjes", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "godeneater", "diluc": "diluc", "venti": "fenti", "eternalsonata": "<PERSON><PERSON>ata", "princessconnect": "prinsesseconnect", "hexenzirkel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cristales": "kristallen", "vcs": "vcs", "pes": "pes", "pocketsage": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantyndiaansk", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efuotbal", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligafandreamers", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efuotbal", "dreamhack": "dreamhack", "gaimin": "gamen", "overwatchleague": "overwatchleague", "cybersport": "cybersport", "crazyraccoon": "gekkeraccoon", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantkompetysje", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "heallibbjen", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "valve", "portal": "portaal", "teamfortress2": "teamfortress2", "everlastingsummer": "ivichsimmer", "goatsimulator": "geitesimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "frijheidplaneet", "transformice": "transformice", "justshapesandbeats": "gewoanfoarmenenbeats", "battlefield4": "battlefield4", "nightinthewoods": "nachtynboskje", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanias", "overcooked": "tefierbakken", "interplanetary": "ynterplanetêr", "helltaker": "heldûnder", "inscryption": "ynskrypsje", "7d2d": "7d2d", "deadcells": "<PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwerchfort", "foxhole": "foksgat", "stray": "ferd<PERSON>e", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "slachfjild1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "<PERSON><PERSON><PERSON><PERSON>", "blackdesert": "swart<PERSON><PERSON><PERSON>", "tabletopsimulator": "tabletopsimulator", "partyhard": "feestjeknalle", "hardspaceshipbreaker": "hurdromteskipsloper", "hades": "hades", "gunsmith": "smid", "okami": "<PERSON>ami", "trappedwithjester": "opslotenmeijester", "dinkum": "dinkum", "predecessor": "foar<PERSON>er", "rainworld": "rein<PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "koloanysimulaasje", "noita": "noita", "dawnofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "tsjusterentsjusterder", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "<PERSON><PERSON><PERSON><PERSON>", "yaga": "jaga", "cubeescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "stêdskylines", "defconheavy": "def<PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsia", "virtualkenopsia": "virtuelekenopsia", "snowrunner": "snie<PERSON><PERSON><PERSON>", "libraryofruina": "biblioteekfanruïne", "l4d2": "l4d2", "thenonarygames": "denonarygames", "omegastrikers": "omegastrikers", "wayfinder": "paads<PERSON><PERSON>", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "plestichplastykeintje", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatekuikenpaard", "dialtown": "dialtown", "smileforme": "glimkjefoarmy", "catnight": "ka<PERSON>j<PERSON>n", "supermeatboy": "supermeatboy", "tinnybunny": "tsiismûske", "cozygrove": "knúsbosk", "doom": "f<PERSON><PERSON><PERSON>", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "<PERSON><PERSON><PERSON>", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "paladins", "earthdefenseforce": "ierdeferdigingsmacht", "huntshowdown": "huntshowdown", "ghostrecon": "geestferkenning", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "joindesquad", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "opstandsânstorm", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "spegelrâne", "divisions2": "divyzjes2", "killzone": "killzone", "helghan": "hel<PERSON>", "coldwarzombies": "kâldeoarlochzombies", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "crosscode", "goldeneye007": "goudeneach007", "blackops2": "blackops2", "sniperelite": "s<PERSON><PERSON><PERSON><PERSON>", "modernwarfare": "moderneoarloch", "neonabyss": "neonôfgrûn", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "boarderlânen", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "oerkarnage", "worldofwarships": "worldofwarships", "back4blood": "weromfoarbloed", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "<PERSON>ys<PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "grottenferhaal", "doometernal": "doometernal", "centuryageofashes": "ieuageofashes", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaanskentiger", "generationzero": "generaasje<PERSON>l", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "woarstman", "ratchetandclank": "ratchetenclank", "chexquest": "chexquest", "thephantompain": "itfantoomspine", "warface": "oarlochsgesicht", "crossfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "atomicheart": "atomyskehert", "blackops3": "blackops3", "vampiresurvivors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybattleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "f<PERSON><PERSON><PERSON>", "battlegrounds": "slachf<PERSON>lden", "frag": "frag", "tinytina": "lytsjefemke", "gamepubg": "gamepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "juegosfps", "convertstrike": "bekearstrike", "warzone2": "warzone2", "shatterline": "breklinefgame", "blackopszombies": "blackopszombies", "bloodymess": "bloedbinde", "republiccommando": "republikeinskekommando", "elitedangerous": "elitedangerous", "soldat": "sold<PERSON>t", "groundbranch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "squad", "destiny1": "skiednis1", "gamingfps": "gamingfps", "redfall": "readfal", "pubggirl": "pubgfamke", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "o<PERSON><PERSON>d", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "<PERSON><PERSON><PERSON><PERSON>", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinassinhekskinwûnderlân", "halo2": "halo2", "payday2": "jildichtsje2", "cs16": "cs16", "pubgindonesia": "pubgindonesië", "pubgukraine": "pubgû<PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "pub<PERSON>s<PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "siepekabelje", "ghostcod": "spûkcod", "csplay": "csplay", "unrealtournament": "unrealtournament", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pist<PERSON><PERSON><PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonwyt", "remnant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "azurelane": "azurelane", "worldofwar": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "weromkomst", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "skiedman", "quake2": "quake2", "microvolts": "mikrofolt", "reddead": "readdea", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "seaofthieves", "rust": "r<PERSON><PERSON>", "conqueronline": "conqueronline", "dauntless": "ûnfersaachlik", "warships": "oarlochsskippen", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "flightrisingfy", "recroom": "recroom", "legendsofruneterra": "legendsofruneterra", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "phantasystaronline2", "maidenless": "frijgezelloos", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "worldoftanks", "crossout": "útkrúse", "agario": "agario", "secondlife": "twa<PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "netoanlinespielen", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpinguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "skûm", "newworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "multiplayer", "pirate101": "pirate101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "jiskefanskepping", "riotmmo": "riotmmo", "silkroad": "sildesrûte", "spiralknights": "spiralknights", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "dragonsprofeet", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "mearspilers", "angelsonline": "ingelsenonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "groeitopia", "starwarsoldrepublic": "starwarsâldrepublik", "grandfantasia": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "blueprotocol": "blau<PERSON><PERSON><PERSON><PERSON>", "perfectworld": "<PERSON>fe<PERSON><PERSON><PERSON><PERSON><PERSON>", "riseonline": "oplinekommen", "corepunk": "corepunk", "adventurequestworlds": "aventoerquestworlds", "flyforfun": "fleachfoar<PERSON>e", "animaljam": "animaljam", "kingdomofloathing": "keningrykfanôfgrysjen", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "streetfighter", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "for<PERSON>or", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "strjittenynrage", "mkdeadlyalliance": "mkdeadlikealliânsje", "nomoreheroes": "gjinheldenmear", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "thekingoffighters", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retrofjochtspullen", "blasphemous": "godslasterlik", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "superknaller", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "syberbots", "armoredwarriors": "pantserkriggers", "finalfight": "<PERSON><PERSON><PERSON><PERSON>f<PERSON><PERSON>", "poweredgear": "poweredgear", "beatemup": "<PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "moördnersinstinkt", "kingoffigthers": "k<PERSON><PERSON>fanf<PERSON><PERSON>ers", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "chivalry2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightferfolch", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "silkson<PERSON><PERSON>s", "silksong": "silksong", "undernight": "ûndernacht", "typelumina": "typelum<PERSON>", "evolutiontournament": "evolúsjetoernoai", "evomoment": "evomomint", "lollipopchainsaw": "lollyketels<PERSON>ch", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "bloodborne", "horizon": "horizon", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodbourne", "uncharted": "ûnbekend", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "be<PERSON>chte", "playstationbuddies": "playstationfreonen", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "<PERSON><PERSON>arte", "persona4": "persona4", "hellletloose": "<PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "godof<PERSON>", "gris": "gris", "trove": "skat", "detroitbecomehuman": "detroitwordtminsklik", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "toeristetrofee", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "crashteamracing", "fivepd": "fyfpd", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playingstation", "samuraiwarriors": "samuraik<PERSON>ers", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "sieleskleau", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "lêstebeskermheilige", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "feestbeest", "warharmmer40k": "warharmmer40k", "fightnightchampion": "fjochtnachtskampioen", "psychonauts": "psychonauten", "mhw": "mhw", "princeofpersia": "prinsvanperzië", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "stj<PERSON>rrebûn", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "h<PERSON>sflipper", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxsearje", "r6xbox": "r6xbox", "leagueofkingdoms": "leagueofkingdoms", "fable2": "fabel2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "trashtv", "skycotl": "skycotl", "erica": "erica", "ancestory": "f<PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "littlemispech", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterprom", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kultfanlam", "duckgame": "einsjespul", "thestanleyparable": "destanleyparable", "towerunite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "occulto": "occulto", "longdrive": "langerit", "satisfactory": "fold<PERSON>and<PERSON>", "pluviophile": "rein<PERSON><PERSON><PERSON>", "underearth": "ûndergrûns", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "tsjusterekoepel", "pizzatower": "pizzatoer", "indiegame": "yndiespul", "itchio": "itchio", "golfit": "golfjeit", "truthordare": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "spultsje", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampoline", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "doare", "scavengerhunt": "speurt<PERSON>t", "yardgames": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "kiesnûmer", "trueorfalse": "wieroff<PERSON>", "beerpong": "beerpong", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "geselligegames", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "freegame", "drinkinggames": "drinkspultsjes", "sodoku": "sodoku", "juegos": "spu<PERSON><PERSON><PERSON>", "mahjong": "mahjong", "jeux": "spu<PERSON><PERSON><PERSON>", "simulationgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "wurdspultsjes", "jeuxdemots": "jeuxdemots", "juegosdepalabras": "wurdspultsjes", "letsplayagame": "litsboartsje", "boredgames": "ferfeelingspultsjes", "oyun": "oyun", "interactivegames": "ynteraktivespultsjes", "amtgard": "amtgard", "staringcontests": "stoarconcours", "spiele": "spu<PERSON><PERSON><PERSON>", "giochi": "spu<PERSON><PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "iphonespultsjes", "boogames": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegame": "krânspul", "hideandseek": "fersjeopjeensiik", "hopscotch": "hink<PERSON>", "arcadegames": "<PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "yakuzagames", "classicgame": "klassikspul", "mindgames": "harsenskrammeljen", "guessthelyric": "rieddetekst", "galagames": "galagames", "romancegame": "romansespultsje", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "tongebrekkers", "4xgames": "4x<PERSON><PERSON><PERSON>", "gamefi": "gamefi", "jeuxdarcades": "arcadespultsjes", "tabletopgames": "tafelspullen", "metroidvania": "metroidvania", "games90": "spultsjes90", "idareyou": "ik<PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumitouedaspults<PERSON>s", "racinggames": "racespultsjes", "ets2": "ets2", "realvsfake": "echtoftep", "playgames": "spieltsjelts<PERSON>s", "gameonline": "gameonline", "onlinegames": "onlinespultsjes", "jogosonline": "onlinespultsjes", "writtenroleplay": "skriuwenrollespul", "playaballgame": "boartsje", "pictionary": "pictionary", "coopgames": "coopgames", "jenga": "jenga", "wiigames": "wiigames", "highscore": "<PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "rollespullen", "burgergames": "burgerspullen", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "fraachspul", "gioco": "gioco", "managementgame": "managementspul", "hiddenobjectgame": "ferburgenobjektspultsje", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formule1game", "citybuilder": "stêdbouwer", "drdriving": "drdrdriving", "juegosarcade": "arcadespultsjes", "memorygames": "oantinkspultsjes", "vulkan": "vulkan", "actiongames": "aksje<PERSON>ull<PERSON>", "blowgames": "b<PERSON>ass<PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "pinballmasines", "oldgames": "âldespultsjes", "couchcoop": "bankkoöp", "perguntados": "frege", "gameo": "gameo", "lasergame": "lasergame", "imessagegames": "imessagespults<PERSON>s", "idlegames": "idlespultsjes", "fillintheblank": "ynfolje", "jeuxpc": "jeuxpc", "rétrogaming": "rétrogaming", "logicgames": "logyskespultsjes", "japangame": "japanspul", "rizzupgame": "rizzupgame", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "spultsjesmeiferneamden", "exitgames": "útweis", "5vs5": "5tsjin5", "rolgame": "rolspul", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "tradisjonelespultsjes", "kniffel": "kniffel", "gamefps": "gamefps", "textbasedgames": "tekstbasearre_spullen", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "weromspul", "thiefgame": "stellerspul", "lawngames": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "tafelfoetbal", "tischfußball": "tafelkuorke", "spieleabende": "spultsjesjûnen", "jeuxforum": "jeuxforum", "casualgames": "tiidfer<PERSON>iwspultsjes", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "ûntsnappingsspullen", "thiefgameseries": "die<PERSON><PERSON>lsear<PERSON>", "cranegames": "kraanspults<PERSON>s", "játék": "spultsje", "bordfodbold": "bordfodbold", "jogosorte": "jogosorte", "mage": "mage", "cargames": "autosjespilen", "onlineplay": "onlineboartsje", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON>", "pursebingos": "tassebingos", "randomizer": "<PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "gamespc", "socialdeductiongames": "sosjaleôfliedingsspullen", "dominos": "dominos", "domino": "domino", "isometricgames": "isometryskespullen", "goodoldgames": "goeieâldenspullen", "truthanddare": "wierheidenoarre", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "speurt<PERSON><PERSON>", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "f2pspiler", "free2play": "grat<PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "fantasypul", "gryonline": "gryonline", "driftgame": "driftspul", "gamesotomes": "gamesoarjemanlju", "halotvseriesandgames": "halotvserieengames", "mushroomoasis": "paddestoeloaze", "anythingwithanengine": "alleswatinmotorhat", "everywheregame": "o<PERSON>game", "swordandsorcery": "swurden<PERSON><PERSON>", "goodgamegiving": "goodspuljeljen", "jugamos": "w<PERSON><PERSON><PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "kompjûterspultsjes", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "ritmespultsjes", "minaturegames": "miniatuerspultsjes", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "selsleatsgamen", "gamemodding": "gamemodding", "crimegames": "misdiedspullen", "dobbelspellen": "do<PERSON>lspu<PERSON>", "spelletjes": "spu<PERSON><PERSON><PERSON>", "spacenerf": "r<PERSON><PERSON><PERSON><PERSON>", "charades": "sjarrades", "singleplayer": "singlespiler", "coopgame": "coopspul", "gamed": "game", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "haadspul", "kingdiscord": "keningdiscord", "scrabble": "scrabble", "schach": "skaak", "shogi": "shogi", "dandd": "dd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "pande<PERSON><PERSON><PERSON><PERSON>", "camelup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopolygame": "monopolyspul", "brettspiele": "b<PERSON>rdspu<PERSON>", "bordspellen": "b<PERSON><PERSON><PERSON><PERSON>", "boardgame": "boerdspul", "sällskapspel": "selskipsspullen", "planszowe": "br<PERSON>ts<PERSON><PERSON>", "risiko": "risiko", "permainanpapan": "boardspultsjes", "zombicide": "zombycide", "tabletop": "tabletop", "baduk": "baduk", "bloodbowl": "bloodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "goboardspul", "connectfour": "fjouwerferbine", "heroquest": "heroquest", "giochidatavolo": "spullentsjetafel", "farkle": "farkle", "carrom": "carrom", "tablegames": "tafelspultsjes", "dicegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "boerdspul", "jocuridesocietate": "geselskipsspultsjes", "deskgames": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "kos<PERSON><PERSON><PERSON><PERSON>n", "creationludique": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "tabletoproljespil", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "skeakelbordspultsjes", "infinitythegame": "infinitythegame", "kingdomdeath": "keningsdea", "yahtzee": "yahtzee", "chutesandladders": "glij<PERSON>enenljedder<PERSON>", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "boardspul", "planszówki": "boardspultsjes", "rednecklife": "red<PERSON><PERSON><PERSON><PERSON>", "boardom": "ferfeelsum", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "jeudesociété", "gameboard": "spulboerd", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "b<PERSON><PERSON>pu<PERSON><PERSON><PERSON>", "twilightimperium": "twilightimperium", "horseopoly": "hyndre<PERSON><PERSON>", "deckbuilding": "kaartspulbouwe", "mansionsofmadness": "wa<PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "boardgames", "shadowsofbrimstone": "skiedenfanbrimstone", "kingoftokyo": "keningfantokyo", "warcaby": "warcaby", "táblajátékok": "b<PERSON><PERSON>pu<PERSON><PERSON><PERSON>", "battleship": "slachskip", "tickettoride": "kaartsjefoardereis", "deskovehry": "b<PERSON>blêdspultsjes", "catán": "kat<PERSON>", "subbuteo": "subbuteo", "jeuxdeplateau": "j<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "br<PERSON>ts<PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "jeuxsociete", "gesellschaftsspiele": "geselskipsspullen", "starwarslegion": "starwarslegion", "gochess": "gochess", "weiqi": "weiqi", "jeuxdesocietes": "selskipsspullen", "terraria": "terraria", "dsmp": "dsmp", "warzone": "oarlochsgebiet", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identityv", "theisle": "<PERSON><PERSON>", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "callofcthulhu", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "tuskenús", "eco": "eko", "monkeyisland": "aapjeilân", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "da<PERSON><PERSON>", "fobia": "fobia", "witchit": "<PERSON><PERSON><PERSON>", "pathologic": "patologysk", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "del<PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "h<PERSON><PERSON><PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "gekkeheit", "dontstarve": "netferhongerje", "eternalreturn": "iwigeweromkomst", "pathoftitans": "<PERSON>aa<PERSON><PERSON><PERSON>", "frictionalgames": "frictionalgames", "hexen": "<PERSON><PERSON><PERSON>", "theevilwithin": "itkweainûs", "realrac": "echterac", "thebackrooms": "deefterkeamers", "backrooms": "efterkeamers", "empiressmp": "empiressmp", "blockstory": "blokfer<PERSON><PERSON>", "thequarry": "<PERSON><PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "stjerjendeljocht", "thewalkingdeadgame": "thewalkingdeadgame", "wehappyfew": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "opkomstfanriken", "stateofsurvivalgame": "stateofsurvivalgame", "vintagestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "azempakket", "alisa": "alisa", "westlendsurvival": "westlendsurvival", "beastsofbermuda": "monstersûtbermuda", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "survivalhor<PERSON>r", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "<PERSON><PERSON><PERSON><PERSON><PERSON>art", "lifeaftergame": "lieftneitspul", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "di<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "grienprojekt", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "flot", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "babes", "littlenightmares2": "littlelittsjenwonder2", "signalis": "signaal", "amandatheadventurer": "amandadeaavonturierster", "sonsoftheforest": "soanenfanhetbosk", "rustvideogame": "rêstvideogame", "outlasttrials": "outlasttrials", "alienisolation": "alienisolaasje", "undawn": "ûndeagjende", "7day2die": "7dagenomdeipegean", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "oerlibje", "propnight": "propnacht", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampier", "deathverse": "<PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "katastrofedokkeredagen", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "libbennei", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "kloktorren3", "aloneinthedark": "allini<PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "midsiuwskedynasty", "projectnimbusgame": "projectnimbusgame", "eternights": "ivigenachten", "craftopia": "craftopia", "theoutlasttrials": "deoutlastproeven", "bunker": "bunker", "worlddomination": "wr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officionassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kcrush", "wh40": "wh40", "warhammer40klove": "warhammer40kleafde", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "tim<PERSON><PERSON><PERSON>us", "vindicare": "<PERSON><PERSON><PERSON><PERSON>", "ilovesororitas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "ikhâldfanvindicare", "iloveassasinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>asinor<PERSON>", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "ittakestwo", "wingspan": "wjukspanne", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "heldenvanmachtenmagy", "btd6": "btd6", "supremecommander": "opperkommandant", "ageofmythology": "ageofmythology", "args": "args", "rime": "rime", "planetzoo": "planetedieretún", "outpost2": "outpost2", "banished": "fer<PERSON><PERSON>", "caesar3": "caesar3", "redalert": "readallarm", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strategygames": "<PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "beskavingsspul", "civilization4": "sivilisaasje4", "factorio": "factorio", "dungeondraft": "fantysjekeamers", "spore": "spoaren", "totalwar": "<PERSON><PERSON><PERSON><PERSON>", "travian": "travian", "forts": "foarten", "goodcompany": "goedselskip", "civ": "civ", "homeworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heidentum": "heidendom", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "flugger<PERSON>l<PERSON><PERSON>", "forthekings": "foardekoningen", "realtimestrategy": "realtimestrateedzje", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "keninkryktwakroanen", "eu4": "eu4", "vainglory": "<PERSON>el<PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "godlikens", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davesynleukealgebraklas", "plagueinc": "pestynk", "theorycraft": "theorycraft", "mesbg": "mesbg", "civilization3": "sivilisaasje3", "4inarow": "4op<PERSON>rige", "crusaderkings3": "krusearrekeningen3", "heroes3": "heroes3", "advancewars": "oarlochsfoarút", "ageofempires2": "ageofempires2", "disciples2": "disciples2", "plantsvszombies": "plantentsjindezombies", "giochidistrategia": "giochidistrategia", "stratejioyunları": "strateezjys<PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "tiidvanwûnders", "dinosaurking": "dinosauruskening", "worldconquest": "wr<PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "heartsofiron4", "companyofheroes": "kompanyfanhelden", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "goesgoeseintje", "phobies": "fobijen", "phobiesgame": "fobyjespul", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "oer<PERSON>urt<PERSON>", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "krúsfarrenkeningen", "cultris2": "cultris2", "spellcraft": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategy", "popfulmail": "popfulmail", "shiningforce": "glinsterjendemacht", "masterduel": "masterduel", "dysonsphereprogram": "dysonsfearprogramma", "transporttycoon": "transportmagnaat", "unrailed": "<PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "flean<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uplandkingdoms": "heechlankeninkriken", "galaxylife": "galaksylibben", "wolvesvilleonline": "wolvesvillageonline", "slaythespire": "slaythespire", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "needforspeed", "needforspeedcarbon": "needforspeedcarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "desims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "oerlibje", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "alicemad<PERSON>ret<PERSON>s", "darkhorseanthology": "darkhorseantology", "phasmophobia": "phasmo<PERSON><PERSON><PERSON>", "fivenightsatfreddys": "fiifnachtenbyfreddy", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "<PERSON><PERSON><PERSON>enachtmer<PERSON><PERSON>", "deadrising": "deaderopstân", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "th<PERSON><PERSON><PERSON>n", "deadisland": "<PERSON><PERSON><PERSON>", "litlemissfortune": "litlemissfortune", "projectzero": "projekt<PERSON>l", "horory": "horory", "jogosterror": "rinn<PERSON>grûwe<PERSON>", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "hoineighbor2", "gamingdbd": "gamingdbd", "thecatlady": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "j<PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "horrorgaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kaartentegendemins<PERSON><PERSON>", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinokel", "codenames": "koadnammen", "dixit": "dixit", "bicyclecards": "fytskaarten", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "patience", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "kaartspyljen", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "pokémonkaarten", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "sportkaarten", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "skopkes", "warcry": "striidrop", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "<PERSON><PERSON><PERSON><PERSON>", "hanafuda": "hana<PERSON>da", "theresistance": "itwjerstân", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohkaarten", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohspul", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohgeit", "briscas": "briscas", "juegocartas": "kaartspul", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON>spults<PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "kaartspul", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "battlespirits", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "jokerskaarten", "facecard": "g<PERSON><PERSON><PERSON><PERSON>", "cardfight": "ka<PERSON>gefjocht", "biriba": "biriba", "deckbuilders": "kaartspulbouwers", "marvelchampions": "marvelk<PERSON><PERSON><PERSON><PERSON>", "magiccartas": "magyskkaarten", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "ynstabileeinhearnen", "cyberse": "cyberse", "classicarcadegames": "klassikespultsjesspullen", "osu": "osu", "gitadora": "gitadora", "dancegames": "dûnsspultsjes", "fridaynightfunkin": "frideitejûnfunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projektmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "g<PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "gewoandjensje", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockthedead", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rhythmgamer": "ritmegamer", "stepmania": "stepmania", "highscorerythmgames": "heechskoarritmespultsjes", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "rhythmheaven", "hypmic": "hypmic", "adanceoffireandice": "indûnsvanfjoereniis", "auditiononline": "audysjeoniline", "itgmania": "itgmaniaa", "juegosderitmo": "ritmespultsjes", "cryptofthenecrodancer": "krypt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rhythmdoctor": "ritmendokter", "cubing": "kubearzjen", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "puzzelspultsjes", "spotit": "s<PERSON><PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blokdoku", "logicpuzzles": "logykapuzzels", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "harsenskrakers", "rubikscube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossword": "krús<PERSON>rd<PERSON>zel", "motscroisés": "motscroisés", "krzyżówki": "krúswurdpuzzels", "nonogram": "nonogram", "bookworm": "boekewjirm", "jigsawpuzzles": "legpuzzels", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "ried<PERSON>", "riddles": "r<PERSON><PERSON><PERSON>", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "binnen", "angrybirds": "lili<PERSON>r<PERSON>chfûgels", "escapesimulator": "ûntsnappingssimulator", "minesweeper": "minesweeper", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "krúswurdpuzzels", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesgame", "puzzlesport": "puzzelsport", "escaperoomgames": "escaperoomspultsjes", "escapegame": "ûntsnappingsspul", "3dpuzzle": "3dpuzzel", "homescapesgame": "homescapesspul", "wordsearch": "wurdensyk<PERSON>", "enigmistica": "enigmatyk", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "riedselsferhalen", "fishdom": "fiskdom", "theimpossiblequiz": "deûnmooglequiz", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "match3puzzel", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "rubikskobus", "cuborubik": "kubusfanrubik", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "thúslânskippen", "puttputt": "minigolf", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON>", "tycoongames": "tycoongames", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cruciverba": "cruciver<PERSON>", "ciphers": "geheimskriuwen", "rätselwörter": "rätselwörter", "buscaminas": "minen_sykje", "puzzlesolving": "pu<PERSON>eloploss<PERSON>", "turnipboy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "r<PERSON><PERSON><PERSON>", "nobodies": "g<PERSON><PERSON>", "guessing": "rieden", "nonograms": "nonogrammen", "kostkirubika": "kostkirubika", "crypticcrosswords": "kryptyskekrúswurdpuzzels", "syberia2": "syberia2", "puzzlehunt": "puzzeljacht", "puzzlehunts": "puzzeltochten", "catcrime": "kattekriminaliteit", "quebracabeça": "puzzel", "hlavolamy": "puzzels", "poptropica": "poptropica", "thelastcampfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "selsbeskreaun", "picopark": "picopark", "wandersong": "wandersjonge", "carto": "carto", "untitledgoosegame": "titelleasgânskespul", "cassetête": "holle_brekker", "limbo": "limbo", "rubiks": "rubiks", "maze": "dûlhof", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "stikken", "portalgame": "portaalspul", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinelli": "r<PERSON><PERSON><PERSON>", "cubomagico": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "ferwûnetûnlân", "monopoly": "monopoly", "futurefight": "toekomstfight", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemystars", "stateofsurvival": "stateofsurvival", "mycity": "<PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "kleurichpoadium", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "riddertocht", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "fuot<PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "keningskeuze", "guardiantales": "beskermerhaalferhalen", "petrolhead": "benzinekop", "tacticool": "taktysk", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "gjin<PERSON>e", "craftsman": "<PERSON><PERSON><PERSON><PERSON>", "supersus": "supersus", "slowdrive": "stadichride", "headsup": "oplet", "wordfeud": "wur<PERSON><PERSON><PERSON>", "bedwars": "bedoarloch", "freefire": "freefire", "mobilegaming": "mobilegaming", "lilysgarden": "lily<PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "<PERSON><PERSON><PERSON>", "8ballpool": "8ballpool", "emergencyhq": "alarmsintraal", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "skodzjeenfriemelje", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "timeprinses", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "búseleafde", "androidgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "criminalcase": "misdiedplak", "summonerswar": "summonerswar", "cookingmadness": "kokkemâlheid", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "leagueofangels", "lordsmobile": "lordsmobile", "tinybirdgarden": "lytsfûgeltún", "gachalife": "gachalife", "neuralcloud": "neuralwolk", "mysingingmonsters": "my<PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "b<PERSON><PERSON><PERSON><PERSON>", "raidshadowlegends": "raidshadowlegends", "warrobots": "warrobots", "mirrorverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "altydûkeferhaal", "futime": "<PERSON><PERSON><PERSON><PERSON>", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "ingress", "slugitout": "útvjochtsje", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "gameofsultans", "arenabreakout": "arenabrekout", "wolfy": "wolfy", "runcitygame": "rinstêdspul", "juegodemovil": "mobiylspultsje", "avakinlife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kogama": "kogama", "mimicry": "<PERSON><PERSON>k", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "bombmybrazyl", "ldoe": "ldoe", "legendonline": "legindeaonline", "otomegame": "otomespul", "mindustry": "mindustry", "callofdragons": "callofdragons", "shiningnikki": "skitterjendenik<PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "paa<PERSON><PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "skaadstryd3", "limbuscompany": "limbuscompany", "demolitionderby3": "sloopderbby3", "wordswithfriends2": "wurdenmeifreonen2", "soulknight": "soulknight", "purrfecttale": "purrfektverhaal", "showbyrock": "showbyrock", "ladypopular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "lolmobyl", "harvesttown": "harvesttown", "perfectworldmobile": "perfektewrâldmobyl", "empiresandpuzzles": "keninkriken_en_puzels", "empirespuzzles": "rykspuzzels", "dragoncity": "d<PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "fanny", "littlenightmare": "lytskenachtmerje", "aethergazer": "aethergazer", "mudrunner": "modderracer", "tearsofthemis": "trien<PERSON><PERSON><PERSON><PERSON>", "eversoul": "altydsielfol", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiecastaways", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "koksmem", "cabalmobile": "cabalmobile", "streetfighterduel": "strjitfjochtersduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "famkesfrontline", "jurassicworldalive": "jurassicworldalive", "soulseeker": "<PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "derwerheiskomme", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "moankettingferhaal", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "mobilesgames", "legendofneverland": "legindenfannealâniet", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "timeraiders", "gamingmobile": "gamingmobyl", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "kwest", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "wr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "reizigerttrpg", "2300ad": "2300nk", "larp": "larp", "romanceclub": "<PERSON><PERSON><PERSON><PERSON>", "d20": "d20", "pokemongames": "pokémonspultsjes", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemonkristal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemon<PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natuer", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "pocketmonstersfy", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonknuffel", "teamystic": "teamystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "glinsterjendepokemon", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "pokemonsliept", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "<PERSON><PERSON><PERSON><PERSON>", "shinyhunter": "glimmer<PERSON><PERSON>", "ajedrez": "skaak", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "skak", "ajedres": "skaak", "chessgirls": "skaakfamkes", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "skaakspul", "japanesechess": "japanskskaak", "chinesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "skaakkanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "iep<PERSON>en", "rook": "rook", "chesscom": "skaakmatkearje", "calabozosydragones": "kerkersendraken", "dungeonsanddragon": "dungeonsendragen", "dungeonmaster": "dungeonmaster", "tiamat": "tiamat", "donjonsetdragons": "donjonsendraken", "oxventure": "oxventure", "darksun": "t<PERSON><PERSON><PERSON>inn<PERSON>", "thelegendofvoxmachina": "delegendefanvoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "tsjustermoor", "minecraftchampionship": "minecraftkampioenskip", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "myntest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "kearslaam", "fru": "fru", "addons": "addons", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "moddedminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "tusk<PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftstêd", "pcgamer": "pcgamer", "jeuxvideo": "jeuxvideo", "gambit": "gambit", "gamers": "gamers", "levelup": "<PERSON><PERSON><PERSON><PERSON>", "gamermobile": "gamermobyl", "gameover": "<PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "pcgamen", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "pcspullen", "casualgaming": "relaxgamen", "gamingsetup": "gamingsetup", "pcmasterrace": "pcmasterrace", "pcgame": "pcspultsje", "gamerboy": "gamer<PERSON>e", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "gameplays", "consoleplayer": "konsolespiler", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgamers", "onlinegaming": "onlinegamen", "semigamer": "semigamer", "gamergirls": "gamergirls", "gamermoms": "gamermemmen", "gamerguy": "gamerguy", "gamewatcher": "gamewatcher", "gameur": "gamer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerfroulju", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "teamkeihardsykje", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "kwests", "alax": "alax", "avgn": "avgn", "oldgamer": "âldgamer", "cozygaming": "gesellichgamen", "gamelpay": "gamelpay", "juegosdepc": "pcspultsjes", "dsswitch": "dswissel", "competitivegaming": "kompetityfjegamen", "minecraftnewjersey": "minecraftnjujersey", "faker": "faker", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heteroseksueelenspul", "gamepc": "gamepc", "girlsgamer": "gamerintsjes", "fnfmods": "fnfmods", "dailyquest": "deistikequest", "gamegirl": "gamefamke", "chicasgamer": "gamerfroulju", "gamesetup": "gameopset", "overpowered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "socialgamer": "sosjal<PERSON>r", "gamejam": "gamejam", "proplayer": "proffspiler", "roleplayer": "rolplayer", "myteam": "mynteam", "republicofgamers": "republykfangamers", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "trijed<PERSON><PERSON>dleginde", "gamerbuddies": "gamermaten", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "kristengamer", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "casualgamer", "89squad": "89squad", "inicaramainnyagimana": "hoebegjinstmendê<PERSON><PERSON>", "insec": "<PERSON><PERSON><PERSON><PERSON>", "gemers": "gamers", "oyunizlemek": "oyunizlemek", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "gamer", "wspólnegranie": "mienskiplikspyljen", "mortdog": "mortdog", "playstationgamer": "playstationgamer", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "sûnegamer", "gtracing": "gtracing", "notebookgamer": "notisjegamer", "protogen": "protogen", "womangamer": "gamefrou", "obviouslyimagamer": "fanselfsprekendingamer", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "forager", "humanfallflat": "minskfaltplat", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nulûntsnapping", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomuzyk", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "fallguys", "switch": "skeake<PERSON>je", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "legindofzel<PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "mario<PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "himelbernfanhetl<PERSON>cht", "tomodachilife": "tomodachileven", "ahatintime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "triennenynhetkeuninkriik", "walkingsimulators": "kuiersimulators", "nintendogames": "nintendospultsjes", "thelegendofzelda": "thelegendofzelda", "dragonquest": "dragonquest", "harvestmoon": "ha<PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "mynf<PERSON>onped<PERSON>", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51spultsjes", "earthbound": "ierdgebûn", "tales": "fer<PERSON>n", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "trijehoekstrategy", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersslimmefrijje", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioendsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendohûnen", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "red<PERSON><PERSON>", "vanillalol": "<PERSON><PERSON><PERSON><PERSON>", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "advertinsjedrager", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspanje", "aatrox": "aatrox", "euw": "gats", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrofideos<PERSON><PERSON><PERSON><PERSON>", "scaryvideogames": "eangstigefidjospullen", "videogamemaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "fideospultsje", "videosgame": "fideospultsjes", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "spulhallen", "acnh": "acnh", "puffpals": "puffmaten", "farmingsimulator": "buorkerijsimulator", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxdútslân", "robloxdeutsch": "robloxdútsk", "erlc": "erlc", "sanboxgames": "sanboxspullen", "videogamelore": "fideospults<PERSON>lea<PERSON>", "rollerdrome": "rollerdrome", "parasiteeve": "parasyteve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "skemerboskje", "dreamscape": "dreamlânskip", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "deaderomte", "amordoce": "leafdeswiet", "videogiochi": "fideospultsjes", "theoldrepublic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videospiele": "videospultsjes", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "aventuerspullen", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "fer<PERSON><PERSON>fan<PERSON><PERSON><PERSON><PERSON>n", "retrogames": "retrogames", "retroarcade": "retroarkade", "vintagecomputing": "âldekompjûters", "retrogaming": "retrogaming", "vintagegaming": "retrogaming", "playdate": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "ûnrjocht2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "loftspul", "zenlife": "zen<PERSON><PERSON>n", "beatmaniaiidx": "beatmaniaiidx", "steep": "steil", "mystgames": "<PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "blockchaingaming", "medievil": "medievil", "consolegaming": "konsolegamen", "konsolen": "konsolen", "outrun": "fuortrenne", "bloomingpanic": "bloeipanik", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "gaming<PERSON>reur", "monstergirlquest": "monsterfamkesquest", "supergiant": "supergigant", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "boerderijsims", "juegosviejos": "âldespultsjes", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "ynteraktyffyksje", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "leafdegekkeguodden", "visualnovel": "visualnovel", "visualnovels": "visualnovels", "rgg": "rgg", "shadowolf": "sk<PERSON><PERSON>wolf", "tcrghost": "tcrgeast", "payday": "<PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "skemerprinses", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sânbak", "aestheticgames": "estetyskespultsjes", "novelavisual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON>", "leafblowerrevolution": "blêdblazersrevolúsje", "wiiu": "wiiu", "leveldesign": "levelûntwerp", "starrail": "starrail", "keyblade": "keyblade", "aplaguetale": "inpesteske", "fnafsometimes": "fnafsoms", "novelasvisuales": "fisueleromans", "robloxbrasil": "robloxbrasyl", "pacman": "pacman", "gameretro": "retrogame", "videojuejos": "videospultsjes", "videogamedates": "videogamedates", "mycandylove": "mynleafdesnoep", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkspullen", "batmangames": "batmanspullen", "returnofreckoning": "weromkomstfanrekkening", "gamstergaming": "gamstergaming", "dayofthetantacle": "dayofthetantacle", "maniacmansion": "maniacmansion", "crashracing": "crashracen", "3dplatformers": "3dplatformers", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "oldschoolgaming", "hellblade": "helf<PERSON><PERSON>", "storygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "lûddûker", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "gammebrûke", "offmortisghost": "fanmor<PERSON><PERSON><PERSON>", "tinybunny": "lytshazze", "retroarch": "retroarch", "powerup": "powerup", "katanazero": "katananul", "famicom": "famicom", "aventurasgraficas": "aventuergrafysk", "quickflash": "fluchflits", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarkades", "f123": "f123", "wasteland": "w<PERSON><PERSON><PERSON><PERSON>", "powerwashsim": "drukspuitsim", "coralisland": "koraaleilân", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "fuotbalfú<PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "twistmetaal", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "stapelskande", "simulator": "simulator", "symulatory": "simulatoren", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wonderlândonline", "skylander": "skylander", "boyfrienddungeon": "freonsjesdungeon", "toontownrewritten": "toontownopnij", "simracing": "simracing", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "stedskchaos", "heavenlybodies": "himelskelichems", "seum": "seum", "partyvideogames": "feestfideospultsjes", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "rom<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hak<PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "itenenfideospults<PERSON>s", "oyunvideoları": "oyunvideoas", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "frachtweinsimulator", "horizonworlds": "horizon<PERSON><PERSON><PERSON><PERSON><PERSON>", "handygame": "handichspultsje", "leyendasyvideojuegos": "leyendasyvideojuegos", "oldschoolvideogames": "oldskoolfidjogames", "racingsimulator": "racesimulator", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentenfanmayhem", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "poartenvanolympus", "monsterhunternow": "monsterhunternow", "rebelstar": "<PERSON><PERSON><PERSON><PERSON>", "indievideogaming": "yndiefideogaming", "indiegaming": "indiegaming", "indievideogames": "indiecomputerspullen", "indievideogame": "indyfideospultsje", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "bufffort", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "projectl", "futureclubgames": "futureklupspullen", "mugman": "mokman", "insomniacgames": "slapeloazespultsjes", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "efterstân", "gamebacklog": "spultsjesachterstan", "gamingbacklog": "gamingefterop", "personnagejeuxvidéos": "fideospulkarakter", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "stêdskylines", "supermonkeyball": "supermon<PERSON><PERSON><PERSON>l", "deponia": "deponia", "naughtydog": "ûndeugendehûn", "beastlord": "beast<PERSON><PERSON>", "juegosretro": "retrospultsjes", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "dopaminereservoar", "staxel": "staxel", "videogameost": "fideospulmuzykost", "dragonsync": "d<PERSON><PERSON><PERSON>", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ikhâldfankofxv", "arcanum": "arka<PERSON>m", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "earsted", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "trystanime", "darkerthanblack": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "animescaling", "animewithplot": "animemeiplot", "pesci": "pesci", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80s", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "<PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseizoen1", "rapanime": "rapanime", "chargemanken": "opladenmankje", "animecover": "animecover", "thevisionofescaflowne": "devisionofescaflowne", "slayers": "slayers", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "bananaanfisk", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "wcbûnehanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "<PERSON><PERSON><PERSON><PERSON>", "fireforce": "f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "moriartydepatriot", "futurediary": "takomstdagboek", "fairytail": "me<PERSON>e", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "mak<PERSON>ndeôfgrûn", "parasyte": "parasyt", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "mearminmelody", "kamisamakiss": "<PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "romantyskemangas", "karneval": "karnaval", "dragonmaid": "d<PERSON>ak<PERSON><PERSON>", "blacklagoon": "swartlagune", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "genieinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "insekeremagyskeyndeks", "sao": "sao", "blackclover": "swartklaverblêd", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "ingelenfandedea", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosemik", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monsterfamke", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "a<PERSON>winss<PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagafantanyahetkweade", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "fistfantnoarden", "mazinger": "mazinger", "blackbuttler": "swartbutler", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "<PERSON><PERSON><PERSON><PERSON><PERSON>gas<PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "gefjochtstoppe", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "hiscoremeid", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "nultwa", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "monsterfaam", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON><PERSON>", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "kleimo<PERSON>", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "fruitsbasket", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "lovelive", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "thebelovednoaitlân", "monstermanga": "monstermanga", "yourlieinapril": "dynleugenynap<PERSON>", "buggytheclown": "buggydeclown", "bokunohero": "bokunohero", "seraphoftheend": "serafynfaneintsje", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "djipseefonge", "jojolion": "jojo<PERSON>", "deadmanwonderland": "deadmanwonderland", "bannafish": "banaanfisk", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON>ss<PERSON>l", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "itenwedstriden", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "duvelslinje", "toyoureternity": "foar<PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "blauweperioade", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "geheimealliânsje", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "wiske", "bluelock": "blaus<PERSON>", "goblinslayer": "goblinslayer", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampiersridder", "mugi": "mugi", "blueexorcist": "blauweeksorsist", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "sjoen", "spyfamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "airgear": "airgear", "magicalgirl": "magyskfamke", "thesevendeadlysins": "desânsûndenopdeadea", "prisonschool": "<PERSON><PERSON>iss<PERSON>alle", "thegodofhighschool": "degodvandemiddelbere", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mydressupdarling": "mynklaaipop", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "r<PERSON><PERSON><PERSON><PERSON>", "animeuniverse": "animeferiening", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoôfkoarte", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "ûndeapech", "romancemanga": "romantykmanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromantyk", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayerneiitswurd", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adioseri": "adjuses<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "stjerrenkommebyinoar", "romanceanime": "roman<PERSON>ske<PERSON>me", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saoyntegralfaktor", "cherrymagic": "<PERSON><PERSON><PERSON><PERSON><PERSON>nemaag<PERSON>", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "ragnarokrecord", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "highschoolofthedead", "germantechno": "dútsetechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prinsvant<PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "assassinklaskeamer", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON>arade", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japanskanime", "animespace": "animeteromte", "girlsundpanzer": "famljesenûnderpanzer", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indieanime": "y<PERSON>ean<PERSON>", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratman", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "<PERSON><PERSON><PERSON><PERSON>", "gashbell": "gashbell", "peachgirl": "pjukfaam", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquestdai": "dragonquestdai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "<PERSON><PERSON>yndekerker", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "recordofragnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "springe_nei_de_loafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialist<PERSON><PERSON><PERSON><PERSON>", "overgeared": "oerdreven", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "heksenhoedenatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "druppelsfangod", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animefamke", "reverseharem": "omkeardeharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "great<PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "sold<PERSON>t", "mybossdaddy": "my<PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "gr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "bloodcanime", "bloodc": "bloedgroep", "talesofdemonsandgods": "ferhalenoandimoanenengoadden", "goreanime": "goreanime", "animegirls": "animefamkes", "sharingan": "<PERSON><PERSON>", "crowsxworst": "kraaijenxslimste", "splatteranime": "splatteranime", "splatter": "spletter", "risingoftheshieldhero": "risingoftheshieldhero", "somalianime": "somalyanime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animeespanje", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjûnôsusume", "childrenofthewhales": "bernfandewalfisken", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "superkampioenen", "animeidols": "animeidols", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "magyske<PERSON><PERSON><PERSON><PERSON>", "callofthenight": "roepfandenacht", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "s<PERSON><PERSON>tún", "tsubasachronicle": "t<PERSON><PERSON>akron<PERSON>", "findermanga": "finderman<PERSON>", "princessjellyfish": "prinsesjûlfisk", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "parady<PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeferset", "persocoms": "persocoms", "omniscientreadersview": "alwittendlêzerssicht", "animecat": "animekater", "animerecommendations": "animerekommandaasjes", "openinganime": "iepening<PERSON>ime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "myntienerromantyskekomedy", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "bleach", "deathnote": "<PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "iis", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "militaireanime", "greenranger": "<PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animestêd", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "ûnsinnanime", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "mijnheldenacademie", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "ûndersykskorps", "onepieceanime": "onepieceanime", "attaquedestitans": "oanfalfandetitanen", "theonepieceisreal": "theonepieceisecht", "revengers": "wrekelingen", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "joyboyeffekt", "digimonstory": "digimonverhaal", "digimontamers": "digimontamers", "superjail": "<PERSON><PERSON><PERSON>", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ourangeselklub", "flawlesswebtoon": "flaterfrije_webtoon", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "flean<PERSON><PERSON><PERSON>s", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "samar", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "allehilligenstjitte", "recuentosdelavida": "fer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}