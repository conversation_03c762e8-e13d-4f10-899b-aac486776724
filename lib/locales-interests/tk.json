{"2048": "2048", "mbti": "mbti", "enneagram": "enneagramma", "astrology": "astrologiýa", "cognitivefunctions": "kognitiw_funksiýalar", "psychology": "psihologiýa", "philosophy": "<PERSON>los<PERSON><PERSON><PERSON><PERSON>", "history": "taryh", "physics": "fizika", "science": "ylym", "culture": "medeniýet", "languages": "diller", "technology": "tehnologi<PERSON><PERSON>", "memes": "memler", "mbtimemes": "mbt<PERSON><PERSON>", "astrologymemes": "astrolog<PERSON><PERSON><PERSON><PERSON><PERSON>", "enneagrammemes": "<PERSON>neagrammimler", "showerthoughts": "duşdakypikirler", "funny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videos": "wideolar", "gadgets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "politics": "politika", "relationshipadvice": "gatnaşykmaslahat", "lifeadvice": "ömürliktälimat", "crypto": "kripto", "news": "habarlar", "worldnews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "learning": "öwreniş", "debates": "debatlar", "conspiracytheories": "konspirasiyateoriyalary", "universe": "älem", "meditation": "meditasi<PERSON>a", "mythology": "mifologiýa", "art": "sungat", "crafts": "senetçilik", "dance": "tans", "design": "<PERSON><PERSON><PERSON><PERSON>", "makeup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beauty": "gözgülük", "fashion": "modanyň", "singing": "aýdymaýtmak", "writing": "ýazmak", "photography": "fotosurat", "cosplay": "<PERSON><PERSON><PERSON>ý", "painting": "surat", "drawing": "çyzgy", "books": "kitaplar", "movies": "kinolar", "poetry": "şygryýet", "television": "telewizor", "filmmaking": "filmsazlyk", "animation": "anima<PERSON><PERSON><PERSON>", "anime": "anime", "scifi": "fantastika", "fantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentaries": "dokumentalfilmler", "mystery": "gizlinlik", "comedy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crime": "<PERSON><PERSON><PERSON><PERSON>", "drama": "drama", "bollywood": "b<PERSON><PERSON><PERSON><PERSON>", "kdrama": "koreadrama", "horror": "<PERSON><PERSON><PERSON><PERSON>", "romance": "romantika", "realitytv": "hakykytelewizion", "action": "hereket", "music": "<PERSON><PERSON><PERSON><PERSON>", "blues": "bl<PERSON>uz", "classical": "klassyk", "country": "<PERSON><PERSON>", "desi": "desi", "edm": "edm", "electronic": "elektronika", "folk": "halk", "funk": "funk", "hiphop": "hiphop", "house": "ja<PERSON>", "indie": "indie", "jazz": "jaz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "pank", "rnb": "rnb", "rap": "rep", "reggae": "reggi", "rock": "rok", "techno": "tehno", "travel": "syýahat", "concerts": "kons<PERSON><PERSON>", "festivals": "festi<PERSON>ar", "museums": "<PERSON><PERSON><PERSON><PERSON>", "standup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theater": "teatr", "outdoors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gardening": "bagbançylyk", "partying": "hezilleşmek", "gaming": "<PERSON><PERSON><PERSON>", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chess": "<PERSON><PERSON><PERSON>", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "<PERSON>har", "baking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cooking": "aşpezlik", "vegetarian": "wegetarian", "vegan": "wegan", "birds": "g<PERSON><PERSON>lar", "cats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dogs": "itler", "fish": "balyk", "animals": "<PERSON><PERSON><PERSON><PERSON>", "blacklivesmatter": "garadurmyşlarynömrideglidir", "environmentalism": "daşkyguртdadolandyryş", "feminism": "feminizm", "humanrights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "lgbtqgoldaýjy", "stopasianhate": "<PERSON><PERSON>ýalylaragarsynenap<PERSON>ly<PERSON>ur", "transally": "transgoldaýjysy", "volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sports": "sport", "badminton": "badminton", "baseball": "beýsbol", "basketball": "basketbol", "boxing": "boks", "cricket": "kriket", "cycling": "welosipedsürmek", "fitness": "fitnes", "football": "futbol", "golf": "golf", "gym": "sport", "gymnastics": "gimnast<PERSON>", "hockey": "hokkey", "martialarts": "sözüşsungaty", "netball": "netbol", "pilates": "pilates", "pingpong": "pingpong", "running": "ylgamak", "skateboarding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiing": "lyžasürmek", "snowboarding": "snoub<PERSON>", "surfing": "sörfiň", "swimming": "ýüzmek", "tennis": "tennis", "volleyball": "woleýbol", "weightlifting": "agyrlykgötermek", "yoga": "ý<PERSON>", "scubadiving": "akwalangüýmek", "hiking": "gezelen<PERSON>", "capricorn": "<PERSON><PERSON>", "aquarius": "dow<PERSON><PERSON><PERSON>", "pisces": "balyk", "aries": "ýyldyz_söweşiji", "taurus": "öküz", "gemini": "gemini", "cancer": "rak", "leo": "leo", "virgo": "wirgo", "libra": "<PERSON><PERSON>i", "scorpio": "akyrep", "sagittarius": "sagittarius", "shortterm": "gysga_möhl<PERSON>li", "casual": "gündelik", "longtermrelationship": "uzakmöhletligatnaşyk", "single": "ýalňyz", "polyamory": "p<PERSON><PERSON><PERSON><PERSON>", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "geý", "lesbian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bisexual": "biseksual", "pansexual": "<PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "itwagapgöräniýär", "dislyte": "dislyte", "rougelikes": "rougel<PERSON><PERSON>", "kingsquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "ruh<PERSON><PERSON>j<PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "legend<PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "spyroejderha", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "güneşdüşýärbaragy", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "bökdençmez", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "açykdünýä", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "zyndangezmek", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribesofmidgard", "planescape": "uçaklandşaft", "lordsoftherealm2": "alagataglaýynlary2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "<PERSON><PERSON><PERSON><PERSON>", "medabots": "medabotlar", "lodsoftherealm2": "patyşanyňjennapetleri2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "juegoderol", "witcher": "<PERSON><PERSON><PERSON>", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "häsiýetdöretmek", "immersive": "çuňňur", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyköneusul", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "mor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "gü<PERSON>ş<PERSON>şyk", "otomegames": "otomeoyun<PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ok<PERSON><PERSON><PERSON><PERSON><PERSON>", "yiikrpg": "yiikrpg", "vampirethemasquerade": "wampirlerinmaskarady", "dimension20": "dimension20", "gaslands": "gazýurtlary", "pathfinder": "ýol<PERSON><PERSON>rk<PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2njyedisjy", "shadowrun": "kölegeuğraş", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "agyrlykgüýjüni<PERSON>üjümi", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "birsynap", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "<PERSON>ök<PERSON><PERSON>dar<PERSON>", "yourturntodie": "nobatdysaňöljek", "persona3": "persona3", "rpghorror": "rpggorkusy", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauderslar", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsöwezler", "mu": "mu", "falloutshelter": "ýadrobombasynyňdüşýänýeri", "gurps": "gurps", "darkestdungeon": "garaňkyziňdan", "eclipsephase": "tutulmabasgynç", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dinastiýasöweşijileri", "skullgirls": "skullgyzlar", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "däliligöreş", "jaggedalliance2": "jaggedalliance2", "neverwinter": "hiçwagtgyş", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelike_o<PERSON>unlar", "gothamknights": "gothamryçarl<PERSON>", "forgottenrealms": "ýatdandüşenälemler", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "multykilemmekany", "childoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ekopank", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "wulkanälem", "fracturedthrones": "döwüktagtlar", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "kölegepank", "finalfantasyxv": "finalfantasyxv", "everoasis": "hemişelikwaha", "hogwartmystery": "hogwartmystery", "deltagreen": "<PERSON>ýaşyl", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "soňkyepoka", "starfinder": "ýyldyztapyjy", "goldensun": "<PERSON><PERSON><PERSON>", "divinityoriginalsin": "diwinityoriginalsin", "bladesinthedark": "garaňkydapyçaklar", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "saýberpank", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "kiberpankkyzyl", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "düşengurluş", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "er<PERSON>ag<PERSON><PERSON><PERSON>", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "iblisdengankalany", "oldschoolrunescape": "könerunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "ylahy", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "k<PERSON>neдünýähasraty", "adventurequest": "başgeçirimgözlegi", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "r<PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofsymphonia": "talesofsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "ýyrtyklşäher", "myfarog": "meninfarogym", "sacredunderworld": "muka<PERSON><PERSON>ý<PERSON>sty", "chainedechoes": "ç<PERSON><PERSON>ý<PERSON><PERSON>", "darksoul": "garaňkyžan", "soulslikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "othercide": "ba<PERSON><PERSON><PERSON>", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "hronobosgatgyç", "pillarsofeternity": "ebediligiňsütünleri", "palladiumrpg": "palladiumrpg", "rifts": "riftler", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "sa<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "legendofdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "möje<PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "kiçijikagaç", "childrenofmorta": "mortanyň<PERSON>galary", "engineheart": "motorýürek", "fable3": "fable3", "fablethelostchapter": "fabletýitirilenbölüm", "hiveswap": "hiveswap", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "ýyldyzmydany", "oldschoolrevival": "köneusulözüne", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "ýabanyowaşlar", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "şalykýürek1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "garaňkylykzyndany", "juegosrpg": "juegosrpg", "kingdomhearts": "patyşalykÿürekleri", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "skiesofarcadia", "shadowhearts": "kölgelerkalby", "nierreplicant": "nierreplicant", "gnosia": "g<PERSON><PERSON>ý<PERSON>", "pennyblood": "gankaşa", "breathoffire4": "otodynyň4", "mother3": "ene3", "cyberpunk2020": "kiberpank2020", "falloutbos": "falloutbos", "anothereden": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "r<PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "jadyny<PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "ha<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spelljammer": "jädygöýüçi", "dragonageorigins": "dragonagebaşlangyjy", "chronocross": "hronogeçiş", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterowadünýä", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "shadowheartscovenant", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "patyşagetiň", "awplanet": "awplanet", "theworldendswithyou": "dünýäseniňbilengutarýar", "dragalialost": "dragalialostgame", "elderscroll": "ýaşulularýazgysy", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytaktika", "grandia": "grandia", "darkheresy": "garaňkybidgatçylyk", "shoptitans": "shopdükanlar", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackbook": "garagitap", "skychildrenoflight": "gökmelegiňçagalary", "gryrpg": "gryrpg", "sacredgoldedition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "castlecrashers": "<PERSON>ah<PERSON><PERSON><PERSON>", "gothicgame": "<PERSON><PERSON><PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "oýunrpg", "prophunt": "propganç", "starrails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofmist": "şäherdumanyndaky", "indierpg": "indierpg", "pointandclick": "görkezwebas", "emilyisawaytoo": "emili<PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "emili<PERSON><PERSON>", "indivisible": "aýrylmaz", "freeside": "<PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postkiberpunk", "deathroadtocanada": "kana<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "monstrçisi", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geoü<PERSON><PERSON><PERSON><PERSON>", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterösgüşi", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nonarygames": "ikilikdäloyunlar", "tacticalrpg": "taktikirpg", "mahoyo": "mahoyo", "animegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "hudaýyýyjy", "diluc": "diluk", "venti": "wenti", "eternalsonata": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "princessconnect": "princessconnect", "hexenzirkel": "hexenzirkel", "cristales": "krist<PERSON><PERSON>", "vcs": "wideokontaktlar", "pes": "pes", "pocketsage": "jübüdanalygysy", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorant<PERSON><PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efutbol", "nba2k": "nba2k", "egames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligasözleyanlaryň", "fifa14": "fifa14", "midlaner": "<PERSON><PERSON><PERSON><PERSON>", "efootball": "efutbol", "dreamhack": "dreamhack", "gaimin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overwatchleague": "overwatchligasy", "cybersport": "kibersport", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "san<PERSON><PERSON><PERSON><PERSON>", "brasilgameshow": "brazilgameshow", "valorantcompetitive": "valorantbäsdeşlik", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "ýarymö<PERSON><PERSON><PERSON>", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "k<PERSON><PERSON>", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "kozasimulýatory", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "erkinlikplaneta", "transformice": "transformice", "justshapesandbeats": "diňeşekillerweritimler", "battlefield4": "battlefield4", "nightinthewoods": "tokaýdagije", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanialar", "overcooked": "aşabişirilen", "interplanetary": "planetaarasy", "helltaker": "<PERSON><PERSON><PERSON>", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "okop", "stray": "azaşyp", "battlefield": "söweşme<PERSON>dany", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uçsuwgämisi", "eyeb": "gözöwül", "blackdesert": "garagum", "tabletopsimulator": "sto<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partyhard": "aýdymdöwlet", "hardspaceshipbreaker": "gatygämidöwüji", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinkum": "hakykylyk", "predecessor": "öňki", "rainworld": "ýag<PERSON>ş<PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "qudgowaklary", "colonysim": "kolo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "uruşyňdaňy", "minionmasters": "minionustatlar", "grimdawn": "grimdawn", "darkanddarker": "garaňkywegaraňky", "motox": "motox", "blackmesa": "gara<PERSON><PERSON><PERSON>", "soulworker": "ruhtarapçy", "datingsims": "aşkoyunlary", "yaga": "yaga", "cubeescape": "kubdangaçmak", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "täzejemgyýet", "citiesskylines": "şäherleri<PERSON>lary", "defconheavy": "defconagyr", "kenopsia": "k<PERSON>psiýa", "virtualkenopsia": "wirtual<PERSON><PERSON><PERSON><PERSON><PERSON>", "snowrunner": "garüzgüji", "libraryofruina": "ruinalaryň<PERSON>y", "l4d2": "l4d2", "thenonarygames": "ikilitdäloyunlar", "omegastrikers": "omegastrikers", "wayfinder": "ýoltapyjy", "kenabridgeofspirits": "keñköprülerruhlaryň", "placidplasticduck": "ýuwaşplastikördek", "battlebit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ultimatechickenhorse": "iňsoňkytowukaty", "dialtown": "şähergepleşik", "smileforme": "maňaüçingülümle", "catnight": "pişikgije", "supermeatboy": "supermeatboy", "tinnybunny": "kiçij<PERSON>ktowşan", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "heläkçilik", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubg": "pubg", "callofdutyzombies": "call<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "pala<PERSON><PERSON>", "earthdefenseforce": "ýerjümlesinigoragbrigadasy", "huntshowdown": "awşowdown", "ghostrecon": "ruhtaňçy", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakillöldürmek", "joinsquad": "toparagoşul", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "gozgalançylykgumtozan", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "ölümbaglanyşygy", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "kod<PERSON>mbi", "mirrorsedge": "<PERSON><PERSON><PERSON>", "divisions2": "bölümler2", "killzone": "öldürişzona", "helghan": "hel<PERSON>", "coldwarzombies": "sowukurşyzombi", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "krosscod", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "snayperэлит", "modernwarfare": "häzirkigüreş", "neonabyss": "neonçuňluk", "planetside2": "planetside2", "mechwarrior": "<PERSON><PERSON><PERSON><PERSON>", "boarderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "tarkowdangatmak", "metalslug": "metalslug", "primalcarnage": "yrtyjakzynsyz", "worldofwarships": "worldofwarships", "back4blood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "hitman", "masseffect": "masseffect", "systemshock": "sistemşok", "valkyriachronicles": "valkyriachronicles", "specopstheline": "harbyoperasiýaçyzygy", "killingfloor2": "killingfloor2", "cavestory": "gowakhekaýasy", "doometernal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "centuryageofashes": "ýüzýyllyňkülperwazy", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "bölüm2", "tythetasmaniantiger": "taýtasmaniýa<PERSON>s", "generationzero": "nesiljero", "enterthegungeon": "topançynyňiçinegir", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "sosiskaadam", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "ellikagry", "warface": "söweşý<PERSON>z", "crossfire": "çaprazot", "atomicheart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "wamp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "<PERSON><PERSON><PERSON>", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "frag", "tinytina": "kiçijiktina", "gamepubg": "gamepubg", "necromunda": "nekromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "fps<PERSON><PERSON>lary", "convertstrike": "özgertmezarbasy", "warzone2": "warzone2", "shatterline": "dargadylanhatdy", "blackopszombies": "blackopszombies", "bloodymess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "respublikakommandosy", "elitedangerous": "elitehowply", "soldat": "soldat", "groundbranch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "toparyň", "destiny1": "ykbal1", "gamingfps": "oýunfps", "redfall": "redfol", "pubggirl": "pubggyz", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "ýazyldy", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "aýlykgüni2", "cs16": "cs16", "pubgindonesia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "empyrion": "empyrion", "pubgczech": "pub<PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON>bal<PERSON>", "ghostcod": "arwahcod", "csplay": "kosplýa", "unrealtournament": "nerealturnirow", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "kontrudara", "cs2": "cs2", "pistolwhip": "pistolşarpma", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechempionlary", "halo3": "halo3", "halo": "halo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonbeyaz", "remnant": "<PERSON>yndy", "azurelane": "azurelane", "worldofwar": "uruş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "returnal", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "kölegeodam", "quake2": "quake2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "hereket", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "deňizgarakçylary", "rust": "pos", "conqueronline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dauntless": "gaýduwsyz", "warships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "uçuşyokary", "recroom": "recroom", "legendsofruneterra": "runeterra_rohatlary", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "fantazýýyldyzonlaýn2", "maidenless": "gyzalasyz", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "kesişdir", "agario": "agario", "secondlife": "ikinjiömür", "aion": "aion", "toweroffantasy": "fan<PERSON><PERSON>jawu<PERSON><PERSON><PERSON>", "netplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "<PERSON>haý<PERSON><PERSON><PERSON><PERSON>", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubpingwin", "lotro": "lotro", "wakfu": "wakfu", "scum": "garak<PERSON><PERSON><PERSON><PERSON>", "newworld": "tä<PERSON>ünýä", "blackdesertonline": "blackdesertonline", "multiplayer": "köpçülikdeoýun", "pirate101": "korsar101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarssöweşmeýdany", "karmaland": "karma<PERSON><PERSON>", "ssbu": "ssbu", "starwarsbattlefront2": "starwarssöweşmeýdany2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "p<PERSON><PERSON><PERSON><PERSON>", "3dchat": "3dgürrüňdeşlik", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklassik", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "ashesofcreationtm", "riotmmo": "riotmmo", "silkroad": "ýüpekýoly", "spiralknights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mulegend": "mulegend", "startrekonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grymmo": "grymmo", "warmane": "ýylyladam", "multijugador": "köpoýunçy", "angelsonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "growtopia": "growtopia", "starwarsoldrepublic": "starwarskönerespublika", "grandfantasia": "ulubelentfantaziý<PERSON>", "blueprotocol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworld": "mükemmeldünýä", "riseonline": "onlaýnöskdür", "corepunk": "corepank", "adventurequestworlds": "başdangeçirmelersoragy", "flyforfun": "uçgünlereýlenç", "animaljam": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "köçesöweşiji", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "üçşanşöhrat", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "wirtual<PERSON><PERSON><PERSON><PERSON><PERSON>", "streetsofrage": "köçeleri<PERSON>by", "mkdeadlyalliance": "mkölümyňyzly", "nomoreheroes": "<PERSON>na<PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "kingoffighterspadyş<PERSON>y", "likeadragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retroboksöýünleri", "blasphemous": "küpürlük", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "superyumruk", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "kiberbotlar", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "soňkysöweş", "poweredgear": "güýçlendirilenenjamlar", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "öldürýänduýgy", "kingoffigthers": "söweşijileriňşasy", "ghostrunner": "arwaçgüýji", "chivalry2": "şöhratgöýlik2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "ýüpeköýünarysy", "silksonggame": "silksongoýny", "silksongnews": "silksonghabarlary", "silksong": "ýüpekjysady", "undernight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "ýazuwlumina", "evolutiontournament": "ewolýusiýaturniri", "evomoment": "evodowatywagtym", "lollipopchainsaw": "çalgynyňgylyjy", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "<PERSON><PERSON><PERSON><PERSON>", "bloodborne": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizon": "gözýetim", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uncharted": "bellibirdäl", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "sonkymyzlar", "infamous": "<PERSON><PERSON><PERSON>", "playstationbuddies": "playstationýoldaşlar", "ps1": "ps1", "oddworld": "geňdünýä", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "<PERSON>geý<PERSON>", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "sögüştaňrysy", "gris": "gris", "trove": "hazyna", "detroitbecomehuman": "detroitadamöwü<PERSON><PERSON><PERSON><PERSON>", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "daňatança", "touristtrophy": "turistkubogy", "lspdfr": "lspdfr", "shadowofthecolossus": "kolossyňkölegesi", "crashteamracing": "crashteamracing", "fivepd": "bäşpd", "tekken7": "tekken7", "devilmaycry": "şeý<PERSON><PERSON>ag<PERSON><PERSON>", "devilmaycry3": "devilmaycry3", "devilmaycry5": "şeýtanaglaýar5", "ufc4": "ufc4", "playingstation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraiwarriors": "samura<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "soňkygoragçy", "soulblade": "j<PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "ad<PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "soňkygoragçy", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "oýungeçiş", "armello": "armello", "partyanimal": "partygöýümli", "warharmmer40k": "warhammer40k", "fightnightchampion": "söweşgijesiçempiony", "psychonauts": "ps<PERSON><PERSON><PERSON><PERSON>", "mhw": "mhw", "princeofpersia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "hemezatzat", "theelderscrolls": "elderscrolls", "gxbox": "gxbox", "battlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "açbilemaňoýny", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skeýt3", "houseflipper": "hausflipper", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "patyşalyklaryňligasy", "fable2": "fable2", "xboxgamepass": "xboxoýungeçidi", "undertale": "undertale", "trashtv": "bokuştelewizor", "skycotl": "skycotl", "erica": "erika", "ancestory": "<PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "kiçijikbägtsizkyz", "sallyface": "salliy<PERSON>z", "franbow": "franbow", "monsterprom": "monsterbaly", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motosikletler", "outerwilds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "<PERSON><PERSON><PERSON>", "cultofthelamb": "cultofthelamb", "duckgame": "ördekoyuny", "thestanleyparable": "stenliniňerteki", "towerunite": "bashyrlaýynbirlik", "occulto": "gizlin", "longdrive": "uzakyol", "satisfactory": "kanagatlandyryjy", "pluviophile": "ýagyşsöýüji", "underearth": "ýerasty", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalkosmosprogrammasy", "kenshi": "kenshi", "spiritfarer": "ruhdargyn", "darkdome": "garaňkygümmez", "pizzatower": "pizzatower", "indiegame": "<PERSON><PERSON><PERSON><PERSON>", "itchio": "itchio", "golfit": "<PERSON><PERSON><PERSON><PERSON>", "truthordare": "dogruçynyayalançyny", "game": "<PERSON><PERSON><PERSON>", "rockpaperscissors": "daşgagyçmaç", "trampoline": "trampolin", "hulahoop": "<PERSON><PERSON><PERSON><PERSON>", "dare": "batyrgay", "scavengerhunt": "gözlegoyun", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "sanseç", "trueorfalse": "hakykymyýalňyşmy", "beerpong": "piwopong", "dicegoblin": "zarçinpeýkamy", "cosygames": "rahatoyunlar", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "<PERSON><PERSON><PERSON>", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "oyunlar", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "simu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "sözsapaklary", "jeuxdemots": "jeuxdemots", "juegosdepalabras": "s<PERSON><PERSON>yunlary", "letsplayagame": "geleliňoýnalyň", "boredgames": "oýunlardanbezmek", "oyun": "oyun", "interactivegames": "interaktiwoyunlar", "amtgard": "amtgard", "staringcontests": "gözgaraşdyrmaýarşlar", "spiele": "<PERSON><PERSON><PERSON><PERSON>", "giochi": "giochi", "geoguessr": "geoguessr", "iphonegames": "<PERSON>ýfon<PERSON><PERSON>", "boogames": "boogames", "cranegame": "krankuýsy", "hideandseek": "gizlenmeoyuny", "hopscotch": "sek<PERSON>me", "arcadegames": "<PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mindgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "sözleriniçak", "galagames": "galagames", "romancegame": "söýgioýny", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "dildöwürler", "4xgames": "4xoýunlar", "gamefi": "gamefi", "jeuxdarcades": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "oýunlar90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "hakykyvsgalp", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "resimdüşündirme", "coopgames": "bileleş<PERSON><PERSON>", "jenga": "jenga", "wiigames": "wiio<PERSON><PERSON>lary", "highscore": "ýokarygol", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "burger<PERSON><PERSON><PERSON>lary", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwgaraedisýa", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "gioco", "managementgame": "dolandyryşoýny", "hiddenobjectgame": "gizlinzatlaryoyuny", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formula1oýny", "citybuilder": "şähergurujy", "drdriving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blowgames": "<PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "pinball<PERSON><PERSON><PERSON><PERSON>", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "diwandaişleşik", "perguntados": "soraglar", "gameo": "<PERSON><PERSON><PERSON>", "lasergame": "lazeroýun", "imessagegames": "imessage<PERSON><PERSON><PERSON><PERSON>", "idlegames": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "boşlugyň_doldur", "jeuxpc": "jeuxpc", "rétrogaming": "rétrogaming", "logicgames": "mantykoyunlar", "japangame": "ýaponiýaoyuny", "rizzupgame": "oýnuňyöwürdip", "subwaysurf": "metrosurf", "jeuxdecelebrite": "jeuxdecelebrite", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5.00E+05", "rolgame": "rol<PERSON>", "dashiegames": "dashiegames", "gameandkill": "<PERSON>ý<PERSON>we<PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "dä<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "kniffel": "kniffel", "gamefps": "oyunfps", "textbasedgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantaziýafutbol", "retrospel": "öňküoýun", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tischfußball": "tischfußball", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "ga<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "kranoyunlary", "játék": "<PERSON><PERSON><PERSON>", "bordfodbold": "stoluňfutboly", "jogosorte": "jogosorte", "mage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cargames": "awtomob<PERSON>yunlary", "onlineplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "oýungijeleri", "pursebingos": "sumkabingolar", "randomizer": "tasadypy", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "gameskomp", "socialdeductiongames": "sosialçaklykoyunlary", "dominos": "dominos", "domino": "domino", "isometricgames": "izometrikioýunlar", "goodoldgames": "könegazoýunlar", "truthanddare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "gözlegagtaryşlar", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "<PERSON><PERSON><PERSON>", "fantasygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "driftgame": "driftoyuny", "gamesotomes": "oyunlarotome", "halotvseriesandgames": "haloteleseriallarweoyunlar", "mushroomoasis": "kömelekbagynyň", "anythingwithanengine": "hereketlendirijiliherzat", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "gylyçwejadygöýlük", "goodgamegiving": "gowioýunbermek", "jugamos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "grykomputerowe", "virgogami": "virgogami", "gogame": "oýnagitjek", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "özüňisöýmekgaming", "gamemodding": "oýunüýtgetmek", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "dobbelspellen", "spelletjes": "<PERSON><PERSON><PERSON><PERSON>", "spacenerf": "kos<PERSON><PERSON><PERSON>", "charades": "ç<PERSON>dy", "singleplayer": "ýekeoý<PERSON>y", "coopgame": "kooperatiwoýun", "gamed": "oýnadym", "forzahorizon": "forzahorizon", "nexus": "neksis", "geforcenow": "geforcenow", "maingame": "esasygry", "kingdiscord": "kingdiscord", "scrabble": "scrabble", "schach": "<PERSON><PERSON><PERSON>", "shogi": "<PERSON><PERSON><PERSON>", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "nard", "onitama": "onitama", "pandemiclegacy": "pande<PERSON><PERSON><PERSON><PERSON>", "camelup": "düýeüstünde", "monopolygame": "monopoly<PERSON>ý<PERSON>", "brettspiele": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "<PERSON><PERSON><PERSON><PERSON>", "planszowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "risiko": "töwekgelçilik", "permainanpapan": "<PERSON>ý<PERSON><PERSON><PERSON>", "zombicide": "zombisid", "tabletop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "gantopu", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "sto<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectfour": "dördünibirleşdir", "heroquest": "gahrymançylyk", "giochidatavolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "karom", "tablegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "<PERSON><PERSON><PERSON>", "parchis": "par<PERSON><PERSON>", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "jemgyýetçilik<PERSON><PERSON><PERSON>", "deskgames": "<PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "ma<PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosmicencounter": "älemgoşulşygy", "creationludique": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "kommutatortablooýunlary", "infinitythegame": "infinityoýny", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "ýokarydaaşakda", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "juegodemesa", "planszówki": "<PERSON><PERSON><PERSON><PERSON>", "rednecklife": "rednekdurm<PERSON>şy", "boardom": "içgysgynçsyzlyk", "applestoapples": "<PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "jeudesociété", "gameboard": "<PERSON>ý<PERSON><PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "krokinol", "jeuxdesociétés": "jeuxdesociétés", "twilightimperium": "şapakimperiýasy", "horseopoly": "atmonopoliýa", "deckbuilding": "güýçlendirişdüzme", "mansionsofmadness": "dälilikk<PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "brimstounkölegeleri", "kingoftokyo": "tokyoşasy", "warcaby": "warcaby", "táblajátékok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tickettoride": "bileteautobus", "deskovehry": "stolus<PERSON><PERSON><PERSON>", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "j<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "stolníhry", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "jemgyýetçilik_oýunlary", "starwarslegion": "ýyldyzurşularylegiony", "gochess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "jeuxdes<PERSON>iet<PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "warzone", "arksurvivalevolved": "arksurvivalevolved", "dayz": "<PERSON><PERSON><PERSON><PERSON>", "identityv": "identityv", "theisle": "ada", "thelastofus": "biziniňsoňkumyz", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautika", "tombraider": "tombraider", "callofcthulhu": "cthulhunyňçagyryşy", "bendyandtheinkmachine": "bendyweçernile<PERSON>y", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eco": "eko", "monkeyisland": "maý<PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "planetad<PERSON><PERSON><PERSON>", "daysgone": "günlergeçdi", "fobia": "fob<PERSON><PERSON><PERSON>", "witchit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathologic": "patalogik", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7günlükölüm", "thelongdark": "uzakgaraňkylyk", "ark": "arka", "grounded": "<PERSON><PERSON><PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "<PERSON><PERSON><PERSON><PERSON>", "dontstarve": "açgalma", "eternalreturn": "baky_ga<PERSON><PERSON><PERSON>", "pathoftitans": "titanlarý<PERSON>ýoly", "frictionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theevilwithin": "içiňdäkiýamanlyk", "realrac": "hakykyýaryş", "thebackrooms": "arkaotaglar", "backrooms": "yzkygözotag", "empiressmp": "empiressmp", "blockstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thequarry": "<PERSON><PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "ölýänýagtylyk", "thewalkingdeadgame": "ölüleriňoýny", "wehappyfew": "bi<PERSON><PERSON><PERSON><PERSON>", "riseofempires": "imperiýalaryňgalmagy", "stateofsurvivalgame": "stateofsurvivaloýny", "vintagestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvival": "arkýaşaýyş", "barotrauma": "bar<PERSON><PERSON><PERSON>", "breathedge": "demalmagyň_gyrasy", "alisa": "alisa", "westlendsurvival": "westlen<PERSON>_galan_dirimiz", "beastsofbermuda": "bermudanyň<PERSON>lary", "frostpunk": "frostpunk", "darkwood": "gara<PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "jansaklamagorky", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "ýaşaýyş<PERSON>n", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "bum<PERSON><PERSON>", "scpfoundation": "sc<PERSON><PERSON><PERSON><PERSON><PERSON>", "greenproject": "ýaşylproýekt", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "sal", "rdo": "rdo", "greenhell": "ýaşylpozgyn", "residentevil5": "residentevil5", "deadpoly": "ölipoly", "residentevil8": "residentevil8", "onironauta": "onironawt", "granny": "ene", "littlenightmares2": "kiçijikgijegorkular2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "tokaýyňogullary", "rustvideogame": "rust<PERSON><PERSON><PERSON><PERSON>", "outlasttrials": "outlast<PERSON><PERSON><PERSON>lary", "alienisolation": "gel<PERSON>şek<PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "irdaň", "7day2die": "7günde2ölmek", "sunlesssea": "günsizdeňiz", "sopravvivenza": "sopravvivenza", "propnight": "döwranýörese", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "owadanwampir", "deathverse": "ölümälem", "cataclysmdarkdays": "kataklizmagaraň<PERSON>ü<PERSON>ler", "soma": "soma", "fearandhunger": "gorkyweaçlyk", "stalkercieńczarnobyla": "stalkertüýnüksöweşijisi", "lifeafter": "döwrümizdensoň", "ageofdarkness": "garaňkylykasyry", "clocktower3": "sagatkulesi3", "aloneinthedark": "garaňkydaýeke", "medievaldynasty": "ortaasyrdynastiýasy", "projectnimbusgame": "projectnimbusgame", "eternights": "ebedigijeleriň", "craftopia": "senetçilikdünyäsi", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "d<PERSON><PERSON><PERSON><PERSON>basylyşy", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "resmytarapdanöld<PERSON><PERSON><PERSON>", "necron": "nekron", "wfrp": "wfrp", "dwarfslayer": "ýüräşiköldüriji", "warhammer40kcrush": "warhammer40kgöwnümdäki", "wh40": "wh40", "warhammer40klove": "warhammer40ksöýgi", "warhammer40klore": "warhammer40klory", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "öçalmak", "ilovesororitas": "sororitalaryňksöýýärin", "ilovevindicare": "ilovevindicare", "iloveassasinorum": "menassasi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40müň", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "ganatgenişligi", "terraformingmars": "marsyözgertmek", "heroesofmightandmagic": "güýçwejadunyňgahrymanları", "btd6": "btd6", "supremecommander": "<PERSON><PERSON><PERSON><PERSON>", "ageofmythology": "mifologiýae<PERSON>", "args": "args", "rime": "rime", "planetzoo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outpost2": "outpost2", "banished": "kowuldy", "caesar3": "sezar3", "redalert": "gyzyld<PERSON><PERSON><PERSON><PERSON>ş", "civilization6": "siwilizasiýa6", "warcraft2": "warcraft2", "commandandconquer": "buýruketweözüňebuýsundur", "warcraft3": "warcraft3", "eternalwar": "bakyjasöweş", "strategygames": "strategi<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "si<PERSON><PERSON><PERSON>iý<PERSON>ý<PERSON>", "civilization4": "medeniýet4", "factorio": "factorio", "dungeondraft": "ý<PERSON>zeminja<PERSON>", "spore": "spor", "totalwar": "<PERSON><PERSON><PERSON><PERSON>", "travian": "travian", "forts": "<PERSON><PERSON><PERSON>", "goodcompany": "<PERSON>wy<PERSON><PERSON><PERSON>", "civ": "medeniýet", "homeworld": "öýdünýä", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "ygtydançalt", "forthekings": "şalararü<PERSON>in", "realtimestrategy": "realtimestrategy", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "patsalykikikorona", "eu4": "eu4", "vainglory": "gopbamçylyk", "ww40k": "ww40k", "godhood": "taňrylyk", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "üçlük", "davesfunalgebraclass": "<PERSON>veñ<PERSON>ňgyçalgebraklas", "plagueinc": "plagueinc", "theorycraft": "teoriýadöretmek", "mesbg": "mesbg", "civilization3": "sivilizasiýa3", "4inarow": "4setirde", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "söweşharplary", "ageofempires2": "ageofempires2", "disciples2": "çägirtdäler2", "plantsvszombies": "ösümliklergarşyzombi", "giochidistrategia": "strategi<PERSON><PERSON><PERSON><PERSON><PERSON>", "stratejioyunları": "stratejioyunları", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "dinozawrpatyşasy", "worldconquest": "dünýäbasypalyş", "heartsofiron4": "heartsofiron4", "companyofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gazgazördek", "phobies": "fob<PERSON>lar", "phobiesgame": "fobiyalaroyuny", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "krestonosypatşalar", "cultris2": "cultris2", "spellcraft": "jadygöýlük", "starwarsempireatwar": "starwarsimperiiaurussy", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategi<PERSON><PERSON>", "popfulmail": "popfulepoç<PERSON>", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "masterduel", "dysonsphereprogram": "dysonsferaprogrammasy", "transporttycoon": "transportmagnat", "unrailed": "relsdendüşdüm", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "uçaktürkmenistanhasraty", "uplandkingdoms": "ýokarydagpatyşalyklary", "galaxylife": "galaktikadurmuş", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespiretk", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "tizlikgerekligi", "needforspeedcarbon": "needforspeedcarbon", "realracing3": "hakykyýaryş3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "dowa<PERSON><PERSON>", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "garaňkyatlygyntama", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "bäşgijefreddinnikinde", "saiko": "saiko", "fatalframe": "elhenççarçuwa", "littlenightmares": "kiçijikgijekiyalamalar", "deadrising": "ölülerdirelýär", "ladydimitrescu": "ladydi<PERSON><PERSON><PERSON>", "homebound": "öýdeotyr", "deadisland": "ölüada", "litlemissfortune": "litlemissbagt", "projectzero": "proýektnol", "horory": "<PERSON><PERSON><PERSON><PERSON>", "jogosterror": "jogöçgünjilik", "helloneighbor": "hellosalam", "helloneighbor2": "salamsalamgoňşy2", "gamingdbd": "oýundbd", "thecatlady": "pişikgelneje", "jeuxhorreur": "j<PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "gorkygaming", "magicthegathering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kartlaradamzarlygyna", "cribbage": "<PERSON><PERSON><PERSON><PERSON>", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinoçl", "codenames": "kodatlar", "dixit": "dixit", "bicyclecards": "welosipedkartoçkalary", "lor": "lor", "euchre": "eukre", "thegwent": "gwent", "legendofrunetera": "runeranyňrowaýaty", "solitaire": "solit<PERSON><PERSON>", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "karttrikler", "playingcards": "oýunkartalary", "marvelsnap": "marvelsnap", "ginrummy": "gin<PERSON>i", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "sow<PERSON><PERSON><PERSON>", "pokemoncards": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "köpükdeniganytcg", "sportscards": "sportkartlary", "cardfightvanguard": "cardfightvanguard", "duellinks": "duel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spades": "️", "warcry": "<PERSON>ö<PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "ýüreklerşasy", "truco": "truko", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yug<PERSON>hkartlary", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohoyuny", "darkmagician": "garaňkyžadygär", "blueeyeswhitedragon": "gökgözliakejderha", "yugiohgoat": "yugiohgoat", "briscas": "brisk<PERSON><PERSON>", "juegocartas": "kart_oýny", "burraco": "burrako", "rummy": "remmi", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgkommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "kartoyunlary", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgprekonkommander", "kartenspiel": "kartenspiel", "carteado": "carteado", "sueca": "sueka", "beloteonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "söweşruhl<PERSON>", "battlespiritssaga": "söweşruhlaryýygyndysy", "jogodecartas": "kartaoyuny", "žolíky": "žolíky", "facecard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfight": "kartsöweşi", "biriba": "biriba", "deckbuilders": "göwerç<PERSON>ý<PERSON>", "marvelchampions": "<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magiccartas": "jadykartoçkalary", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "durnuksyzbirgoçlar", "cyberse": "siber", "classicarcadegames": "klassikarkadaoýunlary", "osu": "osu", "gitadora": "gitadora", "dancegames": "oýuntanslar", "fridaynightfunkin": "<PERSON>nag<PERSON>nagarahez<PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "proýektmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "klong<PERSON>y", "justdance": "tañsadyňla", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "roksmit", "idolish7": "idolish7", "rockthedead": "ölülerioynat", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "tan<PERSON><PERSON><PERSON><PERSON><PERSON>", "rhythmgamer": "rit<PERSON>ý<PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "ýokarygutargyritmoyunlary", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "rit<PERSON><PERSON>y", "hypmic": "hypmic", "adanceoffireandice": "odowyebuzuňtansы", "auditiononline": "<PERSON><PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "kriptanecrodancer", "rhythmdoctor": "ritmlukmany", "cubing": "kubikleme", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spotit": "tapyň", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blokdoku", "logicpuzzles": "logikbasgöşükler", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "kellegoşgular", "rubikscube": "<PERSON><PERSON><PERSON><PERSON>", "crossword": "kros<PERSON>", "motscroisés": "motscroisés", "krzyżówki": "kross<PERSON>lar", "nonogram": "nonogram", "bookworm": "kitapçy", "jigsawpuzzles": "jigsaw_bukjalar", "indovinello": "<PERSON><PERSON><PERSON>", "riddle": "bilmeje", "riddles": "<PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "içinde", "angrybirds": "gah<PERSON>ly<PERSON><PERSON><PERSON>", "escapesimulator": "gaçyşsimulyatory", "minesweeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzleanddragons": "puzzlewedragons", "crosswordpuzzles": "sözkesişmeler", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesoyuny", "puzzlesport": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "gaçyşotagyoýunlary", "escapegame": "ýitimoyuny", "3dpuzzle": "3dbulmaca", "homescapesgame": "homescapesoyuny", "wordsearch": "sözsözlemek", "enigmistica": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "balykpatyşalygy", "theimpossiblequiz": "mümkinbolmadyksorag", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "matç3bulmaja", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikcube": "<PERSON><PERSON><PERSON>", "cuborubik": "kubikrubik", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "tabşyrygyňy", "tycoongames": "magnatoyunlary", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON>", "cruciverba": "kross<PERSON>", "ciphers": "<PERSON><PERSON><PERSON><PERSON>", "rätselwörter": "rätselwörter", "buscaminas": "buscaminas", "puzzlesolving": "çözgütçözýär", "turnipboy": "turpoglan", "adivinanzashot": "tapmacahowduk", "nobodies": "<PERSON><PERSON><PERSON><PERSON>", "guessing": "takmynetmek", "nonograms": "nonogramlar", "kostkirubika": "kostkirubika", "crypticcrosswords": "düşündirýänkrosswordlar", "syberia2": "syberia2", "puzzlehunt": "gapalmagaçgözleg", "puzzlehunts": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "pişik<PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "kelle<PERSON><PERSON>", "hlavolamy": "başagrylary", "poptropica": "<PERSON><PERSON>pi<PERSON>", "thelastcampfire": "soňkýotýagty", "autodefinidos": "autodefinidos", "picopark": "picopark", "wandersong": "gezelenkaý<PERSON>ž", "carto": "karto", "untitledgoosegame": "görkezilmedikgözguş", "cassetête": "kelleagyry", "limbo": "limbo", "rubiks": "rubiks", "maze": "labirint", "tinykin": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikovakostka": "rubikowkubik", "speedcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pieces": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portalgame": "portaloýny", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "pik<PERSON>", "rubixcube": "rubikskubik", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "k<PERSON><PERSON><PERSON><PERSON>", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "geňüýtgeşikdünýä", "monopoly": "monopoliýa", "futurefight": "geljekdekisöweş", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "ýalňyzböri", "gacha": "gaça", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>bly", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemyýyldyzlar", "stateofsurvival": "ýaşaýyşýagdaýy", "mycity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "reňklisahna", "bloonstowerdefense": "bloonsgalabyrany", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "rysarsöwdasy", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "futbolsöweşi", "a3": "a3", "phonegames": "telefonyunlary", "kingschoice": "<PERSON><PERSON><PERSON><PERSON>", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "ben<PERSON><PERSON>", "tacticool": "taktikisupergowy", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "habarsyz", "craftsman": "ussady", "supersus": "supergümanly", "slowdrive": "haýaldraýw", "headsup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wordfeud": "sözsöweşi", "bedwars": "bedwars", "freefire": "freefire", "mobilegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "lilybagy", "farmville2": "farmville2", "animalcrossing": "haýwangeçelgesi", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "arkana", "8ballpool": "8topbilýard", "emergencyhq": "how<PERSON>slykt<PERSON><PERSON><PERSON><PERSON>", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "günortasy", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "titrem<PERSON><PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "wagt<PERSON><PERSON><PERSON><PERSON>", "beatstar": "beatstar", "dragonmanialegend": "ejderhaokgunçligi", "hanabi": "hanabi", "disneymirrorverse": "dis<PERSON><PERSON>", "pocketlove": "jübüňdeýarsöýgi", "androidgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "criminalcase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "naharpisirmekjeňňeligi", "dokkan": "dokkan", "aov": "aov", "triviacrack": "bilimsorag", "leagueofangels": "perişdelerligasy", "lordsmobile": "lordsmobile", "tinybirdgarden": "kiçijikguşbagçasy", "gachalife": "gaçalife", "neuralcloud": "neýralbulut", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "gökmawyzarhiw", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "aýnaälem", "pou": "pou", "warwings": "söweşuçarlary", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "güýçliklezzetlikwagt", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "<PERSON><PERSON><PERSON>", "slugitout": "söweşipçyk", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "soltanlaryň<PERSON>", "arenabreakout": "arenabreakout", "wolfy": "möjek", "runcitygame": "şäheroyunyylaşdyr", "juegodemovil": "juegodemovil", "avakinlife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kogama": "kogama", "mimicry": "mi<PERSON><PERSON>", "blackdesertmobile": "garagumçölmobil", "rollercoastertycoon": "rollerkoastertaýkun", "grandchase": "grandchase", "bombmebrasil": "bombmebrazil", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegame": "otom<PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "ejderahalarynyçagyryşy", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftyaryş2", "pathtonowhere": "ýokyol", "sealm": "sealm", "shadowfight3": "kölegedesöweş3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolisionderbi3", "wordswithfriends2": "dostlarbilensözler2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "pörtdikenhekaýa", "showbyrock": "rokmuzygygörkeziş", "ladypopular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "ajaýypdünýämobil", "empiresandpuzzles": "imperiýalarwebulmacalar", "empirespuzzles": "imperiýalarpazzllar", "dragoncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobilehindistan", "fanny": "göwünjeň", "littlenightmare": "kiçijikkäbuş", "aethergazer": "aethergazer", "mudrunner": "balgatykyjy", "tearsofthemis": "tearofthemisowlary", "eversoul": "hemişelikruh", "gunbound": "gunbound", "gamingmlbb": "oýunmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombilar_sürgünde", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "naharişeňňe", "cabalmobile": "cabalmobile", "streetfighterduel": "köçeurşy", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsfrontline": "gyzlarfrontlaýny", "jurassicworldalive": "jurassikdünýäsidiri", "soulseeker": "jan<PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "geçipbarýarys", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "<PERSON>ý<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "carxdr<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosmobile": "oýunlarmobil", "legendofneverland": "legendofneverland", "pubglite": "pubglite", "gamemobilelegends": "oýunmobayllegendalar", "timeraiders": "wagt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmobile": "mobiloyun", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "pişiklerinsöweşi", "dnd": "<PERSON><PERSON><PERSON><PERSON>", "quest": "kwäst", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "stolunyňrpg", "worldofdarkness": "garaňkylykdünýäsi", "travellerttrpg": "syýahatçyrpg", "2300ad": "2300ýyl", "larp": "larp", "romanceclub": "romantikklub", "d20": "d20", "pokemongames": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemonhrustal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemongyrmyzy", "pokemongo": "pokemongo", "pokemonshowdown": "poke<PERSON>ý<PERSON>ş", "pokemonranger": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonbirleş", "entai": "entai", "hypno": "gipnoz", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "çatot", "pikachu": "<PERSON><PERSON><PERSON><PERSON>", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "aşketçum", "gengar": "gengar", "natu": "natu", "teamrocket": "toparrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlaks", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokeball": "pokeball", "charmander": "<PERSON><PERSON><PERSON>", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "demireller", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "pokemonuky", "heyyoupikachu": "heýsenpikaçu", "pokémonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "çagalarhempokemon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "çarysar", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "çatur", "xadrez": "k<PERSON>şt", "scacchi": "k<PERSON>şt", "schaken": "<PERSON><PERSON><PERSON>", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "şahmatgyzlar", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "jeudéchecs", "japanesechess": "japanşahmat", "chinesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>", "rook": "ruh", "chesscom": "chesscom", "calabozosydragones": "zyndanlarweajdarlar", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "dungynözegçi", "tiamat": "tiamat", "donjonsetdragons": "zindan_ejderha", "oxventure": "oxventure", "darksun": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "voxmaçinanyňlegendasy", "doungenoanddragons": "dndoýnamaýarsyňmy", "darkmoor": "garaňkybatga", "minecraftchampionship": "maýnkraftçempionaty", "minecrafthive": "maýnkrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodlary", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "süýjini<PERSON><PERSON>", "addons": "goşmaçalar", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblok", "minecraftpocket": "ma<PERSON>nkraftj<PERSON><PERSON>", "minecraft360": "minecraft360", "moddedminecraft": "üýtgedilenminecrafter", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "maýnkraftsiti", "pcgamer": "pcgamer", "jeuxvideo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gambit": "gambit", "gamers": "oyunçylar", "levelup": "derej<PERSON><PERSON><PERSON><PERSON>", "gamermobile": "geýmermobaýl", "gameover": "oýuntamamlandy", "gg": "gg", "pcgaming": "kom<PERSON><PERSON><PERSON><PERSON>ýunlary", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "kom<PERSON><PERSON><PERSON><PERSON>ýunlary", "casualgaming": "ýeňilçeo<PERSON>", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmasterrace", "pcgame": "kompýuteroyun", "gamerboy": "oýunçyoglan", "vrgaming": "wrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kgeýmler", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "konsoloyunçy", "boxi": "boxi", "pro": "pro", "epicgamers": "<PERSON>pik<PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "semigamer": "ýarymoyunçy", "gamergirls": "oýunçygyzlar", "gamermoms": "gameneneler", "gamerguy": "gamer_ýigit", "gamewatcher": "oyunsynçy", "gameur": "oýunçy", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerkyzlar", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "týmgüýçbilen", "mallugaming": "mallugaming", "pawgers": "pawgerler", "quests": "questler", "alax": "alax", "avgn": "avgn", "oldgamer": "köneoyunçy", "cozygaming": "<PERSON><PERSON><PERSON><PERSON>", "gamelpay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dsswitch": "dsgeçiş", "competitivegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftnewjersey": "minecraftnýujörsi", "faker": "galp", "pc4gamers": "pc4<PERSON><PERSON><PERSON><PERSON>", "gamingff": "<PERSON><PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "heterose<PERSON><PERSON><PERSON><PERSON>ing", "gamepc": "oyunpc", "girlsgamer": "gyzlargamer", "fnfmods": "fnfmodlar", "dailyquest": "gündelikgözleg", "gamegirl": "oýunçygyz", "chicasgamer": "oýunçygyzlar", "gamesetup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "aşagü<PERSON><PERSON><PERSON>", "socialgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proplayer": "prokemmal", "roleplayer": "rolgejikçi", "myteam": "meniňtopard<PERSON>", "republicofgamers": "gamersrespublikasy", "aorus": "aorus", "cougargaming": "gar<PERSON><PERSON><PERSON><PERSON>", "triplelegend": "üçdürlilegen", "gamerbuddies": "<PERSON>ý<PERSON><PERSON><PERSON><PERSON><PERSON>", "butuhcewekgamers": "gamergy<PERSON>gerek", "christiangamer": "hristian<PERSON><PERSON>r", "gamernerd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nerdgamer": "botanaoyunçy", "afk": "afk", "andregamer": "andregamer", "casualgamer": "ýönekeý<PERSON>", "89squad": "89topary", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "sowukly", "gemers": "<PERSON><PERSON><PERSON><PERSON>", "oyunizlemek": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamertag": "<PERSON><PERSON><PERSON>", "lanparty": "oýunpartiýasy", "videogamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wspólnegranie": "<PERSON>le<PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "playstationoýunçy", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "sag<PERSON><PERSON><PERSON>", "gtracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notebookgamer": "depderçigamer", "protogen": "protogen", "womangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "ýygnaýjy", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nolgaçyş", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomuzyka", "sonicthehedgehog": "sonikkirpi", "sonic": "sonik", "fallguys": "fallguys", "switch": "çalyş", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "göküňçagalaryýagtylygyň", "tomodachilife": "dostlardur<PERSON><PERSON><PERSON>", "ahatintime": "pursa<PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogames": "ninte<PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofzelda": "zeldanyňrowaýaty", "dragonquest": "dragonquest", "harvestmoon": "hasylbaýramy", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "seleste", "breathofthewild": "demalgayngazygy", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "<PERSON><PERSON><PERSON><PERSON>", "earthbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tales": "<PERSON><PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "<PERSON><PERSON><PERSON>laryňgeçelgesi", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendoçili", "tloz": "tloz", "trianglestrategy": "üçburçlukstrategiýasy", "supermariomaker": "supermar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "konkerserbetgün", "nintendos": "nintendolar", "new3ds": "täze3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marioandsonic": "ma<PERSON><PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendoglar", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "gyzy<PERSON><PERSON>", "vanillalol": "wani<PERSON><PERSON>l", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "ligaýalegan", "tốcchiến": "tiz<PERSON><PERSON>şma", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "reklamagötermek", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsispaniýa", "aatrox": "aatrox", "euw": "fuj", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "şako", "ligadaslegendas": "ligadaslegendas", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "altıburçderwezeler", "hextech": "hex<PERSON><PERSON>", "fortnitegame": "fortnitegame", "gamingfortnite": "oýunfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retro<PERSON><PERSON><PERSON><PERSON><PERSON>", "scaryvideogames": "gorky<PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "wideoýasaýjy", "megamanzero": "megamanzero", "videogame": "<PERSON><PERSON><PERSON><PERSON>", "videosgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "söweşblokteatro", "arcades": "<PERSON><PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "fer<PERSON><PERSON>ulýatory", "robloxchile": "robloxçile", "roblox": "roblo<PERSON>", "robloxdeutschland": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "rollerdrom", "parasiteeve": "parazitgi<PERSON><PERSON>", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "düýşpeýzaž", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "ölügiň", "amordoce": "sü<PERSON><PERSON>ýü<PERSON><PERSON>", "videogiochi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theoldrepublic": "könerespublika", "videospiele": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "ýürekgysgynç<PERSON>keder", "storyofseasons": "ýylyňhekaýasy", "retrogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "retroarkada", "vintagecomputing": "könekomppýuter", "retrogaming": "retrogeyming", "vintagegaming": "k<PERSON>neoyunlar", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "adalatsyzlyk2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "rahatdurmuş", "beatmaniaiidx": "beatmaniaiidx", "steep": "dik", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "blokçeýnoýun", "medievil": "ortaasyrmysal", "consolegaming": "kons<PERSON><PERSON>", "konsolen": "konsolen", "outrun": "aşypgeç", "bloomingpanic": "gülýänalada", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "oýungorkunç", "monstergirlquest": "jeňewejgyzsoragy", "supergiant": "supergigant", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackbox<PERSON><PERSON><PERSON>lary", "interactivefiction": "interaktiwhekaýa", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantesamentes", "visualnovel": "wizualnovel", "visualnovels": "wiz<PERSON><PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "möjekgurt", "tcrghost": "tcrarwah", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON><PERSON>", "twilightprincess": "şapakprinsessasy", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON>", "novelavisual": "wizualdessany", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkprofessionalskeyboardçy", "smbz": "smbz", "lamento": "gyna<PERSON><PERSON>", "godhand": "hudaýyň_eli", "leafblowerrevolution": "ýaprakpüfleýjiöwrülişi", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starrail": "ýyldyzýoly", "keyblade": "açargylynç", "aplaguetale": "hasaratekertdir", "fnafsometimes": "fnafdenbir", "novelasvisuales": "wiz<PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "robloxbrazi<PERSON>ýa", "pacman": "pacman", "gameretro": "retrowagt<PERSON><PERSON><PERSON>", "videojuejos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamedates": "<PERSON><PERSON><PERSON><PERSON>", "mycandylove": "myşiriný<PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "sebäbimbolmasa3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "bat<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "hasaplaşmagyňgaýdyşy", "gamstergaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofthetantacle": "tentakulg<PERSON><PERSON>", "maniacmansion": "manyakköşk", "crashracing": "kraşreýsiň", "3dplatformers": "3dplatformerler", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "köneoj<PERSON><PERSON>", "hellblade": "jähennempygy", "storygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "sesdengoraý<PERSON>y", "beyondtwosouls": "ikijanykgoňşusy", "gameuse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "offmortisghost", "tinybunny": "kiçij<PERSON>ktowşan", "retroarch": "retroarch", "powerup": "güýçlen", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "abenturalargrafikasy", "quickflash": "çaltgörkezme", "fzero": "fzero", "gachagaming": "gaça<PERSON><PERSON>", "retroarcades": "retroarkaýdlar", "f123": "f123", "wasteland": "çöllük", "powerwashsim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coralisland": "koraladasы", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "başgad<PERSON><PERSON>ýä", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "futbolfüzýon", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "aýlawmetallar", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "utançýygyny", "simulator": "simulator", "symulatory": "sym<PERSON><PERSON><PERSON><PERSON>", "speedrunner": "<PERSON><PERSON><PERSON><PERSON>", "epicx": "epikx", "superrobottaisen": "superrobottalişum", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "<PERSON><PERSON><PERSON><PERSON>", "gaiaonline": "gaiaonline", "korkuoyunu": "gorkioyuny", "wonderlandonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skylander": "skylander", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "too<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simrace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "şäherhowdanlyk", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "gar", "partyvideogames": "oturylyşykwideooyunlar", "graveyardkeeper": "mazarlyksakçysy", "spaceflightsimulator": "kosmosugursymulýatory", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "kesgindöwgün", "foodandvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "oyunwideolary", "thewolfamongus": "arasymyzdakymöjek", "truckingsimulator": "ýükmaşynsimulýatory", "horizonworlds": "horizondünýäleri", "handygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON>legend<PERSON><PERSON>", "oldschoolvideogames": "köneçewideoýunlar", "racingsimulator": "awtouýşsimulyatory", "beemov": "bee<PERSON>v", "agentsofmayhem": "ma<PERSON><PERSON>i<PERSON>i", "songpop": "aýdympop", "famitsu": "famitsu", "gatesofolympus": "olimpiňderwe<PERSON><PERSON>", "monsterhunternow": "monsterhunternow", "rebelstar": "göterençýyldyz", "indievideogaming": "indi<PERSON><PERSON><PERSON><PERSON><PERSON>", "indiegaming": "indioýun", "indievideogames": "indi<PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogame": "indiewideoýun", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermanukyçokyok", "bufffortress": "güýçlügala", "unbeatable": "ýeňilmez", "projectl": "proýekt", "futureclubgames": "geljekkluboyunlary", "mugman": "<PERSON><PERSON><PERSON><PERSON>", "insomniacgames": "söwdasyzyň_oýunlary", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON>st<PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "backlog", "gamebacklog": "oýunnobadam", "gamingbacklog": "oýunlaryzyň", "personnagejeuxvidéos": "wideooýunlarynyňgahryman<PERSON>", "achievementhunter": "başaryjyaýçy", "cityskylines": "şähergörnüşleri", "supermonkeyball": "supermaýmynşary", "deponia": "deponia", "naughtydog": "erbetit", "beastlord": "haýwanbaşy", "juegosretro": "retrogamelar", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alanwake": "alanwake", "stanleyparable": "stenliparable", "reservatoriodedopamin": "dopaminämmary", "staxel": "staxel", "videogameost": "wideooyunmuzyka", "dragonsync": "ejderhasyňhron", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "syrdüşünce", "neoy2k": "neoy2k", "pcracing": "kom<PERSON><PERSON><PERSON><PERSON>ýunlary", "berserk": "çyldyryp", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "gynçly_anime", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "animekesgitleme", "animewithplot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pesci": "pesci", "retroanime": "retroanime", "animes": "animeler", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "80<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "g<PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "owadaný<PERSON>t", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "2000<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstone1möwsüm", "rapanime": "rapanime", "chargemanken": "zarýatkenmen", "animecover": "animegapak", "thevisionofescaflowne": "eskaflounyňgörüşi", "slayers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90lar", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "<PERSON><PERSON><PERSON>", "bananafish": "ban<PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletaglylyhanakokunfilmi", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "boşluk", "fireforce": "ýangýnsönd<PERSON><PERSON><PERSON>", "moriartythepatriot": "moriart<PERSON><PERSON><PERSON>ot", "futurediary": "gel<PERSON><PERSON><PERSON>", "fairytail": "<PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "<PERSON><PERSON><PERSON>", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "deniz<PERSON>zy<PERSON><PERSON>", "kamisamakiss": "hudaýymyňöpüşi", "blmanga": "blmanga", "horrormanga": "go<PERSON><PERSON><PERSON><PERSON><PERSON>kman<PERSON>", "romancemangas": "romantikimangalar", "karneval": "<PERSON>rna<PERSON>", "dragonmaid": "ejderhagyzmatçy", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "zehinobyektiwi", "shamanking": "şamanşa", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "juju<PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "bellibirýaduwlyindeks", "sao": "sao", "blackclover": "garaýalpak", "tokyoghoul": "tokyoghoul", "onepunchman": "birurgyadan", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goldenkamuy": "altynkamuy", "monstermusume": "monstergyzy", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "tanyaerbetinsaga<PERSON>", "shounenanime": "şounenanim", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "demirgazyldyzynýeňji", "mazinger": "mazinger", "blackbuttler": "garabutler", "towerofgod": "hyzmatyňkülesi", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "çibi", "servamp": "servamp", "howtokeepamummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "<PERSON><PERSON><PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomýawmýaw", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "owadanwegorkunc", "martialpeak": "söweşseniňbelentligi", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "rekordgyz", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "<PERSON><PERSON><PERSON>", "zerotwo": "zerotwoiki", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "a<PERSON><PERSON><PERSON><PERSON>", "vegeta": "wegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "skaramuş", "amiti": "amiti", "sailorsaturn": "deňizçisaturn", "dio": "dio", "sailorpluto": "sailorplýuton", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chainsawman": "çençerşeytan", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "inumini", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "garagyzmetçi", "ergoproxy": "ergoproxy", "claymore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loli": "loli", "horroranime": "<PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "şeýtanadamyňaglaýanbäbegi", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON>öýgiýa<PERSON>a", "sakuracardcaptor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "umibenoetranger": "ummandaky<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "wa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermanga": "monstrkomiks", "yourlieinapril": "apreliňdeý<PERSON>ýň", "buggytheclown": "baggythepalaç", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "kiborg009", "magi": "magi", "deepseaprisoner": "deňiztüýbinidetussag", "jojolion": "jojo<PERSON>", "deadmanwonderland": "ölüadamyňtäsinlikülkesi", "bannafish": "bannabanalyk", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "pandor<PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "şeýtansyzyk", "toyoureternity": "saňahemişelik", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "gökdöwür", "griffithberserk": "griffithberserk", "shinigami": "ş<PERSON>gami", "secretalliance": "g<PERSON><PERSON><PERSON><PERSON>ş<PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "öçürildi", "bluelock": "bluelock", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiki": "şiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vampireknight": "wampirrysary", "mugi": "mugi", "blueexorcist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "airgear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirl": "jadylygyz", "thesevendeadlysins": "ýedisanyölümgünä", "prisonschool": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kissxsis": "öpüşýänuýa", "grandblue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mydressupdarling": "meniňgeýdirişdärilgänim", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "<PERSON><PERSON><PERSON><PERSON>", "swordartonlineabridge": "swordartonlineköpri", "saoabridged": "saogysgaldylan", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpsiho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "ö<PERSON>eýänbagty<PERSON>z", "romancemanga": "romantikimanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromaň", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lolikon", "demonslayertothesword": "gyndygowşakdangylyja", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "otlyyumruk", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "şý<PERSON>jo<PERSON>ý", "starsalign": "ýyldyzlard<PERSON>", "romanceanime": "roman<PERSON><PERSON><PERSON><PERSON>", "tsundere": "tsundere", "yandere": "<PERSON><PERSON><PERSON>", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfaktor", "cherrymagic": "giňmagikasy", "housekinokuni": "öýkinotm", "recordragnarok": "rekordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "ölüleriňmekdebi", "germantechno": "german<PERSON>hn<PERSON>", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "tennisiňşazadasy", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "ölümparad", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "ý<PERSON>ani<PERSON>", "animespace": "animekeşbi", "girlsundpanzer": "gyzlarwetanklar", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON>", "animedub": "animedublaýyş", "animanga": "animemanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "<PERSON>q<PERSON><PERSON><PERSON><PERSON><PERSON>", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animefantastika", "ratman": "ratmannyň", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "nekooglan", "gashbell": "ga<PERSON><PERSON>", "peachgirl": "şeftaligyzy", "cavalieridellozodiaco": "zodiakyňry<PERSON>lary", "mechamusume": "mehamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "manganyňýü<PERSON>gi", "deliciousindungeon": "<PERSON>hard<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "ragnarokynyýazgysy", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "boçirokmuzyka", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialçaltgaty", "overgeared": "aşırıdonanımlı", "toriko": "<PERSON><PERSON>o", "ravemaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "zlosakawal<PERSON>i", "animeshojo": "animegyzy", "reverseharem": "<PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "beýikmuğallımonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "my<PERSON><PERSON>dy", "gear5": "gear5", "grandbluedreaming": "ulumawyköksüýji", "bloodplus": "ganplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "ganda<PERSON><PERSON>", "bloodc": "ganc", "talesofdemonsandgods": "jynlaryňweperişteleriňhekaýalary", "goreanime": "goreani<PERSON><PERSON><PERSON>", "animegirls": "animegyzylar", "sharingan": "<PERSON><PERSON><PERSON>", "crowsxworst": "gargalaryňiňbeterleri", "splatteranime": "splatteranime", "splatter": "çaýkamak", "risingoftheshieldhero": "galkanyngoragynygahrymany", "somalianime": "somal<PERSON><PERSON>i", "riodejaneiroanime": "riodežaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animeispaniýa", "animeciudadreal": "animeşäherreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "akbalyklarynçagalary", "liarliar": "ýalançyýalançy", "supercampeones": "supercampeones", "animeidols": "animeidollar", "isekaiwasmartphone": "isekaýytelefonym", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "sýrlygyzylar", "callofthenight": "gijedençagyryş", "bakuganbrawler": "bakugans<PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "kölegegülşen", "tsubasachronicle": "tsubasachronicle", "findermanga": "finderman<PERSON>", "princessjellyfish": "deniz<PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON><PERSON>", "revuestarlight": "ýyldyzlyşöhle", "animeverse": "animeälemi", "persocoms": "persokomplar", "omniscientreadersview": "hemezatokardyjynyngarayysy", "animecat": "animepişik", "animerecommendations": "animerekommendasiýalar", "openinganime": "açylýananime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "ýetginjekromantikkomediýam", "evangelion": "evangelion", "gundam": "gundam", "macross": "makross", "gundams": "gundamlar", "voltesv": "voltesv", "giantrobots": "ägirtrobotlar", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "<PERSON><PERSON>ka", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deathnote": "ölümhatdy", "cowboybebop": "kowboybeböp", "jjba": "jjba", "jojosbizarreadventure": "jojonıňgeňbizarawantýurasy", "fullmetalalchemist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghiaccio": "buz", "jojobizarreadventures": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "<PERSON><PERSON><PERSON><PERSON>", "greenranger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "<PERSON><PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonbol", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonbaşdangeçirme", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "şonenanim", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "j<PERSON><PERSON>ňekeseýji", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "titanlarahüjüm", "erenyeager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "to<PERSON><PERSON>ç<PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "gözegçilerkompaniyasy", "onepieceanime": "onepieceanime", "attaquedestitans": "titan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theonepieceisreal": "birbölekhakykydyr", "revengers": "<PERSON>rka<PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "ekzorsist<PERSON>kmawi", "joyboyeffect": "joyboyeffekt", "digimonstory": "digimonstory<PERSON><PERSON><PERSON>", "digimontamers": "digimonelderiçiler", "superjail": "superturme", "metalocalypse": "metallokalips", "shinchan": "şinçan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "aýypsyzwebtoon", "kemonofriends": "kemonodostlar", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "şeýdip", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "ählikeramatlylarköçesi", "recuentosdelavida": "ömrüňgüssalary"}