{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astroleg", "cognitivefunctions": "swyddogaethiaugwybyddol", "psychology": "seicoleg", "philosophy": "athroniaeth", "history": "hanes", "physics": "ffiseg", "science": "gwyddoniaeth", "culture": "diwy<PERSON>ant", "languages": "<PERSON><PERSON><PERSON><PERSON>", "technology": "technoleg", "memes": "mîms", "mbtimemes": "mbtimemes", "astrologymemes": "memesastroleg", "enneagrammemes": "memesenniagram", "showerthoughts": "meddyliancawod", "funny": "doniol", "videos": "fideos", "gadgets": "<PERSON><PERSON><PERSON><PERSON>", "politics": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "relationshipadvice": "cyn<PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "cyngorywyd", "crypto": "crypto", "news": "newyddion", "worldnews": "newyddionybyd", "archaeology": "archeoleg", "learning": "dysgu", "debates": "dadle<PERSON>n", "conspiracytheories": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "universe": "bydysawd", "meditation": "myfyrdod", "mythology": "mytholeg", "art": "celf", "crafts": "creff<PERSON>u", "dance": "dawns", "design": "dyl<PERSON><PERSON>", "makeup": "colur", "beauty": "harddwch", "fashion": "ffasiwn", "singing": "canu", "writing": "ysgrifennu", "photography": "ffotogra<PERSON><PERSON>th", "cosplay": "cosplay", "painting": "paentio", "drawing": "celf", "books": "llyfrau", "movies": "ffilmiau", "poetry": "bardd<PERSON><PERSON>", "television": "teledu", "filmmaking": "gwneudffilmiau", "animation": "animeiddiaeth", "anime": "anime", "scifi": "gwyddoniasffuglen", "fantasy": "<PERSON><PERSON><PERSON>", "documentaries": "dogfennau", "mystery": "<PERSON><PERSON><PERSON><PERSON>", "comedy": "comedi", "crime": "trose<PERSON>", "drama": "drama", "bollywood": "bollywood", "kdrama": "dramacorea", "horror": "arswyd", "romance": "r<PERSON>t", "realitytv": "teleduraliti", "action": "g<PERSON><PERSON><PERSON><PERSON>", "music": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blues": "glas", "classical": "classurol", "country": "g<PERSON><PERSON>", "desi": "desi", "edm": "edm", "electronic": "electronig", "folk": "gwerin", "funk": "ffync", "hiphop": "hiphop", "house": "tŷ", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "lladin", "metal": "metel", "pop": "pop", "punk": "pync", "rnb": "rnb", "rap": "rap", "reggae": "rege", "rock": "roc", "techno": "techno", "travel": "teithio", "concerts": "gigs", "festivals": "g<PERSON><PERSON><PERSON>", "museums": "amgueddfeydd", "standup": "sefyllfyny", "theater": "theatr", "outdoors": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "gardening": "garddio", "partying": "partïo", "gaming": "gemau", "boardgames": "gemaufwrdd", "dungeonsanddragons": "dungeonsanddragons", "chess": "gwyddbwyll", "fortnite": "fortnite", "leagueoflegends": "cynghrairyrchwedlau", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "bwyd", "baking": "pobi", "cooking": "coginio", "vegetarian": "llysieuol", "vegan": "<PERSON>gan", "birds": "adar", "cats": "cathod", "dogs": "cŵn", "fish": "pysgod", "animals": "anifeiliaid", "blacklivesmatter": "duonywydaumaterion", "environmentalism": "amgylcheddiaeth", "feminism": "ffeminist<PERSON>th", "humanrights": "hawliaudynol", "lgbtqally": "cefnogwrlhdtc", "stopasianhate": "stopcas<PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "cyfalltrawsry<PERSON><PERSON>l", "volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sports": "<PERSON><PERSON><PERSON>", "badminton": "badminton", "baseball": "<PERSON><PERSON><PERSON><PERSON>", "basketball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxing": "bocso", "cricket": "criaced", "cycling": "beicio", "fitness": "ffitrwydd", "football": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "golf": "golff", "gym": "ym", "gymnastics": "gymnasteg", "hockey": "hoci", "martialarts": "crefftauymladd", "netball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pilates": "pilates", "pingpong": "pinpong", "running": "rhedeg", "skateboarding": "s<PERSON><PERSON><PERSON>", "skiing": "sgïo", "snowboarding": "eirafyrddio", "surfing": "syr<PERSON>io", "swimming": "nofio", "tennis": "tenis", "volleyball": "p<PERSON><PERSON>", "weightlifting": "codipwy<PERSON><PERSON>", "yoga": "ioga", "scubadiving": "sgwbadeifio", "hiking": "cerdded", "capricorn": "capricorn", "aquarius": "dyfrwr", "pisces": "pysgod", "aries": "aries", "taurus": "tarws", "gemini": "gemini", "cancer": "canser", "leo": "leo", "virgo": "virgo", "libra": "libra", "scorpio": "scorpion", "sagittarius": "sagittarius", "shortterm": "<PERSON><PERSON><PERSON><PERSON>", "casual": "anffurfiol", "longtermrelationship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "single": "sengl", "polyamory": "amlgariad", "enm": "enm", "lgbt": "lhdt", "lgbtq": "lhdtc", "gay": "hoyw", "lesbian": "<PERSON><PERSON><PERSON><PERSON>", "bisexual": "<PERSON><PERSON><PERSON><PERSON>", "pansexual": "<PERSON>rywiol", "asexual": "anrhywiol", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "cwng<PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "tanfyd", "legendofspyro": "ch<PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "siberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "sefydlog", "litrpg": "litrpg", "haloinfinite": "haloanfeid<PERSON>", "guildwars": "guildwars", "openworld": "bydagored", "heroesofthestorm": "heroesofthestorm", "cytus": "cytus", "soulslike": "eneidbyg", "dungeoncrawling": "cribo<PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "awyrwedd", "lordsoftherealm2": "arglwyddirteyrnas2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "lliwgarwr", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "si<PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "witcher": "gwrachwr", "dishonored": "anrhydeddwyd", "eldenring": "elden<PERSON>", "darksouls": "<PERSON><PERSON><PERSON>ut<PERSON><PERSON>ll", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "cwymp", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modio", "charactercreation": "creucymeriad", "immersive": "trochol", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "fantasif<PERSON>alhenffasi<PERSON>", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "anghytgord", "finalfantasy7": "ffinalffantasi7", "ff7": "ff7", "morbidmotivation": "moesgosgymhelliant", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "ffŵldros", "otomegames": "gemauotome", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "ymasgyrhampiriad", "dimension20": "dimension20", "gaslands": "tiroeddnwy", "pathfinder": "llwybrfindwr", "pathfinder2ndedition": "pathfinder2ailargraffiad", "shadowrun": "rhediggcysgod", "bloodontheclocktower": "gwaedaryglochdŵr", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "carunitrev", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "rhuthrdisgyrchiant", "rpg": "grf", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "unergyd", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "goruchwyliwr", "yourturntodie": "dytromarw", "persona3": "persona3", "rpghorror": "arswydrpg", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "testunrpg", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "eneidiaecythreuliaid", "mu": "mu", "falloutshelter": "llochesgwymp", "gurps": "gurps", "darkestdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "cyfnodeclips", "disgaea": "disgaea", "outerworlds": "bydoeddallanol", "arpg": "jrfcc", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "rhyfelwyryllwyth", "skullgirls": "skullgirls", "nightcity": "dinasnos", "hogwartslegacy": "etifeddiaethhogwarts", "madnesscombat": "ymladdfadoedd", "jaggedalliance2": "jaggedalliance2", "neverwinter": "bythamywydd", "road96": "cefnffordd96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamnights", "forgottenrealms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "tre<PERSON><PERSON><PERSON><PERSON><PERSON>", "childoflight": "plenty<PERSON>leuni", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "<PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "ffermfwystfilod", "ecopunk": "ecopync", "vermintide2": "llygodmawr2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "teyrnasaudrylliedig", "horizonforbiddenwest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twewy": "twewy", "shadowpunk": "cysgodpync", "finalfantasyxv": "finalfantasyxv", "everoasis": "gwerddonbythol", "hogwartmystery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "taro", "lastepoch": "oes<PERSON><PERSON>", "starfinder": "darganfyddyseren", "goldensun": "hauleuraid", "divinityoriginalsin": "pechodgwreid<PERSON>lyduwdod", "bladesinthedark": "llafnauyndywyllwch", "twilight2000": "cyfnos2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "ciberpynccoch", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "trefnsyrthio", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "tiroedddrwg", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "goroeswydiafol", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "ffantasiterfynol10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "ffermrpg", "oldworldblues": "henyddflin", "adventurequest": "anturcwest", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "<PERSON><PERSON><PERSON><PERSON>", "talesofsymphonia": "chwestraethsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "dinas_rhwyg", "myfarog": "myfarog", "sacredunderworld": "bydsudtandaearol", "chainedechoes": "adleisiaucadwynog", "darksoul": "eneidtywyll", "soulslikes": "eneidwedddebyg", "othercide": "lladfraethodd", "mountandblade": "mynyddallafn", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pillarsofeternity": "colofnautrag<PERSON>ddold<PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "rhwygiadau", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "he<PERSON><PERSON><PERSON>", "legendofdragoon": "chwedonyddraig", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "gwaewydyrapocalyps", "aveyond": "aveyond", "littlewood": "bach_y_coed", "childrenofmorta": "<PERSON><PERSON><PERSON>", "engineheart": "calonfecanic", "fable3": "fable3", "fablethelostchapter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "cy<PERSON><PERSON><PERSON><PERSON><PERSON>", "rollenspiel": "g<PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "tragwyddoleden", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "adfywiadyrhensgol", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "bydoeddcreulon", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "calonteyrnas1", "ff9": "ff9", "kingdomheart2": "teyrnasgalon2", "darknessdungeon": "carcharywyllwch", "juegosrpg": "gemaurpg", "kingdomhearts": "teyrnasloncalon", "kingdomheart3": "teyrnasgalon3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "calonnaugwyllt", "bastion": "cadarn<PERSON>", "drakarochdemoner": "d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "nefoeddarcadia", "shadowhearts": "calonnaucysgod", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "anaddtân4", "mother3": "mam3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "gemauactio", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "calongwrach", "harrypottergame": "gem<PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "fampirfasgarâd", "dračák": "draig", "spelljammer": "sillafrheithiwr", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cocttrpg": "cocttrpg", "huntroyale": "he<PERSON><PERSON><PERSON>", "albertodyssey": "albertodysea", "monsterhunterworld": "bydhelaenwyllt", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "fforwmrpg", "shadowheartscovenant": "shadowheartscovenant", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "teyrnasnddaw", "awplanet": "awblanedig", "theworldendswithyou": "maerbydyngyffindiddiwedd", "dragalialost": "dragalialost", "elderscroll": "hens<PERSON>l", "dyinglight2": "dyinglight2", "finalfantasytactics": "tactegauffantasiterfynol", "grandia": "grandia", "darkheresy": "here<PERSON><PERSON><PERSON>ll", "shoptitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON>", "blackbook": "llyfrdu", "skychildrenoflight": "plentynefyrawyr", "gryrpg": "gryrpg", "sacredgoldedition": "aureura<PERSON><PERSON>dd", "castlecrashers": "ymosodwyrcestyll", "gothicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ys<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "rpggemau", "prophunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starrails": "trenauserennog", "cityofmist": "dinasniwl", "indierpg": "rpgannibynnol", "pointandclick": "clica<PERSON><PERSON><PERSON><PERSON>", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>fy<PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "freeside": "ochrcostyndim", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "ôlseiberpync", "deathroadtocanada": "fforddmarwolaethiganada", "palladium": "paladiwm", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "helwrbwystfilod", "fireemblem": "tânfathodyn", "genshinimpact": "genshinimpact", "geosupremancy": "goruchafiaeth_geo", "persona5": "persona5", "ghostoftsushima": "ysbrydtsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "helwrgwrthfilodcodi", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "rôlchwaraetactegol", "mahoyo": "mahoyo", "animegames": "gemauanime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "deifiarbwyta", "diluc": "diluc", "venti": "venti", "eternalsonata": "sonataebythol", "princessconnect": "cysylltiadtywysog", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorant<PERSON><PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "chwaraeonelectronig", "mlg": "mlg", "leagueofdreamers": "cynghrairbreuddwydwyr", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "e<PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamhack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaimin": "gemio", "overwatchleague": "cynghrai<PERSON><PERSON><PERSON><PERSON>", "cybersport": "echwaraeon", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "<PERSON><PERSON><PERSON><PERSON>", "eracing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantcystadleuol", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "hanner<PERSON>", "left4dead": "gadaelarnôl4marw", "left4dead2": "left4dead2", "valve": "falf", "portal": "porth", "teamfortress2": "teamfortress2", "everlastingsummer": "ha<PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "efelychwrgeifr", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planedrhyddid", "transformice": "transformice", "justshapesandbeats": "dimondsiapiauch<PERSON><PERSON><PERSON>", "battlefield4": "battlefield4", "nightinthewoods": "nosonynycoed", "halflife2": "halflife2", "hacknslash": "haciotor<PERSON>", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "peryglaw2", "metroidvanias": "metroidvanias", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "rhyngblanedol", "helltaker": "uffernwr", "inscryption": "angryptiad", "7d2d": "7d2d", "deadcells": "<PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "ffosdir", "stray": "crwydr", "battlefield": "maescad", "battlefield1": "maesgad1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "llongdanfor", "eyeb": "llygadb", "blackdesert": "an<PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "efelychwrddybwrdd", "partyhard": "partïocaled", "hardspaceshipbreaker": "chw<PERSON><PERSON>lliongofodcaled", "hades": "hades", "gunsmith": "gwneuthurwrarfau", "okami": "<PERSON>ami", "trappedwithjester": "maewg<PERSON>jester<PERSON>lown", "dinkum": "dinkum", "predecessor": "rhagflaenydd", "rainworld": "bydglaw", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "simtrefedigaeth", "noita": "noita", "dawnofwar": "gwawrycyfel", "minionmasters": "meistrimwnion", "grimdawn": "gwaw<PERSON>wyll", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "gweithioddenaid", "datingsims": "simscariad", "yaga": "yaga", "cubeescape": "ciwbdianc", "hifirush": "s<PERSON><PERSON>l", "svencoop": "svencoop", "newcity": "dinasnewydd", "citiesskylines": "dinasoeyddawyr", "defconheavy": "defcontrwm", "kenopsia": "kenopsia", "virtualkenopsia": "cenosiacymunedol", "snowrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "llyfrgellanhrefn", "l4d2": "l4d2", "thenonarygames": "ygemauhebddeuol", "omegastrikers": "omegastrikers", "wayfinder": "llwybrddangoswyr", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "hwyaidenblastigllonydd", "battlebit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ultimatechickenhorse": "ceffyliarunol", "dialtown": "dialtre", "smileforme": "<PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermeatboy", "tinnybunny": "c<PERSON><PERSON><PERSON><PERSON>", "cozygrove": "coedgy<PERSON><PERSON>", "doom": "doomsday", "callofduty": "callofduty", "callofdutyww2": "galwadyletswyddww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "tiroeddffinia<PERSON>", "pubg": "pubg", "callofdutyzombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "copa", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "gemaufarcrygames", "paladins": "paladiniaid", "earthdefenseforce": "lluoeddamddiffynyrbyd", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "ysb<PERSON>drecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "lladdiadeithafol", "joinsquad": "ymunaachwad", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "gwrthryfelstormtywod", "farcry3": "farcry3", "hotlinemiami": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "b4b": "b4b", "codwarzone": "co<PERSON><PERSON><PERSON>l", "callofdutywarzone": "callofdutywarzone", "codzombies": "zombiscod", "mirrorsedge": "ymylochredir", "divisions2": "rhaniadau2", "killzone": "parthladd", "helghan": "hel<PERSON>", "coldwarzombies": "zombisdyrhyfela<PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "awyrf<PERSON><PERSON><PERSON>", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare": "rhyfelmodern", "neonabyss": "neonabys", "planetside2": "planetside2", "mechwarrior": "rhyfelwrmecanyddol", "boarderlands": "ffiniau", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "carneddgyntefig", "worldofwarships": "llongaurhyfelybyd", "back4blood": "yn_ol_am_waed", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "lladdwr", "masseffect": "masseffect", "systemshock": "siocysystem", "valkyriachronicles": "valkyriachronicles", "specopstheline": "llinyrhediadarbennig", "killingfloor2": "killingfloor2", "cavestory": "<PERSON><PERSON><PERSON><PERSON>", "doometernal": "doometragwyddol", "centuryageofashes": "canrifoesoludw", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tytytasmaniaidd", "generationzero": "generationdim", "enterthegungeon": "mentroirgwndwn", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "rhyfelmodern2", "blackops1": "blackops1", "sausageman": "dyn<PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "yboenddrysedd", "warface": "<PERSON><PERSON>br<PERSON><PERSON><PERSON>", "crossfire": "g<PERSON><PERSON><PERSON>", "atomicheart": "calonatomig", "blackops3": "blackops3", "vampiresurvivors": "goroeswyrvampiri<PERSON>", "callofdutybatleroyale": "galwadddyletswyddbrwydrfrenhinol", "moorhuhn": "cywfflarddofednod", "freedoom": "<PERSON><PERSON><PERSON><PERSON>", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "f<PERSON>gio", "tinytina": "<PERSON><PERSON>", "gamepubg": "g<PERSON><PERSON><PERSON><PERSON>", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "g<PERSON><PERSON><PERSON>", "convertstrike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warzone2": "rhyfel2", "shatterline": "llinelldorri", "blackopszombies": "zombiesblackops", "bloodymess": "llanastllwyr", "republiccommando": "republiccommando", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "sgwad", "destiny1": "tynged1", "gamingfps": "gamingfps", "redfall": "cwymp_coch", "pubggirl": "merchpubg", "worldoftanksblitz": "bydotan<PERSON>uchwarae", "callofdutyblackops": "galwaddyletswyddopsddu", "enlisted": "rhestredig", "farlight": "pelldisgleiriol", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "craidd_arfog", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "diwrnodtâl2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgwcrain", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "tafarnomaniach", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "pysgo<PERSON><PERSON><PERSON><PERSON>", "ghostcod": "ysbydcod", "csplay": "csplay", "unrealtournament": "twrnameintanhygoel", "callofdutydmz": "callofdutydmz", "gamingcodm": "gemiocodm", "borderlands2": "borderlands2", "counterstrike": "gwy<PERSON>daro", "cs2": "cs2", "pistolwhip": "ch<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymw2": "galwaddyletswyddmw2", "quakechampions": "pencampw<PERSON>uch<PERSON><PERSON>", "halo3": "halo3", "halo": "helo", "killingfloor": "llaw<PERSON>ladd", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remnant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "azurelane": "azurelane", "worldofwar": "bydorhyfel", "gunvolt": "gunvolt", "returnal": "returnal", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "dynergysgod", "quake2": "quake2", "microvolts": "microfoltau", "reddead": "coch_marw", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "maesgad3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "rwd", "conqueronline": "conqueronline", "dauntless": "diofn", "warships": "llongaurhyfel", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "ehediadcodi", "recroom": "ystafellrec", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "phantasystaronline2", "maidenless": "di<PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "bydt<PERSON><PERSON><PERSON>", "crossout": "c<PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "ailbywyd", "aion": "aion", "toweroffantasy": "tŵrffantasi", "netplay": "ch<PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "<PERSON><PERSON><PERSON><PERSON>", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "royalbrenhinolanifeiliaid", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clwb<PERSON><PERSON><PERSON>", "lotro": "lotro", "wakfu": "wakfu", "scum": "sgym", "newworld": "mundbyw", "blackdesertonline": "blackdesertonline", "multiplayer": "amlchwaraewr", "pirate101": "môrleidr101", "honorofkings": "balchderybrenhinoedd", "fivem": "fivem", "starwarsbattlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dchat": "sgwrs3d", "nostale": "nostale", "tauriwow": "tawriwaw", "wowclassic": "wow<PERSON><PERSON>ig", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "lluchwcread", "riotmmo": "riotmmo", "silkroad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "spiralknights", "mulegend": "ch<PERSON><PERSON><PERSON>", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "proff<PERSON>ddrai<PERSON>", "grymmo": "grymmo", "warmane": "cynn<PERSON><PERSON><PERSON><PERSON><PERSON>", "multijugador": "amlchwaraewr", "angelsonline": "<PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "henweriniaeththeseren", "grandfantasia": "f<PERSON><PERSON><PERSON><PERSON>", "blueprotocol": "protocolglasweild", "perfectworld": "byd<PERSON><PERSON><PERSON><PERSON>", "riseonline": "rheida<PERSON><PERSON>", "corepunk": "corepync", "adventurequestworlds": "antur<PERSON><PERSON><PERSON><PERSON>", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "animaljam", "kingdomofloathing": "teyrnasffoedd", "cityofheroes": "dinasarwyr", "mortalkombat": "mortalkombat", "streetfighter": "ymladdfastryd", "hollowknight": "marchogwag", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "ymladdwrstryd6", "multiversus": "amlysawd", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "ymladdwrrhithwir", "streetsofrage": "cyn<PERSON><PERSON>dd<PERSON><PERSON>", "mkdeadlyalliance": "c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nomoreheroes": "dim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "ybreninadymladdwyr", "likeadragon": "<PERSON><PERSON><PERSON>", "retrofightinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blasphemous": "cableddus", "rivalsofaether": "cys<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "<PERSON><PERSON><PERSON>", "mugen": "mugen", "warofthemonsters": "rhyfelybwystfilod", "jogosdeluta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "botiausiber", "armoredwarriors": "rhy<PERSON>lwyrarmog", "finalfight": "br<PERSON>dr<PERSON>", "poweredgear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "gemauymladd", "killerinstinct": "reddfgwaed", "kingoffigthers": "breninhinymladdwyr", "ghostrunner": "ysbrydredwr", "chivalry2": "marchiaeth2", "demonssouls": "eneidaucythreuliaid", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "<PERSON><PERSON>iant<PERSON>lowhknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "gwenyngwyddsilksong", "silksonggame": "gêmsil<PERSON><PERSON>", "silksongnews": "newyddionsilksong", "silksong": "silksong", "undernight": "tanydywyllwch", "typelumina": "teipiolumina", "evolutiontournament": "twrnamentesblygu", "evomoment": "eiliadevomam", "lollipopchainsaw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "bloodborne", "horizon": "gor<PERSON>l", "pathofexile": "pathofexile", "slimerancher": "ffermwrsleim", "crashbandicoot": "crashbandicoot", "bloodbourne": "gwa<PERSON><PERSON>ig", "uncharted": "an<PERSON><PERSON>s", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "<PERSON><PERSON><PERSON><PERSON>", "infamous": "enwog", "playstationbuddies": "chwaraeongymdeithionplaystation", "ps1": "ps1", "oddworld": "bydrhyfedd", "playstation5": "playstation5", "slycooper": "co<PERSON><PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "dr<PERSON><PERSON><PERSON><PERSON>", "persona4": "persona4", "hellletloose": "anr<PERSON><PERSON><PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "cwm<PERSON><PERSON><PERSON><PERSON><PERSON>", "aisomniumfiles": "ffeiliaiceud<PERSON>d", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "trysor", "detroitbecomehuman": "detroityndod", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "tanywawr", "touristtrophy": "tlwstwerista", "lspdfr": "lspdfr", "shadowofthecolossus": "cysgodycawr", "crashteamracing": "timrasiocrash", "fivepd": "pumpheddiw", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "devilmaycry3", "devilmaycry5": "diafolallgrio5", "ufc4": "ufc4", "playingstation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraiwarriors": "rhyfelwyrsamwrai", "psvr2": "psvr2", "thelastguardian": "ygwarchodwrolaf", "soulblade": "cleddyfenaid", "gta5rp": "gtavcanrôl", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "helfaddynion", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "calonnaucysgod2cyfamod", "pcsx2": "pcsx2", "lastguardian": "gwarchodwrolaf", "xboxone": "xboxone", "forza": "fforza", "cd": "cd", "gamepass": "tocyngema<PERSON>", "armello": "armello", "partyanimal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "pencampwrnosongwffio", "psychonauts": "seiconautiaid", "mhw": "mhw", "princeofpersia": "tywysogpersia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "llinellflaen", "dontstarvetogether": "peidiwch<PERSON>llwgudych_gilydd", "ori": "ori", "spelunky": "ogof<PERSON><PERSON>", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "serennog", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "sgêt3", "houseflipper": "trwsiodai", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "cynghrairybrenhinoedd", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "telffrwts", "skycotl": "awyrfynydd", "erica": "erica", "ancestory": "hynafiad", "cuphead": "cuphead", "littlemisfortune": "bachgenes_anlwcus", "sallyface": "wyne<PERSON><PERSON>", "franbow": "franbow", "monsterprom": "promangenfilod", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "beics", "outerwilds": "gwylltnatur", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "cwltyroen", "duckgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "thestanleyparable", "towerunite": "undebytŵr", "occulto": "oc<PERSON>to", "longdrive": "d<PERSON><PERSON><PERSON><PERSON>", "satisfactory": "boddhaol", "pluviophile": "cariadvyglaw", "underearth": "tandae<PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "rhaglenofodkerbal", "kenshi": "kenshi", "spiritfarer": "ysbry<PERSON>ffer<PERSON>r", "darkdome": "cromenywyll", "pizzatower": "tŵrpizza", "indiegame": "gêmannibynnol", "itchio": "itchio", "golfit": "golffio", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "g<PERSON><PERSON>", "rockpaperscissors": "maenpabursiswrn", "trampoline": "<PERSON><PERSON><PERSON><PERSON>", "hulahoop": "hwlahŵp", "dare": "her", "scavengerhunt": "helfa", "yardgames": "gemaucefn", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "gwi<PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "corachdis", "cosygames": "gemauclyd", "datinggames": "gemaudyd<PERSON>", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "gemauyfed", "sodoku": "sodoku", "juegos": "gemau", "mahjong": "mahjong", "jeux": "gemau", "simulationgames": "gemausimiwleidd<PERSON>", "wordgames": "<PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "<PERSON><PERSON><PERSON>ngê<PERSON>", "boredgames": "gema<PERSON>thma", "oyun": "g<PERSON><PERSON>", "interactivegames": "gemaurhyngweithiol", "amtgard": "amtgard", "staringcontests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiele": "gemau", "giochi": "gemau", "geoguessr": "geoguessr", "iphonegames": "gemauiphone", "boogames": "g<PERSON><PERSON><PERSON>", "cranegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hideandseek": "cudd<PERSON><PERSON><PERSON><PERSON>", "hopscotch": "hopsgôts", "arcadegames": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "mindgames": "gemaumeddwl", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "g<PERSON><PERSON><PERSON>", "romancegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "gemauyanderedigol", "tonguetwisters": "tafodieithoedd", "4xgames": "gemau4x", "gamefi": "gamefi", "jeuxdarcades": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "gemaubwrdd", "metroidvania": "metroidvania", "games90": "gemau90", "idareyou": "migaw<PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "gemaurasio", "ets2": "ets2", "realvsfake": "go_iawn_vs_ffug", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "<PERSON><PERSON><PERSON><PERSON>", "onlinegames": "g<PERSON><PERSON><PERSON><PERSON>", "jogosonline": "g<PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "chwaraerôlysgrifenedig", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "gemaucydweithredol", "jenga": "jenga", "wiigames": "g<PERSON><PERSON><PERSON><PERSON>", "highscore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "g<PERSON><PERSON><PERSON><PERSON>", "kidsgames": "gemauiplant", "skeeball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nfsmwblackedition": "nfsmwduwch", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "gioco", "managementgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "gêmfformiwlaun", "citybuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drdriving": "gyrru", "juegosarcade": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "memorygames": "gemauatgofi<PERSON>", "vulkan": "vulkan", "actiongames": "g<PERSON><PERSON><PERSON>", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "peiriannaupinball", "oldgames": "<PERSON><PERSON><PERSON><PERSON>", "couchcoop": "c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "holwyd", "gameo": "g<PERSON><PERSON>", "lasergame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "gemauimessage", "idlegames": "gemaudigog", "fillintheblank": "llenwirblwch", "jeuxpc": "gemaupc", "rétrogaming": "retrogêmio", "logicgames": "gema<PERSON><PERSON><PERSON><PERSON>", "japangame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "rizzymlaengêm", "subwaysurf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdecelebrite": "gemaucelebau", "exitgames": "<PERSON><PERSON><PERSON><PERSON>", "5vs5": "5v5", "rolgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "gemautraddodiadol", "kniffel": "kniffel", "gamefps": "g<PERSON><PERSON><PERSON>", "textbasedgames": "g<PERSON>utes<PERSON>", "gryparagrafowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fantacalcio": "fantacalcio", "retrospel": "syll<PERSON><PERSON><PERSON>", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "gema<PERSON><PERSON>", "fliperama": "<PERSON><PERSON><PERSON>", "heroclix": "heroclix", "tablesoccer": "pêldroedbwrdd", "tischfußball": "tischfußball", "spieleabende": "nos<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "gemaucasual", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "g<PERSON><PERSON><PERSON>", "thiefgameseries": "cyfreslynlladron", "cranegames": "g<PERSON><PERSON><PERSON><PERSON>", "játék": "g<PERSON><PERSON>", "bordfodbold": "bordfodbôl", "jogosorte": "gemhaplwc", "mage": "mage", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "bingoswallet", "randomizer": "haprychwr", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "gemaupc", "socialdeductiongames": "gemaucymdeithascasgliadol", "dominos": "dominos", "domino": "domino", "isometricgames": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "goodoldgames": "hengemaugwych", "truthanddare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhac", "f2pgamer": "chwaraewrf2p", "free2play": "chwaraeamdim", "fantasygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "<PERSON><PERSON><PERSON><PERSON>", "driftgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "gemauotome", "halotvseriesandgames": "cyfresiteleduagêmauhalo", "mushroomoasis": "gwerddonmadarch", "anythingwithanengine": "unrhywbethgydag_injan", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "cadwynefateganau", "goodgamegiving": "gemgwychrhoi", "jugamos": "j<PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "gemaulabnol", "grykomputerowe": "gemaucomputr", "virgogami": "vir<PERSON><PERSON><PERSON>", "gogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "caruhunantrwychwarae", "gamemodding": "modiogemau", "crimegames": "g<PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "gema<PERSON><PERSON><PERSON>", "spelletjes": "gemau", "spacenerf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "<PERSON><PERSON><PERSON><PERSON>", "singleplayer": "unchwaraewr", "coopgame": "g<PERSON><PERSON><PERSON>dwei<PERSON><PERSON>", "gamed": "<PERSON><PERSON><PERSON><PERSON>", "forzahorizon": "forzahorizon", "nexus": "nexws", "geforcenow": "geforcenow", "maingame": "p<PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "br<PERSON>ndi<PERSON>rd", "scrabble": "scrabble", "schach": "gwyddbwyll", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "gwyddbwyll", "onitama": "onitama", "pandemiclegacy": "etifeddiaethypandemig", "camelup": "<PERSON><PERSON><PERSON><PERSON>", "monopolygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "brettspiele": "gemaubwrdd", "bordspellen": "gemaubwrdd", "boardgame": "gemfwrdd", "sällskapspel": "gêmfwrdd", "planszowe": "gemaubwrdd", "risiko": "risg", "permainanpapan": "gêmaufwrdd", "zombicide": "zombaid", "tabletop": "brwddgê<PERSON>", "baduk": "baduk", "bloodbowl": "pow<PERSON><PERSON><PERSON>", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "ewchgemfwrdd", "connectfour": "pedwarmewncysylltiad", "heroquest": "taithyrarwr", "giochidatavolo": "gemaufwrdd", "farkle": "farkle", "carrom": "carrom", "tablegames": "gêmaubwrdd", "dicegames": "<PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "gêmfwrdd", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "gema<PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "gemaucawod", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "gwrthdrawiadcosmig", "creationludique": "c<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "gemchwaraerôlbwrdd", "cardboardgames": "gemaucarddfwrdd", "eldritchhorror": "ars<PERSON><PERSON><PERSON>itchaidd", "switchboardgames": "gemauswitshfwrdd", "infinitythegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>diwedd", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "cymdeithasol", "juegodemesa": "gêmfwrdd", "planszówki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "bywydcefngwlad", "boardom": "diflastod", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "gêmfwrdd", "gameboard": "bw<PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "gêmaubwrdd", "twilightimperium": "twilightimperium", "horseopoly": "monopolyceffylau", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "plasaumadness", "gomoku": "gomoku", "giochidatavola": "gemaubrwdd", "shadowsofbrimstone": "cysgodionbrimstone", "kingoftokyo": "brenintokyo", "warcaby": "warcaby", "táblajátékok": "gemaubwrdd", "battleship": "llongryfel", "tickettoride": "<PERSON><PERSON><PERSON><PERSON>", "deskovehry": "gemaubwrdd", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "gemaubwrdd", "stolníhry": "gemaubwrdd", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "gemaubyrdд", "gesellschaftsspiele": "gemaucymdeithasol", "starwarslegion": "starwarslegion", "gochess": "ewchchwaraegwyddbwyll", "weiqi": "weiqi", "jeuxdesocietes": "gemaubwrdd", "terraria": "terraria", "dsmp": "dsmp", "warzone": "rhyfelfa", "arksurvivalevolved": "arksurvivalevolved", "dayz": "<PERSON><PERSON><PERSON><PERSON>", "identityv": "identityv", "theisle": "ynys", "thelastofus": "yrolafohonym", "nomanssky": "nomasnsgofod", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "ym<PERSON><PERSON>ni", "eco": "eco", "monkeyisland": "ynysymwnci", "valheim": "valheim", "planetcrafter": "crefwrplanedau", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "ofn", "witchit": "de<PERSON><PERSON>", "pathologic": "patholegol", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dnbt", "thelongdark": "yhirywyllwch", "ark": "arc", "grounded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "cyflwrpydru2", "vrising": "vrising", "madfather": "tadgwallgo", "dontstarve": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "llwybryteitlau", "frictionalgames": "frictionalgames", "hexen": "gwrachod", "theevilwithin": "yddrwgodimewn", "realrac": "realrac", "thebackrooms": "ystafelloeddcefn", "backrooms": "ystafelloeddcefn", "empiressmp": "empiressmp", "blockstory": "stor<PERSON><PERSON><PERSON><PERSON>", "thequarry": "ychwarelr", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "gemthewalkingdead", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON>", "riseofempires": "codia<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagestory": "storihenffasiwn", "arksurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "barotrauma": "bar<PERSON><PERSON><PERSON>", "breathedge": "<PERSON><PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastsofbermuda": "bwystfilodbermuda", "frostpunk": "frostpunk", "darkwood": "coeddywyll", "survivalhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "preswylddrwgpreswylydd", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "trenygwagle", "lifeaftergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "gemaugoresgyn", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "yr<PERSON><PERSON><PERSON><PERSON><PERSON>a", "scpfoundation": "scpfoundation", "greenproject": "prosiectgwyrdd", "kuon": "kuon", "cryoffear": "cridaganofn", "raft": "raft", "rdo": "rdo", "greenhell": "uffernwyrdd", "residentevil5": "residentevil5", "deadpoly": "deadpoli", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "mamgu", "littlenightmares2": "hunllefaubachynos2", "signalis": "signalis", "amandatheadventurer": "amanda<PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "meibionyff<PERSON>t", "rustvideogame": "gêmfideorust", "outlasttrials": "treialongylasty", "alienisolation": "ynysiadallfydol", "undawn": "undawnxboo", "7day2die": "7niwrnod2farw", "sunlesssea": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "<PERSON><PERSON><PERSON>", "propnight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadisland2": "ynysmeirw2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "cariadgwampir", "deathverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "diw<PERSON>dt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soma": "soma", "fearandhunger": "ofnallwgu", "stalkercieńczarnobyla": "stalkercysgodchernobyl", "lifeafter": "bywydwedi", "ageofdarkness": "oesywyllwch", "clocktower3": "tyrclocyn3", "aloneinthedark": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "teyrnasmediefol", "projectnimbusgame": "gêmprojectnimbus", "eternights": "eternights", "craftopia": "crefftopia", "theoutlasttrials": "ytreialongoroesi", "bunker": "by<PERSON>ar", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "lladdroncorrach", "warhammer40kcrush": "crushwarhammer40k", "wh40": "wh40", "warhammer40klove": "cariadwarhammer40k", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "dialedd", "ilovesororitas": "dwincaru_sororitas", "ilovevindicare": "dwincaruvindicare", "iloveassasinorum": "d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templovenenum": "<PERSON><PERSON><PERSON><PERSON>", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "<PERSON><PERSON><PERSON>", "ageofempires": "oesoedyrymerod<PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "<PERSON><PERSON><PERSON>", "terraformingmars": "terraformiomawrth", "heroesofmightandmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "btd6": "btd6", "supremecommander": "pencampwrgoru<PERSON><PERSON>", "ageofmythology": "o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args": "args", "rime": "rime", "planetzoo": "planedsŵ", "outpost2": "tysteb2", "banished": "wediialltudio", "caesar3": "caesar3", "redalert": "rhybuddcoch", "civilization6": "ciwilizasiwn6", "warcraft2": "warcraft2", "commandandconquer": "gorchymynagor<PERSON>fygu", "warcraft3": "warcraft3", "eternalwar": "rhyfeloesoesol", "strategygames": "gemaustrategaeth", "anno2070": "anno2070", "civilizationgame": "gêmgwareiddiad", "civilization4": "gwareiddiad4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "sbor", "totalwar": "rhyfellwy<PERSON>", "travian": "travian", "forts": "caerau", "goodcompany": "cwmnida", "civ": "dinasyddiaeth", "homeworld": "bydcartref", "heidentum": "<PERSON><PERSON><PERSON>", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "cyflymachnagolau", "forthekings": "idrenhinoedd", "realtimestrategy": "strategaethamsereal", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "deyrnasdaugoron", "eu4": "eu4", "vainglory": "balchder", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON><PERSON>", "anno": "anno", "battletech": "techbrwydr", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattric", "davesfunalgebraclass": "doslalgdavehwyl", "plagueinc": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "theorycraft": "theorygynnllunio", "mesbg": "mesbg", "civilization3": "diwylliant3", "4inarow": "4ynolynol", "crusaderkings3": "brenhinoeddycroesgadwyr3", "heroes3": "arwyr3", "advancewars": "rhyfeloeddblaen", "ageofempires2": "oesoedyrymerodraethau2", "disciples2": "disgyblion2", "plantsvszombies": "planhigyn<PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "gemaustrategol", "stratejioyunları": "gemaustrategol", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "oescyfnodrhyfedd", "dinosaurking": "brenindeinosoriaid", "worldconquest": "bydgoncwest", "heartsofiron4": "calonnauohaearn4", "companyofheroes": "cwmniooarwyr", "battleforwesnoth": "brwydrdrosesgyn", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gŵyddgŵyddhwyaden", "phobies": "<PERSON><PERSON><PERSON><PERSON>", "phobiesgame": "g<PERSON><PERSON>ff<PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "gemauc<PERSON>royale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "planblaenol", "turnbased": "tron<PERSON><PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "oesoesymerodraethau4", "civilization5": "gwareiddiad5", "victoria2": "victoria2", "crusaderkings": "brenhinoeddycroesgadau", "cultris2": "cultris2", "spellcraft": "s<PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "rhyfelydymerodriaethstarwars", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategaeth", "popfulmail": "popfulmail", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "meistrde<PERSON>wd", "dysonsphereprogram": "sfferdysonsrhaglen", "transporttycoon": "brenintrafnidiaeth", "unrailed": "unrailed", "magicarena": "<PERSON><PERSON>na", "wolvesville": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ooblets": "ooblets", "planescapetorment": "dihangtartariad", "uplandkingdoms": "<PERSON>en<PERSON><PERSON><PERSON><PERSON><PERSON>", "galaxylife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slaythespire": "slaythespire", "battlecats": "catho<PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "rasiogwefr", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "angenarhyflymder", "needforspeedcarbon": "angenamamddercarbon", "realracing3": "rasiogo3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON>", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "alicegwallgofrwyddyndychwelyd", "darkhorseanthology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "pumnosonyn<PERSON><PERSON>s", "saiko": "seico", "fatalframe": "fframangheuol", "littlenightmares": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "gartregar<PERSON>r", "deadisland": "ynysymarw", "litlemissfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzero": "prosiectdim", "horory": "arswyd", "jogosterror": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor": "he<PERSON><PERSON><PERSON>my<PERSON>", "helloneighbor2": "heloymydog2", "gamingdbd": "gamingdbd", "thecatlady": "ygathwraig", "jeuxhorreur": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "ydewiniaethycasglu", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "cardynerbynydynolryw", "cribbage": "cribej", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "<PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "cardia<PERSON>ic", "lor": "lor", "euchre": "iwcyr", "thegwent": "gwent<PERSON>n", "legendofrunetera": "chwed<PERSON>runeterra", "solitaire": "<PERSON><PERSON><PERSON><PERSON>", "poker": "<PERSON><PERSON><PERSON>", "hearthstone": "tanllechfa<PERSON>", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "r<PERSON><PERSON><PERSON>", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metasŵ", "tradingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "cardiaupoke<PERSON>", "fleshandbloodtcg": "tcgcnawdagwaed", "sportscards": "cardiaucamp", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "<PERSON><PERSON><PERSON><PERSON>", "warcry": "bloeddgad", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "ygw<PERSON><PERSON><PERSON>d", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "disgdwedd", "yugiohgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "de<PERSON><PERSON>", "blueeyeswhitedragon": "llygaidglasdraiggwyn", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rymi", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dwbwl", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "g<PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "gemaucardiау", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "<PERSON><PERSON><PERSON><PERSON>", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "ysbrydionbrwydr", "battlespiritssaga": "sagabrwydrysbrydion", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "žolíky": "žolíky", "facecard": "c<PERSON><PERSON><PERSON><PERSON>", "cardfight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "pencampwraumarvel", "magiccartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "ebolyseryg", "cyberse": "cybsyrs", "classicarcadegames": "gemauarcêdclasurol", "osu": "osu", "gitadora": "gitadora", "dancegames": "gemaudawnsio", "fridaynightfunkin": "nosgwenerdrampio", "fnf": "nosgwe", "proseka": "<PERSON><PERSON>", "projectmirai": "prosiectmirai", "projectdiva": "prosiect<PERSON><PERSON>", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "ar<PERSON><PERSON><PERSON>", "justdance": "dimondawnsio", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "ysgytwdyrmeirw", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "canolyddawnsio", "rhythmgamer": "chwaraeurrythm", "stepmania": "stepmania", "highscorerythmgames": "sgoriauuchgemaurhythm", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "rhythmnefoedd", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON>arhe<PERSON>", "auditiononline": "<PERSON><PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "gemaurhythm", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "<PERSON><PERSON><PERSON><PERSON>", "cubing": "ciwbio", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spotit": "sbydifo", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blocydocy", "logicpuzzles": "posaurhesymeg", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "pospen", "rubikscube": "ciwbrubik", "crossword": "cro<PERSON><PERSON>", "motscroisés": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "krzyżówki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nonogram": "nonogram", "bookworm": "moledr", "jigsawpuzzles": "jigsos", "indovinello": "posyn", "riddle": "pos", "riddles": "posceneon", "rompecabezas": "posau", "tekateki": "posaupenbleth", "inside": "ytumewn", "angrybirds": "<PERSON><PERSON><PERSON>", "escapesimulator": "diancsimiwleiddiwr", "minesweeper": "sapwrffr<PERSON>dron", "puzzleanddragons": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesport": "campgêm", "escaperoomgames": "g<PERSON><PERSON><PERSON><PERSON>", "escapegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "3dposau", "homescapesgame": "gemhomescapes", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "posau", "kulaworld": "<PERSON><PERSON><PERSON><PERSON>", "myst": "cwd", "riddletales": "stra<PERSON><PERSON><PERSON><PERSON>", "fishdom": "pysgodlys", "theimpossiblequiz": "ycwi<PERSON><PERSON>sib", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "chwaraegem3cyfatebol", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON><PERSON>", "rubikcube": "ciwbrubik", "cuborubik": "ciwbrubik", "yapboz": "yapboz", "thetalosprinciple": "egwyddorthalos", "homescapes": "cartrefigadau", "puttputt": "<PERSON><PERSON><PERSON><PERSON>", "qbert": "qbert", "riddleme": "posiad<PERSON>", "tycoongames": "gemautycŵn", "cubosderubik": "ciwbsrubik", "cruciverba": "cruciver<PERSON>", "ciphers": "siffer", "rätselwörter": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON>", "turnipboy": "ho<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "poshotpen", "nobodies": "neb", "guessing": "dy<PERSON><PERSON>", "nonograms": "nonogramau", "kostkirubika": "ciwbriwbrig", "crypticcrosswords": "c<PERSON><PERSON>iri<PERSON><PERSON><PERSON><PERSON>", "syberia2": "syberia2", "puzzlehunt": "he<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "helfattrysorfeon", "catcrime": "trose<PERSON>cat<PERSON>", "quebracabeça": "torbenpenbleth", "hlavolamy": "<PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "hunanddiffiniol", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "gêmgŵ<PERSON><PERSON><PERSON>l", "cassetête": "penbleth", "limbo": "limbo", "rubiks": "rubiks", "maze": "dryswch", "tinykin": "tinykin", "rubikovakostka": "ciwbrubik", "speedcube": "ciwbcyflymder", "pieces": "darnau", "portalgame": "<PERSON><PERSON><PERSON><PERSON>", "bilmece": "bilmece", "puzzelen": "posau", "picross": "picross", "rubixcube": "ciwbrubix", "indovinelli": "posau", "cubomagico": "ciwbhud", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopoly": "monopoli", "futurefight": "brwydryfory", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "blaidd_unig", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bywydbit", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "s<PERSON><PERSON><PERSON><PERSON>", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alchemystars": "seralchemeg", "stateofsurvival": "cyflw<PERSON><PERSON><PERSON>", "mycity": "fyndinas", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "tŵramddiffynbalŵns", "btd": "pen<PERSON><PERSON><PERSON>dd<PERSON><PERSON>", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfrynt", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "dewis<PERSON><PERSON><PERSON>", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "ffanpetrol", "tacticool": "<PERSON><PERSON>l", "cookierun": "cookierun", "pixeldungeon": "carcharannylled", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftsman": "crefftwr", "supersus": "<PERSON><PERSON><PERSON><PERSON>", "slowdrive": "gyrrungwyllt", "headsup": "rhybudd", "wordfeud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bedwars": "bedwars", "freefire": "tânrhydd", "mobilegaming": "gemogêm", "lilysgarden": "<PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "croesianifeiliaid", "bgmi": "bgmi", "teamfighttactics": "tactegaubrwydrotimoedd", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "negesydddirgel", "callofdutymobile": "callofdutymobile", "thearcana": "yrarcana", "8ballpool": "pwl8p<PERSON>l", "emergencyhq": "pencadlysargyfwng", "enstars": "enserenni", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "ysgwydacaflonyddu", "ml": "ml", "bangdream": "breuddwydswnllyd", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "amsertywysoges", "beatstar": "s<PERSON><PERSON><PERSON><PERSON>", "dragonmanialegend": "chwedldraigman<PERSON>", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "cariad<PERSON><PERSON>", "androidgames": "gemauandroid", "criminalcase": "achosdroseddol", "summonerswar": "summonerswar", "cookingmadness": "gwallgofrwyddcoginio", "dokkan": "dokkan", "aov": "gce", "triviacrack": "triviacrack", "leagueofangels": "cynghrairyangelion", "lordsmobile": "lordsmobile", "tinybirdgarden": "gerddfachaderynpitw", "gachalife": "<PERSON><PERSON>d<PERSON><PERSON>", "neuralcloud": "cwmwlniwral", "mysingingmonsters": "mynghaneuwynbwystfilod", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "<PERSON><PERSON><PERSON><PERSON>", "raidshadowlegends": "raidshadowlegends", "warrobots": "robotsrhyfel", "mirrorverse": "mirrorbyd", "pou": "pou", "warwings": "adenyddrhyfel", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "chwedeulbythol", "futime": "sbri", "antiyoy": "gwrthydy", "apexlegendmobile": "apexlegendmobileyo", "ingress": "mynediad", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "meistrary_geiniogau", "punishinggrayraven": "pwniograyraven", "petpals": "ffrindanifail", "gameofsultans": "gemosultaniaid", "arenabreakout": "arenabrêcowt", "wolfy": "ble<PERSON><PERSON>", "runcitygame": "gemrhedegdinas", "juegodemovil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avakinlife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kogama": "kogama", "mimicry": "maldo<PERSON>", "blackdesertmobile": "anialwchtywylls<PERSON><PERSON><PERSON>", "rollercoastertycoon": "meistrcoasterantur", "grandchase": "grandchase", "bombmebrasil": "bomiwfibrasil", "ldoe": "dha", "legendonline": "<PERSON><PERSON><PERSON><PERSON>", "otomegame": "<PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "nikkidisgleiriol", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sealm": "seilm", "shadowfight3": "cysgodymladd3", "limbuscompany": "cwmnilimbus", "demolitionderby3": "derbidymchwel3", "wordswithfriends2": "geiriauchyfffrindiau2", "soulknight": "marchogenydenaid", "purrfecttale": "cneswncam<PERSON>", "showbyrock": "sioeroc", "ladypopular": "merchedpoblogaidd", "lolmobile": "lo<PERSON><PERSON><PERSON><PERSON>", "harvesttown": "trefcynhaeaf", "perfectworldmobile": "bydper<PERSON>ait<PERSON><PERSON><PERSON><PERSON>", "empiresandpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empirespuzzles": "posa<PERSON><PERSON><PERSON>", "dragoncity": "<PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fanny": "f<PERSON>i", "littlenightmare": "<PERSON><PERSON><PERSON><PERSON>bach", "aethergazer": "aethergazer", "mudrunner": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "eversoul", "gunbound": "gyn<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "marchognosol", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombistrosydd", "eveechoes": "eveechoes", "jogocelular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "mambinio", "cabalmobile": "cabalsymudol", "streetfighterduel": "gorneststreetfighter", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gemiobgmi", "girlsfrontline": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jurassicworldalive": "jurassicworldynfyw", "soulseeker": "eneidchwiliwr", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "stor<PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "carx<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosmobile": "gema<PERSON><PERSON><PERSON>", "legendofneverland": "ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeraiders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmobile": "gemaugêm", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "ycathodybrwydr", "dnd": "dnd", "quest": "cwest", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgbwrdd", "worldofdarkness": "bydtywyllwch", "travellerttrpg": "teithiwrtrpg", "2300ad": "2300oc", "larp": "larp", "romanceclub": "clwb<PERSON><PERSON><PERSON>", "d20": "d20", "pokemongames": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "animepokemon", "pokémongo": "pokémongo", "pokemonred": "pokemonred", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "sgwrsbot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "poke<PERSON><PERSON><PERSON>or", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "timrocket", "furret": "furret", "magikarp": "psygodyn", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "poced<PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "t<PERSON><PERSON><PERSON><PERSON>", "pokeball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "cathleuol", "shinypokemon": "poke<PERSON><PERSON>w", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "hwyaden_seicig", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "pokemoncysgu", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "plantatphoc<PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "helwrsgleinioג", "ajedrez": "gwyddbwyll", "catur": "<PERSON>ur", "xadrez": "gwyddbwyll", "scacchi": "gwyddbwyll", "schaken": "gwyddbwyll", "skak": "skak", "ajedres": "gwyddbwyll", "chessgirls": "merchedy_gwyddbwyll", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "blitzbyd", "jeudéchecs": "jeudéchecs", "japanesechess": "shogijapan", "chinesechess": "gwyddbwylltsien<PERSON>idd", "chesscanada": "gwyddbwyllcanada", "fide": "fide", "xadrezverbal": "<PERSON><PERSON><PERSON>", "openings": "<PERSON><PERSON><PERSON>", "rook": "castell", "chesscom": "chesscom", "calabozosydragones": "dungeonsdragons", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "meist<PERSON>lasmwdan", "tiamat": "tiamat", "donjonsetdragons": "ceircarth<PERSON><PERSON><PERSON><PERSON>", "oxventure": "oxventure", "darksun": "haultywyll", "thelegendofvoxmachina": "chwelyrvoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "corsydd_tywyll", "minecraftchampionship": "pencampwriaethminecraft", "minecrafthive": "cwchg<PERSON><PERSON><PERSON>craft", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modsminecraft", "mcc": "mcc", "candleflame": "fflam_cannwyll", "fru": "ffrind", "addons": "ychwanegion", "mcpeaddons": "ychwanosionmcpe", "skyblock": "blocawyr", "minecraftpocket": "minecraftpoced", "minecraft360": "minecraft360", "moddedminecraft": "moddedminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "rhwngdiroedd", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "dinasminecraft", "pcgamer": "pcgamer", "jeuxvideo": "gemaufideo", "gambit": "gambit", "gamers": "gamers", "levelup": "<PERSON><PERSON><PERSON><PERSON>", "gamermobile": "gemwrar<PERSON><PERSON><PERSON>l", "gameover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "gemaupc", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "gemaupc", "casualgaming": "gemaucasual", "gamingsetup": "setupgema<PERSON>", "pcmasterrace": "p<PERSON>w<PERSON><PERSON><PERSON><PERSON>", "pcgame": "pc<PERSON><PERSON><PERSON>", "gamerboy": "bachgengê<PERSON><PERSON>", "vrgaming": "gemvr", "drdisrespect": "drdisrespect", "4kgaming": "gemau4k", "gamerbr": "gamerbr", "gameplays": "gemau", "consoleplayer": "chwaraewr_consol", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgêmŵr", "onlinegaming": "gemioanarlein", "semigamer": "lledchwaraewr", "gamergirls": "merchedigemau", "gamermoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerguy": "bach<PERSON><PERSON><PERSON>", "gamewatcher": "gwylwrgêm", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "merchgêmau", "otoge": "gemgauffeirched", "dedsafio": "dedsafio", "teamtryhard": "tîmtreiocaled", "mallugaming": "mallugê<PERSON>", "pawgers": "pawgers", "quests": "cwests", "alax": "alax", "avgn": "avgn", "oldgamer": "hengeimwr", "cozygaming": "gemiocosy", "gamelpay": "gamelpay", "juegosdepc": "gemaupc", "dsswitch": "newid<PERSON>s", "competitivegaming": "chwaraecystadleuol", "minecraftnewjersey": "minecraftnewjersey", "faker": "ffug", "pc4gamers": "pc4gamerswyr", "gamingff": "gemoff", "yatoro": "yatoro", "heterosexualgaming": "gemauge<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "pcchwarae", "girlsgamer": "merchedigêmwyr", "fnfmods": "modsfnf", "dailyquest": "cwestbeunyddiol", "gamegirl": "merchgêm", "chicasgamer": "merchedigamer", "gamesetup": "trefinugêm", "overpowered": "rhydrymus", "socialgamer": "gemwrgymdeithasol", "gamejam": "gamejam", "proplayer": "prowrfeswr", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "fytîm", "republicofgamers": "gweriniaethygeimwyr", "aorus": "aorus", "cougargaming": "gemiociwgar", "triplelegend": "triphlychwed<PERSON>", "gamerbuddies": "cyfeilliongemau", "butuhcewekgamers": "angenmerchgêmwyr", "christiangamer": "gameriacristnogol", "gamernerd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nerdgamer": "nerdgemwr", "afk": "afk", "andregamer": "andregamer", "casualgamer": "chwara<PERSON>r_achly<PERSON>rol", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "ansicr", "gemers": "gemyrs", "oyunizlemek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamertag": "gamertag", "lanparty": "partilan", "videogamer": "gemwr", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "gemwrplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "chwaraewyriach", "gtracing": "rasiogyda", "notebookgamer": "chwaraewrllyfraunodiadau", "protogen": "protogen", "womangamer": "merchedyngêmio", "obviouslyimagamer": "ynddigonnwingamer", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "bwydgwyllt", "humanfallflat": "codwyninsaml", "supernintendo": "archornintendo", "nintendo64": "nintendo64", "zeroescape": "dimdiangc", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusic", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "newid", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "modrwynffit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "twrnaiace", "ssbm": "ssbm", "skychildrenofthelight": "plant_y_goleuni", "tomodachilife": "bywydtomodachi", "ahatintime": "ahat<PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "tearsofthekingdom", "walkingsimulators": "eresgerdded", "nintendogames": "gemaunintendo", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "llewadcynhaeaf", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "anadlyrg<PERSON><PERSON><PERSON>", "myfriendpedro": "myffrindpedro", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51g<PERSON>m", "earthbound": "daearol", "tales": "stra<PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "p<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "<PERSON><PERSON><PERSON>", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "strategaethtriongl", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "concyrs<PERSON><PERSON><PERSON><PERSON><PERSON>g<PERSON><PERSON>", "nintendos": "nintendos", "new3ds": "3dsnewydd", "donkeykongcountry2": "gwladdiddykong2", "hyrulewarriors": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioacsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "ma<PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "rhwyg", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "lollas", "urgot": "urgot", "zyra": "zyra", "redcanids": "canoidscoch", "vanillalol": "vanillaefennylol", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "cynghrairchwed<PERSON>", "tốcchiến": "ymladdfflach", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendscymru", "aatrox": "aatrox", "euw": "ych", "leagueoflegendseuw": "cynghrairchwedleuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollrwtsh", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "cymanglechwyr", "gaminglol": "gêmiolol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "gemfortnight", "gamingfortnite": "gamiofortnite", "fortnitebr": "fortnitebr", "retrovideogames": "gemaufideoretro", "scaryvideogames": "gemauffide<PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "gwneuthurwrgêmfideo", "megamanzero": "megamanzero", "videogame": "gem<PERSON><PERSON><PERSON>", "videosgame": "fideosgêm", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "<PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "ffrindiaupwff", "farmingsimulator": "efelychiadffermio", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxcymru", "robloxdeutsch": "rob<PERSON>xcymraeg", "erlc": "erlc", "sanboxgames": "gemausandbox", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "coed<PERSON><PERSON>", "dreamscape": "breuddwydwedd", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "gofodmarw", "amordoce": "<PERSON>ia<PERSON><PERSON><PERSON>", "videogiochi": "fideogêmau", "theoldrepublic": "hen<PERSON><PERSON><PERSON>", "videospiele": "fideogemau", "touhouproject": "<PERSON><PERSON><PERSON><PERSON>", "dreamcast": "breuddwydcast", "adventuregames": "g<PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "anturactiwn", "storyofseasons": "storiytymhora<PERSON>", "retrogames": "gema<PERSON><PERSON>", "retroarcade": "<PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "cyfrifiaduronhenffasiwn", "retrogaming": "chwaraehenfasiwn", "vintagegaming": "gemauhenffasiwn", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "anghyfiawnder2", "shadowthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "<PERSON><PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "serth", "mystgames": "gemaucyfrinachol", "blockchaingaming": "gemaublockcadwyn", "medievil": "canoloesol", "consolegaming": "gemauconsol", "konsolen": "consolau", "outrun": "guro", "bloomingpanic": "panicblodeuo", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "cwesthymerchfwystfil", "supergiant": "cawr", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "simffermio", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "gemaub<PERSON><PERSON><PERSON><PERSON><PERSON>", "interactivefiction": "ficsiynrhyngweithiol", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "nofelweledol", "visualnovels": "nofelauweledol", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tcrghost": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "blwchtywod", "aestheticgames": "gemaugwedd", "novelavisual": "nofelweledol", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "llaw_duw", "leafblowerrevolution": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>dd<PERSON>", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starrail": "starrail", "keyblade": "arfallwedd", "aplaguetale": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "fnafsometimes": "fnafsometimes", "novelasvisuales": "nofelauweledol", "robloxbrasil": "robloxcymru", "pacman": "pacman", "gameretro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videojuejos": "gemaufideo", "videogamedates": "gemauafideopwyn<PERSON><PERSON><PERSON>", "mycandylove": "fymelysuncariad", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "g<PERSON><PERSON>hul<PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "dychweliaddialedd", "gamstergaming": "gamstergêmio", "dayofthetantacle": "diwrnodytentacl", "maniacmansion": "plastigofnadwy", "crashracing": "rasiodarogwrthdar<PERSON>", "3dplatformers": "platfformwyr3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "c<PERSON><PERSON><PERSON><PERSON>", "storygames": "gemaust<PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "swniangosgoi", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "defnydd<PERSON><PERSON><PERSON>", "offmortisghost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinybunny": "c<PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "pwerup", "katanazero": "katanasero", "famicom": "famicom", "aventurasgraficas": "ant<PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "fflachcyflym", "fzero": "fzero", "gachagaming": "gêmia<PERSON><PERSON>", "retroarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "anialwch", "powerwashsim": "sim<PERSON><PERSON><PERSON><PERSON><PERSON>", "coralisland": "ynysc<PERSON>rel", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "rhyfelwyrranime2", "footballfusion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "pentwrcywilydd", "simulator": "simulydd", "symulatory": "efelychiadau", "speedrunner": "cyflymrhedwr", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gem<PERSON><PERSON><PERSON>", "gaiaonline": "<PERSON><PERSON><PERSON><PERSON>", "korkuoyunu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "skylander": "skylander", "boyfrienddungeon": "cariaddonjon", "toontownrewritten": "toontownailysgrifennu", "simracing": "r<PERSON><PERSON><PERSON><PERSON>", "simrace": "rasiosim", "pvp": "pvp", "urbanchaos": "anhrefndrefol", "heavenlybodies": "cyrffnefolaidd", "seum": "seum", "partyvideogames": "chwaraeonfidigêmpartion", "graveyardkeeper": "ceidwadmynwent", "spaceflightsimulator": "efelychyddgofod", "legacyofkain": "etifedd<PERSON>thkain", "hackandslash": "hacaslash", "foodandvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "fideosgê<PERSON><PERSON>", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "simulator<PERSON><PERSON>", "horizonworlds": "bydoeddgorwel", "handygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racingsimulator": "e<PERSON><PERSON><PERSON>dd<PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "pyrtholympus", "monsterhunternow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rebelstar": "serenrebel", "indievideogaming": "gêmaufideoannibynol", "indiegaming": "gemioindie", "indievideogames": "gemaufideoannibynol", "indievideogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "caerfowr", "unbeatable": "anhygoel", "projectl": "prosiectl", "futureclubgames": "gemauclwbdyfodol", "mugman": "myggyn", "insomniacgames": "jocaumdicwsg", "supergiantgames": "gemausupergigantic", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "gemceleste", "aperturescience": "aperturescience", "backlog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamebacklog": "ciwllwythgemau", "gamingbacklog": "pentwrgê<PERSON><PERSON>", "personnagejeuxvidéos": "cymeriadaugêmaufideo", "achievementhunter": "llowrhelwyr", "cityskylines": "awyrlunyddinas", "supermonkeyball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deponia": "deponia", "naughtydog": "cidrwg", "beastlord": "arglwyddibwystfil", "juegosretro": "gema<PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "g<PERSON><PERSON><PERSON>leyparable", "reservatoriodedopamin": "cronfahaeddopamin", "staxel": "staxel", "videogameost": "bandsonwrefideogemau", "dragonsync": "syncydraig", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "dwincariokofxv", "arcanum": "<PERSON><PERSON><PERSON><PERSON>", "neoy2k": "neoy2k", "pcracing": "rasiop<PERSON>", "berserk": "gwyllt", "baki": "baki", "sailormoon": "lleuadforwyn", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "cynhwrf", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animedrist", "darkerthanblack": "ty<PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "graddiolanime", "animewithplot": "animehefoplot", "pesci": "pesci", "retroanime": "animereto", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "an<PERSON><PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON><PERSON>", "darklord": "arglwyddtywyll", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "me<PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "haff<PERSON>me", "2000sanime": "animeoedd2000", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "<PERSON><PERSON><PERSON>", "thevisionofescaflowne": "gweledigaethescaflowne", "slayers": "lladdwyr", "tokyomajin": "tokyomajin", "anime90s": "anime90au", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "pysgod<PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "hanakokuntygwelltaibach", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "mangaskipbeat", "vanitas": "oferedd", "fireforce": "gry<PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "dyddiadurdyfodol", "fairytail": "tylwythteg", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "paraseit", "punpun": "pwndipwn", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "meledinôrmôr", "kamisamakiss": "<PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "mangaarswyd", "romancemangas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "lllyndd<PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "geniws", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "c<PERSON><PERSON><PERSON><PERSON><PERSON>", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "mynegaihudolpenodol", "sao": "sao", "blackclover": "c<PERSON><PERSON><PERSON><PERSON>", "tokyoghoul": "tokyoghoul", "onepunchman": "undynunddyrnod", "hetalia": "hetalia", "kagerouproject": "prosiectkagerou", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animechwaraeon", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "curiadangelion", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagatanyugdrwg", "shounenanime": "animeshonen", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "ybachgenarthebwystfil", "fistofthenorthstar": "dw<PERSON><PERSON><PERSON><PERSON>d", "mazinger": "mazinger", "blackbuttler": "gwasgwrdu", "towerofgod": "tŵrduw", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "suticadw<PERSON><PERSON>", "fullmoonwosagashite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "ciwtachreulon", "martialpeak": "copamladfaol", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "sero_dau", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "g<PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "fanny", "vegeta": "lys<PERSON><PERSON><PERSON>", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "morwrsatŵrn", "dio": "dio", "sailorpluto": "môriwrnp<PERSON><PERSON><PERSON>", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON>", "chainsawman": "<PERSON><PERSON><PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inumimi": "incwn", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "cleddyfmawr", "loli": "loli", "horroranime": "animearswydigrwydd", "fruitsbasket": "basgefffrwy<PERSON><PERSON>", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "cariadfyw", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "dieithryndimtraethfi", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "ynyshonedigaddawyd", "monstermanga": "monstermanga", "yourlieinapril": "<PERSON><PERSON><PERSON><PERSON>ddynebrill", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "seraff<PERSON>ddiwedd", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "carcha<PERSON>rdy<PERSON>nderoedd", "jojolion": "jojo<PERSON>", "deadmanwonderland": "deadmanwonderland", "bannafish": "bananabysgod", "sukuna": "<PERSON>kuna", "darwinsgame": "gem<PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "calonnauepandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "llinelldiafol", "toyoureternity": "itinamtragwyddoldeb", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "cyfnodglas", "griffithberserk": "griffithyngwallgo", "shinigami": "shinigami", "secretalliance": "c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "dilëwyd", "bluelock": "glasglo", "goblinslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "curiadsh<PERSON>jo", "vampireknight": "marchogfampir", "mugi": "mygi", "blueexorcist": "ydeisbydclas", "slamdunk": "slamdync", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spyfamily": "teuluysbïwr", "airgear": "<PERSON><PERSON><PERSON><PERSON>", "magicalgirl": "merchferiol", "thesevendeadlysins": "ysaithpechodneuol", "prisonschool": "ysgolgarchar", "thegodofhighschool": "yduwynysgolyuwchradd", "kissxsis": "cusan_chwaer", "grandblue": "glasmawr", "mydressupdarling": "fynhol<PERSON><PERSON>dg<PERSON><PERSON>gio", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeuniferse", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoweditarryn", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "an<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "mangacariad", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "<PERSON><PERSON><PERSON><PERSON>", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "<PERSON><PERSON><PERSON><PERSON>", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayeratycleddyf", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "dyrnaiddân", "adioseri": "ffarw<PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "sertrebyrengilydd", "romanceanime": "animecariad", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "factorintegrolsao", "cherrymagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "t<PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "co<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "<PERSON><PERSON><PERSON>", "highschoolofthedead": "ysgoluw<PERSON>raddfymarwolion", "germantechno": "technogermanaidd", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "tywy<PERSON><PERSON><PERSON><PERSON>", "tonikawa": "t<PERSON><PERSON><PERSON><PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "classllofryddiaethysgol", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejapan", "animespace": "gofodanime", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "go<PERSON><PERSON><PERSON><PERSON>", "animedub": "animedubbio", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indieanime": "animeannibynnol", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "dynllygoden", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "bachgencath", "gashbell": "gashbell", "peachgirl": "merch<PERSON>li", "cavalieridellozodiaco": "march<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "caloncmanga", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "cofnoddragnarok", "funamusea": "hwyladoniol", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "gorddrwsio", "toriko": "<PERSON><PERSON>o", "ravemaster": "meistrrave", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "gweithy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangaywbywyd", "dropsofgod": "crefftddiod", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "haremg<PERSON>rthwyneb", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "athrogwychonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "fybostadi", "gear5": "gêr5", "grandbluedreaming": "breudd<PERSON><PERSON><PERSON>", "bloodplus": "bloodplus", "bloodplusanime": "animegwaedplus", "bloodcanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodc": "gwadc", "talesofdemonsandgods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "goreanime", "animegirls": "animegenod", "sharingan": "<PERSON><PERSON>", "crowsxworst": "brainxgwaethaf", "splatteranime": "animesblatsh", "splatter": "sblash", "risingoftheshieldhero": "codiryt<PERSON><PERSON><PERSON><PERSON>", "somalianime": "animesomalïaidd", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimednabachod", "animeyuri": "animeyuri", "animeespaña": "animesbaen", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "plantymorfil", "liarliar": "celwyddgicelwyddgi", "supercampeones": "pencampwrgorau", "animeidols": "animeidols", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "hogenodswythiol", "callofthenight": "<PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "brwydrwrbakugan", "bakuganbrawlers": "ymladdfabakugan", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "gardddycysgod", "tsubasachronicle": "tsubasachronicle", "findermanga": "finderman<PERSON>", "princessjellyfish": "morgad<PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "cusanparad<PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animeverse": "animefyd", "persocoms": "cymeriadaupersonol", "omniscientreadersview": "golwgdarllenwyddhol<PERSON><PERSON>dus", "animecat": "cathaniме", "animerecommendations": "animeargymhellion", "openinganime": "animeagoriadol", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "ymladdwrgunsymudol", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebiganime", "bleach": "cannu", "deathnote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmetalalchemist": "alchemyddmetelllawn", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animefilwrol", "greenranger": "rangergwyrdd", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rdaf", "animecity": "dinasnime", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "undarn", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "antur<PERSON><PERSON><PERSON><PERSON>ig", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaku", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "lladdwrcythreuliaid", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "ymosodiadartitan", "erenyeager": "erenyeager", "myheroacademia": "myheroaacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "sgwadarolygon", "onepieceanime": "animeonepieceanime", "attaquedestitans": "ymosodiadyrtitaniaid", "theonepieceisreal": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "revengers": "dialwyr", "mobpsycho": "mobseico", "aonoexorcist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "joyboyeffect": "e<PERSON><PERSON><PERSON><PERSON><PERSON>", "digimonstory": "stor<PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "carcha<PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "metalocalyps", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "clwblletyanouran", "flawlesswebtoon": "webtoonperffaith", "kemonofriends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "gwersyllaysgafn", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "hanes<PERSON><PERSON><PERSON>d"}