{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "am<PERSON><PERSON><PERSON>", "cognitivefunctions": "<PERSON><PERSON><PERSON><PERSON>", "psychology": "<PERSON><PERSON><PERSON><PERSON>", "philosophy": "nkà<PERSON><PERSON><PERSON>", "history": "akụk<PERSON><PERSON>e", "physics": "physics", "science": "<PERSON><PERSON><PERSON>", "culture": "omenala", "languages": "<PERSON><PERSON><PERSON><PERSON>", "technology": "teknọlọjị", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "memeastrology", "enneagrammemes": "enneagrammemes", "showerthoughts": "echichemgbammiri", "funny": "<PERSON><PERSON><PERSON>", "videos": "vidiyo", "gadgets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "politics": "nd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "relationshipadvice": "nd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "n<PERSON><PERSON><PERSON>", "crypto": "crypto", "news": "akụkọ", "worldnews": "akụkọ<PERSON>", "archaeology": "akụkọiheochie", "learning": "mmụta", "debates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "e<PERSON><PERSON><PERSON><PERSON><PERSON>", "universe": "eluigwe", "meditation": "ntụgharịuche", "mythology": "akụkọala", "art": "nka", "crafts": "nka_aka", "dance": "nkwankwọ", "design": "imewe", "makeup": "etemeete", "beauty": "mma", "fashion": "<PERSON><PERSON><PERSON>", "singing": "<PERSON><PERSON><PERSON><PERSON>", "writing": "ideedemakwụkwọ", "photography": "foto", "cosplay": "cosplay", "painting": "esei<PERSON>", "drawing": "eserese", "books": "akwụkwọ", "movies": "i<PERSON>onyonyo", "poetry": "uri", "television": "telivishọ<PERSON>", "filmmaking": "<PERSON><PERSON><PERSON><PERSON>", "animation": "i<PERSON>onyonyo", "anime": "anime", "scifi": "<PERSON><PERSON><PERSON><PERSON>", "fantasy": "echichendị", "documentaries": "akwụkwọnkọwa", "mystery": "n<PERSON><PERSON>", "comedy": "<PERSON><PERSON><PERSON><PERSON>", "crime": "mpụ", "drama": "nkpọtụ", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "egwu", "romance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realitytv": "iheonyonyetiivi", "action": "<PERSON><PERSON><PERSON>", "music": "egwu", "blues": "blues", "classical": "klassik", "country": "obodo", "desi": "desi", "edm": "edm", "electronic": "elektronik", "folk": "ndiobodo", "funk": "f<PERSON><PERSON><PERSON>", "hiphop": "hiphop", "house": "ụlọ", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "íg<PERSON><PERSON><PERSON><PERSON>", "pop": "pop", "punk": "<PERSON><PERSON><PERSON>", "rnb": "rnb", "rap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reggae": "reggae", "rock": "nku<PERSON>", "techno": "teknọ", "travel": "njem", "concerts": "egwu", "festivals": "ememme", "museums": "ibeakụkọ", "standup": "<PERSON><PERSON><PERSON>", "theater": "<PERSON><PERSON><PERSON><PERSON>", "outdoors": "nèzí", "gardening": "<PERSON><PERSON>", "partying": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgames": "egwu<PERSON>g<PERSON><PERSON>", "dungeonsanddragons": "dungeonsnadragons", "chess": "chess", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "nri", "baking": "<PERSON><PERSON><PERSON>", "cooking": "nri", "vegetarian": "onye_anagh<PERSON>_eri_anụ", "vegan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cats": "nwamba", "dogs": "nkịta", "fish": "<PERSON><PERSON><PERSON>", "animals": "<PERSON><PERSON><PERSON>", "blacklivesmatter": "nd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "environmentalism": "gbasoi<PERSON><PERSON><PERSON><PERSON><PERSON>", "feminism": "nkwa<PERSON><PERSON><PERSON>ị", "humanrights": "<PERSON><PERSON><PERSON>", "lgbtqally": "nkwadolgbtq", "stopasianhate": "kpọsịnkpọasịanyaike", "transally": "nkwadondịtrans", "volunteering": "inyeakaafọifuefe", "sports": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "badminton": "badminton", "baseball": "baseball", "basketball": "basketball", "boxing": "ịkụọkpọ", "cricket": "kriketi", "cycling": "ịnyaígwèékwū", "fitness": "<PERSON><PERSON><PERSON>", "football": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "golf": "golf", "gym": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gymnastics": "<PERSON><PERSON><PERSON><PERSON>", "hockey": "egwuregwu_hockey", "martialarts": "<PERSON><PERSON><PERSON><PERSON>", "netball": "netball", "pilates": "pilates", "pingpong": "pingp<PERSON><PERSON>", "running": "ịgbaọsọ", "skateboarding": "ịgbasketi", "skiing": "<PERSON><PERSON><PERSON><PERSON>", "snowboarding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "surfing": "ịkwọ<PERSON><PERSON><PERSON><PERSON>", "swimming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tennis": "tenis", "volleyball": "volleyball", "weightlifting": "m<PERSON><PERSON><PERSON>", "yoga": "ihe_yoga", "scubadiving": "mmiri<PERSON><PERSON><PERSON><PERSON>", "hiking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "capricorn": "capricorn", "aquarius": "aquarius", "pisces": "pisces", "aries": "aries", "taurus": "taurus", "gemini": "gemini", "cancer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leo": "leo", "virgo": "virgo", "libra": "libra", "scorpio": "akpị", "sagittarius": "sagittarius", "shortterm": "obereoge", "casual": "nkịtị", "longtermrelationship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "single": "nwaaf<PERSON>naan<PERSON>", "polyamory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enm": "enm", "lgbt": "lgbtq", "lgbtq": "lgbtq", "gay": "nwokenasinasinanwoke", "lesbian": "nwan<PERSON><PERSON><PERSON><PERSON><PERSON>", "bisexual": "nwokeoronwanyi", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "ndi<PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "akukongara", "soulreaver": "onwemkpuruobi", "suikoden": "su<PERSON><PERSON>", "subverse": "<PERSON><PERSON><PERSON>", "legendofspyro": "akụkọnkespyro", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "spyroagwọ", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yokaiwatch": "yokaiwatch", "rocksteady": "kwụsieike", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openworld": "uwameghe", "heroesofthestorm": "dikenweta<PERSON>", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "ijerionuuno", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON>", "planescape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsoftherealm2": "ugwuanyanwuuwa2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "onye_naeri_agba", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "si<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "eg<PERSON><PERSON><PERSON>_asusu", "witcher": "witcher", "dishonored": "emeriela", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "mkpọda", "fallout3": "mgbapụ3", "fallout4": "mgbaputafallout4", "skyrim": "skyrim", "elderscrolls": "akwụkwọakụkọochie", "modding": "imerube", "charactercreation": "<PERSON><PERSON><PERSON>", "immersive": "ịbanye", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyochieoge", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "onyelovenkiti", "otomegames": "egwu<PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaofogeoge", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "akụkụ20", "gaslands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder": "onye_naegosi_uzo", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "ọsọ_ndò", "bloodontheclocktower": "<PERSON><PERSON>_ne<PERSON>_<PERSON><PERSON><PERSON>_elekere", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "mmeruike<PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "otungbọ", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "onyelikwu", "yourturntodie": "obu<PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpgegwuegwu", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "ndịnak<PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "mkpụ<PERSON><PERSON><PERSON><PERSON>_ndịmm<PERSON>", "mu": "mu", "falloutshelter": "ụlọmkpuchionwuatọm", "gurps": "gurps", "darkestdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "mgbanweojii", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "njikọtaisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skullgirls": "ụmụagb<PERSON><PERSON>", "nightcity": "obodo<PERSON>l<PERSON>", "hogwartslegacy": "akụkọọdịnalahogwarts", "madnesscombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "ụzọ96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "egwuregwundịkaibe", "gothamknights": "nd<PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "alaụwa_echefuru", "dragonlance": "dragonlance", "arenaofvalor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "obodokaatonị", "childoflight": "nwaìhè", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "oche_eze_gba<PERSON><PERSON>_agbaji", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "oasiskaogeonyenile", "hogwartmystery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltaakwụkwọ<PERSON>ụ", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "k<PERSON><PERSON>", "lastepoch": "ogeikpeazụ", "starfinder": "onye_nchọta_kpakpando", "goldensun": "<PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "divinityiz<PERSON>unk<PERSON><PERSON>", "bladesinthedark": "agụbanamk<PERSON>ụ<PERSON>ụ", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkuhie", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "chi", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "njemumataot<PERSON>u", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "egwueg<PERSON><PERSON><PERSON>", "roleplayinggames": "egwuegwuịkpọụta", "finalfantasy9": "finalfantasy9", "sunhaven": "anw<PERSON><PERSON>", "talesofsymphonia": "akụkọsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfarog": "myfarog", "sacredunderworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainedechoes": "echoesjikọtara", "darksoul": "obinakpuru", "soulslikes": "soulsdịka", "othercide": "ndịọz<PERSON>", "mountandblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "iheomumenaoge", "pillarsofeternity": "nkumebiebighebi", "palladiumrpg": "palladiumrpg", "rifts": "nkewa", "tibia": "ụkwụchụ<PERSON>ụ", "thedivision": "nkewa", "hellocharlotte": "ndewo<PERSON><PERSON><PERSON>", "legendofdragoon": "akukoebubedragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "obere_osisi", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "obimotọ", "fable3": "akụkọ3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "and<PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "kpakpandoigweigwe", "oldschoolrevival": "nkwaghaghachimgbochinịgbè", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "obinkeoma1", "ff9": "ff9", "kingdomheart2": "obishiobi2", "darknessdungeon": "<PERSON><PERSON>ị<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "egwuregwurpg", "kingdomhearts": "alae<PERSON>_obi", "kingdomheart3": "obinkingdom3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "<PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON>", "gloomhaven": "udo<PERSON><PERSON><PERSON>", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "ebe_nchekwa", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "eluigwearcadia", "shadowhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mother3": "nne3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "mgbapụbos", "anothereden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "egwuegwuịmearụ", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "obi<PERSON><PERSON><PERSON>", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON>", "spelljammer": "ọkwanjemba", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "ogod<PERSON><PERSON>", "cocttrpg": "cocttrpg", "huntroyale": "ịchụntagba", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowheartscovenant": "mgbaghaomumeshadowheart", "bladesoul": "mmagụbamkp<PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "alae<PERSON>_biara", "awplanet": "<PERSON><PERSON><PERSON><PERSON>", "theworldendswithyou": "ụwakwụ<PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "akwụkwọochieochie", "dyinglight2": "ọkụnaanwụ2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "okpukper<PERSON><PERSON><PERSON>", "shoptitans": "ahịaitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackbook": "akwụkwọojii", "skychildrenoflight": "ụ<PERSON>ụ<PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "castlecrashers": "ndinaakụ<PERSON><PERSON>", "gothicgame": "egwu<PERSON><PERSON>wu<PERSON>ị<PERSON>ị<PERSON>ị", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "mm<PERSON><PERSON><PERSON><PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "egwuegwurpg", "prophunt": "n<PERSON><PERSON><PERSON><PERSON>", "starrails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofmist": "obo<PERSON><PERSON><PERSON><PERSON>", "indierpg": "egwuegwuon<PERSON>", "pointandclick": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisawaytoo": "<PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "enweghiikenkewa", "freeside": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "ụzọọnwụga<PERSON>da", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "ọchụntagburu", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona5": "persona5", "ghostoftsushima": "mmụọ<PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "nch<PERSON><PERSON>_n<PERSON><PERSON><PERSON>_an<PERSON>_ọhịa_ebili", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "onye_naeri_mkp<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "etrianodyssey": "etrianodyssey", "nonarygames": "egwu<PERSON>gwuabughịnwokemaọbụnwaanyị", "tacticalrpg": "rpgnzuzoatumatu", "mahoyo": "mahoyo", "animegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "onye_naeri_chi", "diluc": "diluc", "venti": "venti", "eternalsonata": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "princessconnect": "njikọn<PERSON><PERSON><PERSON>", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "onye_amamihe_akpa", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindia", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "egwuregwueletroniki", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "egwuregwudigital", "mlg": "mlg", "leagueofdreamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "ebolubọọlụ", "dreamhack": "nrọọs<PERSON>", "gaimin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overwatchleague": "overwatchleague", "cybersport": "egwuregwukọmputa", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "egwuregwuriot", "eracing": "mechipu", "brasilgameshow": "egwuegwubrazil", "valorantcompetitive": "asọmp<PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "ndụọkara", "left4dead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead2": "left4dead2", "valve": "valvu", "portal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "teamfortress2": "teamfortress2", "everlastingsummer": "nwezugbeenweghinkpomoku", "goatsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "<PERSON><PERSON><PERSON>", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "aghaanọ4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "gbukotakwucha", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanias", "overcooked": "gwa<PERSON><PERSON><PERSON>", "interplanetary": "netitiplanetịdị", "helltaker": "onye_naeweta_mmụ<PERSON>_ọj<PERSON>", "inscryption": "inscryption", "7d2d": "7ụbọchị2ọnwụ", "deadcells": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "ọgba_n<PERSON><PERSON>", "stray": "gbafuo", "battlefield": "a<PERSON><PERSON><PERSON>", "battlefield1": "aghaụwa1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eyeb": "anya", "blackdesert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partyhard": "obiagbago", "hardspaceshipbreaker": "ngwangwansitereikewap<PERSON>", "hades": "hades", "gunsmith": "onye_ọr<PERSON>_egbe", "okami": "<PERSON>ami", "trappedwithjester": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinkum": "eziokwu", "predecessor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rainworld": "<PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "colonysim", "noita": "noita", "dawnofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minionmasters": "minionmasters", "grimdawn": "ụtụ<PERSON><PERSON>", "darkanddarker": "gbaaịch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "onye_ọr<PERSON>_mkp<PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "egwu<PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "mgbapụcube", "hifirush": "nkwadorush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON>", "defconheavy": "<PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsia", "virtualkenopsia": "<PERSON>zugbeọ<PERSON>", "snowrunner": "onye_naagba_na_snow", "libraryofruina": "ọbaakwụkwọnkeọgbaagha", "l4d2": "l4d2", "thenonarygames": "egwuregwund<PERSON><PERSON>maọbụla", "omegastrikers": "omegastrikers", "wayfinder": "onye_nch<PERSON><PERSON>_<PERSON>", "kenabridgeofspirits": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placidplasticduck": "u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ịkdịnwayọọ", "battlebit": "<PERSON><PERSON><PERSON><PERSON>", "ultimatechickenhorse": "ọkụkọị<PERSON>", "dialtown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "nwaoke<PERSON>ek<PERSON>oke", "tinnybunny": "obereoke", "cozygrove": "obia<PERSON><PERSON>", "doom": "mbibi", "callofduty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyww2": "callofdutyww2", "rainbow6": "egwuregwu6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "okeala", "pubg": "pubg", "callofdutyzombies": "call<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "<PERSON><PERSON><PERSON>", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "egwuregwufarcry", "paladins": "paladins", "earthdefenseforce": "ndị<PERSON><PERSON>", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto5": "grandtheftauto5", "warz": "agha", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "joinsquad": "<PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "onye_egbu_egbu3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "ọnwụnakwụ<PERSON>ị", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "ọkpụkpọọgụwarzone", "codzombies": "codzombies", "mirrorsedge": "oninwaugogbe", "divisions2": "nkewa2", "killzone": "ogbugbundiammuo", "helghan": "hel<PERSON>", "coldwarzombies": "aghaoyizombie", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "onye_egbe_kachas<PERSON>_mma", "modernwarfare": "<PERSON><PERSON><PERSON><PERSON>", "neonabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planetside2": "planetside2", "mechwarrior": "<PERSON><PERSON><PERSON><PERSON>", "boarderlands": "oke_ala", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "uwadcs", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "a<PERSON><PERSON><PERSON><PERSON>", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON>", "back4blood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warframe": "warframe", "rainbow6siege": "eg<PERSON>aghaanọ6siege", "xcom": "xcom", "hitman": "onye_ogbugbu", "masseffect": "masseffect", "systemshock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killingfloor2": "egbuegbuala2", "cavestory": "ọgbad<PERSON>", "doometernal": "ọnw<PERSON><PERSON><PERSON><PERSON>", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "nkewa2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "aghaohuurunwa2", "blackops1": "blackops1", "sausageman": "nwokesose<PERSON>", "ratchetandclank": "ratchetnaclank", "chexquest": "chexquest", "thephantompain": "mgbunwuimo", "warface": "<PERSON><PERSON><PERSON><PERSON>", "crossfire": "mgbag<PERSON><PERSON>", "atomicheart": "obin<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "ndị<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "okuegwuonwucallofdutybattleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "nnwere", "battlegrounds": "<PERSON><PERSON><PERSON>a", "frag": "gbuchaa", "tinytina": "obereb<PERSON><PERSON>", "gamepubg": "egwuregwupubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "egwuregwufps", "convertstrike": "mgbanwenweta", "warzone2": "aghaobodo2", "shatterline": "mgbawamgbawa", "blackopszombies": "blackopszombies", "bloodymess": "ọbarag<PERSON><PERSON>", "republiccommando": "onyeagharepublic", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "ngalụ<PERSON><PERSON>ala<PERSON>", "squad": "ndiotu", "destiny1": "nkebiara1", "gamingfps": "egwuregwufps", "redfall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubggirl": "nwaanyugbọpubg", "worldoftanksblitz": "uwa<PERSON><PERSON>litz", "callofdutyblackops": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enlisted": "etinyereaka", "farlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "oberetinasnalauluebube", "halo2": "halo2", "payday2": "ụb<PERSON>chịụgwọọrụ2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "ndiukwupubg", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "nchakod", "ghostcod": "<PERSON>ụọ<PERSON><PERSON><PERSON>gwu", "csplay": "csplay", "unrealtournament": "egwuregwundigbo", "callofdutydmz": "akpọokudmz", "gamingcodm": "egwuregwucodm", "borderlands2": "borderlands2", "counterstrike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cs2": "cs2", "pistolwhip": "egbekwe", "callofdutymw2": "ọkụọgụmw2", "quakechampions": "egwuregwuọkachamara", "halo3": "halo3", "halo": "halo", "killingfloor": "alaogbugbu", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonọcha", "remnant": "nkụ", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "nlọghachi", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "nwokeonwunyo", "quake2": "egwuala2", "microvolts": "microvolts", "reddead": "<PERSON><PERSON>_nw<PERSON><PERSON>", "standoff2": "nkwuọtọ2", "harekat": "harekat", "battlefield3": "aghaegbe3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "okeosi<PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "nchara", "conqueronline": "merie<PERSON><PERSON>", "dauntless": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "ubo<PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flightrising": "ịgbalite", "recroom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofruneterra": "akukoiforuneterra", "pso2": "pso2", "myster": "<PERSON><PERSON><PERSON><PERSON>", "phantasystaronline2": "phantasystaronline2", "maidenless": "<PERSON>we<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "uwatank", "crossout": "kpo<PERSON>o", "agario": "agario", "secondlife": "nd<PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "eg<PERSON><PERSON><PERSON><PERSON><PERSON>", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "njikọtaisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "ndiojoo", "newworld": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pirate101": "pirate101", "honorofkings": "<PERSON>sọ<PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "obodonyinya", "3dchat": "3dnkata", "nostale": "a<PERSON><PERSON><PERSON><PERSON>ghị", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "ntụchaokike", "riotmmo": "riotmmo", "silkroad": "ụzọsilk", "spiralknights": "spiralknights", "mulegend": "muonye_ukwu", "startrekonline": "startreknaintanetị", "vindictus": "vindictus", "albiononline": "albinonime", "bladeandsoul": "mpianamk<PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "am<PERSON><PERSON><PERSON><PERSON>_dragọn", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "egwu<PERSON>g<PERSON><PERSON>", "angelsonline": "ndịmmụọọz<PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "<PERSON><PERSON><PERSON>ụ", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "<PERSON><PERSON><PERSON><PERSON>", "blueprotocol": "blueprotocol", "perfectworld": "u<PERSON><PERSON><PERSON><PERSON>", "riseonline": "kulitena_intaneti", "corepunk": "corepunk", "adventurequestworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "animaljam", "kingdomofloathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "obododiribendume", "mortalkombat": "<PERSON><PERSON><PERSON>", "streetfighter": "<PERSON><PERSON><PERSON>ụ<PERSON>", "hollowknight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalgearsolid": "metalgearsolid", "forhonor": "ma<PERSON><PERSON><PERSON><PERSON>e", "tekken": "tekken", "guiltygear": "ikpeochiomume", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "onyelusoọgụokporo6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "njikọtaeg<PERSON>", "nomoreheroes": "<PERSON>g<PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "ezenkungfu", "likeadragon": "dịkadragon", "retrofightinggames": "eg<PERSON><PERSON><PERSON>wu<PERSON>", "blasphemous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rivalsofaether": "ndiiroaether", "persona4arena": "persona4arena", "marvelvscapcom": "marvelmegidecapcom", "supersmash": "<PERSON><PERSON><PERSON><PERSON>", "mugen": "mugen", "warofthemonsters": "aguugwuannuohia", "jogosdeluta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "robotụkọmputa", "armoredwarriors": "ndị<PERSON><PERSON><PERSON>", "finalfight": "mkpọtụik<PERSON>", "poweredgear": "ngwangwankike", "beatemup": "kụọya", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "mmụọọbị<PERSON>igbu", "kingoffigthers": "ezefighters", "ghostrunner": "onye_ọsọ_mmụọ", "chivalry2": "chivalry2", "demonssouls": "mkp<PERSON><PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "egwuregwuikpeiferemụ", "hollowknightsequel": "egwuregwuaghaisequel", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "udosilksong", "silksonggame": "eg<PERSON><PERSON><PERSON>son<PERSON>", "silksongnews": "akụkọsilksong", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evolutiontournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evomoment": "ogemoment", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "akụkọberseria", "bloodborne": "ọbara_eburu", "horizon": "elu", "pathofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "<PERSON><PERSON><PERSON><PERSON>", "uncharted": "am<PERSON><PERSON>ị", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "<PERSON><PERSON><PERSON><PERSON>", "infamous": "<PERSON>aama", "playstationbuddies": "ndịenyibooplaystation", "ps1": "ps1", "oddworld": "ụwanke<PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "mgbawankewa", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "ndịòtùkampanị", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "chinekeagha", "gris": "gris", "trove": "nchekwa", "detroitbecomehuman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatsaber": "egwu<PERSON><PERSON><PERSON>", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON>", "touristtrophy": "nzuutaọbịaafrica", "lspdfr": "lspdfr", "shadowofthecolossus": "onyinyoch<PERSON>", "crashteamracing": "egwuregwuị<PERSON>ọ", "fivepd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken7": "tekken7", "devilmaycry": "ekwensuenwereikeibekwa", "devilmaycry3": "devilmaycry3", "devilmaycry5": "ekwensuenwereikwaakwa5", "ufc4": "ufc4", "playingstation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraiwarriors": "ndịdikeasa<PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "mkp<PERSON><PERSON><PERSON><PERSON><PERSON>_agha", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "nchọpụtanwoke", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "mkpụr<PERSON><PERSON>ishadow2ọgbụ<PERSON>", "pcsx2": "pcsx2", "lastguardian": "onyencheikpeazụ", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "egwuregwungafe", "armello": "armello", "partyanimal": "onye_oriri_na_nkwu", "warharmmer40k": "warharmmer40k", "fightnightchampion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "ndịnaeleg<PERSON>", "mhw": "mhw", "princeofpersia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "ihuagha", "dontstarvetogether": "kaanyị<PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "kpakpandoigwe", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "onye_naagbanwe_<PERSON>lọ", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ọgbakọndieze", "fable2": "akụkọ2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skycotl": "skycotl", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sallyface": "ihusally", "franbow": "franbow", "monsterprom": "nnukwuegwuregwuị<PERSON>", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "ụgbọala", "outerwilds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON>", "duckgame": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "akukoukostanley", "towerunite": "towerjikọta", "occulto": "zoro", "longdrive": "<PERSON><PERSON><PERSON>", "satisfactory": "odimma", "pluviophile": "onyenweremmiri", "underearth": "okpuruala", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "onyeedummu<PERSON>", "darkdome": "ọchịchịr<PERSON>e", "pizzatower": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indiegame": "egwuegwundi", "itchio": "itchio", "golfit": "g<PERSON><PERSON><PERSON><PERSON>", "truthordare": "e<PERSON>kwumaebub<PERSON>", "game": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rockpaperscissors": "nkụmenk<PERSON>", "trampoline": "trampolin", "hulahoop": "mgbanakankpa", "dare": "n<PERSON><PERSON><PERSON>", "scavengerhunt": "nchọtaegwuregwu", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "<PERSON>zieokwuma<PERSON>ị", "beerpong": "bọọlụbia", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON>", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "egwu<PERSON><PERSON><PERSON><PERSON>", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "egwurinmmanya", "sodoku": "sodoku", "juegos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "egwuregwuñomiegwu", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "jeuxdemots", "juegosdepalabras": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "kaanyịkpọọegwuregwu", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interactivegames": "egwuregwumme<PERSON>", "amtgard": "amtgard", "staringcontests": "asọ<PERSON><PERSON><PERSON><PERSON>", "spiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochi": "giochi", "geoguessr": "geoguessr", "iphonegames": "egwuregwuiphone", "boogames": "e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hideandseek": "zoonae<PERSON>", "hopscotch": "ụkwụ_ngwụ", "arcadegames": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "egwuregwuyakuza", "classicgame": "egwuregwuochie", "mindgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON>", "galagames": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4xgames": "egwuregwu4x", "gamefi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdarcades": "egwuegwuarcade", "tabletopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "egwuregwu90", "idareyou": "gbamagbama", "mozaa": "mozaa", "fumitouedagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "egwuregwu<PERSON>s<PERSON>", "ets2": "ets2", "realvsfake": "ezigbokaghaezigbo", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "egwuegwu<PERSON>netị", "onlinegames": "egwuegwu<PERSON>netị", "jogosonline": "egwuregwu<PERSON>ị", "writtenroleplay": "edem<PERSON>_eg<PERSON><PERSON><PERSON>wu", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "<PERSON>gwueg<PERSON>mme<PERSON>", "jenga": "jenga", "wiigames": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "highscore": "akara_elu", "jeuxderôles": "egwuregwuak<PERSON>ọ", "burgergames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsgames": "egwu<PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmweditionojii", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "gioco", "managementgame": "egwuregwunlekota", "hiddenobjectgame": "egwuregwuịch<PERSON>taiheezoro", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "egwuregwuformula1", "citybuilder": "onye_naewu_obodo", "drdriving": "ụgbọalaụgb<PERSON>", "juegosarcade": "egwuregwuarcade", "memorygames": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "egwu<PERSON>gwuime", "blowgames": "e<PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "igwepinball", "oldgames": "egwuregwuochie", "couchcoop": "nnọkọ<PERSON><PERSON><PERSON>", "perguntados": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lasergame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "egwuregwuimessage", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxpc": "egwuegwukọmputa", "rétrogaming": "egwuegwuvidiokoge", "logicgames": "egwu<PERSON>gwuak<PERSON><PERSON>", "japangame": "egwu<PERSON>gwu<PERSON>pan", "rizzupgame": "buli<PERSON><PERSON><PERSON><PERSON>", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "e<PERSON><PERSON><PERSON><PERSON>", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5na5", "rolgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashiegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameandkill": "egwuriegwumagbuo", "traditionalgames": "egwuriọ<PERSON>", "kniffel": "kniffel", "gamefps": "egwuegwufps", "textbasedgames": "egwueg<PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "nkọwandị", "fantacalcio": "fantacalcio", "retrospel": "retrospelụ", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "egwu<PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "<PERSON><PERSON><PERSON><PERSON>", "tischfußball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "eg<PERSON><PERSON>g<PERSON>nd<PERSON>", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "eg<PERSON><PERSON><PERSON>wu<PERSON>gbapụ", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "eg<PERSON><PERSON>gwu<PERSON><PERSON>", "játék": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bordfodbold": "bordfodbola", "jogosorte": "jogosorte", "mage": "onye_mgbaasị", "cargames": "eg<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "egwuegwu<PERSON>netị", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "randomizer": "randomizer", "msx": "msx", "anagrammi": "mgbag<PERSON>", "gamespc": "egwuregwukọmputa", "socialdeductiongames": "egwu<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "dominos": "dominos", "domino": "domino", "isometricgames": "egwuregwuisometric", "goodoldgames": "egwu<PERSON>gwuochiemgbede", "truthanddare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "ị<PERSON>ọn<PERSON><PERSON>", "jeuxvirtuel": "egwuegwukọmputa", "romhack": "romhack", "f2pgamer": "onye_egwureg<PERSON>_nefu", "free2play": "eg<PERSON><PERSON><PERSON>_nefu", "fantasygame": "egwuregwuechiche", "gryonline": "gr<PERSON><PERSON><PERSON><PERSON>", "driftgame": "egwuregwu<PERSON>ụ", "gamesotomes": "egwu<PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotvseriesnaegwuregwu", "mushroomoasis": "oasismushroom", "anythingwithanengine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "mm<PERSON>ghanamgbaasaa", "goodgamegiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "<PERSON><PERSON><PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "egwuregwulabzero", "grykomputerowe": "egwuregwukọmputa", "virgogami": "virgogami", "gogame": "kaanie", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "egwuregwu<PERSON>re", "ridgeracertype4": "ụdịridgeracer4", "selflovegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "eg<PERSON><PERSON><PERSON><PERSON><PERSON>", "spelletjes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spacenerf": "<PERSON><PERSON><PERSON><PERSON>", "charades": "iheegwuikuku", "singleplayer": "<PERSON><PERSON><PERSON><PERSON>", "coopgame": "<PERSON>gwueg<PERSON>mme<PERSON>", "gamed": "ejerechaa", "forzahorizon": "forzahorizon", "nexus": "njikọ", "geforcenow": "geforcenow", "maingame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "ezediscord", "scrabble": "skrabu", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "nchọ", "onitama": "onitama", "pandemiclegacy": "iheditaoriaobodo", "camelup": "ịny<PERSON>lu", "monopolygame": "egwurimonopoly", "brettspiele": "egwuregwuboard", "bordspellen": "eg<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "boardgame": "egwuregwuboardị", "sällskapspel": "eg<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "planszowe": "egwuregwu_tebulu", "risiko": "risiko", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombicide", "tabletop": "<PERSON><PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "ọgbaragb<PERSON>", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "gaak<PERSON><PERSON><PERSON>", "connectfour": "njikọanọ", "heroquest": "ọchịchọdi<PERSON>", "giochidatavolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "<PERSON><PERSON><PERSON><PERSON>", "tablegames": "egwuregwuokpokoro", "dicegames": "e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchisi", "jogodetabuleiro": "egwuregwuebee", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "n<PERSON><PERSON>lumigwe", "creationludique": "ike<PERSON>ke", "tabletoproleplay": "egwuakụkọnteebulu", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "egwụ<PERSON><PERSON><PERSON>", "switchboardgames": "egwuregwuswitchboard", "infinitythegame": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "ọnwụalaeze", "yahtzee": "yahtzee", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "egwure<PERSON><PERSON>_teb<PERSON>l", "rednecklife": "nd<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardom": "ike_gw<PERSON><PERSON>", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "egwu<PERSON>g<PERSON><PERSON>", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "egwuegwuebube", "twilightimperium": "alae<PERSON>_anyasị", "horseopoly": "inyinyaopoly", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "onyinyochitebrimstone", "kingoftokyo": "ezenkotokyo", "warcaby": "warcaby", "táblajátékok": "egwuregwu_tebulu", "battleship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tickettoride": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "egwu<PERSON>gwuebod", "stolníhry": "egwure<PERSON><PERSON>_teb<PERSON>l", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "egwuregwuebube", "gesellschaftsspiele": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "starwarslegion", "gochess": "gaachess", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "agh<PERSON><PERSON><PERSON>", "arksurvivalevolved": "arksurvivalevolved", "dayz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "identityv": "identityv", "theisle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thelastofus": "nkeonwuikpeazu", "nomanssky": "eluigwe<PERSON>un<PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tomburaida", "callofcthulhu": "ọkụkpọcthulhu", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "netibeanyị", "eco": "<PERSON><PERSON><PERSON>", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "daysgone": "<PERSON><PERSON><PERSON><PERSON>", "fobia": "<PERSON><PERSON><PERSON>", "witchit": "akọọya", "pathologic": "<PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7<PERSON><PERSON><PERSON><PERSON>ịnkwụ<PERSON>ị", "thelongdark": "<PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "kwụ<PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "ọnọdụire2", "vrising": "vrising", "madfather": "<PERSON><PERSON><PERSON><PERSON>", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "<PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "ụzọndịdike", "frictionalgames": "egwu<PERSON>g<PERSON>ng<PERSON><PERSON><PERSON>", "hexen": "amusu", "theevilwithin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "ezigborac", "thebackrooms": "<PERSON><PERSON>ọaz<PERSON>", "backrooms": "<PERSON><PERSON>ọaz<PERSON>", "empiressmp": "empiressmp", "blockstory": "akụkọngọngọ", "thequarry": "ebe_a_na<PERSON><PERSON><PERSON><PERSON><PERSON>_nkume", "tlou": "tlou", "dyinglight": "ọkụna<PERSON><PERSON>ụ", "thewalkingdeadgame": "egwuregwuonwụndịejenaije", "wehappyfew": "anyịolenaole", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagestory": "akụkọochie", "arksurvival": "ndụnọgideark", "barotrauma": "barotrauma", "breathedge": "iku<PERSON>", "alisa": "alisa", "westlendsurvival": "njikọtaakụkụọdịdaanwụ", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "oyi_mg<PERSON>ha", "darkwood": "osisiochicha", "survivalhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "ụgbọoloko_efu", "lifeaftergame": "ndụkamegachaae<PERSON>wu<PERSON>wu", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "oruakwukwondu", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON>", "raft": "ụgbọ_mmiri", "rdo": "rdo", "greenhell": "ọhịaakwụkwọndụmgbagwojuanya", "residentevil5": "residentevil5", "deadpoly": "polyịnw<PERSON><PERSON><PERSON>", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "nne_ochie", "littlenightmares2": "obereanyaunyi2", "signalis": "mgbaàmà", "amandatheadventurer": "amanda<PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "ụmụnwokeọhịa", "rustvideogame": "egwuegwuvidionduru", "outlasttrials": "nn<PERSON><PERSON><PERSON>", "alienisolation": "mmekpuruobianbiara", "undawn": "<PERSON><PERSON><PERSON><PERSON>", "7day2die": "7ụbọchịka2nwụọ", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "sopravvivenza", "propnight": "a<PERSON><PERSON><PERSON><PERSON>", "deadisland2": "agbụ<PERSON><PERSON><PERSON>wụ2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "ọnwụverse", "cataclysmdarkdays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "onye_nledo_onyinyo_chernobyl", "lifeafter": "ndụmgbeemel<PERSON>", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "uloelekere3", "aloneinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "egwuregwuprojectnimbus", "eternights": "<PERSON><PERSON><PERSON>", "craftopia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theoutlasttrials": "nnwaleikpeasị", "bunker": "ebe_n<PERSON><PERSON>", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "r<PERSON><PERSON>", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "ogbundwerfu", "warhammer40kcrush": "warhammer40kahuhuanya", "wh40": "wh40", "warhammer40klove": "ihụnanyawarhammer40k", "warhammer40klore": "akụkọwarhammer40k", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iloveassasinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templovenenum": "ịhụnanyaogengaefe", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "<PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "nkesanku", "terraformingmars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofmightandmagic": "ndiikembanagbanchaasatọ", "btd6": "btd6", "supremecommander": "onye<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofmythology": "afọnduag<PERSON><PERSON><PERSON>", "args": "args", "rime": "rime", "planetzoo": "<PERSON><PERSON><PERSON><PERSON>", "outpost2": "ụlọnche2", "banished": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "ọkụgbara", "civilization6": "obodo6", "warcraft2": "warcraft2", "commandandconquer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strategygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "eg<PERSON><PERSON>gwu<PERSON><PERSON><PERSON><PERSON>", "civilization4": "mmepe4", "factorio": "factorio", "dungeondraft": "ihedungeni", "spore": "spore", "totalwar": "a<PERSON><PERSON><PERSON>", "travian": "travian", "forts": "ụlọakụkọ", "goodcompany": "ezigbonkemkọ<PERSON>ị<PERSON>", "civ": "civ", "homeworld": "<PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "ngwangwakariaihe", "forthekings": "makaezenwoke", "realtimestrategy": "strategynoge", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eu4": "eu4", "vainglory": "mpako", "ww40k": "ww40k", "godhood": "chi", "anno": "anno", "battletech": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "okpuatọ", "davesfunalgebraclass": "klaasịalgebrandave", "plagueinc": "oriaojo<PERSON>", "theorycraft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "mmepeanya3", "4inarow": "4nus<PERSON>", "crusaderkings3": "crusaderkings3", "heroes3": "dike3", "advancewars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "ọgbaakaekpere2", "disciples2": "ndịnaesobe2", "plantsvszombies": "osis<PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stratejioyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "afoojuju", "dinosaurking": "ezedino", "worldconquest": "mmerinjikwauwa", "heartsofiron4": "obi_igwe4", "companyofheroes": "ndịdikengozigbokọta", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "og<PERSON>gh<PERSON><PERSON><PERSON>", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "ọgazọgazọbọbọ", "phobies": "egwu", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "uzuofufe", "turnbased": "ogaa<PERSON><PERSON>u", "bomberman": "bomberman", "ageofempires4": "ọgbaakaekperemiri4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "atụmatụ", "popfulmail": "<PERSON><PERSON><PERSON><PERSON>", "shiningforce": "<PERSON><PERSON>", "masterduel": "eg<PERSON><PERSON><PERSON>", "dysonsphereprogram": "mmemmedysonsphere", "transporttycoon": "ọgara<PERSON>", "unrailed": "enwe<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "akamakaọsọ<PERSON>ọ<PERSON>", "uplandkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galaxylife": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "eg<PERSON><PERSON>", "battlecats": "aghaon<PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simsiti", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "ịgbaọsọ", "granturismo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeed": "ọsọọsọdị<PERSON>", "needforspeedcarbon": "mkpafọụgbọalacarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "njemuka<PERSON>", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "akụkọims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON>", "deadbydaylight": "nwụrụnwụsitenambalịabalị", "alicemadnessreturns": "alice<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "akwụkwọakụkọndịnaabụghịnd<PERSON>amaamabụ<PERSON><PERSON>", "phasmophobia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "abalịisenatfreddys", "saiko": "saiko", "fatalframe": "onyonkakwuru", "littlenightmares": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "ọnwụnabilie", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "nụlọ", "deadisland": "agwaetiti_ndi_nwuru", "litlemissfortune": "obereenyechioma", "projectzero": "prọje<PERSON><PERSON><PERSON>", "horory": "egwu", "jogosterror": "eg<PERSON><PERSON><PERSON>_egwu", "helloneighbor": "n<PERSON><PERSON><PERSON>", "helloneighbor2": "nnoọndiagbataobi2", "gamingdbd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thecatlady": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "egwuregwuegwu", "horrorgaming": "egwuregwuegwu", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "okpokoro", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "kaadị<PERSON>ny<PERSON>nya_ígwè", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "akukorunetera", "solitaire": "kaad<PERSON>_<PERSON>", "poker": "poka", "hearthstone": "nku<PERSON><PERSON>", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "igodommeputa", "cardtricks": "ka<PERSON><PERSON><PERSON>", "playingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "gin<PERSON><PERSON><PERSON>", "netrunner": "onye_naagba_net", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "egwu<PERSON><PERSON><PERSON><PERSON>", "sportscards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "ọgụkaadịvanguard", "duellinks": "<PERSON><PERSON><PERSON><PERSON>", "spades": "mpio", "warcry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "iguzogideonwe", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "egwuregwuyugioh", "darkmagician": "onyelag<PERSON><PERSON>", "blueeyeswhitedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "<PERSON><PERSON><PERSON><PERSON>", "briscas": "briscas", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "eg<PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON>ọagh<PERSON>", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "žolíky": "ka<PERSON><PERSON>", "facecard": "<PERSON><PERSON><PERSON><PERSON>", "cardfight": "<PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "ndị<PERSON><PERSON><PERSON><PERSON>", "magiccartas": "ak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skipbo": "mapụbo", "unstableunicorns": "ịnyịnyaunstable", "cyberse": "saịba", "classicarcadegames": "egwuegwuakadịmgbochịochie", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "on<PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "<PERSON>ụọegwuọnwụ", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "ebeegwuegwu", "rhythmgamer": "onye_egwu_rhythm", "stepmania": "stepmania", "highscorerythmgames": "egwuegwu<PERSON><PERSON>", "pkxd": "pkxd", "sidem": "nnem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "ekikemeeluigwe", "hypmic": "hypmic", "adanceoffireandice": "egwuọkụnaice", "auditiononline": "<PERSON><PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubing": "ịgbakube", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "mkpụ<PERSON><PERSON><PERSON><PERSON>regwumgbagwojuanya", "spotit": "<PERSON><PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "okwunkpuruecheiche", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "nkọchauche", "rubikscube": "<PERSON><PERSON><PERSON><PERSON>", "crossword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "motscroisés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "krzyżówki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nonogram": "nonogram", "bookworm": "onye_naagụ_akwụkwọ", "jigsawpuzzles": "egwuregwungwangwa", "indovinello": "nkọwaokwu", "riddle": "nkọcha", "riddles": "ilu", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "nime", "angrybirds": "nn<PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "gbap<PERSON><PERSON><PERSON>", "minesweeper": "mgbapụtaogbunigwe", "puzzleanddragons": "puzzlenadragonsgasị", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesport": "egwuregwumgbagwojuanya", "escaperoomgames": "egwu<PERSON>gwu<PERSON>ụ", "escapegame": "eg<PERSON><PERSON><PERSON>wu<PERSON>gbapụ", "3dpuzzle": "3dpuzzle", "homescapesgame": "egwu<PERSON>g<PERSON><PERSON>", "wordsearch": "ịchọọ<PERSON>wu", "enigmistica": "enigmistica", "kulaworld": "<PERSON><PERSON><PERSON>", "myst": "mistiri", "riddletales": "akukonjuju", "fishdom": "azụ<PERSON>", "theimpossiblequiz": "<PERSON><PERSON><PERSON><PERSON>", "candycrush": "candycrush", "littlebigplanet": "ober<PERSON>ụwaụkwụ", "match3puzzle": "egwuegwuatọatọ", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "okwukwurubiik", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>gwu", "puttputt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "qbert": "qbert", "riddleme": "kpọọ<PERSON><PERSON>", "tycoongames": "egwuregwuọgaranya", "cubosderubik": "cubosderubik", "cruciverba": "cruciver<PERSON>", "ciphers": "ndịnzuzo", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "nd<PERSON><PERSON><PERSON><PERSON><PERSON>", "guessing": "nburu", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "okwuomumien<PERSON><PERSON>", "syberia2": "syberia2", "puzzlehunt": "ịchọenigma", "puzzlehunts": "nchọtangram", "catcrime": "mpụnwamba", "quebracabeça": "mgbagwojuanya", "hlavolamy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_ọgb<PERSON>b<PERSON>", "poptropica": "poptropica", "thelastcampfire": "ọ<PERSON><PERSON>", "autodefinidos": "nkọwaonaweonyenweya", "picopark": "picopark", "wandersong": "nkwa<PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "egwuregwuọgazụanwegh<PERSON>", "cassetête": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "limbo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rubiks": "rubiks", "maze": "mgbagharị<PERSON>", "tinykin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "nkewa", "portalgame": "egwuregwuọ<PERSON>", "bilmece": "bilmece", "puzzelen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "a<PERSON><PERSON>_omimi", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "<PERSON><PERSON><PERSON><PERSON>_gbagọr<PERSON>_a<PERSON>bag<PERSON>", "monopoly": "monopoly", "futurefight": "<PERSON><PERSON><PERSON><PERSON>", "mobilelegends": "egwuregwumkpanaka", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "ndubit", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "kpokpuikike", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "<PERSON>s<PERSON><PERSON><PERSON><PERSON>", "alchemystars": "kpakpandoọkụ", "stateofsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "obodommụ", "arknights": "arknights", "colorfulstage": "agbanaegbuke", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfrontigbo", "knightrun": "ọsọdike", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "agbab<PERSON>ụ", "a3": "a3", "phonegames": "egwuriegwuekwentị", "kingschoice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guardiantales": "akukoandichebe", "petrolhead": "onyenweụgb<PERSON>", "tacticool": "tacticool", "cookierun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "amụghịihenademe", "craftsman": "ọkaome<PERSON>", "supersus": "naịmkpọtụaka", "slowdrive": "ọgbag<PERSON>ụ", "headsup": "maraọkwa", "wordfeud": "<PERSON><PERSON><PERSON><PERSON>", "bedwars": "<PERSON><PERSON><PERSON>", "freefire": "freefire", "mobilegaming": "<PERSON>g<PERSON><PERSON><PERSON>tị", "lilysgarden": "ubililysgarden", "farmville2": "obifarmville2", "animalcrossing": "an<PERSON><PERSON>", "bgmi": "bgmi", "teamfighttactics": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clashofclans": "agh<PERSON><PERSON><PERSON>", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofduty<PERSON>eg<PERSON>", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "hqmber<PERSON>", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "nrọbangdream", "clashofclan": "agh<PERSON><PERSON><PERSON>", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "adaoge", "beatstar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "androidgames": "egwu<PERSON>gwu<PERSON><PERSON>", "criminalcase": "<PERSON><PERSON><PERSON><PERSON>", "summonerswar": "aghaakwụkwọndịota", "cookingmadness": "<PERSON>g<PERSON><PERSON>_isi_esi_nri", "dokkan": "dokkan", "aov": "aov", "triviacrack": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leagueofangels": "njik<PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "obereugbennụ", "gachalife": "ndụgacha", "neuralcloud": "neuralcloud", "mysingingmonsters": "my<PERSON>ingmons<PERSON>", "nekoatsume": "nwa<PERSON><PERSON><PERSON><PERSON>", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "warrobots", "mirrorverse": "eluig<PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "antiyoy": "mgbochịyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "ịbanye", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "egonzuola", "punishinggrayraven": "ntachapunishinggrayraven", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "egwurisultans", "arenabreakout": "ọ<PERSON>ụ<PERSON><PERSON>nag<PERSON>", "wolfy": "wolfy", "runcitygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegodemovil": "<PERSON>g<PERSON><PERSON><PERSON>tị", "avakinlife": "nduavakin", "kogama": "kogama", "mimicry": "nṅomia", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "onyelorolakos<PERSON>", "grandchase": "grandchase", "bombmebrasil": "bombmenigeria", "ldoe": "ldoe", "legendonline": "a<PERSON><PERSON><PERSON><PERSON>", "otomegame": "egwu<PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "okuokwunaga", "shiningnikki": "nik<PERSON><PERSON><PERSON>", "carxdriftracing2": "ụgbọalanyadriftọsọ2", "pathtonowhere": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "ọgụumuru3", "limbuscompany": "ụlọọ<PERSON>", "demolitionderby3": "ogbugbunugbasaatọ", "wordswithfriends2": "okwunaenyi2", "soulknight": "onye_agha_obi", "purrfecttale": "<PERSON>kụ<PERSON>ọnwa<PERSON>ụ", "showbyrock": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ladypopular": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "olumobile", "harvesttown": "obodo<PERSON><PERSON><PERSON>", "perfectworldmobile": "u<PERSON><PERSON><PERSON><PERSON><PERSON>", "empiresandpuzzles": "alae<PERSON>_na_eg<PERSON>regwu_mgbagwoju_anya", "empirespuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "obalandiagwo", "garticphone": "garticphone", "battlegroundmobileind": "<PERSON>ghaek<PERSON><PERSON><PERSON><PERSON><PERSON>", "fanny": "ikpu", "littlenightmare": "oberech<PERSON>che<PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "onye_naagba_napịtị", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ik<PERSON>ama<PERSON>", "eversoul": "mkp<PERSON><PERSON><PERSON>", "gunbound": "gunbound", "gamingmlbb": "egwuregwumlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "<PERSON><PERSON><PERSON><PERSON>", "eveechoes": "eveechoes", "jogocelular": "egwuregwuekwentị", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "nneisinri", "cabalmobile": "cabalmobile", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsfrontline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jurassicworldalive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulseeker": "onwechaziukpuruobi", "gettingoverit": "ịgafeya", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "akomoon", "carxdriftracingonline": "ụgbọalaxdriftxịgbaọsọonline", "jogosmobile": "egwuregwuekwentị", "legendofneverland": "akụkọirineverland", "pubglite": "pubglite", "gamemobilelegends": "egwuegwumobilelegends", "timeraiders": "ndịnawakiriaoge", "gamingmobile": "egwuregwuekwentị", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnr", "quest": "nchọ", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdemesa", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "onwuegwuonuonyenwe<PERSON>", "2300ad": "2300ad", "larp": "larp", "romanceclub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "d20": "d20", "pokemongames": "e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "ndintutuegbugbereọ<PERSON>ụ", "porygon": "porygon", "pokemonunite": "pokemonunjik<PERSON>ta", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonodo", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON>_ogwu_aṅ<PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "poke<PERSON><PERSON><PERSON>", "teamystic": "otumystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "ọkụ<PERSON>ụ", "shinypokemon": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "akaigwe", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>", "heyyoupikachu": "heynwapikachum", "pokémonmaster": "onye_isi_pok<PERSON>mon", "pokémonsleep": "ụrapok<PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "onye_nch<PERSON>_ihe_naegbuke", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>wu", "jeudéchecs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "japanesechess": "chessjapan", "chinesechess": "chesschina", "chesscanada": "<PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "og<PERSON>", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "nkpọrọ<PERSON>l<PERSON>nadragon", "dungeonsanddragon": "dungeonsnadragọn", "dungeonmaster": "onyelekọtadungọn", "tiamat": "tiamat", "donjonsetdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oxventure": "oxventure", "darksun": "an<PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "akukoifovoxmachina", "doungenoanddragons": "imer<PERSON><PERSON><PERSON><PERSON>", "darkmoor": "darkmoor", "minecraftchampionship": "asọmpiminecraft", "minecrafthive": "egwuregwuminecraft", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "mgbakwunye", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftakpa", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmegharịrị", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "netitialanabụọ", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "obodominecraft", "pcgamer": "pcgamer", "jeuxvideo": "egwuregwuvidiyo", "gambit": "gambit", "gamers": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "levelup": "kwaliteego", "gamermobile": "egwuegwuekwentị", "gameover": "<PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "egwuregwukọmputa", "gamen": "gamen", "oyunoynamak": "igboigbo", "pcgames": "egwuegwukọmputa", "casualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>gwu", "pcmasterrace": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgame": "eg<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerboy": "nwa<PERSON>eg<PERSON>egwu", "vrgaming": "egwuegwuvr", "drdisrespect": "drdisrespect", "4kgaming": "egwuregwu4k", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "onye_egwuregwu_console", "boxi": "boxi", "pro": "pro", "epicgamers": "ndị<PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "egwuregwu<PERSON>ị", "semigamer": "onye_egwureg<PERSON>_nwaobere", "gamergirls": "ụm<PERSON><PERSON><PERSON><PERSON>", "gamermoms": "ndịnnegamer", "gamerguy": "nwokegemu", "gamewatcher": "onyenleriegwuregwu", "gameur": "onwe<PERSON><PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "ụm<PERSON><PERSON><PERSON><PERSON>", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "ndinaagbasiike", "mallugaming": "egwuegwumallu", "pawgers": "nd<PERSON><PERSON><PERSON><PERSON>", "quests": "<PERSON><PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "ekeregwu", "cozygaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamelpay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepc": "egwuregwukọmputa", "dsswitch": "mgbanweds", "competitivegaming": "egwu<PERSON>gwuasọmp<PERSON>", "minecraftnewjersey": "minecraftnewjersey", "faker": "onye_a<PERSON><PERSON><PERSON>", "pc4gamers": "pc4gamers", "gamingff": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "egwuegwundinaenwenmasinkaamumnwaanyinamnwoke", "gamepc": "egwuegwukọmputa", "girlsgamer": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "ụmụnwaanyịgamer", "gamesetup": "nhazịegwuregwu", "overpowered": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "socialgamer": "onye_egwu<PERSON><PERSON><PERSON>_soshal", "gamejam": "egwuregwungwa", "proplayer": "onye_eg<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "roleplayer": "onye_naeme_ihe_n<PERSON>ri", "myteam": "ndịotum", "republicofgamers": "republicndigamers", "aorus": "aorus", "cougargaming": "egwuregwucougar", "triplelegend": "onyelepuruanyaatọ", "gamerbuddies": "nd<PERSON><PERSON><PERSON><PERSON><PERSON>", "butuhcewekgamers": "mụnwany<PERSON><PERSON><PERSON>", "christiangamer": "onye_egwure<PERSON><PERSON>_kristi", "gamernerd": "onye_eg<PERSON><PERSON><PERSON><PERSON>_n<PERSON><PERSON>", "nerdgamer": "ọkammụtaegwuegwu", "afk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "andregamer": "andregamer", "casualgamer": "onye_egwuregwu_nwayọọ", "89squad": "89squad", "inicaramainnyagimana": "ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>yi<PERSON>", "insec": "<PERSON>we<PERSON><PERSON><PERSON>", "gemers": "nd<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunizlemek": "<PERSON><PERSON><PERSON>gwuregwu", "gamertag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lanparty": "egwuegwu<PERSON>netị", "videogamer": "onye_egwuregwu_vidiyo", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "onye_egwuregwu_playstation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "onye_eg<PERSON><PERSON><PERSON><PERSON>_ah<PERSON><PERSON>_dị_mma", "gtracing": "gtracing", "notebookgamer": "onye_egwuregwu_akwụkwọ", "protogen": "protogen", "womangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "onye_na<PERSON><PERSON><PERSON>_nri_<PERSON><PERSON>a", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "mgbapụefu", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "ndịnaadaada", "switch": "can<PERSON>a", "zelda": "zelda", "smashbros": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofzelda": "a<PERSON><PERSON><PERSON><PERSON>_<PERSON>ri_nke_zelda", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "mgbana<PERSON><PERSON>", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "onyelemariokartnkeji", "wii": "wii", "aceattorney": "<PERSON><PERSON><PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "<PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "ndue<PERSON><PERSON><PERSON>ne", "ahatintime": "<PERSON><PERSON><PERSON>og<PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "simu<PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogames": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "ọnwaowuwe", "mariobros": "marion<PERSON><PERSON><PERSON><PERSON><PERSON>", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON>hị<PERSON>", "myfriendpedro": "enyimpedro", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "egwuregwu51", "earthbound": "ụ<PERSON>", "tales": "akụkọ", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "<PERSON><PERSON>ọnkeikpe", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "atụmatụtriangle", "supermariomaker": "onyenaemepụtasupermario", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "3<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "onyelusoaghahyrule", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioandsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "marion<PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "ịgbawa", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "liigịnchịkọbandịegwuegwu", "urgot": "urgot", "zyra": "zyra", "redcanids": "nkị<PERSON><PERSON>ie", "vanillalol": "vanillalol", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "egwuregwulegend", "tốcchiến": "a<PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspen", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "egwu<PERSON><PERSON><PERSON><PERSON>", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "egwuregwufortnite", "gamingfortnite": "egwuegwufortnite", "fortnitebr": "fortnitebr", "retrovideogames": "egwuregwuvidiokoge", "scaryvideogames": "egwuregwuvidiondịnaatụegwu", "videogamemaker": "ndinaemegemu", "megamanzero": "megamanzero", "videogame": "egwuregwuvidiyo", "videosgame": "egwuregwuvidiyo", "professorlayton": "profe<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>gwu", "acnh": "acnh", "puffpals": "ndienyiafufu", "farmingsimulator": "egwu<PERSON>gwu<PERSON><PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "robloxigbo", "erlc": "erlc", "sanboxgames": "egwuregwuigba", "videogamelore": "akụkọegwuegwuvidiyo", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "okeohia", "dreamscape": "alaulorondi", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ọrọakwakwọ", "deadspace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "egwuregwuvidiyo", "theoldrepublic": "republicochere", "videospiele": "egwuregwuvidiyo", "touhouproject": "touhouproject", "dreamcast": "nrọọmaka", "adventuregames": "egwu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "iheomumengbaegwuregwu", "storyofseasons": "akụkọogeafọ", "retrogames": "egwuregwuochie", "retroarcade": "egwuregwuochie", "vintagecomputing": "kọmpụtaochie", "retrogaming": "egwuregwuochie", "vintagegaming": "egwuregwuvideochaa", "playdate": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "akwụghịoto2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "egwuruigwe", "zenlife": "ndụd<PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "gbagoro", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "egwuegwublockchain", "medievil": "mgbeoge<PERSON>", "consolegaming": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolen", "outrun": "<PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "egwuregwuegwu", "monstergirlquest": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiant": "n<PERSON><PERSON><PERSON><PERSON>", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "egwuregwuochie", "bethesda": "bethesda", "jackboxgames": "egwuegwujackbox", "interactivefiction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "ikpezunke2", "amantesamentes": "amantesamentes", "visualnovel": "akụkọnkiri", "visualnovels": "akụkọonyonyo", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON><PERSON>", "tcrghost": "tcrghost", "payday": "ụbọ<PERSON>ịak<PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "<PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "igbe_ájá", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "akụkọonyonyo", "thecrew2": "ndiotu2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "egwuregwuochie", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "mgbanweifufeakwukwo", "wiiu": "wiiu", "leveldesign": "ichepụtaọkwa", "starrail": "ụzọkpakpando", "keyblade": "keyblade", "aplaguetale": "ọrị<PERSON><PERSON>", "fnafsometimes": "fnafmgbe<PERSON><PERSON>", "novelasvisuales": "akụkọndịnaelegheanya", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "egwuregwuochie", "videojuejos": "egwuregwuvidiyo", "videogamedates": "egwuegwuvidiomkpakọ<PERSON>ị<PERSON>", "mycandylove": "ịhụnanyamtọọụtọ", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "el<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justcause3": "naanịmakana3", "hulkgames": "egwuregwuhulk", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "nloghachindigbo", "gamstergaming": "gamstergaming", "dayofthetantacle": "ụbọchịnkeọ<PERSON>ụ<PERSON>ụ", "maniacmansion": "ụlọnnukwuonyeliberibe", "crashracing": "ọsọụgb<PERSON>", "3dplatformers": "egwuregwuụkwụatọ3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "eg<PERSON><PERSON><PERSON>wu<PERSON>oge", "hellblade": "<PERSON><PERSON><PERSON><PERSON>", "storygames": "egwuakụ<PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "onye_na_ag<PERSON>h<PERSON>_<PERSON>da", "beyondtwosouls": "kar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "<PERSON><PERSON>eg<PERSON>regwu", "offmortisghost": "offmortisghost", "tinybunny": "obereoke", "retroarch": "retroarch", "powerup": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "<PERSON>wangwa", "fzero": "fzero", "gachagaming": "egwu<PERSON>gwugacha", "retroarcades": "retroarcades", "f123": "f123", "wasteland": "al<PERSON><PERSON><PERSON>", "powerwashsim": "powerwashsim", "coralisland": "agwaetitimma", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "ngwakọtabọ<PERSON>ụ", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "onye_mbara_igwe", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "igwemebiri", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "nkọ<PERSON>ehere", "simulator": "simuleta", "symulatory": "symulatory", "speedrunner": "onye_ọsọ_ngwa_ngwa", "epicx": "epicx", "superrobottaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "egwuregwuvidiyo", "gaiaonline": "gaiaonline", "korkuoyunu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "alaebubeonline", "skylander": "skylander", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "eg<PERSON><PERSON><PERSON><PERSON><PERSON>", "simrace": "ịgbaọs<PERSON>", "pvp": "pvp", "urbanchaos": "ọgbagharag<PERSON>", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "seum", "partyvideogames": "egwu<PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "onyelekọtaịlị", "spaceflightsimulator": "simulator<PERSON><PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "ihenketa_kain", "hackandslash": "tigbugbuonye", "foodandvideogames": "nri_na_egwure<PERSON><PERSON>_vidiyo", "oyunvideoları": "egwuregwuvidiyo", "thewolfamongus": "nkị<PERSON>", "truckingsimulator": "<PERSON>nyaụgb<PERSON>ị<PERSON>", "horizonworlds": "<PERSON><PERSON><PERSON><PERSON>", "handygame": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "akụkọọdịnalanavidiogemu", "oldschoolvideogames": "egwuregwuvidioochiebere", "racingsimulator": "egwuregwuọs<PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "nd<PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "e<PERSON><PERSON><PERSON>", "famitsu": "famitsu", "gatesofolympus": "ọnụụzọolympus", "monsterhunternow": "on<PERSON><PERSON><PERSON><PERSON>", "rebelstar": "kpakpandoonyennupuisi", "indievideogaming": "egwuegwuvidiondipụ<PERSON>", "indiegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogames": "egwuregwuvidiondependenti", "indievideogame": "egwuregwuvidiondipụr<PERSON>", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spider<PERSON><PERSON><PERSON><PERSON><PERSON>", "bufffortress": "bufffortress", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "<PERSON><PERSON><PERSON>", "futureclubgames": "egwuregwu<PERSON><PERSON><PERSON><PERSON>", "mugman": "<PERSON><PERSON><PERSON><PERSON>", "insomniacgames": "ndianaegbuchiegburungame", "supergiantgames": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "egwuregwuceleste", "aperturescience": "aperturescience", "backlog": "iheechebeputara", "gamebacklog": "eg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "egwuregwundicheberem", "personnagejeuxvidéos": "person<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "<PERSON><PERSON><PERSON><PERSON>", "beastlord": "onyelorianụ", "juegosretro": "egwuregwuochie", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservatoriodedopamin", "staxel": "staxel", "videogameost": "egwuegwuegwuvidiyo", "dragonsync": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>f<PERSON>v<PERSON><PERSON>", "arcanum": "<PERSON><PERSON><PERSON><PERSON>", "neoy2k": "neoy2k", "pcracing": "ọsọụgb<PERSON>ala", "berserk": "gbawara_agbawa", "baki": "bakị", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "mbido", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "n<PERSON><PERSON><PERSON><PERSON>", "animewithplot": "animenwerenwerestorị", "pesci": "pesci", "retroanime": "animechaa", "animes": "anime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "animenke80s", "90sanime": "anime90s", "darklord": "onyenwe<PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "okachiepogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000s", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "animecover", "thevisionofescaflowne": "<PERSON><PERSON><PERSON>ụnkeescaflow<PERSON>", "slayers": "ndịogbuogbu", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON>zụbanana", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "hanakokunkejirikamakpọchiụlọmposi", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireforce": "<PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "moriart<PERSON><PERSON><PERSON><PERSON>", "futurediary": "akwụkwọụbọchị<PERSON>", "fairytail": "akụkọifo", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "emerenanimeabyssugbo", "parasyte": "<PERSON><PERSON><PERSON><PERSON>", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "a<PERSON><PERSON>ụnk<PERSON>", "blmanga": "blmanga", "horrormanga": "akwụkwọnd<PERSON>wu", "romancemangas": "akwụkwọman<PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "am<PERSON><PERSON><PERSON><PERSON>", "shamanking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acertainmagicalindex": "akwụkwọ<PERSON><PERSON><PERSON>", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "ntụakaotu", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8njedimgbaghari", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "n<PERSON><PERSON><PERSON>ọ<PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosis<PERSON><PERSON><PERSON>", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "animeụwand<PERSON>", "sagaoftanyatheevil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "ọkpọakandịuguru", "mazinger": "mazinger", "blackbuttler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "ọnwat<PERSON><PERSON><PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "on<PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "omunyemamokunkunk<PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwoabụọ", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "akpụkpọụkwụsatọn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "gbara", "oldanime": "animeochiaa", "chainsawman": "nwokoigweosisi", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "animediegwu", "fruitsbasket": "nkataụmkp<PERSON><PERSON>", "devilmancrybaby": "nwokemmụọak<PERSON>", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangaefu", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON>ụ<PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "<PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "alaokwerepuruekwenwa", "monstermanga": "<PERSON><PERSON><PERSON><PERSON>", "yourlieinapril": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "buggytheclown": "buggytheclown", "bokunohero": "bukunoherọ", "seraphoftheend": "nje<PERSON><PERSON><PERSON><PERSON>ọzịng<PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "onye_mkp<PERSON><PERSON><PERSON>_<PERSON><PERSON>_o<PERSON><PERSON>i", "jojolion": "jojo<PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON>wueb<PERSON>", "bannafish": "<PERSON>zụbanana", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "dim", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "obi_pandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "kadkaptọsakura", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "rukugiebeebe", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "ọgenigbannasọsọ", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "njik<PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bluelock": "bluelọk", "goblinslayer": "onyenaegbugoblin", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "gbak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyfamily": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "airgear": "i<PERSON><PERSON><PERSON>", "magicalgirl": "nway<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "mmehieụmụanwụ<PERSON>", "prisonschool": "ụlọmkpọrọakwụkwọ", "thegodofhighschool": "chinekeụlọakwụkwọsekọndrị", "kissxsis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "elui<PERSON><PERSON><PERSON><PERSON>", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "ọnwụghị<PERSON>ị", "romancemanga": "akụk<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animearjentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayernagbas<PERSON>be<PERSON><PERSON>o", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON>", "firepunch": "ọkụkpọ", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starsalign": "kpakpandondoyiri", "romanceanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "ọgw<PERSON>ry", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "ndekọragnarọk", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "mgbeocheemanga", "highschoolofthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "tekn<PERSON><PERSON><PERSON>", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "onye_isi_tenis", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "k<PERSON><PERSON>ị<PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "ogbugbuigwe", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejapan", "animespace": "<PERSON><PERSON><PERSON><PERSON>", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "onweuq", "indieanime": "animendịị", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "nwaoke", "haremanime": "animeharem", "kochikame": "kochikame", "nekoboy": "nwaokenwamba", "gashbell": "gashbell", "peachgirl": "nwa<PERSON><PERSON><PERSON><PERSON><PERSON>", "cavalieridellozodiaco": "nd<PERSON><PERSON><PERSON><PERSON>", "mechamusume": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "ndiik<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquestdai": "dragonquestdai", "heartofmanga": "o<PERSON><PERSON><PERSON>", "deliciousindungeon": "ụtọ<PERSON>me<PERSON>ụ", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "ndekọitaọg<PERSON>", "funamusea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "onyelekwamekwe", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "ọtụtụnwokendịnaachọmụnwaanyị", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "nnukwuonankuzionizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "nnamụkpuruego", "gear5": "gear5", "grandbluedreaming": "nrọgrandbluenk<PERSON>u", "bloodplus": "ọbaraagbakwu<PERSON>ere", "bloodplusanime": "bloodplusanime", "bloodcanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodc": "<PERSON><PERSON>", "talesofdemonsandgods": "akụk<PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "nlekirianimegasị", "animegirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "crowsxnjọkacha", "splatteranime": "animemkpọsa", "splatter": "gbasa", "risingoftheshieldhero": "mbilitena<PERSON><PERSON><PERSON>", "somalianime": "animesomalia", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedị<PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animespain", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON>", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "nd<PERSON><PERSON><PERSON><PERSON>", "animeidols": "animeidols", "isekaiwasmartphone": "isekaibusmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "ọkụokuabalị", "bakuganbrawler": "onyelụọg<PERSON>", "bakuganbrawlers": "ndiaghabakugan", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "chọtamanga", "princessjellyfish": "<PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "nsusuakparaiso", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeverse", "persocoms": "persocoms", "omniscientreadersview": "ọgụgụanyaonweihemmaniilenile", "animecat": "animenwaolologbo", "animerecommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openinganime": "mmegheanimeanime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "akụkọ<PERSON><PERSON>", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "ekwentiakwugboegugundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "ọgwụncha", "deathnote": "akwụkwọọnwụ", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "agh<PERSON><PERSON>", "greenranger": "greenranger", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "o<PERSON><PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "onye_egbu_mmuo_ojo", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "oguzundititan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onepieceanime": "animeonepiece", "attaquedestitans": "ogbunigwendịdike", "theonepieceisreal": "theonepiecedịadị", "revengers": "ndị<PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "mmetụ<PERSON><PERSON>", "digimonstory": "akụkọdigi<PERSON>", "digimontamers": "digimontamers", "superjail": "superjail", "metalocalypse": "igwemetalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "klọbụnd<PERSON>", "flawlesswebtoon": "webtoonzuruchaokeọ<PERSON>ụla", "kemonofriends": "<PERSON><PERSON><PERSON>", "utanoprincesama": "utanoprincesama", "animecom": "animekom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "amos<PERSON>ef<PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "ogborondiasonso", "recuentosdelavida": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}