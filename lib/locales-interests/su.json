{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "<PERSON><PERSON><PERSON><PERSON>", "cognitivefunctions": "fungsikognitif", "psychology": "psikologi", "philosophy": "filsafat", "history": "sajarah", "physics": "<PERSON><PERSON>a", "science": "<PERSON><PERSON><PERSON>", "culture": "budaya", "languages": "basa", "technology": "téknologi", "memes": "meme", "mbtimemes": "mbtimemes", "astrologymemes": "mémeastrologina", "enneagrammemes": "mememenneagram", "showerthoughts": "pikiraneukdikamar<PERSON><PERSON>", "funny": "lucu", "videos": "pidéo", "gadgets": "gadget", "politics": "politik", "relationshipadvice": "na<PERSON>é<PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "nasé<PERSON><PERSON>ahi<PERSON>", "crypto": "kripto", "news": "berita", "worldnews": "beritadunya", "archaeology": "arkéologi", "learning": "<PERSON><PERSON><PERSON>", "debates": "debat", "conspiracytheories": "teorikonspirasi", "universe": "<PERSON><PERSON><PERSON><PERSON>", "meditation": "meditasi", "mythology": "mitologi", "art": "seni", "crafts": "<PERSON><PERSON><PERSON><PERSON>", "dance": "nari", "design": "desain", "makeup": "dandan", "beauty": "<PERSON><PERSON><PERSON><PERSON>", "fashion": "modeu", "singing": "nyanyi", "writing": "nulis", "photography": "potografi", "cosplay": "cosplay", "painting": "ngagambar", "drawing": "gambar", "books": "buku", "movies": "pilem", "poetry": "sajak", "television": "televisi", "filmmaking": "nyieunfilm", "animation": "animasi", "anime": "anime", "scifi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasy": "fantasi", "documentaries": "doku<PERSON><PERSON>", "mystery": "misteri", "comedy": "komedi", "crime": "kajahatan", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horor", "romance": "asmara", "realitytv": "tvirealitas", "action": "aksi", "music": "musik", "blues": "blues", "classical": "klasik", "country": "nagara", "desi": "desi", "edm": "edm", "electronic": "éléktronik", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "imah", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "batu", "techno": "tékno", "travel": "nglambang", "concerts": "konser", "festivals": "festival", "museums": "museum", "standup": "ngadeg", "theater": "téater", "outdoors": "luar", "gardening": "kebon", "partying": "pestapora", "gaming": "<PERSON><PERSON><PERSON>", "boardgames": "paramaénansakotak", "dungeonsanddragons": "dungeonsanddragons", "chess": "<PERSON>ur", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "kadah<PERSON>", "baking": "ng<PERSON><PERSON>", "cooking": "masak", "vegetarian": "vegetarian", "vegan": "vegan", "birds": "manuk", "cats": "ucing", "dogs": "anjing", "fish": "lauk", "animals": "sasatoan", "blacklivesmatter": "hidupurangkulithideungpenting", "environmentalism": "lingkunganhirup", "feminism": "feminisme", "humanrights": "<PERSON><PERSON><PERSON>", "lgbtqally": "sekutulgbtq", "stopasianhate": "stopbenciasianaté", "transally": "sekututrans", "volunteering": "<PERSON><PERSON><PERSON>", "sports": "olahraga", "badminton": "bulutangkis", "baseball": "baseball", "basketball": "b<PERSON><PERSON><PERSON>", "boxing": "tinju", "cricket": "j<PERSON><PERSON><PERSON>", "cycling": "sa<PERSON><PERSON>", "fitness": "kabugaran", "football": "bal<PERSON>an", "golf": "golf", "gym": "gym", "gymnastics": "gimnastik", "hockey": "hoki", "martialarts": "belad<PERSON>", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "lumpat", "skateboarding": "skateboarding", "skiing": "ski", "snowboarding": "snowboarding", "surfing": "ngaombak", "swimming": "ngojay", "tennis": "<PERSON><PERSON><PERSON>", "volleyball": "voli", "weightlifting": "ang<PERSON><PERSON><PERSON><PERSON>", "yoga": "yoga", "scubadiving": "n<PERSON><PERSON><PERSON><PERSON>", "hiking": "hiking", "capricorn": "capricorn", "aquarius": "aquarius", "pisces": "pisces", "aries": "aries", "taurus": "taurus", "gemini": "gemini", "cancer": "kanker", "leo": "leo", "virgo": "virgo", "libra": "libra", "scorpio": "skor<PERSON>", "sagittarius": "sagitarius", "shortterm": "<PERSON><PERSON><PERSON>", "casual": "santuy", "longtermrelationship": "hubunganjauhpanjang", "single": "single", "polyamory": "polia<PERSON><PERSON>", "enm": "tkp", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "homo", "lesbian": "lesbian", "bisexual": "biseksual", "pansexual": "<PERSON><PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kingsquest", "soulreaver": "jiwagarong", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "legend<PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "roguelike", "syberia": "siberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "sunsetoverdrive", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "duniaterbuka", "heroesofthestorm": "pahlawanbadai", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "ngajelajahdungeon", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsoftherealm2": "pangéransaalamdunya2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "warnaholics", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "simulasiimmersive", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "witcher": "witcher", "dishonored": "<PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "immersif", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivasimorbid", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "sukafo<PERSON>inta", "otomegames": "pama<PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinawaktu", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimensi20", "gaslands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "shadowrun", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravityrush", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "oneshot", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "juragan", "yourturntodie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpghoror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "banditpangarayat", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "teksrpg", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "pangkengbom", "gurps": "gurps", "darkestdungeon": "dungeonpokék", "eclipsephase": "fases<PERSON>ru<PERSON>", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skullgirls": "skullgirls", "nightcity": "kotawendengan", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "<PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "jalan96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "kotakartun", "childoflight": "buda<PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "dunia<PERSON><PERSON><PERSON>", "monsterrancher": "monsterrancher<PERSON>", "ecopunk": "ekopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltaijo", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "hajar", "lastepoch": "epochpanungtungan", "starfinder": "pamojokbintang", "goldensun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "dosadivineoriginal", "bladesinthedark": "pisod<PERSON><PERSON><PERSON>", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "fallenorder", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "lemburjahat", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "dunyakolonasakit", "adventurequest": "patualanganpetualangan", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "pat<PERSON><PERSON><PERSON>an", "roleplayinggames": "perma<PERSON><PERSON>an", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "talesofsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "myfarog", "sacredunderworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chainedechoes": "chainedechoes", "darksoul": "<PERSON><PERSON><PERSON><PERSON>", "soulslikes": "souslikes", "othercide": "satusisi", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "pilarkalanggengan", "palladiumrpg": "palladiumrpg", "rifts": "riftah", "tibia": "tibia", "thedivision": "thedivision", "hellocharlotte": "halocharlotte", "legendofdragoon": "legendaragon<PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampir<PERSON>k<PERSON><PERSON><PERSON>", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werewolftheapocalypse", "aveyond": "aveyond", "littlewood": "ka<PERSON>u<PERSON>ung", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "hat<PERSON>in", "fable3": "fabel3", "fablethelostchapter": "fabelsababkaileungit", "hiveswap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edeneternal", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "starfield", "oldschoolrevival": "olds<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "karajaanhati1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "gel<PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "kaulin<PERSON><PERSON>g", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klanmalkavian", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON>", "bastion": "<PERSON><PERSON>", "drakarochdemoner": "drakonsarsetansetan", "skiesofarcadia": "langitarcadia", "shadowhearts": "hatebayangan", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "breathoffire4", "mother3": "indung3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "pamaénanb<PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "kronokros", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "forumrpg", "shadowheartscovenant": "pak<PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "karajaandatang", "awplanet": "awplanet", "theworldendswithyou": "duniaberakhirdenganmu", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "<PERSON><PERSON><PERSON><PERSON>", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "<PERSON>hir<PERSON><PERSON>", "blackbook": "b<PERSON><PERSON><PERSON><PERSON>", "skychildrenoflight": "<PERSON><PERSON>klang<PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "édisiemassacred", "castlecrashers": "castlecrashers", "gothicgame": "pamaénangotik", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gameroleplay", "prophunt": "prophunt", "starrails": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofmist": "kotakabut", "indierpg": "rpgindie", "pointandclick": "tunjeuknaklikkeuneun", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freeside": "<PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "paskacyberpunk", "deathroadtocanada": "jalankamatikakanada", "palladium": "paladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "pambur<PERSON><PERSON>er", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremasi", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "souleater", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "rp<PERSON><PERSON><PERSON>", "mahoyo": "mahoyo", "animegames": "gameanime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "godeater", "diluc": "diluc", "venti": "venti", "eternalsonata": "sonataabadiabadi", "princessconnect": "princessconnect", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pés", "pocketsage": "kantongbijaksana", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindia", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligaimpian", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "ébalpédés", "dreamhack": "dreamhack", "gaimin": "<PERSON><PERSON><PERSON>", "overwatchleague": "ligaoverwatch", "cybersport": "cybersport", "crazyraccoon": "rakuncageur", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "ngahapus", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantkompetitif", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON>", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "klep", "portal": "portalan", "teamfortress2": "teamfortress2", "everlastingsummer": "usum<PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "simulatorkambing", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planétbébas", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "battlefield4", "nightinthewoods": "pewendiandaleuweung", "halflife2": "halflife2", "hacknslash": "gebug<PERSON>us", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvania", "overcooked": "ka<PERSON><PERSON><PERSON><PERSON>", "interplanetary": "antarplan<PERSON><PERSON>", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "lianglubang", "stray": "<PERSON>yas<PERSON>", "battlefield": "medanperang", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "ubot", "eyeb": "alis", "blackdesert": "guru<PERSON><PERSON><PERSON>", "tabletopsimulator": "tabletopsimulator", "partyhard": "<PERSON><PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "perusakkabalpangrasangatos", "hades": "hades", "gunsmith": "tukangbedil", "okami": "<PERSON>ami", "trappedwithjester": "kajebakbarengbadut", "dinkum": "bener", "predecessor": "<PERSON><PERSON><PERSON><PERSON>", "rainworld": "<PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "simkoloni", "noita": "noita", "dawnofwar": "subuhperang", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "petengjeungpeteng", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "simkencan", "yaga": "yaga", "cubeescape": "kaburtikotak", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "kota<PERSON><PERSON>", "citiesskylines": "kotakotalangit", "defconheavy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsia", "virtualkenopsia": "kénopsiavirtual", "snowrunner": "snowrunner", "libraryofruina": "perpustakaanruina", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenaje<PERSON><PERSON>rwa<PERSON>", "placidplasticduck": "bebeksuntukplastik", "battlebit": "battlebit", "ultimatechickenhorse": "kudajagokangeutingakeun", "dialtown": "kotadial", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "malemucing", "supermeatboy": "supermeatboy", "tinnybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygrove": "cozygrove", "doom": "tungtung", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "puncak", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "gaméfarcry", "paladins": "paladins", "earthdefenseforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "huntshowdown", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakillpisan", "joinsquad": "gabungsquad", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "pemberontakanbadaipasir", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "ujungsisikeun", "divisions2": "divisi2", "killzone": "zonabunuh", "helghan": "hel<PERSON>", "coldwarzombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "kod<PERSON>alib", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "perangmodern", "neonabyss": "neonngarep", "planetside2": "planetside2", "mechwarrior": "méchwarrior", "boarderlands": "boarderlands", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "karna<PERSON><PERSON><PERSON>", "worldofwarships": "duniakapalperang", "back4blood": "balikdeuidahdarah", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "hitman", "masseffect": "masseffect", "systemshock": "sistemsyok", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "carita<PERSON><PERSON>", "doometernal": "doometernal", "centuryageofashes": "jaman<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "generasinol", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "lalakisosis", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "nyerinafantom", "warface": "beungeutperang", "crossfire": "tembakansilang", "atomicheart": "atomicheart", "blackops3": "blackops3", "vampiresurvivors": "vampiresurvivors", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "hayamanjing", "freedoom": "kabebasan", "battlegrounds": "medantempur", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "gamepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "kaulinfps", "convertstrike": "konvérstrajk", "warzone2": "warzone2", "shatterline": "<PERSON><PERSON><PERSON><PERSON>", "blackopszombies": "zombiesblackops", "bloodymess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "republikcommando", "elitedangerous": "elitebahaya", "soldat": "<PERSON><PERSON><PERSON><PERSON>", "groundbranch": "cabangdarat", "squad": "skuad", "destiny1": "takdir1", "gamingfps": "gamingfps", "redfall": "redfall", "pubggirl": "ceweupubg", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "didap<PERSON>", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "gajian2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "sabunlauk", "ghostcod": "horoscod", "csplay": "cosplay", "unrealtournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "gebukpistol", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonbodas", "remnant": "s<PERSON><PERSON>", "azurelane": "azurelane", "worldofwar": "dunya_perang", "gunvolt": "gunvolt", "returnal": "b<PERSON><PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "lindudua", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "reddead", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "lautankabanditan", "rust": "karat", "conqueronline": "conqueronline", "dauntless": "<PERSON><PERSON><PERSON><PERSON>", "warships": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "p<PERSON>kal<PERSON>", "warthunder": "warthunder", "flightrising": "flightrising", "recroom": "kamarsantai", "legendsofruneterra": "legendsofruneterra", "pso2": "pso2", "myster": "misteri", "phantasystaronline2": "phantasystaronline2", "maidenless": "mojang", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "duniatank", "crossout": "<PERSON><PERSON><PERSON><PERSON>", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "menarafantasi", "netplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubpinguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "b<PERSON><PERSON><PERSON>", "newworld": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "multiplayer", "pirate101": "bajaktlaut101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "obrol3d", "nostale": "nostale", "tauriwow": "tauruswow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "jal<PERSON><PERSON><PERSON>", "spiralknights": "spiralknights", "mulegend": "mulegenda", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "nabinaga", "grymmo": "g<PERSON>nah<PERSON>", "warmane": "<PERSON><PERSON><PERSON><PERSON>", "multijugador": "multipemain", "angelsonline": "bidadarionline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsrepublikkolot", "grandfantasia": "fantasihebat", "blueprotocol": "blueprotocol", "perfectworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riseonline": "bangkitonline", "corepunk": "corepunk", "adventurequestworlds": "petualangand<PERSON><PERSON><PERSON>", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "hewanser<PERSON>", "kingdomofloathing": "<PERSON><PERSON><PERSON>", "cityofheroes": "kotapahlanwan", "mortalkombat": "mortalkombat", "streetfighter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "kananghormatan", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nomoreheroes": "euweuhdewipahlawan", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "rajakampung", "likeadragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "gamelaw<PERSON><PERSON><PERSON>l", "blasphemous": "hu<PERSON><PERSON>", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "superpukul", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "robotsiber", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "gelutpanungtung", "poweredgear": "<PERSON><PERSON><PERSON><PERSON>", "beatemup": "babukperang", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "gamegelut", "killerinstinct": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoffigthers": "rajatempur", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "ksatria2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "kaulinansilksong", "silksongnews": "wartasilksong", "silksong": "silksong", "undernight": "pepetengan", "typelumina": "typelum<PERSON>", "evolutiontournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evomoment": "momenboo", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "dongengberseria", "bloodborne": "bloodborne", "horizon": "cakrawala", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodbourne", "uncharted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "the<PERSON><PERSON><PERSON>", "infamous": "kaso<PERSON>", "playstationbuddies": "rerencangancahplaystation", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "dewa<PERSON><PERSON>", "gris": "gris", "trove": "parongpokan", "detroitbecomehuman": "detroitjadimanuasia", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "tropip<PERSON><PERSON><PERSON>a", "lspdfr": "lspdfr", "shadowofthecolossus": "bayanga<PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "crashteamracing", "fivepd": "limapd", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "maenlo<PERSON>", "samuraiwarriors": "samurajuarangan", "psvr2": "psvr2", "thelastguardian": "walipaunggulan", "soulblade": "jiwabedog", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "buru<PERSON><PERSON>i", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "satopesta", "warharmmer40k": "warhammer40k", "fightnightchampion": "<PERSON><PERSON><PERSON><PERSON>", "psychonauts": "psikonauts", "mhw": "mhw", "princeofpersia": "pangéranpérsia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "palagan", "dontstarvetogether": "ulahkapokbabareng<PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "kabeungketkabintang", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON>per<PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "leagueofkingdoms", "fable2": "dongéng2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "tvsampahmoe", "skycotl": "skycotl", "erica": "<PERSON><PERSON>", "ancestory": "<PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "<PERSON>mm<PERSON><PERSON><PERSON>", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kultusdomba", "duckgame": "pamaénanbéb<PERSON>k", "thestanleyparable": "thestanleyparable", "towerunite": "towerunite", "occulto": "gaib", "longdrive": "jalan<PERSON><PERSON>", "satisfactory": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "spiritfarer", "darkdome": "kubahpoék", "pizzatower": "pizzatower", "indiegame": "indiegame", "itchio": "itchio", "golfit": "golfit", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON><PERSON>", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolin", "hulahoop": "<PERSON><PERSON><PERSON><PERSON>", "dare": "wani", "scavengerhunt": "burulolongkrangan", "yardgames": "kaulin<PERSON><PERSON>", "pickanumber": "pilihangka", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "man<PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "gamejeungalemes", "datinggames": "pasilingan", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sudoku", "juegos": "<PERSON><PERSON><PERSON>", "mahjong": "mahjong", "jeux": "<PERSON><PERSON><PERSON>", "simulationgames": "simualsigame", "wordgames": "kakapéngan", "jeuxdemots": "ka<PERSON><PERSON><PERSON>", "juegosdepalabras": "kaulinanke<PERSON>", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "pamaénanbos<PERSON>", "oyun": "<PERSON><PERSON><PERSON>", "interactivegames": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "adontotonan", "spiele": "<PERSON><PERSON><PERSON>", "giochi": "giochi", "geoguessr": "geoguessr", "iphonegames": "paurenamaniphone", "boogames": "boogames", "cranegame": "mesingapit", "hideandseek": "pepencoan", "hopscotch": "ucing", "arcadegames": "geulisarkade", "yakuzagames": "kaulinananyakuza", "classicgame": "kasikireubeut", "mindgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "galagames", "romancegame": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "gameyandere", "tonguetwisters": "omonganbeungkeut", "4xgames": "4xgames", "gamefi": "gamefi", "jeuxdarcades": "kaulinankampung", "tabletopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "kaulinan90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "pabalaplomba", "ets2": "ets2", "realvsfake": "<PERSON><PERSON><PERSON><PERSON>", "playgames": "ulinkeun", "gameonline": "gameolonline", "onlinegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "roleplay<PERSON><PERSON><PERSON>", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "g<PERSON><PERSON><PERSON><PERSON>", "highscore": "<PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "burgergeims", "kidsgames": "pamaénana<PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwedisiblack", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON>", "gioco": "gioco", "managementgame": "pamaénmanajemén", "hiddenobjectgame": "gamedojongbarang", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "paulinanformula1", "citybuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drdriving": "nyetirnyetir", "juegosarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "memorygames": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blowgames": "<PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "mesinpinball", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "rebat<PERSON><PERSON>", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "gameo", "lasergame": "lasergame", "imessagegames": "paaulinanimessage", "idlegames": "<PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "isianpatempatkosong", "jeuxpc": "kaulinanpc", "rétrogaming": "rétrogaming", "logicgames": "<PERSON>ulinana<PERSON><PERSON>", "japangame": "japanulinajepang", "rizzupgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subwaysurf": "balapsasubway", "jeuxdecelebrite": "pamaén<PERSON><PERSON><PERSON><PERSON>", "exitgames": "kalu<PERSON><PERSON>", "5vs5": "5vs5", "rolgame": "per<PERSON><PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "kaulinantradisional", "kniffel": "kniffel", "gamefps": "gamefps", "textbasedgames": "pauhantéks", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgame": "palingmaling", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "méjabola", "tischfußball": "tischfußball", "spieleabende": "malemmaen", "jeuxforum": "jeuxforum", "casualgames": "g<PERSON><PERSON><PERSON><PERSON>", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "mesingaruang", "játék": "<PERSON><PERSON><PERSON>", "bordfodbold": "bordfodbold", "jogosorte": "jogosorte", "mage": "dukun", "cargames": "kaulinanmobil", "onlineplay": "ulinone<PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "malemgame", "pursebingos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "randomizer": "acakacakan", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "gamékomputer", "socialdeductiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominos": "dominos", "domino": "domino", "isometricgames": "kaulinanisométrik", "goodoldgames": "gamejadulyangkeren", "truthanddare": "kabeneranngatantangan", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "buruancar<PERSON>rang", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "gamedrift", "gamesotomes": "gamesotome", "halotvseriesandgames": "halotvseriessaragame", "mushroomoasis": "oasisjamur", "anythingwithanengine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "pedang<PERSON><PERSON>", "goodgamegiving": "pertandinganseruseru", "jugamos": "<PERSON><PERSON><PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON>", "minaturegames": "<PERSON><PERSON><PERSON>leuti<PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "cintadirisarengngagem", "gamemodding": "gamemodding", "crimegames": "kejahatangame", "dobbelspellen": "dobbelspellen", "spelletjes": "<PERSON><PERSON><PERSON>", "spacenerf": "spacenerf", "charades": "pat<PERSON><PERSON><PERSON><PERSON>", "singleplayer": "pamérmaéndorangan", "coopgame": "gamékooperasi", "gamed": "<PERSON><PERSON><PERSON><PERSON>", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "ulinkeun", "kingdiscord": "rajadiscord", "scrabble": "skrebel", "schach": "<PERSON>ur", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "warisanpandemi", "camelup": "untabalapan", "monopolygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brettspiele": "brettspiele", "bordspellen": "boardgame", "boardgame": "boardgame", "sällskapspel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planszowe": "planszowe", "risiko": "risiko", "permainanpapan": "permainanpapan", "zombicide": "zombisid", "tabletop": "mejamaen", "baduk": "baduk", "bloodbowl": "bloodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "golakumaenboard", "connectfour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heroquest": "heroquest", "giochidatavolo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "karom", "tablegames": "pamaénméja", "dicegames": "ka<PERSON>berdad<PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "creationludique": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "roleplay<PERSON><PERSON><PERSON>", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "horor_sangar", "switchboardgames": "kaulinanswitchboard", "infinitythegame": "infinitythegame", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "gaul", "juegodemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "boardgame", "rednecklife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardom": "b<PERSON><PERSON><PERSON>", "applestoapples": "apeljeungapel", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "papanpermaenan", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "krokinol", "jeuxdesociétés": "dolanbordgame", "twilightimperium": "twilightimperium", "horseopoly": "kudaopoli", "deckbuilding": "ngawangundecks", "mansionsofmadness": "rumahgedegila", "gomoku": "gomoku", "giochidatavola": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "kalangkangbrimstone", "kingoftokyo": "r<PERSON><PERSON><PERSON>", "warcaby": "damdam", "táblajátékok": "boardgames", "battleship": "kapalperang", "tickettoride": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "kaulinandésktop", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "j<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "ka<PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "starwarslegion", "gochess": "go<PERSON>inkasak<PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "ka<PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "zonaperang", "arksurvivalevolved": "arksurvivalevolved", "dayz": "poé", "identityv": "identityv", "theisle": "pulo", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eco": "eco", "monkeyisland": "pulau<PERSON>et", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "<PERSON><PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "<PERSON><PERSON><PERSON><PERSON>", "pathologic": "patologis", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "poekpanjang", "ark": "ark", "grounded": "<PERSON><PERSON><PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "bapa<PERSON><PERSON>", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "b<PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "jalantitans", "frictionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexen": "dukun", "theevilwithin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "realrac", "thebackrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backrooms": "kamarburi", "empiressmp": "empiressmp", "blockstory": "caritablok", "thequarry": "<PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "cahayapade<PERSON>", "thewalkingdeadgame": "thewalkingdeadgame", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "kebang<PERSON><PERSON>kai<PERSON><PERSON>", "stateofsurvivalgame": "gamestateofsurvival", "vintagestory": "caritabaheu<PERSON>", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "breathedge", "alisa": "alisa", "westlendsurvival": "sa<PERSON><PERSON><PERSON><PERSON><PERSON>", "beastsofbermuda": "sat<PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON>ung<PERSON>ek", "survivalhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "keretakosong", "lifeaftergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "kaulinansalametkeun", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "yayasanscp", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "rakit", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "<PERSON><PERSON><PERSON>", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "nini", "littlenightmares2": "kawasserampeuting2", "signalis": "sinyal", "amandatheadventurer": "amandasis<PERSON><PERSON>ual<PERSON>", "sonsoftheforest": "putraleuweung", "rustvideogame": "rustvideogame", "outlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "aliennyasorangan", "undawn": "undawn", "7day2die": "7haripasteh", "sunlesssea": "laut<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "salamet", "propnight": "malemprop", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "vampirganteng", "deathverse": "alampati", "cataclysmdarkdays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkerbayanganchernobyl", "lifeafter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON>", "clocktower3": "menarajam3", "aloneinthedark": "sorangansapeutingeun", "medievaldynasty": "dinastijamankuno", "projectnimbusgame": "gameprojectnimbus", "eternights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftopia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theoutlasttrials": "theoutlasttrials", "bunker": "bungker", "worlddomination": "dominasidunya", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "tukangbunu<PERSON><PERSON>put", "warhammer40kcrush": "warhammer40knaksir", "wh40": "wh40", "warhammer40klove": "cintawarhammer40k", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "nyintasorority", "ilovevindicare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "iloveassasinorum": "abdicintaassasinorum", "templovenenum": "cintadicandikamiteunleungit", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "templovaheula", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40rb", "tetris": "tetris", "lioden": "lioden", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "jang<PERSON>g<PERSON>", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "<PERSON>ahl<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "btd6": "btd6", "supremecommander": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofmythology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "args": "args", "rime": "rime", "planetzoo": "duniadus<PERSON>", "outpost2": "pos2", "banished": "<PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "siagabeureum", "civilization6": "peradaban6", "warcraft2": "warcraft2", "commandandconquer": "komandannaklukkeun", "warcraft3": "warcraft3", "eternalwar": "perangabadi", "strategygames": "strategigame", "anno2070": "anno2070", "civilizationgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization4": "peradaban4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spora", "totalwar": "perangtotal", "travian": "travian", "forts": "<PERSON><PERSON>", "goodcompany": "sa<PERSON><PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "heidentum": "kapercayaankuno", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "leuwihgancangtiburicahya", "forthekings": "<PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "stratégirealtime", "starctaft": "starcraft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "karajaanduamahkota", "eu4": "eu4", "vainglory": "kasombongan", "ww40k": "ww40k", "godhood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "kelasaljabarsengdave", "plagueinc": "wabahpanyakit", "theorycraft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "peradaban3", "4inarow": "4sajajar", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "<PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "umurkarajaanna2", "disciples2": "murid2", "plantsvszombies": "tutuwu<PERSON>law<PERSON><PERSON>", "giochidistrategia": "paticerahataktik", "stratejioyunları": "stratejioyunları", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON>", "worldconquest": "dominasidunya", "heartsofiron4": "heartsofiron4", "companyofheroes": "padukabatur", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "soangsoangbebek", "phobies": "kasieunan", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "g<PERSON><PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "peradaban5", "victoria2": "victoria2", "crusaderkings": "rajaperang", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "stra<PERSON>gi", "popfulmail": "popfulmail", "shiningforce": "kakuatanmancorong", "masterduel": "masterduel", "dysonsphereprogram": "dysonsphereprogram", "transporttycoon": "rajangkutan", "unrailed": "teutrackedan", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "galaxylife": "hirupgalaxy", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "kalahkeunmenara", "battlecats": "ucingtarung", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "balapvirtual", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "needforspeedcarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "teteplanggengan", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "alicemad<PERSON>ret<PERSON>s", "darkhorseanthology": "antologikudapeupeut", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "limawengidifreddy", "saiko": "saiko", "fatalframe": "fatalframe", "littlenightmares": "ngimpiburukhaleutik", "deadrising": "<PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadisland": "<PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "cileuncialnasibnaas", "projectzero": "proyékzero", "horory": "horor", "jogosterror": "jogosterror", "helloneighbor": "halohalo", "helloneighbor2": "haloneighbor2", "gamingdbd": "gamingdbd", "thecatlady": "sianunggaduh<PERSON><PERSON>", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "horo<PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kartulawanadamkadunya", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "<PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "kartubisiklet", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legendofrun<PERSON>a", "solitaire": "solitér", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON>", "playingcards": "kartumaenan", "marvelsnap": "marvelsnap", "ginrummy": "gin<PERSON>i", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kartukoleksi", "pokemoncards": "kartupokemon", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "kartuolahraga", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "sekop", "warcry": "jeri<PERSON>per<PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "lotre", "hanafuda": "hana<PERSON>da", "theresistance": "<PERSON><PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "kart<PERSON>ugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "gamé<PERSON><PERSON><PERSON>", "darkmagician": "<PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohpangpangna", "briscas": "briska", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "remi", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobel", "mtgcommander": "mtgcommander", "cotorro": "obrolan", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "kartu", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "battlespirits", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "žolíky": "kaléngkéng", "facecard": "kartuwajah", "cardfight": "aduka<PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "marvelja<PERSON>an", "magiccartas": "kartus<PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "kudaunikornteukemah", "cyberse": "siber", "classicarcadegames": "gémeklasikarkade", "osu": "osu", "gitadora": "gitadora", "dancegames": "pamaénantari", "fridaynightfunkin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "proyékmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "gita<PERSON><PERSON>o", "clonehero": "clonehero", "justdance": "<PERSON><PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "goyangsimeninggal", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "tarisentral", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "iramasorga", "hypmic": "hypmic", "adanceoffireandice": "tarianseuneujeungés", "auditiononline": "audisionline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "doktérirama", "cubing": "kubiking", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "gametekatekan", "spotit": "te<PERSON><PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blokdoku", "logicpuzzles": "tekatekilogika", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "tatangkaranatak", "rubikscube": "rubikscube", "crossword": "tekateka<PERSON><PERSON>", "motscroisés": "katakrusil", "krzyżówki": "te<PERSON><PERSON><PERSON><PERSON>", "nonogram": "nonogram", "bookworm": "kut<PERSON><PERSON>", "jigsawpuzzles": "tebaktekijigsawpuzzle", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "tatar<PERSON><PERSON><PERSON>", "riddles": "tatar<PERSON><PERSON><PERSON>", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "jero", "angrybirds": "manukambek", "escapesimulator": "escapesimulator", "minesweeper": "n<PERSON><PERSON><PERSON><PERSON>g", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "tekatekasilang", "kurushi": "k<PERSON>hi", "gardenscapesgame": "pamaliark<PERSON><PERSON>", "puzzlesport": "olahragapuzzle", "escaperoomgames": "kamarescapegame", "escapegame": "kacanghideung", "3dpuzzle": "3dpuzzle", "homescapesgame": "gaméhomescapes", "wordsearch": "néangan_kecap", "enigmistica": "enigmistika", "kulaworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myst": "misterius", "riddletales": "tekatekidongeng", "fishdom": "fishdom", "theimpossiblequiz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "tékatékacocok3", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "unik<PERSON><PERSON>", "rubikcube": "kubiksrubikatawakubikrubik", "cuborubik": "kubusrubik", "yapboz": "yapboz", "thetalosprinciple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homescapes": "<PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "tekatekiboo", "tycoongames": "kaulinankonglomerat", "cubosderubik": "kubusrubik", "cruciverba": "tekatekasilang", "ciphers": "sandi", "rätselwörter": "rätselwörter", "buscaminas": "buscaminas", "puzzlesolving": "ngaréngsépuzzle", "turnipboy": "budaklabujepang", "adivinanzashot": "tekatekisot", "nobodies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guessing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nonograms": "nonogram", "kostkirubika": "koskirubika", "crypticcrosswords": "tekatekingaru<PERSON><PERSON>", "syberia2": "syberia2", "puzzlehunt": "buruanpuzzle", "puzzlehunts": "tékatékipuzzle", "catcrime": "dosatekoceng", "quebracabeça": "tekateki", "hlavolamy": "tatar<PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "<PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "otodefinisi", "picopark": "picopark", "wandersong": "ngumbara", "carto": "karto", "untitledgoosegame": "kauliahgansaanonim", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubiks", "maze": "tatar<PERSON><PERSON><PERSON>", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "<PERSON><PERSON><PERSON>", "portalgame": "gaméportal", "bilmece": "tatar<PERSON><PERSON><PERSON>", "puzzelen": "ngarangkai", "picross": "picross", "rubixcube": "rubik<PERSON><PERSON>", "indovinelli": "tatar<PERSON><PERSON><PERSON>", "cubomagico": "kubikrubik", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "twistedwonderland", "monopoly": "monopoli", "futurefight": "<PERSON><PERSON><PERSON>a", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "ajugjugan", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemystars", "stateofsurvival": "stateofsurvival", "mycity": "kotakuring", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "knightrun", "fireemblemheroes": "pahlawanemblemseuneu", "honkaiimpact": "honkaiimpact", "soccerbattle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "<PERSON><PERSON><PERSON><PERSON>", "kingschoice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guardiantales": "dongengparajagad", "petrolhead": "g<PERSON><PERSON><PERSON>", "tacticool": "taktiskeren", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "kurangupdate", "craftsman": "tukang", "supersus": "supersus", "slowdrive": "nyetirkalem", "headsup": "<PERSON><PERSON><PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "bedwars", "freefire": "freefire", "mobilegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "kebunsibly", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "bilyar8bola", "emergencyhq": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "j<PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "goyangjeunggeliat", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatstar": "béatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "ka<PERSON><PERSON><PERSON><PERSON>", "androidgames": "gamésandroid", "criminalcase": "kasuskriminal", "summonerswar": "summonerswar", "cookingmadness": "ma<PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "<PERSON><PERSON><PERSON><PERSON>", "leagueofangels": "ligamalaikat", "lordsmobile": "lordsmobile", "tinybirdgarden": "kebuntikuskecil", "gachalife": "gachalife", "neuralcloud": "awanneuronal", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "nekokumpul", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "warrobots", "mirrorverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "jangjangperang", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "wakeuseuseurieun", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "lebet", "slugitout": "<PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "kalahkeuntik", "wolfy": "wolfy", "runcitygame": "maengameunggkota", "juegodemovil": "gamehp", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "niru", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "bombmebrasil", "ldoe": "ldoe", "legendonline": "legendaonline", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "callofdragons", "shiningnikki": "nikkingarelap", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "j<PERSON><PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "gelutbayang3", "limbuscompany": "limbuscompany", "demolitionderby3": "derbirusak3", "wordswithfriends2": "katakatsabarakanca2", "soulknight": "ksatriajiwа", "purrfecttale": "meongtastik", "showbyrock": "showbyrock", "ladypopular": "mojangpopuler", "lolmobile": "bob<PERSON><PERSON>", "harvesttown": "kotapanén", "perfectworldmobile": "duniasamputnamobile", "empiresandpuzzles": "emperiumjeungpuzzle", "empirespuzzles": "tekatek<PERSON><PERSON><PERSON>", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "perangmobileindonesia", "fanny": "<PERSON><PERSON><PERSON><PERSON>", "littlenightmare": "impianbeurateuleutik", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "ciconysadis", "eversoul": "<PERSON><PERSON><PERSON>na", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiekasingkur", "eveechoes": "eveechoes", "jogocelular": "<PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "ma<PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "girlsfrontline", "jurassicworldalive": "jurassicworldhirup", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "caritamooncha<PERSON>", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "geummobile", "legendofneverland": "legéndaneverland", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "timeraiders", "gamingmobile": "gamingmobile", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "thebattlecats", "dnd": "dnd", "quest": "kuis", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgdimeja", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "travellertrpg", "2300ad": "2300sm", "larp": "larp", "romanceclub": "klubroman", "d20": "d20", "pokemongames": "pokémongames", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokémonkristal", "pokemonanime": "pokémonanime", "pokémongo": "pokémongo", "pokemonred": "pokémonbeureum", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "éntai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "ngobrol", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonungu", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "tim<PERSON>t", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "pok<PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "timmistik", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "pokemonngagurilap", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "tangankeras", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON><PERSON>", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "budakjeungpokemon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "pembur<PERSON><PERSON><PERSON>", "ajedrez": "<PERSON>ur", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "<PERSON>ur", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "cewekcatur", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "blitzdunya", "jeudéchecs": "jeudéchecs", "japanesechess": "cat<PERSON>jepang", "chinesechess": "<PERSON><PERSON><PERSON>a", "chesscanada": "cat<PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "<PERSON><PERSON><PERSON><PERSON>", "openings": "bukaan", "rook": "<PERSON><PERSON>", "chesscom": "chesscom", "calabozosydragones": "calabozosydragones", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "dalangdungeon", "tiamat": "tiamat", "donjonsetdragons": "dun<PERSON>dannaga", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "legendarivoxmachina", "doungenoanddragons": "dungenoanddragons", "darkmoor": "<PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "ka<PERSON>araanminecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetés", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modminecraft", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "tambahan", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftdimodif", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "antarnagara", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "kotaminecraft", "pcgamer": "gamerkomputer", "jeuxvideo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gambit": "gambit", "gamers": "gamers", "levelup": "naiklevel", "gamermobile": "gamermobile", "gameover": "bureusgame", "gg": "gg", "pcgaming": "gamingpc", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON>", "pcgames": "gamékomputer", "casualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "setupgaming", "pcmasterrace": "pcmasterrace", "pcgame": "gamekomputer", "gamerboy": "budakgame", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "maen4k", "gamerbr": "gamerbr", "gameplays": "<PERSON>ang<PERSON><PERSON>", "consoleplayer": "pam<PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "gamerkeren", "onlinegaming": "maenannagaring", "semigamer": "setengahgamer", "gamergirls": "gamercéwék", "gamermoms": "ibuibugemar", "gamerguy": "cowogamer", "gamewatcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "g<PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamercewek", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "timkerastopol", "mallugaming": "malayugaming", "pawgers": "pawgers", "quests": "tugas", "alax": "alax", "avgn": "avgn", "oldgamer": "gamerlawas", "cozygaming": "gamingkeueung", "gamelpay": "ngulik<PERSON><PERSON>", "juegosdepc": "juegosdepc", "dsswitch": "dsswitch", "competitivegaming": "gamingkompetitif", "minecraftnewjersey": "minecraftnewjersey", "faker": "palsuan", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "gaminghetero", "gamepc": "gamepési", "girlsgamer": "awéwégamer", "fnfmods": "fnfmods", "dailyquest": "questsapopoé", "gamegirl": "gamecéwék", "chicasgamer": "mojang<PERSON>r", "gamesetup": "setupgame", "overpowered": "opeuhpisan", "socialgamer": "sosialgedegamer", "gamejam": "gamejam", "proplayer": "proyékmaén", "roleplayer": "roleplayer", "myteam": "tim<PERSON><PERSON>", "republicofgamers": "republikgamer", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "legenda3x", "gamerbuddies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "butuhcewekgamers": "butuhcewekgamers", "christiangamer": "gamerkristen", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "kumputergamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "gamerkasual", "89squad": "89squad", "inicaramainnyagimana": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "insec": "euweuhpede", "gemers": "gemerz", "oyunizlemek": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamertag": "nametag_gamer", "lanparty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamer": "gamérvideo", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "gamersplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "gamersuliscatetan", "protogen": "protogen", "womangamer": "awéwégamer", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "téangan", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "kaburnol", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusik", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "fallguys", "switch": "switch", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "skychildrenofthelight", "tomodachilife": "<PERSON><PERSON><PERSON><PERSON>", "ahatintime": "satitikdiwaktos", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "simulator<PERSON><PERSON>", "nintendogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "bulansabit", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "napaspanteubeungkeut", "myfriendpedro": "sobat<PERSON>ring", "legendsofzelda": "legendsofzelda", "donkeykong": "dongkikong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "<PERSON><PERSON><PERSON><PERSON>", "earthbound": "teubeungkeut_bumi", "tales": "<PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "strategisegitiga", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "3dsbaru", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioandsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "anjingbeureum", "vanillalol": "panilalol", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "ngabawaiklan", "lolzinho": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspanyol", "aatrox": "aatrox", "euw": "iyuh", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolls", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "gerbanghe<PERSON>", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "vidéogamerétro", "scaryvideogames": "videogameserem", "videogamemaker": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "v<PERSON><PERSON><PERSON><PERSON>", "videosgame": "vidéogame", "professorlayton": "profe<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "arkade", "acnh": "acnh", "puffpals": "sobatbeuheum", "farmingsimulator": "simulatortani", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "kaulinansanbox", "videogamelore": "loregamevideo", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamscape": "alamimpi", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amordoce": "cintaman<PERSON>", "videogiochi": "v<PERSON><PERSON><PERSON><PERSON>", "theoldrepublic": "republikakuno", "videospiele": "videospiele", "touhouproject": "touhouproject", "dreamcast": "impian", "adventuregames": "palandonganpetualangan", "wolfenstein": "wolfenstein", "actionadventure": "aks<PERSON><PERSON>ualanga<PERSON>", "storyofseasons": "caritataun", "retrogames": "game<PERSON>no", "retroarcade": "<PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "komputerjadul", "retrogaming": "gamingjaman90an", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "<PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "teuadil2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "lang<PERSON><PERSON><PERSON>", "zenlife": "<PERSON><PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "<PERSON><PERSON><PERSON><PERSON>", "mystgames": "mistigames", "blockchaingaming": "blockchaingéming", "medievil": "medievil", "consolegaming": "ngagéms<PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolen", "outrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "monstergirlquest", "supergiant": "supergede", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "sim<PERSON><PERSON>", "juegosviejos": "kadigjengvintage", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "fiksinteraktif", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "novelvisual", "visualnovels": "novelvisual", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON>", "tcrghost": "tcrhanteu", "payday": "g<PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "putrise<PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "kotakpasir", "aestheticgames": "ka<PERSON><PERSON><PERSON><PERSON>", "novelavisual": "novelvisual", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "gamejaman", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "kaduhung", "godhand": "leungeundewa", "leafblowerrevolution": "révolus<PERSON><PERSON><PERSON>p", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON>", "starrail": "starrail", "keyblade": "keyblade", "aplaguetale": "dongengwabah", "fnafsometimes": "fnafsawakilah", "novelasvisuales": "novelvisual", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "gameklasik", "videojuejos": "videoju<PERSON><PERSON>", "videogamedates": "pacarananjeungvideogame", "mycandylove": "candykutersayang", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "so<PERSON>anwae", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "mula<PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "haritenta<PERSON>", "maniacmansion": "rumahhoror", "crashracing": "balapan<PERSON><PERSON>", "3dplatformers": "platform3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "gamingjadul", "hellblade": "pedangnajim", "storygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "ng<PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON>ji<PERSON>", "gameuse": "<PERSON><PERSON><PERSON>", "offmortisghost": "offmortisghost", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "powerup", "katanazero": "katananol", "famicom": "famicom", "aventurasgraficas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "k<PERSON>up<PERSON>lat", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "a<PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "<PERSON><PERSON><PERSON><PERSON>", "powerwashsim": "simulatorcucimesin", "coralisland": "<PERSON><PERSON><PERSON><PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "du<PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "logampilin", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "tumpukanwirang", "simulator": "simulator", "symulatory": "simu<PERSON>i", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "pacaranyadungeon", "toontownrewritten": "toonto<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "kaosurkota", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "na<PERSON><PERSON>", "partyvideogames": "videopermainankondangan", "graveyardkeeper": "panungguiseum", "spaceflightsimulator": "simulatorpen<PERSON>bang<PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "warisankain", "hackandslash": "tebasantebasan", "foodandvideogames": "kadaharannvideogame", "oyunvideoları": "oyunvideoları", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "simulatorngamuatan", "horizonworlds": "<PERSON><PERSON><PERSON><PERSON>", "handygame": "pamaénangampang", "leyendasyvideojuegos": "dongéngjeungvideogame", "oldschoolvideogames": "videogamejamandulu", "racingsimulator": "simulatorbalap", "beemov": "bee<PERSON>v", "agentsofmayhem": "agénsagebusuk", "songpop": "laguebak", "famitsu": "famitsu", "gatesofolympus": "gerbangolympus", "monsterhunternow": "monsterhunternow", "rebelstar": "bintangpemberontak", "indievideogaming": "gameindiesunda", "indiegaming": "gamingindié", "indievideogames": "gameindé", "indievideogame": "gamevideoindie", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "<PERSON><PERSON><PERSON><PERSON>", "projectl": "proyékl", "futureclubgames": "kaulinanklubmasadepan", "mugman": "mugman", "insomniacgames": "kaulinaninsomnia", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "gameceleste", "aperturescience": "aperturescience", "backlog": "tunggakan", "gamebacklog": "kaulinanbeulumtamat", "gamingbacklog": "backloggame", "personnagejeuxvidéos": "karaktergamevid<PERSON>o", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "langitbandung", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "anjingnakal", "beastlord": "raja<PERSON>o", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "staxel": "staxel", "videogameost": "lagu<PERSON>", "dragonsync": "nagasinkron", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "cintakofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "balappc", "berserk": "ngamuk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animesedih", "darkerthanblack": "hideungleuwihita<PERSON>", "animescaling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animewithplot": "anime<PERSON><PERSON><PERSON>na", "pesci": "pesci", "retroanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animes": "anime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80an", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "pangéranpoék", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterpogi": "master<PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000an", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonemusim1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "sampulani<PERSON>", "thevisionofescaflowne": "thevisionofescaflowne", "slayers": "slayers", "tokyomajin": "tokyomajin", "anime90s": "anime90an", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "iwakpisang", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "ha<PERSON><PERSON><PERSON><PERSON><PERSON>upe<PERSON>", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "kasombongan", "fireforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "diarymasdepan", "fairytail": "dongengparabok", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "parasit", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "ka<PERSON><PERSON>yium", "blmanga": "blmanga", "horrormanga": "<PERSON><PERSON><PERSON>", "romancemangas": "mangaromantis", "karneval": "karnaval", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "lagoonhideung", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "jenius_inc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "acertainmagicalindex", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosismik", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "anim<PERSON><PERSON><PERSON>", "sagaoftanyatheevil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shounenanime": "<PERSON>shounen", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "budakjeunggaganasan", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "<PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "imutjeungmedeni", "martialpeak": "puncak<PERSON><PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "cewehhiscore", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "<PERSON><PERSON><PERSON><PERSON>", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "animejaman", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "insumimi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "b<PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "hi<PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "umibenjaltrangér", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "thepromisedneverland", "monstermanga": "komikmonster", "yourlieinapril": "dusinanupriap<PERSON>", "buggytheclown": "buggythebadut", "bokunohero": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "<PERSON><PERSON><PERSON><PERSON><PERSON>ung<PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "deadmanwonderland", "bannafish": "pisanglauk", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "perangdahar", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "garispetan", "toyoureternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "aliansirusia", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "di<PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "goblinslayer", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "ksatriavampir", "mugi": "mugi", "blueexorcist": "blueexorcist", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "spyfamily", "airgear": "airgear", "magicalgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "tu<PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "sakolapanjara", "thegodofhighschool": "<PERSON><PERSON><PERSON><PERSON>", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "darlingdandananku", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeuniverse", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saosingget", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "matiuntung", "romancemanga": "mangaromance", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animéromans", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animéargéntina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayerkasedang", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "pu<PERSON><PERSON><PERSON><PERSON>", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "bintangcocog", "romanceanime": "anim<PERSON><PERSON><PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saofaktorintegral", "cherrymagic": "<PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "rekamragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "<PERSON><PERSON><PERSON><PERSON>", "highschoolofthedead": "smaparant<PERSON>", "germantechno": "<PERSON>k<PERSON><PERSON><PERSON>", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "assassinclassroom", "animemanga": "an<PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejepang", "animespace": "ruangang<PERSON><PERSON>", "girlsundpanzer": "girlsundpanzer", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "animeindie", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "beuri<PERSON><PERSON><PERSON>", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "budakucing", "gashbell": "gashbell", "peachgirl": "gadispecat", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "hat<PERSON>man<PERSON>", "deliciousindungeon": "nyamipoknadungeon", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "recordofragnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "lo<PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "witchhatatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "guruhebat<PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "ngarimpeungranblue", "bloodplus": "bloodplus", "bloodplusanime": "animé<PERSON><PERSON>od<PERSON><PERSON>", "bloodcanime": "animedarahim", "bloodc": "golongandarah", "talesofdemonsandgods": "dongengsetanjeungdewa", "goreanime": "goreanime", "animegirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatteranime": "animebecekeun", "splatter": "cipratan", "risingoftheshieldhero": "naeknaangsangpahlawantameng", "somalianime": "animesomalia", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "lintuhgeuseukalahdijieun", "animeyuri": "animeyuri", "animeespaña": "animespanyol", "animeciudadreal": "anim<PERSON><PERSON><PERSON><PERSON><PERSON>", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "boh<PERSON><PERSON><PERSON>", "supercampeones": "superjuara", "animeidols": "animeidol", "isekaiwasmartphone": "isekainasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofthenight": "panggilanpeuting", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "kebonbayangan", "tsubasachronicle": "tsubasachronicle", "findermanga": "finderman<PERSON>", "princessjellyfish": "put<PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekiss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animéverse", "persocoms": "persocoms", "omniscientreadersview": "bacaansangatmahataén", "animecat": "kukucinganimé", "animerecommendations": "rekomendasianime", "openinganime": "b<PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "komediromantisremajakuring", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundam", "voltesv": "voltesv", "giantrobots": "robotgedegede", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "peranggedeg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mekanisme", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "ngabéléndong", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animemiliter", "greenranger": "range<PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "kotaanime", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonslayer", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "korpsurvey", "onepieceanime": "animeonepiecé", "attaquedestitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theonepieceisreal": "the<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "revengers": "<PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "efekjoboy", "digimonstory": "caritadigimon", "digimontamers": "digimontamers", "superjail": "<PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonparipurna", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "tukangsiharerung", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "jalanallsaints", "recuentosdelavida": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}