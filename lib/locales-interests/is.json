{"2048": "2048", "mbti": "mbti", "enneagram": "enneagramm", "astrology": "stjö<PERSON><PERSON><PERSON><PERSON>", "cognitivefunctions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "philosophy": "<PERSON><PERSON><PERSON><PERSON>", "history": "saga", "physics": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "science": "vísindi", "culture": "menning", "languages": "tungumál", "technology": "tækni", "memes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mbtimemes": "mbtimemes", "astrologymemes": "stjörnuspekimím", "enneagrammemes": "enneagrammím", "showerthoughts": "s<PERSON><PERSON><PERSON><PERSON><PERSON>ð<PERSON><PERSON>", "funny": "fyndið", "videos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gadgets": "tæki", "politics": "stjórn<PERSON><PERSON><PERSON>", "relationshipadvice": "tengslaráð", "lifeadvice": "ráðumlífið", "crypto": "<PERSON>ry<PERSON><PERSON>", "news": "<PERSON><PERSON><PERSON><PERSON>", "worldnews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "learning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "debates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "samsæriskenningar", "universe": "<PERSON><PERSON><PERSON><PERSON>", "meditation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mythology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "art": "list", "crafts": "<PERSON><PERSON><PERSON><PERSON>", "dance": "dans", "design": "<PERSON><PERSON><PERSON><PERSON>", "makeup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beauty": "fegur<PERSON>", "fashion": "tíska", "singing": "syngja", "writing": "skrif", "photography": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosplay": "cosplay", "painting": "<PERSON><PERSON><PERSON><PERSON>", "drawing": "<PERSON><PERSON>na", "books": "b<PERSON><PERSON><PERSON>", "movies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poetry": "<PERSON><PERSON><PERSON><PERSON>", "television": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filmmaking": "kvikmyndagerð", "animation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anime": "anime", "scifi": "vísindaskáldskapur", "fantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentaries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mystery": "dularfullur", "comedy": "grín", "crime": "glæpur", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "hrollvekja", "romance": "rómantík", "realitytv": "raunveruleika<PERSON><PERSON><PERSON><PERSON>", "action": "<PERSON><PERSON><PERSON><PERSON>", "music": "tónlist", "blues": "blús", "classical": "klassískt", "country": "land", "desi": "desi", "edm": "edm", "electronic": "rafmagns", "folk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funk": "<PERSON><PERSON><PERSON>", "hiphop": "hiphop", "house": "h<PERSON>", "indie": "inní", "jazz": "d<PERSON>s", "kpop": "kpopp", "latin": "latín", "metal": "<PERSON><PERSON><PERSON><PERSON>", "pop": "pop<PERSON>ð", "punk": "<PERSON><PERSON><PERSON>", "rnb": "rnb", "rap": "rapp", "reggae": "regg<PERSON>", "rock": "rokk", "techno": "teknó", "travel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "concerts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "festivals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "museums": "<PERSON><PERSON><PERSON><PERSON>", "standup": "gr<PERSON><PERSON><PERSON><PERSON>", "theater": "leik<PERSON><PERSON>", "outdoors": "útivist", "gardening": "garðyrkja", "partying": "djamm", "gaming": "spilun", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chess": "skák", "fortnite": "fortnite", "leagueoflegends": "<PERSON><PERSON><PERSON>", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pok<PERSON><PERSON>", "food": "matur", "baking": "<PERSON><PERSON><PERSON><PERSON>", "cooking": "eldamennska", "vegetarian": "græ<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegan": "vegan", "birds": "fuglar", "cats": "k<PERSON><PERSON><PERSON>", "dogs": "hundar", "fish": "fiskur", "animals": "<PERSON><PERSON><PERSON>", "blacklivesmatter": "svörtlífskipta", "environmentalism": "umhverfisvernd", "feminism": "femínismi", "humanrights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "lgbtqbandamaður", "stopasianhate": "stöðvumhaturgegnaasíubúum", "transally": "transbandamaður", "volunteering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ast<PERSON><PERSON>", "sports": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "badminton": "badminton", "baseball": "ha<PERSON><PERSON><PERSON><PERSON>", "basketball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boxing": "box", "cricket": "krikket", "cycling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fitness": "he<PERSON>a", "football": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "golf": "golf", "gym": "ræ<PERSON>", "gymnastics": "íþróttafimleikum", "hockey": "hokkí", "martialarts": "kampsport", "netball": "körfuknattleikur", "pilates": "p<PERSON><PERSON>", "pingpong": "píngpong", "running": "hlaup", "skateboarding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiing": "skíði", "snowboarding": "snjóbrettaiðkun", "surfing": "yfirborð", "swimming": "sund", "tennis": "tennis", "volleyball": "röfbólti", "weightlifting": "lyftingar", "yoga": "jóga", "scubadiving": "köfun", "hiking": "gönguferðir", "capricorn": "steingeit", "aquarius": "vatnsberinn", "pisces": "fiskur", "aries": "fylgislok", "taurus": "naut", "gemini": "gemini", "cancer": "krabbamein", "leo": "leo", "virgo": "<PERSON><PERSON>ja", "libra": "vægin", "scorpio": "s<PERSON><PERSON><PERSON><PERSON>", "sagittarius": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shortterm": "stutttíma", "casual": "<PERSON><PERSON>lap<PERSON>ð", "longtermrelationship": "langtímasamband", "single": "einhleypur", "polyamory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enm": "enm", "lgbt": "<PERSON><PERSON><PERSON>", "lgbtq": "<PERSON><PERSON><PERSON>", "gay": "samkynhneigður", "lesbian": "<PERSON><PERSON><PERSON>", "bisexual": "tvíkynhneigð", "pansexual": "pansexúal", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "konungsleit", "soulreaver": "sálnaræningi", "suikoden": "su<PERSON><PERSON>", "subverse": "undirheimur", "legendofspyro": "goðsögnumelddreka", "rouguelikes": "rouguelikes", "syberia": "síbería", "rdr2": "rdr2", "spyrothedragon": "spy<PERSON>rekinn", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "sólseturyfirakstur", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rokföst", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openworld": "opinnheimur", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "dýflissupot<PERSON>", "jetsetradio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tribesofmidgard": "ættb<PERSON><PERSON>arm<PERSON>garð<PERSON>", "planescape": "flugvélalandslagið", "lordsoftherealm2": "herrarríkisins2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "litaæta", "medabots": "medabots", "lodsoftherealm2": "landareignir2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "upplifunarhermileikir", "okage": "okage", "juegoderol": "hlutverkaleikur", "witcher": "vitring<PERSON>", "dishonored": "svívirtur", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "persónuskö<PERSON>n", "immersive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasygamliskólinn", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "veikindahvatning", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegames": "o<PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vamp<PERSON>rarnarlífsetrið", "dimension20": "dimension20", "gaslands": "benzínland", "pathfinder": "vegaleiðangur", "pathfinder2ndedition": "pathfinder2útgáfa", "shadowrun": "sku<PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "ástelsknikki", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>þ<PERSON><PERSON><PERSON>", "rpg": "hlutverkaspil", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "eittskot", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yourturntodie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "hryllingshlutverkaleikir", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "útilegumenn", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "textahlutverkaleikur", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "krísarsk<PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "sólmyrkvastig", "disgaea": "disgaea", "outerworlds": "geimheimar", "arpg": "hlutverkaleikur", "crpg": "hlutverkaspil", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastywarriors", "skullgirls": "hausastelpurnar", "nightcity": "borgarlífið", "hogwartslegacy": "hogwartsarfleifð", "madnesscombat": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "al<PERSON><PERSON><PERSON>", "road96": "vegur96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelíkuleikir", "gothamknights": "gothamknights", "forgottenrealms": "g<PERSON><PERSON><PERSON><PERSON>", "dragonlance": "dragonlance", "arenaofvalor": "bardagavöllurhetja", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "skrímslaræktandi", "ecopunk": "vistpönk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "volcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "ljónaljósafjarðarvestur", "twewy": "twewy", "shadowpunk": "skuggapönk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartmystery": "hogwart<PERSON>ndarm<PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smíta", "lastepoch": "<PERSON><PERSON><PERSON>", "starfinder": "stjörnuleit", "goldensun": "gullinnsól", "divinityoriginalsin": "gu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>d", "bladesinthedark": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "røkkuð2000", "sandevistan": "sandevistan", "cyberpunk": "kýberpönk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "kíberpönkrautt", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "fallnaskipun", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimpact", "aethyr": "<PERSON><PERSON><PERSON><PERSON>", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "gamlarunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "guðdómleiki", "pf2": "pf2", "farmrpg": "búgarðarhlutverkaleikur", "oldworldblues": "gam<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "ævin<PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "hlutverkaleikir", "roleplayinggames": "roll<PERSON><PERSON>l", "finalfantasy9": "finalfantasy9", "sunhaven": "sólarhöfn", "talesofsymphonia": "talesofsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "volong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "<PERSON><PERSON><PERSON><PERSON>", "sacredunderworld": "heilagtundirheimar", "chainedechoes": "keðjubergmál", "darksoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "<PERSON><PERSON><PERSON><PERSON>ík<PERSON>", "othercide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mountandblade": "reiðogblað", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pillarsofeternity": "stoð<PERSON><PERSON><PERSON>lif<PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "rifts", "tibia": "<PERSON><PERSON><PERSON>bein", "thedivision": "<PERSON><PERSON><PERSON>", "hellocharlotte": "hallocharlotte", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "varúlfurheimsendir", "aveyond": "aveyond", "littlewood": "littaskógur", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable3": "fable3", "fablethelostchapter": "ævintýriðglataðikaflinn", "hiveswap": "hiveswap", "rollenspiel": "hlutverkaleikur", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "eilífð<PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "stjörnusvið", "oldschoolrevival": "gömlutískalifnar", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkundborgir", "savageworlds": "villturogbanvænt", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "hlutverkaleikir", "kingdomhearts": "konungsríkihjartna", "kingdomheart3": "kingdomhearts3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "ættbálkurmalkavian", "harvestella": "uppskerutíð", "gloomhaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wildhearts": "villthjörtu", "bastion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "himinnarcadia", "shadowhearts": "skuggahjörtu", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "peningablóð", "breathoffire4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mother3": "móðir3", "cyberpunk2020": "netpönk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "hlutverkaleikir", "roleplaygame": "hlutverkaleikur", "fabulaultima": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "ha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampírunagrímuballið", "dračák": "dreki", "spelljammer": "geimstý<PERSON>ður", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "tímakross", "cocttrpg": "spilhlutverkaleikur", "huntroyale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhun<PERSON><PERSON><PERSON><PERSON>", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "hlutverkaleikjaspjall", "shadowheartscovenant": "skuggahjartasáttmálinn", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "komintilvalda", "awplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "öldungaskruna", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "myrkuvillutrú", "shoptitans": "ve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forumrpg": "spjallhlutverkaspil", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackbook": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skychildrenoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "gotnesku<PERSON><PERSON><PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "draugasímintókýó", "fallout2d20": "fallout2d20", "gamingrpg": "hlutverkaleikir", "prophunt": "spámannaleit", "starrails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofmist": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "bendaogsmell<PERSON>", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "óskiptanlegur", "freeside": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "epic7": "epískt7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "eftirstimpönk", "deathroadtocanada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladium": "palladíum", "knightjdr": "<PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "veiðiskr<PERSON><PERSON><PERSON>", "fireemblem": "eldmerkið", "genshinimpact": "genshinimpact", "geosupremancy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona5": "persona5", "ghostoftsushima": "draugatsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "veiðiáskr<PERSON><PERSON><PERSON>", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "örlöginnæturnnar", "etrianodyssey": "etrianodyssey", "nonarygames": "ókynseginleikir", "tacticalrpg": "taktískthlutverkaleikirpg", "mahoyo": "mahoyo", "animegames": "animeleikir", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "gu<PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "eilífðarsónata", "princessconnect": "prinsessutengingar", "hexenzirkel": "nornak<PERSON><PERSON>", "cristales": "krist<PERSON><PERSON>", "vcs": "fjármögnun", "pes": "pess", "pocketsage": "vasavitringur", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorant<PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nba2k": "nba2k", "egames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlg": "mlg", "leagueofdreamers": "d<PERSON><PERSON><PERSON>alagið", "fifa14": "fifa14", "midlaner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "efootball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamhack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gaimin": "geimin", "overwatchleague": "overwatchdeild", "cybersport": "keppnistölvuleikir", "crazyraccoon": "brjá<PERSON>ðiþvottabjörn", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brasilgameshow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valorantcompetitive": "valorantcompetitive", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "hliðskrá2", "halflife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead": "vinstrify<PERSON>rda<PERSON>ða", "left4dead2": "left4dead2", "valve": "loki", "portal": "<PERSON><PERSON><PERSON>", "teamfortress2": "teamfortress2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "geit<PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "frelsisplánetan", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "battlefield4", "nightinthewoods": "nóttinskóginum", "halflife2": "halflife2", "hacknslash": "höggogskera", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanialeikir", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "milliplaneta", "helltaker": "helltaker", "inscryption": "<PERSON><PERSON><PERSON><PERSON>", "7d2d": "7daga<PERSON><PERSON><PERSON><PERSON>", "deadcells": "<PERSON>u<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dvergavirkið", "foxhole": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stray": "villtur", "battlefield": "slagvöllur", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "<PERSON><PERSON><PERSON><PERSON>", "eyeb": "augnbrún", "blackdesert": "svarturafrumerkur", "tabletopsimulator": "bor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partyhard": "festafast", "hardspaceshipbreaker": "<PERSON><PERSON><PERSON><PERSON>imskipabrotari", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "fastmeðgrínistanum", "dinkum": "ekta", "predecessor": "forverinn", "rainworld": "rigningarheimur", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minionmasters": "minionmeistar<PERSON>", "grimdawn": "grimdawn", "darkanddarker": "dimmtogsvertari", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "stef<PERSON>ótaleikir", "yaga": "yaga", "cubeescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hifirush": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "svencoop": "svencoop", "newcity": "nýborg", "citiesskylines": "bor<PERSON>skyggni", "defconheavy": "<PERSON><PERSON>marksviðbúnaður", "kenopsia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virtualkenopsia": "sýndareyðitómleiki", "snowrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "thenonarygames", "omegastrikers": "omegastrikers", "wayfinder": "leiðarvísir", "kenabridgeofspirits": "kenbrúandiheima", "placidplasticduck": "róleguplastönd", "battlebit": "bardagabiti", "ultimatechickenhorse": "fullkomnakjúklingahestur", "dialtown": "ske<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "brostufirimig", "catnight": "kattakvöld", "supermeatboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinnybunny": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygrove": "not<PERSON><PERSON><PERSON><PERSON>", "doom": "dó<PERSON>r", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "<PERSON><PERSON><PERSON><PERSON>", "pubg": "pubg", "callofdutyzombies": "callofdutyzomb<PERSON><PERSON>", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcryleikir", "paladins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "earthdefenseforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "huntshowdown", "ghostrecon": "draugasveit", "grandtheftauto5": "grandtheftautofimm", "warz": "str<PERSON><PERSON>", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "<PERSON><PERSON><PERSON><PERSON>", "joinsquad": "liðásamband", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "uppreisnarsandstormur", "farcry3": "farcry3", "hotlinemiami": "heittlínumiamí", "maxpayne": "maxpayne", "hitman3": "leigumorðingi3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "b4b": "f4f", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombí", "mirrorsedge": "spegi<PERSON><PERSON><PERSON><PERSON>", "divisions2": "skildir2", "killzone": "dauð<PERSON><PERSON><PERSON><PERSON><PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "kaldastríðsuppvakningar", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "flughern<PERSON><PERSON><PERSON>", "crosscode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goldeneye007": "gullnauga007", "blackops2": "blackops2", "sniperelite": "snipers<PERSON><PERSON><PERSON>", "modernwarfare": "nútímastríð", "neonabyss": "neonhylur", "planetside2": "planetside2", "mechwarrior": "vélstríðskappi", "boarderlands": "<PERSON><PERSON><PERSON><PERSON>", "owerwatch": "ower<PERSON>", "rtype": "tegund", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "frumstæðurfráfæring", "worldofwarships": "heimsstyriðaskipa", "back4blood": "aftur4blóð", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "leig<PERSON><PERSON><PERSON><PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "kerfissokk", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specops<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killingfloor2": "drepah<PERSON>ðin2", "cavestory": "hell<PERSON>ga", "doometernal": "dómsdagseileen", "centuryageofashes": "aldarhundraðöskuanna", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "muah", "division2": "tveggjanskiptingin", "tythetasmaniantiger": "tythetasmanskatígurin<PERSON>", "generationzero": "kynslóðn<PERSON>l", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "nútímastríð2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "hermanns<PERSON><PERSON>", "crossfire": "krosseldur", "atomicheart": "<PERSON><PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "frelsistortærið", "battlegrounds": "v<PERSON>g<PERSON><PERSON><PERSON>", "frag": "brot", "tinytina": "örlítlatína", "gamepubg": "leikurinnpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "fpsleikir", "convertstrike": "<PERSON><PERSON><PERSON><PERSON>", "warzone2": "stríðssvæði2", "shatterline": "brot<PERSON><PERSON>a", "blackopszombies": "blackopszombies", "bloodymess": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "sérsveitarher<PERSON>ður", "elitedangerous": "elitedangerous", "soldat": "<PERSON><PERSON><PERSON><PERSON>", "groundbranch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "vinal<PERSON><PERSON><PERSON><PERSON>", "destiny1": "örlög1", "gamingfps": "leikja<PERSON>ps", "redfall": "rauðfall", "pubggirl": "pubgstelpa", "worldoftanksblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farlight": "<PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "örsmáartínuundralönd", "halo2": "halo2", "payday2": "launadagur2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgúkra<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empyrion": "empyrion", "pubgczech": "pubgtékkland", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostcod": "spökulubbur", "csplay": "csplay", "unrealtournament": "óraunverulegmót", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "s<PERSON><PERSON><PERSON><PERSON>tameistari", "halo3": "halo3", "halo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killingfloor": "g<PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonhví<PERSON>", "remnant": "leifar", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "endurkomuvídeóleikur", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "jarðskjálfti2", "microvolts": "örvolt", "reddead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "ha<PERSON>ar", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "sjóræningjaspilið", "rust": "ryð", "conqueronline": "conqueronline", "dauntless": "<PERSON><PERSON><PERSON><PERSON>", "warships": "herskip", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "<PERSON>g<PERSON><PERSON>", "recroom": "<PERSON><PERSON><PERSON><PERSON>", "legendsofruneterra": "sa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "mistur", "phantasystaronline2": "fantasíustjarnaánetinu2", "maidenless": "ekkertbrúðurefni", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON>ursk<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "strikayðir", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "netspil", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "ofurdýrakonungleg", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "<PERSON>g<PERSON><PERSON><PERSON>", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klúbbinnpenni", "lotro": "lotro", "wakfu": "wakfu", "scum": "rusl", "newworld": "nýr<PERSON>ur", "blackdesertonline": "blackdesertonline", "multiplayer": "fjölspilun", "pirate101": "sjóræningi101", "honorofkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dchat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nostale": "ekkigamalt", "tauriwow": "taurivá", "wowclassic": "wowclassic", "worldofwarcraft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "öskusköpunar", "riotmmo": "riotmmo", "silkroad": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mulegend": "muþ<PERSON><PERSON><PERSON>", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeogandisoul", "evony": "evony", "dragonsprophet": "drakaboðorðið", "grymmo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warmane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "multijugador": "fjölspilun", "angelsonline": "eng<PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "a<PERSON><PERSON><PERSON><PERSON><PERSON>", "dcuniverseonline": "<PERSON>cal<PERSON><PERSON><PERSON><PERSON><PERSON>", "growtopia": "growtopia", "starwarsoldrepublic": "stjörnustríðgamlaveldið", "grandfantasia": "stórkostlegadraumsýn", "blueprotocol": "bláskipulag", "perfectworld": "fullkominnheimur", "riseonline": "r<PERSON><PERSON><PERSON>", "corepunk": "kjarnipönk", "adventurequestworlds": "ævintýraheimaleiðangur", "flyforfun": "flaug<PERSON><PERSON><PERSON><PERSON>", "animaljam": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "konungsríkiviðbjó<PERSON>sins", "cityofheroes": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "sýndarbardagamaður", "streetsofrage": "g<PERSON><PERSON><PERSON>_<PERSON>_reiði", "mkdeadlyalliance": "mkbanvæntbandalag", "nomoreheroes": "getiðekkijunkaðverahetjur", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "<PERSON><PERSON>og<PERSON><PERSON>", "retrofightinggames": "retróbaráttunarleikir", "blasphemous": "guð<PERSON>", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelmótikapcom", "supersmash": "víðfrægurslagur", "mugen": "mugen", "warofthemonsters": "stríðskrímsla", "jogosdeluta": "bardagaleikir", "cyberbots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "lokabardagi", "poweredgear": "kraftbúnaður", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "bardagaleikir", "killerinstinct": "morðó<PERSON><PERSON><PERSON><PERSON>t", "kingoffigthers": "kón<PERSON>rb<PERSON><PERSON>manna", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "riddaramennska2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightframhald", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksongkor<PERSON><PERSON>", "silksonggame": "silksongleikurinn", "silksongnews": "silksongfréttir", "silksong": "silkisöngur", "undernight": "<PERSON><PERSON><PERSON><PERSON>", "typelumina": "skrifstílsljos", "evolutiontournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evomoment": "mömmuaugnablik", "lollipopchainsaw": "sleikipinnafjöðursög", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "bloodborne", "horizon": "víðátta", "pathofexile": "pathofexile", "slimerancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashbandicoot": "crashbandicoot", "bloodbourne": "bló<PERSON><PERSON><PERSON>ð", "uncharted": "ókönn<PERSON>ð", "horizonzerodawn": "núllupphafsrönd", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplús", "lastofus": "lastofus", "infamous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstationbuddies": "playstationfélagarnir", "ps1": "ps1", "oddworld": "oddskrítinnheimur", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "k<PERSON><PERSON>nahliðið", "persona4": "persona4", "hellletloose": "<PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "vill<PERSON><PERSON>", "aisomniumfiles": "aisomniumskr<PERSON><PERSON>", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detroitbecomehuman": "detroitverðurmanneskja", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "skuggastyttunnar", "crashteamracing": "hraðskáksliðið", "fivepd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken7": "tekken7", "devilmaycry": "djöfullinnmágrenja", "devilmaycry3": "devilmaycry3", "devilmaycry5": "takkidjaflinnmegaræsir5", "ufc4": "ufc4", "playingstation": "spilaðáleikjastöð", "samuraiwarriors": "samúræjastríðsmenn", "psvr2": "psvr2", "thelastguardian": "síðastivör<PERSON><PERSON><PERSON>", "soulblade": "sálarsverð", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "karlaleit", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "skuggahjörtu2sáttmálinn", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "<PERSON><PERSON><PERSON>", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warharmmer40k", "fightnightchampion": "bardagakvellidmeistarinn", "psychonauts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhw": "geðheilbrigðisvika", "princeofpersia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "albtflæðir", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "stríðsvígstöðvar", "dontstarvetogether": "ekkisveltasamanspilaðu", "ori": "ori", "spelunky": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "stjörnubundinn", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "deildkonunga", "fable2": "fable2", "xboxgamepass": "xboxleikjapass", "undertale": "undertale", "trashtv": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skycotl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "litlafrökenóheppni", "sallyface": "sallyandlit", "franbow": "franbow", "monsterprom": "skrímslaballi", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "mótor<PERSON>j<PERSON><PERSON>", "outerwilds": "ytriheimar", "pbbg": "mggk", "anshi": "anshi", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duckgame": "öndaleikur", "thestanleyparable": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerunite": "<PERSON><PERSON><PERSON><PERSON>", "occulto": "huli<PERSON>", "longdrive": "langtakstur", "satisfactory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pluviophile": "rigningarunnandi", "underearth": "undirheimsins", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalgeimáæ<PERSON>unin", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>j<PERSON><PERSON>", "darkdome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pizzatower": "pizzaturn", "indiegame": "s<PERSON><PERSON><PERSON>stæðirleikir", "itchio": "itchio", "golfit": "golfa", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "leikur", "rockpaperscissors": "stein<PERSON>axpapp<PERSON><PERSON>", "trampoline": "trampólín", "hulahoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dare": "þora", "scavengerhunt": "leyn<PERSON>it", "yardgames": "gar<PERSON><PERSON><PERSON><PERSON>", "pickanumber": "veldueinatölu", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON>", "beerpong": "b<PERSON><PERSON><PERSON><PERSON>", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "notale<PERSON><PERSON><PERSON><PERSON>", "datinggames": "uppátækjasambönd", "freegame": "<PERSON><PERSON><PERSON>", "drinkinggames": "djammleikir", "sodoku": "sudoku", "juegos": "<PERSON><PERSON>r", "mahjong": "mahjong", "jeux": "<PERSON><PERSON>r", "simulationgames": "<PERSON><PERSON><PERSON><PERSON>", "wordgames": "orðaleikir", "jeuxdemots": "orðaleikir", "juegosdepalabras": "orðaleikir", "letsplayagame": "spi<PERSON><PERSON><PERSON>", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "leikur", "interactivegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "glápsleikir", "spiele": "spiele", "giochi": "<PERSON><PERSON>r", "geoguessr": "geoguessr", "iphonegames": "iphoneleikir", "boogames": "booleikir", "cranegame": "kló", "hideandseek": "f<PERSON><PERSON><PERSON><PERSON>", "hopscotch": "para<PERSON><PERSON>", "arcadegames": "t<PERSON>l<PERSON>leik<PERSON>alur", "yakuzagames": "yakuzaleikir", "classicgame": "klassískurleikur", "mindgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "giskaálagið", "galagames": "galagames", "romancegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "tungubrotarar", "4xgames": "4xleikir", "gamefi": "gamefi", "jeuxdarcades": "spilakassaleikir", "tabletopgames": "bor<PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "leikir90", "idareyou": "þ<PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "leikinfáðueðaáðir", "racinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "raunveruleikigegenlygi", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosonline": "jogosonline", "writtenroleplay": "skrifaðhlutverkaleikur", "playaballgame": "spi<PERSON><PERSON><PERSON>", "pictionary": "te<PERSON><PERSON>_og_giska", "coopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "wiileikir", "highscore": "<PERSON><PERSON><PERSON>", "jeuxderôles": "hlutverkaleikir", "burgergames": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsgames": "<PERSON><PERSON><PERSON><PERSON>", "skeeball": "keiluspil", "nfsmwblackedition": "nfsmwsvartaútgáfan", "jeuconcour": "jeuconcour", "tcgplayer": "tcgs<PERSON><PERSON><PERSON>", "juegodepreguntas": "spurningaleikur", "gioco": "gioco", "managementgame": "stjó<PERSON><PERSON>rleikur", "hiddenobjectgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roolipelit": "hlutverkaleikir", "formula1game": "formúlu1leikur", "citybuilder": "b<PERSON><PERSON><PERSON>gg<PERSON><PERSON>", "drdriving": "kíkkaíakstur", "juegosarcade": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "memorygames": "minnisleikir", "vulkan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "actiongames": "aðgerðale<PERSON>r", "blowgames": "blása_leikir", "pinballmachines": "<PERSON>par<PERSON><PERSON><PERSON>", "oldgames": "gam<PERSON><PERSON><PERSON><PERSON>", "couchcoop": "sófasamvinna", "perguntados": "spurningar", "gameo": "leikur", "lasergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "imessageleikir", "idlegames": "a<PERSON><PERSON><PERSON>aleysisleikir", "fillintheblank": "fylltuíeyðuna", "jeuxpc": "tölvuleikir", "rétrogaming": "retróleikir", "logicgames": "rökfræðileikir", "japangame": "japansleikur", "rizzupgame": "rizzuppaleikinn", "subwaysurf": "neðan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdecelebrite": "frægðarleikir", "exitgames": "útgönguleikir", "5vs5": "5gegn5", "rolgame": "hlutverkaleikur", "dashiegames": "dashiegames", "gameandkill": "leikaogslá<PERSON>", "traditionalgames": "hefðbundnirleikir", "kniffel": "kniffel", "gamefps": "leikja<PERSON>ps", "textbasedgames": "textaleikir", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrospel": "afturábakspegillinn", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "gar<PERSON><PERSON><PERSON><PERSON>", "fliperama": "spi<PERSON><PERSON><PERSON>", "heroclix": "heroclix", "tablesoccer": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tischfußball": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spieleabende": "spilakvelda", "jeuxforum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casualgames": "geðveikleikir", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>jas<PERSON><PERSON>", "cranegames": "kranaleikir", "játék": "leikur", "bordfodbold": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosorte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "<PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "leik<PERSON><PERSON><PERSON><PERSON>", "pursebingos": "handtöskubingó", "randomizer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "tölvuleikir", "socialdeductiongames": "félagsheilaþrautaleikir", "dominos": "dominos", "domino": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isometricgames": "ísómetrískleikir", "goodoldgames": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>góðu", "truthanddare": "sann<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "sýndarveruleikaleikir", "romhack": "rómhakk", "f2pgamer": "f2pspilari", "free2play": "ókeypistíleikja", "fantasygame": "fantas<PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "rekstrarleikur", "gamesotomes": "leikiotomes", "halotvseriesandgames": "haloþættirnirogleikirnir", "mushroomoasis": "sveppavín", "anythingwithanengine": "<PERSON><PERSON><PERSON>em<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "alhliðaleikur", "swordandsorcery": "sverðoggal<PERSON>r", "goodgamegiving": "got<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "spilum", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "tölvuleikir", "virgogami": "meygjusköpun", "gogame": "kláraðaleik", "jeuxderythmes": "taktleikir", "minaturegames": "smáleikir", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>ástagaming", "gamemodding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crimegames": "glæpaleikir", "dobbelspellen": "bor<PERSON><PERSON><PERSON>", "spelletjes": "<PERSON><PERSON>r", "spacenerf": "geimleikfangageymir", "charades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgame": "samvinnuspil", "gamed": "geimaði", "forzahorizon": "forzahorizon", "nexus": "tengsl", "geforcenow": "geforcenow", "maingame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "konungurdiscord", "scrabble": "krossgátur", "schach": "skák", "shogi": "shogi", "dandd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catan": "catan", "ludo": "<PERSON><PERSON><PERSON><PERSON>", "backgammon": "bakgammon", "onitama": "onitama", "pandemiclegacy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "camelup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopolygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brettspiele": "brettspil", "bordspellen": "bor<PERSON><PERSON><PERSON>", "boardgame": "bor<PERSON><PERSON><PERSON>", "sällskapspel": "bor<PERSON><PERSON><PERSON>", "planszowe": "bor<PERSON><PERSON><PERSON>", "risiko": "<PERSON><PERSON><PERSON><PERSON>", "permainanpapan": "bor<PERSON><PERSON><PERSON>", "zombicide": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletop": "bor<PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectfour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroquest": "hetjuleit", "giochidatavolo": "bor<PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "karróm", "tablegames": "bor<PERSON><PERSON><PERSON>", "dicegames": "teningaspil", "yatzy": "yatzy", "parchis": "parchís", "jogodetabuleiro": "bor<PERSON><PERSON><PERSON>", "jocuridesocietate": "samfélagsspil", "deskgames": "skrifborðsleikir", "alpharius": "alpharius", "masaoyunları": "fjöldaleikir", "marvelcrisisprotocol": "marvelkrísumálsskipan", "cosmicencounter": "geimverufundur", "creationludique": "skö<PERSON><PERSON>gleði", "tabletoproleplay": "borðhlutverkaleikir", "cardboardgames": "pappaspil", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "skiptiborðsleikir", "infinitythegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "konungsríkisdauði", "yahtzee": "yatzee", "chutesandladders": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "társas": "félagslíf", "juegodemesa": "bor<PERSON><PERSON><PERSON>", "planszówki": "bor<PERSON><PERSON><PERSON>", "rednecklife": "sveitastelpulíf", "boardom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applestoapples": "e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "bor<PERSON><PERSON><PERSON>", "gameboard": "spi<PERSON><PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "krokinól", "jeuxdesociétés": "bor<PERSON><PERSON><PERSON>", "twilightimperium": "geimskákveldheimur", "horseopoly": "hestapólí", "deckbuilding": "kortsspilagerð", "mansionsofmadness": "vit<PERSON><PERSON>u<PERSON>lur", "gomoku": "gomoku", "giochidatavola": "bor<PERSON><PERSON><PERSON>", "shadowsofbrimstone": "skuggarbrimsteins", "kingoftokyo": "kóngurtókýó", "warcaby": "warcaby", "táblajátékok": "bor<PERSON><PERSON><PERSON>", "battleship": "<PERSON><PERSON><PERSON><PERSON>", "tickettoride": "farmiðiaðferðast", "deskovehry": "bor<PERSON><PERSON><PERSON>", "catán": "kat<PERSON>", "subbuteo": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdeplateau": "bor<PERSON><PERSON><PERSON>", "stolníhry": "bor<PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "bor<PERSON><PERSON><PERSON>", "gesellschaftsspiele": "samfélagsspil", "starwarslegion": "starwarslegion", "gochess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "bor<PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "str<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i", "arksurvivalevolved": "arksurvivalevolved", "dayz": "da<PERSON><PERSON>", "identityv": "identityv", "theisle": "<PERSON><PERSON><PERSON>", "thelastofus": "síðast<PERSON>ðalokka<PERSON>", "nomanssky": "en<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "grafhýsisr<PERSON>ninginn", "callofcthulhu": "ka<PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eco": "<PERSON><PERSON><PERSON><PERSON>svænt", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "plánetan", "daysgone": "liðnirlífsdagar", "fobia": "fóbía", "witchit": "nornasettað", "pathologic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "norðurgarður", "7dtd": "7dagar", "thelongdark": "myrk<PERSON>ð<PERSON><PERSON>", "ark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grounded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "geðveikurpabbi", "dontstarve": "ikkisvelta", "eternalreturn": "síeiliðendurtekning", "pathoftitans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "frictionalgames", "hexen": "nornir", "theevilwithin": "illskanninnra", "realrac": "alvöruhjólreiðar", "thebackrooms": "bakherbergin", "backrooms": "bakvídd", "empiressmp": "empiressmp", "blockstory": "b<PERSON><PERSON><PERSON><PERSON>", "thequarry": "grjótnámið", "tlou": "tlou", "dyinglight": "deyjaþeg<PERSON><PERSON><PERSON><PERSON>iðke<PERSON><PERSON>", "thewalkingdeadgame": "thewalkingdeadleikurinn", "wehappyfew": "viðhinuf<PERSON><PERSON>", "riseofempires": "rísríkjanna", "stateofsurvivalgame": "stateofsurvivalgame", "vintagestory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvival": "arklifun", "barotrauma": "loftþ<PERSON><PERSON>stingsáverki", "breathedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "vesturbæjarlifun", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "hryllingslifi", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "t<PERSON>mlestafarþeginn", "lifeaftergame": "lífeftirgeim", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scps<PERSON><PERSON><PERSON><PERSON>", "greenproject": "grænverkefni", "kuon": "kuon", "cryoffear": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "fleki", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "<PERSON>u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil8": "residentevil8", "onironauta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "granny": "amma", "littlenightmares2": "littamartröðunum2", "signalis": "merki", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "synirskógarins", "rustvideogame": "rust<PERSON><PERSON><PERSON><PERSON>", "outlasttrials": "úthaldstilraunir", "alienisolation": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "morgunsól", "7day2die": "7daga2deyja", "sunlesssea": "<PERSON><PERSON><PERSON>laus<PERSON>j<PERSON><PERSON>", "sopravvivenza": "lifun", "propnight": "uppástungakvöld", "deadisland2": "dauðaeyjað2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampíra", "deathverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "hamfarirdökkudagar", "soma": "suma", "fearandhunger": "óttioghun<PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "lí<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "aldarmyrkur", "clocktower3": "klukkuturn3", "aloneinthedark": "eineinhvímmyrkri", "medievaldynasty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "projectnimbusleikurinn", "eternights": "eilífðarnætur", "craftopia": "fönd<PERSON><PERSON>ur", "theoutlasttrials": "outlast<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bunker": "bunker", "worlddomination": "he<PERSON>y<PERSON>rr<PERSON>ð", "rocketleague": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tft": "þfþ", "officioassassinorum": "<PERSON>or<PERSON><PERSON><PERSON><PERSON><PERSON>", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40k<PERSON>tin", "wh40": "wh40", "warhammer40klove": "warhammer40kást", "warhammer40klore": "warhammer40kheimspeki", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "musteriskulexus", "vindicare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovesororitas": "elskafélagssystur", "ilovevindicare": "égelskavindicare", "iloveassasinorum": "égelskamorðingjaröðina", "templovenenum": "templovenenum", "templocallidus": "staðbundnakuldakast", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "hofbóðibr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "morðingjastarfið", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40þús", "tetris": "tetris", "lioden": "ljónaspilið", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "þaðþarftvo", "wingspan": "vængfang", "terraformingmars": "terraformingámars", "heroesofmightandmagic": "hættirmeðkraftiogaldri", "btd6": "btd6", "supremecommander": "æðstistjórna<PERSON>", "ageofmythology": "aldurgođsagna", "args": "<PERSON><PERSON><PERSON>", "rime": "hrímbora", "planetzoo": "planet<PERSON>od<PERSON><PERSON><PERSON><PERSON><PERSON>", "outpost2": "útstöð2", "banished": "útskúfaður", "caesar3": "caesar3", "redalert": "rauð<PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "stjórnaðuogsigraðu", "warcraft3": "warcraft3", "eternalwar": "eilífðarstríð", "strategygames": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "si<PERSON><PERSON><PERSON>arleikur", "civilization4": "sið<PERSON>ning4", "factorio": "factorio", "dungeondraft": "fangehlaðborð", "spore": "sp<PERSON><PERSON>", "totalwar": "<PERSON><PERSON>darstr<PERSON><PERSON>", "travian": "travian", "forts": "kofar", "goodcompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "heimsheimur", "heidentum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON>ra<PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "konungsríkitvöarkórónur", "eu4": "evrópusambandið4", "vainglory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40þ", "godhood": "<PERSON>u<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno": "ártal", "battletech": "tæk<PERSON><PERSON>usta", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davesskemmtilegialgebrutími", "plagueinc": "plágueintökk", "theorycraft": "kenningasmiður", "mesbg": "mesbg", "civilization3": "siðmenning3", "4inarow": "4íröð", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "framfarastríð", "ageofempires2": "ageofempires2", "disciples2": "lærisveinar2", "plantsvszombies": "plönturmótvinsælar", "giochidistrategia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stratejioyunları": "hernaðaráætlunarleikir", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "he<PERSON>y<PERSON>rr<PERSON>ð", "heartsofiron4": "járnhjörtu4", "companyofheroes": "félaghetja", "battleforwesnoth": "or<PERSON>anu<PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerdrápssveit", "goosegooseduck": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phobies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "útiplanið", "turnbased": "taktengdur", "bomberman": "sprengju<PERSON><PERSON><PERSON>", "ageofempires4": "ageofempires4", "civilization5": "civ5", "victoria2": "victoria2", "crusaderkings": "konungakrossferð", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "stefna", "popfulmail": "poppfullurpóstur", "shiningforce": "glansafl", "masterduel": "meistaraeinvígi", "dysonsphereprogram": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transporttycoon": "flutningsjöfur", "unrailed": "afspori", "magicarena": "<PERSON><PERSON>na", "wolvesville": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "upphéraðskonungsríki", "galaxylife": "vetrarbrautalíf", "wolvesvilleonline": "úlfarbærnetið", "slaythespire": "si<PERSON>rvegaspi<PERSON>ð", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simborg", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "<PERSON><PERSON><PERSON><PERSON>", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "þ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ð<PERSON>", "needforspeedcarbon": "þörffyrirhraðakolefni", "realracing3": "alvöruakstursleikur3", "trackmania": "trackmania", "grandtourismo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON>", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "alicemad<PERSON>ret<PERSON>s", "darkhorseanthology": "hulduljóðfræðisafn", "phasmophobia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "fimmkvöldhjafreddys", "saiko": "saiko", "fatalframe": "banvænilegirrammar", "littlenightmares": "litlarmartranir", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "litlamissógæfa", "projectzero": "núllverkefni", "horory": "<PERSON><PERSON><PERSON><PERSON>", "jogosterror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "hællógranni2", "gamingdbd": "gamingdbd", "thecatlady": "ka<PERSON>konan", "jeuxhorreur": "hrollleikir", "horrorgaming": "h<PERSON>lingsleikir", "magicthegathering": "spilinmiklisamkomur", "mtg": "mtg", "tcg": "spil", "cardsagainsthumanity": "kortgegnmannkyninu", "cribbage": "k<PERSON><PERSON><PERSON><PERSON>", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinok<PERSON>ll", "codenames": "duln<PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "<PERSON><PERSON><PERSON><PERSON>", "thegwent": "thegwent", "legendofrunetera": "sagnaarúnaterru", "solitaire": "kapall", "poker": "<PERSON><PERSON><PERSON>", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "lyklasmiðja", "cardtricks": "kortabrögð", "playingcards": "spil", "marvelsnap": "marvelsnap", "ginrummy": "rommí", "netrunner": "nettglímir", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kaupsýningarkort", "pokemoncards": "poke<PERSON>pil", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "spilafíktvanguard", "duellinks": "duellinks", "spades": "️", "warcry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "<PERSON><PERSON><PERSON>", "hanafuda": "hana<PERSON>da", "theresistance": "mótspyrnan", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohspil", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "tvíkeppnisskífa", "yugiohgame": "yug<PERSON>hl<PERSON><PERSON><PERSON>", "darkmagician": "dökkurmeistarinn", "blueeyeswhitedragon": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "briscas": "tromm", "juegocartas": "spilaspil", "burraco": "bur<PERSON>o", "rummy": "rommí", "grawkarty": "g<PERSON><PERSON><PERSON><PERSON>", "dobble": "<PERSON><PERSON><PERSON><PERSON>", "mtgcommander": "mtgcommander", "cotorro": "s<PERSON><PERSON><PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgdómari", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgforsmíðaðurstjórnandi", "kartenspiel": "s<PERSON><PERSON><PERSON>", "carteado": "spi<PERSON><PERSON>", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "fjandabardagar", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "spilaspil", "žolíky": "jokerarnir", "facecard": "kort", "cardfight": "kortaslag", "biriba": "biriba", "deckbuilders": "spilastokkasmiðir", "marvelchampions": "marvel_hetjur", "magiccartas": "töfrakort", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "skuggaheimur", "skipbo": "skip<PERSON>", "unstableunicorns": "óstöðugireinahyrningar", "cyberse": "kýberöryggi", "classicarcadegames": "klassískleikjasalurinn", "osu": "osu", "gitadora": "gitadora", "dancegames": "dansspil", "fridaynightfunkin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "gítarhetja", "clonehero": "klónhetja", "justdance": "baradansa", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rokksmiður", "idolish7": "idolish7", "rockthedead": "rok<PERSON><PERSON><PERSON><PERSON>ða", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "dansmiðstöð", "rhythmgamer": "taktspilarar", "stepmania": "stepmania", "highscorerythmgames": "háskorry<PERSON><PERSON><PERSON>", "pkxd": "pkxd", "sidem": "<PERSON><PERSON><PERSON><PERSON>all", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "hljóðspenna", "rhythmheaven": "takttengdihimnaríki", "hypmic": "hypmic", "adanceoffireandice": "eldsdansogís", "auditiononline": "áheyrnarprufaánets", "itgmania": "þaðergáfukvöl", "juegosderitmo": "taktleikir", "cryptofthenecrodancer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rhythmdoctor": "taktlæknirinn", "cubing": "kubbaleikir", "wordle": "or<PERSON><PERSON>", "teniz": "teniz", "puzzlegames": "púsluspil", "spotit": "s<PERSON><PERSON><PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "kub<PERSON><PERSON>", "logicpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "he<PERSON>brot", "rubikscube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossword": "k<PERSON><PERSON><PERSON><PERSON>", "motscroisés": "krossgátur", "krzyżówki": "krossgátur", "nonogram": "nonógram", "bookworm": "bókabarnið", "jigsawpuzzles": "labbipúsl", "indovinello": "g<PERSON><PERSON>", "riddle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riddles": "<PERSON><PERSON><PERSON>", "rompecabezas": "þraut", "tekateki": "tekateki", "inside": "inni", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minesweeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "krossgátur", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesleikur", "puzzlesport": "þ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "flóttaleikirnar", "escapegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "3dpúsl", "homescapesgame": "homescapesle<PERSON><PERSON><PERSON>", "wordsearch": "orðaleit", "enigmistica": "<PERSON><PERSON><PERSON>", "kulaworld": "kúlheimur", "myst": "vinur", "riddletales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "fiskheimur", "theimpossiblequiz": "ómögulegaspurningakeppnin", "candycrush": "nammiðakrossað", "littlebigplanet": "littlebigplanet", "match3puzzle": "match3þraut", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "k<PERSON>", "rubikcube": "<PERSON><PERSON><PERSON><PERSON>", "cuborubik": "kubburikurubik", "yapboz": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "thetalosprinciple": "thetalosprinciple", "homescapes": "heimilislandslagið", "puttputt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "qbert": "qbert", "riddleme": "þ<PERSON><PERSON><PERSON><PERSON><PERSON>", "tycoongames": "auðkýfaleikir", "cubosderubik": "kubburnarúbiks", "cruciverba": "k<PERSON><PERSON><PERSON><PERSON>", "ciphers": "<PERSON><PERSON><PERSON><PERSON>", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "sprengjuleit", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "gáturheitar", "nobodies": "enginn", "guessing": "a<PERSON><PERSON><PERSON>", "nonograms": "nonókross", "kostkirubika": "kostkirúbíks", "crypticcrosswords": "dulduðkrossgátur", "syberia2": "syberia2", "puzzlehunt": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "þ<PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "kattaklúður", "quebracabeça": "þraut", "hlavolamy": "<PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "síðastabálið", "autodefinidos": "sjálfskilgreindir", "picopark": "picopark", "wandersong": "ferð<PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "óheitlausaríáluleikurinn", "cassetête": "hausverkur", "limbo": "millibilsástand", "rubiks": "rubiks", "maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinykin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikovakostka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "speedcube": "<PERSON>rað<PERSON>ingur", "pieces": "stykki", "portalgame": "portalgame", "bilmece": "þraut", "puzzelen": "púsluspil", "picross": "myndakross", "rubixcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinelli": "<PERSON><PERSON><PERSON>", "cubomagico": "töfrakubbur", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "undursamleg<PERSON><PERSON><PERSON>", "monopoly": "einokun", "futurefight": "framtíðarbarátta", "mobilelegends": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "spc", "lonewolf": "ein<PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "<PERSON><PERSON><PERSON><PERSON>", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "stjörnuensemble", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "kökurunnarkóngadæmið", "alchemystars": "alchemystars", "stateofsurvival": "ástandlifunar", "mycity": "borgínmín", "arknights": "arknights", "colorfulstage": "litríktsviðið", "bloonstowerdefense": "blúnaturnvö<PERSON>in", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperframhlið", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "eldmerkihetjur", "honkaiimpact": "honkaiimpact", "soccerbattle": "<PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "símaleikir", "kingschoice": "konungskjör", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "<PERSON><PERSON><PERSON><PERSON>", "tacticool": "<PERSON><PERSON><PERSON><PERSON>", "cookierun": "kexjakeppni", "pixeldungeon": "pixelfangehelsið", "arcaea": "arcaea", "outoftheloop": "ekkiígangin<PERSON>", "craftsman": "handverkari", "supersus": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "slowdrive": "hægakstur", "headsup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wordfeud": "orðastríð", "bedwars": "rúmstr<PERSON>ð", "freefire": "freefire", "mobilegaming": "farsímaleikir", "lilysgarden": "garð<PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "lagliðsbaráttuaðferðir", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enstars": "enstj<PERSON><PERSON><PERSON>", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "landbúnaðar<PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "skjálftarogfikta", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclanleikurinn", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "tímaprinssesa", "beatstar": "taktsterrí", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disneyspegil<PERSON>uri<PERSON>", "pocketlove": "kósýelskur", "androidgames": "androidle<PERSON>r", "criminalcase": "glæpamál", "summonerswar": "summonerswar", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "spurning<PERSON><PERSON><PERSON><PERSON>", "leagueofangels": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "örlítilfuglaskrúðgarður", "gachalife": "gachalife", "neuralcloud": "taugaský", "mysingingmonsters": "syngjandiskrímslinsérbiðin", "nekoatsume": "kisukoma", "bluearchive": "blááskjalasafn", "raidshadowlegends": "raidshadowlegends", "warrobots": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "str<PERSON>ðsvæng<PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futime": "<PERSON><PERSON><PERSON><PERSON>", "antiyoy": "<PERSON><PERSON><PERSON><PERSON>", "apexlegendmobile": "apexlegendmobile", "ingress": "inngangur", "slugitout": "kýldupáþað", "mpl": "mpl", "coinmaster": "my<PERSON><PERSON><PERSON><PERSON>", "punishinggrayraven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfy": "<PERSON><PERSON><PERSON>", "runcitygame": "hlaupaborgarleikar", "juegodemovil": "farsímaleikur", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "eftirhermur", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rússíbanatýkúnn", "grandchase": "grandchase", "bombmebrasil": "sprengdumigbrasilía", "ldoe": "ldoe", "legendonline": "þ<PERSON>óðsagaáneti", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>staðar", "sealm": "sælinumót", "shadowfight3": "skuggabardagi3", "limbuscompany": "limbuscompany", "demolitionderby3": "krotleikur3", "wordswithfriends2": "vinirmeðorð2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "mja<PERSON>f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ý<PERSON>", "showbyrock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladypopular": "stel<PERSON><PERSON>dy", "lolmobile": "<PERSON><PERSON><PERSON><PERSON>", "harvesttown": "<PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "fullkominnheim<PERSON><PERSON><PERSON><PERSON>", "empiresandpuzzles": "heimsveldioggátur", "empirespuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "slagvöllurámobilind", "fanny": "rass", "littlenightmare": "<PERSON><PERSON>ð<PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "tárinmín", "eversoul": "eilífðars<PERSON><PERSON>", "gunbound": "skotheimur", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "riddaraörk", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "uppvakningareyjarskeggjar", "eveechoes": "eveechoes", "jogocelular": "farsímaleikur", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "v4": "v4", "cookingmama": "el<PERSON>enn<PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "streetfighterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "leik<PERSON>bg<PERSON>", "girlsfrontline": "stelpuframlín<PERSON>", "jurassicworldalive": "jurassicworldalive", "soulseeker": "sálarleit", "gettingoverit": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "tunglk<PERSON>ðjusaga", "carxdriftracingonline": "bílad<PERSON>ing<PERSON><PERSON><PERSON>", "jogosmobile": "farsímaleikir", "legendofneverland": "goðsögneverlands", "pubglite": "pubglite", "gamemobilelegends": "leikjafarsímaaðdáendur", "timeraiders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmobile": "farsímaleikir", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "bardagakettirnir", "dnd": "btm", "quest": "verkefni", "giochidiruolo": "hlutverkaleikir", "dnd5e": "dnd5e", "rpgdemesa": "taflahlutverkaspil", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "ferðalangurtrpg", "2300ad": "2300ek", "larp": "hlutverkaspil", "romanceclub": "rómantíkklúbb<PERSON>", "d20": "d20", "pokemongames": "pokemonle<PERSON>r", "pokemonmysterydungeon": "pokémonleynidýflur", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokémoneinvígi", "pokemonranger": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "lipeep": "var<PERSON><PERSON>n", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "he<PERSON><PERSON>", "hypno": "s<PERSON><PERSON><PERSON><PERSON>", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtvö", "paldea": "paldea", "pokemonscarlet": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatot": "chat<PERSON>ð", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonfjólublátt", "pokemonpurpura": "pokemonfjólublátt", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "náttu", "teamrocket": "liðrokket", "furret": "ferret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "vasapokemonar", "nuzlocke": "nuzlocke", "pokemonplush": "pok<PERSON><PERSON><PERSON><PERSON>", "teamystic": "li<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokeball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charmander": "charmander", "pokemonromhack": "pokémonromhökk", "pubgmobile": "pubgmobile", "litten": "<PERSON><PERSON>u", "shinypokemon": "sk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokévore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "pokemonsvefn", "heyyoupikachu": "heihælópíkasú", "pokémonmaster": "pokémonmeistari", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "krakkarnirokkemon", "pokemonsnap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "glitruveiðimaður", "ajedrez": "skák", "catur": "<PERSON>ur", "xadrez": "skák", "scacchi": "skák", "schaken": "schaken", "skak": "skák", "ajedres": "tafl", "chessgirls": "skákskelpurnar", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "heimsblikk", "jeudéchecs": "jeudéchecs", "japanesechess": "japansktskák", "chinesechess": "kínverskskák", "chesscanada": "skákkanada", "fide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xadrezverbal": "mun<PERSON><PERSON><PERSON><PERSON>", "openings": "opnanir", "rook": "hrókur", "chesscom": "chesscom", "calabozosydragones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonmaster": "dungeonmeistari", "tiamat": "tiamat", "donjonsetdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oxventure": "oxventure", "darksun": "dökkursól", "thelegendofvoxmachina": "goðsöginumvoxmachina", "doungenoanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmoor": "myrkurmór", "minecraftchampionship": "minecraftmeistaramót", "minecrafthive": "minecraftkúfan", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "við<PERSON><PERSON><PERSON>", "mcpeaddons": "mcpe<PERSON><PERSON>b<PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftvasaútgáfa", "minecraft360": "minecraft360", "moddedminecraft": "moddaðminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "millilanda", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftborg", "pcgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvideo": "tölvuleikir", "gambit": "gambit", "gamers": "spilarar", "levelup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameover": "leikurlokið", "gg": "gg", "pcgaming": "tölvuleikir", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "tölvuleikir", "casualgaming": "s<PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "leikjauppsetning", "pcmasterrace": "tölvumeistarakynið", "pcgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vrgaming": "sýndarveruleikaspilun", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "leikspilun", "consoleplayer": "t<PERSON><PERSON><PERSON>leik<PERSON>i", "boxi": "boxi", "pro": "atvinnumaður", "epicgamers": "e<PERSON><PERSON><PERSON>geymarar", "onlinegaming": "netleikir", "semigamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamergirls": "tölvuleikjastelpur", "gamermoms": "gamermömmur", "gamerguy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamewatcher": "leikjaspæjari", "gameur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerstelpur", "otoge": "otome", "dedsafio": "dedsafio", "teamtryhard": "liðhörkulega", "mallugaming": "<PERSON><PERSON><PERSON><PERSON>", "pawgers": "voffvoff", "quests": "<PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "gamlag<PERSON><PERSON><PERSON>", "cozygaming": "notale<PERSON><PERSON>", "gamelpay": "gammalborgun", "juegosdepc": "tölvuleikir", "dsswitch": "dsswitch", "competitivegaming": "samkeppnisleikir", "minecraftnewjersey": "minecraftnewjersey", "faker": "<PERSON><PERSON><PERSON><PERSON>", "pc4gamers": "pc4gamers", "gamingff": "<PERSON><PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "<PERSON><PERSON><PERSON><PERSON>", "gamepc": "tölvuleikir", "girlsgamer": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "<PERSON><PERSON><PERSON><PERSON>", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "stelpur_sem_spila", "gamesetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "ofursterkt", "socialgamer": "f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "leikjahakkathon", "proplayer": "atvinnumaður", "roleplayer": "<PERSON><PERSON><PERSON><PERSON>", "myteam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republicofgamers": "lýðveldileikjanna", "aorus": "aorus", "cougargaming": "púmaleikir", "triplelegend": "þrefaldhetja", "gamerbuddies": "leikjafélag<PERSON>", "butuhcewekgamers": "þar<PERSON>ge<PERSON>", "christiangamer": "kristinnleikjaspilari", "gamernerd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nerdgamer": "nördleikjari", "afk": "afk", "andregamer": "andregamer", "casualgamer": "spi<PERSON><PERSON>", "89squad": "89sveitin", "inicaramainnyagimana": "hvernigáaðspilahana", "insec": "<PERSON><PERSON><PERSON><PERSON>", "gemers": "gema<PERSON>", "oyunizlemek": "oyunizlemek", "gamertag": "geimatag", "lanparty": "<PERSON>npar<PERSON>ý", "videogamer": "t<PERSON><PERSON><PERSON>leik<PERSON>i", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "playstationspilari", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "heilbrigðurgamer", "gtracing": "gtracing", "notebookgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>sp<PERSON><PERSON>", "protogen": "protogen", "womangamer": "kvennleikjaspilari", "obviouslyimagamer": "augljóslegaeile<PERSON>pi<PERSON>i", "mario": "mario", "papermario": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariogolf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "fæðuleitarmaður", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendo64": "nintendo64", "zeroescape": "núllf<PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusic", "sonicthehedgehog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switch": "skipta", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "goðsöguzel<PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "hringfit", "amiibo": "amiibo", "megaman": "<PERSON>gama<PERSON><PERSON>", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "saksóknaralögfræðingur", "ssbm": "ssbm", "skychildrenofthelight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "vinadalífið", "ahatintime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "tárinkonungsríkisins", "walkingsimulators": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogames": "nintendoleikir", "thelegendofzelda": "hringavöldurzelda", "dragonquest": "dragonquest", "harvestmoon": "uppskerutungl", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "villtöndurheima", "myfriendpedro": "vinurminnpedro", "legendsofzelda": "legendsofzelda", "donkeykong": "asnakong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "<PERSON><PERSON><PERSON><PERSON>", "earthbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tales": "<PERSON><PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "þ<PERSON><PERSON><PERSON><PERSON><PERSON>áætlun", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendos": "nintendos", "new3ds": "ný3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyr<PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariofylkissuperstjörnur", "marioandsonic": "marioogsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "zeldaleikurinn", "palia": "palia", "marioandluigi": "mariooglúígí", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vanillalol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wildriftph": "wildriftph", "lolph": "hlæjandiaðdeyja", "leagueoflegend": "leagueoflegend", "tốcchiến": "hrað<PERSON>ikur", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "aug<PERSON>ý<PERSON><PERSON><PERSON><PERSON><PERSON>", "lolzinho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspánn", "aatrox": "aatrox", "euw": "æj", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "tunglari", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milljón", "shaco": "shaco", "ligadaslegendas": "deildinhennargoðsagnanna", "gaminglol": "geiminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retróleikir", "scaryvideogames": "hræðilegirtölvuleikir", "videogamemaker": "t<PERSON>lvuleikjah<PERSON><PERSON><PERSON>ður", "megamanzero": "megamanzero", "videogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videosgame": "vídeóleikir", "professorlayton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "puffavinir", "farmingsimulator": "búskaparhermir", "robloxchile": "robloxísland", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxísland", "robloxdeutsch": "robloxislensku", "erlc": "erlc", "sanboxgames": "sandkassaleikir", "videogamelore": "tölvuleikjasögur", "rollerdrome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>v<PERSON><PERSON><PERSON>", "parasiteeve": "sníkjudýrakvöld", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "myrkurskógur", "dreamscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amordoce": "sætuelska", "videogiochi": "tölvuleikir", "theoldrepublic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videospiele": "tölvuleikir", "touhouproject": "touhouproject", "dreamcast": "draumaspá", "adventuregames": "ævin<PERSON><PERSON><PERSON>ei<PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "retróleikir", "retroarcade": "retr<PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "gamaltölvudót", "retrogaming": "retróleikir", "vintagegaming": "retrós<PERSON><PERSON><PERSON>", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "kjúklingasnakk", "injustice2": "óréttlæti2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "zennavídd", "beatmaniaiidx": "beatmaniaiidx", "steep": "<PERSON><PERSON><PERSON>", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "blockchaingaming", "medievil": "<PERSON><PERSON><PERSON><PERSON>", "consolegaming": "leikjatölvur", "konsolen": "konsólina", "outrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "blómstrarvesen", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "hryllingsspil", "monstergirlquest": "drekastelpuþraut", "supergiant": "risastór", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "bændaleikir", "juegosviejos": "gam<PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxleikir", "interactivefiction": "gagnvirkskáldskapur", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "elskendurálska", "visualnovel": "<PERSON><PERSON><PERSON><PERSON>", "visualnovels": "sjónrænarskáldsögur", "rgg": "rgg", "shadowolf": "skugg<PERSON><PERSON><PERSON><PERSON>", "tcrghost": "tcrdraug", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "spjallkatrín", "twilightprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandkassi", "aestheticgames": "f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thecrew2": "liðið2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smbz": "smbz", "lamento": "harma", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "laufblásararbyltingin", "wiiu": "wiiu", "leveldesign": "hönnunarborð", "starrail": "stjörnuspor", "keyblade": "lyklaverð", "aplaguetale": "plágusaga", "fnafsometimes": "fnafeinstöku", "novelasvisuales": "sjónrænarskáldsögur", "robloxbrasil": "robloxísland", "pacman": "pacman", "gameretro": "retróleikir", "videojuejos": "tölvuleikir", "videogamedates": "tölvuleikjadeit", "mycandylove": "mínsamelskaði", "megaten": "megaten", "mortalkombat11": "mortalkombatvíðbrigði11", "everskies": "everskies", "justcause3": "baraafþvíað3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "batman<PERSON><PERSON><PERSON>", "returnofreckoning": "endurkomabakslaga", "gamstergaming": "leikjagangster", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "geðveiktglæsihýsi", "crashracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "3dvettvangsleikir", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "gammalskólageim", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "söguleikir", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "afmortisghost", "tinybunny": "litlikanína", "retroarch": "retroarch", "powerup": "styrktars<PERSON>", "katanazero": "katanan<PERSON>l", "famicom": "famicom", "aventurasgraficas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quickflash": "<PERSON>ra<PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gachaleikir", "retroarcades": "gamlatölvuspilasalir", "f123": "f123", "wasteland": "eyðimörk", "powerwashsim": "kraftþvottahermir", "coralisland": "kórallueyja", "syberia3": "sýbería3", "grymmorpg": "grimmthlutverkaspil", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animeherjar2", "footballfusion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "geimlandnemi", "legomarvel": "<PERSON><PERSON><PERSON><PERSON>", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "hermir", "symulatory": "kepp<PERSON><PERSON><PERSON>", "speedrunner": "<PERSON>ra<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicx": "epicx", "superrobottaisen": "ofurmennavélmenni", "dcuo": "dcuo", "samandmax": "samogmax", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skylander": "himinkappinn", "boyfrienddungeon": "k<PERSON>rastafangehsi", "toontownrewritten": "teiknibærinnendurskrifaður", "simracing": "kappakstur", "simrace": "kappaksturshermun", "pvp": "pvp", "urbanchaos": "b<PERSON><PERSON>brölt", "heavenlybodies": "himneskirkroppar", "seum": "séf<PERSON>l", "partyvideogames": "partítölvuleikir", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "geimflughermir", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "leikjamyndbönd", "thewolfamongus": "úlfurinníokkurhópi", "truckingsimulator": "vörubílaleikur", "horizonworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handygame": "þægilegturleikur", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON>sögurogleikirinn", "oldschoolvideogames": "gamlaðgæðaleikir", "racingsimulator": "kappaksturshermirinn", "beemov": "bee<PERSON>v", "agentsofmayhem": "umboðsmennglundróðans", "songpop": "söngpop<PERSON>", "famitsu": "famitsu", "gatesofolympus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunternow": "veiðimanskrímslnúna", "rebelstar": "<PERSON><PERSON><PERSON><PERSON>", "indievideogaming": "indítölvuleikir", "indiegaming": "indieleikir", "indievideogames": "indítölvuleikir", "indievideogame": "indítölvuleikur", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermangeðveikinn", "bufffortress": "vöðvavígi", "unbeatable": "ósigrandi", "projectl": "projectl", "futureclubgames": "framtíðarklúbbsleikir", "mugman": "<PERSON><PERSON><PERSON><PERSON>", "insomniacgames": "svefnleysileikir", "supergiantgames": "risastóru<PERSON><PERSON>n", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "vísindagáttinnar", "backlog": "bakskrá", "gamebacklog": "óklaraðirleikir", "gamingbacklog": "leikjabunki", "personnagejeuxvidéos": "tölvuleikjapersónur", "achievementhunter": "trophæðiveiðimaður", "cityskylines": "borgarskýjalínur", "supermonkeyball": "ofurápakúla", "deponia": "deponia", "naughtydog": "óþekkturhun<PERSON>r", "beastlord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosretro": "retróleikir", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "dópamíngeymslur", "staxel": "staxel", "videogameost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsync": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "égelskakofxv", "arcanum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neoy2k": "neoy2k", "pcracing": "pctölvuleikjakeppni", "berserk": "ka<PERSON><PERSON>", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "sorglegtteiknimynd", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "<PERSON><PERSON><PERSON><PERSON>", "animewithplot": "animeímeðsöguþr<PERSON>ði", "pesci": "pesci", "retroanime": "retroanime", "animes": "öníme", "supersentai": "ofurhetjusveit", "samuraichamploo": "samúræsjamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "myrkrahöfðingi", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraix": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseríu1", "rapanime": "rapanime", "chargemanken": "<PERSON><PERSON>ð<PERSON>luk<PERSON><PERSON>", "animecover": "animeumslag", "thevisionofescaflowne": "sýnescaflowne", "slayers": "slæjarar", "tokyomajin": "tókýómajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "banananskur", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "skólastúlkuhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireforce": "eldsveit", "moriartythepatriot": "moriarty<PERSON><PERSON><PERSON><PERSON>", "futurediary": "framtíðardagbók", "fairytail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parasyte": "sník<PERSON><PERSON><PERSON><PERSON>", "punpun": "<PERSON><PERSON><PERSON><PERSON>", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "hafmeyjulag", "kamisamakiss": "kamisamakyss", "blmanga": "blmanga", "horrormanga": "hryllingsmanga", "romancemangas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "karneval": "karnival", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "geniusinc": "snillingurásamt", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "dj<PERSON>", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "visskvísitala", "sao": "sao", "blackclover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tokyoghoul": "tokyoghoul", "onepunchman": "einshöggsmaður", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "njósnafjölskyldan", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "undurseggforgang", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "kakegúrí", "dragonballsuper": "dragonballsuper", "hypnosismic": "seiðisshæfileikinn", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "skrímslastúlka", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sukasuka": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "englahjörtslög", "isekaianime": "isekaianime", "sagaoftanyatheevil": "ævintýritönjuhinnarillskeyttu", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "strákurinnogskrímslið", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "mazinger", "blackbuttler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "<PERSON><PERSON><PERSON><PERSON>", "elfenlied": "<PERSON><PERSON><PERSON><PERSON>", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "þjónustuvámpíra", "howtokeepamummy": "hvernigáaðhaldaímúmmíu", "fullmoonwosagashite": "fullmániosaga<PERSON>i", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "sættoggrótesk", "martialpeak": "kampsportartoppurinn", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinji": "shinji", "zerotwo": "nulltvö", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "skrímslastelpa", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "<PERSON><PERSON><PERSON>", "sailorsaturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dio": "dio", "sailorpluto": "sjómaðurp<PERSON><PERSON><PERSON><PERSON>", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chainsawman": "keð<PERSON><PERSON>gama<PERSON><PERSON><PERSON>", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "ka<PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON><PERSON><PERSON>", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "hryllinganime", "fruitsbasket": "ávaxtakarfa", "devilmancrybaby": "devilmancrybabygrátur", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "elskalíf", "sakuracardcaptor": "sakurakortasafnari", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "lovað<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermanga": "monstermanga", "yourlieinapril": "þitt<PERSON>fíaprí<PERSON>", "buggytheclown": "klú<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bokunohero": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "<PERSON><PERSON><PERSON><PERSON>", "deepseaprisoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "dauðamannaundraland", "bannafish": "bannaðurfiskur", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "pandorahjörtu", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "matarbardagar", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "tile<PERSON><PERSON>tíðar", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberser<PERSON>r", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "framtíðardagbók", "mahoutsukainoyome": "<PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuki": "yuki", "erased": "<PERSON><PERSON><PERSON>", "bluelock": "bl<PERSON><PERSON><PERSON>", "goblinslayer": "álfadráparinn", "detectiveconan": "leynilögganconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojotaktur", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slamdunk": "öruggursigur", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "skyggnst", "spyfamily": "njósnafjölskylda", "airgear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirl": "töfrastúlka", "thesevendeadlysins": "sjöhöfuðsyndirnir", "prisonschool": "fangelsisskóli", "thegodofhighschool": "æðiháskólagúðinn", "kissxsis": "kossxsystur", "grandblue": "st<PERSON><PERSON><PERSON><PERSON><PERSON>", "mydressupdarling": "klæðadúkkanmín", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeheimurinn", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpsycho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "rómantíkmanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeást", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentína", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodlad": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodbyeeri": "hæ<PERSON><PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON>", "adioseri": "hæ<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "stjörnurnarstillastsaman", "romanceanime": "rómantísktteiknimyndir", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "k<PERSON><PERSON>rjagal<PERSON><PERSON>", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "skrásetturagnarök", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "framhaldsskólidauðans", "germantechno": "þýsktteknó", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "algjörlega", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "mor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "da<PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japanskteiknimyndasögur", "animespace": "teiknimyndaheimur", "girlsundpanzer": "stel<PERSON>_og_skri<PERSON><PERSON>kar", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON>", "animedub": "animedúbbað", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indieanime": "indíanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animevísindaskáldskap", "ratman": "<PERSON><PERSON><PERSON>", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gashbell": "gashbell", "peachgirl": "ferskjustelpa", "cavalieridellozodiaco": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mechamusume": "vélstúlkur", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "<PERSON><PERSON><PERSON>bitsjklúbbur", "dragonquestdai": "dragonquestdai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "ljúffengtífan<PERSON><PERSON>", "manhviyaoi": "karleðhvívei", "recordofragnarok": "ragnaröksskrá", "funamusea": "skemmtisafn", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "hoppaíloafers", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "erbyggilegoferlið", "overgeared": "yfirgírað", "toriko": "<PERSON><PERSON>o", "ravemaster": "rævmeistarinn", "kkondae": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chobits": "chobits", "witchhatatelier": "nornas<PERSON>ð<PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunoljón", "kamen": "kamen", "mangaislife": "mangaerlífið", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>ð", "loscaballerosdelzodia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeshojo": "animestelpur", "reverseharem": "öfugtharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "frábærkennar<PERSON>ni<PERSON>", "gridman": "netkappinn", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "blóðíteiknaðarmyndum", "bloodc": "bl<PERSON>ðflok<PERSON><PERSON>", "talesofdemonsandgods": "sö<PERSON>rdjöflaogguða", "goreanime": "g<PERSON><PERSON><PERSON><PERSON>", "animegirls": "animestelpurnar", "sharingan": "<PERSON><PERSON>", "crowsxworst": "krákarxverstu", "splatteranime": "splatteranime", "splatter": "sletta", "risingoftheshieldhero": "kaldhæðnakappinn", "somalianime": "sómalísktanime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "ógeðfelldtekið", "animeyuri": "animejúrí", "animeespaña": "animeísland", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "b<PERSON>rn<PERSON>í<PERSON>um", "liarliar": "lygar<PERSON>gari", "supercampeones": "ofur<PERSON><PERSON><PERSON><PERSON>", "animeidols": "<PERSON><PERSON><PERSON><PERSON>", "isekaiwasmartphone": "ísekaivasmartphone", "midorinohibi": "gr<PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "töfrastúl<PERSON><PERSON>", "callofthenight": "kvöldkallið", "bakuganbrawler": "bakuganbardagamaður", "bakuganbrawlers": "bakuganbardagamenn", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "s<PERSON>ggagarður", "tsubasachronicle": "tsubasachronicle", "findermanga": "fin<PERSON><PERSON><PERSON>", "princessjellyfish": "marg<PERSON>tt<PERSON><PERSON><PERSON><PERSON>", "kuragehime": "hnetukóngúlókona", "paradisekiss": "parad<PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeheimurinn", "persocoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omniscientreadersview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecat": "animekötttur", "animerecommendations": "animeráðleggingar", "openinganime": "opnunaranime", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "míntáningsrómantískagrínmynd", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundamar", "voltesv": "voltesv", "giantrobots": "risa<PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "farsímabarðagavélmenniggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "vélmenni", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "booanime", "bleach": "<PERSON><PERSON><PERSON><PERSON>", "deathnote": "<PERSON><PERSON>ð<PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ævintýri", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "herinn_anime", "greenranger": "gr<PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "stjörnurefur", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecity": "animeborg", "animetamil": "animetamíl", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimon<PERSON><PERSON><PERSON><PERSON><PERSON>", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "mínhet<PERSON><PERSON><PERSON><PERSON><PERSON>", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "könnunarsveitin", "onepieceanime": "onepieceanime", "attaquedestitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theonepieceisreal": "onepiece<PERSON><PERSON><PERSON>", "revengers": "<PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "geðs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aonoexorcist": "aonoexorcist", "joyboyeffect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimonstory": "digimonsaga", "digimontamers": "digimontamers", "superjail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "málmheimsendir", "shinchan": "shinchan", "watamote": "vatamóte", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "slakaðáþvíkampa", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "norninfljúgandi", "wotakoi": "animeáhugafólk", "konanime": "konanime", "clannad": "clannad", "justbecause": "baraafþvíað", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "allraheilö<PERSON><PERSON><PERSON>", "recuentosdelavida": "lífssögur"}