{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cognitivefunctions": "fonksiyonênhişî", "psychology": "psîkolojî", "philosophy": "felsefe", "history": "<PERSON><PERSON><PERSON>", "physics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "science": "zanist", "culture": "çand", "languages": "z<PERSON>", "technology": "teknolojî", "memes": "m<PERSON><PERSON>", "mbtimemes": "mbtimemes", "astrologymemes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enneagrammemes": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showerthoughts": "ramanênserşokê", "funny": "bixenin", "videos": "v<PERSON><PERSON>o", "gadgets": "amûr", "politics": "siyaset", "relationshipadvice": "şîretênpeywendi<PERSON>ê", "lifeadvice": "jiyanêşîretan", "crypto": "kripto", "news": "nûçe", "worldnews": "nûç<PERSON>ê<PERSON><PERSON><PERSON><PERSON>", "archaeology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "learning": "fêrbûn", "debates": "nîqaş", "conspiracytheories": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "universe": "g<PERSON><PERSON><PERSON>", "meditation": "meditasyon", "mythology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "art": "huner", "crafts": "huner", "dance": "dans", "design": "<PERSON><PERSON><PERSON><PERSON>", "makeup": "makyaj", "beauty": "bedew", "fashion": "moda", "singing": "stran<PERSON><PERSON><PERSON><PERSON>", "writing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "photography": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosplay": "cosplay", "painting": "wênesazî", "drawing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "books": "pirtûk", "movies": "<PERSON><PERSON><PERSON>", "poetry": "hel<PERSON><PERSON>", "television": "televizyon", "filmmaking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animation": "animasyon", "anime": "anime", "scifi": "scifi", "fantasy": "xeyal", "documentaries": "belgefilm", "mystery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "comedy": "komidî", "crime": "sûc", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "tirs", "romance": "ev<PERSON>n", "realitytv": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "çalakî", "music": "muzîk", "blues": "blues", "classical": "klasîk", "country": "we<PERSON><PERSON>", "desi": "desi", "edm": "edm", "electronic": "elektronîk", "folk": "xelk", "funk": "funk", "hiphop": "hiphop", "house": "mal", "indie": "indie", "jazz": "caz", "kpop": "kpop", "latin": "latînî", "metal": "metal", "pop": "pop", "punk": "pank", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "tekno", "travel": "geşt", "concerts": "konser", "festivals": "festîval", "museums": "muze", "standup": "rawestin", "theater": "<PERSON><PERSON>", "outdoors": "derve", "gardening": "baxçevanî", "partying": "<PERSON><PERSON><PERSON><PERSON>", "gaming": "lîstik", "boardgames": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "zîndanû<PERSON>derha", "chess": "şetrenc", "fortnite": "fortnite", "leagueoflegends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "<PERSON><PERSON><PERSON>", "baking": "nanp<PERSON><PERSON><PERSON><PERSON>", "cooking": "çê<PERSON><PERSON>ê", "vegetarian": "<PERSON><PERSON><PERSON><PERSON>", "vegan": "vegan", "birds": "çûk", "cats": "pişî<PERSON>", "dogs": "seg", "fish": "baliq", "animals": "a<PERSON>l", "blacklivesmatter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "environmentalism": "jîngehparêzî", "feminism": "femînîzm", "humanrights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "lgbtqpiştgir", "stopasianhate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "piş<PERSON><PERSON><PERSON>êhevza<PERSON>n", "volunteering": "xêrxwazî", "sports": "spor", "badminton": "<PERSON><PERSON><PERSON><PERSON>", "baseball": "beysbol", "basketball": "basketbol", "boxing": "boks", "cricket": "kriket", "cycling": "çerxeswarî", "fitness": "fitness", "football": "futbol", "golf": "golf", "gym": "<PERSON><PERSON><PERSON><PERSON>", "gymnastics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hockey": "hokê", "martialarts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "netball", "pilates": "pilates", "pingpong": "p<PERSON><PERSON><PERSON><PERSON>", "running": "<PERSON><PERSON><PERSON>", "skateboarding": "skateboard", "skiing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "snowboarding": "berfêlîstik", "surfing": "<PERSON><PERSON><PERSON><PERSON>", "swimming": "a<PERSON><PERSON><PERSON><PERSON>", "tennis": "tenîs", "volleyball": "voleybol", "weightlifting": "kilohelanîn", "yoga": "yoga", "scubadiving": "cixînêavêkûr", "hiking": "hiking", "capricorn": "gakur", "aquarius": "şûbatî", "pisces": "pisces", "aries": "berek", "taurus": "ga", "gemini": "gemini", "cancer": "pence<PERSON><PERSON><PERSON>", "leo": "<PERSON><PERSON><PERSON>", "virgo": "<PERSON><PERSON><PERSON>", "libra": "<PERSON><PERSON><PERSON>", "scorpio": "akrep", "sagittarius": "<PERSON><PERSON><PERSON><PERSON>", "shortterm": "<PERSON><PERSON><PERSON>", "casual": "nefermî", "longtermrelationship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "single": "tenê", "polyamory": "pirhezkî", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gey", "lesbian": "lezbiyen", "bisexual": "dubendî", "pansexual": "pan<PERSON><PERSON><PERSON>", "asexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "seg<PERSON>n<PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "questhikmdar", "soulreaver": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "<PERSON><PERSON><PERSON><PERSON>", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "rojavaçûnêdeajot", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "şerêncivatan", "openworld": "cîhanavekirî", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "zinxûrdanê", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "balafirgeş<PERSON>", "lordsoftherealm2": "mîrênqadê2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "reng<PERSON><PERSON>", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "lîstikênkûrvekûr", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "witcher": "witcher", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "giyanê<PERSON><PERSON>", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "encam", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "mod<PERSON>rin", "charactercreation": "a<PERSON>randinakarek<PERSON><PERSON>", "immersive": "navxweyî", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motîvasyonamirî", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "evînê<PERSON><PERSON>dî", "otomegames": "lîst<PERSON>ênot<PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinayademê", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "pîvan20", "gaslands": "erdêngazê", "pathfinder": "<PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2emçapa", "shadowrun": "baziyasîber", "bloodontheclocktower": "xwînlisersaetêkaşkestî", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "barkêşîyêbezê", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "carekê", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "serwer", "yourturntodie": "no<PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpgtirsnak", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "rêw<PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "zindanatarî", "eclipsephase": "qonaxastêrkan", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "girêdanaîshaq", "diabloimmortal": "diabloimmortal", "dynastywarriors": "şervanêndînamîkê", "skullgirls": "skullgirls", "nightcity": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "şerêdî<PERSON><PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliancê2", "neverwinter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "road96": "riya96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "lîstikên<PERSON>uel<PERSON>", "gothamknights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "cîhanênjirbîrkirî", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "baj<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childoflight": "zarok<PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "monsterxwedîker", "ecopunk": "ekopank", "vermintide2": "bermayîşeytan2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "textêşikestî", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "siyapunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "razahogwart", "deltagreen": "deltagreen", "diablo": "<PERSON><PERSON><PERSON><PERSON>", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goldensun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "xwedayêgunehêyekemîn", "bladesinthedark": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "sayberpank", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunksor", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "r<PERSON><PERSON>etî", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "şeytanêzindîmayî", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "xwedayîtî", "pf2": "pf2", "farmrpg": "lîstikarpgcotkariyê", "oldworldblues": "xemêncihanêkevn", "adventurequest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "lîstikênroleplayê", "roleplayinggames": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "roj<PERSON><PERSON>", "talesofsymphonia": "çîrokênsenfoniya", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "bajarêşikestî", "myfarog": "faroga_min", "sacredunderworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chainedechoes": "zincîradengvedan", "darksoul": "giyanê<PERSON><PERSON>", "soulslikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "othercide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mountandblade": "çekûşûr", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "<PERSON><PERSON><PERSON>", "pillarsofeternity": "stûnênherheyînê", "palladiumrpg": "palladiumrpg", "rifts": "<PERSON><PERSON><PERSON>", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "efsaneyasorete", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampîromaskekirî", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "gurepêzîapokalî<PERSON>ê", "aveyond": "aveyond", "littlewood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childrenofmorta": "zarokên<PERSON><PERSON>", "engineheart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fable3": "çîrok3", "fablethelostchapter": "çîrokabeşawenda", "hiveswap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edenherheyî", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "zîndanatarî", "juegosrpg": "lîstikênrpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "<PERSON><PERSON><PERSON><PERSON>lk<PERSON><PERSON>", "harvestella": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "asmanênarcadia", "shadowhearts": "dil<PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "xwînapenî", "breathoffire4": "bêhnaagir4", "mother3": "dêya3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "lîstikalîstikrola", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>baleyê", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "e<PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "dembazî", "cocttrpg": "cocttrpg", "huntroyale": "nêç<PERSON>raşahan<PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "forumarpg", "shadowheartscovenant": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "padîşahiyatê", "awplanet": "cîhanaaw", "theworldendswithyou": "dinyabitêdestu", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "firoş<PERSON><PERSON><PERSON><PERSON><PERSON>", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON>", "blackbook": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skychildrenoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "rê<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "castlecrashers": "şerkerênkele<PERSON>ê", "gothicgame": "lîstikagotîk", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "lîstikrpg", "prophunt": "nêçîrapêxember", "starrails": "<PERSON>t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "lîstikarpgserxwebûn", "pointandclick": "nîşanbideûbitikîne", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "veqetanayî", "freeside": "alîyêazad", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "piştîsayberpank", "deathroadtocanada": "rêyamirinêberbicanada", "palladium": "paladyûm", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "erdnîgarîserdestî", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "herelem<PERSON><PERSON><PERSON>bi<PERSON><PERSON><PERSON>", "souleater": "<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "lîstikênenecimarî", "tacticalrpg": "rpgtaktîkî", "mahoyo": "mahoyo", "animegames": "lîstikênan<PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "ahengaherhey<PERSON>", "princessconnect": "princessconnect", "hexenzirkel": "hexenzirkel", "cristales": "kr<PERSON><PERSON>", "vcs": "vc", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valoranthindî", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "lîstikênelektronîk", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "lîgaxewînb<PERSON>nan", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "lîstik", "overwatchleague": "lîgaoverwatch", "cybersport": "lîstikênelektronîk", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "pêşbirkê<PERSON><PERSON><PERSON>v", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantpêşbazî", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "nîv<PERSON><PERSON><PERSON>", "left4dead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead2": "left4dead2", "valve": "valv", "portal": "<PERSON><PERSON>", "teamfortress2": "teamfortress2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "simulatorap<PERSON>z", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transformice": "transformice", "justshapesandbeats": "tenêşiklûdeng", "battlefield4": "şerêmeydanê4", "nightinthewoods": "şevadaştan", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvania", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "navstêrkan", "helltaker": "dojehgi<PERSON>", "inscryption": "şîfrek<PERSON>n", "7d2d": "7h2h", "deadcells": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "kelehadûcan", "foxhole": "stargeh", "stray": "bêx<PERSON><PERSON>", "battlefield": "şerdec<PERSON><PERSON>", "battlefield1": "meydanaşer1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "çavb", "blackdesert": "çolareş", "tabletopsimulator": "lîstikasi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partyhard": "partykebiheq", "hardspaceshipbreaker": "keştiyanş<PERSON><PERSON><PERSON>", "hades": "hades", "gunsmith": "çekçêker", "okami": "<PERSON>ami", "trappedwithjester": "girt<PERSON><PERSON><PERSON><PERSON><PERSON>", "dinkum": "rast", "predecessor": "<PERSON><PERSON>ş<PERSON><PERSON><PERSON>", "rainworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolonîsîm", "noita": "noita", "dawnofwar": "destpê<PERSON><PERSON><PERSON>", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "tar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "giya<PERSON><PERSON>", "datingsims": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "bajarênû", "citiesskylines": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "def<PERSON><PERSON><PERSON>", "kenopsia": "kenopsiya", "virtualkenopsia": "kenopsiyavîrtuel", "snowrunner": "<PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "pirtûkxaneyaxirabûnê", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenporakîgiyan", "placidplasticduck": "qazaplastîkîaram", "battlebit": "şerbit", "ultimatechickenhorse": "hespapûkêherîdawî", "dialtown": "bajarêax<PERSON>inê", "smileforme": "jiminlêbikene", "catnight": "şevamişk", "supermeatboy": "supermeatboy", "tinnybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygrove": "cozygrove", "doom": "qiya<PERSON>", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "<PERSON><PERSON><PERSON>", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "<PERSON><PERSON><PERSON><PERSON>", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "lîstikênfar<PERSON>ry", "paladins": "paladins", "earthdefenseforce": "<PERSON><PERSON>zapar<PERSON><PERSON><PERSON>", "huntshowdown": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakuştin", "joinsquad": "beşdarîtîmêbibe", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "şerêrêgemar", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "kujer3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "beş2", "killzone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosscode": "xaçkod", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare": "şerêmodern", "neonabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planetside2": "planetside2", "mechwarrior": "şervanêmek", "boarderlands": "<PERSON><PERSON><PERSON>", "owerwatch": "ower<PERSON>", "rtype": "cureyenivîsê", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "back4blood": "vegerexwînê", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "kujer", "masseffect": "masseffect", "systemshock": "şokapergehê", "valkyriachronicles": "valkyriachronicles", "specopstheline": "xetaoperasyonêntaybet", "killingfloor2": "killingfloor2", "cavestory": "çîrokakêşkeftê", "doometernal": "<PERSON><PERSON><PERSON><PERSON><PERSON>demî", "centuryageofashes": "sedsalaserketirê", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "azarafantom", "warface": "şerûrû", "crossfire": "<PERSON><PERSON><PERSON><PERSON>", "atomicheart": "dilêatomî", "blackops3": "blackops3", "vampiresurvivors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freedoom": "azadî", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "kujer", "tinytina": "tinatiçûk", "gamepubg": "lîstikpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "lîstikênfps", "convertstrike": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warzone2": "şerê2", "shatterline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackopszombies": "zombiyênblackops", "bloodymess": "tevliheviyêşerm", "republiccommando": "republikkommando", "elitedangerous": "elitedangerousê", "soldat": "esker", "groundbranch": "l<PERSON><PERSON><PERSON>", "squad": "heval", "destiny1": "destîn1", "gamingfps": "lîstikênfps", "redfall": "şemşûr", "pubggirl": "keçapubg", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON>", "farlight": "ronakîyadûr", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "coreduzirxkirî", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "roja_mûçê2", "cs16": "cs16", "pubgindonesia": "pubgîndonezya", "pubgukraine": "pubg<PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgşêtî", "empyrion": "empyrion", "pubgczech": "pubgçekî", "titanfall2": "titanfall2", "soapcod": "sab<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostcod": "xeyalcod", "csplay": "csplay", "unrealtournament": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "lîstikcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "destmalpê", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remnant": "be<PERSON><PERSON><PERSON>", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "vegerin", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "zilamêsîber", "quake2": "erdhej2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "<PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "xwar", "conqueronline": "conqueronline", "dauntless": "b<PERSON><PERSON><PERSON>", "warships": "ke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "firînabilindbûn", "recroom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofruneterra": "efsaneyênruneterra", "pso2": "pso2", "myster": "sir<PERSON>", "phantasystaronline2": "phantasystaronline2", "maidenless": "bêkeçik", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "cîhanatankan", "crossout": "jêbike", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "lî<PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON><PERSON>", "newworld": "c<PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "lî<PERSON><PERSON><PERSON><PERSON>beş", "pirate101": "korsan101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dchat": "3dchat", "nostale": "notale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "xwelîyaafirandinê", "riotmmo": "riotmmo", "silkroad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spiralknights": "<PERSON><PERSON><PERSON>ênspîral", "mulegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "evony": "evony", "dragonsprophet": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grymmo": "grymmo", "warmane": "ger<PERSON><PERSON>", "multijugador": "pirlîstik", "angelsonline": "milya<PERSON>ên<PERSON><PERSON><PERSON><PERSON>ê", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarskomart<PERSON><PERSON><PERSON><PERSON><PERSON>", "grandfantasia": "grandfantazî", "blueprotocol": "protok<PERSON><PERSON><PERSON><PERSON>", "perfectworld": "cîhanapêrfekt", "riseonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "corepunk": "corepunk", "adventurequestworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flyforfun": "bifirejiboşahî", "animaljam": "animaljam", "kingdomofloathing": "keyîpad<PERSON>ş<PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "bajarêlehengam", "mortalkombat": "mortalkombat", "streetfighter": "şerkerêkolanan", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "şervanêvîrtual", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "şerkerêşah", "likeadragon": "we<PERSON><PERSON><PERSON>", "retrofightinggames": "lîstik<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blasphemous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "<PERSON><PERSON><PERSON><PERSON>", "mugen": "mugen", "warofthemonsters": "şerêcinawiran", "jogosdeluta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "robotê<PERSON>iber", "armoredwarriors": "şervanênzirxpoş", "finalfight": "şerêdawî", "poweredgear": "gearêbihez", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "î<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "xeyalbaz", "chivalry2": "rûmetdarî2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "dûmahiyahollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "lîstikatasilk", "silksongnews": "nûçeyênsilksong", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON>", "typelumina": "tîpal<PERSON>îna", "evolutiontournament": "pêşbaziyapêşketinê", "evomoment": "demb<PERSON>yer<PERSON>yikê", "lollipopchainsaw": "lolîpopzincîrmotorî", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "çîrokênberseria", "bloodborne": "xwînzad", "horizon": "<PERSON><PERSON><PERSON>", "pathofexile": "rêyasirgûn<PERSON>", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uncharted": "nexşekir<PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "meyadawî", "infamous": "navdar", "playstationbuddies": "hevalênplaystation", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "kompanyayarêzber", "aisomniumfiles": "dosyênxewnaai", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "kesk", "trove": "depo", "detroitbecomehuman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "hetaspêdê", "touristtrophy": "xelatagirokê", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashteamracing": "crashteamracing", "fivepd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON>dikaredîgirî", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playingstation", "samuraiwarriors": "şervanênsamûrayî", "psvr2": "psvr2", "thelastguardian": "paşî<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "ş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "dilênsiyê2peyman", "pcsx2": "pcsx2", "lastguardian": "parastvanêdawî", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "partîbaz", "warharmmer40k": "warhammer40k", "fightnightchampion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "<PERSON><PERSON><PERSON><PERSON>", "mhw": "mhw", "princeofpersia": "<PERSON><PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "<PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "nanx<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "stêrbendî", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "xanîjiveker", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable2": "çîrok2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "rûpelênepak", "skycotl": "asmanêmeks<PERSON><PERSON><PERSON>", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "biçûkxanimtalîreş", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "proma_cinî", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motor", "outerwilds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kultêbarxê", "duckgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "xweparabîlistanley", "towerunite": "towerunite", "occulto": "<PERSON><PERSON><PERSON><PERSON>", "longdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "satisfactory": "<PERSON><PERSON><PERSON>", "pluviophile": "<PERSON><PERSON><PERSON><PERSON>", "underearth": "bin<PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "kûbetatarî", "pizzatower": "pizzatower", "indiegame": "lîstikaserbixwe", "itchio": "itchio", "golfit": "golfit", "truthordare": "rastîyananivşîn", "game": "lîstik", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolin", "hulahoop": "ho<PERSON><PERSON><PERSON>", "dare": "w<PERSON>rek", "scavengerhunt": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "lîstikênhewşê", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "rastannaxwest", "beerpong": "beerpong", "dicegoblin": "zarêperî", "cosygames": "lîstikênxweş", "datinggames": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "lîstikbelaş", "drinkinggames": "lîstikên<PERSON><PERSON><PERSON>ê", "sodoku": "sodoku", "juegos": "lîstik", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "lîstikênsîmulasyonê", "wordgames": "lîstikênpeyvan", "jeuxdemots": "peyvanelîstik", "juegosdepalabras": "lîstikênpeyvan", "letsplayagame": "em<PERSON><PERSON><PERSON><PERSON>", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "oyun", "interactivegames": "lîstikêninteraktîf", "amtgard": "amtgard", "staringcontests": "pêşbirkênçavlêxistinê", "spiele": "spiele", "giochi": "lîstik", "geoguessr": "geoguessr", "iphonegames": "lîstikênîphone", "boogames": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hideandseek": "veşartokî", "hopscotch": "lorlor", "arcadegames": "lîstikênarkadê", "yakuzagames": "lîstikênyakuza", "classicgame": "lîstikahêklasîk", "mindgames": "lîstikênhişî", "guessthelyric": "ji_gotinên_texmîn_bike", "galagames": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancegame": "lî<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4xgames": "lîstikên4x", "gamefi": "gamefi", "jeuxdarcades": "lîstikênarkadê", "tabletopgames": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "lîstik90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "lîstikênpêşbaziyê", "ets2": "ets2", "realvsfake": "rastîvssaxte", "playgames": "yarîyanbike", "gameonline": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegames": "lîstikê<PERSON>lay<PERSON>", "jogosonline": "lîstikênonline", "writtenroleplay": "rolanivîsandî", "playaballgame": "lîstikêbikebilîze", "pictionary": "pictionary", "coopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "highscore": "p<PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "lîstikênburgeran", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwversiyonareş", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "gioco", "managementgame": "lîstikairêvebirî<PERSON>ê", "hiddenobjectgame": "lîstikatiştênveşartî", "roolipelit": "l<PERSON>st<PERSON><PERSON><PERSON><PERSON>", "formula1game": "lîstikformula1", "citybuilder": "bajarêava", "drdriving": "drdriving", "juegosarcade": "lîstikênarkade", "memorygames": "lîstikênbîranînê", "vulkan": "vulkan", "actiongames": "lîstikênçalakiyê", "blowgames": "lîstikênpûfkirinê", "pinballmachines": "makîneyênpinballê", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "hevalberhemlikolanê", "perguntados": "p<PERSON><PERSON>", "gameo": "lîstik", "lasergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "lîstikênimessageyê", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "valahîtijîbike", "jeuxpc": "lîstikênpc", "rétrogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "logicgames": "lîstikênmentiqî", "japangame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "lîstikaxweşbûnê", "subwaysurf": "metroyêserfkirin", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "lîstikênderketinê", "5vs5": "5li5", "rolgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dashiegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameandkill": "lîstik<PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "lîstik<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "lîstikfps", "textbasedgames": "lîstikêntekstî", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgame": "lîst<PERSON><PERSON>z", "lawngames": "lîstikênbexçe", "fliperama": "flîperama", "heroclix": "heroclix", "tablesoccer": "maseyafutbolê", "tischfußball": "tischfußball", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "lîstikênsivik", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "lîstikê<PERSON>rev<PERSON>n<PERSON>", "thiefgameseries": "dîzanbazî", "cranegames": "lîstikênv<PERSON>nç", "játék": "lîstik", "bordfodbold": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosorte": "jogosorte", "mage": "<PERSON><PERSON><PERSON><PERSON>", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "randomizer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammî", "gamespc": "lîstikênpc", "socialdeductiongames": "lîstikênvedîtina<PERSON>î", "dominos": "dominos", "domino": "domino", "isometricgames": "lîstikênizometrîk", "goodoldgames": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "rastîûwêrekî", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "lîstikênv<PERSON>rtuel", "romhack": "romhack", "f2pgamer": "lîstik<PERSON><PERSON>ş", "free2play": "belaşebileyîz", "fantasygame": "lîst<PERSON><PERSON><PERSON>", "gryonline": "gryliserxetê", "driftgame": "lîstikadriftê", "gamesotomes": "lîst<PERSON>ênot<PERSON>", "halotvseriesandgames": "halotvseriesûlîstikan", "mushroomoasis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anythingwithanengine": "hertiştekmotorê", "everywheregame": "lîst<PERSON><PERSON><PERSON>derê", "swordandsorcery": "şûrûsêrbazî", "goodgamegiving": "lîstikxweşbexşîn", "jugamos": "<PERSON><PERSON><PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "lîstikênlabsifir", "grykomputerowe": "lîst<PERSON><PERSON>nkomp<PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "<PERSON><PERSON>lîstik", "jeuxderythmes": "lîstikênrîtmê", "minaturegames": "lîstikênpiçûk", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "xweşewiştîlîstik", "gamemodding": "modagi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crimegames": "lîstikênsûc", "dobbelspellen": "lîstikê<PERSON><PERSON>", "spelletjes": "lîstik", "spacenerf": "spacenerf", "charades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "singleplayer": "yalîstikêtekesî", "coopgame": "lî<PERSON><PERSON><PERSON><PERSON>beş", "gamed": "lîstik", "forzahorizon": "forzahorizon", "nexus": "gir<PERSON><PERSON>", "geforcenow": "geforcenow", "maingame": "lîstikaserekî", "kingdiscord": "şahêdiscord", "scrabble": "scrabble", "schach": "şetrenc", "shogi": "şogî", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "tavlê", "onitama": "onitama", "pandemiclegacy": "m<PERSON>rasapandemiyê", "camelup": "hêş<PERSON>rêxwebibin", "monopolygame": "lîstikaserdestiyê", "brettspiele": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "lîst<PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "lîstikêncivatê", "planszowe": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "risiko": "met<PERSON><PERSON>", "permainanpapan": "lîstikêntexteyê", "zombicide": "zombîkuj", "tabletop": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baduk": "xirab", "bloodbowl": "xwînêgolikê", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "lîst<PERSON><PERSON><PERSON><PERSON>", "connectfour": "çarlihevgir<PERSON><PERSON>", "heroquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidatavolo": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "karom", "tablegames": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "zarq<PERSON><PERSON><PERSON>n", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "lîstikêncivakê", "deskgames": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "krîzamarvelprotokol", "cosmicencounter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "creationludique": "lîstikê<PERSON><PERSON><PERSON>î", "tabletoproleplay": "lîstik<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "lîstikênkartonî", "eldritchhorror": "tirs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "lîstikêntab<PERSON>ê", "infinitythegame": "lîstikabêdawî", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "pêşketinûpaşvekişîn", "társas": "civakî", "juegodemesa": "lîstikamêzê", "planszówki": "planşow<PERSON>", "rednecklife": "jiyanagundî", "boardom": "<PERSON><PERSON><PERSON><PERSON>", "applestoapples": "sê<PERSON><PERSON><PERSON>v", "jeudesociété": "lîstikêncivakî", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "<PERSON><PERSON><PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "lîstikêncivakî", "twilightimperium": "împeratoriyaxemilê", "horseopoly": "horseopoly", "deckbuilding": "<PERSON><PERSON><PERSON>kar<PERSON><PERSON>", "mansionsofmadness": "koşkênşêtiyê", "gomoku": "gomoku", "giochidatavola": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "sîbeyênbrimstone", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcaby": "damepolonî", "táblajátékok": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "keştîyaşer", "tickettoride": "bilêtasiwariyê", "deskovehry": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON>", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "lîstikêntexteyê", "stolníhry": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "lîstikêncivakî", "gesellschaftsspiele": "lîstikêncivakî", "starwarslegion": "starwarslegion", "gochess": "goşetrenc", "weiqi": "weiqi", "jeuxdesocietes": "jeuxdes<PERSON>iet<PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "şerîstangeh", "arksurvivalevolved": "arksurvivalevolved", "dayz": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityv": "identityv", "theisle": "girav", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "bangawazcthulhu", "bendyandtheinkmachine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "conanexiles": "conanexiles", "eft": "eft", "amongus": "navmeye", "eco": "eko", "monkeyisland": "giravabizinê", "valheim": "valheim", "planetcrafter": "planetçêker", "daysgone": "rojênçûn", "fobia": "fobî", "witchit": "efsûnêbike", "pathologic": "nexweşîya_ruhî", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7ro<PERSON>", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "erd<PERSON><PERSON><PERSON>", "stateofdecay2": "rewşapûçînê2", "vrising": "vrising", "madfather": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarve": "<PERSON><PERSON><PERSON><PERSON>nebe", "eternalreturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "lîstikênferksiyonê", "hexen": "<PERSON><PERSON><PERSON><PERSON>", "theevilwithin": "şeytanêhundirî", "realrac": "realrac", "thebackrooms": "odeyênpaşiyê", "backrooms": "ode<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiressmp": "empiressmp", "blockstory": "çîrokablok", "thequarry": "kanî", "tlou": "tlou", "dyinglight": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "lîstika<PERSON><PERSON>î<PERSON>", "wehappyfew": "emhindikêndilxweş", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "lî<PERSON><PERSON><PERSON><PERSON>şamayînê", "vintagestory": "çîrokaberê", "arksurvival": "maneriz<PERSON>î", "barotrauma": "bar<PERSON><PERSON><PERSON>", "breathedge": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "westlendrazgarî", "beastsofbermuda": "ajandêbermû<PERSON>ê", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "tren<PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "jiyanapiştalîstikê", "survivalgames": "lîstikênrizgariyê", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "ş<PERSON><PERSON><PERSON>v", "scpfoundation": "scpfoundation", "greenproject": "projeyekesk", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "kelek", "rdo": "rdo", "greenhell": "dojehakesk", "residentevil5": "residentevil5", "deadpoly": "polyê<PERSON><PERSON>", "residentevil8": "residentevil8", "onironauta": "onîronauta", "granny": "<PERSON><PERSON><PERSON>", "littlenightmares2": "kabûsênşevêbiçûk2", "signalis": "sî<PERSON><PERSON>", "amandatheadventurer": "amanda<PERSON><PERSON>", "sonsoftheforest": "kurêndaristan<PERSON>", "rustvideogame": "lîstikavîdeorust", "outlasttrials": "ceribandinênberdewam", "alienisolation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "be<PERSON>g", "7day2die": "7ro<PERSON>2<PERSON><PERSON>", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "sopravvivenza", "propnight": "şevaprop", "deadisland2": "mirîyêgiravê2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "rojênreştêhilweşînê", "soma": "soma", "fearandhunger": "tirsûbirçîbûn", "stalkercieńczarnobyla": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeafter": "piştîjiyanê", "ageofdarkness": "serde<PERSON><PERSON><PERSON>", "clocktower3": "danziya3", "aloneinthedark": "bitenêlistariyê", "medievaldynasty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "lîstikprojenimbus", "eternights": "şevênherheyî", "craftopia": "honeristan", "theoutlasttrials": "ceribandinêneberdewamîyê", "bunker": "bunker", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "kuştinênfermî", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kdildar", "wh40": "wh40", "warhammer40klove": "warhammer40kevîn", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "evînîyaxweşkiyênza<PERSON>h", "ilovevindicare": "ezjivindicarehezkm", "iloveassasinorum": "ezjiassasinoranhezkikim", "templovenenum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "herduşêyek", "wingspan": "baskên_firînê", "terraformingmars": "terraformkirinamerîxê", "heroesofmightandmagic": "qehremanênhêzûefsûnê", "btd6": "btd6", "supremecommander": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofmythology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "args": "args", "rime": "rime", "planetzoo": "ajalanzoo", "outpost2": "outpost2", "banished": "<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "şaristanî6", "warcraft2": "warcraft2", "commandandconquer": "fermanûkontrol", "warcraft3": "warcraft3", "eternalwar": "şerêherheyî", "strategygames": "lîstikênstratejiyê", "anno2070": "anno2070", "civilizationgame": "lîstikamedeniyetê", "civilization4": "şaristanî4", "factorio": "factorio", "dungeondraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spore": "spor", "totalwar": "şerêgiştî", "travian": "travian", "forts": "qele", "goodcompany": "hevalbendîbaş", "civ": "şaristanî", "homeworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heidentum": "olperest<PERSON>", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "jibo<PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "stratejiyademgirtî", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "<PERSON>îşahiyaduwtac", "eu4": "eu4", "vainglory": "xwepesindî", "ww40k": "ww40k", "godhood": "xwedatî", "anno": "sal", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "<PERSON><PERSON><PERSON>", "davesfunalgebraclass": "dersaeljebrayaxweşadave", "plagueinc": "nexweşiyaserdemê", "theorycraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "şaristanî3", "4inarow": "4rêz<PERSON>", "crusaderkings3": "crusaderkings3", "heroes3": "qehremanên3", "advancewars": "şerênpêşketî", "ageofempires2": "temenêîmperatoriyên2", "disciples2": "şagirt2", "plantsvszombies": "nebatlidijîzombî", "giochidistrategia": "lîstikênstratejiyê", "stratejioyunları": "lîstikênstratejîk", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "şahêdîne<PERSON>", "worldconquest": "feth<PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "dil<PERSON><PERSON><PERSON>n4", "companyofheroes": "hevalbendiyalehengî", "battleforwesnoth": "şerêwesnothê", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "qazqazmîrîşk", "phobies": "fobî", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "lîstik<PERSON>lash<PERSON><PERSON>", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "nobet<PERSON>", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON>ş<PERSON>ênx<PERSON>çparêz", "cultris2": "cultris2", "spellcraft": "sihirbazî", "starwarsempireatwar": "starwarsşerêîmperatoriyê", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "stratej<PERSON>", "popfulmail": "postagiranîşandar", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "bernameyadev<PERSON><PERSON><PERSON><PERSON>", "transporttycoon": "karb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unrailed": "jirelûnederket", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "şahîyên<PERSON><PERSON>", "galaxylife": "jiyanagalaksiyê", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespire", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "pêw<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "<PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "alicedîndarbûnavegerîne", "darkhorseanthology": "antolojiyahespêtarî", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saiko": "saiko", "fatalframe": "çarçoveyadojehê", "littlenightmares": "kabûsênşevêbiçûk", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "malê<PERSON><PERSON><PERSON>", "deadisland": "giravamirî", "litlemissfortune": "litlemissfortune", "projectzero": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horory": "tirsnak", "jogosterror": "jogosterror", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "silavecîn2", "gamingdbd": "gamingdbd", "thecatlady": "jinakew", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kartênlijdijî<PERSON><PERSON>hi<PERSON>", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "navênkodî", "dixit": "<PERSON><PERSON><PERSON>", "bicyclecards": "kart<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "e<PERSON><PERSON>yarune<PERSON>", "solitaire": "solitaire", "poker": "poker", "hearthstone": "<PERSON><PERSON><PERSON><PERSON>", "uno": "yek", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "miftasaz", "cardtricks": "xapandinêkartan", "playingcards": "pêxistinalîstikan", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kartênbazirganiyê", "pokemoncards": "poke<PERSON><PERSON>t", "fleshandbloodtcg": "tcgxwînûgoşt", "sportscards": "kartênsporê", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "ma<PERSON>e", "warcry": "<PERSON><PERSON><PERSON><PERSON>_<PERSON>er", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hanafuda": "hana<PERSON>da", "theresistance": "be<PERSON><PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "kart<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "sêrbazêtarî", "blueeyeswhitedragon": "çavşînajdihaspî", "yugiohgoat": "yugiohgoat", "briscas": "<PERSON><PERSON><PERSON>", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobil", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "lîstikênde<PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carteado": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "žolíky": "kart", "facecard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfight": "şerêkartan", "biriba": "biriba", "deckbuilders": "lêkerêndekan", "marvelchampions": "şampiyonê<PERSON>mar<PERSON>", "magiccartas": "sihirketanmagîk", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "yekornêneberdest", "cyberse": "sayberseks", "classicarcadegames": "lîstikênarkadêyênklasîk", "osu": "osu", "gitadora": "gitadora", "dancegames": "l<PERSON>st<PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "şevareynîcumeyêkêfê", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "<PERSON>je<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "gita<PERSON><PERSON>o", "clonehero": "clonehero", "justdance": "tenêdanse", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "dansanavendî", "rhythmgamer": "lîstik<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "lîstikênrîtmênpuanênbilind", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "govendaagirûcemidê", "auditiononline": "auditiononline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "lîstikênrîtmê", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "doktorê<PERSON>îtmê", "cubing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordle": "wordle", "teniz": "tenîs", "puzzlegames": "lîstikênpuzzle", "spotit": "bib<PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blokdoku", "logicpuzzles": "çareseriyênmentiqî", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikscube": "kûbîkrubîk", "crossword": "fêrbazî", "motscroisés": "motscroisés", "krzyżówki": "xaçepirs", "nonogram": "nonogram", "bookworm": "p<PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "çareseriyêntabloyî", "indovinello": "metel", "riddle": "<PERSON><PERSON><PERSON>", "riddles": "metelok", "rompecabezas": "sereşkestî", "tekateki": "tekateki", "inside": "navxweyî", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "lîst<PERSON><PERSON><PERSON><PERSON>", "minesweeper": "mîn<PERSON>ş", "puzzleanddragons": "puzzleûejderha", "crosswordpuzzles": "çarepeyv", "kurushi": "k<PERSON>hi", "gardenscapesgame": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "lîstikênodeyênrevînê", "escapegame": "lîstikaderketinê", "3dpuzzle": "puzzleya3d", "homescapesgame": "lîstikahomescapes", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "tê<PERSON><PERSON><PERSON>", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "çîrokênmetel", "fishdom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theimpossiblequiz": "testa_nemumkin", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "puzzle3lihevhatî", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "ec<PERSON>b", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "kubarubîk", "yapboz": "yapboz", "thetalosprinciple": "prensîbathalos", "homescapes": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "minpirsbike", "tycoongames": "lîstikêntycoon", "cubosderubik": "kûbikênrûbîk", "cruciverba": "xaçepirs", "ciphers": "şîfre", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "çareserkirinêpuzzle", "turnipboy": "kurîkgundî", "adivinanzashot": "texmînashot", "nobodies": "kesnebin", "guessing": "texmînkirin", "nonograms": "nonogram", "kostkirubika": "kostkirubika", "crypticcrosswords": "xaçepirsgirênepenî", "syberia2": "sîbîrya2", "puzzlehunt": "lê<PERSON>î<PERSON>uzzle", "puzzlehunts": "lîstikênraz<PERSON>", "catcrime": "sûcê<PERSON>î<PERSON>", "quebracabeça": "<PERSON><PERSON><PERSON><PERSON>", "hlavolamy": "mozîk", "poptropica": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thelastcampfire": "agirêdaw<PERSON>", "autodefinidos": "xwebîdiyarkirî", "picopark": "picopark", "wandersong": "geştûstran", "carto": "carto", "untitledgoosegame": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "limbo": "nîvco", "rubiks": "<PERSON><PERSON><PERSON>", "maze": "maze", "tinykin": "piçûçik", "rubikovakostka": "rubikovakostka", "speedcube": "kûbîkbilez", "pieces": "per<PERSON>e", "portalgame": "lîstikaportal", "bilmece": "bilmece", "puzzelen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "mamikan", "cubomagico": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopoly": "monopolî", "futurefight": "şerêpêşerojê", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "gûr<PERSON>ten<PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ensemblestars": "stêrênensemblê", "asphalt9": "asfalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvival": "rew<PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "b<PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "şanoyarengîn", "bloonstowerdefense": "bloonsberojbirca", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "şerêdokkanê", "fategrandorder": "fategrandorder", "hyperfront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "knightrun": "bazîknightê", "fireemblemheroes": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "honkaiimpact": "honkaiimpact", "soccerbattle": "şerêfutbolê", "a3": "a3", "phonegames": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guardiantales": "çîrokênparêzvan", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "taktîkûl", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftsman": "honerker", "supersus": "pir<PERSON><PERSON><PERSON>", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "şerênivînan", "freefire": "freefire", "mobilegaming": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "baxçeyalîlî", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "tîmşerêtaktîkan", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobileyê", "thearcana": "thearcana", "8ballpool": "8topêbîlyard", "emergencyhq": "navenda_lezgîn", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "r<PERSON>j<PERSON>baş", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "prensazemê", "beatstar": "st<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "ev<PERSON><PERSON><PERSON><PERSON>", "androidgames": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "criminalcase": "dozakrinal", "summonerswar": "summonerswar", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leagueofangels": "komelemelaiketan", "lordsmobile": "lordsmobile", "tinybirdgarden": "baxçeyêçûkêbiçûk", "gachalife": "gachalife", "neuralcloud": "ew<PERSON><PERSON><PERSON>", "mysingingmonsters": "s<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "arş<PERSON>vaş<PERSON>n", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "c<PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "şerêbaskên", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "çîrokêherdem", "futime": "demxweş", "antiyoy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "apexlegendmobile": "apexlegendmobile", "ingress": "destpê<PERSON><PERSON>", "slugitout": "şerbike", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "derketinameydanê", "wolfy": "gur", "runcitygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegodemovil": "juegodemovil", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "lunaparkêxewnan", "grandchase": "grandchase", "bombmebrasil": "bombmebiminbrezîlya", "ldoe": "ldoe", "legendonline": "efsaneyaonline", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "nikkî<PERSON>biriqe", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "rêyaberêkûderê", "sealm": "sealm", "shadowfight3": "şerekêsîbera3", "limbuscompany": "kompanyalimbus", "demolitionderby3": "derbiyahilweşînê3", "wordswithfriends2": "peyvênbihevalantev2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "çîrokapiştmist", "showbyrock": "showbyrock", "ladypopular": "jinakefêm<PERSON>s", "lolmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "cihanakamêlê", "empiresandpuzzles": "împaratorîûpuzzle", "empirespuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "meydanîşerêmobî<PERSON>hin<PERSON>ê", "fanny": "kûn", "littlenightmare": "kabûsaxirpiçûk", "aethergazer": "aethergazer", "mudrunner": "<PERSON>ê<PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "herdemcan", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "şovalyeyêtar<PERSON>", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiyênderketî", "eveechoes": "eveechoes", "jogocelular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "şerêkolanêduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "lîstikbgmi", "girlsfrontline": "keçênxetapêş", "jurassicworldalive": "jurassicworldzindî", "soulseeker": "giyangerok", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "çîrokamoonchai", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofneverland": "efsaneyaxwendina", "pubglite": "pubglite", "gamemobilelegends": "lîstikamobillegends", "timeraiders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingmobile": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "şerêpisîkan", "dnd": "dnd", "quest": "ger", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpglimasê", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "rêwîttrpg", "2300ad": "2300pd", "larp": "larp", "romanceclub": "klûbaevînê", "d20": "d20", "pokemongames": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON>or", "pokemongo": "pokemongo", "pokemonshowdown": "poke<PERSON>ş<PERSON>", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "hentai", "hypno": "<PERSON><PERSON><PERSON><PERSON>", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "sohbet", "pikachu": "pîkaçû", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "tîmamistî<PERSON>", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "piçûkê", "shinypokemon": "pokemonêgeş", "mesprit": "mesprit", "pokémoni": "pokémonî", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "nêçîrvanêbirisqîn", "ajedrez": "şetrenc", "catur": "şetrenc", "xadrez": "şetrenc", "scacchi": "şetrenc", "schaken": "şetrenc", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "keçênşetrencê", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "lîstikasatrancê", "japanesechess": "şetrencajaponî", "chinesechess": "şetrencaçînî", "chesscanada": "satrancakanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "destpêk", "rook": "keleh", "chesscom": "chesscom", "calabozosydragones": "calabozosûdragones", "dungeonsanddragon": "zîndanû<PERSON>derha", "dungeonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "zîndanû<PERSON>derha", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "efsaneyavoxmachina", "doungenoanddragons": "dungeonû<PERSON>der<PERSON>", "darkmoor": "aximê_tarî", "minecraftchampionship": "pêşbaziyaminecraftê", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "mod<PERSON>n<PERSON><PERSON>", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "<PERSON><PERSON><PERSON><PERSON>", "addons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftdest", "minecraft360": "minecraft360", "moddedminecraft": "minecraftêguherîtî", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "navberaerdan", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "bajarêminecraft", "pcgamer": "lîstikvanêpc", "jeuxvideo": "lîstikên<PERSON><PERSON><PERSON><PERSON><PERSON>", "gambit": "gambit", "gamers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "levelup": "astbilindkirin", "gamermobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameover": "xilas", "gg": "gg", "pcgaming": "lîstikênpc", "gamen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "lîstikênpc", "casualgaming": "lîstikênre<PERSON>t", "gamingsetup": "setupageman", "pcmasterrace": "pcmasterrace", "pcgame": "lîstikêpc", "gamerboy": "<PERSON><PERSON><PERSON>", "vrgaming": "lîstikênvr", "drdisrespect": "drdisrespect", "4kgaming": "lîstika4k", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "lîstikvanêkonsolê", "boxi": "boxi", "pro": "pro", "epicgamers": "lîstikvanênepîk", "onlinegaming": "lîstikê<PERSON>lay<PERSON>", "semigamer": "nîvlîstikvan", "gamergirls": "keçêngamer", "gamermoms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerguy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamewatcher": "lîst<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerkeçan", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "tîmatêxwewestîne", "mallugaming": "lîstikvanê<PERSON>", "pawgers": "pawgers", "quests": "<PERSON><PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygaming": "lîstikênxweş", "gamelpay": "lîstikbide", "juegosdepc": "lîstikênkompûterê", "dsswitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "competitivegaming": "lîstikênpêşbaziyê", "minecraftnewjersey": "minecraftnewjersey", "faker": "sexte", "pc4gamers": "pc4<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "lîstikaheterosexual", "gamepc": "lîstikêpc", "girlsgamer": "keçêngamer", "fnfmods": "fnfmods", "dailyquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamegirl": "lîstikçîkeç", "chicasgamer": "keçêngamer", "gamesetup": "lîst<PERSON><PERSON><PERSON><PERSON>", "overpowered": "zêdebihez", "socialgamer": "lîstikvanêcivakî", "gamejam": "lîstikjam", "proplayer": "lîstikvanêprofesyonel", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "<PERSON><PERSON><PERSON><PERSON>", "republicofgamers": "komarageran", "aorus": "aorus", "cougargaming": "lîstikacougar", "triplelegend": "s<PERSON><PERSON><PERSON><PERSON>", "gamerbuddies": "hevalbazî", "butuhcewekgamers": "butuhkeçagamers", "christiangamer": "lîstikvanêxir<PERSON><PERSON><PERSON><PERSON>", "gamernerd": "lîstikvanxurt", "nerdgamer": "<PERSON>rd<PERSON><PERSON><PERSON><PERSON><PERSON>", "afk": "afk", "andregamer": "andregamer", "casualgamer": "lîstikvanêdemdemî", "89squad": "89tîm", "inicaramainnyagimana": "çawaserekebikim", "insec": "ne<PERSON>lek<PERSON>", "gemers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunizlemek": "oyunizlemek", "gamertag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lanparty": "partyalanê", "videogamer": "lîstikvanê<PERSON><PERSON><PERSON><PERSON>", "wspólnegranie": "lî<PERSON><PERSON><PERSON><PERSON>beş", "mortdog": "mortdog", "playstationgamer": "lîstikvanêplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "lîstikvanêsa<PERSON>lem", "gtracing": "lê<PERSON>z<PERSON><PERSON>_otomobîlan", "notebookgamer": "lêyîst<PERSON><PERSON><PERSON>", "protogen": "protogen", "womangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "<PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "muzîkanintendo", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "lîstikvanênpa<PERSON>îzê", "switch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringuîwîk", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "parêzerêdozê", "ssbm": "ssbm", "skychildrenofthelight": "zarokênasmanêrona<PERSON>ê", "tomodachilife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>ê", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "lîstikê<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogames": "lîstikênnintendo", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "bayêçolê", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51lîstik", "earthbound": "l<PERSON><PERSON><PERSON>", "tales": "<PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "ma<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendoşîlî", "tloz": "tloz", "trianglestrategy": "stratejiyasêgo<PERSON>e", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "ninte<PERSON><PERSON>", "new3ds": "3dsênû", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "şervanênhyrulê", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioyûsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "rov<PERSON><PERSON><PERSON><PERSON><PERSON>", "vanillalol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "ligalejendan", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspanya", "aatrox": "aatrox", "euw": "iyy", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "lî<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaminglol": "lîst<PERSON><PERSON><PERSON><PERSON>", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "lîstikêfortnite", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "lîstikênv<PERSON><PERSON><PERSON><PERSON>", "scaryvideogames": "lîstikênvîdyoyêtirsnak", "videogamemaker": "lîstikçêker", "megamanzero": "megamanzero", "videogame": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videosgame": "lîstikên<PERSON><PERSON><PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "şanoyaşerêblokê", "arcades": "<PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "hevalênpûfê", "farmingsimulator": "lîst<PERSON><PERSON><PERSON>", "robloxchile": "robloxçîlê", "roblox": "roblo<PERSON>", "robloxdeutschland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "robloxkurd<PERSON>", "erlc": "erlc", "sanboxgames": "lîstikênsandboxê", "videogamelore": "çîrokêlîstikan", "rollerdrome": "rollerdrome", "parasiteeve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamscape": "xewnewar", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "dizeyaderbûnyînmezin", "deadspace": "cihmirî", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "lîstikên<PERSON><PERSON><PERSON><PERSON><PERSON>", "theoldrepublic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videospiele": "videolîstik", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "lîstikê<PERSON>r<PERSON>bêrê", "wolfenstein": "wolfenstein", "actionadventure": "çalakîserûd", "storyofseasons": "çîrokawerzan", "retrogames": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "komputerênkevn", "retrogaming": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "ro<PERSON><PERSON><PERSON>îstikê", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "neheqî2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "ji<PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "tûj", "mystgames": "lîstikên<PERSON><PERSON>", "blockchaingaming": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievil": "nav<PERSON>n", "consolegaming": "l<PERSON>st<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolan", "outrun": "rev<PERSON>n", "bloomingpanic": "gulşînatirs", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "lêgerînakeçêncindî", "supergiant": "supermezin", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "lîstikênçandiniyê", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "lîstikênjackbox", "interactivefiction": "çîrokaînterak<PERSON>î<PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "diljînekevserkeftî", "visualnovel": "romanêndî<PERSON><PERSON><PERSON>", "visualnovels": "romanênbidîtbarî", "rgg": "rgg", "shadowolf": "gurêtar<PERSON>", "tcrghost": "tcrghost", "payday": "r<PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "prense<PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "qutiyaxwêliyê", "aestheticgames": "lîstikênestetîk", "novelavisual": "romanaçavdîtî", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "godhand": "destêxwedê", "leafblowerrevolution": "şoreşalêxap<PERSON>", "wiiu": "wiiu", "leveldesign": "leveldesign", "starrail": "starrail", "keyblade": "kilîtşûr", "aplaguetale": "belayekêserê", "fnafsometimes": "fnafhincarcaran", "novelasvisuales": "romanên<PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "robloxbrezîlya", "pacman": "pacman", "gameretro": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videojuejos": "videolîstik", "videogamedates": "lîstikênvî<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "jibertenê3", "hulkgames": "lîstikênhulk", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "ve<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "roja<PERSON><PERSON>", "maniacmansion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crashracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "3dplatformer", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "lîstikênkevneşêweyê", "hellblade": "şûrêdozexê", "storygames": "çîrokîlîstik", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "lîstikbikaranîn", "offmortisghost": "offmortisghost", "tinybunny": "keroşkêpiçûk", "retroarch": "retroarch", "powerup": "hêzlêzêdebike", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "serpêhatiyêngrafîk", "quickflash": "flashbilez", "fzero": "fzero", "gachagaming": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "çolistan", "powerwashsim": "lîstikapaqijkirinabihêz", "coralisland": "giravêkoral<PERSON>", "syberia3": "sîb<PERSON>rya3", "grymmorpg": "rpgyageranxweş", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animewarriors2", "footballfusion": "futboltêkelî", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simulator", "symulatory": "simulatorî", "speedrunner": "lezbileyîk", "epicx": "epîkx", "superrobottaisen": "superrobot<PERSON><PERSON>", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "wonderlandonline", "skylander": "<PERSON><PERSON><PERSON>", "boyfrienddungeon": "d<PERSON>lberînzîndanê", "toontownrewritten": "toontownrewrittennûkirî", "simracing": "lîst<PERSON>ajokariy<PERSON>", "simrace": "pêşbaziyas<PERSON><PERSON>", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "bedenênezmûnî", "seum": "<PERSON><PERSON><PERSON>", "partyvideogames": "lîstikênvîdyoyêpartiyê", "graveyardkeeper": "goristan", "spaceflightsimulator": "sim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "xwarinûlîstikênvîdyoyê", "oyunvideoları": "oyunvideoları", "thewolfamongus": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handygame": "lîstikadestan", "leyendasyvideojuegos": "leyendasyvideojuegos", "oldschoolvideogames": "lîstikênv<PERSON><PERSON><PERSON><PERSON>", "racingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "famitsu": "famitsu", "gatesofolympus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunternow": "nûkanêgreramonstêran", "rebelstar": "st<PERSON><PERSON><PERSON><PERSON>hil<PERSON>", "indievideogaming": "lîstikênvîdyoyênserbixwe", "indiegaming": "lîstikênserxwebûn", "indievideogames": "lîstikênvîdyoyênserxwebûn", "indievideogame": "lîstikavîdyoyaserxwebûn", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spider<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bufffortress": "qeleyaxurt", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "projel", "futureclubgames": "lîstikênklûbapaşerojê", "mugman": "mugman", "insomniacgames": "lîstikênşevnedîtikan", "supergiantgames": "lîstikêngigan<PERSON><PERSON><PERSON>", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "zanistaaperture", "backlog": "paşmay<PERSON>", "gamebacklog": "lîstikênpaşmayî", "gamingbacklog": "lîstikênpaşmayî", "personnagejeuxvidéos": "karakterênlîstikênvîdyoyê", "achievementhunter": "destkevtîxwaz", "cityskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "segêşeytanî", "beastlord": "xwedi<PERSON>ê<PERSON><PERSON><PERSON>", "juegosretro": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriûdaristanakor", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "rezervadopamînê", "staxel": "staxel", "videogameost": "videolîstikost", "dragonsync": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ezjikofxvhezdıkım", "arcanum": "nependî", "neoy2k": "neoy2k", "pcracing": "pêşbaziyapc", "berserk": "har", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "iniciald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animeyaxîş", "darkerthanblack": "tarî<PERSON>rjireş", "animescaling": "pîvandinaanîmeyê", "animewithplot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pesci": "pesci", "retroanime": "anîmeyaretro", "animes": "anime", "supersentai": "supersentaî", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON><PERSON>", "90sanime": "90<PERSON>me", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogiyê_xweşik", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "2000anîme", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonedemsala1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "<PERSON><PERSON><PERSON><PERSON>", "thevisionofescaflowne": "dîtinavaescaflowne", "slayers": "kujer", "tokyomajin": "tokyomajin", "anime90s": "anime90an", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toaletê<PERSON>rê<PERSON>îhana<PERSON>n", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "mangaskipbeat", "vanitas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fireforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "rojnivîskapaşerojê", "fairytail": "çîrokêperî", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "<PERSON><PERSON><PERSON><PERSON>", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "melodiyaperiyêderyayê", "kamisamakiss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemangas": "mangayênromantîk", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "gola_reş", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "segêkoçerbûngostray", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "endeksekêmagîkêdiyarîkrî", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8bêdawî", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "<PERSON><PERSON><PERSON><PERSON>î<PERSON><PERSON><PERSON>", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monsterkîzan", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "anîmeyawerzişê", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "animeisekai", "sagaoftanyatheevil": "çîrokatanyaxerab", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "kurvûdrindebir", "fistofthenorthstar": "destêstêrkaşimal", "mazinger": "mazinger", "blackbuttler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "çawadayêmûmyayêbiparêzî", "fullmoonwosagashite": "heyvaşavêdigerîm", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "xweşikûecêb", "martialpeak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "keçahîskora", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "<PERSON><PERSON><PERSON><PERSON>", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "keçacinê", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "lîstik", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "inûmîmî", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "kl<PERSON><PERSON>", "loli": "loli", "horroranime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "şeytanmirovzarokdibêje", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangaxweş", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "evî<PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "ezbiyanîmepêwîst", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "neverlendasozkirî", "monstermanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yourlieinapril": "derewated<PERSON><PERSON><PERSON><PERSON>", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "<PERSON><PERSON><PERSON><PERSON>", "deepseaprisoner": "girtiyêderyayakûr", "jojolion": "jojo<PERSON>", "deadmanwonderland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "dilêpandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "xetaşeytan", "toyoureternity": "hetahetayê", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "şînîgamî", "secretalliance": "hevbendiya<PERSON>î", "mirainikki": "rojnivîskapaşerojê", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON><PERSON><PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "cinperest", "detectiveconan": "conandetekt<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "çavlêgeriya", "spyfamily": "malbataca<PERSON>", "airgear": "bayêhewayê", "magicalgirl": "keçamagîkî", "thesevendeadlysins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "dibistanagi<PERSON><PERSON>", "thegodofhighschool": "xwedayêdibistanêbilind", "kissxsis": "maçxwişk", "grandblue": "grandblue", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoqurtkirî", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "ne<PERSON>_nebext", "romancemanga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolicon": "lo<PERSON>on", "demonslayertothesword": "şeytankujêhetajûrê", "bloodlad": "bloodlad", "goodbyeeri": "bibieriher<PERSON>", "firepunch": "kulmaagir", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceanime": "anîmeyaromanî", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "highschoolofthedead": "dibistanaxuy<PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "teknoyaelmani", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animeyajaponî", "animespace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsundpanzer": "keçûtank", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON>", "animedub": "<PERSON><PERSON><PERSON>", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "anîmeyaserxwebûn", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "haremanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kochikame": "kochikame", "nekoboy": "kurêpisîk", "gashbell": "gashbell", "peachgirl": "keçaqesp", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "dilêmangayê", "deliciousindungeon": "xweştemtîênderînê", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "xwedêmindestpêkir", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>vê<PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialgelekzehmetе", "overgeared": "zêdehemûçek", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "atelyeyacad<PERSON><PERSON>an", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "dilopênxwed<PERSON>", "loscaballerosdelzodia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeshojo": "animeshojo", "reverseharem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "xewnagrandblu", "bloodplus": "xwînplus", "bloodplusanime": "animeyaxûnpozîtîf", "bloodcanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodc": "xwîn", "talesofdemonsandgods": "çîrokacinûxwedayan", "goreanime": "goreanime", "animegirls": "animeholik", "sharingan": "<PERSON><PERSON>", "crowsxworst": "crowsxnebaştirîn", "splatteranime": "anîmeyaxûnê", "splatter": "ri<PERSON><PERSON><PERSON>", "risingoftheshieldhero": "ji<PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "animeyasomalî", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animespanya", "animeciudadreal": "animecityreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "zarok<PERSON><PERSON><PERSON><PERSON>", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "superpehlevan", "animeidols": "animeidols", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "rojênkesk", "magicalgirls": "keçênseĥirdar", "callofthenight": "<PERSON><PERSON>ş<PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "keçastêrka", "shadowgarden": "baxçeyêsîber", "tsubasachronicle": "tsubasachronicle", "findermanga": "<PERSON><PERSON><PERSON><PERSON>", "princessjellyfish": "<PERSON>nse<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "stêrkarevûyê", "animeverse": "animeverse", "persocoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omniscientreadersview": "xwîneravê<PERSON><PERSON><PERSON>în", "animecat": "piş<PERSON><PERSON><PERSON><PERSON>", "animerecommendations": "animeypêşniyarkirin", "openinganime": "anîmeya<PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "komediyaromantî<PERSON><PERSON><PERSON><PERSON>min", "evangelion": "evangelion", "gundam": "gundam", "macross": "makros", "gundams": "gundam", "voltesv": "voltesv", "giantrobots": "robotêngirs", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mek", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deathnote": "<PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "qeşa", "jojobizarreadventures": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "anîmeyaleş<PERSON>î", "greenranger": "rangerkesk", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupindê3yemîn", "animecity": "bajareanimaê", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "serp<PERSON><PERSON><PERSON><PERSON>gi<PERSON>", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON>n", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "şeytankuj", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "leşkerêlêkolînê", "onepieceanime": "onepieceanime", "attaquedestitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theonepieceisreal": "onepiecerastîye", "revengers": "<PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "<PERSON><PERSON><PERSON><PERSON>", "aonoexorcist": "aonoexorcist", "joyboyeffect": "bandoraşahîbûnê", "digimonstory": "ç<PERSON>rokadigimon", "digimontamers": "digimontamers", "superjail": "girt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonêbêkêmasî", "kemonofriends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "cadûgeraesmanî", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "tenêjiberku", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "kola<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recuentosdelavida": "se<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}