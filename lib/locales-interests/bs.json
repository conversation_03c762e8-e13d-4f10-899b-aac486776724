{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrologija", "cognitivefunctions": "kognitivnefunkcije", "psychology": "psihologija", "philosophy": "filozofija", "history": "historija", "physics": "fizika", "science": "nauka", "culture": "kultura", "languages": "jez<PERSON>", "technology": "tehnologija", "memes": "memovi", "mbtimemes": "m<PERSON><PERSON><PERSON><PERSON>", "astrologymemes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enneagrammemes": "<PERSON>nea<PERSON><PERSON><PERSON><PERSON><PERSON>", "showerthoughts": "<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "smiješno", "videos": "videi", "gadgets": "gadžeti", "politics": "politika", "relationshipadvice": "savjetizveze", "lifeadvice": "životnisavjeti", "crypto": "kripto", "news": "vijesti", "worldnews": "svjetskevijesti", "archaeology": "arheologija", "learning": "učenje", "debates": "rasprave", "conspiracytheories": "teorijezavjere", "universe": "univerzum", "meditation": "meditacija", "mythology": "mitologija", "art": "umjetnost", "crafts": "zana<PERSON>", "dance": "ples", "design": "<PERSON><PERSON><PERSON>", "makeup": "šminka", "beauty": "l<PERSON><PERSON>a", "fashion": "moda", "singing": "<PERSON><PERSON><PERSON><PERSON>", "writing": "pisanje", "photography": "fotografija", "cosplay": "cosplay", "painting": "slikanje", "drawing": "crtanje", "books": "knjige", "movies": "filmovi", "poetry": "poezija", "television": "televizija", "filmmaking": "<PERSON><PERSON><PERSON><PERSON>", "animation": "animacija", "anime": "anime", "scifi": "naučnafantastika", "fantasy": "fantazija", "documentaries": "dokumentarci", "mystery": "<PERSON><PERSON><PERSON>", "comedy": "komedija", "crime": "<PERSON><PERSON><PERSON><PERSON>", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horor", "romance": "romansa", "realitytv": "rijalititv", "action": "ak<PERSON><PERSON>", "music": "muzika", "blues": "blues", "classical": "klasični", "country": "država", "desi": "desi", "edm": "edm", "electronic": "elektronika", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "k<PERSON><PERSON>a", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "<PERSON><PERSON><PERSON>", "metal": "metal", "pop": "pop", "punk": "pank", "rnb": "rnb", "rap": "rep", "reggae": "reggae", "rock": "rok", "techno": "tehno", "travel": "<PERSON><PERSON><PERSON>", "concerts": "<PERSON><PERSON><PERSON><PERSON>", "festivals": "festivali", "museums": "muzeji", "standup": "standup", "theater": "poz<PERSON>š<PERSON>", "outdoors": "napolju", "gardening": "baštovanstvo", "partying": "<PERSON><PERSON><PERSON>", "gaming": "gaming", "boardgames": "društvenéigre", "dungeonsanddragons": "tamniceidragoni", "chess": "<PERSON>ah", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "hrana", "baking": "pečenje", "cooking": "kuh<PERSON><PERSON>", "vegetarian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegan": "vegan", "birds": "ptice", "cats": "<PERSON><PERSON><PERSON>", "dogs": "psi", "fish": "riba", "animals": "životinje", "blacklivesmatter": "crnizivotisubitni", "environmentalism": "ekologija", "feminism": "feminizam", "humanrights": "<PERSON>juds<PERSON><PERSON><PERSON>", "lgbtqally": "lgbtqsaveznik", "stopasianhate": "stopazijskojmržnji", "transally": "transsaveznik", "volunteering": "volontiranje", "sports": "sport", "badminton": "badminton", "baseball": "bejzbol", "basketball": "<PERSON><PERSON><PERSON><PERSON>", "boxing": "boks", "cricket": "kriket", "cycling": "bicikliranje", "fitness": "fitnes", "football": "fudbal", "golf": "golf", "gym": "teretana", "gymnastics": "gimnast<PERSON>", "hockey": "hokej", "martialarts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "netbal", "pilates": "pilates", "pingpong": "stonite<PERSON>", "running": "trčanje", "skateboarding": "skejt", "skiing": "<PERSON><PERSON><PERSON>", "snowboarding": "snouboarding", "surfing": "<PERSON><PERSON><PERSON>", "swimming": "plivanje", "tennis": "tenis", "volleyball": "od<PERSON><PERSON><PERSON>", "weightlifting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "joga", "scubadiving": "ronjenje", "hiking": "planinarenje", "capricorn": "jarac", "aquarius": "vodolija", "pisces": "ribe", "aries": "ovan", "taurus": "bik", "gemini": "<PERSON><PERSON><PERSON><PERSON>", "cancer": "rak", "leo": "leo", "virgo": "<PERSON><PERSON><PERSON><PERSON>", "libra": "vaga", "scorpio": "škorpija", "sagittarius": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortterm": "kratkotrajno", "casual": "ležern<PERSON>", "longtermrelationship": "dugogodišnjaveza", "single": "samac", "polyamory": "poliamor", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gej", "lesbian": "<PERSON><PERSON><PERSON>jk<PERSON>", "bisexual": "biseksualni", "pansexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "asexual": "aseksualan", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kraljevskikvest", "soulreaver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverzum", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "sibirija", "rdr2": "rdr2", "spyrothedragon": "spajrozmajigrealjegendicna", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "zalazaksuncezavozačem", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "čvrstkaostena", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "otvorenisvjet", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "tamnicegužvanje", "jetsetradio": "jetsetradio", "tribesofmidgard": "plem<PERSON><PERSON><PERSON><PERSON>", "planescape": "planescape", "lordsoftherealm2": "gospodaricarstava2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "medabots": "medaboti", "lodsoftherealm2": "lordicarstava2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "uronjensimulatori", "okage": "okage", "juegoderol": "juegoderol", "witcher": "vještac", "dishonored": "nepočastvovan", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modiranje", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "imerzivno", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldskul", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidnamotivacija", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "ludakzal<PERSON>bav", "otomegames": "otomeigrе", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinavremena", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirthemasquerade", "dimension20": "dimension20", "gaslands": "gaslands", "pathfinder": "putokaz", "pathfinder2ndedition": "pathfinder2izdanje", "shadowrun": "shadowrun", "bloodontheclocktower": "krvnasatnijtoranj", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "ljubavnikki", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravitacijski<PERSON><PERSON>š", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "jedanpo<PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "overlord", "yourturntodie": "tvojredjeumrijeti", "persona3": "persona3", "rpghorror": "rpghoror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "pljačkaši", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtekst", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "sklonište", "gurps": "gurps", "darkestdungeon": "najcrnjatamnica", "eclipsephase": "fazazatmjenja", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dinast<PERSON><PERSON><PERSON><PERSON>", "skullgirls": "skullgirls", "nightcity": "noćnigrad", "hogwartslegacy": "hogvartslegend", "madnesscombat": "bor<PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "road96": "put96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelike", "gothamknights": "gothamknights", "forgottenrealms": "zaboravljenecarstava", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "crtangrad", "childoflight": "dijetesvjetlosti", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonsvjet", "monsterrancher": "odgajivačmonstruma", "ecopunk": "ekopanik", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "sjenovitipank", "finalfantasyxv": "finalfantasyxv", "everoasis": "svakodrvo", "hogwartmystery": "hogwartsmisterija", "deltagreen": "deltazeleni", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "posljednjaepoha", "starfinder": "zvjezdotražac", "goldensun": "zlatnosunce", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "oštriceu<PERSON><PERSON>", "twilight2000": "sumrak2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "zlozemlje", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "djavoljiprezivilac", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "božanstvenost", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "tugapozastaro", "adventurequest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "sunčanoluka", "talesofsymphonia": "priče_simfonije", "honkaistarrail": "honkaistarrail", "wolong": "volong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "razorenigrad", "myfarog": "myfarog", "sacredunderworld": "svetipodzemnisvjet", "chainedechoes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i", "darksoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "soulslikes", "othercide": "drugoubojstvo", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "hronotrigger", "pillarsofeternity": "stuboviviječnosti", "palladiumrpg": "palladiumrpg", "rifts": "<PERSON>ov<PERSON>", "tibia": "goljenica", "thedivision": "divizija", "hellocharlotte": "zdravocharlotte", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirskamaskarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "<PERSON><PERSON><PERSON><PERSON>", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "engineheart": "motornosrce", "fable3": "fable3", "fablethelostchapter": "fablepogubljenopolje", "hiveswap": "razmjenaroja", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edanv<PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "starfield", "oldschoolrevival": "oldschoolpovratak", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "divljisvijetovi", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kraljevstvosrca2", "darknessdungeon": "tamnicatamnica", "juegosrpg": "rpgigre", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomhearts3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "žetva", "gloomhaven": "gloomhaven", "wildhearts": "divljesrca", "bastion": "bastion", "drakarochdemoner": "devojk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "nebonadsnom", "shadowhearts": "sjenkeosrca", "nierreplicant": "nierreplicant", "gnosia": "gnozija", "pennyblood": "peniblud", "breathoffire4": "dimaognja4", "mother3": "majka3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "vještičjesrce", "harrypottergame": "harry<PERSON><PERSON><PERSON>ra", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirskimaskenbal", "dračák": "zmajče", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "lovkraljevski", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "shadowheartovzavjet", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "kraljevst<PERSON><PERSON><PERSON><PERSON>", "awplanet": "awplaneta", "theworldendswithyou": "svijetsezavršavastobom", "dragalialost": "dragalialost", "elderscroll": "starijsvitak", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "mra<PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "forumigrazauloge", "golarion": "golarion", "earthmagic": "zemljanamagija", "blackbook": "crnaknjiža", "skychildrenoflight": "nebeskadje<PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "sivaigrazauloga", "sacredgoldedition": "svetozlatnaizdanja", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "gotičkaigrica", "scarletnexus": "scarletovezao", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gejmingrpg", "prophunt": "prophunt", "starrails": "zvjezdanepruge", "cityofmist": "gradma<PERSON>", "indierpg": "indiejpg", "pointandclick": "pokaziišklikaj", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freeside": "slobodnastrana", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcajberpank", "deathroadtocanada": "smrt_put_za_kanadu", "palladium": "paladi<PERSON><PERSON>", "knightjdr": "vitezdr", "monsterhunter": "lovacnamonstrume", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremacija", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhanterrise", "nier": "nier", "dothack": "hakuj", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarnijeigre", "tacticalrpg": "taktičkirpg", "mahoyo": "mahoyo", "animegames": "animegejming", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "bogogrizac", "diluc": "diluc", "venti": "venti", "eternalsonata": "vječnasonata", "princessconnect": "princessconnect", "hexenzirkel": "vješticjikrug", "cristales": "krist<PERSON>", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindijski", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efudbal", "nba2k": "nba2k", "egames": "igrice", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligasanjara", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efudbal", "dreamhack": "dreamhack", "gaimin": "gejming", "overwatchleague": "overwatchliga", "cybersport": "kibersport", "crazyraccoon": "l<PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valoranttakmičarski", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "poluživot", "left4dead": "lijevizamrtve", "left4dead2": "left4dead2", "valve": "ventil", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "vječnoljeto", "goatsimulator": "simulator<PERSON>za", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planetaslobode", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "battlefield4", "nightinthewoods": "no<PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanije", "overcooked": "prepečeno", "interplanetary": "međuplanetarno", "helltaker": "paklenjak", "inscryption": "upisivanje", "7d2d": "7d2d", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "tvrđavapatuljaka", "foxhole": "rov", "stray": "lutalica", "battlefield": "bojnopolje", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "obrve", "blackdesert": "crnapustinja", "tabletopsimulator": "stonosimulator", "partyhard": "lud<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "hardspaceshipbreaker", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "zarobljensaludom", "dinkum": "pravi", "predecessor": "prethodnik", "rainworld": "kišnisvijet", "cavesofqud": "pecinekvda", "colonysim": "kolo<PERSON><PERSON><PERSON>", "noita": "noita", "dawnofwar": "svitanjerate", "minionmasters": "minionmasters", "grimdawn": "mračnazora", "darkanddarker": "mračnoitamnije", "motox": "motox", "blackmesa": "crnamesa", "soulworker": "dušaradnica", "datingsims": "simulacijezabavljanja", "yaga": "yaga", "cubeescape": "bjegizakocke", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "novigrad", "citiesskylines": "gradoviigradnja", "defconheavy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsija", "virtualkenopsia": "virtuelnakenopsija", "snowrunner": "snijegot<PERSON><PERSON>", "libraryofruina": "bibliotekauništenja", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "putokaz", "kenabridgeofspirits": "<PERSON>duhov<PERSON>", "placidplasticduck": "mirnaplastičnapatka", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatnakokoškonj", "dialtown": "dialtown", "smileforme": "smirni<PERSON>e", "catnight": "noćmač<PERSON>", "supermeatboy": "supermesodječak", "tinnybunny": "malimaca", "cozygrove": "utočnigaj", "doom": "propast", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdu<PERSON><PERSON><PERSON><PERSON>", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "pala<PERSON><PERSON>", "earthdefenseforce": "odbranazeminskihsnaga", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "p<PERSON><PERSON><PERSON>ises<PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "pobunapeščanaoluja", "farcry3": "farcry3", "hotlinemiami": "vručalinijamajami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombiji", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "divizije2", "killzone": "ubilačkazona", "helghan": "hel<PERSON>", "coldwarzombies": "zomb<PERSON>hladnogra<PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "borbeniavioniigre", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "snajperista", "modernwarfare": "moderniratkovi", "neonabyss": "neonambis", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "graničarskežemlje", "owerwatch": "ower<PERSON>", "rtype": "rtip", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "prvobitnimasakr", "worldofwarships": "worldofwarships", "back4blood": "povratakzakrv", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "ubica", "masseffect": "masseffect", "systemshock": "sistemskišok", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopsthelineig<PERSON>", "killingfloor2": "killingfloor2", "cavestory": "pećinskepriče", "doometernal": "doometernal", "centuryageofashes": "s<PERSON><PERSON><PERSON><PERSON>edobapepela", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generationzero": "<PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "fantomskibol", "warface": "ratnolice", "crossfire": "crossfire", "atomicheart": "atomskosrce", "blackops3": "blackops3", "vampiresurvivors": "vampirskipreživjeljavači", "callofdutybatleroyale": "callofdutybattleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "sloboda", "battlegrounds": "b<PERSON><PERSON><PERSON><PERSON>", "frag": "frag", "tinytina": "malatini<PERSON>", "gamepubg": "pubgigra", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "fpsigrе", "convertstrike": "konvertovaćudarac", "warzone2": "warzone2", "shatterline": "razbijenopravo", "blackopszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodymess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "republikanskakomanda", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "granaobrambena", "squad": "ekipa", "destiny1": "sudbina1", "gamingfps": "gamingfps", "redfall": "<PERSON><PERSON><PERSON><PERSON>", "pubggirl": "pubgcura", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "oklopnojezgro", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinineč<PERSON>nezemlje", "halo2": "halo2", "payday2": "platniday", "cs16": "cs16", "pubgindonesia": "pubgindonesija", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgomanija", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "sapunskibakalar", "ghostcod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "csplay": "cosplay", "unrealtournament": "nerealturnir", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pistoljizbič", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercellijada", "neonwhite": "neonbijela", "remnant": "ostatak", "azurelane": "azurelane", "worldofwar": "svjetratova", "gunvolt": "gunvolt", "returnal": "returnal", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "čovjeksjene", "quake2": "quake2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "morerazbojnika", "rust": "rust", "conqueronline": "conqueronline", "dauntless": "neustrašiv", "warships": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "letdrakonaigre", "recroom": "drust<PERSON>ai<PERSON>", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "<PERSON><PERSON><PERSON>", "phantasystaronline2": "phantasystaronline2", "maidenless": "bezđevojke", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "precrtaj", "agario": "agario", "secondlife": "drug<PERSON>ž<PERSON><PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superživotinjskirojal", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "klubpingvin", "lotro": "lotro", "wakfu": "wakfu", "scum": "šljam", "newworld": "noviavet", "blackdesertonline": "blackdesertonline", "multiplayer": "multiplayer", "pirate101": "pirat101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponigrad", "3dchat": "3dčet", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "hodočasnikkažuputa", "spiralknights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mulegend": "mulegenda", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "prorokzmajeva", "grymmo": "grymmo", "warmane": "<PERSON><PERSON><PERSON>", "multijugador": "multiigrač", "angelsonline": "anđelion<PERSON>", "lunia": "lunia", "luniaz": "luni<PERSON>i", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverzumonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsstararepublika", "grandfantasia": "velikafantazija", "blueprotocol": "blueprotokol", "perfectworld": "savršensvjet", "riseonline": "usponionline", "corepunk": "corepunk", "adventurequestworlds": "adventurequestworlds", "flyforfun": "<PERSON><PERSON><PERSON><PERSON>", "animaljam": "animaljam", "kingdomofloathing": "kraljevstvopreziranjaigara", "cityofheroes": "grad<PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "ulič<PERSON>borac", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "začast", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiverzum", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuelniborac", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mksmtrzavljavez", "nomoreheroes": "nemavišeheroja", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "kraljboraca", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retroborbeneigre", "blasphemous": "blasfemično", "rivalsofaether": "rivalioveetera", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "superudarac", "mugen": "mugen", "warofthemonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "sajberbotovi", "armoredwarriors": "oklopniratnici", "finalfight": "završnaborba", "poweredgear": "opremljena_snaga", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "borbenjeigre", "killerinstinct": "ubilačkiinstinkt", "kingoffigthers": "kraljboraca", "ghostrunner": "duhs<PERSON><PERSON><PERSON><PERSON>", "chivalry2": "viteštvo2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightnast<PERSON><PERSON>", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksongstršljen", "silksonggame": "silksonggame", "silksongnews": "vijestisilksong", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "<PERSON><PERSON><PERSON>", "evolutiontournament": "evolucijskiturniraj", "evomoment": "evotrenutka", "lollipopchainsaw": "lizalikasamotorlančanom", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "priceizberserije", "bloodborne": "bloodborne", "horizon": "horizont", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodbourne", "uncharted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "<PERSON><PERSON><PERSON><PERSON>", "playstationbuddies": "playstationprijatelji", "ps1": "ps1", "oddworld": "čudnisvjet", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbi<PERSON>", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "haošnaslobodi", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "godof<PERSON>", "gris": "gris", "trove": "trove", "detroitbecomehuman": "detroitpostajeljudski", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "turistickitrofej", "lspdfr": "lspdfr", "shadowofthecolossus": "<PERSON>je<PERSON><PERSON>losa", "crashteamracing": "crashteamracing", "fivepd": "petpd", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "igranjestejšn", "samuraiwarriors": "samuraji", "psvr2": "psvr2", "thelastguardian": "posljednjistrazar", "soulblade": "dušasablja", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "posljednjicuvar", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "<PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "šampionborbenenoći", "psychonauts": "psihonauti", "mhw": "mhw", "princeofpersia": "princperzije", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "bojišnica", "dontstarvetogether": "nezajednoglad<PERSON>j<PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "zvjezdanovezani", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON>eprepravl<PERSON>č", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxserija", "r6xbox": "r6xbox", "leagueofkingdoms": "ligakraljevstava", "fable2": "bajka2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "traštv", "skycotl": "nebozsjaj", "erica": "erica", "ancestory": "predci", "cuphead": "cuphead", "littlemisfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "č<PERSON><PERSON>š<PERSON>bal", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kultjagnjeta", "duckgame": "<PERSON><PERSON><PERSON>", "thestanleyparable": "stenliparabola", "towerunite": "towerunite", "occulto": "okultno", "longdrive": "dugavožnja", "satisfactory": "zadovoljavajuće", "pluviophile": "kišoljubac", "underearth": "ispodzemlje", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "mrač<PERSON><PERSON><PERSON><PERSON>", "pizzatower": "pizzatower", "indiegame": "indieigra", "itchio": "itchio", "golfit": "golfit", "truthordare": "istinailismjelost", "game": "igra", "rockpaperscissors": "kamenpa<PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolina", "hulahoop": "hula<PERSON>", "dare": "<PERSON><PERSON><PERSON><PERSON>", "scavengerhunt": "potragazablagom", "yardgames": "igrenad<PERSON><PERSON><PERSON>", "pickanumber": "odaberibroj", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "birbong", "dicegoblin": "kockargoblin", "cosygames": "cozyigrice", "datinggames": "i<PERSON><PERSON><PERSON><PERSON>", "freegame": "besplatnaigra", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "juegos", "mahjong": "mahjong", "jeux": "igre", "simulationgames": "simulacioneigre", "wordgames": "igrerječima", "jeuxdemots": "igrarečima", "juegosdepalabras": "igrerečima", "letsplayagame": "hajdemoseigramo", "boredgames": "dosadneigre", "oyun": "oyun", "interactivegames": "interaktivneigre", "amtgard": "amtgard", "staringcontests": "<PERSON><PERSON><PERSON>", "spiele": "igre", "giochi": "igre", "geoguessr": "geoguessr", "iphonegames": "igrezaiphone", "boogames": "boogames", "cranegame": "<PERSON><PERSON><PERSON><PERSON>", "hideandseek": "sak<PERSON><PERSON><PERSON><PERSON>", "hopscotch": "skakutanje", "arcadegames": "arkadneigre", "yakuzagames": "yakuzaigre", "classicgame": "klasičnaigra", "mindgames": "mentalneignre", "guessthelyric": "pogo<PERSON><PERSON><PERSON>", "galagames": "galagames", "romancegame": "igra_romantike", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "brzalice", "4xgames": "4xigre", "gamefi": "gamefi", "jeuxdarcades": "jeuxdarcades", "tabletopgames": "igrezastolom", "metroidvania": "metroidvania", "games90": "igre90", "idareyou": "<PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "igrezatrke", "ets2": "ets2", "realvsfake": "stvarnostprotivlažno", "playgames": "igrajigrice", "gameonline": "onlinegame", "onlinegames": "onlinegames", "jogosonline": "onlineigre", "writtenroleplay": "pisaniroleplay", "playaballgame": "igrajloptaškuigru", "pictionary": "pictionary", "coopgames": "kooperativneigre", "jenga": "jenga", "wiigames": "wiigre", "highscore": "najboljirezultat", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON>", "burgergames": "igreburgera", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwcrnaedicija", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "igrapitanja", "gioco": "gioco", "managementgame": "igra<PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "igrapronađiskrivenestvari", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formula1igra", "citybuilder": "gradograditelj", "drdriving": "drvozimdrvozim", "juegosarcade": "arcadeigre", "memorygames": "igrepamćenja", "vulkan": "vulkan", "actiongames": "akcionigre", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "pinball<PERSON><PERSON><PERSON>", "oldgames": "stareigre", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "igrica", "lasergame": "laserskaigra", "imessagegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "idlegames": "idlegames", "fillintheblank": "popuniprazno", "jeuxpc": "igrepc", "rétrogaming": "retrogejming", "logicgames": "logičkeigre", "japangame": "japanskaigra", "rizzupgame": "rizzupigra", "subwaysurf": "subwaysurfanje", "jeuxdecelebrite": "igrecelebrity", "exitgames": "izlazneigrе", "5vs5": "5protiv5", "rolgame": "<PERSON><PERSON><PERSON><PERSON>", "dashiegames": "dashiegames", "gameandkill": "igrajubij", "traditionalgames": "tradicionalneigre", "kniffel": "jamb", "gamefps": "igrefps", "textbasedgames": "igretekstom", "gryparagrafowe": "paragrafovig<PERSON><PERSON><PERSON>", "fantacalcio": "fantacalcio", "retrospel": "retrospel", "thiefgame": "igrazlodjaja", "lawngames": "igrenad<PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "stoninogomet", "tischfußball": "stoninogometimaboo", "spieleabende": "igračkevečeri", "jeuxforum": "jeuxforum", "casualgames": "casualigre", "fléchettes": "strelice", "escapegames": "igrezabježanja", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "igrea<PERSON>mat", "játék": "igra", "bordfodbold": "bordfodbold", "jogosorte": "igresrece", "mage": "magovnjak", "cargames": "autigre", "onlineplay": "onlineigranje", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "igračkevečeri", "pursebingos": "bingozatorbice", "randomizer": "randomizator", "msx": "msx", "anagrammi": "anagrami", "gamespc": "gamespc", "socialdeductiongames": "društvenededuktivneigre", "dominos": "domino", "domino": "domino", "isometricgames": "izometrijskeigre", "goodoldgames": "dobrestareigre", "truthanddare": "istinailiizbor", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "virtuelneigre", "romhack": "romhak", "f2pgamer": "f2pigrac", "free2play": "besplatno_za_igru", "fantasygame": "fantazijskaigra", "gryonline": "gryonline", "driftgame": "driftingigra", "gamesotomes": "igrice<PERSON><PERSON>", "halotvseriesandgames": "halotvserijeiigre", "mushroomoasis": "oazag<PERSON>jiva", "anythingwithanengine": "sveštoimamotor", "everywheregame": "svudjegame", "swordandsorcery": "mačimagija", "goodgamegiving": "dobragameratonjenje", "jugamos": "igramo", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "računarskeigre", "virgogami": "virgorigami", "gogame": "idemoigra", "jeuxderythmes": "ritmičkeigre", "minaturegames": "minijaturneigre", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "samoljubavgaming", "gamemodding": "modovanjeigara", "crimegames": "igrezlocina", "dobbelspellen": "dobbelspellen", "spelletjes": "<PERSON><PERSON><PERSON><PERSON>", "spacenerf": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "šarade", "singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgame": "kooperativnaigra", "gamed": "gejmao", "forzahorizon": "forzahorizon", "nexus": "neksus", "geforcenow": "geforcenow", "maingame": "glavnaigra", "kingdiscord": "kingdiscord", "scrabble": "screbls", "schach": "<PERSON>ah", "shogi": "<PERSON><PERSON>i", "dandd": "dandd", "catan": "katan", "ludo": "ludo", "backgammon": "tavla", "onitama": "onitama", "pandemiclegacy": "nasljeđepandemije", "camelup": "kamilahore", "monopolygame": "monopoliigra", "brettspiele": "brettspiele", "bordspellen": "društveneigre", "boardgame": "društvenajigra", "sällskapspel": "društveneigre", "planszowe": "drustveneigre", "risiko": "risiko", "permainanpapan": "društveneigre", "zombicide": "zombicid", "tabletop": "tabletop", "baduk": "baduk", "bloodbowl": "krvavabowla", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "ideskanaigru", "connectfour": "četiripovezana", "heroquest": "heroquest", "giochidatavolo": "stonieigre", "farkle": "farkle", "carrom": "carrom", "tablegames": "igrezastolom", "dicegames": "igrekockicama", "yatzy": "jat<PERSON>", "parchis": "parchis", "jogodetabuleiro": "društvenаigra", "jocuridesocietate": "društveneigre", "deskgames": "igrenastolu", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelkrizniprotokol", "cosmicencounter": "kosmič<PERSON><PERSON><PERSON>", "creationludique": "kreativnaigranja", "tabletoproleplay": "ston<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "kartonskeigrе", "eldritchhorror": "jez<PERSON><PERSON>", "switchboardgames": "igrezaprebacivanje", "infinitythegame": "igricabeskonacnost", "kingdomdeath": "smrtkraljevstva", "yahtzee": "j<PERSON><PERSON><PERSON>", "chutesandladders": "zmijeitoplice", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "društvenagra", "planszówki": "drustveneigre", "rednecklife": "seljačkiživot", "boardom": "dosada", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "društveneigre", "gameboard": "ploča_za_igru", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "krokinol", "jeuxdesociétés": "društveneigre", "twilightimperium": "twilightimperium", "horseopoly": "hors<PERSON><PERSON>", "deckbuilding": "gradnjašpila", "mansionsofmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "igrenastolu", "shadowsofbrimstone": "sjenebramstouna", "kingoftokyo": "kraljtokija", "warcaby": "warcaby", "táblajátékok": "društveneigre", "battleship": "ratnibrod", "tickettoride": "voznjadoavanture", "deskovehry": "drustveneigre", "catán": "catán", "subbuteo": "stonikonogomet", "jeuxdeplateau": "j<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "društvenéigre", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "jeuxsociete", "gesellschaftsspiele": "gesellschaftsspiele", "starwarslegion": "starwarslegion", "gochess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "društveneigre", "terraria": "terraria", "dsmp": "dsmp", "warzone": "warzone", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identityv", "theisle": "ostrvo", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "poz<PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON><PERSON><PERSON><PERSON>", "eco": "eko", "monkeyisland": "ostrvomajmuna", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "<PERSON><PERSON><PERSON><PERSON>", "fobia": "fobija", "witchit": "pretisamnom", "pathologic": "<PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dsz", "thelongdark": "<PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "prizemljen", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "ludiocac", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "vječnipovratak", "pathoftitans": "<PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "frictionalgames", "hexen": "heksiranje", "theevilwithin": "zlounama", "realrac": "realrac", "thebackrooms": "zadnjesobe", "backrooms": "pozadinskesobe", "empiressmp": "empiressmp", "blockstory": "blokp<PERSON>ča", "thequarry": "kamenolom", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "thewalkingdeadigra", "wehappyfew": "mi<PERSON><PERSON><PERSON><PERSON>", "riseofempires": "usponcarstava", "stateofsurvivalgame": "stateofsurvivaligra", "vintagestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "preživjelizapad", "beastsofbermuda": "zvjeribermude", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "hororpreživljavanja", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "praznivoz", "lifeaftergame": "životposljeutakmice", "survivalgames": "igrezapreživljavanje", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfondacija", "greenproject": "zeleniprojekt", "kuon": "kuon", "cryoffear": "plačemseodstraha", "raft": "splav", "rdo": "rdo", "greenhell": "zelenipakao", "residentevil5": "residentevil5", "deadpoly": "mrtavpoli", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "baka", "littlenightmares2": "malenocnemore2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "rustvideogejm", "outlasttrials": "preživiprobanja", "alienisolation": "izolacijavanzemaljaca", "undawn": "zora", "7day2die": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sunlesssea": "morebezsunca", "sopravvivenza": "preživljavanje", "propnight": "<PERSON><PERSON><PERSON><PERSON>", "deadisland2": "mrtvoostrvo2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "deathverse", "cataclysmdarkdays": "kataklizmamračnidani", "soma": "soma", "fearandhunger": "strahiglad", "stalkercieńczarnobyla": "stalkerčernob<PERSON><PERSON>", "lifeafter": "životposlije", "ageofdarkness": "d<PERSON><PERSON><PERSON>rkne", "clocktower3": "toranjsasatom3", "aloneinthedark": "samu<PERSON><PERSON>i", "medievaldynasty": "srednjovjekovnadinastija", "projectnimbusgame": "projectnimbusgame", "eternights": "vječnenoći", "craftopia": "zanatluk", "theoutlasttrials": "theoutlastpokusaji", "bunker": "bunker", "worlddomination": "vladanjemsvijeta", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "oficioassassinorum", "necron": "nekron", "wfrp": "wfrp", "dwarfslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kzaljubljenost", "wh40": "wh40", "warhammer40klove": "warhammer40kljubav", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "ilovesestrinstava", "ilovevindicare": "volimvindicare", "iloveassasinorum": "volimassasinorum", "templovenenum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40h", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "trebajudvoje", "wingspan": "rasponk<PERSON>a", "terraformingmars": "terraformingmarsa", "heroesofmightandmagic": "herojimocimagije", "btd6": "btd6", "supremecommander": "vrhovnikomandant", "ageofmythology": "eramitologije", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "protjeran", "caesar3": "cezar3", "redalert": "crvenialarm", "civilization6": "civilizacija6", "warcraft2": "warcraft2", "commandandconquer": "komandujiv<PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "vječnirat", "strategygames": "strate<PERSON><PERSON><PERSON><PERSON>", "anno2070": "anno2070", "civilizationgame": "igracivlizacije", "civilization4": "civilizacija4", "factorio": "factorio", "dungeondraft": "tamnicakartografija", "spore": "sport", "totalwar": "totalnirat", "travian": "travian", "forts": "tvr<PERSON><PERSON>", "goodcompany": "dobrodruštvo", "civ": "civ", "homeworld": "svijetza<PERSON>čaja", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "zakiraljeve", "realtimestrategy": "strategijauživo", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "kraljevstvodvijekrune", "eu4": "eu4", "vainglory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "božanstvo", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "<PERSON><PERSON><PERSON>", "davesfunalgebraclass": "daveovazabavničasalgebre", "plagueinc": "kugainc", "theorycraft": "<PERSON><PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "civilizacija3", "4inarow": "4zaredom", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "ageofempires2", "disciples2": "učenici2", "plantsvszombies": "plantsvszombies", "giochidistrategia": "strateškeigrе", "stratejioyunları": "strateškeigrе", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "dobačudesa", "dinosaurking": "dinosaurking", "worldconquest": "osvajanjesvijeta", "heartsofiron4": "srcagvozda4", "companyofheroes": "četniciheroji", "battleforwesnoth": "bitkazawesnoth", "aoe3": "aoe3", "forgeofempires": "carstvoimperija", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "guskaguskapatka", "phobies": "fobije", "phobiesgame": "igrafobija", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "<PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilizacija5", "victoria2": "victoria2", "crusaderkings": "kraljevi_krstaši", "cultris2": "cultris2", "spellcraft": "čarolija", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estrategija", "popfulmail": "pun<PERSON><PERSON><PERSON>", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "masterduel", "dysonsphereprogram": "programdysonovesfere", "transporttycoon": "transportmogul", "unrailed": "<PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "bijegizpakla", "uplandkingdoms": "planinekraljevstva", "galaxylife": "galaktičkiživot", "wolvesvilleonline": "vukogradnawebu", "slaythespire": "pokorišpil", "battlecats": "bitkemačaka", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "velik<PERSON><PERSON><PERSON>", "needforspeed": "potrebazabrzinu", "needforspeedcarbon": "potrebazabrzinukarbon", "realracing3": "stvarnetrke3", "trackmania": "trackmania", "grandtourismo": "velikat<PERSON><PERSON><PERSON>", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "gubsims4", "fnaf": "fnaf", "outlast": "izdržiprezivi", "deadbydaylight": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "alice<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "antologijatamnogkonja", "phasmophobia": "fobija_od_du<PERSON>a", "fivenightsatfreddys": "petvečerikodfredija", "saiko": "saiko", "fatalframe": "kobnikadar", "littlenightmares": "ma<PERSON>unoćnomore", "deadrising": "mrt<PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "kućnizatvorenici", "deadisland": "mrtviostrov", "litlemissfortune": "malanesretnica", "projectzero": "projekt<PERSON><PERSON>", "horory": "horory", "jogosterror": "joga<PERSON><PERSON>", "helloneighbor": "zdravokomšija", "helloneighbor2": "zdravokomšija2", "gamingdbd": "gamingdbd", "thecatlady": "mačkadama", "jeuxhorreur": "hororigre", "horrorgaming": "horrorigre", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "karteprocovjecanstva", "cribbage": "c<PERSON><PERSON><PERSON>", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pin<PERSON>le", "codenames": "<PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "juker", "thegwent": "thegwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "pasi<PERSON>s", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "trikovi_sa_kartama", "playingcards": "karte", "marvelsnap": "marvelsnap", "ginrummy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "poke<PERSON><PERSON>te", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "sportskarte", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "️pik", "warcry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "kraljsrca", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "otpor", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yug<PERSON>hkar<PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisc", "yugiohgame": "yugiohigra", "darkmagician": "crnimag", "blueeyeswhitedragon": "plaveokebijelizmaj", "yugiohgoat": "yugiohkoza", "briscas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegocartas": "j<PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "remi", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "d<PERSON>jnas<PERSON>doba", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "dekartoveigre", "mtgjudge": "mtgjudge", "juegosdecartas": "kartaškeigreboo", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "duhoviborbe", "battlespiritssaga": "saga<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "<PERSON>ž<PERSON><PERSON>", "facecard": "lice", "cardfight": "karta<PERSON><PERSON>_borba", "biriba": "biriba", "deckbuilders": "<PERSON>rad<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "marvelšampioni", "magiccartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "nestabilnijednorog", "cyberse": "sajber", "classicarcadegames": "klasičnearkadneigre", "osu": "osu", "gitadora": "gitadora", "dancegames": "igreplesanja", "fridaynightfunkin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projektmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "gitarskiheroj", "clonehero": "klonheroj", "justdance": "<PERSON><PERSON><PERSON><PERSON>", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rokajmrtve", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "plesnicentar", "rhythmgamer": "ritmgejmer", "stepmania": "stepmania", "highscorerythmgames": "najboljiritmigre", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "ritamnebeske", "hypmic": "hypmic", "adanceoffireandice": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "onlineaudicija", "itgmania": "itgmanija", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "doktorzaritam", "cubing": "kockanje", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "igreslagalica", "spotit": "uocito", "rummikub": "remi", "blockdoku": "blokdoku", "logicpuzzles": "logičkezagonetke", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikscube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossword": "ukrstenice", "motscroisés": "ukrštenice", "krzyżówki": "ukrštene", "nonogram": "nonogram", "bookworm": "knjiškimoljac", "jigsawpuzzles": "slagalice", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "zagonetka", "riddles": "zagonetke", "rompecabezas": "slagalica", "tekateki": "tekateki", "inside": "unutra", "angrybirds": "ljutiteptice", "escapesimulator": "simulatorbjegova", "minesweeper": "minepolje", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "ukrštenerječi", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesigra", "puzzlesport": "puzzlesport", "escaperoomgames": "escaperoomigre", "escapegame": "igricabježanja", "3dpuzzle": "3dpuzla", "homescapesgame": "homescapesgame", "wordsearch": "potragazariječima", "enigmistica": "enigmatika", "kulaworld": "svijet<PERSON><PERSON>", "myst": "mist", "riddletales": "zagonetkeprice", "fishdom": "fishdom", "theimpossiblequiz": "nemogućik<PERSON>z", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "slaganje3puzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "kvirkasto", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yapboz": "<PERSON><PERSON><PERSON><PERSON>", "thetalosprinciple": "principta<PERSON><PERSON>", "homescapes": "kućnipejzaži", "puttputt": "minigolf", "qbert": "qbert", "riddleme": "zagonetajmi", "tycoongames": "tajkunigre", "cubosderubik": "cubosderubik", "cruciverba": "k<PERSON>ž<PERSON><PERSON><PERSON><PERSON>", "ciphers": "<PERSON><PERSON><PERSON>", "rätselwörter": "zagonetkerijeci", "buscaminas": "minesweeper", "puzzlesolving": "rješavanjezagonetki", "turnipboy": "<PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "niko", "guessing": "pogađanje", "nonograms": "nonogrami", "kostkirubika": "kostkirubika", "crypticcrosswords": "kriptičneukrštenerijeci", "syberia2": "sibir2", "puzzlehunt": "potragazazagonetkom", "puzzlehunts": "potragezazagonetke", "catcrime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "slagalica", "hlavolamy": "mozgalice", "poptropica": "poptropica", "thelastcampfire": "poslednjalogorskavatra", "autodefinidos": "samodefinisani", "picopark": "picopark", "wandersong": "wandersongpjesma", "carto": "carto", "untitledgoosegame": "bezimenapatkigra", "cassetête": "glavolomka", "limbo": "limbo", "rubiks": "rubiks", "maze": "lavirint", "tinykin": "tinykin", "rubikovakostka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "speedcube": "speedcube", "pieces": "komadi", "portalgame": "portaligra", "bilmece": "zagagonetka", "puzzelen": "puzzelen", "picross": "pikros", "rubixcube": "rub<PERSON><PERSON>_kocka", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "rub<PERSON><PERSON>_kocka", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "uvrnutazemljačudesa", "monopoly": "monopoli", "futurefight": "bud<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlzvijezda", "coc": "coc", "lonewolf": "vuksamotnjak", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "životnaigra", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemystars", "stateofsurvival": "stanjeprezivljavanja", "mycity": "mojgrad", "arknights": "arknights", "colorfulstage": "šarenascena", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hiper<PERSON><PERSON><PERSON>", "knightrun": "viteskatrka", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "fudbalskaborbazabodove", "a3": "a3", "phonegames": "igrezatelefon", "kingschoice": "kraljevskiizbor", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "benzinac", "tacticool": "takticool", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON>", "craftsman": "zanatlija", "supersus": "<PERSON><PERSON><PERSON><PERSON>", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "pazi", "wordfeud": "wordfeud", "bedwars": "bedwars", "freefire": "slobodnavatra", "mobilegaming": "mobilnogejming", "lilysgarden": "lilyjevrt", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "misticname<PERSON><PERSON>", "callofdutymobile": "callofdutymobile", "thearcana": "arkane", "8ballpool": "bilijarkugle8", "emergencyhq": "hitnipoziv", "enstars": "enstars", "randonautica": "<PERSON><PERSON><PERSON><PERSON>", "maplestory": "maplestory", "albion": "albion", "hayday": "zlatnodoba", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegenda", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "ljubavudzepua", "androidgames": "androidigre", "criminalcase": "kriminalistič<PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "kvizomanija", "leagueofangels": "ligaanđela", "lordsmobile": "lordsmobile", "tinybirdgarden": "malabašticaptica", "gachalife": "gachalife", "neuralcloud": "neuralnioблак", "mysingingmonsters": "mojepjevaju<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "p<PERSON><PERSON><PERSON>", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "<PERSON><PERSON><PERSON>", "antiyoy": "protivtebeoy", "apexlegendmobile": "apexlegendmobile", "ingress": "ingress", "slugitout": "b<PERSON>m<PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "kažnjavanjusivigavran", "petpals": "l<PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "igrasultana", "arenabreakout": "arenabreakout", "wolfy": "vuč<PERSON>", "runcitygame": "igragradatrčanje", "juegodemovil": "mobilnaigra", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "toboganmagnata", "grandchase": "grandchase", "bombmebrasil": "bombardujmebrazil", "ldoe": "ldoe", "legendonline": "legendaonline", "otomegame": "otomeg<PERSON>", "mindustry": "mindustry", "callofdragons": "pozivzmajeva", "shiningnikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "<PERSON><PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolišnderbi3", "wordswithfriends2": "wordsprijateljima2", "soulknight": "v<PERSON>z<PERSON>š<PERSON>", "purrfecttale": "purr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showbyrock": "showbyrock", "ladypopular": "<PERSON><PERSON><PERSON><PERSON>", "lolmobile": "lolmobitel", "harvesttown": "žetvenigrad", "perfectworldmobile": "svijetsavršenmobilni", "empiresandpuzzles": "carstvaiigrice", "empirespuzzles": "slag<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "zmajskigrad", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileindia", "fanny": "guza", "littlenightmare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "blatnjak", "tearsofthemis": "suzetemine", "eversoul": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombisispisanici", "eveechoes": "eveechoes", "jogocelular": "mobilnaigra", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "streetfighterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ron<PERSON>", "jurassicworldalive": "jurassicworlduzivo", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "prekidamprošlost", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "pricanašemjeseca", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "jogosmobile", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "igremobilelegends", "timeraiders": "vremenskipljačkaši", "gamingmobile": "mobilnogejming", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "bitkazamač<PERSON>", "dnd": "dnd", "quest": "kvest", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgzastolom", "worldofdarkness": "svijettame", "travellerttrpg": "putnikttrpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "romansaklub", "d20": "d20", "pokemongames": "pokemonigre", "pokemonmysterydungeon": "pokemonmisterijatamnica", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemonkristal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonred", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "usneee", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hip<PERSON><PERSON>", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "razgovor", "pikachu": "<PERSON><PERSON><PERSON><PERSON>", "roxie": "roxie", "pokemonviolet": "pokemonljubičasto", "pokemonpurpura": "pokemonljubičasta", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "t<PERSON><PERSON><PERSON>", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "džepnimonstrumi", "nuzlocke": "nuzlocke", "pokemonplush": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "gvozdeneruke", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokemonmajstor", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "dje<PERSON>_i_pokemon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "šarmirati", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "<PERSON>ah", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "<PERSON>ah", "schaken": "<PERSON>ah", "skak": "skak", "ajedres": "šahovi", "chessgirls": "šahistkinje", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "svjetskiblic", "jeudéchecs": "<PERSON>ah", "japanesechess": "japanskišah", "chinesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "šah<PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rook": "top", "chesscom": "chesscom", "calabozosydragones": "tamniceidragoni", "dungeonsanddragon": "tamniceizmajevi", "dungeonmaster": "dungeonmaster", "tiamat": "tiamat", "donjonsetdragons": "tamniceidragoni", "oxventure": "oxventure", "darksun": "tamnosunce", "thelegendofvoxmachina": "legendaovox<PERSON><PERSON><PERSON>", "doungenoanddragons": "dungeonsanddragons", "darkmoor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "minecraftšampionat", "minecrafthive": "minecraftkosnica", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodovi", "mcc": "mcc", "candleflame": "plamensvijece", "fru": "fru", "addons": "<PERSON><PERSON><PERSON>", "mcpeaddons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "modiraniminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "međuze<PERSON>l<PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftgrad", "pcgamer": "pc<PERSON><PERSON><PERSON>", "jeuxvideo": "jeuxvideo", "gambit": "gambit", "gamers": "g<PERSON><PERSON><PERSON>", "levelup": "napredovanje", "gamermobile": "gamernamobilu", "gameover": "gotovoje", "gg": "gg", "pcgaming": "pcigre", "gamen": "gejming", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "pcigre", "casualgaming": "ležernogejming", "gamingsetup": "gejmskapostava", "pcmasterrace": "pc<PERSON><PERSON><PERSON>", "pcgame": "pcigrica", "gamerboy": "gejmerska_faca", "vrgaming": "vrigre", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerba", "gameplays": "g<PERSON><PERSON><PERSON>", "consoleplayer": "konzolaš", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgejmeri", "onlinegaming": "onlinegaming", "semigamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamergirls": "gejmergirls", "gamermoms": "gejmermame", "gamerguy": "gejmer", "gamewatcher": "gejm<PERSON>č", "gameur": "gejmer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gejmerkeuše", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "teamnapornaguranje", "mallugaming": "mallugaming", "pawgers": "šapičari", "quests": "kvest", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON>", "cozygaming": "ugodnogejming", "gamelpay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepc": "<PERSON>grezap<PERSON>", "dsswitch": "dsmijenjam", "competitivegaming": "kompetitivnogejming", "minecraftnewjersey": "minecraftnewjersey", "faker": "lažnjak", "pc4gamers": "pc4gejmeri", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heteroseksualnogejming", "gamepc": "gamepc", "girlsgamer": "gejmericedjevojke", "fnfmods": "fnfmodovi", "dailyquest": "dnevnizadatak", "gamegirl": "gejmerka", "chicasgamer": "gejmericee", "gamesetup": "postavkeigre", "overpowered": "prejaksmokrcat", "socialgamer": "društvenigejmer", "gamejam": "gamejam", "proplayer": "<PERSON><PERSON><PERSON><PERSON>", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "mojteam", "republicofgamers": "republikagejmera", "aorus": "aorus", "cougargaming": "kugarejming", "triplelegend": "trostr<PERSON><PERSON><PERSON>a", "gamerbuddies": "gamerbuddies", "butuhcewekgamers": "trebamgejmerku", "christiangamer": "hrišćanskigejmer", "gamernerd": "gejmeršteber", "nerdgamer": "nerd<PERSON>j<PERSON>", "afk": "afk", "andregamer": "andregamer", "casualgamer": "ležernigejmer", "89squad": "89ek<PERSON><PERSON>", "inicaramainnyagimana": "inicijaramainjanjegimana", "insec": "<PERSON><PERSON><PERSON>", "gemers": "g<PERSON><PERSON><PERSON>", "oyunizlemek": "gledanjeigara", "gamertag": "gejmertag", "lanparty": "lanžurka", "videogamer": "gejmer", "wspólnegranie": "zajedničkoigranje", "mortdog": "mortdog", "playstationgamer": "playstationgejmer", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "zdravigejmer", "gtracing": "gtracing", "notebookgamer": "notebookgejmer", "protogen": "protogen", "womangamer": "gejmerica", "obviouslyimagamer": "naravnodajamgejmer", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "humanfallflat": "lju<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "nultiizlaz", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomuzika", "sonicthehedgehog": "son<PERSON><PERSON><PERSON><PERSON>", "sonic": "sonik", "fallguys": "fallguys", "switch": "prebaci", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megamen", "majorasmask": "<PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "mariokartmajstor", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "nebeskadjeca_svjetlosti", "tomodachilife": "tomodachilife", "ahatintime": "šeširuvremenu", "tearsofthekingdom": "sljedbenikraljevstva", "walkingsimulators": "simulatorišet<PERSON><PERSON>", "nintendogames": "nintendoigre", "thelegendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "dragonquest": "dragonquest", "harvestmoon": "žetvenimjesec", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "dah_divljine", "myfriendpedro": "mojprijateljpedro", "legendsofzelda": "legende_<PERSON>_<PERSON><PERSON>i", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51igra", "earthbound": "prizemljen", "tales": "prič<PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "luigijevozdanje", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendočile", "tloz": "tloz", "trianglestrategy": "trojugaostrategija", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "groznapetnicaukonkersu", "nintendos": "nintendosi", "new3ds": "novi3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "ratnicihyrulea", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioisonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "ma<PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "ligalegendilas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vanillalol": "van<PERSON><PERSON>l", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "ligalegendi", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "<PERSON><PERSON><PERSON>", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "ligalegendiišpanija", "aatrox": "aatrox", "euw": "jao", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lolcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligalegendi", "gaminglol": "g<PERSON><PERSON><PERSON>l", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON>ks<PERSON>j<PERSON>", "hextech": "hekstehnologija", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideogamnice", "scaryvideogames": "strasnevideoigre", "videogamemaker": "kreatorvideoigara", "megamanzero": "megamanzero", "videogame": "videoigra", "videosgame": "videosgejm", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "arkade", "acnh": "acnh", "puffpals": "pu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "simulatorpol<PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "robloxnjemački", "erlc": "erlc", "sanboxgames": "sanboxigre", "videogamelore": "videoigrice", "rollerdrome": "rollerdrom", "parasiteeve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "sanjarenje", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "mrtviprostor", "amordoce": "amorslatka", "videogiochi": "videoigre", "theoldrepublic": "stararepublika", "videospiele": "videoigre", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "avanturisti<PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "akcijskaavantura", "storyofseasons": "pričaodišnjihdoba", "retrogames": "retrogej<PERSON><PERSON>", "retroarcade": "retroarkade", "vintagecomputing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogaming": "retrogejming", "vintagegaming": "vintagegejming", "playdate": "<PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "nepravda2", "shadowthehedgehog": "<PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "igraneba", "zenlife": "zenživot", "beatmaniaiidx": "beatmaniaiidx", "steep": "strmo", "mystgames": "misteriozneigre", "blockchaingaming": "blockchainigranje", "medievil": "srednjovjekovlje", "consolegaming": "konzolegejming", "konsolen": "konzole", "outrun": "<PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "cvijetapanika", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "hororbalkan", "monstergirlquest": "questdjevojk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiant": "<PERSON><PERSON><PERSON><PERSON>", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "farmingsimulacije", "juegosviejos": "starigejms", "bethesda": "bethesda", "jackboxgames": "jackboxigre", "interactivefiction": "interaktivnafikcija", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantepribezbrigu", "visualnovel": "viz<PERSON><PERSON><PERSON>", "visualnovels": "vizuelneromane", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON>", "tcrghost": "tcrduh", "payday": "platnilist", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "<PERSON>zes<PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "pesak", "aestheticgames": "estetskeigrе", "novelavisual": "viz<PERSON><PERSON><PERSON>", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogejm", "tonyhawkproskater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smbz": "smbz", "lamento": "<PERSON><PERSON><PERSON>", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "revolucijaduva<PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "dizajnnivoa", "starrail": "starrail", "keyblade": "keyblade", "aplaguetale": "kuga", "fnafsometimes": "fnafsometimes", "novelasvisuales": "vizuelnenevele", "robloxbrasil": "robloxbrazil", "pacman": "pacman", "gameretro": "retrogejming", "videojuejos": "videoju<PERSON><PERSON>", "videogamedates": "videogamedejts", "mycandylove": "mojaljubavslatkisa", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulkigre", "batmangames": "batmenigre", "returnofreckoning": "povratakobračuna", "gamstergaming": "gamstergaming", "dayofthetantacle": "dan<PERSON><PERSON><PERSON>", "maniacmansion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crashracing": "trkeudaranje", "3dplatformers": "3dplatformeri", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "oldschoolgaming", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "priče_igre", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "izbegavanjezvuka", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "koristeig<PERSON>", "offmortisghost": "offmortisghost", "tinybunny": "malizekon<PERSON>", "retroarch": "retroarch", "powerup": "nadogradi", "katanazero": "katana<PERSON><PERSON>", "famicom": "famicom", "aventurasgraficas": "aventurasgraficas", "quickflash": "brziblic", "fzero": "fzero", "gachagaming": "gachai<PERSON><PERSON><PERSON>", "retroarcades": "retroarkade", "f123": "f123", "wasteland": "pustinja", "powerwashsim": "simulatorpranjapritiskom", "coralisland": "koralnoostrvo", "syberia3": "siberija3", "grymmorpg": "mračnimorpg", "bloxfruit": "bloxfruit", "anotherworld": "drugisvjet", "metaquest": "metaquest", "animewarrios2": "animeratnici2", "footballfusion": "fuzijafudbala", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "uvrnutimetal", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simulator", "symulatory": "simulatori", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "samima<PERSON>", "grywideo": "videoigre", "gaiaonline": "gaiaonline", "korkuoyunu": "hororigra", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "too<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simrace": "simtrka", "pvp": "pvp", "urbanchaos": "grad<PERSON><PERSON><PERSON>", "heavenlybodies": "božanskatijelа", "seum": "ljutnja", "partyvideogames": "partyvideogameice", "graveyardkeeper": "čuvargroblja", "spaceflightsimulator": "simulator<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "hranaivideigre", "oyunvideoları": "oyunvideoları", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "simulator<PERSON><PERSON><PERSON>", "horizonworlds": "horizonsvjetovi", "handygame": "priručnaigrica", "leyendasyvideojuegos": "legendeivideigre", "oldschoolvideogames": "starevideogrice", "racingsimulator": "simulatortrka", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentipohemetežа", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "ka<PERSON><PERSON>oli<PERSON>", "monsterhunternow": "monsterhuntersad", "rebelstar": "buntovnazvijezda", "indievideogaming": "indievideogejming", "indiegaming": "indiegaming", "indievideogames": "indievideoigre", "indievideogame": "indievideogame", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermanbezsan", "bufffortress": "bufffestung", "unbeatable": "nepobjediv", "projectl": "projekat", "futureclubgames": "igrezabuduciklub", "mugman": "<PERSON><PERSON><PERSON><PERSON>", "insomniacgames": "igrebezspavanja", "supergiantgames": "superdžinovskjeigre", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celesteigra", "aperturescience": "aperturescience", "backlog": "zaliha", "gamebacklog": "gamebacklog", "gamingbacklog": "gejmerska_zaliha", "personnagejeuxvidéos": "likizigrica", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "gradskihorizonti", "supermonkeyball": "supermajmunskalopta", "deponia": "deponia", "naughtydog": "<PERSON><PERSON>š<PERSON><PERSON>", "beastlord": "zvjeroljubac", "juegosretro": "retrogejming", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "rezervoardopamina", "staxel": "staxel", "videogameost": "videogamesoundtrack", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "volimkofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pctrke", "berserk": "ludilo", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "tužnianime", "darkerthanblack": "crnjeoddcrnog", "animescaling": "animeskaliranje", "animewithplot": "animesaradnjom", "pesci": "pesci", "retroanime": "retroanime", "animes": "animei", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "80<PERSON><PERSON>me", "90sanime": "90<PERSON>me", "darklord": "lordtame", "popeetheperformer": "popapevaperformer", "masterpogi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "2000anime", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesezona1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "animecover", "thevisionofescaflowne": "vizijaescaflownea", "slayers": "ubice", "tokyomajin": "tokyomajin", "anime90s": "anime90ih", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "bananarib", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "vatrenesnage", "moriartythepatriot": "moriart<PERSON><PERSON><PERSON>ot", "futurediary": "bud<PERSON><PERSON>idnev<PERSON>", "fairytail": "bajka", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parasyte": "<PERSON><PERSON><PERSON>", "punpun": "<PERSON><PERSON><PERSON><PERSON>", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "sirenamelodia", "kamisamakiss": "poljubackamija", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "ljubavnimange", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "crnal<PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformari", "geniusinc": "g<PERSON><PERSON><PERSON><PERSON>", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "određenimagičniindeks", "sao": "sao", "blackclover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportskianime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "a<PERSON><PERSON><PERSON>_igra", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "saga<PERSON><PERSON>j<PERSON><PERSON>", "shounenanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanja", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "vuzetsjevernezvijezde", "mazinger": "mazinger", "blackbuttler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "towerofgod", "elfenlied": "vilinskapovijest", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "<PERSON><PERSON>", "servamp": "servamp", "howtokeepamummy": "kakozadržatimamicu", "fullmoonwosagashite": "punnomjeseczatrazim", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "slatkoijezivo", "martialpeak": "borilačkivrh", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "š<PERSON>ž<PERSON>", "zerotwo": "nuladvoje", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "de<PERSON>jk<PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloj", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON><PERSON>", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "crnimajordomo", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horo<PERSON><PERSON>", "fruitsbasket": "korpas<PERSON>ća", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "ljubavuživo", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "samučedavimstranskijezik", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "obećana<PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermanga": "monstermanga", "yourlieinapril": "tvojaaprilskalaž", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "zatvorenikdubokogmora", "jojolion": "jojo<PERSON>", "deadmanwonderland": "zemljačudamrvih", "bannafish": "bananafish", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "muž", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "pandorinasrca", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "linijadjavola", "toyoureternity": "zauvijektvoj", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "plavaperioda", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "izbrisano", "bluelock": "plavozaključanje", "goblinslayer": "ubicagoblina", "detectiveconan": "detektivconan", "shiki": "šiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampirskivitez", "mugi": "mugi", "blueexorcist": "plavi_egzorcist", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "oplakao", "spyfamily": "spyfamily", "airgear": "vazdušnaoprema", "magicalgirl": "magič<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "sedamsmrtnihg<PERSON><PERSON><PERSON><PERSON>", "prisonschool": "zatvorskaskola", "thegodofhighschool": "thegodofsrednjeskole", "kissxsis": "poljubacxseka", "grandblue": "plavoplav<PERSON>", "mydressupdarling": "mojadraganapra<PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeuniverz", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "nesrećaneumrlih", "romancemanga": "romantikamanga", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromansa", "senpai": "senpai", "blmanhwas": "b<PERSON>hve", "animeargentina": "animeargentina", "lolicon": "lolikon", "demonslayertothesword": "demonslayerdokorica", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "adioseri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "zvijezdeseslažu", "romanceanime": "romanticnianime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfaktor", "cherrymagic": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "snimaj<PERSON><PERSON><PERSON>", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "srednjaškolamrtvih", "germantechno": "njemačkitehno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prin<PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "razredubica", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "smrtnaparada", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japanskaanime", "animespace": "animeprostor", "girlsundpanzer": "djevojkeipanzeri", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON>", "animedub": "animedab", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "nosačuq", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "štakor", "haremanime": "harem<PERSON><PERSON>", "kochikame": "kochikame", "nekoboy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gashbell": "gashbell", "peachgirl": "breskvadjevojka", "cavalieridellozodiaco": "kavaljerizodijaka", "mechamusume": "mehađevojke", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "jaričinbitčklub", "dragonquestdai": "dragonquestdai", "heartofmanga": "srcemange", "deliciousindungeon": "ukusnoutamnici", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "zapisragnaroka", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "preskočinamokas<PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "rejvmajstor", "kkondae": "kkondae", "chobits": "čobits", "witchhatatelier": "ateljezvještičjihš<PERSON>šira", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangaježivot", "dropsofgod": "kapibožje", "loscaballerosdelzodia": "vitezovizodijaka", "animeshojo": "<PERSON>š<PERSON>jo", "reverseharem": "obrnutharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "velikučiteljonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "mojšefetata", "gear5": "oprema5", "grandbluedreaming": "velik<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "krvavecanime", "bloodc": "krvnag", "talesofdemonsandgods": "prič<PERSON>_o_demonima_i_bogovima", "goreanime": "goreanime", "animegirls": "animecure", "sharingan": "<PERSON><PERSON>", "crowsxworst": "vranexnajgore", "splatteranime": "anime_krv_svugdje", "splatter": "sljapkanje", "risingoftheshieldhero": "uspo<PERSON><PERSON><PERSON><PERSON>", "somalianime": "somal<PERSON>j<PERSON><PERSON><PERSON>", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "zauzetsmeljem", "animeyuri": "<PERSON><PERSON><PERSON>", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "dje<PERSON>_kitova", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "superkampioni", "animeidols": "<PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "zelenidani", "magicalgirls": "čarobnedevojke", "callofthenight": "noćnipoziv", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "magič<PERSON>d<PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "vrtsjene", "tsubasachronicle": "tsubasachronicle", "findermanga": "<PERSON>na<PERSON><PERSON><PERSON>", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradajzpoljubac", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animesvemir", "persocoms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "omniscientreadersview": "sveznaoc<PERSON><PERSON>ev<PERSON>gled", "animecat": "animemačka", "animerecommendations": "animp<PERSON><PERSON><PERSON>", "openinganime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinichirowatanabe": "šiniičirovatanable", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "moja<PERSON>_romanticna_komedija", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundami", "voltesv": "<PERSON><PERSON><PERSON>", "giantrobots": "divovskiroboți", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "meh", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanimu", "bleach": "b<PERSON><PERSON><PERSON>", "deathnote": "biljeskasmrti", "cowboybebop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarnaavantura", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "vojskaanime", "greenranger": "zele<PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zoro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "zvjezdanalisica", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animecity", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiecee", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonavantura", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "animešibalice", "bokunoheroacademia": "mojaheroakademija", "jujustukaitsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drstone": "drkamen", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaku", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "ubicalavova", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "napadnatitane", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onepieceanime": "onepieceanime", "attaquedestitans": "nap<PERSON><PERSON><PERSON>", "theonepieceisreal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "revengers": "osvetnici", "mobpsycho": "<PERSON><PERSON><PERSON><PERSON>", "aonoexorcist": "aoegzorcista", "joyboyeffect": "efektjoyboya", "digimonstory": "pričeodigimonima", "digimontamers": "dig<PERSON><PERSON><PERSON><PERSON>", "superjail": "superzatvor", "metalocalypse": "metalocalypse", "shinchan": "<PERSON><PERSON><PERSON><PERSON>", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "besprekornowebtoonce", "kemonofriends": "kemonoprijatelji", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurukamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "letećavještica", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON>ko", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "svesvetačkaaulica", "recuentosdelavida": "pri<PERSON><PERSON>_iz_života"}