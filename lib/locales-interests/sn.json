{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "n<PERSON><PERSON><PERSON>", "cognitivefunctions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychology": "psychology", "philosophy": "<PERSON><PERSON><PERSON>", "history": "n<PERSON><PERSON><PERSON>", "physics": "<PERSON><PERSON><PERSON><PERSON>", "science": "saiyenzi", "culture": "tsika", "languages": "<PERSON><PERSON><PERSON>", "technology": "teknoloji", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "masvikirondomamemes", "enneagrammemes": "enneagrammemes", "showerthoughts": "pfungazvandiriseshower", "funny": "zvopfidza", "videos": "mav<PERSON><PERSON><PERSON>", "gadgets": "zvigadzeti", "politics": "zvematongerwo", "relationshipadvice": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "zanod<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>yu", "crypto": "crypto", "news": "nhau", "worldnews": "nhedzidzepasi", "archaeology": "archaeology", "learning": "kudzidza", "debates": "<PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "dzidzisozvinopokana", "universe": "pasi<PERSON>e", "meditation": "kufungisisa", "mythology": "ngano", "art": "<PERSON><PERSON><PERSON><PERSON>", "crafts": "ma<PERSON>a_em<PERSON>ko", "dance": "danzi", "design": "<PERSON><PERSON><PERSON><PERSON>", "makeup": "zvekuzora", "beauty": "runako", "fashion": "fashoni", "singing": "kuimba", "writing": "kunyora", "photography": "mafoto", "cosplay": "cosplay", "painting": "kupenda", "drawing": "kutema", "books": "ma<PERSON><PERSON><PERSON>", "movies": "<PERSON><PERSON><PERSON><PERSON>", "poetry": "nhetembo", "television": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filmmaking": "zvekugadziramafirimu", "animation": "animation", "anime": "anime", "scifi": "scifi", "fantasy": "fantasy", "documentaries": "madocumentary", "mystery": "chakavanzika", "comedy": "<PERSON><PERSON><PERSON>", "crime": "uts<PERSON>i", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "<PERSON><PERSON><PERSON>", "romance": "rudo", "realitytv": "nhepfenyurotv", "action": "chiito", "music": "muziki", "blues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "nyika", "desi": "desi", "edm": "edm", "electronic": "em<PERSON><PERSON><PERSON>", "folk": "<PERSON><PERSON>", "funk": "funk", "hiphop": "hiphop", "house": "imba", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "simbi", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "dombo", "techno": "techno", "travel": "kuf<PERSON><PERSON>", "concerts": "<PERSON><PERSON><PERSON><PERSON>", "festivals": "mhemberero", "museums": "mamuseum", "standup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theater": "theatre", "outdoors": "panze", "gardening": "kurima", "partying": "kup<PERSON>", "gaming": "<PERSON><PERSON><PERSON>", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "dungeonsanddragons", "chess": "chess", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "chikafu", "baking": "kubika", "cooking": "kubika", "vegetarian": "vegetarian", "vegan": "chika<PERSON>_chisina_nyama", "birds": "shiri", "cats": "<PERSON><PERSON><PERSON>", "dogs": "imbwa", "fish": "hove", "animals": "mhuka", "blacklivesmatter": "hupenyuhwevatemahwakakosha", "environmentalism": "kuchengetedzazvakatikomberedza", "feminism": "feminism", "humanrights": "kodzerodzivanhu", "lgbtqally": "lgbt<PERSON><PERSON><PERSON><PERSON><PERSON>", "stopasianhate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "volunteering": "kuz<PERSON><PERSON><PERSON>", "sports": "<PERSON><PERSON><PERSON>", "badminton": "b<PERSON><PERSON><PERSON><PERSON>", "baseball": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "basketball": "basketball", "boxing": "b<PERSON><PERSON><PERSON>", "cricket": "kiriketi", "cycling": "<PERSON><PERSON><PERSON><PERSON>", "fitness": "fiti", "football": "nhabvu", "golf": "gorofu", "gym": "jimu", "gymnastics": "gymnastics", "hockey": "hockey", "martialarts": "zvekurwa", "netball": "netbhora", "pilates": "pilates", "pingpong": "pingpong", "running": "k<PERSON><PERSON><PERSON>", "skateboarding": "skateboarding", "skiing": "kutsvedza", "snowboarding": "kus<PERSON><PERSON>_muchando", "surfing": "k<PERSON><PERSON><PERSON>", "swimming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tennis": "tennis", "volleyball": "nhabvu", "weightlifting": "kus<PERSON><PERSON>za_zvinorema", "yoga": "yoga", "scubadiving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiking": "kuf<PERSON><PERSON>_mumakomo", "capricorn": "nyer<PERSON><PERSON>_ye<PERSON><PERSON><PERSON>", "aquarius": "aquarius", "pisces": "pisces", "aries": "nyered<PERSON>_ye<PERSON>ye", "taurus": "nyati", "gemini": "gemini", "cancer": "<PERSON><PERSON><PERSON>", "leo": "leo", "virgo": "virgo", "libra": "libra", "scorpio": "chisingakure", "sagittarius": "sagittarius", "shortterm": "<PERSON>wen<PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "zvisina_kunyanya", "longtermrelationship": "hukamabatanidzerefu", "single": "musina", "polyamory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "<PERSON><PERSON><PERSON>", "lesbian": "<PERSON><PERSON><PERSON>", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "imbwazvinogariris<PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "mutambowehumambo", "soulreaver": "mup<PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "nganoeyaspyro", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "r<PERSON><PERSON><PERSON>", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "nyikapasi", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "kuf<PERSON><PERSON>_mum<PERSON><PERSON>a", "jetsetradio": "jetsetradio", "tribesofmidgard": "ma<PERSON><PERSON>_em<PERSON><PERSON>", "planescape": "nzvirayendege", "lordsoftherealm2": "vashe_venyika2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "colorvore", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "zvinokunyudzamuzvirongwa", "okage": "okage", "juegoderol": "mutambowenyaya", "witcher": "witcher", "dishonored": "kunyadziswa", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "kush<PERSON><PERSON>", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "kugad<PERSON>rid<PERSON>", "charactercreation": "kugadziravatambo", "immersive": "zvakanyura", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "kufarakudzora", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "ndichit<PERSON><PERSON>_nerudo", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "nyikadzinemafuta", "pathfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "kum<PERSON><PERSON>_murima", "bloodontheclocktower": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "k<PERSON><PERSON><PERSON>muchadenga", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "kamwechete", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "mutongi", "yourturntodie": "ndiwezvino", "persona3": "persona3", "rpghorror": "rpgzvinotyisa", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "vapambi", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "zvinyorwarpg", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mu": "mu", "falloutshelter": "dumbareng<PERSON>d<PERSON>", "gurps": "gurps", "darkestdungeon": "ng<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "nyikazvekunze", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skullgirls": "skullgirls", "nightcity": "guta_reusiku", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "hondoyeupengo", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothamknights": "gothamknights", "forgottenrealms": "nyikazviwakanwa", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vakomana2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "mumvurirebel", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "chakavan<PERSON><PERSON>hog<PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "rova", "lastepoch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starfinder": "nyenyedzitsvagi", "goldensun": "zuvasvoima", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "mapfumoeri<PERSON>", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "aka<PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "nyikadzinoipawo", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "mwari", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "zvekarezvenyika", "adventurequest": "rwendo", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "ngandzedzesymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfarog": "myfarog", "sacredunderworld": "nzvimboyepasi", "chainedechoes": "nzwidzadzakabatana", "darksoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "soulslikes", "othercide": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "mountandblade": "ngororomhindomukondo", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "nguvadzinokonzera", "pillarsofeternity": "m<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "kukakavadzana", "tibia": "tibia", "thedivision": "bato", "hellocharlotte": "salicharlotte", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werewolfapocalypse", "aveyond": "aveyond", "littlewood": "dogogonya", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON>", "engineheart": "moyo_wein<PERSON>i", "fable3": "ngano3", "fablethelostchapter": "nganoirechapterrarasika", "hiveswap": "hiveswap", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "n<PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "kudzokakwezvekare", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "nyikadzinotyisa", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "mitamborpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "harvestella", "gloomhaven": "gumbonhamo", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowhearts": "moyo_we<PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "mutamwedziamoto4", "mother3": "amai3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "rimwepasichando", "roleplaygames": "mitamboyekuedzesera", "roleplaygame": "mutamboroleplay", "fabulaultima": "zvakanyanyisazve", "witchsheart": "moyo_<PERSON><PERSON><PERSON>i", "harrypottergame": "muta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vamp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cocttrpg": "cocttrpg", "huntroyale": "kuvendachiroyale", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "awplanet": "<PERSON><PERSON><PERSON><PERSON>", "theworldendswithyou": "nyikaiperapamwenemwe", "dragalialost": "dragalialost", "elderscroll": "vakuruvakakomba", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shoptitans": "tezvekutengesa", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "ma<PERSON><PERSON><PERSON><PERSON>", "blackbook": "b<PERSON><PERSON><PERSON>", "skychildrenoflight": "<PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "dzorokegolde", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "mutambowegothic", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "mutambowema<PERSON><PERSON><PERSON>a", "prophunt": "kuvendachiprofita", "starrails": "n<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "cityofmist": "guta_remhute", "indierpg": "indierpg", "pointandclick": "bayay<PERSON><PERSON>", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freeside": "dzemahara", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "mushureumeaftersaibapunk", "deathroadtocanada": "nzirainorufuyekucanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremancy", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "mitamboyavasirivarume", "tacticalrpg": "mutambowemanyorerwa", "mahoyo": "mahoyo", "animegames": "mitam<PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "ruzisingaperi", "princessconnect": "princessconnect", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "homwechenjere", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantmuindia", "dota": "dota", "madden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "nhabvu", "dreamhack": "kurota", "gaimin": "tichitambe", "overwatchleague": "ligayeoverwatch", "cybersport": "mit<PERSON><PERSON>emakomb<PERSON><PERSON><PERSON>", "crazyraccoon": "rakunapengo", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "b<PERSON><PERSON>i", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantmakwik<PERSON>", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON>", "left4dead": "vakasara4vakafa", "left4dead2": "left4dead2", "valve": "v<PERSON><PERSON><PERSON>", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "mbudzi_simulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "nyikayakasununguka", "transformice": "transformice", "justshapesandbeats": "maumbochinhambonhete", "battlefield4": "battlefield4", "nightinthewoods": "usik<PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "g<PERSON><PERSON><PERSON>", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "njoziyemvura2", "metroidvanias": "metroidvanias", "overcooked": "p<PERSON><PERSON><PERSON><PERSON>", "interplanetary": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "helltaker": "m<PERSON><PERSON><PERSON><PERSON>", "inscryption": "inscryption", "7d2d": "7mazuva2mazuva", "deadcells": "maseruzakafa", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "nharedustuvi", "foxhole": "go<PERSON><PERSON><PERSON>", "stray": "nzira", "battlefield": "nhandoyehondo", "battlefield1": "hondoyekutanga", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "maziso", "blackdesert": "gwenga", "tabletopsimulator": "mutambowepachigaro", "partyhard": "<PERSON><PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hades": "<PERSON><PERSON><PERSON>", "gunsmith": "mhizha_wepf<PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "ndakavharir<PERSON><PERSON><PERSON>", "dinkum": "chaiz<PERSON>", "predecessor": "akatangira", "rainworld": "nyikayemvura", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "simulatorwecolony", "noita": "noita", "dawnofwar": "manguvaehondo", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "mushand<PERSON><PERSON>u", "datingsims": "mitamboyer<PERSON>", "yaga": "yaga", "cubeescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "magu<PERSON><PERSON><PERSON>", "defconheavy": "defconheavy", "kenopsia": "kenopsia", "virtualkenopsia": "<PERSON><PERSON><PERSON><PERSON>", "snowrunner": "snowrunner", "libraryofruina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "mitamboyevanonarygames", "omegastrikers": "omegastrikers", "wayfinder": "m<PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenakubatanidzemweya", "placidplasticduck": "jekejekeplastikidhadha", "battlebit": "hondoitsomba", "ultimatechickenhorse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dialtown": "guta", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "usikuhwekitsi", "supermeatboy": "supermeatboy", "tinnybunny": "kane<PERSON><PERSON><PERSON><PERSON>", "cozygrove": "cozygrove", "doom": "kudodomoka", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdutyzombiezvidhoma", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "mitamboyefarcry", "paladins": "mapaladins", "earthdefenseforce": "mutsvairowepasirese", "huntshowdown": "kuvendashowdown", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "hondo", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "joinsquad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "hondoyemhechemhando", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "rufarweupenyu", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "mirrorsedge", "divisions2": "kupatsanurwa2", "killzone": "nzvimboyekuuraya", "helghan": "hel<PERSON>", "coldwarzombies": "hondoyechandomazombies", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "<PERSON><PERSON><PERSON>ashash<PERSON>", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "hondoyemazuvaano", "neonabyss": "g<PERSON><PERSON><PERSON>", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "migan<PERSON>", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "k<PERSON><PERSON>yahond<PERSON>", "worldofwarships": "worldofwarships", "back4blood": "back4blood", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "mhondi", "masseffect": "masseffect", "systemshock": "<PERSON><PERSON><PERSON>amadz<PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "nyayeyebako", "doometernal": "dambud<PERSON><PERSON>", "centuryageofashes": "zanaremakore", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "muru<PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "kuruwadzirekusipo", "warface": "hurunhondo", "crossfire": "kubatana", "atomicheart": "moyo_unogona_kuputika", "blackops3": "blackops3", "vampiresurvivors": "varoyi_vanorarama", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "rus<PERSON><PERSON><PERSON>o", "battlegrounds": "nzvimbondondo", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "mutambopubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "mitamboy<PERSON><PERSON>", "convertstrike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warzone2": "warzone2", "shatterline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackopszombies": "blackopszombies", "bloodymess": "d<PERSON><PERSON><PERSON>dza", "republiccommando": "republiccommando", "elitedangerous": "elitedangerous", "soldat": "musoja", "groundbranch": "b<PERSON><PERSON><PERSON>", "squad": "squad", "destiny1": "mugumo1", "gamingfps": "mit<PERSON><PERSON><PERSON><PERSON><PERSON>", "redfall": "redfall", "pubggirl": "musikanapubg", "worldoftanksblitz": "nyikaemitanki", "callofdutyblackops": "callofdutyblackops", "enlisted": "aka<PERSON><PERSON><PERSON>", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "zuvarubhadharo2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "sipo_hove", "ghostcod": "ghostcod", "csplay": "csplay", "unrealtournament": "muta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "mitamboyecodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "<PERSON><PERSON><PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "mwenje", "killingfloor": "k<PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neon<PERSON>a", "remnant": "zvasara", "azurelane": "azurelane", "worldofwar": "nyikayehondo", "gunvolt": "gunvolt", "returnal": "dzo<PERSON>cherechedza", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "mun<PERSON><PERSON>", "quake2": "kudengenyeka2", "microvolts": "microvolts", "reddead": "chena", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "gungwadzezvabiwa", "rust": "ngura", "conqueronline": "kukundaonline", "dauntless": "<PERSON><PERSON><PERSON>", "warships": "ngara<PERSON>", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "hondoyegore", "flightrising": "kubhururukakwemazai", "recroom": "imbarekutandaramo", "legendsofruneterra": "nhaudzedzeruneterra", "pso2": "pso2", "myster": "chakavanzika", "phantasystaronline2": "phantasystaronline2", "maidenless": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "nyikazvezvitangi", "crossout": "bvisai", "agario": "agario", "secondlife": "hupeny<PERSON>_hwe<PERSON><PERSON>ri", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "tamba_panetiweki", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "kubatanidzakwaisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "musvinu", "newworld": "nyikaitsva", "blackdesertonline": "blackdesertonline", "multiplayer": "mit<PERSON><PERSON>_ya<PERSON><PERSON>", "pirate101": "pirate101", "honorofkings": "mutambowemashe", "fivem": "fivem", "starwarsbattlefront": "hondo<PERSON><PERSON><PERSON><PERSON>", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarshondoyebattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "guta_remabhiza", "3dchat": "3dchat", "nostale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tauriwow": "<PERSON><PERSON><PERSON><PERSON>", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "mukandowesilika", "spiralknights": "spiralknights", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "muporofitaweshato", "grymmo": "grymmo", "warmane": "<PERSON><PERSON><PERSON><PERSON>", "multijugador": "mitamboyanevamwe", "angelsonline": "ngirizionline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "blueprotocol", "perfectworld": "nyikazvisingaperi", "riseonline": "mukaonline", "corepunk": "corepunk", "adventurequestworlds": "nzvendozvinoshamisa", "flyforfun": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomofloathing": "nyikayeloathing", "cityofheroes": "ganzarematenga", "mortalkombat": "mortalkombat", "streetfighter": "kurwamumugwagwa", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON>do", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "migwagwad<PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "mkbatanid<PERSON><PERSON><PERSON>", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "mamboekutwamachembere", "likeadragon": "<PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blasphemous": "blasphemous", "rivalsofaether": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "kurovanakunyanyisa", "mugen": "mugen", "warofthemonsters": "hondoyezvikara", "jogosdeluta": "mitam<PERSON><PERSON><PERSON>", "cyberbots": "ma<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "kuru<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poweredgear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "rovapfu<PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "mitamboyekur<PERSON>", "killerinstinct": "<PERSON><PERSON><PERSON><PERSON>", "kingoffigthers": "mambowevat<PERSON><PERSON><PERSON>", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "chivalry2", "demonssouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "silksongnews": "nhand<PERSON><PERSON><PERSON>", "silksong": "silksong", "undernight": "usiku", "typelumina": "typelum<PERSON>", "evolutiontournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "evomoment": "nguvayeevo", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "nganodzeberseria", "bloodborne": "r<PERSON><PERSON><PERSON>", "horizon": "horizon", "pathofexile": "pathofexile", "slimerancher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashbandicoot": "crashbandicoot", "bloodbourne": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "uncharted": "k<PERSON>tisvik<PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infamous": "anonyad<PERSON><PERSON><PERSON>", "playstationbuddies": "shamwaridzepaplaystation", "ps1": "ps1", "oddworld": "nyikai<PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "mafairaasomnium", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "<PERSON><PERSON><PERSON><PERSON>", "detroitbecomehuman": "detroitinovemunhu", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "kusvikamambakwedza", "touristtrophy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "mumveriwechikara", "crashteamracing": "crashteamracing", "fivepd": "fivepd", "tekken7": "tekken7", "devilmaycry": "mudhimonianosuwa", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "ndichitambastation", "samuraiwarriors": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "murid<PERSON>_weku<PERSON><PERSON><PERSON>a", "soulblade": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "tsvagatsvaga", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "moyo_wemimvuri2chibvumirano", "pcsx2": "pcsx2", "lastguardian": "mupindigwe<PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "<PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "psychonauts", "mhw": "mhw", "princeofpersia": "muchindawepersia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "nhandoyehondo", "dontstarvetogether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "nyeredzi_dzakabatana", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "kutengesadzimu", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligayamambo", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "zvetvzvisingabatsiri", "skycotl": "skycotl", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "romb<PERSON><PERSON><PERSON>", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterprom", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerwilds": "kunzekunemapeto", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "chinamatoramakwai", "duckgame": "muta<PERSON><PERSON><PERSON>ha", "thestanleyparable": "thestanleyparable", "towerunite": "batanaipadanho", "occulto": "chakavanzika", "longdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "satisfactory": "zvinogutsa", "pluviophile": "inodamvura", "underearth": "pasipasi", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "mufambiwemweya", "darkdome": "domerer<PERSON>", "pizzatower": "pizzatower", "indiegame": "mutambowepadiki", "itchio": "itchio", "golfit": "golfit", "truthordare": "chok<PERSON><PERSON><PERSON>a", "game": "mutambo", "rockpaperscissors": "dombobepagunting", "trampoline": "trampoline", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "tsitsi", "scavengerhunt": "kutsvagatsvaga", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "nhemayokana", "beerpong": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegoblin": "daisigoblin", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "mitamboyekufambidzana", "freegame": "mutsetsefree", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "<PERSON><PERSON><PERSON>", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "mitamboyekued<PERSON>", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "ngatitambechitambo", "boredgames": "zvemitamboyekuzevezera", "oyun": "oyun", "interactivegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "kuka<PERSON><PERSON>na", "spiele": "<PERSON><PERSON><PERSON>", "giochi": "<PERSON><PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "mitamboyeiphone", "boogames": "mitamboyeboo", "cranegame": "mutambowecrane", "hideandseek": "titsvande", "hopscotch": "<PERSON><PERSON><PERSON><PERSON>", "arcadegames": "mitamboychinangwa", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "muta<PERSON><PERSON>kar<PERSON>", "mindgames": "mitam<PERSON>epfungwa", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancegame": "muta<PERSON><PERSON><PERSON><PERSON>", "yanderegames": "mitamboyekunyepera", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4xgames": "4xgames", "gamefi": "gamefi", "jeuxdarcades": "mitambozvekare", "tabletopgames": "mitamboyepatafura", "metroidvania": "metroidvania", "games90": "mitambo90", "idareyou": "ndinokusheedzera", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "nderechaidii_nekwekunyepa", "playgames": "tamba<PERSON><PERSON><PERSON>", "gameonline": "mutambowe<PERSON>mhepo", "onlinegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "kunyorabatanidzwa", "playaballgame": "tambamutambo", "pictionary": "pictionary", "coopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "<PERSON><PERSON><PERSON>", "highscore": "n<PERSON><PERSON><PERSON>", "jeuxderôles": "mitambodzinotamba", "burgergames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "bhora_rekuteya", "nfsmwblackedition": "nfsmwedzemadema", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "mutambowemibvunzo", "gioco": "gioco", "managementgame": "mutambowekutonga", "hiddenobjectgame": "muta<PERSON>we<PERSON><PERSON>hu", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "mutamboweformula1", "citybuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drdriving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "mitamboarcade", "memorygames": "mitamboyekundiyeuka", "vulkan": "vulkan", "actiongames": "mitamboz<PERSON><PERSON><PERSON><PERSON>", "blowgames": "miteseremweya", "pinballmachines": "pinballmachines", "oldgames": "mitamboekare", "couchcoop": "pas<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "mutambo", "lasergame": "muta<PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "mitamboimessage", "idlegames": "mitamboyekungoridzwa", "fillintheblank": "zad<PERSON>_pakasara", "jeuxpc": "mitambopc", "rétrogaming": "rétrogaming", "logicgames": "mitamboyekufunga", "japangame": "mutambowejapan", "rizzupgame": "simb<PERSON>mu<PERSON><PERSON>", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5vs5", "rolgame": "mutamborole", "dashiegames": "dashiegames", "gameandkill": "tamba_uuraye", "traditionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "mitambofps", "textbasedgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "ndez<PERSON><PERSON><PERSON>", "thiefgame": "mutambowembavha", "lawngames": "mitamboyechivanze", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tischfußball": "tischfußball", "spieleabende": "spie<PERSON><PERSON>de", "jeuxforum": "jeuxforum", "casualgames": "mitamboyek<PERSON><PERSON>", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "mitamboyekutiza", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "mitam<PERSON>ecrane", "játék": "mutambo", "bordfodbold": "bordfodbold", "jogosorte": "jogosorte", "mage": "m<PERSON><PERSON>", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "kutambaonline", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "randomizer": "randomizer", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "mitambopc", "socialdeductiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominos": "dominos", "domino": "domino", "isometricgames": "mitamboisometric", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "chokwadinemutambo", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "kutsvagatsvaga", "jeuxvirtuel": "mitamboparunhare", "romhack": "romhack", "f2pgamer": "mutambiwemalara", "free2play": "mahara", "fantasygame": "mutambowe<PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "mutambowe<PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "mitamboechirume", "halotvseriesandgames": "halotvnematambo", "mushroomoasis": "oasisyehowa", "anythingwithanengine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "mutambo<PERSON><PERSON>ose", "swordandsorcery": "munondowebarendatsambo", "goodgamegiving": "mutambowekupachigare", "jugamos": "tinota<PERSON>", "lab8games": "mitamboyemulab8", "labzerogames": "mitamboyelabzero", "grykomputerowe": "mit<PERSON><PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON>", "minaturegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "kudazviganwa", "gamemodding": "kukosh<PERSON>agemhur<PERSON>", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "mitambodzinona<PERSON><PERSON>", "spelletjes": "<PERSON><PERSON><PERSON>", "spacenerf": "spacenerf", "charades": "mitamboyekuenzan<PERSON>", "singleplayer": "mutambiunega", "coopgame": "mutambowekubatana", "gamed": "ndakatambisa", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "muta<PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "mambodiscord", "scrabble": "scrabble", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "bhao", "onitama": "onitama", "pandemiclegacy": "dambudzikodenda", "camelup": "ngomanyemba", "monopolygame": "mutambowemonopoly", "brettspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "mitambodzinotambirwa", "boardgame": "mutambowema<PERSON>i", "sällskapspel": "mitamboyekuta<PERSON>", "planszowe": "planszowe", "risiko": "risiko", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "kupondwa_kwezvitunha", "tabletop": "tafura", "baduk": "baduk", "bloodbowl": "bhora_reropa", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "endagembodhi", "connectfour": "batazvina", "heroquest": "rwendo_remagamba", "giochidatavolo": "mitamboyematafura", "farkle": "farkle", "carrom": "carrom", "tablegames": "mitamboyematafura", "dicegames": "mitamboyemadice", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "muta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "kusanganan<PERSON><PERSON>", "creationludique": "kus<PERSON><PERSON><PERSON>var<PERSON><PERSON>", "tabletoproleplay": "mutambowepatafura", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "zvinotyisazvisinganzwisisike", "switchboardgames": "mitamboyeswitchboard", "infinitythegame": "muta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "rufekingdom", "yahtzee": "yahtzee", "chutesandladders": "mativinzve", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "mutambowetafura", "planszówki": "boardgames", "rednecklife": "hupenyu_hwe<PERSON><PERSON>wa", "boardom": "kufin<PERSON>", "applestoapples": "mi<PERSON><PERSON>michero", "jeudesociété": "mutambowenzira", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horseopoly": "horseopoly", "deckbuilding": "kuvaka_dheki", "mansionsofmadness": "dzimbadzekupenga", "gomoku": "gomoku", "giochidatavola": "mitamboyepatafura", "shadowsofbrimstone": "mimviriyebrimstone", "kingoftokyo": "mambozvetokyo", "warcaby": "warcaby", "táblajátékok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "ng<PERSON><PERSON><PERSON><PERSON>", "tickettoride": "ticketiyekufamba", "deskovehry": "mitamboyetafura", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "mitambopatafura", "stolníhry": "mitamboyematafura", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "mitamboyekuta<PERSON>", "starwarslegion": "starwarslegion", "gochess": "endachess", "weiqi": "weiqi", "jeuxdesocietes": "mitamboyekuta<PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "hondo", "arksurvivalevolved": "arksurvivalevolved", "dayz": "ma<PERSON>va", "identityv": "identityv", "theisle": "theisle", "thelastofus": "ve<PERSON><PERSON><PERSON><PERSON><PERSON>", "nomanssky": "devedzerisina", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "da<PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "pakatipenyu", "eco": "eco", "monkeyisland": "monkeyisland", "valheim": "valheim", "planetcrafter": "muumbigweplanet", "daysgone": "mazvaukapfuura", "fobia": "fobia", "witchit": "witchit", "pathologic": "pathologic", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7mazuva", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "ndakarambidzwa", "stateofdecay2": "mamiriroekuora2", "vrising": "vrising", "madfather": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarve": "musanzara", "eternalreturn": "dzokerorakusinga<PERSON>i", "pathoftitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexen": "m<PERSON><PERSON>", "theevilwithin": "zvakaipamukati", "realrac": "realrac", "thebackrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backrooms": "dzimbareshure", "empiressmp": "empiressmp", "blockstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thequarry": "chib<PERSON><PERSON>", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "mutambowevakafa", "wehappyfew": "v<PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "muta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagestory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "kufema", "alisa": "alisa", "westlendsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "chando_chechikwereti", "darkwood": "<PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "voidtrain", "lifeaftergame": "hupenyu_mushure_memutambo", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "chirongwachegirini", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "chikepe", "rdo": "rdo", "greenhell": "dambehell", "residentevil5": "residentevil5", "deadpoly": "vakafapoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "mbuya", "littlenightmares2": "zvinotyisausikudiki2", "signalis": "signalis", "amandatheadventurer": "amanda<PERSON><PERSON>mb<PERSON>", "sonsoftheforest": "vanakomudo<PERSON>", "rustvideogame": "mutambowerust", "outlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "kuzviparadzanisanevatorwa", "undawn": "mambakwedza", "7day2die": "7day2die", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "sopravvivenza", "propnight": "propnight", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "rufenyika", "cataclysmdarkdays": "zvakawandazverima", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "mushurepewo", "ageofdarkness": "nguvazerima", "clocktower3": "clocktower3", "aloneinthedark": "ndoga_murima", "medievaldynasty": "nzvezvemazuvaekare", "projectnimbusgame": "mutambowemitamboyanimba<PERSON>", "eternights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftopia": "nyikadzinoumhizha", "theoutlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bunker": "bunker", "worlddomination": "kutonganyika", "rocketleague": "rocketleague", "tft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "muura<PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kcrush", "wh40": "wh40", "warhammer40klove": "rudowarhammer40k", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "ndinodasisters", "ilovevindicare": "ndinodavindicare", "iloveassasinorum": "ndinodaassasinorum", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "mapapiro", "terraformingmars": "kugadzirama<PERSON><PERSON><PERSON><PERSON>hu", "heroesofmightandmagic": "magambaesimbane<PERSON>", "btd6": "btd6", "supremecommander": "mut<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofmythology": "zerapazvitendero", "args": "args", "rime": "chando", "planetzoo": "nyikayemhuka", "outpost2": "outpost2", "banished": "<PERSON><PERSON><PERSON>zing<PERSON>", "caesar3": "caesar3", "redalert": "ch<PERSON>shambaradza", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "tonga_ubate", "warcraft3": "warcraft3", "eternalwar": "hondoyasingaperi", "strategygames": "mitamboyekufunga", "anno2070": "anno2070", "civilizationgame": "mutambowenyika", "civilization4": "budiriro4", "factorio": "factorio", "dungeondraft": "mapadzingatonga", "spore": "spore", "totalwar": "hondo<PERSON><PERSON><PERSON>", "travian": "travian", "forts": "nhare", "goodcompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "nyikapamweya", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "k<PERSON><PERSON><PERSON><PERSON>za", "forthekings": "zvavamadzimambo", "realtimestrategy": "nziradzinorarama", "starctaft": "starcraft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eu4": "eu4", "vainglory": "kuz<PERSON>udza", "ww40k": "ww40k", "godhood": "<PERSON><PERSON>", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davekilasiyematamusandoadzinemafaro", "plagueinc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theorycraft": "kufungisisa", "mesbg": "mesbg", "civilization3": "budiriro3", "4inarow": "4mumutsara", "crusaderkings3": "crusaderkings3", "heroes3": "magamba3", "advancewars": "hondoyekuvamba", "ageofempires2": "zivazveumambohwekare2", "disciples2": "vateveri2", "plantsvszombies": "zvirim<PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "mit<PERSON><PERSON><PERSON><PERSON><PERSON>", "stratejioyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "mambodhidhakosoro", "worldconquest": "kutoranyika", "heartsofiron4": "moyo_wesimbi4", "companyofheroes": "sanganoremhare", "battleforwesnoth": "hondoyewesnoth", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phobies": "matya", "phobiesgame": "muta<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "mutamboweclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "kunzeendege", "turnbased": "panotendeukana", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "ng<PERSON><PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON>", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estratégia", "popfulmail": "popfulmail", "shiningforce": "simbarekugwinya", "masterduel": "mutambowehuru", "dysonsphereprogram": "chirongo<PERSON><PERSON><PERSON><PERSON><PERSON>", "transporttycoon": "mutongiwezvekutakura", "unrailed": "ndin<PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "kurwiswanevemata<PERSON><PERSON><PERSON><PERSON>", "uplandkingdoms": "nyikadzinoshe", "galaxylife": "hupeny<PERSON>_hwen<PERSON><PERSON><PERSON>", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlecats": "makatihondo", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "njuganjuga", "needforspeedcarbon": "kudafastcarbon", "realracing3": "kutsidzanazvechokwadi3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "k<PERSON><PERSON>", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "alicekudzokakupenga", "darkhorseanthology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phasmophobia": "kut<PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saiko": "saiko", "fatalframe": "rubatsofanhete", "littlenightmares": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "wa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON>", "deadisland": "chitsuwachemakufa", "litlemissfortune": "litlemissfortune", "projectzero": "projectzero", "horory": "in<PERSON><PERSON><PERSON>", "jogosterror": "kutyaitsamanyonya", "helloneighbor": "moro<PERSON><PERSON>", "helloneighbor2": "moroihama2", "gamingdbd": "mutambowedbd", "thecatlady": "mukadziwekatsi", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "makadhibicycle", "lor": "lor", "euchre": "euchre", "thegwent": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofrunetera": "tsambatanorwerune<PERSON>", "solitaire": "solitaire", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "<PERSON><PERSON><PERSON>", "netrunner": "mujaho", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "maka<PERSON><PERSON>tengeserana", "pokemoncards": "poke<PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "makadhi_emitambo", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "<PERSON><PERSON>", "warcry": "hondo", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "mambomanazvo", "truco": "truco", "loteria": "roteria", "hanafuda": "hana<PERSON>da", "theresistance": "kuramba", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "maka<PERSON><PERSON>_eyu<PERSON>h", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "mutamboweyugioh", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "briscas": "zvikwapenga", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "mitam<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "hondo", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "carteado", "sueca": "sueca", "beloteonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "hondoyemweya", "jogodecartas": "mutambowekadhiyi", "žolíky": "zvakanaka", "facecard": "chiso", "cardfight": "kur<PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "vanogad<PERSON>rad<PERSON><PERSON>", "marvelchampions": "vatambidzivemarvel", "magiccartas": "nganoremashiripiti", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "<PERSON><PERSON><PERSON>", "unstableunicorns": "twizausingagadzikane", "cyberse": "<PERSON><PERSON><PERSON><PERSON>", "classicarcadegames": "mit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "osu": "osu", "gitadora": "gitadora", "dancegames": "mitamboyekuta<PERSON>", "fridaynightfunkin": "fridaynightfunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "g<PERSON><PERSON>o", "clonehero": "clonehero", "justdance": "tambazvako", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "svirasvarerwenevakafa", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "dancecentral", "rhythmgamer": "muta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "zvikurumarythmgames", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "mutambowedenga", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "bvunzapaonline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "mitambod<PERSON>ori<PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubing": "cubing", "wordle": "wordle", "teniz": "tenizi", "puzzlegames": "mit<PERSON><PERSON>emakwik<PERSON>", "spotit": "uchione", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "madud<PERSON>roxakafungisisa", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "mapfungwazvinetso", "rubikscube": "rubikscube", "crossword": "muchinjikwa", "motscroisés": "motscroisés", "krzyżówki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nonogram": "nonogram", "bookworm": "bookworm", "jigsawpuzzles": "majigsawpuzzle", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "chira<PERSON>we", "riddles": "zvirahwe", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "mukati", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "kutizimuteedzeri", "minesweeper": "muchekacheka", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "mutambowemichero", "puzzlesport": "mutambowemitambo", "escaperoomgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapegame": "mutamboweku<PERSON><PERSON><PERSON>uka", "3dpuzzle": "3dpuzzle", "homescapesgame": "mutsedzehomescapes", "wordsearch": "tsvagazwi", "enigmistica": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kulaworld": "kulaworld", "myst": "chakavanzika", "riddletales": "nganodzinemipikicha", "fishdom": "hovedzimafish", "theimpossiblequiz": "bvunzoyasingagone", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "mutambomitatu", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikcube": "rubikcube", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "mad<PERSON><PERSON>", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "ndibvunze", "tycoongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "cubosderubik", "cruciverba": "cruciver<PERSON>", "ciphers": "zvimisikidzo", "rätselwörter": "mazwi_emapuzzle", "buscaminas": "buscaminas", "puzzlesolving": "kupazurapuzzle", "turnipboy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "adivina<PERSON>shot", "nobodies": "vasingazviwi", "guessing": "kufungidzira", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "zvipikisazvisinganzwisisike", "syberia2": "syberia2", "puzzlehunt": "kutsvagapuzzle", "puzzlehunts": "kutsvagazvinhu", "catcrime": "mhosvayekat<PERSON>", "quebracabeça": "quebracabeça", "hlavolamy": "hlavolamy", "poptropica": "poptropica", "thelastcampfire": "motoyekuped<PERSON><PERSON><PERSON>", "autodefinidos": "vakazvidzora", "picopark": "picopark", "wandersong": "nziyo_dzekufamba", "carto": "carto", "untitledgoosegame": "muta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "musoro_unorwadza", "limbo": "limbo", "rubiks": "rubiks", "maze": "maze", "tinykin": "ka<PERSON><PERSON>", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "mapieces", "portalgame": "mutambowepotali", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "zvirahwe", "cubomagico": "cubomagico", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "twistedwonderland", "monopoly": "monopoly", "futurefight": "hondoyeramangwana", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "chim<PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "hupenyu", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "nyeredzi_dzemutambo", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "nyeredzi_dzealchemy", "stateofsurvival": "nyikai<PERSON><PERSON><PERSON>", "mycity": "guta_rangu", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "nhabveyenhabvu", "a3": "a3", "phonegames": "mitamboyenhare", "kingschoice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guardiantales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "petrolhead": "mwendapeturu", "tacticool": "tacticool", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "handizivizvirikuitika", "craftsman": "mhizha", "supersus": "inoshamisa", "slowdrive": "kufambazvishoma", "headsup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "bedwars", "freefire": "freefire", "mobilegaming": "mitamboyanhare", "lilysgarden": "bind<PERSON>ilys<PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "kurwadzanakwemaclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "emergencyhq", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "k<PERSON><PERSON>_nekutamba_tamba", "ml": "ml", "bangdream": "kurota", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatstar": "n<PERSON><PERSON><PERSON>", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "mapenziepaketchetako", "androidgames": "mitamboyeandroid", "criminalcase": "mhos<PERSON><PERSON><PERSON><PERSON>ngwa", "summonerswar": "summonerswar", "cookingmadness": "kubikakunopenga", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "ligue<PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "<PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "nyunyinzidzangudzinoimba", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "nyika<PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "kup<PERSON>", "slugitout": "zvirwirwane", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "kurovanhokorapanhangagray", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "kub<PERSON>_mubhawa", "wolfy": "mbwa", "runcitygame": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegodemovil": "mutambowenhare", "avakinlife": "hupenyu_hwe<PERSON>kin", "kogama": "kogama", "mimicry": "kutevedzeramazwi", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "mujahondow<PERSON><PERSON><PERSON><PERSON>", "grandchase": "grandchase", "bombmebrasil": "bombmenimubrazil", "ldoe": "ldoe", "legendonline": "mutandionline", "otomegame": "muta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "s<PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "mazwineushamwari2", "soulknight": "mutambimweya", "purrfecttale": "kurekakunakisekwemhuka", "showbyrock": "rat<PERSON><PERSON><PERSON><PERSON><PERSON>", "ladypopular": "mukadzianezvizhinji", "lolmobile": "lolnharembwa", "harvesttown": "guta_regohwo", "perfectworldmobile": "nyikazviripapfungwa", "empiresandpuzzles": "maumambonemapuzzles", "empirespuzzles": "mapuzzleemumambo", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "nhandoyehondodzenyika", "fanny": "beche", "littlenightmare": "horoma<PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "mudrunner", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "<PERSON><PERSON><PERSON><PERSON>", "gunbound": "p<PERSON>ti", "gamingmlbb": "mutambomlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eveechoes": "eveechoes", "jogocelular": "mutambowerunhare", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "kurwadzanedzemigwagwa", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsfrontline": "vasikirifrontline", "jurassicworldalive": "jurassicworldanorarama", "soulseeker": "mutsvakiwemweya", "gettingoverit": "kuzvidarika", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "motokaridzinod<PERSON>ngaidza", "jogosmobile": "mitamboyenhare", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "mutambowenhamba", "timeraiders": "nguvadeva<PERSON><PERSON><PERSON>", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "musatib<PERSON>nze", "quest": "b<PERSON><PERSON>o", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "mutambowemakomo", "worldofdarkness": "nyikayer<PERSON>", "travellerttrpg": "vatsvtsvetenderaboo", "2300ad": "2300ad", "larp": "kutamba", "romanceclub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "d20": "d20", "pokemongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonred", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "taura", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "t<PERSON><PERSON><PERSON><PERSON>", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "mhukadzvinodiki", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "timuy<PERSON>", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "chaka<PERSON>ya", "shinypokemon": "pokemona<PERSON>pen<PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "heyyouwepikachú", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "mutsvakirwezvinopenya", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "vachasikanavetarisi", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "blitzdzepasi", "jeudéchecs": "jeudéchecs", "japanesechess": "chessechijapan", "chinesechess": "chessechichina", "chesscanada": "chesscanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "mikana", "rook": "bhachi", "chesscom": "chesscom", "calabozosydragones": "calabozosydragones", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "murid<PERSON>_wedungeon", "tiamat": "tiamat", "donjonsetdragons": "donjonsnemad<PERSON><PERSON><PERSON>", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "nganoyevoxmachina", "doungenoanddragons": "dungeonanddragons", "darkmoor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "shashindyemutambominecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modzidzedzemaincraft", "mcc": "mcc", "candleflame": "mwenje", "fru": "fru", "addons": "zvekuwedzera", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftrakagadziriswa", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgamer": "mutambiwepcgame", "jeuxvideo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gambit": "<PERSON><PERSON><PERSON><PERSON>", "gamers": "vatambi", "levelup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermobile": "gamermafoni", "gameover": "mat<PERSON>wa", "gg": "gg", "pcgaming": "mitamboyepapc", "gamen": "gamen", "oyunoynamak": "kudyidzanemitambo", "pcgames": "mitamboyepacomputer", "casualgaming": "mitam<PERSON>akungoit<PERSON>", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmasterrace", "pcgame": "mutambowepc", "gamerboy": "mutsezigemu", "vrgaming": "mutambowevr", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON><PERSON>", "consoleplayer": "mutambiweconsolemu", "boxi": "boxi", "pro": "pro", "epicgamers": "vatambivanehuwandu", "onlinegaming": "mitamboyepaindaneti", "semigamer": "mutsambigamer", "gamergirls": "vasikirab<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermoms": "maigamesvanenge", "gamerguy": "mutambiwezvemitambo", "gamewatcher": "muta<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "vachikadzevanemitambo", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "vap<PERSON><PERSON>", "quests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "mutezvigamer", "cozygaming": "mutambowakunakidza", "gamelpay": "gamelpay", "juegosdepc": "mitambopapc", "dsswitch": "shand<PERSON><PERSON>", "competitivegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftnewjersey": "minecraftnewjersey", "faker": "mup<PERSON>fit<PERSON>", "pc4gamers": "p<PERSON><PERSON><PERSON><PERSON>", "gamingff": "mit<PERSON><PERSON>emakomb<PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "mutambowepakombiyuta", "girlsgamer": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnfmods": "fnfmods", "dailyquest": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamegirl": "muts<PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesetup": "gad<PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "socialgamer": "mutambiwemasocial", "gamejam": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "proplayer": "mutambiwe<PERSON>hando", "roleplayer": "mutamb<PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "tim<PERSON><PERSON><PERSON>", "republicofgamers": "nyikayevatambi", "aorus": "aorus", "cougargaming": "mukadzi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "triplelegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerbuddies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "butuhcewekgamers": "ndinodacewekgamers", "christiangamer": "mutamb<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "mutambiwezvemitambo", "nerdgamer": "nerdy<PERSON><PERSON><PERSON>", "afk": "ndisipo", "andregamer": "andregamer", "casualgamer": "mutamb<PERSON><PERSON>", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "k<PERSON><PERSON>vi<PERSON>", "gemers": "magemers", "oyunizlemek": "oyunizlemek", "gamertag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lanparty": "lanparty", "videogamer": "mutambiwezvevhidhiyo", "wspólnegranie": "wspólnegranie", "mortdog": "mortdog", "playstationgamer": "mutambiweplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "muta<PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "mutambiwezvinyorwa", "protogen": "protogen", "womangamer": "muka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "zviripachivenezvendimutagamer", "mario": "mario", "papermario": "papermario", "mariogolf": "gorofuramario", "samusaran": "sa<PERSON><PERSON>", "forager": "muk<PERSON><PERSON>", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "mi<PERSON><PERSON><PERSON>yanintendo", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON><PERSON>", "switch": "zvinja", "zelda": "zelda", "smashbros": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "ma<PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "mutambiwezvitsigamotokari", "wii": "wii", "aceattorney": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "hupenyu_hweshamwari", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "mitukirwekufamba", "nintendogames": "mitamboyenintendo", "thelegendofzelda": "nganoyelinkyazelda", "dragonquest": "dragonquest", "harvestmoon": "gochekererwa", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "mweyadwemusang<PERSON>", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "legendsofzelda", "donkeykong": "d<PERSON>ikongi", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "mitambo51", "earthbound": "panyika", "tales": "nyaya", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "zvinemutsemuzvirongo", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioandsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "mbwanana_dzenintendo", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "marion<PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "ndabatwa", "zyra": "zyra", "redcanids": "humbwamutsvuku", "vanillalol": "vanillakkkk", "wildriftph": "wildriftph", "lolph": "kuseka", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspain", "aatrox": "aatrox", "euw": "svikazvino", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "mitamboyekuseka", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "mutambowefortnite", "gamingfortnite": "mutambowefortnite", "fortnitebr": "fortnitebr", "retrovideogames": "mitamboekare", "scaryvideogames": "mit<PERSON><PERSON>emav<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "mugadziriwez<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "muta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videosgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "mekede", "acnh": "acnh", "puffpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ung<PERSON><PERSON>", "farmingsimulator": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxdeutschland", "robloxdeutsch": "robloxchishona", "erlc": "erlc", "sanboxgames": "<PERSON><PERSON><PERSON>emahombekombe", "videogamelore": "nyayadzevhidhiyogemu", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "kurota", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "nzvimboisina", "amordoce": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theoldrepublic": "repubur<PERSON><PERSON><PERSON><PERSON>", "videospiele": "mitamborvideo", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "mitambodzinokunakidza", "wolfenstein": "wolfenstein", "actionadventure": "chiitokuchinhuhondo", "storyofseasons": "nyayayemwaka", "retrogames": "mitamboitsaru", "retroarcade": "mitamboekare", "vintagecomputing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "kuchezakunevangu", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "kusarurama2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "mutsetseyedenga", "zenlife": "up<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>e", "beatmaniaiidx": "beatmaniaiidx", "steep": "mawere", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "mitamboyepablockchain", "medievil": "yekare", "consolegaming": "mutambowepaconsolemu", "konsolen": "konsolen", "outrun": "kut<PERSON><PERSON>", "bloomingpanic": "kutyamadzwabwepanik", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "bvunzanyikad<PERSON>orwadzo", "supergiant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "mitamboitsaru", "bethesda": "bethesda", "jackboxgames": "mitamboyejackbox", "interactivefiction": "nyayadzinopind<PERSON><PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovels": "manyoroengano", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tcrghost": "tcrghost", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "mambatesikwaprincess", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "mitam<PERSON>ine<PERSON><PERSON><PERSON>", "novelavisual": "novelavisual", "thecrew2": "dzecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "muta<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "n<PERSON><PERSON><PERSON>", "godhand": "ruo<PERSON>mwari", "leafblowerrevolution": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "gad<PERSON>rirwomu<PERSON>bo", "starrail": "starrail", "keyblade": "keyblade", "aplaguetale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnafsometimes": "fnafdzmhmdzmre", "novelasvisuales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "muta<PERSON>wekar<PERSON><PERSON>", "videojuejos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamedates": "mitamboyemavhidhiyogame", "mycandylove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "chingodaizvo3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "kudzokakwechigumbuso", "gamstergaming": "gamstergaming", "dayofthetantacle": "zuvaremakakatanwa", "maniacmansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashracing": "mu<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "3dplatformers", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "chiwembozana", "storygames": "mitamboyenyaya", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "nzeveyenzeve", "beyondtwosouls": "kunzekwemweya2", "gameuse": "mashandisoemagame", "offmortisghost": "offmortisghost", "tinybunny": "ka<PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "powerup", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "aventurasgraficas", "quickflash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "kutambamitambotyetyechance", "retroarcades": "mage<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "dongo", "powerwashsim": "powerwashsim", "coralisland": "chitsuwachemakungwa", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "nyikayimwe", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "simbidzakamweya", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simulator", "symulatory": "symulatory", "speedrunner": "<PERSON><PERSON><PERSON>", "epicx": "zvekupenga", "superrobottaisen": "superrobhotakurwa", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "mutambowekutyisa", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "mukomanawemuseve", "toontownrewritten": "toontownrewrittenzvakare", "simracing": "mu<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "simrace": "mujaho", "pvp": "pvp", "urbanchaos": "gungano_remudhorobha", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "shai<PERSON><PERSON>", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "muchengetedziwema<PERSON><PERSON>", "spaceflightsimulator": "simulatorendegekufambamuchadenga", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hackandslash": "kucheka_uye_kubaya", "foodandvideogames": "chika<PERSON>_ne<PERSON><PERSON><PERSON>_ye<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "mitambovideos", "thewolfamongus": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "mutambowemalori", "horizonworlds": "nharaundadzepasirese", "handygame": "mutambowan<PERSON><PERSON>", "leyendasyvideojuegos": "nganonemitambo", "oldschoolvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racingsimulator": "mutedziwezvemujaho", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "nziyo", "famitsu": "famitsu", "gatesofolympus": "masuwoekuolympus", "monsterhunternow": "kuvhimamanyoka", "rebelstar": "gwejaregandanga", "indievideogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indiegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogame": "mutambo<PERSON><PERSON><PERSON><PERSON><PERSON>", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spiderman<PERSON><PERSON><PERSON><PERSON>", "bufffortress": "nhareikasimba", "unbeatable": "isingakundwe", "projectl": "projectl", "futureclubgames": "mitamboyemuclubmuramangwana", "mugman": "muk<PERSON>e", "insomniacgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "mitambomikuruhombe", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "mutambowaceleste", "aperturescience": "sainyenzeyechiedza", "backlog": "zvandisiyazvakawanda", "gamebacklog": "mutarowemita<PERSON>", "gamingbacklog": "mitam<PERSON>andakatadza", "personnagejeuxvidéos": "person<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "muv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "imbwadzakarara", "beastlord": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "dzvimbodzedhopamine", "staxel": "staxel", "videogameost": "muti<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ndinodakofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "berserk": "kupenga", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animeyekusuwa", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "zvekuyeraanime", "animewithplot": "animeyakakos<PERSON>", "pesci": "pesci", "retroanime": "retroanime", "animes": "maanime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "<PERSON><PERSON><PERSON>", "darklord": "mutong<PERSON>_werima", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON>_akanaka", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonemwaka1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "animecover", "thevisionofescaflowne": "chiratidzoechaesca<PERSON>ne", "slayers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "hover<PERSON><PERSON>a", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "upenyu_hunopfuura", "fireforce": "motowe<PERSON><PERSON>", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "diaryyeramangwana", "fairytail": "ngano", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "zvakagadzirwamugomba", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "ka<PERSON>ama<PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "mangayezvityisa", "romancemangas": "<PERSON><PERSON><PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "mus<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "blacklagoon", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "geniusinc": "jeni<PERSON>", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "acertainmagicalindex", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "ng<PERSON><PERSON>_dzerufu", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "zvikararus<PERSON><PERSON>", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animeyemitambo", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "muta<PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "animerekuendasekune", "sagaoftanyatheevil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "b<PERSON><PERSON>", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "muk<PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "fistofyanorthstar", "mazinger": "mazinger", "blackbuttler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "kungomamigar<PERSON><PERSON>", "fullmoonwosagashite": "mwedziwazereunditsvage", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "nhambeyehondo", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "muk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "<PERSON><PERSON><PERSON>", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "musikanawezvikara", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON><PERSON><PERSON><PERSON>", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "handim<PERSON>i", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "bakatwa", "loli": "loli", "horroranime": "animedzinotyisa", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "<PERSON>uru<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "r<PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "ndirimutorwa", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "nyikayakapikirwa", "monstermanga": "monstermanga", "yourlieinapril": "nhemawakomunakubvumbi", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "ng<PERSON><PERSON>_ye<PERSON><PERSON><PERSON><PERSON>a", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "mashavi", "deepseaprisoner": "musungwawemugungwagakadzika", "jojolion": "jojo<PERSON>", "deadmanwonderland": "nyikayevakafa", "bannafish": "bhanana<PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "mutambowadarwin", "husbu": "murume", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "moyo_wepandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "hondoyechikafu", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "muts<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "kuzvisingaperi", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "chibvumir<PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "dzimwa", "bluelock": "bluelock", "goblinslayer": "mu<PERSON><PERSON>_we<PERSON>goblin", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "blueexorcist", "slamdunk": "zvekuhwinha", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "ndak<PERSON><PERSON>", "spyfamily": "spyfamily", "airgear": "airgear", "magicalgirl": "musikanawezvi<PERSON>ura", "thesevendeadlysins": "zvitadzimuserezvechinomwe", "prisonschool": "chetejera", "thegodofhighschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kissxsis": "<PERSON><PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "mutambowakas<PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animepasirese", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON>", "romancemanga": "manga<PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "rudo<PERSON>nime", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animesimbabwe", "lolicon": "lo<PERSON>on", "demonslayertothesword": "vapondimad<PERSON><PERSON><PERSON>", "bloodlad": "bloodlad", "goodbyeeri": "sarab<PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON>", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "nyeredzedzinosangana", "romanceanime": "rutendoeanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "rekodha<PERSON>narok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "chikororwevakafa", "germantechno": "technorekugermany", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "mutong<PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "assassinclassroom", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "rufu_parade", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animeyejapan", "animespace": "animespace", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "gonzo", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gashbell": "gashbell", "peachgirl": "musikanawepeach", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "chib<PERSON><PERSON>_chema<PERSON><PERSON><PERSON>i", "dragonquestdai": "dragonquestdai", "heartofmanga": "moyowemanga", "deliciousindungeon": "chinonakamudungeon", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "nyandi<PERSON><PERSON>ma<PERSON><PERSON>", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "svetauku<PERSON>bhu<PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON>", "overgeared": "pfeka_nhodzire", "toriko": "<PERSON><PERSON>o", "ravemaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kkondae": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chobits": "chobits", "witchhatatelier": "witchhatatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "vak<PERSON>avan<PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "mudzidzisimu<PERSON>ru<PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "musoja", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "kurota_kukuru_kwebhuruu", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "roparanime", "bloodc": "rop<PERSON><PERSON>a", "talesofdemonsandgods": "<PERSON><PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON>", "goreanime": "goreanime", "animegirls": "vasi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "crowsxzvakaipisisa", "splatteranime": "splatteranime", "splatter": "<PERSON><PERSON>", "risingoftheshieldhero": "k<PERSON><PERSON><PERSON>k<PERSON>gambarenhovo", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedaitorwa", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON>", "liarliar": "mup<PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "supercampeones", "animeidols": "animeidols", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "vasikiriwemashiripiti", "callofthenight": "kudanakvehusiku", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "<PERSON>awemanga", "princessjellyfish": "princess<PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekiss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeverse", "persocoms": "persocoms", "omniscientreadersview": "muoni<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecat": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "animerecommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openinganime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "rwevangungun<PERSON>udiwa", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "magundams", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "muru<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "booanime", "bleach": "b<PERSON><PERSON>a", "deathnote": "rufu", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "maso<PERSON><PERSON><PERSON>", "greenranger": "greenranger", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "surveycorps", "onepieceanime": "onepieceanime", "attaquedestitans": "kurukirwanemagamba", "theonepieceisreal": "theonepieceiripozvokwadi", "revengers": "vam<PERSON><PERSON>i", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "joyboyeffect", "digimonstory": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kemonofriends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "muroyi_an<PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "allsaintsstreet", "recuentosdelavida": "nhoroondodzeupenyu"}