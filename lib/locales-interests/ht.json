{"2048": "2048", "mbti": "mbti", "enneagram": "eneagram", "astrology": "astroloji", "cognitivefunctions": "fonksyonkognitif", "psychology": "<PERSON><PERSON><PERSON><PERSON>", "philosophy": "filozofi", "history": "istwa", "physics": "fizik", "science": "syans", "culture": "kilti", "languages": "lang", "technology": "teknoloji", "memes": "m<PERSON>m", "mbtimemes": "mbtimemes", "astrologymemes": "memastroloji", "enneagrammemes": "memèneyagram", "showerthoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "komik", "videos": "videyo", "gadgets": "<PERSON><PERSON><PERSON><PERSON>", "politics": "politik", "relationshipadvice": "konsèysour<PERSON><PERSON><PERSON>", "lifeadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "kripto", "news": "nouv<PERSON>l", "worldnews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "a<PERSON><PERSON><PERSON>", "learning": "a<PERSON>nn", "debates": "deba", "conspiracytheories": "konspirasyonteori", "universe": "linivè", "meditation": "meditasyon", "mythology": "<PERSON><PERSON><PERSON>", "art": "atizay", "crafts": "atizana", "dance": "danse", "design": "<PERSON><PERSON><PERSON>", "makeup": "ma<PERSON><PERSON><PERSON>", "beauty": "b<PERSON><PERSON><PERSON>", "fashion": "mòd", "singing": "chante", "writing": "ekri", "photography": "fotografi", "cosplay": "cosplay", "painting": "penti", "drawing": "desen", "books": "liv", "movies": "sinema", "poetry": "p<PERSON><PERSON>", "television": "televizyon", "filmmaking": "fèfilm", "animation": "animasyon", "anime": "anime", "scifi": "si<PERSON>k", "fantasy": "<PERSON><PERSON><PERSON>", "documentaries": "dokimantè", "mystery": "<PERSON><PERSON>", "comedy": "komedi", "crime": "krim", "drama": "dram", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "orè", "romance": "womans", "realitytv": "telereyalite", "action": "aksyon", "music": "mizik", "blues": "bluz", "classical": "klasik", "country": "peyi", "desi": "desi", "edm": "edm", "electronic": "elektwonik", "folk": "mounpèp", "funk": "funk", "hiphop": "hiphop", "house": "kay", "indie": "indie", "jazz": "jaz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "rege", "rock": "wòch", "techno": "techno", "travel": "v<PERSON>aj", "concerts": "konsè", "festivals": "festivites", "museums": "<PERSON><PERSON><PERSON>", "standup": "kanpe", "theater": "teyat", "outdoors": "deyo", "gardening": "jaden", "partying": "f<PERSON>t", "gaming": "j<PERSON><PERSON><PERSON>", "boardgames": "jwètdeso", "dungeonsanddragons": "<PERSON><PERSON>yed<PERSON><PERSON>", "chess": "echèk", "fortnite": "fortnite", "leagueoflegends": "l<PERSON><PERSON><PERSON><PERSON>", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "manje", "baking": "fèbonbon", "cooking": "kwi<PERSON>", "vegetarian": "vejet<PERSON><PERSON>", "vegan": "vejetalyen", "birds": "zwazo", "cats": "mimi", "dogs": "chen", "fish": "p<PERSON>on", "animals": "zannimo", "blacklivesmatter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "environmentalism": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "feminism": "feminism", "humanrights": "dwaleman", "lgbtqally": "alyelgbtq", "stopasianhate": "stop<PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "<PERSON><PERSON><PERSON><PERSON>", "volunteering": "volontarya", "sports": "espò", "badminton": "badminton", "baseball": "bezbol", "basketball": "b<PERSON><PERSON><PERSON>", "boxing": "b<PERSON>ks", "cricket": "kriket", "cycling": "<PERSON>k<PERSON>", "fitness": "fòm", "football": "foutbòl", "golf": "golf", "gym": "jim", "gymnastics": "<PERSON><PERSON><PERSON><PERSON>", "hockey": "oki", "martialarts": "<PERSON><PERSON><PERSON>", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "kouri", "skateboarding": "patin", "skiing": "ski", "snowboarding": "snowboarding", "surfing": "sèf", "swimming": "naje", "tennis": "tenis", "volleyball": "voleybol", "weightlifting": "levepwa", "yoga": "yoga", "scubadiving": "plonje", "hiking": "<PERSON><PERSON><PERSON>", "capricorn": "ka<PERSON>rikòn", "aquarius": "<PERSON><PERSON><PERSON><PERSON>", "pisces": "p<PERSON>on", "aries": "aries", "taurus": "toro", "gemini": "jimo", "cancer": "kans<PERSON>", "leo": "lyon", "virgo": "vyèj", "libra": "balans", "scorpio": "eskò<PERSON><PERSON>", "sagittarius": "<PERSON><PERSON><PERSON>", "shortterm": "koutèm", "casual": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "longtermrelationship": "relasyonalontèm", "single": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "polyamory": "polia<PERSON><PERSON>", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gason<PERSON>renmen<PERSON><PERSON>", "lesbian": "lezbyen", "bisexual": "biseksyèl", "pansexual": "panseksyèl", "asexual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "chyen<PERSON>e", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "rechè<PERSON><PERSON>", "soulreaver": "ravi<PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subvès", "legendofspyro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "rouguelikes", "syberia": "siberi", "rdr2": "rdr2", "spyrothedragon": "spyroldragon", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "ewodlatanpèt", "cytus": "cytus", "soulslike": "tankoujwèkianm", "dungeoncrawling": "fèdandjon", "jetsetradio": "jetsetradyo", "tribesofmidgard": "trib<PERSON><PERSON><PERSON>", "planescape": "avyonpeyizaj", "lordsoftherealm2": "lordsoftherealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "patfofegzil", "immersivesims": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "jwòdwòl", "witcher": "witcher", "dishonored": "<PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "konsekans", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "kreyekaraktè", "immersive": "<PERSON><PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivasyonmakab", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegames": "jwèotome", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vanpithemasquerade", "dimension20": "dimansyon20", "gaslands": "gaslandè", "pathfinder": "<PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2yèmedisyon", "shadowrun": "kourilonbraj", "bloodontheclocktower": "sansuòlòj", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "chokgravite", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yourturntodie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "malfektè", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "nanmdemon", "mu": "mu", "falloutshelter": "<PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "kachodonjonpif<PERSON>nwa", "eclipsephase": "fazeklips", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "lyeizak", "diabloimmortal": "djabimontel", "dynastywarriors": "dynastywarriors", "skullgirls": "skullgirls", "nightcity": "lavinuit", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "konbafoli", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "jw<PERSON><PERSON><PERSON><PERSON>", "gothamknights": "gothamknights", "forgottenrealms": "wayomkitebliye", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "pititmistik", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "ranchmonster", "ecopunk": "ekopink", "vermintide2": "ratinvazyon2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "twònfrakase", "horizonforbiddenwest": "horizonentèdi_lwès", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "<PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "djab", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "kraze", "lastepoch": "<PERSON><PERSON><PERSON><PERSON>ò<PERSON>", "starfinder": "chechwaetowal", "goldensun": "<PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "diviniteoriginè<PERSON><PERSON><PERSON>", "bladesinthedark": "lamdanfènwa", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "sibèpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "lòdtonbe", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "tè<PERSON>onnen", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "divinite", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "vyelemondogri", "adventurequest": "avantijkès", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "jwètwòljwe", "roleplayinggames": "jwètwòl", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "istwa_symphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "myfarog", "sacredunderworld": "mondsakreanba", "chainedechoes": "chainedechoes", "darksoul": "nanmdè<PERSON>ak", "soulslikes": "soulslik", "othercide": "lòtkote", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "pilyoletènite", "palladiumrpg": "palladiumrpg", "rifts": "div<PERSON>s", "tibia": "tibia", "thedivision": "divizyon", "hellocharlotte": "elocharlotte", "legendofdragoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vanpirolamaskarad", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "lougawouapokalip<PERSON>", "aveyond": "aveyond", "littlewood": "tibwa", "childrenofmorta": "pitit<PERSON><PERSON><PERSON>", "engineheart": "kèjenyan", "fable3": "fable3", "fablethelostchapter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "echanjesp<PERSON><PERSON>", "rollenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "chandetwal", "oldschoolrevival": "retoukoulòlalekòldantan", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "kachodonjon", "juegosrpg": "jwètrpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "klanmalkavian", "harvestella": "<PERSON><PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "kèsovaj", "bastion": "basyon", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "syèlarcadia", "shadowhearts": "kèsònb", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "sanp<PERSON>di", "breathoffire4": "souflidife4", "mother3": "manman3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "yon<PERSON>òteden", "roleplaygames": "jwètwòl", "roleplaygame": "jwòlwòl", "fabulaultima": "fabilima", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampimaskarad", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "kwazekwonotanp", "cocttrpg": "jwodwòltrpg", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "forumjwòl", "shadowheartscovenant": "konkòshadowheart", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "<PERSON>òmka<PERSON>", "awplanet": "aw<PERSON><PERSON><PERSON><PERSON>", "theworldendswithyou": "lemonn_ap_fini_avèw", "dragalialost": "dragazaljakap<PERSON><PERSON>", "elderscroll": "v<PERSON><PERSON><PERSON><PERSON>", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON>", "shoptitans": "<PERSON><PERSON><PERSON>", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "ma<PERSON><PERSON>", "blackbook": "kanètnwa", "skychildrenoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryrpg": "gryrpg", "sacredgoldedition": "edisyonlòsakre", "castlecrashers": "castlecrashers", "gothicgame": "gotikgame", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "jwètrpg", "prophunt": "jwetkachchekache", "starrails": "chemenzetwal", "cityofmist": "<PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "pwenteklike", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "enseparab", "freeside": "libète", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladium": "paladyòm", "knightjdr": "kavalyelatebd", "monsterhunter": "chasèmous", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "jeosi<PERSON><PERSON><PERSON>", "persona5": "persona5", "ghostoftsushima": "sentèmdetsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "nan<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "tacticalrpg", "mahoyo": "mahoyo", "animegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "eten<PERSON><PERSON><PERSON>", "princessconnect": "prensèskonekte", "hexenzirkel": "hexenzirkel", "cristales": "kristal", "vcs": "vks", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorant<PERSON>yen", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "espò", "mlg": "mlg", "leagueofdreamers": "ligrevè", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gamen", "overwatchleague": "lig<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cybersport": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crazyraccoon": "raton<PERSON>al", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantkompetitif", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead2": "left4dead2", "valve": "valv", "portal": "p<PERSON><PERSON><PERSON>", "teamfortress2": "teamfortress2", "everlastingsummer": "<PERSON>è<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "goatsimilatè", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "lib<PERSON><PERSON>è<PERSON><PERSON><PERSON><PERSON>", "transformice": "transformice", "justshapesandbeats": "jisfomakritem", "battlefield4": "battlefield4", "nightinthewoods": "nannbwa", "halflife2": "halflife2", "hacknslash": "k<PERSON><PERSON><PERSON><PERSON>", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanias", "overcooked": "twotwòp", "interplanetary": "entèplanetè", "helltaker": "helltaker", "inscryption": "enskripyon", "7d2d": "7d2d", "deadcells": "<PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "fòdmen", "foxhole": "<PERSON><PERSON><PERSON><PERSON>", "stray": "<PERSON><PERSON><PERSON><PERSON>", "battlefield": "bataylaviktwa", "battlefield1": "bataylaterèn1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "sousmaren", "eyeb": "<PERSON><PERSON><PERSON><PERSON>", "blackdesert": "black<PERSON>ert", "tabletopsimulator": "jeditabsimulate", "partyhard": "fetejiskalafen", "hardspaceshipbreaker": "espasvesodifisil", "hades": "hades", "gunsmith": "f<PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "p<PERSON><PERSON>ak<PERSON><PERSON>", "dinkum": "verite", "predecessor": "predesèsè", "rainworld": "<PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON>vq<PERSON>", "colonysim": "kolonisim", "noita": "noita", "dawnofwar": "lagèdegran", "minionmasters": "<PERSON><PERSON><PERSON><PERSON>", "grimdawn": "grimdawn", "darkanddarker": "nwaenwa", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "travayèlespri", "datingsims": "jwètrandevou", "yaga": "yaga", "cubeescape": "echapekib", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "nouvoville", "citiesskylines": "vilayosyè<PERSON>", "defconheavy": "defconlourd", "kenopsia": "kenopsia", "virtualkenopsia": "videken<PERSON>ia", "snowrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "bib<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "kenponespri", "placidplasticduck": "kanaplastiktrankil", "battlebit": "bataybit", "ultimatechickenhorse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dialtown": "vilkonvèsasyon", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "nuitchat", "supermeatboy": "supermeatboy", "tinnybunny": "tiptibounboun", "cozygrove": "cozygrove", "doom": "malè", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callof<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apex": "apèks", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "j<PERSON><PERSON><PERSON>far<PERSON>ry", "paladins": "paladins", "earthdefenseforce": "fòslatedefen", "huntshowdown": "lachasdemon", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "waz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultratouye", "joinsquad": "antrenanskwad", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "ensijan<PERSON><PERSON><PERSON><PERSON>tenpè<PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "kod<PERSON>mbi", "mirrorsedge": "mirrorsedge", "divisions2": "divizyon2", "killzone": "zonlanmò", "helghan": "hel<PERSON>", "coldwarzombies": "zonbigèf<PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "konbaayèl", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON><PERSON>", "modernwarfare": "lagègmòdèn", "neonabyss": "neonabim", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "f<PERSON><PERSON><PERSON>", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "back4blood": "bak4san", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "<PERSON><PERSON>n", "masseffect": "masseffect", "systemshock": "choksistèm", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "istwa_kav", "doometernal": "doometernel", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "jeran<PERSON><PERSON><PERSON>", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "j<PERSON>ayonzewo", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "nègsosis", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossfire": "tiraye", "atomicheart": "atomicheart", "blackops3": "blackops3", "vampiresurvivors": "vanpirsivivan", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "libète", "battlegrounds": "chandebatay", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "jwèpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "jwesfps", "convertstrike": "konv<PERSON><PERSON><PERSON><PERSON>", "warzone2": "warzone2", "shatterline": "liya<PERSON><PERSON>e", "blackopszombies": "zonbinwa", "bloodymess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "komanndorepiblik", "elitedangerous": "elitdan<PERSON><PERSON>", "soldat": "solda", "groundbranch": "groundbranch", "squad": "ekip", "destiny1": "destiny1", "gamingfps": "jwetfps", "redfall": "redfall", "pubggirl": "pubgfi", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "anrole", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pub<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "<PERSON><PERSON><PERSON>", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "p<PERSON><PERSON><PERSON><PERSON>", "ghostcod": "kodmò", "csplay": "jwètkaraktè", "unrealtournament": "<PERSON><PERSON><PERSON><PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pistolè<PERSON><PERSON><PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "chanpyontranblemandtè", "halo3": "halo3", "halo": "oreyòl", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "blanneon", "remnant": "rès", "azurelane": "azurelane", "worldofwar": "mondlag<PERSON>", "gunvolt": "gunvolt", "returnal": "retounen", "halo4": "alo4", "haloreach": "haloreach", "shadowman": "nègonb", "quake2": "tranblemanntè2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "mour<PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "lanmèvolè", "rust": "wouy", "conqueronline": "konkerianliy", "dauntless": "bravkè", "warships": "batolagè", "dayofdragons": "j<PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "vòlavyon", "recroom": "sal<PERSON><PERSON><PERSON><PERSON>", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "<PERSON><PERSON>", "phantasystaronline2": "phantasystaronline2", "maidenless": "sanfanm", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "mondtank", "crossout": "efase", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "<PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "supèanimalwayal", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "mnsp", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "eskrò", "newworld": "nouvo<PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "jwèpli<PERSON><PERSON><PERSON><PERSON>", "pirate101": "pirat101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "lag<PERSON><PERSON><PERSON><PERSON><PERSON>", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauri<PERSON><PERSON>", "wowclassic": "wowklasik", "worldofwarcraft": "mondwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "sanndkrey<PERSON>yon", "riotmmo": "riotmmo", "silkroad": "woutlaswa", "spiralknights": "spiralknights", "mulegend": "mulejann", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "pwofetidragon", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "milt<PERSON><PERSON><PERSON><PERSON>", "angelsonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsrepiblikansyen", "grandfantasia": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blueprotocol": "protokolble", "perfectworld": "lemonpafè", "riseonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "corepunk": "corepunk", "adventurequestworlds": "adventurequestworlds", "flyforfun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "animaljam", "kingdomofloathing": "<PERSON>òmre<PERSON><PERSON><PERSON>", "cityofheroes": "vildesewo", "mortalkombat": "mortalkombat", "streetfighter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "pou<PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "batayvirtyèl", "streetsofrage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mkdeadlyalliance": "alyanslanmòboo", "nomoreheroes": "pakgeneroken", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "waroyèkombatan", "likeadragon": "kouwagan", "retrofightinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blasphemous": "blasfèm", "rivalsofaether": "rival<PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "lagèmonsyo", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "cyberbot", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "finalfite", "poweredgear": "zouti_pwisan", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "bataydjwèt", "killerinstinct": "enstenmòtèl", "kingoffigthers": "wafigt<PERSON>", "ghostrunner": "kourèfantòm", "chivalry2": "chivalry2", "demonssouls": "namdemon", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "sekelholowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "jwetsilks<PERSON>", "silksongnews": "nouvèlsilksong", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON>", "typelumina": "typelum<PERSON>", "evolutiontournament": "evo<PERSON><PERSON><PERSON><PERSON><PERSON>", "evomoment": "evomoman", "lollipopchainsaw": "tiponponmitwayèz", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "tikontberseria", "bloodborne": "sanmennen", "horizon": "orizon", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "sanmaladi", "uncharted": "te<PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "d<PERSON>ny<PERSON>_nan_nou", "infamous": "enfame", "playstationbuddies": "zanmiplaystation", "ps1": "ps1", "oddworld": "lemondetrange", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "<PERSON>lop<PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "konpanivagabon", "aisomniumfiles": "dosyesomnyomai", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON>", "gris": "gri", "trove": "trez<PERSON>", "detroitbecomehuman": "detroitvinimen", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "jisk<PERSON>van", "touristtrophy": "<PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "lonbrajk<PERSON>s", "crashteamracing": "crashteamracing", "fivepd": "senkpd", "tekken7": "tekken7", "devilmaycry": "dyabkapabkriye", "devilmaycry3": "devilmaycry3", "devilmaycry5": "dyablkapabkriye5", "ufc4": "ufc4", "playingstation": "apjweavèkstation", "samuraiwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "soulblade", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "lachaslòm", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "kèsèkrè2alyans", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "fòs", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "chan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "<PERSON><PERSON><PERSON><PERSON>", "mhw": "mhw", "princeofpersia": "prenslapes", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "<PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "pamourityansanm", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "konekteavekzetwal", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "kayapkolokant", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligwayi", "fable2": "fab2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "televiyonfatra", "skycotl": "skycotl", "erica": "erica", "ancestory": "z<PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "tibon<PERSON><PERSON><PERSON>", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "p<PERSON><PERSON><PERSON>tè", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "moto", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kiltia<PERSON><PERSON><PERSON>", "duckgame": "jwètkannad", "thestanleyparable": "stanley<PERSON><PERSON><PERSON>", "towerunite": "towerunite", "occulto": "occulto", "longdrive": "w<PERSON><PERSON><PERSON>", "satisfactory": "pwòp", "pluviophile": "mounk<PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "<PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "jeometridach", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "pasajlespri", "darkdome": "<PERSON><PERSON><PERSON>", "pizzatower": "pizzatower", "indiegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itchio": "itchio", "golfit": "golfit", "truthordare": "veriteosinon", "game": "j<PERSON><PERSON><PERSON>", "rockpaperscissors": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "tranpolin", "hulahoop": "<PERSON><PERSON><PERSON><PERSON>", "dare": "defi", "scavengerhunt": "<PERSON><PERSON><PERSON>", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "chwaziyon", "trueorfalse": "vreofo", "beerpong": "byèpong", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "jwètbodam", "sodoku": "sodoku", "juegos": "j<PERSON><PERSON><PERSON>", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "jwodmo", "jeuxdemots": "jeudemo", "juegosdepalabras": "jwodmo", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "oyun", "interactivegames": "<PERSON>we<PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "konpetisyongadefikse", "spiele": "j<PERSON><PERSON><PERSON>", "giochi": "giochi", "geoguessr": "geoguessr", "iphonegames": "jwètiphone", "boogames": "boogames", "cranegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hideandseek": "ka<PERSON><PERSON><PERSON>", "hopscotch": "marelkoksèl", "arcadegames": "jwètakad", "yakuzagames": "yakuzagames", "classicgame": "jwetklasik", "mindgames": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "devinelyrics", "galagames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "langde<PERSON>e", "4xgames": "jwèt4x", "gamefi": "gamefi", "jeuxdarcades": "jwètakad", "tabletopgames": "jw<PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "jwèt90", "idareyou": "moundefiw", "mozaa": "mozaa", "fumitouedagames": "fumitouwedagèm", "racinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "reelvsfake", "playgames": "jwejw<PERSON><PERSON>", "gameonline": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegames": "j<PERSON><PERSON><PERSON><PERSON>", "jogosonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "roleplay<PERSON><PERSON>", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "desine", "coopgames": "koupje", "jenga": "jenga", "wiigames": "jwèt<PERSON><PERSON>", "highscore": "segondeplas", "jeuxderôles": "jwòdwòl", "burgergames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwedisyonnwa", "jeuconcour": "jek<PERSON><PERSON><PERSON>", "tcgplayer": "tcgplayer", "juegodepreguntas": "juegodepreguntas", "gioco": "gioco", "managementgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "jwèkachekache", "roolipelit": "jwètwòl", "formula1game": "formula1game", "citybuilder": "batilavil", "drdriving": "drdriving", "juegosarcade": "jwètakad", "memorygames": "jwèmemwa", "vulkan": "vulkan", "actiongames": "jwiaksyon", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "machinpinball", "oldgames": "jwèansyen", "couchcoop": "kouchk<PERSON><PERSON>", "perguntados": "kesyon", "gameo": "gameo", "lasergame": "jwètlazè", "imessagegames": "jwètimessage", "idlegames": "jwolouziv", "fillintheblank": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxpc": "jeuxpc", "rétrogaming": "retrogaming", "logicgames": "jwèlojik", "japangame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "remontejwèw", "subwaysurf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdecelebrite": "jwèdselebrite", "exitgames": "jesotigam", "5vs5": "5kont5", "rolgame": "jwòwòl", "dashiegames": "dashiegames", "gameandkill": "gameandkill", "traditionalgames": "jwèttradisyonèl", "kniffel": "kniffel", "gamefps": "jwèfps", "textbasedgames": "jekomsyotèks", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retwoespèl", "thiefgame": "jwètvòlè", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "babyfout", "tischfußball": "tischfußball", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jefo", "casualgames": "jez<PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "flech<PERSON><PERSON>", "escapegames": "jwevad", "thiefgameseries": "jwètvolè", "cranegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "játék": "<PERSON><PERSON><PERSON><PERSON>", "bordfodbold": "bordfodbold", "jogosorte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mage": "maj", "cargames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "sakamenbingo", "randomizer": "owaza", "msx": "msx", "anagrammi": "anagram", "gamespc": "jwòpc", "socialdeductiongames": "jwèdeduksyonsosyal", "dominos": "domino", "domino": "domino", "isometricgames": "jwèizometrik", "goodoldgames": "jwètansyenyo", "truthanddare": "veriteosinon", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "jw<PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "free2play": "grat<PERSON><PERSON><PERSON>", "fantasygame": "jwèfantezi", "gryonline": "gryonline", "driftgame": "jwèdrift", "gamesotomes": "jwètotome", "halotvseriesandgames": "seriakjewhalo", "mushroomoasis": "oazischanpiyon", "anythingwithanengine": "nenpòtkibagayakimotè", "everywheregame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "epeakmayi", "goodgamegiving": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "noujwe", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "jwèvideyo", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "renmentetwajwèt", "gamemodding": "modifikasyonjwèt", "crimegames": "jwèkrim", "dobbelspellen": "jwètdeso", "spelletjes": "j<PERSON><PERSON><PERSON>", "spacenerf": "spacenerf", "charades": "charad", "singleplayer": "singelpleyè", "coopgame": "jwètkòlaborasyon", "gamed": "gamed", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "jwè<PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "wakingdiscord", "scrabble": "skrabl", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "lido", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "camelup": "chamokouche", "monopolygame": "jwètmonopoli", "brettspiele": "brettspiele", "bordspellen": "jwètdeso", "boardgame": "jwèsosyete", "sällskapspel": "jwedsosyete", "planszowe": "planszowe", "risiko": "risk", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombisid", "tabletop": "soutab", "baduk": "baduk", "bloodbowl": "bloodbowl", "cluedo": "kousyonkrim", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "alejwetabosyete", "connectfour": "katkoneksyon", "heroquest": "kesyonewo", "giochidatavolo": "jwès<PERSON>latab", "farkle": "farkle", "carrom": "carrom", "tablegames": "tablej<PERSON><PERSON><PERSON>", "dicegames": "j<PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "jw<PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "jwètsosyete", "deskgames": "jwòbiwo", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "rankontkosmik", "creationludique": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "jwèdwòltabdwòl", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "jwètekomitatè", "infinitythegame": "infinitythegame", "kingdomdeath": "lanmòrwayòm", "yahtzee": "yahtzee", "chutesandladders": "g<PERSON><PERSON><PERSON>", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "j<PERSON><PERSON><PERSON>òtab", "planszówki": "jwètsosyete", "rednecklife": "vilavèy", "boardom": "lan<PERSON>", "applestoapples": "<PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "jwetsosyete", "gameboard": "jw<PERSON><PERSON><PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "jwèdsosyete", "twilightimperium": "twilightimperium", "horseopoly": "monopolcheval", "deckbuilding": "konstwikàt", "mansionsofmadness": "<PERSON><PERSON><PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "lonbrajbrimstone", "kingoftokyo": "wa<PERSON><PERSON><PERSON>", "warcaby": "<PERSON><PERSON><PERSON>", "táblajátékok": "jwètdetab", "battleship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tickettoride": "tik<PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "deskovehry", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "jwètdeto", "stolníhry": "jwètdetab", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "jwetsosyete", "gesellschaftsspiele": "jwèsosyete", "starwarslegion": "starwarslegion", "gochess": "<PERSON><PERSON><PERSON><PERSON><PERSON>è<PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "jwèdsosyete", "terraria": "terrarya", "dsmp": "dsmp", "warzone": "lagè", "arksurvivalevolved": "arksurvivalevolved", "dayz": "jou", "identityv": "identityv", "theisle": "zile", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "paminou", "eco": "eko", "monkeyisland": "z<PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "<PERSON><PERSON><PERSON><PERSON>", "daysgone": "jou<PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "witchit", "pathologic": "patolojik", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7jdtd", "thelongdark": "lagrannwa", "ark": "ark", "grounded": "atè", "stateofdecay2": "leta2dekonpozisyon", "vrising": "vrising", "madfather": "papa<PERSON><PERSON>", "dontstarve": "paki<PERSON>gan<PERSON>n", "eternalreturn": "re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "chem<PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexen": "<PERSON><PERSON><PERSON>", "theevilwithin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "realrac", "thebackrooms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backrooms": "backrooms", "empiressmp": "empiressmp", "blockstory": "istwa_blòk", "thequarry": "lamin", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "jwètthewalkingdead", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON>", "riseofempires": "leveanpi", "stateofsurvivalgame": "jwetsiviestado", "vintagestory": "istwa_an<PERSON>en", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "soufèlakòte", "alisa": "alisa", "westlendsurvival": "westlendsurvival", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "bwanwa", "survivalhorror": "or<PERSON><PERSON><PERSON>ò", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "lavi_apre_jwèt", "survivalgames": "jwèsivival", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "lagèmsayenm", "scpfoundation": "scpfoundation", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON>", "raft": "rad<PERSON>", "rdo": "rdo", "greenhell": "lan<PERSON>è<PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "polipolimò", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "granni", "littlenightmares2": "tikochmarenwa2", "signalis": "siyal", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "pititforè", "rustvideogame": "jwèvideorustla", "outlasttrials": "outlasttrials", "alienisolation": "izolasyonalyen", "undawn": "<PERSON><PERSON><PERSON>", "7day2die": "7<PERSON>2<PERSON><PERSON>", "sunlesssea": "lanmèsansolèy", "sopravvivenza": "siviv", "propnight": "nuitprop", "deadisland2": "ilmòyo2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathverse": "v<PERSON><PERSON><PERSON>", "cataclysmdarkdays": "kataklismjoutenèb", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "lavi_apre", "ageofdarkness": "epòkfènwa", "clocktower3": "latouòlòj3", "aloneinthedark": "pòkontmwanannwa", "medievaldynasty": "dinastymedyeval", "projectnimbusgame": "projectnimbusgame", "eternights": "n<PERSON><PERSON><PERSON><PERSON>", "craftopia": "craftopia", "theoutlasttrials": "theoutlasttrials", "bunker": "<PERSON><PERSON><PERSON>", "worlddomination": "do<PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "oficioassassinorum", "necron": "nekron", "wfrp": "wfrp", "dwarfslayer": "ti<PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kflann", "wh40": "wh40", "warhammer40klove": "ren<PERSON><PERSON><PERSON><PERSON>", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindicare": "<PERSON><PERSON><PERSON>", "ilovesororitas": "mwenrenmensòròrite", "ilovevindicare": "mwenrenmenvandike", "iloveassasinorum": "mwenrenmenassasinorum", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "fòdegen2", "wingspan": "anvègad", "terraformingmars": "teraformé<PERSON>s", "heroesofmightandmagic": "ewodmajikakfòs", "btd6": "btd6", "supremecommander": "kòmandansipwèm", "ageofmythology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "args": "args", "rime": "rim", "planetzoo": "<PERSON><PERSON><PERSON><PERSON>", "outpost2": "outpost2", "banished": "eg<PERSON>le", "caesar3": "seza3", "redalert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "sivilizasyon6", "warcraft2": "warcraft2", "commandandconquer": "kòmandeekòkraze", "warcraft3": "warcraft3", "eternalwar": "lagèetenèl", "strategygames": "jwèstrateji", "anno2070": "anno2070", "civilizationgame": "jwsivilizasyon", "civilization4": "sivilizasyon4", "factorio": "faktorio", "dungeondraft": "kachoprizon", "spore": "espò", "totalwar": "gètotan", "travian": "travian", "forts": "fò", "goodcompany": "bon<PERSON><PERSON><PERSON>", "civ": "siv", "homeworld": "<PERSON><PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "pirapid<PERSON><PERSON><PERSON><PERSON>", "forthekings": "pourowa", "realtimestrategy": "estratejitankontan", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "<PERSON><PERSON><PERSON>dek<PERSON><PERSON>", "eu4": "me4", "vainglory": "<PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "divinite", "anno": "anno", "battletech": "batèyteknik", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davesfunalgebraclass", "plagueinc": "plaginc", "theorycraft": "teorik<PERSON><PERSON><PERSON>", "mesbg": "mesbg", "civilization3": "sivilizasyon3", "4inarow": "4<PERSON><PERSON><PERSON><PERSON>", "crusaderkings3": "crusaderkings3", "heroes3": "ewo3", "advancewars": "lagègavanse", "ageofempires2": "lajdesanpi2", "disciples2": "disip2", "plantsvszombies": "plantkotzònbi", "giochidistrategia": "jwèestrateji", "stratejioyunları": "stratejioyunları", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "epòkmèvèy", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON>", "worldconquest": "mondkonkèt", "heartsofiron4": "kèfè4", "companyofheroes": "konpaniyewo", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "zwawakanadkana", "phobies": "fobi", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "j<PERSON>è<PERSON><PERSON>lashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON>yò", "turnbased": "to<PERSON>a", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "sivilizasyon5", "victoria2": "victoria2", "crusaderkings": "waroyal", "cultris2": "cultris2", "spellcraft": "ma<PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "lagèzetwalenpèrianalayè", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estrateji", "popfulmail": "popfulmail", "shiningforce": "fòslimyè", "masterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "pwogramsfèdyson", "transporttycoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unrailed": "sanray", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "toumanplanescapetorment", "uplandkingdoms": "wayòmupland", "galaxylife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "krazesp<PERSON><PERSON>", "battlecats": "chatbatay", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "be<PERSON>en<PERSON>tès", "needforspeedcarbon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "sèvivplilontan", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "antolojichevalnwa", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saiko": "saiko", "fatalframe": "ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "movivanka<PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON><PERSON>", "deadisland": "<PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzero": "projezewo", "horory": "orè", "jogosterror": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor": "bonjourvwazen", "helloneighbor2": "alowazenkòtlakay2", "gamingdbd": "jwètdbd", "thecatlady": "<PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "katkonlimnanite", "cribbage": "ekribaj", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinòk", "codenames": "kodsekrè", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legendofrun<PERSON>a", "solitaire": "solitè", "poker": "pok<PERSON>", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "tourcart", "playingcards": "kat<PERSON>we", "marvelsnap": "marvelsnap", "ginrummy": "<PERSON><PERSON><PERSON><PERSON>", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "ka<PERSON><PERSON>j", "pokemoncards": "kat<PERSON><PERSON><PERSON>", "fleshandbloodtcg": "tcgchèakzosangreyèl", "sportscards": "katspò", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "pik", "warcry": "k<PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "triko", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "rezistanslan", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "<PERSON><PERSON>kar<PERSON><PERSON>io<PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "ma<PERSON><PERSON><PERSON>n<PERSON>", "blueeyeswhitedragon": "jeblematkoulèlsyèldragblan", "yugiohgoat": "yugiohgoat", "briscas": "briska", "juegocartas": "jwèkat", "burraco": "bur<PERSON>o", "rummy": "remi", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "jwèd<PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "dye<PERSON>", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "katado", "sueca": "sueca", "beloteonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "batayespri", "battlespiritssaga": "batalesprisaga", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "žolíky": "žolíky", "facecard": "fijmwen", "cardfight": "bataykat", "biriba": "biriba", "deckbuilders": "konstrikèdèk", "marvelchampions": "chan<PERSON><PERSON><PERSON><PERSON>", "magiccartas": "katmajikyo", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "inokònyanstab", "cyberse": "<PERSON><PERSON><PERSON><PERSON>", "classicarcadegames": "jwetakadklasik", "osu": "osu", "gitadora": "gitadora", "dancegames": "jeudans", "fridaynightfunkin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "ewo<PERSON>ta", "clonehero": "clonehero", "justdance": "anndanse", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "dansantral", "rhythmgamer": "jwèritm", "stepmania": "stepmania", "highscorerythmgames": "segwopwenritmjwèt", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "danslanmouakglas", "auditiononline": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "juegosderitmo", "cryptofthenecrodancer": "kriptofthenekrodansè", "rhythmdoctor": "doktèritm", "cubing": "kib", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "jwètpuzzle", "spotit": "j<PERSON><PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blokdoku", "logicpuzzles": "pwoblèmlojik", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON>", "rubikscube": "rubikskib", "crossword": "mokwaze", "motscroisés": "mokwaze", "krzyżówki": "krzyżówki", "nonogram": "nonogram", "bookworm": "ratbiblyotèk", "jigsawpuzzles": "<PERSON><PERSON><PERSON><PERSON>", "indovinello": "keksyon", "riddle": "keksyon", "riddles": "<PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "andidan", "angrybirds": "zwazomove", "escapesimulator": "echapesimilatè", "minesweeper": "deminè", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "tigzèkwaze", "kurushi": "k<PERSON>hi", "gardenscapesgame": "jwègardenscapes", "puzzlesport": "jwètpuzzle", "escaperoomgames": "jweeskaproomyo", "escapegame": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "3dpuzzle", "homescapesgame": "jwethomescapes", "wordsearch": "<PERSON><PERSON><PERSON>", "enigmistica": "enigmistica", "kulaworld": "<PERSON><PERSON><PERSON><PERSON>", "myst": "myst", "riddletales": "tikridiswayo", "fishdom": "fishdom", "theimpossiblequiz": "kezenp<PERSON>sib", "candycrush": "candycrush", "littlebigplanet": "tipetitgwoplanèt", "match3puzzle": "jwètpuzzle3match", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "kibrikibik", "cuborubik": "kibrikrubik", "yapboz": "yapboz", "thetalosprinciple": "prensip<PERSON><PERSON>", "homescapes": "lakaykreyol", "puttputt": "minigolf", "qbert": "qbert", "riddleme": "v<PERSON><PERSON><PERSON>", "tycoongames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "kibrikribik", "cruciverba": "cruciver<PERSON>", "ciphers": "kòd", "rätselwörter": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "rezoudkastet", "turnipboy": "tigasongarçon", "adivinanzashot": "divinetshot", "nobodies": "pesonn", "guessing": "devine", "nonograms": "nonogram", "kostkirubika": "kostkirubika", "crypticcrosswords": "kwazemokache", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "krim<PERSON>", "quebracabeça": "quebracabeça", "hlavolamy": "palavolamy", "poptropica": "<PERSON><PERSON>pi<PERSON>", "thelastcampfire": "dènyekanpfi", "autodefinidos": "autodefinidos", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON>", "carto": "kato", "untitledgoosegame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "<PERSON><PERSON><PERSON><PERSON>", "limbo": "lanbo", "rubiks": "rubiks", "maze": "labirent", "tinykin": "tipitit", "rubikovakostka": "rubikovakostka", "speedcube": "kibwapitès", "pieces": "pyès", "portalgame": "jwètportal", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "p<PERSON><PERSON><PERSON><PERSON>", "rubixcube": "kibrikibik", "indovinelli": "keksyon", "cubomagico": "kibmagik", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "wonderland<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monopoly": "monopoli", "futurefight": "bataylaven", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "zetwalansanm", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "zetwalakim", "stateofsurvival": "etatdesurvival", "mycity": "v<PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "etajkolore", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "i<PERSON>è<PERSON><PERSON>", "knightrun": "<PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "bataysokè", "a3": "a3", "phonegames": "jwètelefòn", "kingschoice": "<PERSON><PERSON>wa", "guardiantales": "kontgadyen", "petrolhead": "fanatikwout", "tacticool": "tacticool", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "padans<PERSON><PERSON>", "craftsman": "atizan", "supersus": "sipèsispèk", "slowdrive": "rouledousman", "headsup": "atansyon", "wordfeud": "wodfèd", "bedwars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "freefire": "freefire", "mobilegaming": "jwetmobil", "lilysgarden": "j<PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymobile": "callofdutymobile", "thearcana": "arcana", "8ballpool": "8ballpool", "emergencyhq": "emergencyhq", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "tranbletekokote", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "prensèstan", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialejann", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON>n<PERSON>up<PERSON><PERSON>", "androidgames": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "criminalcase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "man<PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "jerepònskeske", "leagueofangels": "ligdezanj", "lordsmobile": "lordsmobile", "tinybirdgarden": "tijadenfl", "gachalife": "gachalife", "neuralcloud": "nwajnewonal", "mysingingmonsters": "chantemonstmwen", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "warrobots", "mirrorverse": "linivèparalèl", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "lèmamize", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobil", "ingress": "antre", "slugitout": "<PERSON>ay<PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameofsultans": "j<PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenaescape", "wolfy": "wolfy", "runcitygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegodemovil": "jwèmobil", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "imitasyon", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "bon<PERSON><PERSON><PERSON>", "ldoe": "ldoe", "legendonline": "lejandsoukoneksyon", "otomegame": "jwèotome", "mindustry": "mindustry", "callofdragons": "r<PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "nikkinap", "carxdriftracing2": "machinndriftcourse2", "pathtonowhere": "chemenokote", "sealm": "sealm", "shadowfight3": "bataylonb3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolisyondebi3", "wordswithfriends2": "pawòlakzanmi2", "soulknight": "cheval<PERSON><PERSON><PERSON>", "purrfecttale": "istwa_pafe", "showbyrock": "showbyrock", "ladypopular": "fi_popilè", "lolmobile": "lolmobil", "harvesttown": "v<PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "lemonpafèmobil", "empiresandpuzzles": "anpiakpuzzle", "empirespuzzles": "jwètanpiyo", "dragoncity": "dragoncity", "garticphone": "gatiktelefòn", "battlegroundmobileind": "batayterenmobilend", "fanny": "fani", "littlenightmare": "tiko<PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "machinterain", "tearsofthemis": "lame<PERSON><PERSON><PERSON><PERSON>", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zonbiyoegare", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "jènnfifrontline", "jurassicworldalive": "jurassicworldalive", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "femsoulaje", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "istwa_moonchai", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "jwèmobil", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "jwè<PERSON><PERSON><PERSON><PERSON>", "timeraiders": "timeraiders", "gamingmobile": "jwetmobilyo", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "lagèchat", "dnd": "padekouptelefonam", "quest": "<PERSON><PERSON><PERSON><PERSON>", "giochidiruolo": "jwètwòl", "dnd5e": "dnd5e", "rpgdemesa": "jwètdwòlsoulatab", "worldofdarkness": "mondtenèb", "travellerttrpg": "travellerttrpg", "2300ad": "2300ad", "larp": "jwòdwòl", "romanceclub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "d20": "d20", "pokemongames": "poke<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "ipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonpurpura": "poke<PERSON><PERSON><PERSON>", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "nati", "teamrocket": "ekiproket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON>ò<PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "ekipmistik", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "poke<PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "mendefer", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "p<PERSON><PERSON><PERSON><PERSON>", "heyyoupikachu": "eywoupika<PERSON><PERSON>", "pokémonmaster": "pokémon<PERSON>è<PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "timo<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "shinyhunter", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "skak", "ajedres": "echèk", "chessgirls": "fichèk", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "mondblitz", "jeudéchecs": "jeudéchecs", "japanesechess": "echecjapone", "chinesechess": "echèkchinwa", "chesscanada": "<PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "echèkpawòl", "openings": "ouvertures", "rook": "ruk", "chesscom": "chesscom", "calabozosydragones": "prizonakedwagon", "dungeonsanddragon": "donjonedragon", "dungeonmaster": "mètj<PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "prizoakdwagon", "oxventure": "oxventure", "darksun": "solèynwa", "thelegendofvoxmachina": "lejandvoxmachina", "doungenoanddragons": "donjoneakdwagon", "darkmoor": "darkmoor", "minecraftchampionship": "chan<PERSON><PERSON><PERSON>craft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "teslanmwen", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "flan<PERSON><PERSON><PERSON>", "fru": "fru", "addons": "ekstansyon", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmodifye", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "antreter", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "vilmine<PERSON>", "pcgamer": "jwèpcgame", "jeuxvideo": "jwevidyo", "gambit": "ganbit", "gamers": "gamers", "levelup": "nivosiperye", "gamermobile": "gamerimobil", "gameover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "j<PERSON><PERSON><PERSON><PERSON>tè", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "jwèpc", "casualgaming": "jwètdetant", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmasterrace", "pcgame": "jwèsoulòdinatè", "gamerboy": "<PERSON><PERSON><PERSON>", "vrgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drdisrespect": "drdisrespect", "4kgaming": "jwèt4k", "gamerbr": "gamerbr", "gameplays": "jwètvideyo", "consoleplayer": "jouwèkonsòl", "boxi": "boxi", "pro": "p<PERSON><PERSON><PERSON><PERSON>è<PERSON>", "epicgamers": "epikgamè", "onlinegaming": "<PERSON><PERSON><PERSON><PERSON>", "semigamer": "semijwè", "gamergirls": "fitigamè", "gamermoms": "mamanngamer", "gamerguy": "tigasonjwèt", "gamewatcher": "j<PERSON>è<PERSON><PERSON>", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "g<PERSON><PERSON><PERSON><PERSON>", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "<PERSON><PERSON><PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "<PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygaming": "jwètdetant", "gamelpay": "gamelpay", "juegosdepc": "jwòpc", "dsswitch": "dsch<PERSON><PERSON>", "competitivegaming": "j<PERSON><PERSON>tkompetitif", "minecraftnewjersey": "minecraftnewjersey", "faker": "fo", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "jwè<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "j<PERSON><PERSON><PERSON><PERSON><PERSON>tè", "girlsgamer": "figamè", "fnfmods": "fnfmods", "dailyquest": "defilajoune", "gamegirl": "figam", "chicasgamer": "figamè", "gamesetup": "konfigirasyon", "overpowered": "tw<PERSON>f<PERSON>", "socialgamer": "jwèsosyal", "gamejam": "gamejam", "proplayer": "proyopro", "roleplayer": "roleplayè", "myteam": "<PERSON><PERSON><PERSON>", "republicofgamers": "repiblikgamè", "aorus": "aorus", "cougargaming": "kougajwè<PERSON>", "triplelegend": "<PERSON><PERSON><PERSON><PERSON>", "gamerbuddies": "zanmijwèvideyo", "butuhcewekgamers": "bezwenfigamè", "christiangamer": "gamerkretyen", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "paptala", "andregamer": "andregamer", "casualgamer": "jw<PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "89squad", "inicaramainnyagimana": "kikòmansayefè", "insec": "ensékirite", "gemers": "gemè", "oyunizlemek": "oyunizlemek", "gamertag": "nongame", "lanparty": "lanparty", "videogamer": "videogamè", "wspólnegranie": "j<PERSON><PERSON><PERSON>", "mortdog": "mortdog", "playstationgamer": "jwètèplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "jw<PERSON><PERSON>", "gtracing": "<PERSON>ursmachin", "notebookgamer": "gamènotebook", "protogen": "protogen", "womangamer": "fanmgamer", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "humanfallflat": "mountonbe", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomizik", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "tidediritè", "switch": "chanje", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "avokakrim<PERSON><PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "timounsielnanlimyè", "tomodachilife": "lavikamarad", "ahatintime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "tearsofthekingdom", "walkingsimulators": "<PERSON>m<PERSON><PERSON><PERSON><PERSON>", "nintendogames": "nintendogames", "thelegendofzelda": "lejanddzel<PERSON>", "dragonquest": "dragonquest", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "<PERSON><PERSON><PERSON>", "breathofthewild": "soufbwa", "myfriendpedro": "zanmimpedro", "legendsofzelda": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51<PERSON><PERSON><PERSON><PERSON>", "earthbound": "teryen", "tales": "istwa", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "estrate<PERSON><PERSON><PERSON>", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "konkesfridegèl", "nintendos": "nintendos", "new3ds": "nouvo3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "<PERSON><PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "chichennitendo", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vanillalol": "<PERSON>i<PERSON><PERSON><PERSON><PERSON>", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "batayflash", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "<PERSON>p<PERSON><PERSON>", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsayiti", "aatrox": "aatrox", "euw": "b<PERSON>k", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "jwetlol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "pòthèks", "hextech": "hextech", "fortnitegame": "jeufòtnite", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "jev<PERSON><PERSON><PERSON>", "scaryvideogames": "jwevidyokifèpè", "videogamemaker": "kreatwòjwevideyo", "megamanzero": "megamanzero", "videogame": "jwèvideyo", "videosgame": "videoj<PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "batayb<PERSON>kteya", "arcades": "akad", "acnh": "acnh", "puffpals": "z<PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "<PERSON>mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "sanboxgames", "videogamelore": "istwa_jwètvideyo", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "revpeyi<PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "espaslanmò", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "videoj<PERSON><PERSON><PERSON>", "theoldrepublic": "lavieyrepiblik", "videospiele": "videoj<PERSON><PERSON><PERSON>", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "istwadessezon", "retrogames": "jwèretwo", "retroarcade": "jwetretro", "vintagecomputing": "òdinatèansyen", "retrogaming": "jwètretro", "vintagegaming": "jw<PERSON><PERSON>", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "enjistis2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "vitrankil", "beatmaniaiidx": "beatmaniaiidx", "steep": "apik", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievil": "<PERSON><PERSON><PERSON><PERSON>", "consolegaming": "jwètkonnsòl", "konsolen": "konsòl", "outrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "kesyonmonstèfi", "supergiant": "jeyan", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "jwè<PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "jwèansyen", "bethesda": "bethesda", "jackboxgames": "jwètjackbox", "interactivefiction": "fiksyonenteraktif", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "fanmakfanm", "visualnovel": "romanvisyèl", "visualnovels": "romanvisyèl", "rgg": "rgg", "shadowolf": "b<PERSON><PERSON><PERSON><PERSON>", "tcrghost": "tcrghost", "payday": "jou<PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "prens<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "jwèestetik", "novelavisual": "romanvizye", "thecrew2": "ekip2lan", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "jw<PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "lamendye", "leafblowerrevolution": "revolisyonsoufèyfèy", "wiiu": "wiiu", "leveldesign": "nivokonstriksyon", "starrail": "starrail", "keyblade": "k<PERSON><PERSON><PERSON>", "aplaguetale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fnafsometimes": "fnafsometimesht", "novelasvisuales": "romanvisyèl", "robloxbrasil": "robloxbrezil", "pacman": "pacman", "gameretro": "jw<PERSON><PERSON>", "videojuejos": "videoj<PERSON><PERSON><PERSON>", "videogamedates": "dat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "am<PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mòtalkombat11", "everskies": "everskies", "justcause3": "jiskepam3", "hulkgames": "jwèthulk", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "mansyonmanyak", "crashracing": "kousotomobil", "3dplatformers": "platfòm3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "lagèlanfè", "storygames": "jwètrepòtaj", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "evi<PERSON><PERSON>", "beyondtwosouls": "p<PERSON><PERSON><PERSON><PERSON>", "gameuse": "<PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "offmortisghost", "tinybunny": "tipitilapentyèt", "retroarch": "retroarch", "powerup": "powerup", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "aventuregrafik", "quickflash": "ekleraklè", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroakad", "f123": "f123", "wasteland": "tè<PERSON><PERSON>", "powerwashsim": "simlavajpresyon", "coralisland": "coralisland", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animegèrye2", "footballfusion": "footballfisyon", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astronè", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "metaltòde", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "pilwont", "simulator": "simi<PERSON><PERSON>", "symulatory": "simi<PERSON><PERSON>", "speedrunner": "speedrunner", "epicx": "epikx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ò", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "toontownrekriti", "simracing": "simracing", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON>", "seum": "seum", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "gadyensimityè", "spaceflightsimulator": "similat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "manje_ak_jw<PERSON>tvideyo", "oyunvideoları": "oyunvideoları", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "horizonworlds", "handygame": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "lejandakyejwovideyo", "oldschoolvideogames": "jwevideyo", "racingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "mizikpop", "famitsu": "famitsu", "gatesofolympus": "pò<PERSON><PERSON><PERSON><PERSON>", "monsterhunternow": "chasemonstwokounye", "rebelstar": "zet<PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indiegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogames": "endijwovideyo", "indievideogame": "jwèvideoyoendepandan", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bufffortress": "buff<PERSON><PERSON>", "unbeatable": "imbatab", "projectl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "futureclubgames": "jwetkliblavni", "mugman": "mugman", "insomniacgames": "jwekinsomniyak", "supergiantgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "pwogram", "gamebacklog": "jwòpoufjwe", "gamingbacklog": "jwèpamfinjwe", "personnagejeuxvidéos": "p<PERSON><PERSON>jjwevideyo", "achievementhunter": "chas<PERSON><PERSON><PERSON>", "cityskylines": "sitwèlvil", "supermonkeyball": "supermonkeyball", "deponia": "deponi", "naughtydog": "chensal", "beastlord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosretro": "jw<PERSON><PERSON>", "kentuckyroutezero": "woutkentuckyzero", "oriandtheblindforest": "oriakforenwaravèg", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "<PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "staxel": "staxel", "videogameost": "videogameost", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "mwenrenmenkofxv", "arcanum": "akann", "neoy2k": "neoy2k", "pcracing": "kouripcr", "berserk": "bezèk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "an<PERSON><PERSON><PERSON><PERSON>", "darkerthanblack": "pinoukebagay", "animescaling": "echèlanim", "animewithplot": "animeavèkentrig", "pesci": "pesci", "retroanime": "animeretwo", "animes": "anime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "anime90yo", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "metkabann", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "2000anime", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesezon1", "rapanime": "rapanime", "chargemanken": "chajmanken", "animecover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thevisionofescaflowne": "vizionescaflowne", "slayers": "slayers", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "p<PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "twaletlignenanako", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "brigadedife", "moriartythepatriot": "moriartypatriyòtla", "futurediary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fairytail": "kont", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "<PERSON><PERSON><PERSON>", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "melodisirenndlo", "kamisamakiss": "<PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "<PERSON><PERSON><PERSON>", "romancemangas": "mangaromantik", "karneval": "kanaval", "dragonmaid": "s<PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "laganwa", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "tè<PERSON><PERSON><PERSON>è", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungoustraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "yonsentenendèksmajik", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8enfiniti", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "zanjdèdlanmò", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "ipnosismik", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "<PERSON><PERSON><PERSON>", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animespò", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "jwètarwin", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "istwa_tanya_mechan_an", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "tigasonakb<PERSON><PERSON>", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "kijanpouwkenbenonmomi", "fullmoonwosagashite": "lanmèlplènwkapchache", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "martialpeak", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "<PERSON><PERSON><PERSON><PERSON>", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "animedantan", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "majordomnwa", "ergoproxy": "ergoproxy", "claymore": "gwo_zam", "loli": "loli", "horroranime": "animeterifyan", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "v<PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "peyi_pwom<PERSON><PERSON>_ki_pap_janm_rive", "monstermanga": "mangamonste", "yourlieinapril": "mantè<PERSON><PERSON><PERSON><PERSON>", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "serafenfinal", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "maji", "deepseaprisoner": "prizonyelanmèpwofon", "jojolion": "jojo<PERSON>", "deadmanwonderland": "peyi_mò_mèvèy", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "mari", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "kèpandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "lagèmanje", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "liyabòld<PERSON>b", "toyoureternity": "poutouletènite", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "r<PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "alyansek<PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "efase", "bluelock": "bluelock", "goblinslayer": "goblenslayer", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "batman<PERSON><PERSON><PERSON>", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "egzòsisble", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scry", "spyfamily": "fanmiespyon", "airgear": "airgear", "magicalgirl": "tifimajik", "thesevendeadlysins": "setpechemotel", "prisonschool": "prizonlekòl", "thegodofhighschool": "bondyelekòlsegondè", "kissxsis": "bobes<PERSON>", "grandblue": "gwoble", "mydressupdarling": "b<PERSON><PERSON><PERSON>òtmwen", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeinivè", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "<PERSON><PERSON><PERSON><PERSON>", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON>ov<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "manga_damou", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "k<PERSON>ai", "animeromance": "romansanime", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeajanten", "lolicon": "lolikon", "demonslayertothesword": "demonslayeraklèpèe", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adioseri": "adyoseri", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "zetwalaliyen", "romanceanime": "animweomantik", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "<PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "anrejistreragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "lise<PERSON><PERSON><PERSON>", "germantechno": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON>ò", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejapone", "animespace": "animespas", "girlsundpanzer": "tifiakechatank", "akb0048": "akb0048", "hopeanuoli": "espwaklèkèflèch", "animedub": "animedoublaj", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "animendyan", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "<PERSON><PERSON><PERSON>", "haremanime": "an<PERSON><PERSON><PERSON>", "kochikame": "kochikame", "nekoboy": "tigason", "gashbell": "gashbell", "peachgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cavalieridellozodiaco": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "k<PERSON><PERSON><PERSON>", "deliciousindungeon": "delikatoudandonjon", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "rejistragnarok", "funamusea": "pletiamizan", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "bochithewok", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "titoryeltwodifisil", "overgeared": "tw<PERSON><PERSON>e", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "atelyelòsèy", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "haremenvè", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "great<PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "<PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "revgranble", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "sanganime", "bloodc": "san", "talesofdemonsandgods": "istwa_demon_ak_bondye", "goreanime": "goreanime", "animegirls": "animefanm", "sharingan": "<PERSON><PERSON>", "crowsxworst": "kòwxpiwochen", "splatteranime": "animesang", "splatter": "ekla<PERSON><PERSON><PERSON>", "risingoftheshieldhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animeespay", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "rekòmandasyon", "childrenofthewhales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liarliar": "mantiman<PERSON><PERSON>", "supercampeones": "superchanpyon", "animeidols": "animeidol", "isekaiwasmartphone": "isekaitelefonmwen", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "maji<PERSON><PERSON>", "callofthenight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "jadensekrè", "tsubasachronicle": "tsubasachronicle", "findermanga": "j<PERSON><PERSON><PERSON><PERSON>", "princessjellyfish": "<PERSON>ns<PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekiss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animevès", "persocoms": "persocoms", "omniscientreadersview": "lektèkikonnentoubagay", "animecat": "chatanime", "animerecommendations": "rekòmandasyon_anime", "openinganime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "komediromantikadolesanmwen", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "wob<PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilfight<PERSON><PERSON><PERSON><PERSON>", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "klowòks", "deathnote": "nòtlanmò", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "avantibi<PERSON><PERSON><PERSON>", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animesolda", "greenranger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animevil", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "dig<PERSON><PERSON><PERSON><PERSON>", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "ataksoutitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "k<PERSON><PERSON><PERSON>s", "onepieceanime": "onepieceanime", "attaquedestitans": "atakdetitan", "theonepieceisreal": "the<PERSON><PERSON><PERSON><PERSON><PERSON>", "revengers": "revan<PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "joyboyeffect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimonstory": "istwa_digimon", "digimontamers": "digimontamers", "superjail": "superprizon", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonpafè", "kemonofriends": "zanmikaraktèbèt", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "lougawoulvole", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "paske", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "toutrisensent", "recuentosdelavida": "istwa_lavi_m"}