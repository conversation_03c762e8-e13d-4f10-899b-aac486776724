{"2048": "2048", "mbti": "mbti", "enneagram": "эннеаграмма", "astrology": "астрология", "cognitivefunctions": "когнитивдикфункциялар", "psychology": "психология", "philosophy": "философия", "history": "тарых", "physics": "физика", "science": "илим", "culture": "маданият", "languages": "тил<PERSON>ер", "technology": "технология", "memes": "мемдер", "mbtimemes": "mbtimemдер", "astrologymemes": "астрологиямемдер", "enneagrammemes": "эннеаграмамемдер", "showerthoughts": "душтагыойлор", "funny": "күлкүлүү", "videos": "видеолор", "gadgets": "гадже<PERSON><PERSON><PERSON>р", "politics": "саясат", "relationshipadvice": "мамилебоюнчакеңеш", "lifeadvice": "жашоокеңеши", "crypto": "крипто", "news": "жаңылыктар", "worldnews": "дүйнөжаңылыктары", "archaeology": "археология", "learning": "үйрөнүү", "debates": "дебат<PERSON><PERSON>р", "conspiracytheories": "конспирациятеориялары", "universe": "aa<PERSON>am", "meditation": "медитация", "mythology": "мифология", "art": "өнөр", "crafts": "колөнөр", "dance": "бийле", "design": "ди<PERSON><PERSON><PERSON>н", "makeup": "макияж", "beauty": "сулуулук", "fashion": "мода", "singing": "ырдоо", "writing": "жазуу", "photography": "сүрөт", "cosplay": "косплей", "painting": "сүрөт_тартуу", "drawing": "чийүү", "books": "китептер", "movies": "кинолор", "poetry": "поэзия", "television": "телевизор", "filmmaking": "кино<PERSON>улук", "animation": "анимация", "anime": "аниме", "scifi": "илимийфантастика", "fantasy": "фантазия", "documentaries": "документалдыктасмалар", "mystery": "сыр", "comedy": "комедия", "crime": "кылмыш", "drama": "драма", "bollywood": "болливуд", "kdrama": "кдорама", "horror": "коркунучтуу", "romance": "романтика", "realitytv": "реалити<PERSON><PERSON>у", "action": "экшн", "music": "музыка", "blues": "көңүлсүздүк", "classical": "классикалык", "country": "кыргызстан", "desi": "дези", "edm": "edm", "electronic": "электроника", "folk": "элдик", "funk": "фанк", "hiphop": "хипхоп", "house": "үй", "indie": "инди", "jazz": "жаз", "kpop": "кпоп", "latin": "латын", "metal": "металл", "pop": "поп", "punk": "панк", "rnb": "rnb", "rap": "рэп", "reggae": "регги", "rock": "рок", "techno": "техно", "travel": "саякат", "concerts": "концерттер", "festivals": "фестивалдар", "museums": "музейлер", "standup": "тикболуп", "theater": "театр", "outdoors": "табигатта", "gardening": "бакчачылык", "partying": "түнкүтүн", "gaming": "оюндар", "boardgames": "үстөлоюндары", "dungeonsanddragons": "зындандаржанаажыдаарлар", "chess": "шахмат", "fortnite": "фортнайт", "leagueoflegends": "лигалегенд", "starcraft": "starcraft", "minecraft": "майнкра<PERSON>т", "pokemon": "покемон", "food": "тамак", "baking": "печенье_жасоо", "cooking": "тамакжасоо", "vegetarian": "вегетариан", "vegan": "веган", "birds": "кана<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cats": "мышыктар", "dogs": "иттер", "fish": "балык", "animals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklivesmatter": "караадамдардынжашоомаанилүү", "environmentalism": "экологизм", "feminism": "феминизм", "humanrights": "адамукуктары", "lgbtqally": "лгбткколдоочу", "stopasianhate": "азиатта<PERSON>г<PERSON>жекkö<PERSON><PERSON>ÿнÿтоктотуу", "transally": "трансдарды_колдойм", "volunteering": "ыктыярчылык", "sports": "спорт", "badminton": "бадминтон", "baseball": "бейсбол", "basketball": "баскетбол", "boxing": "бокс", "cricket": "крикет", "cycling": "велотебүү", "fitness": "фитнес", "football": "футбол", "golf": "гольф", "gym": "спортзал", "gymnastics": "гимнастика", "hockey": "хоккей", "martialarts": "согушөнөрү", "netball": "нетбол", "pilates": "пилатес", "pingpong": "пингпонг", "running": "чуркоо", "skateboarding": "скейтборд", "skiing": "лыжа", "snowboarding": "сноуборд", "surfing": "сёрфинг", "swimming": "суугужуу", "tennis": "теннис", "volleyball": "волейбол", "weightlifting": "штангакөтөрүү", "yoga": "йога", "scubadiving": "акваланг", "hiking": "жөөсалып", "capricorn": "козерог", "aquarius": "суюучу", "pisces": "балык", "aries": "овен", "taurus": "торпок", "gemini": "жуптуздар", "cancer": "рак", "leo": "арстан", "virgo": "бик<PERSON><PERSON>", "libra": "тараза", "scorpio": "скорпион", "sagittarius": "стрелец", "shortterm": "кыскамөөнөттүү", "casual": "кадимки", "longtermrelationship": "узакмөөнөттүүмамиле", "single": "бойдок", "polyamory": "полиамория", "enm": "enm", "lgbt": "лгбт", "lgbtq": "лг<PERSON><PERSON>к", "gay": "гей", "lesbian": "лесбиянка", "bisexual": "бисексуал", "pansexual": "пансексуал", "asexual": "асексуал", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "ассасинскрид", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "иттер", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "падышалыкизденүү", "soulreaver": "жан<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ч", "suikoden": "суйкоден", "subverse": "субверсе", "legendofspyro": "спайролегендасы", "rouguelikes": "rouguelikes", "syberia": "сибирь", "rdr2": "rdr2", "spyrothedragon": "спайродракон", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "күнбатышжолдо", "arkham": "аркхам", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "йокайвотч", "rocksteady": "туруктуу", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "ачыкдүйнө", "heroesofthestorm": "heroesofthestorm", "cytus": "cytus", "soulslike": "жан<PERSON><PERSON><PERSON>ил", "dungeoncrawling": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>а<PERSON><PERSON>у", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribesofmidgard", "planescape": "учаксахна", "lordsoftherealm2": "лордторпадышалыктын2", "baldursgate": "балдурсгейт", "colorvore": "түстөрсүйүүчү", "medabots": "медаботтор", "lodsoftherealm2": "lordstherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "чөмүлтүүчөнсимдер", "okage": "окаге", "juegoderol": "ролдукоюн", "witcher": "ведьмак", "dishonored": "намыссыз", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "котор", "wynncraft": "wynn<PERSON>", "witcher3": "ведьмак3", "fallout": "фоллаут", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "элдерскроллс", "modding": "моддинг", "charactercreation": "кейипгерчүүлөө", "immersive": "чөмүлтүүчү", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "файналфэнтезиолдскул", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "файналфэнтези", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "лала<PERSON>елл", "dissidia": "диссидия", "finalfantasy7": "файналфэнтези7", "ff7": "ff7", "morbidmotivation": "караңгымотивация", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "отоме", "suckerforlove": "ашыкпаймын", "otomegames": "отомеоюндары", "stardew": "стардью", "stardewvalley": "stardewvalley", "ocarinaoftime": "окаринаубактысы", "yiikrpg": "yiikrpg", "vampirethemasquerade": "вампирдинмаскарады", "dimension20": "өлчөм20", "gaslands": "газже<PERSON><PERSON><PERSON>р", "pathfinder": "жол<PERSON><PERSON><PERSON><PERSON><PERSON>ч", "pathfinder2ndedition": "pathfinder2ндбасылымы", "shadowrun": "көлөкөчабуу", "bloodontheclocktower": "саатмунарасындагыкан", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "гравитациягакаршыкуруш", "rpg": "rpg", "dota2": "dota2", "xenoblade": "ксеноблейд", "oneshot": "бирмүмкүнчүлүк", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "кожоюн", "yourturntodie": "сенинөлүүчүкезегиң", "persona3": "persona3", "rpghorror": "rpgкор<PERSON><PERSON><PERSON><PERSON>ч", "elderscrollsonline": "элдерскроллсонлайн", "reka": "река", "honkai": "хонкай", "marauders": "талаптоноочулар", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "эпиксевен", "rpgtext": "rpgтекст", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "демонжандар", "mu": "mu", "falloutshelter": "жер_астындагы_баш_калкалоочу_жай", "gurps": "gurps", "darkestdungeon": "эңкараңгызындан", "eclipsephase": "тутулуубаскычы", "disgaea": "disgaea", "outerworlds": "сырткыдүйнөлөр", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "айзекбайлоо", "diabloimmortal": "diabloimmortal", "dynastywarriors": "династиясогушчулары", "skullgirls": "skullgirls", "nightcity": "түнкышаар", "hogwartslegacy": "хогвартсмурасы", "madnesscombat": "акылсызсалгылашуу", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "жол96", "vtmb": "vtmb", "chimeraland": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelike_оюндар", "gothamknights": "готэмрыцарлары", "forgottenrealms": "унутулганаалам", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "мульт<PERSON><PERSON><PERSON><PERSON>", "childoflight": "жарыктынбаласы", "aq3d": "aq3d", "mogeko": "могеко", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "дигимондүйнөсү", "monsterrancher": "монстрасмотрчу", "ecopunk": "экопанк", "vermintide2": "vermintide2", "xeno": "ксено", "vulcanverse": "вулкандүйнөсү", "fracturedthrones": "сынганчактар", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "көлөкөпанк", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "хогвартсыры", "deltagreen": "дель<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>л", "diablo": "диабло", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "смайт", "lastepoch": "акыркыдоор", "starfinder": "жылдызтапкыч", "goldensun": "алтынкүн", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "караңгыдагыканжарлар", "twilight2000": "сүрөткө2000", "sandevistan": "сандевистан", "cyberpunk": "киб<PERSON><PERSON><PERSON>анк", "cyberpunk2077": "киберпанк2077", "cyberpunkred": "киберпанкред", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "кулагандүзүм", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "жамандыкжерлер", "genshinimact": "гэншинимпакт", "aethyr": "aethyr", "devilsurvivor": "шай<PERSON><PERSON><PERSON>д<PERSON>наманкалган", "oldschoolrunescape": "эскимектепrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "аниме5е", "divinity": "кудайчылык", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "эскидүйнөкайгысы", "adventurequest": "укмуштуудайжорук", "dagorhir": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayingames": "ролдукоюндар", "roleplayinggames": "ролдукоюндар", "finalfantasy9": "finalfantasy9", "sunhaven": "күнпанасы", "talesofsymphonia": "симфониятариху", "honkaistarrail": "honkaistarrail", "wolong": "улуңдо", "finalfantasy13": "finalfantasy13", "daggerfall": "даггерфолл", "torncity": "жырты<PERSON><PERSON><PERSON>нша<PERSON>р", "myfarog": "менинфарогум", "sacredunderworld": "ыйыктуулдүнүя", "chainedechoes": "чынжырдынжаңырыктары", "darksoul": "караңгыжан", "soulslikes": "соулслайктар", "othercide": "башкаөлтүрүү", "mountandblade": "mountandblade", "inazumaeleven": "инадзумаэлевен", "acvalhalla": "acvalhalla", "chronotrigger": "хронотриггер", "pillarsofeternity": "түбөлүктүнтүркүктөрү", "palladiumrpg": "palladiumrpg", "rifts": "аж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "tibia": "тибия", "thedivision": "бөлүнүү", "hellocharlotte": "сала<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т", "legendofdragoon": "legendofdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "вампирдинмаскасы", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "карышкыркыяматкүнү", "aveyond": "эйвонд", "littlewood": "кичинекойтокой", "childrenofmorta": "мортанынбалдары", "engineheart": "моторжүрөк", "fable3": "жомок3", "fablethelostchapter": "жомоктожоголгонбөлүм", "hiveswap": "hiveswap", "rollenspiel": "рольдукоюн", "harpg": "harpg", "baldursgates": "балдурсгейтс", "edeneternal": "түбөлүктүүэден", "finalfantasy16": "finalfantasy16", "andyandleyley": "андыжаналейли", "ff15": "ff15", "starfield": "жылдызталаасы", "oldschoolrevival": "эскимектепкайрадан", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "жапайыдүйнөлөр", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "жүрөкпадышалыгы1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "караңгызындан", "juegosrpg": "rpgоюндары", "kingdomhearts": "кыролдукжүрөктөр", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "кланмалкавиан", "harvestella": "жыйынаар", "gloomhaven": "gloomhaven", "wildhearts": "жапайыжүрөктөр", "bastion": "бастион", "drakarochdemoner": "драконжанадемондор", "skiesofarcadia": "skiesofarcadia", "shadowhearts": "көлөкөжүрөктөр", "nierreplicant": "nierreplicant", "gnosia": "гнозия", "pennyblood": "канкесүүчү", "breathoffire4": "отжалыны4", "mother3": "эне3", "cyberpunk2020": "киберпанк2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "ролдукоюндар", "roleplaygame": "ролдукоюн", "fabulaultima": "укмуштуудайакыркы", "witchsheart": "бүбү_жүрөгү", "harrypottergame": "гаррипоттероюну", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "вампирлермаскарады", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "сыйкырдуучукораблик", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "хроноөтмө", "cocttrpg": "cocttrpg", "huntroyale": "аңчылыкпадышалыгы", "albertodyssey": "альбертодиссея", "monsterhunterworld": "монстрхантерворлд", "bg3": "bg3", "xenogear": "ксеногир", "temtem": "темтем", "rpgforum": "rpgфорум", "shadowheartscovenant": "шэдоухарттынкелишими", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "падышалыккелет", "awplanet": "awplanet", "theworldendswithyou": "дүйнөсенинмененбүтөт", "dragalialost": "dragalialost", "elderscroll": "аксакалдыңжазуусу", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "караңгыереси", "shoptitans": "shoptitans", "forumrpg": "форумrpg", "golarion": "голарион", "earthmagic": "жердинсыйкыры", "blackbook": "кара<PERSON>tep", "skychildrenoflight": "асманбалдары", "gryrpg": "gryrpg", "sacredgoldedition": "ыйыкалтынбасылыш", "castlecrashers": "сепилталкалоочулар", "gothicgame": "готикоюн", "scarletnexus": "скарлетнексус", "ghostwiretokyo": "ghostwiretokyoкыргызча", "fallout2d20": "fallout2d20", "gamingrpg": "оюнрпг", "prophunt": "пророндуу", "starrails": "жылдыздууреелдер", "cityofmist": "туман<PERSON><PERSON><PERSON>р", "indierpg": "индиrpg", "pointandclick": "черти<PERSON><PERSON><PERSON>с", "emilyisawaytoo": "эмилидагыжок", "emilyisaway": "эмиличыкты", "indivisible": "бөлүнбөс", "freeside": "боштарап", "epic7": "эпик7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "симбарум", "postcyberpunk": "посткиберпанк", "deathroadtocanada": "канадагабаргандыңжолу", "palladium": "пал<PERSON><PERSON><PERSON><PERSON>", "knightjdr": "рыц<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "монстргеаңчы", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "географиялыкүстөмдүк", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "секиро", "monsterhunterrise": "мостердовордончу", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "наригемдер", "tacticalrpg": "тактикалыкrpg", "mahoyo": "mahoyo", "animegames": "анимеоюндар", "damganronpa": "дамганронпа", "granbluefantasy": "granbluefantasy", "godeater": "куд<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ч", "diluc": "дил<PERSON>к", "venti": "венти", "eternalsonata": "түбөлүктүүсоната", "princessconnect": "принцессконнект", "hexenzirkel": "hexenzirkel", "cristales": "кристалл<PERSON>ар", "vcs": "венчуркапитал", "pes": "пес", "pocketsage": "чөнтөкакылман", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantиндиялык", "dota": "дота", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "эоюндар", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "киберспорт", "mlg": "млг", "leagueofdreamers": "кыялдан<PERSON>учуларлигасы", "fifa14": "fifa14", "midlaner": "мид<PERSON><PERSON><PERSON><PERSON><PERSON>р", "efootball": "efutbol", "dreamhack": "dreamhack", "gaimin": "гейминг", "overwatchleague": "overwatchлигасы", "cybersport": "киберспорт", "crazyraccoon": "жиндикөчкөн", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "электрондукжарыш", "brasilgameshow": "бразилиягеймшоу", "valorantcompetitive": "valorantмелдеш", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "портал2", "halflife": "жарымөмүр", "left4dead": "өлүккалган", "left4dead2": "left4dead2", "valve": "клапан", "portal": "портал", "teamfortress2": "teamfortress2", "everlastingsummer": "түбөлүктүүжай", "goatsimulator": "эчкисимулятору", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "эркиндикпланетасы", "transformice": "transformice", "justshapesandbeats": "жөнгөйформалардарыбийлер", "battlefield4": "battlefield4", "nightinthewoods": "токойдагытүн", "halflife2": "halflife2", "hacknslash": "чабуукесүү", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "метроидваниялар", "overcooked": "күйүпкеткен", "interplanetary": "планеталараралык", "helltaker": "жерөзү", "inscryption": "инскрипшн", "7d2d": "7к2к", "deadcells": "өлүкклеткалар", "nierautomata": "нирaut<PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "жаманкалааоюну", "foxhole": "окоп", "stray": "тентип", "battlefield": "жоокерталаасы", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "суүзмөкеме", "eyeb": "айаб", "blackdesert": "караталаа", "tabletopsimulator": "үстөлоюндары", "partyhard": "түнкүсүнтосунгонбийле", "hardspaceshipbreaker": "катуумейкиндиккемесындыргыч", "hades": "гадес", "gunsmith": "курал_устасы", "okami": "оками", "trappedwithjester": "жестерменентуткунда", "dinkum": "динкум", "predecessor": "предшественник", "rainworld": "жамгырдүйнөсү", "cavesofqud": "qudүңгүрлөрү", "colonysim": "колониясим", "noita": "noita", "dawnofwar": "согуштундаңаты", "minionmasters": "миньондорустаты", "grimdawn": "grimdawn", "darkanddarker": "караңгыданкараңгыга", "motox": "motox", "blackmesa": "караме́за", "soulworker": "жан<PERSON><PERSON><PERSON>а<PERSON><PERSON>ы", "datingsims": "симуляциялыксүйүүоюндары", "yaga": "йага", "cubeescape": "кубдантүтүү", "hifirush": "салкынагым", "svencoop": "svencoop", "newcity": "жаңышаар", "citiesskylines": "шаарлардынасманы", "defconheavy": "катуукырдаал", "kenopsia": "кеңдүнүйөбошболгондо", "virtualkenopsia": "виртуалдыккенопсия", "snowrunner": "кар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>у", "libraryofruina": "руинакитепканасы", "l4d2": "l4d2", "thenonarygames": "нонарыоюндар", "omegastrikers": "omegaстрайкерс", "wayfinder": "жол<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>у", "kenabridgeofspirits": "кенарухтардынкөпүрөсү", "placidplasticduck": "тынчпластикөрдөк", "battlebit": "согушбит", "ultimatechickenhorse": "ултиматтоокбалапан", "dialtown": "шаардыкчалуу", "smileforme": "менүчүнжылмай", "catnight": "мышыктүн", "supermeatboy": "супермитбой", "tinnybunny": "кичинекейкоён", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "карг<PERSON>ш", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "чекара<PERSON><PERSON>р", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "палади<PERSON><PERSON><PERSON>р", "earthdefenseforce": "жердикоргонүүкүчтөрү", "huntshowdown": "huntshowdown", "ghostrecon": "призрак_барлагыч", "grandtheftauto5": "grandtheftauto5", "warz": "согуштар", "sierra117": "сиерра117", "dayzstandalone": "dayzstandalone", "ultrakill": "өтөөлтүрүү", "joinsquad": "командагакошул", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "козголоңчылыккумчабуулу", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "макспейн", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "өлүмкапкан", "b4b": "б4б", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "кодзомби", "mirrorsedge": "айнектинкыры", "divisions2": "бөлүмдөр2", "killzone": "өлтүрүүзонасы", "helghan": "hel<PERSON>", "coldwarzombies": "муздакмайданзомбилер", "metro2033": "метро2033", "metalgear": "metalgear", "acecombat": "эйскомбат", "crosscode": "крестдоо", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "мергенэлита", "modernwarfare": "заманба<PERSON>согуш", "neonabyss": "неонтуңгуюк", "planetside2": "planetside2", "mechwarrior": "мехсогушкер", "boarderlands": "чекаралыкжерлер", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "тарковдонкачуу", "metalslug": "металслаг", "primalcarnage": "алгачкыкыргын", "worldofwarships": "worldofwarships", "back4blood": "кангаалбагандык", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "киллер", "masseffect": "массэффект", "systemshock": "системашок", "valkyriachronicles": "валькириялыкжылнаамалар", "specopstheline": "спецоперацияларчеги", "killingfloor2": "killingfloor2", "cavestory": "үңкүрокуясы", "doometernal": "түбөлүктүүкырылуу", "centuryageofashes": "centuryageofashesоюну", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "дивизия2", "tythetasmaniantiger": "тытасманиялыкжолборс", "generationzero": "нөлмуун", "enterthegungeon": "окатынкир", "jakanddaxter": "джэ<PERSON>жанадэкстер", "modernwarfare2": "модернварфар2", "blackops1": "blackops1", "sausageman": "сосискачелек", "ratchetandclank": "рэтчетжанакланк", "chexquest": "chexquest", "thephantompain": "элесоору", "warface": "согушжүзү", "crossfire": "крестаттуу_от", "atomicheart": "атомдүкжүрөк", "blackops3": "blackops3", "vampiresurvivors": "вампирлердентирикалуу", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "эркиндик", "battlegrounds": "батлгр<PERSON><PERSON>нддар", "frag": "фраг", "tinytina": "кичинекейтина", "gamepubg": "pubgоюну", "necromunda": "некромунда", "metalgearsonsoflibert": "металгирсынулдарынынэркиндиги", "juegosfps": "fpsoюндары", "convertstrike": "convertstrike", "warzone2": "warzone2", "shatterline": "сызыкбузуу", "blackopszombies": "blackopszombies", "bloodymess": "кандууб<PERSON>шаламандык", "republiccommando": "республикандыккомандос", "elitedangerous": "элитакоркунучтуу", "soldat": "солдат", "groundbranch": "groundbranch", "squad": "тусовка", "destiny1": "кадыр1", "gamingfps": "оюнfps", "redfall": "кызылкүз", "pubggirl": "pubgкыз", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "келишилди", "farlight": "узакжарык", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "темирмыктуу", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "айлыкалдым2", "cs16": "cs16", "pubgindonesia": "pubgиндонезия", "pubgukraine": "pubgук<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "пабгомания", "empyrion": "эмпирион", "pubgczech": "pubgчех", "titanfall2": "titanfall2", "soapcod": "самындыктреска", "ghostcod": "арба<PERSON><PERSON><PERSON><PERSON><PERSON>к", "csplay": "косплей", "unrealtournament": "ыйкымсызтурнир", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "тапанчаменчабуу", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "гало", "killingfloor": "өлтүрүүполу", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "неонак", "remnant": "калдык", "azurelane": "azurelane", "worldofwar": "согушдүйнөсү", "gunvolt": "gunvolt", "returnal": "кайтып_келүү", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "көлөкөадам", "quake2": "quake2", "microvolts": "микровольттор", "reddead": "кызылөлүк", "standoff2": "standoff2", "harekat": "ара<PERSON><PERSON>т", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "элсворд", "seaofthieves": "деңизделикаракчылар", "rust": "ржавчина", "conqueronline": "conqueronline", "dauntless": "кайратт<PERSON>у", "warships": "согушкемелери", "dayofdragons": "ажыдаарларкүнү", "warthunder": "warthunder", "flightrising": "flightrising", "recroom": "recroom", "legendsofruneterra": "legendsofruneterra", "pso2": "pso2", "myster": "мистер", "phantasystaronline2": "фантазистаронлайн2", "maidenless": "кызсыз", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "дүйнөлүктанктар", "crossout": "кесипсал", "agario": "agario", "secondlife": "экинчижашоо", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "онлайноюн", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "нинокуникроссдүйнө", "reddeadonline": "reddeadonline", "superanimalroyale": "супера<PERSON>б<PERSON>наткоролдугу", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "найтон<PERSON>айн", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "клубпингвин", "lotro": "lotro", "wakfu": "вакфу", "scum": "алсыз", "newworld": "жаңыдүйнө", "blackdesertonline": "blackdesertonline", "multiplayer": "көпоюнчу", "pirate101": "корсар101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsсогушмайданы", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "понита<PERSON>н", "3dchat": "3dчат", "nostale": "ностале", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "неопеттер", "moba": "моба", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "ashesofcreationкр", "riotmmo": "riotmmo", "silkroad": "жибекжолу", "spiralknights": "спиральныерыцарлар", "mulegend": "muлегенда", "startrekonline": "стартрекондуку", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "драконтаргыбыздындайын", "grymmo": "гриммо", "warmane": "вармейн", "multijugador": "көпоюнчу", "angelsonline": "периштеле<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcaaламыонлайн", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsэскиреспублика", "grandfantasia": "гранфантазия", "blueprotocol": "blueprotocol", "perfectworld": "идеалдуудүнйө", "riseonline": "онлайнөс", "corepunk": "корпанк", "adventurequestworlds": "укмуштуудуйнолор", "flyforfun": "учууменензавалан", "animaljam": "жаныбарларжыйыны", "kingdomofloathing": "күлкүлүүпадышалык", "cityofheroes": "баа<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ы", "mortalkombat": "мортлкомбат", "streetfighter": "көчөмушкер", "hollowknight": "hollowknight", "metalgearsolid": "металгирсолид", "forhonor": "намысүчүн", "tekken": "теккен", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "мультиверсус", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "виртуалсогушкер", "streetsofrage": "көчөлөрдөгүачуу", "mkdeadlyalliance": "mkөлүмгөдуштукалдашуу", "nomoreheroes": "баатырларбүттү", "mhr": "mhr", "mortalkombat12": "морталкомбат12", "thekingoffighters": "thekingoffighters", "likeadragon": "аждаадай", "retrofightinggames": "ретроуруштуучуоюндар", "blasphemous": "тазалыккакаршы", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "супербашта<PERSON>гыш", "mugen": "муген", "warofthemonsters": "монстрлардынсогушу", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "кибер_боттор", "armoredwarriors": "курал_куралданган_жоокерлер", "finalfight": "акыркысалгылашуу", "poweredgear": "күчтүүжабдык", "beatemup": "урупташтыр", "blazblue": "blazblue", "mortalkombat9": "мортолкомбат9", "fightgames": "күрөшоюндары", "killerinstinct": "өлтүргүчинстинкт", "kingoffigthers": "жоокерлердинпадышасы", "ghostrunner": "арбакжөө", "chivalry2": "рыцарлык2", "demonssouls": "демонру<PERSON>у", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightсиквели", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "жибекырчыкаары", "silksonggame": "silksonggame", "silksongnews": "silksongnews", "silksong": "жибектүүыр", "undernight": "түнүчүндө", "typelumina": "жазуунунжарыгы", "evolutiontournament": "эволюциятурнири", "evomoment": "evomoment", "lollipopchainsaw": "чупачупсчынжырбычак", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "канду<PERSON><PERSON><PERSON><PERSON>ун", "horizon": "горизонт", "pathofexile": "pathofexile", "slimerancher": "слаймфермер", "crashbandicoot": "крашбандикут", "bloodbourne": "кандыноокот", "uncharted": "жаңы_жерлер", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "биздинакыркы", "infamous": "атактуу", "playstationbuddies": "playstationдостор", "ps1": "ps1", "oddworld": "кызыктайдүнүйө", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "сплитгейт", "persona4": "персона4", "hellletloose": "салсалыпкой", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aiсомниумфайлс", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "куда<PERSON><PERSON>огуш", "gris": "грис", "trove": "кен<PERSON>", "detroitbecomehuman": "детройтадамболуу", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "таңаткычейин", "touristtrophy": "туристтиктрофей", "lspdfr": "lspdfr", "shadowofthecolossus": "колосстунколокосу", "crashteamracing": "crashteamracing", "fivepd": "бешpd", "tekken7": "tekken7", "devilmaycry": "шайта<PERSON><PERSON><PERSON>лашыболот", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playstation_ойноп", "samuraiwarriors": "самур<PERSON><PERSON><PERSON>оочулар", "psvr2": "psvr2", "thelastguardian": "акыркысакчы", "soulblade": "жа<PERSON><PERSON><PERSON><PERSON><PERSON>ч", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "эркектерменталда<PERSON>уу", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "акыркысакчы", "xboxone": "xboxone", "forza": "форза", "cd": "cd", "gamepass": "оюнпасс", "armello": "armello", "partyanimal": "тусовщик", "warharmmer40k": "warhammer40k", "fightnightchampion": "мушта<PERSON>уукечесичемпиону", "psychonauts": "психонавттар", "mhw": "mhw", "princeofpersia": "персияныңханзадасы", "theelderscrollsskyrim": "элдерскроллсскайрим", "pantarhei": "пант<PERSON><PERSON><PERSON>й", "theelderscrolls": "кыргызулуттар", "gxbox": "gxbox", "battlefront": "согушмайданы", "dontstarvetogether": "ачкаөлбөйлүбиргеболойлу", "ori": "ori", "spelunky": "спелунки", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "жылдызгабайланган", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "үйсатуучу", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "падышалардынлигасы", "fable2": "эртеги2", "xboxgamepass": "xboxгеймпасс", "undertale": "undertale", "trashtv": "таштелевизор", "skycotl": "асманкотл", "erica": "эрика", "ancestory": "ат<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "кичинекимкыздынкырсыгы", "sallyface": "сэллифейс", "franbow": "фрэнбоу", "monsterprom": "монстрпром", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "мотоциклдер", "outerwilds": "сырткыжапайылар", "pbbg": "pbbg", "anshi": "ан<PERSON>и", "cultofthelamb": "козунункультусу", "duckgame": "өрдөкоюну", "thestanleyparable": "стэнлипарабола", "towerunite": "мунарабирикте", "occulto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "longdrive": "узакжол", "satisfactory": "канааттандырарлык", "pluviophile": "жамгырсүйөр", "underearth": "жералдында", "assettocorsa": "assettocorsa", "geometrydash": "геометриядаш", "kerbal": "кербал", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "кенши", "spiritfarer": "жандымчы", "darkdome": "караңгыкуббо", "pizzatower": "пиццамунара", "indiegame": "индиоюн", "itchio": "itchio", "golfit": "гольфта", "truthordare": "чындыкжедосалам", "game": "ойун", "rockpaperscissors": "таштармаккагаз", "trampoline": "батут", "hulahoop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dare": "эр", "scavengerhunt": "издөөоюну", "yardgames": "короочойуну", "pickanumber": "номертанда", "trueorfalse": "жалга<PERSON>бычынбы", "beerpong": "пивопонг", "dicegoblin": "сөөктотунткун", "cosygames": "жайлуу_оюндар", "datinggames": "сүйлөшүүоюндары", "freegame": "бекер<PERSON><PERSON>ун", "drinkinggames": "ичүүоюндары", "sodoku": "судоку", "juegos": "оюндар", "mahjong": "маджонг", "jeux": "оюндар", "simulationgames": "симуляцияоюндары", "wordgames": "сөзоюндары", "jeuxdemots": "сөзоюндары", "juegosdepalabras": "сөзоюндары", "letsplayagame": "ойнойолу", "boredgames": "зериктиргеноюндар", "oyun": "оюн", "interactivegames": "интерактивдүүоюндар", "amtgard": "amtgard", "staringcontests": "бактырыш_мелдештери", "spiele": "ойноо", "giochi": "оюндар", "geoguessr": "geoguessr", "iphonegames": "iphoneоюндары", "boogames": "boo_оюндары", "cranegame": "крандыкоюну", "hideandseek": "жашынмак", "hopscotch": "секирмеойну", "arcadegames": "аркадаоюндар", "yakuzagames": "якузаоюндары", "classicgame": "классикалыкоюн", "mindgames": "акылоюндары", "guessthelyric": "лирика<PERSON><PERSON><PERSON><PERSON>п", "galagames": "galagames", "romancegame": "сүйүүоюну", "yanderegames": "yandere<PERSON>юндар", "tonguetwisters": "тилчалгычтар", "4xgames": "4xоюндар", "gamefi": "gamefi", "jeuxdarcades": "аркадаоюндары", "tabletopgames": "үстөлоюндары", "metroidvania": "metroidvania", "games90": "оюндар90", "idareyou": "сенибатынасыңбы", "mozaa": "mozaa", "fumitouedagames": "fumitouedaоюндары", "racinggames": "автож<PERSON><PERSON><PERSON>штар", "ets2": "ets2", "realvsfake": "чынэмес", "playgames": "оюноюноо", "gameonline": "онлайноюн", "onlinegames": "онлайноюндар", "jogosonline": "онлайноюндар", "writtenroleplay": "жазылганролдукоюн", "playaballgame": "топойноштук", "pictionary": "сүрөтменентабуу", "coopgames": "биргеойноолор", "jenga": "дженга", "wiigames": "wiiоюндары", "highscore": "жогоркыупай", "jeuxderôles": "ролдукоюндар", "burgergames": "бургероюндары", "kidsgames": "балдароюндары", "skeeball": "скибол", "nfsmwblackedition": "nfsmwкарабаскысы", "jeuconcour": "оюнконкурс", "tcgplayer": "tcgplayer", "juegodepreguntas": "суроолороюну", "gioco": "gioco", "managementgame": "башка<PERSON><PERSON><PERSON><PERSON>юну", "hiddenobjectgame": "жашырылганбуюмоюну", "roolipelit": "roolиоюндар", "formula1game": "formula1оюну", "citybuilder": "шаар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>у", "drdriving": "dr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "аркадаоюндары", "memorygames": "эсоюндары", "vulkan": "вулкан", "actiongames": "экшеноюндар", "blowgames": "үйлөөоюндары", "pinballmachines": "пинболма<PERSON>иналары", "oldgames": "эскиоюндар", "couchcoop": "диванкооп", "perguntados": "суроолор", "gameo": "оюндар", "lasergame": "лазерта<PERSON>мак", "imessagegames": "imessageоюндары", "idlegames": "боштукоюндар", "fillintheblank": "толтуртекстти", "jeuxpc": "компьютероюндары", "rétrogaming": "ретроойндор", "logicgames": "логикалыкоюндар", "japangame": "япониялыкоюн", "rizzupgame": "rizzupgame", "subwaysurf": "метродатолкуну", "jeuxdecelebrite": "атактуужылдыздароюндары", "exitgames": "чыгышоюндары", "5vs5": "5ке5", "rolgame": "ролоюну", "dashiegames": "dashiegames", "gameandkill": "оюндаөлтүр", "traditionalgames": "салттууоюндар", "kniffel": "книффель", "gamefps": "оюнfps", "textbasedgames": "текстүүоюндар", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "фантакалчо", "retrospel": "ретроспел", "thiefgame": "ууружайоюну", "lawngames": "газончоюндар", "fliperama": "флиперама", "heroclix": "heroclix", "tablesoccer": "үстөлфутболу", "tischfußball": "тишфусболл", "spieleabende": "оюнкечелери", "jeuxforum": "jeuxforum", "casualgames": "жөнөкөйоюндар", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "качууоюндары", "thiefgameseries": "ууругоюндарсериясы", "cranegames": "крандуюнчалыш", "játék": "оюн", "bordfodbold": "үстөлфутболу", "jogosorte": "жогосорте", "mage": "сыйкырчы", "cargames": "машинаоюндары", "onlineplay": "онлайнойноо", "mölkky": "мөлкки", "gamenights": "оюнкечелер", "pursebingos": "сумкабинголор", "randomizer": "рандомайзер", "msx": "msx", "anagrammi": "анаграмма", "gamespc": "компьютердикоюндар", "socialdeductiongames": "коомдуктабуучуоюндар", "dominos": "домино", "domino": "домино", "isometricgames": "изометриялыкоюндар", "goodoldgames": "жакшыэскиоюндар", "truthanddare": "чындыкжанатайындама", "mahjongriichi": "маджонгриичи", "scavengerhunts": "издөөоюндары", "jeuxvirtuel": "виртуалдыкоюндар", "romhack": "ромхак", "f2pgamer": "f2pоюнчу", "free2play": "бекеройноо", "fantasygame": "фантазияоюну", "gryonline": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "driftgame": "дрифтоюну", "gamesotomes": "отомоюндар", "halotvseriesandgames": "haloтвсериалдарыжанаоюндар", "mushroomoasis": "козукарындыоазис", "anythingwithanengine": "кыймылдаткычыбарболсоболот", "everywheregame": "баардыкжердеоюн", "swordandsorcery": "кылы<PERSON>жанасыйкыр", "goodgamegiving": "жакшыоюнберүү", "jugamos": "ойнойбуз", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "компьютероюндары", "virgogami": "бикечбүктөө", "gogame": "оюнга", "jeuxderythmes": "ритмоюндары", "minaturegames": "миниатюралыкоюндар", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "өзүнөзүсүйүүоюндары", "gamemodding": "оюнмоддоо", "crimegames": "кылмышоюндары", "dobbelspellen": "настолкалар", "spelletjes": "оюндар", "spacenerf": "космоснерф", "charades": "жаңылмач", "singleplayer": "жалгызоюнчу", "coopgame": "биргелешипойноо", "gamed": "ойнодум", "forzahorizon": "forzahorizon", "nexus": "бай<PERSON><PERSON><PERSON><PERSON>ш", "geforcenow": "geforcenow", "maingame": "негизгиоюн", "kingdiscord": "падышадискорд", "scrabble": "скраббл", "schach": "шахмат", "shogi": "шоги", "dandd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catan": "катан", "ludo": "людо", "backgammon": "тогузкоргоол", "onitama": "onitama", "pandemiclegacy": "пандемиямурасы", "camelup": "төөсейіні", "monopolygame": "монополияоюну", "brettspiele": "үстөлоюндары", "bordspellen": "үстөлоюндары", "boardgame": "үстөлоюну", "sällskapspel": "үстөлоюндары", "planszowe": "үстөлоюндары", "risiko": "тобокел", "permainanpapan": "үстөлоюндары", "zombicide": "зомбиөлтүрүү", "tabletop": "үстөлоюндары", "baduk": "бадук", "bloodbowl": "кандыта<PERSON>ак", "cluedo": "клуэдо", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "сенет", "goboardgame": "тактаоюнойно", "connectfour": "төртүнбайланыштыр", "heroquest": "баатырдыкиздөө", "giochidatavolo": "үстөлоюндары", "farkle": "фаркл", "carrom": "каром", "tablegames": "үстөлоюндары", "dicegames": "кубикоюндары", "yatzy": "ятцы", "parchis": "парчис", "jogodetabuleiro": "үстөлоюну", "jocuridesocietate": "коомдукоюндар", "deskgames": "үстөлоюндары", "alpharius": "алфар<PERSON><PERSON><PERSON>", "masaoyunları": "масаоюндары", "marvelcrisisprotocol": "marvelкризистикпротокол", "cosmicencounter": "космостукташуу", "creationludique": "көңүлдүүжаратуу", "tabletoproleplay": "үстөлүстүндөгүролдуойундар", "cardboardgames": "картонойындору", "eldritchhorror": "элдричкоркунуч", "switchboardgames": "коммутатороюндары", "infinitythegame": "инфинитиоюну", "kingdomdeath": "падышалыктынөлүмү", "yahtzee": "ятзи", "chutesandladders": "жыла<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>натепкичтер", "társas": "социалдык", "juegodemesa": "үстөлоюну", "planszówki": "үстөлоюндары", "rednecklife": "ауылдыкжашоо", "boardom": "зеригүү", "applestoapples": "алмаменалма", "jeudesociété": "коомдукоюн", "gameboard": "оюнтактасы", "dominó": "домино", "kalah": "калах", "crokinole": "крокинол", "jeuxdesociétés": "үстөлоюндары", "twilightimperium": "сүүрсумуруңимперия", "horseopoly": "атополия", "deckbuilding": "колода_куруу", "mansionsofmadness": "акылдансадырганүйлөр", "gomoku": "гомоку", "giochidatavola": "үстөлоюндары", "shadowsofbrimstone": "бримстоундункөлөкөлөрү", "kingoftokyo": "токионункоролу", "warcaby": "да<PERSON><PERSON><PERSON><PERSON><PERSON>", "táblajátékok": "үстөлоюндары", "battleship": "согушкемеси", "tickettoride": "поездгебилет", "deskovehry": "үстөлоюндары", "catán": "катан", "subbuteo": "субутео", "jeuxdeplateau": "үстөлоюндары", "stolníhry": "stolníhry", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "үстөлоюндары", "gesellschaftsspiele": "коомдукоюндар", "starwarslegion": "starwarslegion", "gochess": "шахматойно", "weiqi": "вэйци", "jeuxdesocietes": "jeuxdes<PERSON>iet<PERSON>", "terraria": "террариа", "dsmp": "dsmp", "warzone": "согушмайданы", "arksurvivalevolved": "arksurvivalevolved", "dayz": "деиз", "identityv": "identityv", "theisle": "арал", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "томбрейдер", "callofcthulhu": "ктулхунунчакырыгы", "bendyandtheinkmachine": "бендижанасыякорона", "conanexiles": "conanexiles", "eft": "эфт", "amongus": "амонгас", "eco": "эко", "monkeyisland": "маймылдыарал", "valheim": "valheim", "planetcrafter": "планет<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>у", "daysgone": "өткөнкүндөр", "fobia": "фобия", "witchit": "бүбүлө", "pathologic": "патологиялык", "zomboid": "зомбоид", "northgard": "northgard", "7dtd": "7<PERSON><PERSON><PERSON>", "thelongdark": "узунтүн", "ark": "ковчег", "grounded": "жерг<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>нган", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "өжөрата", "dontstarve": "ачкакалба", "eternalreturn": "түбөлүктүүкайтып", "pathoftitans": "титандардынжолу", "frictionalgames": "фрикционалоюндар", "hexen": "бүбүбакшылар", "theevilwithin": "ичиндегижамандык", "realrac": "чынбаракелдик", "thebackrooms": "арткабөлмөлөр", "backrooms": "арткабөлмөлөр", "empiressmp": "empiressmp", "blockstory": "блоктарыкы", "thequarry": "кар<PERSON><PERSON>р", "tlou": "tlou", "dyinglight": "өлүүчүжарык", "thewalkingdeadgame": "өлүктөроюнубасуу", "wehappyfew": "азсанактуубактылуулар", "riseofempires": "империялардынкөтөрүлүшү", "stateofsurvivalgame": "stateofsurvivalоюну", "vintagestory": "винтажокуя", "arksurvival": "arkсуутирүүбуз", "barotrauma": "баротравма", "breathedge": "дем_алуу_чети", "alisa": "алиса", "westlendsurvival": "бат<PERSON><PERSON><PERSON><PERSON><PERSON>тажашооучун", "beastsofbermuda": "бермуданынжырткычтары", "frostpunk": "frostpunk", "darkwood": "караңгытокой", "survivalhorror": "коркунучтуутируүоюну", "residentevil": "резидентэвил", "residentevil2": "резидентэвил2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "боштукпоезд", "lifeaftergame": "оюндансоңкужашоо", "survivalgames": "тирүүкалууоюндары", "sillenthill": "сайлент<PERSON><PERSON>лл", "thiswarofmine": "бұлсоғысымдыкі", "scpfoundation": "scpфондациясы", "greenproject": "жашылдолбоор", "kuon": "куон", "cryoffear": "коркуудансыздоо", "raft": "сал", "rdo": "rdo", "greenhell": "жашылтозок", "residentevil5": "резидентэвил5", "deadpoly": "өлүкполи", "residentevil8": "residentevil8", "onironauta": "онейронавт", "granny": "бабушка", "littlenightmares2": "кичинекейтүнгүкабустар2", "signalis": "сигналдар", "amandatheadventurer": "амандамаалымкоочу", "sonsoftheforest": "токойбалдары", "rustvideogame": "rustvideogame", "outlasttrials": "издөөсынактары", "alienisolation": "чоочунбөлүнүү", "undawn": "таң_атканча", "7day2die": "7күнсаөлөр", "sunlesssea": "күнсүздеңиз", "sopravvivenza": "тирүүкалуу", "propnight": "пропнайт", "deadisland2": "өлүүарал2", "ikemensengoku": "икеменсенгоку", "ikemenvampire": "ikемэнвампир", "deathverse": "өлүмааламы", "cataclysmdarkdays": "апааткараңгыкүндөр", "soma": "сома", "fearandhunger": "коркуучжанаачкалык", "stalkercieńczarnobyla": "сталкерчернобылдынкөлөкөсү", "lifeafter": "кийинкижашоо", "ageofdarkness": "караңгылыкдоору", "clocktower3": "сааткөзөмөлмунарасы3", "aloneinthedark": "караңгыдажалгыз", "medievaldynasty": "ортовекундукдинастиясы", "projectnimbusgame": "projectnimbusоюну", "eternights": "түбөлүктүүтүндөр", "craftopia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theoutlasttrials": "theoutlasttrials", "bunker": "бу<PERSON><PERSON><PERSON><PERSON>", "worlddomination": "дүйнөнүбийлөө", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "оффициоассассинорум", "necron": "некрон", "wfrp": "wfrp", "dwarfslayer": "эргеж<PERSON><PERSON>гуч", "warhammer40kcrush": "warhammer40kсүйүү", "wh40": "wh40", "warhammer40klove": "warhammer40kсүйүү", "warhammer40klore": "warhammer40kтарыхы", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "храмкулексус", "vindicare": "винтикаре", "ilovesororitas": "сорорситаларгаашыкпын", "ilovevindicare": "ilovevindicare", "iloveassasinorum": "iloveassasinorum", "templovenenum": "темптеѳсүрүнөм", "templocallidus": "темплокаллидус", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "40k": "40миң", "tetris": "тетрис", "lioden": "арстан", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "экөөбиздинишболот", "wingspan": "канатту<PERSON><PERSON>ук", "terraformingmars": "марстыөзгөртүү", "heroesofmightandmagic": "күчтүүлөрдүнжанасыйкырдынбаатырлары", "btd6": "btd6", "supremecommander": "башкыкомандачы", "ageofmythology": "мифологиядоору", "args": "args", "rime": "уйкаш", "planetzoo": "планетазоо", "outpost2": "аутпост2", "banished": "четке_жок", "caesar3": "caesar3", "redalert": "кызылэскертүү", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "башкаружанабагындыр", "warcraft3": "warcraft3", "eternalwar": "түбөлүктүүсогуш", "strategygames": "стратегияоюндары", "anno2070": "anno2070", "civilizationgame": "цивилизацияоюну", "civilization4": "цивилизация4", "factorio": "factorio", "dungeondraft": "зындандолбоору", "spore": "спора", "totalwar": "толуксогуш", "travian": "travian", "forts": "чептер", "goodcompany": "жакшыкомпания", "civ": "цив", "homeworld": "туул<PERSON><PERSON><PERSON><PERSON><PERSON>р", "heidentum": "бутпарастык", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "жарыктанылдам", "forthekings": "падышаларүчүн", "realtimestrategy": "реалдуубакыттагыстратегия", "starctaft": "starctaft", "sidmeierscivilization": "сидмейерсцивилизация", "kingdomtwocrowns": "падышалыкэкитаж", "eu4": "eu4", "vainglory": "намыс", "ww40k": "ww40k", "godhood": "кудайлык", "anno": "анно", "battletech": "баттлтех", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "хеттрик", "davesfunalgebraclass": "дэйвдинкызыктуучалгебрасабагы", "plagueinc": "плага<PERSON>нк", "theorycraft": "теориятүзүү", "mesbg": "mesbg", "civilization3": "цивилизация3", "4inarow": "4төртөкатар", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "алдыңкысогуштар", "ageofempires2": "ageofempires2", "disciples2": "disciples2", "plantsvszombies": "өсүмдүктөржанатаздарга", "giochidistrategia": "стратегияоюндары", "stratejioyunları": "стратегияоюндары", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "кереметтердоору", "dinosaurking": "динозаврпадыша", "worldconquest": "дүйнөнүбаштап", "heartsofiron4": "heartsofiron4", "companyofheroes": "баатырлардынкомпаниясы", "battleforwesnoth": "уэсноттунсогушу", "aoe3": "aoe3", "forgeofempires": "империялардынказаны", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "казказөрдөк", "phobies": "фобиялар", "phobiesgame": "фобияоюну", "gamingclashroyale": "ойноорclashroyale", "adeptusmechanicus": "адептусмеханикус", "outerplane": "сырткыдүйнө", "turnbased": "кезектешүү", "bomberman": "бомбермен", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "виктория2", "crusaderkings": "крестоносецпадышалар", "cultris2": "cultris2", "spellcraft": "сыйкырчылык", "starwarsempireatwar": "starwarsимперияныңсогушу", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "стратегия", "popfulmail": "попфулпочта", "shiningforce": "жаркыраганкүч", "masterduel": "мастердуэль", "dysonsphereprogram": "дайсонсферасыпрограммасы", "transporttycoon": "транспортма<PERSON>нат", "unrailed": "жолдончыкпа", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "уублеттер", "planescapetorment": "planescapetorment", "uplandkingdoms": "бийиктөөлкөлөр", "galaxylife": "галактикалыкжашоо", "wolvesvilleonline": "карышкырларайылыонлайн", "slaythespire": "slaythespireойнойм", "battlecats": "мышыктарсогушу", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "симша<PERSON>р", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "грантуризмо", "needforspeed": "ылдамдыккажет", "needforspeedcarbon": "тездикмуктаждыгыкарбон", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "грандтуризмо", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "даа<PERSON>дуубол", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "элисакайраэсинденчыгат", "darkhorseanthology": "караңгыаттынтоптому", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "бешкечтефреддиде", "saiko": "сайко", "fatalframe": "өлүмкадр", "littlenightmares": "кичинекейкүнтүштөр", "deadrising": "өлүктөртурушу", "ladydimitrescu": "ледидимитреску", "homebound": "үйдөчүкүттөлгөн", "deadisland": "өлүүарал", "litlemissfortune": "кичинекембактысыз", "projectzero": "нөлдүкдолбоор", "horory": "коркунучтуу", "jogosterror": "жүгүрүүдөнкоркунуч", "helloneighbor": "сәләмсосед", "helloneighbor2": "кошгойлом2", "gamingdbd": "оюндбд", "thecatlady": "мышыктарга_жинди", "jeuxhorreur": "коркунучтууоюндар", "horrorgaming": "коркунучтууоюндар", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "карточнаоюн", "cardsagainsthumanity": "кишилерге_каршы_карталар", "cribbage": "криббедж", "minnesotamtg": "миннесотаmtg", "edh": "edh", "monte": "монте", "pinochle": "пинокль", "codenames": "кодтукаттар", "dixit": "диксит", "bicyclecards": "велосипедкарталары", "lor": "lor", "euchre": "юкер", "thegwent": "thegwent", "legendofrunetera": "legendofrun<PERSON>a", "solitaire": "пасьянс", "poker": "покер", "hearthstone": "hearthstone", "uno": "уно", "schafkopf": "шафкопф", "keyforge": "ачкычтасоо", "cardtricks": "карталарменфокустар", "playingcards": "ойнокарталар", "marvelsnap": "marvelsnap", "ginrummy": "д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "netrunner": "нетраннер", "gwent": "гвинт", "metazoo": "метазоопарк", "tradingcards": "саудакарталары", "pokemoncards": "покемонкарталары", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "спорткарточкалар", "cardfightvanguard": "cardfightvanguard", "duellinks": "дуэл<PERSON><PERSON>н<PERSON>с", "spades": "️", "warcry": "согушураан", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "жүрөктөрдүнпадышасы", "truco": "трюк", "loteria": "лотерея", "hanafuda": "ханафуда", "theresistance": "каршылык", "transformerstcg": "трансформерлерtcg", "doppelkopf": "доппелькопф", "yugiohcards": "юугиокарталары", "yugiohtcg": "yugiohtcg", "yugiohduel": "югиохдуэль", "yugiohocg": "юугиоокарточнаяоюну", "dueldisk": "дуэлдиск", "yugiohgame": "yugiohоюну", "darkmagician": "караөнөрчү", "blueeyeswhitedragon": "көкөздөрактамак", "yugiohgoat": "югиохмыкты", "briscas": "брискас", "juegocartas": "карта<PERSON>юну", "burraco": "буррако", "rummy": "рамми", "grawkarty": "гравкарты", "dobble": "дабл", "mtgcommander": "mtgкомандир", "cotorro": "которро", "jeuxdecartes": "карталар_оюндары", "mtgjudge": "mtgjudge", "juegosdecartas": "картаоюндары", "duelyst": "дуэлист", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgалдыналынганкомандир", "kartenspiel": "карта<PERSON>юну", "carteado": "кар<PERSON><PERSON><PERSON><PERSON><PERSON>", "sueca": "sueca", "beloteonline": "белото<PERSON><PERSON><PERSON><PERSON>н", "karcianki": "карточкалар", "battlespirits": "баттлспиритс", "battlespiritssaga": "баттылыкрухсагасы", "jogodecartas": "карта<PERSON>юну", "žolíky": "жолуктар", "facecard": "жүзкарта", "cardfight": "кар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>у", "biriba": "biriba", "deckbuilders": "палубакурткандар", "marvelchampions": "marvelчемпиондору", "magiccartas": "сыйкырдуукарталар", "yugiohmasterduel": "югиомастердуэл", "shadowverse": "shadowverse", "skipbo": "скипбо", "unstableunicorns": "туруксузбирмүйүздүүлөр", "cyberse": "кибердүйнө", "classicarcadegames": "классикалыкаркадаоюндар", "osu": "осу", "gitadora": "gitadora", "dancegames": "бийоюндары", "fridaynightfunkin": "жумаакечесикөңүлачуу", "fnf": "fnf", "proseka": "просека", "projectmirai": "проектмирай", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "гита<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>й", "clonehero": "клонгерой", "justdance": "жөнгөнбийле", "hatsunemiku": "хацунэмику", "prosekai": "prosekai", "rocksmith": "рокустасы", "idolish7": "idolish7", "rockthedead": "өлүктөрдүсалкындат", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "бийборбору", "rhythmgamer": "ритмоюнчу", "stepmania": "stepmania", "highscorerythmgames": "жогорукуупайменменритмоюндору", "pkxd": "pkxd", "sidem": "сидем", "ongeki": "онгеки", "soundvoltex": "soundvoltex", "rhythmheaven": "ритмасманы", "hypmic": "hypmic", "adanceoffireandice": "отжанамуздунбийи", "auditiononline": "онлайнкастинг", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "ыргакоюндары", "cryptofthenecrodancer": "криптанекродансери", "rhythmdoctor": "ритмдоктор", "cubing": "кубик_чогултуу", "wordle": "wordle", "teniz": "тениз", "puzzlegames": "табышмакоюндар", "spotit": "табыңыз", "rummikub": "руммикуб", "blockdoku": "блокдоку", "logicpuzzles": "логикалыктабышмактар", "sudoku": "судоку", "rubik": "рубик", "brainteasers": "башсындыргычтар", "rubikscube": "рубиккубу", "crossword": "кроссворд", "motscroisés": "кроссворддор", "krzyżówki": "krzyżówki", "nonogram": "нонограмма", "bookworm": "кайра<PERSON><PERSON><PERSON>учу", "jigsawpuzzles": "жыйнакташкандапазлдар", "indovinello": "табышмак", "riddle": "жумак", "riddles": "жаңылмачтар", "rompecabezas": "табышмак", "tekateki": "табышмак", "inside": "ичинде", "angrybirds": "ачууланганчымчыктар", "escapesimulator": "качуусимулятору", "minesweeper": "ми<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ч", "puzzleanddragons": "пазлжанадракондор", "crosswordpuzzles": "кроссворддор", "kurushi": "куруши", "gardenscapesgame": "gardenscapesоюну", "puzzlesport": "табышмакспорт", "escaperoomgames": "качуубөлмөоюндары", "escapegame": "качыпкутулууоюну", "3dpuzzle": "3дпазл", "homescapesgame": "homescapesоюну", "wordsearch": "сөздөртабуу", "enigmistica": "табышмактар", "kulaworld": "кулажылдайсың", "myst": "мист", "riddletales": "табышмактуужомоктор", "fishdom": "балыкпадышалыгы", "theimpossiblequiz": "мүмкүнэмесвикторина", "candycrush": "candycrush", "littlebigplanet": "кичинекейчоңпланета", "match3puzzle": "3төтайкелүүтабышмак", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "кыйшык", "rubikcube": "рубиккубу", "cuborubik": "кубикрубик", "yapboz": "япб<PERSON>з", "thetalosprinciple": "талоспринциби", "homescapes": "үйжасалгалоо", "puttputt": "паттпатт", "qbert": "qbert", "riddleme": "табышмактап", "tycoongames": "магнатоюндары", "cubosderubik": "рубиккубтары", "cruciverba": "кроссворд", "ciphers": "шиф<PERSON><PERSON><PERSON>р", "rätselwörter": "табышмаксөздөр", "buscaminas": "ми<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ч", "puzzlesolving": "табышмакчечүү", "turnipboy": "шалгамбала", "adivinanzashot": "табышмактышуу", "nobodies": "эчкимдер", "guessing": "болжолдоо", "nonograms": "ноногралар", "kostkirubika": "косткирубика", "crypticcrosswords": "табышмактуукроссворддор", "syberia2": "сибирь2", "puzzlehunt": "табышмактабуу", "puzzlehunts": "табышмактапмаоюндар", "catcrime": "мышыккылмыш", "quebracabeça": "башталамык", "hlavolamy": "табышмактар", "poptropica": "поптропика", "thelastcampfire": "акыркыоткөзү", "autodefinidos": "өзүнөзүаныктагандар", "picopark": "пикопарк", "wandersong": "кыдыруыры", "carto": "карто", "untitledgoosegame": "аталышыжокказойыну", "cassetête": "баштыкатырма", "limbo": "лимбо", "rubiks": "рубик", "maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinykin": "кичин<PERSON><PERSON>ей", "rubikovakostka": "рубиккубу", "speedcube": "тездиккуб", "pieces": "түзүмдөр", "portalgame": "порталоюну", "bilmece": "билмеце", "puzzelen": "табышмакчечүү", "picross": "пикросс", "rubixcube": "рубиккубу", "indovinelli": "табышмактар", "cubomagico": "сыйкырдууккуб", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "бурмаланганкереметдүйнө", "monopoly": "монополия", "futurefight": "келечекүчүнсалгылаш", "mobilelegends": "мобайллегендс", "brawlstars": "brawlstars", "brawlstar": "бравлстар", "coc": "кок", "lonewolf": "жалгызкарышкыр", "gacha": "гача", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "пикмин<PERSON>лум", "ff": "ff", "ensemblestars": "ансамбльжылдыздары", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "алхимияжылдыздары", "stateofsurvival": "тирүүкалууабалы", "mycity": "менинбиркарым", "arknights": "аркнайтс", "colorfulstage": "түстүүсахна", "bloonstowerdefense": "balондорубузууоюну", "btd": "btd", "clashroyale": "clashroyale", "angela": "ангела", "dokkanbattle": "доккансалгыла<PERSON>уу", "fategrandorder": "fategrandorder", "hyperfront": "гиперфронт", "knightrun": "рыцарьчуркоо", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "футболсалгылашуу", "a3": "a3", "phonegames": "телефонуноюндары", "kingschoice": "падышанынтандоосу", "guardiantales": "кароолчубаалар", "petrolhead": "бен<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "тактикулдуу", "cookierun": "cookierun", "pixeldungeon": "пиксельзиндан", "arcaea": "arcaea", "outoftheloop": "кабарсыз", "craftsman": "усталар", "supersus": "өтөшектуу", "slowdrive": "жайхайдоо", "headsup": "көңүлбур", "wordfeud": "сөзсогушу", "bedwars": "bedwars", "freefire": "фрифа<PERSON>р", "mobilegaming": "мобилдикоюндар", "lilysgarden": "лилиянынбакчасы", "farmville2": "фармвилл2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "командалыксогушуюнтактикасы", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "мистикалыккабар", "callofdutymobile": "callofdutymobileойну", "thearcana": "аркана", "8ballpool": "8топбильярд", "emergencyhq": "тезжардамштабы", "enstars": "энстарз", "randonautica": "рандонавтика", "maplestory": "maplestory", "albion": "альбион", "hayday": "айылчарба", "onmyoji": "онмёдзи", "azurlane": "azurlane", "shakesandfidget": "шейкесэндфиджет", "ml": "мл", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "драконраджа", "timeprincess": "убакытпринцессасы", "beatstar": "жылдызга_айлан", "dragonmanialegend": "айдаһарбашкаруулегендасы", "hanabi": "хана<PERSON>и", "disneymirrorverse": "диснеймирроверс", "pocketlove": "чөнтөксүйүү", "androidgames": "and<PERSON><PERSON><PERSON>ндар", "criminalcase": "криминалдыкиш", "summonerswar": "summonerswar", "cookingmadness": "тамакжасоотунтуктук", "dokkan": "доккан", "aov": "aov", "triviacrack": "тривиакрэк", "leagueofangels": "периштелерлигасы", "lordsmobile": "lordsmobile", "tinybirdgarden": "кичинекейкуштарбагы", "gachalife": "gachalife", "neuralcloud": "нейробулут", "mysingingmonsters": "менинырдаганжелмогуздарым", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "согушроботтору", "mirrorverse": "күзгүдүнүя", "pou": "пу", "warwings": "согушканаттары", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "эвертейл", "futime": "көңүлачуубакыт", "antiyoy": "антий<PERSON>й", "apexlegendmobile": "apexlegendmobile", "ingress": "кирүү", "slugitout": "урушалычыкташ", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "үйжаныбарлары", "gameofsultans": "султандардыноюну", "arenabreakout": "аренадансүрүлүп", "wolfy": "карышкыр", "runcitygame": "шаарынойнойчурка", "juegodemovil": "мобилдикоюн", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "мимикрия", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "роллеркостертайкун", "grandchase": "grandchase", "bombmebrasil": "менибразилиябомбала", "ldoe": "ldoe", "legendonline": "легенд<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "otomegame": "отомеоюну", "mindustry": "mindustry", "callofdragons": "аждардардынчакырыгы", "shiningnikki": "жаркыраганникки", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "жолжок", "sealm": "sealm", "shadowfight3": "көлөкөсалгылашуу3", "limbuscompany": "limbuscompany", "demolitionderby3": "автоталкашуу3", "wordswithfriends2": "сөздөрдостормененбирге2", "soulknight": "жа<PERSON><PERSON><PERSON><PERSON><PERSON>ы", "purrfecttale": "мурртамаангеме", "showbyrock": "showbyrock", "ladypopular": "популярдуукыз", "lolmobile": "lo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>л", "harvesttown": "урожайшаар", "perfectworldmobile": "идеалдуудүйнөмобайл", "empiresandpuzzles": "империялардапазлдар", "empirespuzzles": "империяпазлдары", "dragoncity": "ажд<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileind", "fanny": "көкүрөк", "littlenightmare": "кичинекейкошкустүш", "aethergazer": "эфиркароочу", "mudrunner": "чоңчуркоочу", "tearsofthemis": "темистинкөзжаштары", "eversoul": "түбөлүкжан", "gunbound": "gunbound", "gamingmlbb": "оюнmlbb", "dbdmobile": "dbdmobile", "arknight": "арк<PERSON><PERSON>т", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "зомбиробинзондор", "eveechoes": "eveechoes", "jogocelular": "j<PERSON><PERSON><PERSON>", "mariokarttour": "мариокарттур", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "тамакжасоочуапа", "cabalmobile": "cabalmobile", "streetfighterduel": "көчөкөлөккөндүэл", "lesecretdhenri": "анридинсыры", "gamingbgmi": "gamingbgmi", "girlsfrontline": "кыздардынфронту", "jurassicworldalive": "jurassicworldalive", "soulseeker": "жансыздагыч", "gettingoverit": "унутупжатам", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "айчайхикаясы", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "мобилдикоюндар", "legendofneverland": "легенданынтүбөлүктүүөлкөсү", "pubglite": "pubglite", "gamemobilelegends": "оюнмобайллегендалар", "timeraiders": "убакытчапкынчылар", "gamingmobile": "мобилдикоюндар", "marvelstrikeforce": "marvelстрайкфорс", "thebattlecats": "мышыктарсогушу", "dnd": "дн<PERSON><PERSON><PERSON><PERSON><PERSON>", "quest": "квест", "giochidiruolo": "ролдукоюндар", "dnd5e": "dnd5e", "rpgdemesa": "үстөлрпг", "worldofdarkness": "караңгылыкдүйнөсү", "travellerttrpg": "саякатчыttrpg", "2300ad": "2300жыл", "larp": "ролдукоюн", "romanceclub": "романклубу", "d20": "d20", "pokemongames": "покемоноюндары", "pokemonmysterydungeon": "покемонмистериядунжон", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "покемонкристалл", "pokemonanime": "покемонаниме", "pokémongo": "pokémongo", "pokemonred": "покемонкызыл", "pokemongo": "покемонго", "pokemonshowdown": "покемоншоудаун", "pokemonranger": "покемонрейнджер", "lipeep": "эриндеринкөр", "porygon": "поригон", "pokemonunite": "pokemonunite", "entai": "энтай", "hypno": "гипно", "empoleon": "эмполеон", "arceus": "арце<PERSON>с", "mewtwo": "мьюту", "paldea": "палдеа", "pokemonscarlet": "покемонкызыл", "chatot": "чатот", "pikachu": "пикачу", "roxie": "рокси", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "покемонпурпура", "ashketchum": "эшкетчум", "gengar": "гэнгар", "natu": "натуралдуу", "teamrocket": "teamrocket", "furret": "фуррет", "magikarp": "магика<PERSON>п", "mimikyu": "мимикю", "snorlax": "снорлакс", "pocketmonsters": "чөнтөкмонстрлар", "nuzlocke": "нузлок", "pokemonplush": "покемонплюш", "teamystic": "командамистик", "pokeball": "покебол", "charmander": "ча<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "литтен", "shinypokemon": "жалтыракпокемон", "mesprit": "mesprit", "pokémoni": "покемон", "ironhands": "темиркол", "kabutops": "кабутопс", "psyduck": "псайдак", "umbreon": "умбреон", "pokevore": "покевор", "ptcg": "ptcg", "piplup": "пиплуп", "pokemonsleep": "покемонуйку", "heyyoupikachu": "эйсенпикачу", "pokémonmaster": "pokémonчебери", "pokémonsleep": "покемонуйку", "kidsandpokemon": "балдарженепокемон", "pokemonsnap": "покемонснап", "bulbasaur": "бульбазавр", "lucario": "лукарио", "charizar": "чаризар", "shinyhunter": "шайныхантер", "ajedrez": "шахмат", "catur": "шахмат", "xadrez": "шахмат", "scacchi": "scacchi", "schaken": "шахмат", "skak": "скак", "ajedres": "шахмат", "chessgirls": "шахматкыздар", "magnuscarlsen": "магнускарлсен", "worldblitz": "дүйнөблиц", "jeudéchecs": "шахмат", "japanesechess": "жапо<PERSON><PERSON>ахмат", "chinesechess": "кыта<PERSON><PERSON>ахматы", "chesscanada": "шахматканада", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "ачылыштар", "rook": "ладья", "chesscom": "chesscom", "calabozosydragones": "зындандаржанаажыдаарлар", "dungeonsanddragon": "драконжаназындан", "dungeonmaster": "зынданустасы", "tiamat": "tiamat", "donjonsetdragons": "зындандаржанаажыдаарлар", "oxventure": "oxventure", "darksun": "караңгыкүн", "thelegendofvoxmachina": "воксмашинанынлегендасы", "doungenoanddragons": "дунженэнддрагонс", "darkmoor": "караңгыкөл", "minecraftchampionship": "майнкрафтчемпионаты", "minecrafthive": "minecrafthive", "minecraftbedrock": "майнкрафтбедрок", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "майнкрафтмоддор", "mcc": "mcc", "candleflame": "шамчырагы", "fru": "фру", "addons": "кошумчалар", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "скайблок", "minecraftpocket": "майнкрафтчөнтөк", "minecraft360": "minecraft360", "moddedminecraft": "модификацияланганminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "экиөлкөнүнортосунда", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftшаары", "pcgamer": "пкгеймер", "jeuxvideo": "видеооюндар", "gambit": "гамбит", "gamers": "геймерлер", "levelup": "деңгээлдиарттыр", "gamermobile": "геймермобайл", "gameover": "оюнбүттү", "gg": "gg", "pcgaming": "компоюндору", "gamen": "гейминг", "oyunoynamak": "оюнойномок", "pcgames": "компоюндору", "casualgaming": "кадимкиоюндар", "gamingsetup": "оюнтехникасы", "pcmasterrace": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgame": "пкоюн", "gamerboy": "геймербала", "vrgaming": "vrоюндары", "drdisrespect": "drdisrespect", "4kgaming": "4kоюндар", "gamerbr": "геймербр", "gameplays": "оюндар", "consoleplayer": "консольоюнчусу", "boxi": "boxi", "pro": "про", "epicgamers": "эпикалыкоюнчулар", "onlinegaming": "онлайнойну", "semigamer": "жарымгеймер", "gamergirls": "геймердевочкалар", "gamermoms": "геймероюнчуапалар", "gamerguy": "геймерпацан", "gamewatcher": "оюнбайкоочу", "gameur": "геймер", "grypc": "grypc", "rangugamer": "рангугеймер", "gamerschicas": "геймердевушкалар", "otoge": "отоге", "dedsafio": "де<PERSON><PERSON><PERSON>", "teamtryhard": "командакаттууаракеттенүүчүлөр", "mallugaming": "маллуг<PERSON><PERSON>минг", "pawgers": "лапачылар", "quests": "квесттер", "alax": "алакс", "avgn": "avgn", "oldgamer": "эскиоюнчу", "cozygaming": "жа<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>минг", "gamelpay": "оюнойноо", "juegosdepc": "компоюндору", "dsswitch": "dsкотор<PERSON>у", "competitivegaming": "киберспорт", "minecraftnewjersey": "minecraftньюджерси", "faker": "жа<PERSON><PERSON><PERSON><PERSON>", "pc4gamers": "pc4геймерлер", "gamingff": "<PERSON>ю<PERSON><PERSON><PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "гетеросексуалдыкоюн", "gamepc": "компоюну", "girlsgamer": "кыздар<PERSON>йнайт", "fnfmods": "fnfмоддор", "dailyquest": "күндөлүктапшырма", "gamegirl": "оюнкыз", "chicasgamer": "геймеркыздар", "gamesetup": "оюнорнотуу", "overpowered": "ашыкэрчкүчтүү", "socialgamer": "социалг<PERSON>ймер", "gamejam": "оюнжам", "proplayer": "прооюнчу", "roleplayer": "рольоюнчу", "myteam": "менинкоманда", "republicofgamers": "геймерлердинреспубликасы", "aorus": "aorus", "cougargaming": "пумагейминг", "triplelegend": "үчдүктуулегенда", "gamerbuddies": "оюнчудостор", "butuhcewekgamers": "ойнучукыздаркерек", "christiangamer": "христиан<PERSON>йнчу", "gamernerd": "геймернерд", "nerdgamer": "нердоюнчу", "afk": "афк", "andregamer": "андрег<PERSON>ймер", "casualgamer": "жөнөкөйоюнчу", "89squad": "89топ", "inicaramainnyagimana": "иникарамайннягимана", "insec": "сенемин", "gemers": "геймерлер", "oyunizlemek": "оюнкөрүү", "gamertag": "геймертег", "lanparty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "videogamer": "видеооюнчу", "wspólnegranie": "биргеойноо", "mortdog": "мортдог", "playstationgamer": "playstationойноочу", "justinwong": "жастинвонг", "healthygamer": "соосалмакоюнчу", "gtracing": "gtracing", "notebookgamer": "ноутбукгеймер", "protogen": "протоген", "womangamer": "ая<PERSON><PERSON><PERSON><PERSON>мер", "obviouslyimagamer": "албеттеменгеймермин", "mario": "марио", "papermario": "кагазмарио", "mariogolf": "мариогольф", "samusaran": "самусаран", "forager": "табигытчагжыйноочу", "humanfallflat": "адамтүшөт", "supernintendo": "супернинтендо", "nintendo64": "nintendo64", "zeroescape": "нөлдөнкачуу", "waluigi": "валуиджи", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomузыка", "sonicthehedgehog": "соникежик", "sonic": "соник", "fallguys": "фоллгайс", "switch": "которуу", "zelda": "зельда", "smashbros": "smashbros", "legendofzelda": "зелданынлегендасы", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "пикмин", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "мегамен", "majorasmask": "majorasmask", "mariokartmaster": "мариокартустасы", "wii": "wii", "aceattorney": "эйсаттерни", "ssbm": "ssbm", "skychildrenofthelight": "асманбалдары", "tomodachilife": "томодачижашоо", "ahatintime": "бирөбөгөндөтыйып", "tearsofthekingdom": "королдукканкөзжаштары", "walkingsimulators": "жөөсимуляторлор", "nintendogames": "nintendoоюндары", "thelegendofzelda": "зелданынлегендасы", "dragonquest": "драконисти", "harvestmoon": "айтолкуну", "mariobros": "марио<PERSON><PERSON><PERSON>гандар", "runefactory": "runefactory", "banjokazooie": "банджоказуи", "celeste": "селесте", "breathofthewild": "зелданынжапайытабияты", "myfriendpedro": "досумпедро", "legendsofzelda": "legendsofzelda", "donkeykong": "донкиконг", "mariokart": "mariокарт", "kirby": "кирби", "51games": "51оюн", "earthbound": "жерг<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>нган", "tales": "жомоктор", "raymanlegends": "raymanlegends", "luigismansion": "лу<PERSON><PERSON><PERSON><PERSON><PERSON>инсарайы", "animalcrosssing": "animalcrossing", "taikonotatsujin": "тайконотацу<PERSON>ин", "nintendo3ds": "nintendo3ds", "supermariobros": "супермариобратья", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendoчили", "tloz": "tloz", "trianglestrategy": "үчбурчтуктактика", "supermariomaker": "супермариожасоочу", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "супермарио64", "conkersbadfurday": "конкерсжамандайжума", "nintendos": "nintendo<PERSON>", "new3ds": "жаңы3ds", "donkeykongcountry2": "донкиконгөлкөсү2", "hyrulewarriors": "хай<PERSON><PERSON><PERSON><PERSON>оочулары", "mariopartysuperstars": "марипартисуперстарс", "marioandsonic": "мари<PERSON>жанасоник", "banjotooie": "банжотуи", "nintendogs": "нинтендогс", "thezelda": "зельда", "palia": "palia", "marioandluigi": "мар<PERSON><PERSON><PERSON><PERSON><PERSON>луид<PERSON>и", "mariorpg": "марир<PERSON>г", "zeldabotw": "zeldabotw", "yuumimain": "юумимейн", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "ила<PERSON>й", "aram": "арам", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "ургот", "zyra": "зайра", "redcanids": "кызылиттер", "vanillalol": "ванилла<PERSON>ай", "wildriftph": "wildriftkg", "lolph": "катыр", "leagueoflegend": "лигалегенд", "tốcchiến": "тезжеңиш", "gragas": "граггас", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>у", "lolzinho": "лолзиньо", "leagueoflegendsespaña": "leagueoflegendsиспания", "aatrox": "aatrox", "euw": "фуу", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "кайле", "samira": "самира", "akali": "акали", "lunari": "лунари", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "а<PERSON><PERSON><PERSON><PERSON>", "milio": "милио", "shaco": "шако", "ligadaslegendas": "ligadaslegendas", "gaminglol": "оюндардыкарма", "nasus": "nasus", "teemo": "тимо", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "алтыбурчтуударбайтар", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "оюнфортнайт", "fortnitebr": "fortnitebr", "retrovideogames": "ретровидеооюндар", "scaryvideogames": "коркунучтуувидеооюндар", "videogamemaker": "видеооюнжасоочу", "megamanzero": "мегамензеро", "videogame": "видеооюн", "videosgame": "видеооюндар", "professorlayton": "профессорлейтон", "overwatch": "overwatch", "ow2": "оу2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "оюндарзалдары", "acnh": "acnh", "puffpals": "пуфтуудостор", "farmingsimulator": "фермердиксимуляциясы", "robloxchile": "robloxчили", "roblox": "роблокс", "robloxdeutschland": "robloxкыргызстан", "robloxdeutsch": "robloxкыргызча", "erlc": "эрлс", "sanboxgames": "sanboxоюндар", "videogamelore": "видеооюнтарыхы", "rollerdrome": "роликтеме", "parasiteeve": "паразитмайрамы", "gamecube": "геймкьюб", "starcraft2": "starcraft2", "duskwood": "караңгытокой", "dreamscape": "түшкөрүнүш", "starcitizen": "starcitizen", "yanderesimulator": "яндересимулятор", "grandtheftauto": "чоңуурулук", "deadspace": "өлүкмейкиндик", "amordoce": "таттуусүйүү", "videogiochi": "видеооюндар", "theoldrepublic": "эскиреспублика", "videospiele": "видеооюндар", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "укмуштуудууоюндар", "wolfenstein": "вольфенштайн", "actionadventure": "экшнприключение", "storyofseasons": "жылмезгилдериокуясы", "retrogames": "ретрооюндар", "retroarcade": "ретроаркада", "vintagecomputing": "эскикомпьютер", "retrogaming": "ретроойундар", "vintagegaming": "винтаждыкоюндор", "playdate": "ойноошуу", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "багзнакс", "injustice2": "адилетсиздик2", "shadowthehedgehog": "шэдоукирпи", "rayman": "rayman", "skygame": "асмандаоюн", "zenlife": "тынчжашоо", "beatmaniaiidx": "beatmaniaiidx", "steep": "тик", "mystgames": "мистикалыкоюндар", "blockchaingaming": "блокчейноюндор", "medievil": "ортоғасыр", "consolegaming": "консольоюндар", "konsolen": "консоль", "outrun": "ашыпкет", "bloomingpanic": "гүлдөпбаракелдим", "tobyfox": "tobyfox", "hoyoverse": "hойоверс", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "оюнкоркунуч", "monstergirlquest": "кыздаржиндинизденүүсү", "supergiant": "супергигант", "disneydreamlightvalle": "диснейдримлайтвэлли", "farmingsims": "фермердиксимуляциялар", "juegosviejos": "эскиоюндар", "bethesda": "бетесда", "jackboxgames": "jackboxоюндары", "interactivefiction": "интерактивдүүкөркөмчыгарма", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "акылдуулардынсүйүүсү", "visualnovel": "визуалдыкроман", "visualnovels": "визуалдыкроман", "rgg": "rgg", "shadowolf": "карышкыр", "tcrghost": "tcrghost", "payday": "айлыкалуу", "chatherine": "кэтринменсүйлөш", "twilightprincess": "күүгүмпринцесса", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "кумсандык", "aestheticgames": "эстетикалыкоюндар", "novelavisual": "визуалдыкроман", "thecrew2": "экипаж2", "alexkidd": "алекскидд", "retrogame": "ретрооюн", "tonyhawkproskater": "тонихоукпрокейтер", "smbz": "smbz", "lamento": "лаементо", "godhand": "кудайдынколу", "leafblowerrevolution": "жалбыракүйлөткүчреволюциясы", "wiiu": "wiiu", "leveldesign": "деңгээлдизайн", "starrail": "жылдызжол", "keyblade": "ачкычкылыч", "aplaguetale": "обокертуу", "fnafsometimes": "fnafsometimes", "novelasvisuales": "визуалдыкромандар", "robloxbrasil": "роблоксбразилия", "pacman": "пакмэн", "gameretro": "retroоюндар", "videojuejos": "видеооюндар", "videogamedates": "видеооюндардажолугушуулар", "mycandylove": "менинтаттуусүйүүм", "megaten": "мегатен", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "жөнгана3", "hulkgames": "халкоюндары", "batmangames": "бэтменоюндары", "returnofreckoning": "кайтыпкелүү", "gamstergaming": "оюнчугеймер", "dayofthetantacle": "коргоондункүнү", "maniacmansion": "маньякүй", "crashracing": "жар<PERSON>шавариясы", "3dplatformers": "3dплатформерлер", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "эскимектептикоюндар", "hellblade": "темиртоо", "storygames": "окуялароюндары", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "үндөнкачуу", "beyondtwosouls": "экижандантышкары", "gameuse": "оюнколдонуу", "offmortisghost": "оффмортисарбак", "tinybunny": "кичинекейкоён", "retroarch": "ретро<PERSON><PERSON>ч", "powerup": "күчөт", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "графикалыкукмуштар", "quickflash": "тезжарк", "fzero": "fzero", "gachagaming": "гачаоюндары", "retroarcades": "ретроаркадалар", "f123": "f123", "wasteland": "чөл", "powerwashsim": "электржуугучсим", "coralisland": "коралларалы", "syberia3": "сибирь3", "grymmorpg": "грыммоrpg", "bloxfruit": "bloxfruit", "anotherworld": "башкадүйнө", "metaquest": "metaquest", "animewarrios2": "анимеваррио2", "footballfusion": "футболбиригүү", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "астронир", "legomarvel": "легомарвел", "wranduin": "урандуин<PERSON><PERSON><PERSON>ташы", "twistedmetal": "бура<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>а<PERSON>л", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "уятүйүлгөнү", "simulator": "симулятор", "symulatory": "симуляторлор", "speedrunner": "спидранн<PERSON>р", "epicx": "эпикx", "superrobottaisen": "суперроботтайсен", "dcuo": "dcuo", "samandmax": "сэмжанамакс", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "коркууоюну", "wonderlandonline": "wonderlandонл<PERSON><PERSON>н", "skylander": "аландардыноюнчугу", "boyfrienddungeon": "жигитзындан", "toontownrewritten": "toontownжаңыртылган", "simracing": "симрей<PERSON>инг", "simrace": "симрейс", "pvp": "pvp", "urbanchaos": "ша<PERSON><PERSON><PERSON><PERSON>нта<PERSON>ж<PERSON><PERSON>л", "heavenlybodies": "асмандагыденелер", "seum": "сеум", "partyvideogames": "тусойдүнвидеооюндары", "graveyardkeeper": "көрүстөнсакчысы", "spaceflightsimulator": "космостукучуусимулятору", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "чабуужанакесүү", "foodandvideogames": "тамакжанавидеооюндар", "oyunvideoları": "oyunvideoloru", "thewolfamongus": "арасыздагыкарышкыр", "truckingsimulator": "жүккташуусимулятору", "horizonworlds": "horizonworlds", "handygame": "оңойоюн", "leyendasyvideojuegos": "легендаларжанавидеооюндар", "oldschoolvideogames": "эскимектепоюндары", "racingsimulator": "жарышсимулятору", "beemov": "bee<PERSON>v", "agentsofmayhem": "майхемагенттери", "songpop": "ырпоп", "famitsu": "фамицу", "gatesofolympus": "олимптиндарбазалары", "monsterhunternow": "монстр<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rebelstar": "козголоңчужылдыз", "indievideogaming": "индиоюндар", "indiegaming": "индиоюндар", "indievideogames": "инди_видеоюндар", "indievideogame": "инди_видеоигра", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "спайдерменинсомниак", "bufffortress": "күчтүүчеп", "unbeatable": "жеңилбес", "projectl": "проектl", "futureclubgames": "келечекклубдуноюндары", "mugman": "кружкаадам", "insomniacgames": "уйкусузоюндар", "supergiantgames": "супергиганттыноюндар", "henrystickman": "генристикмен", "henrystickmin": "генристикмин", "celestegame": "селестеоюну", "aperturescience": "aperturescience", "backlog": "артта_калган", "gamebacklog": "оюндаркуйругу", "gamingbacklog": "оюндартизмеси", "personnagejeuxvidéos": "видеооюнперсонаждары", "achievementhunter": "жетишкендикизилдөөчү", "cityskylines": "шаарасмандары", "supermonkeyball": "супермаймылтоп", "deponia": "deponia", "naughtydog": "тентекит", "beastlord": "жырткычбий", "juegosretro": "ретрооюндар", "kentuckyroutezero": "кентуккижолунөл", "oriandtheblindforest": "ориженеайкөлорманы", "alanwake": "аланвейк", "stanleyparable": "стэнлипарабол", "reservatoriodedopamin": "допаминсактагычы", "staxel": "staxel", "videogameost": "видеооюнсаундтрек", "dragonsync": "ажыда<PERSON><PERSON><PERSON><PERSON><PERSON>хрон", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "аркана", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "жинди", "baki": "баки", "sailormoon": "сейлормун", "saintseiya": "сентсейя", "inuyasha": "инуяша", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "инициалд", "elhazard": "элхазард", "dragonballz": "драго<PERSON>бо<PERSON>з", "sadanime": "кайгылууаниме", "darkerthanblack": "караңгыдандакараңгы", "animescaling": "анимемасштабдоо", "animewithplot": "сюжеттүүаниме", "pesci": "песси", "retroanime": "ретроаниме", "animes": "аниме", "supersentai": "суперсентай", "samuraichamploo": "самурайчамплу", "madoka": "мадока", "higurashi": "хигу<PERSON><PERSON><PERSON>и", "80sanime": "80аниме", "90sanime": "90аниме", "darklord": "караха<PERSON><PERSON><PERSON><PERSON>р", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "мастерпоги", "samuraix": "самурайx", "dbgt": "dbgt", "veranime": "верааниме", "2000sanime": "2000жылдарданиме", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "дрстоунбиринчисезон", "rapanime": "рэпаниме", "chargemanken": "chargemanken", "animecover": "анимекавер", "thevisionofescaflowne": "эскафлоундункөрүнүш", "slayers": "мыктылар", "tokyomajin": "токиом<PERSON><PERSON>ин", "anime90s": "90аниме", "animcharlotte": "ани<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>та", "gantz": "gantz", "shoujo": "сёдзё", "bananafish": "бананбалык", "jujutsukaisen": "жужутсукайсен", "jjk": "jjk", "haikyu": "ха<PERSON><PERSON>ю", "toiletboundhanakokun": "да<PERSON><PERSON><PERSON><PERSON>к<PERSON>набайланганханакокун", "bnha": "bnha", "hellsing": "хел<PERSON><PERSON><PERSON><PERSON>г", "skipbeatmanga": "skipbeat<PERSON><PERSON>н<PERSON>а", "vanitas": "vanitas", "fireforce": "откүч", "moriartythepatriot": "мориартипатриот", "futurediary": "келечеккүндөлүк", "fairytail": "эртеги", "dorohedoro": "дорохедоро", "vinlandsaga": "винландсага", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "паразит", "punpun": "пунпун", "shingekinokyojin": "shingekinokyojin", "mushishi": "мушиши", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "суукызымелодиясы", "kamisamakiss": "кудайдыноопкесу", "blmanga": "blmanga", "horrormanga": "коркунучтуумангасы", "romancemangas": "романтикалыкмангалар", "karneval": "кар<PERSON>вал", "dragonmaid": "айдаһаркызматчы", "blacklagoon": "каралагуна", "kentaromiura": "кентаромиура", "mobpsycho100": "мобпсихо100", "terraformars": "терраформарс", "geniusinc": "гений_инк", "shamanking": "шаманкороль", "kurokonobasket": "курокобаскетболу", "jugo": "jugo", "bungostraydogs": "бунгострейдогс", "jujustukaisen": "жужуцукайсен", "jujutsu": "жужут<PERSON>у", "yurionice": "yurionice", "acertainmagicalindex": "белгилүүбирсыйкыриндекс", "sao": "sao", "blackclover": "караклевер", "tokyoghoul": "токиогуль", "onepunchman": "бирсокку", "hetalia": "хеталия", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "тоару", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "шпионүйбүлө", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "дороро", "wondereggpriority": "wondereggpriority", "angelsofdeath": "өлүмпериштелери", "kakeguri": "какегури", "dragonballsuper": "dragonballsuper", "hypnosismic": "гипнозмикрофон", "goldenkamuy": "алтынкамуй", "monstermusume": "монстркыздар", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "спортанимеси", "sukasuka": "сукасука", "arwinsgame": "ar<PERSON><PERSON>инoю<PERSON>у", "angelbeats": "анже<PERSON><PERSON>итс", "isekaianime": "исекайаниме", "sagaoftanyatheevil": "таняжамандыктынсагасы", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "таня", "durarara": "дурарара", "prettycure": "prettycure", "theboyandthebeast": "ба<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>нажайдар", "fistofthenorthstar": "түндүктүнжулдузунунмуштуму", "mazinger": "мазин<PERSON><PERSON>р", "blackbuttler": "кара<PERSON><PERSON><PERSON><PERSON><PERSON>р", "towerofgod": "кудайдынмунарасы", "elfenlied": "эльфендүшүү", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "чиби", "servamp": "сервамп", "howtokeepamummy": "мумияныкантипсактоо", "fullmoonwosagashite": "толукайдыиздепжүрөм", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "токиомяумяу", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "жаракысынакоркунучтуу", "martialpeak": "согушчулук", "bakihanma": "бакиханма", "hiscoregirl": "жогоркыупайкыз", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "мерукочан", "dabi": "да<PERSON>и", "johnconstantine": "джонконстантин", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "шин<PERSON>и", "zerotwo": "нөлэки", "inosuke": "иносуке", "nezuko": "незуко", "monstergirl": "монстркыз", "kanae": "kanae", "yone": "yone", "mitsuki": "мицуки", "kakashi": "какаши", "lenore": "ленор", "benimaru": "бенимару", "saitama": "сайтама", "sanji": "сан<PERSON>и", "bakugo": "бакуго", "griffith": "гриффит", "ririn": "ririn", "korra": "корра", "vanny": "ванный", "vegeta": "вегета", "goromi": "goromi", "luci": "люси", "reigen": "рейген", "scaramouche": "скарамуш", "amiti": "амити", "sailorsaturn": "сейлорсатурн", "dio": "дио", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "руна", "oldanime": "эскианиме", "chainsawman": "чейнсоумен", "bungoustraydogs": "бродягабалдар", "jogo": "оюн", "franziska": "franziska", "nekomimi": "неконочтор", "inumimi": "инумими", "isekai": "исекай", "tokyorevengers": "токиоревенджерс", "blackbutler": "караткаймактайкызматчы", "ergoproxy": "эргопрокси", "claymore": "клеймор", "loli": "лоли", "horroranime": "коркунучтууаниме", "fruitsbasket": "жемиштерсебети", "devilmancrybaby": "шайтана<PERSON>а<PERSON><PERSON><PERSON><PERSON>айт", "noragami": "норагами", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "сэйнэн", "lovelive": "cүйүүчөрүдө", "sakuracardcaptor": "сакуракарталгычы", "umibenoetranger": "уминиченөтөнүүчү", "owarinoseraph": "оваринонасераф", "thepromisedneverland": "убадакылганэчкачанжер", "monstermanga": "монстермангасы", "yourlieinapril": "апрелдегижалганың", "buggytheclown": "баггитикклоун", "bokunohero": "бокунохеро", "seraphoftheend": "серафимдинаягы", "trigun": "тригун", "cyborg009": "киборг009", "magi": "сыйкырчы", "deepseaprisoner": "деңиздүбтуткун", "jojolion": "jojo<PERSON>", "deadmanwonderland": "өлүкадамкереметөлкө", "bannafish": "ба<PERSON><PERSON><PERSON><PERSON>", "sukuna": "сукуна", "darwinsgame": "дарвиндиноюну", "husbu": "күйөө", "sugurugeto": "сугуругето", "leviackerman": "левиакерман", "sanzu": "санзу", "sarazanmai": "сарад<PERSON><PERSON>н<PERSON><PERSON>й", "pandorahearts": "пандоражүрөктөр", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "тамакуруштары", "cardcaptorsakura": "карткармоочусакура", "stolas": "столас", "devilsline": "шайтандынсызыгы", "toyoureternity": "түбөлүккөчейин", "infpanime": "infpаниме", "eleceed": "элисид", "akamegakill": "akamegakill", "blueperiod": "көкмезгил", "griffithberserk": "гриффитберсерк", "shinigami": "шинигами", "secretalliance": "ж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>а<PERSON>с", "mirainikki": "болочоккүндөлүк", "mahoutsukainoyome": "сыйкырчынынжубайы", "yuki": "юки", "erased": "жоюлду", "bluelock": "көкқұлып", "goblinslayer": "гоблиндиөлтүргөн", "detectiveconan": "детективконан", "shiki": "шики", "deku": "деку", "akitoshinonome": "акитошиноме", "riasgremory": "риасгремори", "shojobeat": "шож<PERSON>бит", "vampireknight": "вампиррыцары", "mugi": "муги", "blueexorcist": "көкшайтан", "slamdunk": "сламданк", "zatchbell": "за<PERSON><PERSON><PERSON><PERSON>л", "mashle": "машл", "scryed": "ыйладым", "spyfamily": "шпиондукүйлөбүз", "airgear": "аэр<PERSON><PERSON>р", "magicalgirl": "сыйкырдуукыз", "thesevendeadlysins": "жетиөлүмкасыздыкгүнөө", "prisonschool": "түрмөмектеп", "thegodofhighschool": "лицейдинкудайы", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "грандблу", "mydressupdarling": "менинкийимкийгенкуурчагым", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "розенмайден", "animeuniverse": "анимеааламы", "swordartonlineabridge": "swordartonlineкөпүрө", "saoabridged": "сао_кыскартылган", "hoshizora": "жылдыздуутасма", "dragonballgt": "dragonballgt", "bocchitherock": "боччито<PERSON>у", "kakegurui": "какегуруи", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "өлбөстүктууратсыз", "romancemanga": "романсманга", "blmanhwa": "blманхва", "kimetsunoyaba": "kimetsunoyaba", "kohai": "кохай", "animeromance": "анимеромантика", "senpai": "сенпай", "blmanhwas": "бл<PERSON>а<PERSON><PERSON>валар", "animeargentina": "анимеаргентина", "lolicon": "лоликон", "demonslayertothesword": "шайтанөлтүргүчкылычка", "bloodlad": "bloodlad", "goodbyeeri": "эриментайтама", "firepunch": "отжумуштук", "adioseri": "адиосэри", "tatsukifujimoto": "тацукифужимото", "kinnikuman": "кинни<PERSON>у<PERSON>ан", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "жылдыздартуу<PERSON>а<PERSON><PERSON><PERSON>т", "romanceanime": "романтикааниме", "tsundere": "цундере", "yandere": "яндере", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "кенга<PERSON>шура", "saointegralfactor": "saoинтегралфактор", "cherrymagic": "сыйкырдуувишня", "housekinokuni": "уйкинокуну", "recordragnarok": "рекордрагнарок", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "фудан<PERSON>и", "retromanga": "ретроманга", "highschoolofthedead": "өлүктөрдүнмектеби", "germantechno": "германтехно", "oshinoko": "ошиноко", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "винландсага", "mangaka": "мангака", "dbsuper": "dbsuper", "princeoftennis": "теннистинханзаадасы", "tonikawa": "тоникава", "esdeath": "эсдес", "dokurachan": "докурачан", "bjalex": "б<PERSON>а<PERSON><PERSON><PERSON>с", "assassinclassroom": "өлтүрүүчүлөрклассы", "animemanga": "анимеманга", "bakuman": "бакуман", "deathparade": "өлүмпарады", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "жапонаниме", "animespace": "анимемейкиндик", "girlsundpanzer": "кызд<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>анктар", "akb0048": "akb0048", "hopeanuoli": "үмүттүжаажебе", "animedub": "аниметолукдоб", "animanga": "анимеманга", "tsurune": "цуруне", "uqholder": "uqcarmap", "indieanime": "индианиме", "bungoustray": "бунгоустрей", "dagashikashi": "дага<PERSON>и<PERSON>а<PERSON>и", "gundam0": "gundam0", "animescifi": "анимефантастика", "ratman": "kelемишадам", "haremanime": "гареманиме", "kochikame": "кочикаме", "nekoboy": "некобой", "gashbell": "гашбелл", "peachgirl": "персиккыз", "cavalieridellozodiaco": "зодиактынрыцарлары", "mechamusume": "мехакыздар", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "ярычынсойкукурамы", "dragonquestdai": "dragonquestdai", "heartofmanga": "манганынжүрөгү", "deliciousindungeon": "даамдуузынданда", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "рагнарөктүнжазуусу", "funamusea": "көңүлдүүмузей", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "мангааниме", "bochitherock": "бочитерок", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloaferге_секир", "shuumatsunovalkyrie": "шууматсунонвалкирия", "tutorialistoohard": "окутуучуоордук", "overgeared": "ашыкчажабдыктуу", "toriko": "торико", "ravemaster": "рейвустаты", "kkondae": "ккондэ", "chobits": "чобитс", "witchhatatelier": "сыйкырчыларательеси", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "сангатсунолион", "kamen": "камен", "mangaislife": "мангабу<PERSON><PERSON>ашоо", "dropsofgod": "кудайдынтамчылары", "loscaballerosdelzodia": "лосзодиакрыцарлары", "animeshojo": "анимекыз", "reverseharem": "тескериаремдик", "saintsaeya": "сентсейя", "greatteacheronizuka": "уулупедагогонидзука", "gridman": "гридмен", "kokorone": "кокороне", "soldato": "солдат", "mybossdaddy": "менин<PERSON><PERSON><PERSON>чыатам", "gear5": "gear5", "grandbluedreaming": "улуукөкасман", "bloodplus": "кандыкошуу", "bloodplusanime": "bloodplusаниме", "bloodcanime": "кандуанимэ", "bloodc": "кандынтиби", "talesofdemonsandgods": "жиндердинжанакудайлардынжомоктору", "goreanime": "гореаниме", "animegirls": "анимекыздар", "sharingan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crowsxworst": "каргаларэңкөтүсү", "splatteranime": "чачыранганиме", "splatter": "чачырандылар", "risingoftheshieldhero": "калканчанкаарманыныңкөтөрүлүшү", "somalianime": "сомалианиме", "riodejaneiroanime": "риодежанейроаниме", "slimedattaken": "алыптыралды", "animeyuri": "анимеюри", "animeespaña": "анимеиспания", "animeciudadreal": "анимесиудадреал", "murim": "мурим", "netjuunosusume": "netjuuно<PERSON>у<PERSON>уш", "childrenofthewhales": "киттердинбалдары", "liarliar": "жалганчыжалганчы", "supercampeones": "супержеңүүчүлөр", "animeidols": "анимекумир<PERSON>ер", "isekaiwasmartphone": "исекайсмартфонменен", "midorinohibi": "жашылкүндөр", "magicalgirls": "сыйкырдуукыздар", "callofthenight": "түнчакырыгы", "bakuganbrawler": "bakuганбрөйлер", "bakuganbrawlers": "бакуган<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>лар", "natsuki": "нацуки", "mahoushoujo": "махошоужо", "shadowgarden": "көлөкөбакча", "tsubasachronicle": "tsubasaхроника", "findermanga": "манганыздар", "princessjellyfish": "принцессамедуза", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "парадизкисс", "kurochan": "курочан", "revuestarlight": "revuestarlight", "animeverse": "аниме<PERSON><PERSON><PERSON>ам", "persocoms": "персокомдор", "omniscientreadersview": "баарынбилгенокурмандынкөзкарашы", "animecat": "анимемышык", "animerecommendations": "анимесунуштар", "openinganime": "ачылышаниме", "shinichirowatanabe": "шиничироватанабе", "uzumaki": "узумаки", "myteenromanticcomedy": "менинөспүрүмромантикалыккомедиям", "evangelion": "евангелион", "gundam": "гандам", "macross": "мак<PERSON><PERSON><PERSON>с", "gundams": "гундамдар", "voltesv": "өзгөрүшчү", "giantrobots": "алпкаруутроботтор", "neongenesisevangelion": "неонгенезисевангелион", "codegeass": "кодгесс", "mobilefighterggundam": "мобил<PERSON>айтергандам", "neonevangelion": "неонэвангелион", "mobilesuitgundam": "мобилсьюитгундам", "mech": "меч", "eurekaseven": "эврикажети", "eureka7": "эврика7", "thebigoanime": "thebooаниме", "bleach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathnote": "өлүмдептери", "cowboybebop": "ковбойбебоп", "jjba": "jjba", "jojosbizarreadventure": "жожонунукмуштуудайокуялары", "fullmetalalchemist": "толукметаллалхимик", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "жожонункызыктууокуялары", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "аскердиканиме", "greenranger": "жа<PERSON><PERSON><PERSON><PERSON><PERSON>йндж<PERSON>р", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "токиорев", "zorro": "зорро", "leonscottkennedy": "леонскотткеннеди", "korosensei": "коросенсей", "starfox": "жылдызтүлкү", "ultraman": "ультрамен", "salondelmanga": "салондельманга", "lupinthe3rd": "люпентүчүнчү", "animecity": "ани<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animetamil": "анимэтамил", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "наруто", "narutoshippuden": "наруточелинген", "onepiece": "біркесек", "animeonepiece": "анимеонепис", "dbz": "dbz", "dragonball": "дра<PERSON><PERSON><PERSON><PERSON><PERSON>р", "yugioh": "югио", "digimon": "дигимон", "digimonadventure": "digimonукмуштуу", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "гоку", "broly": "броли", "shonenanime": "шоненаниме", "bokunoheroacademia": "менинкаарманакадемиям", "jujustukaitsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "отака", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "жиндиңкыргыны", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "титанд<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>л", "erenyeager": "эренйегер", "myheroacademia": "менинбаатыракадемиям", "boruto": "боруто", "rwby": "rwby", "dandadan": "да<PERSON><PERSON>а<PERSON><PERSON>н", "tomodachigame": "томодачиоюну", "akatsuki": "акацуки", "surveycorps": "чалгынчылар", "onepieceanime": "onepieceаниме", "attaquedestitans": "титандардынчабуулу", "theonepieceisreal": "onepieceчындыкбарго", "revengers": "өч_алуучулар", "mobpsycho": "mobpsycho", "aonoexorcist": "көксайтан_көөкөрү", "joyboyeffect": "joyboyэффект", "digimonstory": "дигимонокуя", "digimontamers": "digimontamers", "superjail": "суперабак", "metalocalypse": "металокалипсис", "shinchan": "шин<PERSON><PERSON>н", "watamote": "watamote", "uramichioniisan": "урамичиагай", "uruseiyatsura": "уруйжылдыздар", "gintama": "гинтама", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "кемчиликсизвебтун", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "анимеком", "bobobobobobobo": "бобобобобобобо", "yuukiyuuna": "юук<PERSON>ю<PERSON>на", "nichijou": "күнүмдүк", "yurucamp": "юрукамп", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "учуучуаялзат", "wotakoi": "wotakoi", "konanime": "конаниме", "clannad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justbecause": "жөнэле", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "бардыкыйыктуулардынкөчөсү", "recuentosdelavida": "жашоодонэскермелер"}