{"2048": "2048", "mbti": "mbti", "enneagram": "eneagrama", "astrology": "astrologia", "cognitivefunctions": "funtziokognitiboak", "psychology": "psikologia", "philosophy": "filosofia", "history": "historia", "physics": "<PERSON><PERSON>a", "science": "zientzia", "culture": "kultura", "languages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "technology": "teknologia", "memes": "memeak", "mbtimemes": "mbtimemeak", "astrologymemes": "astrologiamemeak", "enneagrammemes": "enneagramamemeak", "showerthoughts": "dutxapentsamenduak", "funny": "<PERSON><PERSON><PERSON><PERSON>", "videos": "<PERSON><PERSON><PERSON>", "gadgets": "gailu", "politics": "politika", "relationshipadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "bi<PERSON>tz<PERSON><PERSON><PERSON><PERSON>", "crypto": "kripto", "news": "be<PERSON><PERSON>", "worldnews": "munduko_albisteak", "archaeology": "arkeologia", "learning": "ikastenari", "debates": "eztabaidak", "conspiracytheories": "konspirazioteoriak", "universe": "unibertsoa", "meditation": "meditazioa", "mythology": "mitologia", "art": "arte", "crafts": "eskulanak", "dance": "da<PERSON><PERSON>", "design": "diseinua", "makeup": "makillaj<PERSON>", "beauty": "ed<PERSON><PERSON><PERSON>", "fashion": "moda", "singing": "kantuan", "writing": "idazten", "photography": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosplay": "cosplay", "painting": "pintura", "drawing": "ma<PERSON>z<PERSON><PERSON>", "books": "liburuak", "movies": "p<PERSON><PERSON><PERSON>", "poetry": "poesia", "television": "telebista", "filmmaking": "zinegintza", "animation": "animazioa", "anime": "anime", "scifi": "zientziafikzioa", "fantasy": "fantasia", "documentaries": "dokumentalak", "mystery": "misteri<PERSON>", "comedy": "komedia", "crime": "krimena", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "beld<PERSON><PERSON>", "romance": "maitasuna", "realitytv": "telebista", "action": "ekint<PERSON>", "music": "musika", "blues": "blues", "classical": "klasikoa", "country": "<PERSON><PERSON><PERSON>", "desi": "desi", "edm": "edm", "electronic": "elektronika", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "etxe", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "bidaiak", "concerts": "kontzertuak", "festivals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "museums": "museoak", "standup": "<PERSON><PERSON><PERSON><PERSON>", "theater": "<PERSON><PERSON><PERSON><PERSON>", "outdoors": "kanpoan", "gardening": "lorategiak", "partying": "parranda", "gaming": "gaming", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "mazmorraketadragoiak", "chess": "xake", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "janaria", "baking": "gozo<PERSON>tza", "cooking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vegetarian": "<PERSON><PERSON><PERSON><PERSON>", "vegan": "begano", "birds": "txoriak", "cats": "katuak", "dogs": "txakurrak", "fish": "arrain", "animals": "<PERSON><PERSON><PERSON>", "blacklivesmatter": "blacklivesmatter", "environmentalism": "ingurumenismo", "feminism": "feminismoa", "humanrights": "gizaes<PERSON>bideak", "lgbtqally": "lgbtqaliatu", "stopasianhate": "asiarrenaurkakogorrotoariez", "transally": "transaliatu", "volunteering": "b<PERSON><PERSON><PERSON><PERSON>", "sports": "k<PERSON><PERSON>", "badminton": "badminton", "baseball": "beisbola", "basketball": "saskibaloia", "boxing": "boxeoa", "cricket": "kriketa", "cycling": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fitness": "fitness", "football": "futbola", "golf": "golf", "gym": "gimnasioa", "gymnastics": "gimnasia", "hockey": "hockeya", "martialarts": "arteb<PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "saskibaloia", "pilates": "pilates", "pingpong": "pingpong", "running": "kor<PERSON>", "skateboarding": "skateboarding", "skiing": "eskia", "snowboarding": "snowboarda", "surfing": "surfa", "swimming": "<PERSON><PERSON><PERSON><PERSON>", "tennis": "tenisa", "volleyball": "boleibol", "weightlifting": "halterofilia", "yoga": "yoga", "scubadiving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiking": "men<PERSON>zal<PERSON><PERSON><PERSON>", "capricorn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aquarius": "a<PERSON><PERSON>", "pisces": "piszis", "aries": "aries", "taurus": "taurus", "gemini": "gemini", "cancer": "minbizia", "leo": "leo", "virgo": "birgo", "libra": "libra", "scorpio": "scorpio", "sagittarius": "sagitario", "shortterm": "epelab<PERSON><PERSON>", "casual": "informal", "longtermrelationship": "harreman_luze", "single": "single", "polyamory": "polimaitasuna", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "<PERSON><PERSON>", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "asexuala", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "txakurrakikus<PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingsquest": "erre<PERSON><PERSON><PERSON><PERSON>", "soulreaver": "arimaharpailtzailea", "suikoden": "su<PERSON><PERSON>", "subverse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofspyro": "spyrorenelezenda", "rouguelikes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia": "siberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "il<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "munduirekia", "heroesofthestorm": "heroesofthestorm", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "dungeoncrawling", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribesofmidgard", "planescape": "planoescape", "lordsoftherealm2": "lordsoftherealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "kolorezale", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "simula<PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "roljo<PERSON>a", "witcher": "witcher", "dishonored": "desohoratua", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modde<PERSON>", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "murg<PERSON><PERSON><PERSON>", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motibaziosadikoa", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "maitatzekoeroagoa", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "gaslurraldeak", "pathfinder": "bidegile", "pathfinder2ndedition": "pathfinder2garrenedizioa", "shadowrun": "shadowrun", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "grabitatekolpea", "rpg": "jrj", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "auker<PERSON>_bakarra", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "agintari", "yourturntodie": "zuretxa<PERSON><PERSON>", "persona3": "persona3", "rpghorror": "rpgizua", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "pira<PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "<PERSON><PERSON><PERSON><PERSON>", "rpgtext": "rpgtestua", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "falloutbabeslekua", "gurps": "gurps", "darkestdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "bestekomunduak", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "di<PERSON><PERSON><PERSON><PERSON><PERSON>", "dynastywarriors": "dynastywarriors", "skullgirls": "skullgirls", "nightcity": "gau<PERSON><PERSON>a", "hogwartslegacy": "hog<PERSON><PERSON>dar<PERSON>", "madnesscombat": "bor<PERSON><PERSON>dness", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "errepidea96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikeak", "gothamknights": "gothamknights", "forgottenrealms": "ahaztutakoerreinuak", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "argiunesemea", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonmundua", "monsterrancher": "munstrohazku<PERSON><PERSON>", "ecopunk": "ekopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanversea", "fracturedthrones": "tronoakpuskaturik", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "itzalpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON><PERSON>", "hogwartmystery": "hog<PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltaberdea", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "azkenepoka", "starfinder": "izarbilatzaile", "goldensun": "eguz<PERSON>ur<PERSON>", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "iluntasuneanezpatak", "twilight2000": "ilunabera2000", "sandevistan": "sandevistan", "cyberpunk": "ciberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "ordenerorita", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "l<PERSON><PERSON><PERSON><PERSON>rak", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "debilubi<PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "mund<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "a<PERSON><PERSON><PERSON>a", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofsymphonia": "talesofsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sacredunderworld": "mundusakratuazpikoa", "chainedechoes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darksoul": "a<PERSON><PERSON><PERSON><PERSON>", "soulslikes": "soulslikes", "othercide": "bestealdetik", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "kronotrigger", "pillarsofeternity": "betierekozutabeak", "palladiumrpg": "palladiumrpg", "rifts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tibia": "tibia", "thedivision": "thedivision", "hellocharlotte": "kaixocharlotte", "legendofdragoon": "legendofdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "banpiroamaskara", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "otsogizonapokalipsia", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "mortakose<PERSON>ak", "engineheart": "motorbihотza", "fable3": "fable3", "fablethelostchapter": "fablegaldutakokapitulua", "hiveswap": "hiveswap", "rollenspiel": "roleplay", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON>zelaia", "oldschoolrevival": "oldschoolberpizkundea", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "mundubasatiak", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "iluntasunmazmorra", "juegosrpg": "rpgjo<PERSON>ak", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "udazkena", "gloomhaven": "gloomhaven", "wildhearts": "bihotzbasatiak", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "arcadikozeruak", "shadowhearts": "<PERSON>zalbihot<PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "odolsentimo", "breathoffire4": "suarenarasa4", "mother3": "ama3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "best<PERSON><PERSON>n", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "roleplay<PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "sorg<PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "harry<PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "eizaerrege", "albertodyssey": "al<PERSON>dis<PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforoa", "shadowheartscovenant": "shadowheartsenalkartzea", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "awplanet": "awplaneta", "theworldendswithyou": "mundualabatugaitik", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "<PERSON><PERSON>itz<PERSON>", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "lurmagia", "blackbook": "kontaktuiltzua", "skychildrenoflight": "zerukoumeak", "gryrpg": "gryrpg", "sacredgoldedition": "sakratuaurreedizioa", "castlecrashers": "gaztelubirrintzaileak", "gothicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "gamingrpg", "prophunt": "<PERSON><PERSON><PERSON><PERSON>", "starrails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "indierpg", "pointandclick": "klikaetagertatu", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON>", "indivisible": "zatiezina", "freeside": "albolibre", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "kanadarakoheriotzaerrepidea", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "munstroehiztaria", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geosupremazia", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "rpgtaktikoa", "mahoyo": "mahoyo", "animegames": "animejokoak", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "betierekosoinua", "princessconnect": "printzesarenkonexioa", "hexenzirkel": "sorgintaldea", "cristales": "kristalak", "vcs": "vcs", "pes": "pes", "pocketsage": "patrikapentsal<PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantindiar", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "ejo<PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ametsgileenliga", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "jola<PERSON>", "overwatchleague": "overwatchliga", "cybersport": "ziberkirolea", "crazyraccoon": "mapatxeroa", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorant<PERSON><PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "balbula", "portal": "atari", "teamfortress2": "teamfortress2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "ahuntzsimulatzailea", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "battlefield4", "nightinthewoods": "bas<PERSON><PERSON>an", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvaniak", "overcooked": "<PERSON><PERSON><PERSON>", "interplanetary": "planetarteko", "helltaker": "infernuhartzailea", "inscryption": "inskripzioa", "7d2d": "7d2d", "deadcells": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "lubaki", "stray": "kale", "battlefield": "g<PERSON><PERSON><PERSON><PERSON>", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eyeb": "betazainak", "blackdesert": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partyhard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "espaziontzizailezailea", "hades": "hades", "gunsmith": "armero", "okami": "<PERSON>ami", "trappedwithjester": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinkum": "dinkum", "predecessor": "aurre<PERSON>a", "rainworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "qudkobak", "colonysim": "koloniasimu", "noita": "noita", "dawnofwar": "ger<PERSON><PERSON><PERSON>", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "ilunailunago", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON>jostun<PERSON>", "datingsims": "datingsimak", "yaga": "yaga", "cubeescape": "kub<PERSON><PERSON>", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "<PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsia", "virtualkenopsia": "kenopsiabirtuala", "snowrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "hondamendikoliburutegia", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "bidegile", "kenabridgeofspirits": "kenaspirituzubia", "placidplasticduck": "ahatet<PERSON>len<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlebit": "battlebit", "ultimatechickenhorse": "ultimateoilaskozaldia", "dialtown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "irri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "katuengaua", "supermeatboy": "supermeatboy", "tinnybunny": "tinnytxitxi", "cozygrove": "cozygrove", "doom": "hondamendia", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdu<PERSON><PERSON>mb<PERSON>ak", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paladins": "paladins", "earthdefenseforce": "<PERSON>ur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "huntshowdown": "huntshowdown", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrahil", "joinsquad": "ta<PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "insurgencysandstorm", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "ispi<PERSON>rt<PERSON>", "divisions2": "zatiketak2", "killzone": "hildakogunea", "helghan": "hel<PERSON>", "coldwarzombies": "zombihilhotzagerra", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "aceconbat", "crosscode": "crosscode", "goldeneye007": "urrebegia007", "blackops2": "blackops2", "sniperelite": "frankotiratzaileelite", "modernwarfare": "ger<PERSON><PERSON><PERSON><PERSON>", "neonabyss": "<PERSON>leizea", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "<PERSON><PERSON><PERSON>lude<PERSON>", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "escapefromtarkovtik", "metalslug": "metalslug", "primalcarnage": "b<PERSON><PERSON><PERSON>etsaia", "worldofwarships": "worldofwarships", "back4blood": "odo<PERSON><PERSON><PERSON><PERSON>", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "<PERSON><PERSON><PERSON>", "masseffect": "masseffect", "systemshock": "sistemakolpea", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doometernal": "doometernal", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mua", "division2": "division2", "tythetasmaniantiger": "tytasmaniakotigrea", "generationzero": "zerobelaunaldia", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "saltxitxagizoia", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "minamamu<PERSON>", "warface": "gerraaurpegia", "crossfire": "fuego_cruzado", "atomicheart": "atomicheart", "blackops3": "blackops3", "vampiresurvivors": "vampirebiziraunak", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "askatasunpocalypse", "battlegrounds": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "juegosfps", "convertstrike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warzone2": "warzone2", "shatterline": "haust<PERSON><PERSON><PERSON>", "blackopszombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodymess": "o<PERSON><PERSON>zkonahas<PERSON><PERSON>", "republiccommando": "errepublikakokomandoa", "elitedangerous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soldat": "soldadu", "groundbranch": "lur<PERSON>r", "squad": "kuadrilla", "destiny1": "destiny1", "gamingfps": "joko<PERSON><PERSON>", "redfall": "redfall", "pubggirl": "pubgneska", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "militar<PERSON>in", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "ordainsaria2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "pubgtxekiar", "titanfall2": "titanfall2", "soapcod": "xaboibakailua", "ghostcod": "mamucod", "csplay": "csplay", "unrealtournament": "unrealtournament", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "<PERSON><PERSON><PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonzuria", "remnant": "aztarna", "azurelane": "azurelane", "worldofwar": "ger<PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "it<PERSON>lera", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "itzalgizoa", "quake2": "quake2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "<PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "itsaslapur", "rust": "rust", "conqueronline": "conqueronline", "dauntless": "ausartsu", "warships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "dragoieg<PERSON>k", "warthunder": "warthunder", "flightrising": "hegazkinaltxaketa", "recroom": "aisialdiaretoa", "legendsofruneterra": "legendsofruneterra", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "phantasystaronline2", "maidenless": "neskamutilik_gabe", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "tankemundua", "crossout": "bazkenguratu", "agario": "agario", "secondlife": "bigarrenbizitza", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "zikina", "newworld": "mund<PERSON><PERSON>a", "blackdesertonline": "blackdesertonline", "multiplayer": "joka<PERSON><PERSON>_anitz", "pirate101": "pirata101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowklasikoa", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "sorkuntzarenerrautsak", "riotmmo": "riotmmo", "silkroad": "zetabidea", "spiralknights": "spiralknights", "mulegend": "mulegenda", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "dragoienprofezia", "grymmo": "grymmo", "warmane": "beroa", "multijugador": "multijokatzaile", "angelsonline": "aingeruaksarean", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsrepublikazaharra", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "blueprotocol", "perfectworld": "munduh<PERSON><PERSON>", "riseonline": "lineansartu", "corepunk": "corepunk", "adventurequestworlds": "adventurequestworlds", "flyforfun": "hegalditxikiak", "animaljam": "animaljam", "kingdomofloathing": "loathing<PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "streetfighter", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "for<PERSON>or", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "b<PERSON><PERSON><PERSON>ritzabi<PERSON><PERSON><PERSON>", "streetsofrage": "kalee<PERSON>kohaserreak", "mkdeadlyalliance": "mkalianzahilgarria", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "borrokalarienerregea", "likeadragon": "<PERSON><PERSON><PERSON>", "retrofightinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blasphemous": "blasfemo", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "superkolpe", "mugen": "mugen", "warofthemonsters": "muns<PERSON>eng<PERSON><PERSON>", "jogosdeluta": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberbots": "<PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "gudariblindatuak", "finalfight": "azkenborrokaldia", "poweredgear": "ekipatupotentea", "beatemup": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "hiltzaileinstintua", "kingoffigthers": "borrokalarienerregea", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "zalduntasuna2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightjarraipena", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksongjokoa", "silksongnews": "silksongnews", "silksong": "silksong", "undernight": "gauazpian", "typelumina": "idatzilumina", "evolutiontournament": "bilakaeratorneoa", "evomoment": "evomoment", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "bloodborne", "horizon": "horizonte", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "odole<PERSON><PERSON>", "uncharted": "esploratugabea", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "azkenogu", "infamous": "osp<PERSON><PERSON><PERSON>", "playstationbuddies": "playstationlagunak", "ps1": "ps1", "oddworld": "mundubitxia", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "altxorra", "detroitbecomehuman": "detroitbecomehuman", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "goizaldera", "touristtrophy": "turistasaria", "lspdfr": "lspdfr", "shadowofthecolossus": "kolosoaren_itzala", "crashteamracing": "crashteamracing", "fivepd": "bostpolizia", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "jolasgeltokia", "samuraiwarriors": "sa<PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "azkenbabeslea", "soulblade": "a<PERSON>ezpa<PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "gizonehiza", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "azkenbabeslea", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "parrandazalea", "warharmmer40k": "warhammer40k", "fightnightchampion": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "psikonautak", "mhw": "mhw", "princeofpersia": "persiakoprintzea", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "g<PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "espeleo", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "izarretaralotuta", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "etxebirgaitzaile", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "erregerizenligan", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "telebazarra", "skycotl": "skycotl", "erica": "erica", "ancestory": "arbaso", "cuphead": "cuphead", "littlemisfortune": "txikimalasuerte", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "munstroprom", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motozaleak", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "<PERSON><PERSON>tsarenkultu", "duckgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "stanleyparadoxa", "towerunite": "<PERSON><PERSON><PERSON><PERSON>", "occulto": "ezkutuan", "longdrive": "bidaialuzea", "satisfactory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pluviophile": "eurimaitale", "underearth": "lurpean", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "espi<PERSON><PERSON><PERSON><PERSON>", "darkdome": "kup<PERSON><PERSON><PERSON>", "pizzatower": "pizzadorrea", "indiegame": "indiejokoa", "itchio": "itchio", "golfit": "golfit", "truthordare": "egiaalaausardia", "game": "joko", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "<PERSON><PERSON><PERSON><PERSON>", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "ausartu", "scavengerhunt": "altxorrenbila", "yardgames": "<PERSON><PERSON><PERSON><PERSON>", "pickanumber": "aukeratuzenbakia", "trueorfalse": "egia<PERSON><PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "dadoiratxo", "cosygames": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "ligatzejolastu", "freegame": "doakojo<PERSON>a", "drinkinggames": "edatekojokoak", "sodoku": "sodoku", "juegos": "joko<PERSON>", "mahjong": "mahjong", "jeux": "joko<PERSON>", "simulationgames": "simula<PERSON>_jokoak", "wordgames": "hitzekinjolasten", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "hitzekinjolasten", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "oyun", "interactivegames": "jokointeraktiboak", "amtgard": "amtgard", "staringcontests": "beg<PERSON>lehiaket<PERSON>", "spiele": "joko<PERSON>", "giochi": "giochi", "geoguessr": "geoguessr", "iphonegames": "iphonekojokoak", "boogames": "boo<PERSON><PERSON>", "cranegame": "gar<PERSON><PERSON><PERSON><PERSON>", "hideandseek": "ezkutaketan", "hopscotch": "txingoka", "arcadegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "jokoklasikoa", "mindgames": "b<PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancegame": "amod<PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "4xgames": "4x<PERSON><PERSON><PERSON>", "gamefi": "gamefi", "jeuxdarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "jokoak90", "idareyou": "au<PERSON>tz<PERSON>", "mozaa": "mozaa", "fumitouedagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "egiazkoafaltsuaala", "playgames": "jola<PERSON><PERSON>", "gameonline": "jokoonline", "onlinegames": "jokoonline", "jogosonline": "onlinejokoak", "writtenroleplay": "idatzizkorolplay", "playaballgame": "partidatxobatjokatu", "pictionary": "pictionary", "coopgames": "koopjo<PERSON>ak", "jenga": "jenga", "wiigames": "wii<PERSON><PERSON><PERSON>", "highscore": "puntuazioaltua", "jeuxderôles": "rolipartidak", "burgergames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwediziobeltz", "jeuconcour": "lehiaketa", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "gioco", "managementgame": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "ezkutukobjektuenjokoa", "roolipelit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "formula1game": "formula1jokoa", "citybuilder": "hirigilea", "drdriving": "drdriving", "juegosarcade": "jokozalondo", "memorygames": "memoria<PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "flippermakinak", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "sofajokoa", "perguntados": "galdetuak", "gameo": "<PERSON><PERSON><PERSON><PERSON>", "lasergame": "laserjokoa", "imessagegames": "imessage<PERSON><PERSON><PERSON>", "idlegames": "jokogeldirik", "fillintheblank": "hue<PERSON><PERSON><PERSON><PERSON>", "jeuxpc": "jokoakpc", "rétrogaming": "retrojokoak", "logicgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "japangame": "japoniajokoa", "rizzupgame": "ligatzejokoanahobetu", "subwaysurf": "metrosurfa", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5vs5", "rolgame": "roljo<PERSON>a", "dashiegames": "dashiegames", "gameandkill": "j<PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "joko<PERSON>di<PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "joko<PERSON><PERSON>", "textbasedgames": "testudunjokok", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "atzerapegira", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "lorategi_jolasak", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "mahaiafutbola", "tischfußball": "mahaifutbola", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "<PERSON><PERSON><PERSON><PERSON>", "casualgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegames": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "játék": "jokoa", "bordfodbold": "mahaifutbola", "jogosorte": "jokosor<PERSON>", "mage": "mago", "cargames": "autojokoak", "onlineplay": "onlinejokoa", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON>", "pursebingos": "poltsakobingoak", "randomizer": "ausazkoa", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "jokoak_pc", "socialdeductiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominos": "dominos", "domino": "domino", "isometricgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodoldgames": "jokoklasikoak", "truthanddare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "altxorbilaketak", "jeuxvirtuel": "jokobirtualak", "romhack": "romhack", "f2pgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "free2play": "doanguzaiteke", "fantasygame": "fantasiajokoa", "gryonline": "gryonline", "driftgame": "driftjokoa", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "halotbsaileraketajokoak", "mushroomoasis": "perretxuoasia", "anythingwithanengine": "edozermotor", "everywheregame": "<PERSON><PERSON>_j<PERSON>a", "swordandsorcery": "ezpataetamagia", "goodgamegiving": "partidaona<PERSON><PERSON><PERSON>", "jugamos": "jola<PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "miniaturazkojokoak", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crimegames": "krimenjo<PERSON>ak", "dobbelspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spelletjes": "<PERSON><PERSON><PERSON><PERSON>", "spacenerf": "espazionerfa", "charades": "mitxak", "singleplayer": "bakarreko_jokalaria", "coopgame": "co<PERSON><PERSON><PERSON><PERSON>", "gamed": "joka<PERSON>", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "jokonagusia", "kingdiscord": "kinggdiscord", "scrabble": "scrabble", "schach": "schach", "shogi": "shogi", "dandd": "dyd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "pandemiarenondarea", "camelup": "gameraigor<PERSON>", "monopolygame": "monopolyjokoa", "brettspiele": "brettspiele", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "<PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "sällskapspel", "planszowe": "<PERSON><PERSON><PERSON><PERSON>", "risiko": "arriskua", "permainanpapan": "ma<PERSON><PERSON>a", "zombicide": "zombihilketa", "tabletop": "<PERSON><PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "odolontzia", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectfour": "<PERSON><PERSON><PERSON><PERSON>", "heroquest": "heroquest", "giochidatavolo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "carrom", "tablegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "partxis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "idazmahaijokoak", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "topaketekosmikoa", "creationludique": "sorkuntza_ludikoa", "tabletoproleplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "kartoiz<PERSON>_jokoak", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "infinitythegame": "infinityjokoa", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "igoeraketajaitsierak", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "basur<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardom": "asperraldia", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "giza<PERSON><PERSON><PERSON><PERSON>", "gameboard": "j<PERSON><PERSON><PERSON>", "dominó": "domin<PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "twilightimperium", "horseopoly": "zaldiopoly", "deckbuilding": "ma<PERSON>eraik<PERSON>za", "mansionsofmadness": "er<PERSON><PERSON>mans<PERSON><PERSON>", "gomoku": "gomoku", "giochidatavola": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "brimstonekoitzalak", "kingoftokyo": "tokyokoerregea", "warcaby": "dama", "táblajátékok": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "itsasontzia", "tickettoride": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catán": "catán", "subbuteo": "mahai_futbola", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "starwarslegion", "gochess": "<PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "ger<PERSON><PERSON><PERSON>", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identityv", "theisle": "theisle", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "cth<PERSON>hur<PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "amongus", "eco": "eko", "monkeyisland": "tximin<PERSON>la", "valheim": "valheim", "planetcrafter": "planetasortzailea", "daysgone": "egunakiraganak", "fobia": "fobia", "witchit": "sorgindu", "pathologic": "<PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7egun", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "zigortu<PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "aitaeroa", "dontstarve": "ezgosetu", "eternalreturn": "bet<PERSON><PERSON><PERSON>", "pathoftitans": "titanenbidea", "frictionalgames": "frictionalgames", "hexen": "sorgina<PERSON>", "theevilwithin": "barn<PERSON><PERSON><PERSON><PERSON>", "realrac": "realrac", "thebackrooms": "atzegelak", "backrooms": "atzegelak", "empiressmp": "empiressmp", "blockstory": "blokeistorioa", "thequarry": "harrobiak", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "thewalkingdeadjokoa", "wehappyfew": "gutxibatzukzoriontsu", "riseofempires": "inperioengorakada", "stateofsurvivalgame": "stateofsurvivalgame", "vintagestory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvival": "arkbizirik", "barotrauma": "barotrauma", "breathedge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "westlendbiziirautea", "beastsofbermuda": "bermudakopiztiak", "frostpunk": "frostpunk", "darkwood": "baseilun", "survivalhorror": "beldurrezko_biziiraupena", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "tren<PERSON><PERSON><PERSON>", "lifeaftergame": "jokoaetarenondoren", "survivalgames": "bi<PERSON>raute<PERSON><PERSON>ak", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfundazioa", "greenproject": "proiektuberdea", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "raft", "rdo": "rdo", "greenhell": "infernutxuria", "residentevil5": "residentevil5", "deadpoly": "deadpoly", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "amona", "littlenightmares2": "gaubeldurtxikiak2", "signalis": "<PERSON><PERSON><PERSON>", "amandatheadventurer": "amandaabenturazalea", "sonsoftheforest": "bas<PERSON>ensemeak", "rustvideogame": "rustvideojokoa", "outlasttrials": "outlastprobak", "alienisolation": "alienisolazioa", "undawn": "egunsentia", "7day2die": "7egun2hil", "sunlesssea": "itsasoeguzkigabea", "sopravvivenza": "sopravvivenza", "propnight": "propnight", "deadisland2": "deadisland2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "hondamendiegunilunak", "soma": "soma", "fearandhunger": "beld<PERSON>ragosea", "stalkercieńczarnobyla": "txernobilekostalk<PERSON>", "lifeafter": "ondorengobizitza", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON>", "clocktower3": "dorretxua3", "aloneinthedark": "bakarrikilunpean", "medievaldynasty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "projectnimbusjokoa", "eternights": "<PERSON><PERSON><PERSON><PERSON>", "craftopia": "<PERSON><PERSON><PERSON><PERSON>", "theoutlasttrials": "outlastfrogak", "bunker": "bunker", "worlddomination": "munduarenmenperatzea", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "hiltzailebulegoa", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "nanoehiltzaile", "warhammer40kcrush": "warhammer40kmaitemindua", "wh40": "wh40", "warhammer40klove": "warhammer40kmaite", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindicare": "vindicare", "ilovesororitas": "sororidadmaitedut", "ilovevindicare": "ilovebindicare", "iloveassasinorum": "maitezaitutsasinorum", "templovenenum": "tenplumaitenum", "templocallidus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "alferrikdenbora", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "birenkontuada", "wingspan": "<PERSON><PERSON><PERSON><PERSON>", "terraformingmars": "marteterraformatzea", "heroesofmightandmagic": "heroesofmightandmagic", "btd6": "btd6", "supremecommander": "komandantenagusia", "ageofmythology": "mitologiarenagaraia", "args": "args", "rime": "rime", "planetzoo": "planetazoo", "outpost2": "outpost2", "banished": "kanporatua", "caesar3": "caesar3", "redalert": "alertago<PERSON><PERSON>", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "aginduetakonkistatu", "warcraft3": "warcraft3", "eternalwar": "betierrak<PERSON>_gerra", "strategygames": "estrategiajokoak", "anno2070": "anno2070", "civilizationgame": "zibilizaziojokoa", "civilization4": "civilization4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "espora", "totalwar": "ger<PERSON><PERSON><PERSON>", "travian": "travian", "forts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goodcompany": "konpainiaona", "civ": "civ", "homeworld": "mundua", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "estrategiadenboarean", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eu4": "eu4", "vainglory": "harrokeria", "ww40k": "mm40k", "godhood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "daverenaljebraklasedibertsia", "plagueinc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theorycraft": "teoriasor<PERSON>", "mesbg": "nesbg", "civilization3": "civilization3", "4inarow": "4idansegidan", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "gerraaurreratuak", "ageofempires2": "ageofempires2", "disciples2": "jarleak2", "plantsvszombies": "landarevsbizidunhilak", "giochidistrategia": "estrategiajokoak", "stratejioyunları": "estrategiajokoak", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "dinosauruoerregea", "worldconquest": "mundukudeaketa", "heartsofiron4": "heartsofiron4", "companyofheroes": "heroienkompania", "battleforwesnoth": "wesnothgatakoborroka", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phobies": "fobiak", "phobiesgame": "fobias<PERSON><PERSON><PERSON>", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "kanpoplanoa", "turnbased": "txandaka", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "erregekrutzatuak", "cultris2": "cultris2", "spellcraft": "sorginkeria", "starwarsempireatwar": "starwarsgerraimperioa", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estrategia", "popfulmail": "popfulmail", "shiningforce": "indarargitsua", "masterduel": "masterduel", "dysonsphereprogram": "dysonenesferaprograma", "transporttycoon": "garraiomagnatua", "unrailed": "trenbiderikgabe", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "goilurraldeetakoresumak", "galaxylife": "galaxiabizitz<PERSON>", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slay<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlecats": "katugudariak", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "needforspeedcarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "iraun", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "alicemad<PERSON>ret<PERSON>s", "darkhorseanthology": "zaldibeltzantologia", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "fivenightsatfreddys", "saiko": "saiko", "fatalframe": "beldurmarko", "littlenightmares": "gau<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "hilen<PERSON>tze", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "etxeraturik", "deadisland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "zortetxartxo", "projectzero": "proiektuazero", "horory": "beld<PERSON><PERSON>", "jogosterror": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor": "kaix<PERSON>uz<PERSON><PERSON>", "helloneighbor2": "kaixoauzokide2", "gamingdbd": "gamingdbd", "thecatlady": "katander<PERSON>", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "beldurrezkojokoetan", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "gizateriarenaurkakokartak", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "kodeizenoak", "dixit": "dixit", "bicyclecards": "barajak", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "runetakoelezaharrak", "solitaire": "baka<PERSON><PERSON>", "poker": "pokerra", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "giltzaforja", "cardtricks": "kartatrikuak", "playingcards": "ka<PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "trukekromoak", "pokemoncards": "pokemonkartak", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "k<PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "️", "warcry": "guda<PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "bihotzerrege", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "erresistentzia", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohkartak", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "<PERSON><PERSON><PERSON><PERSON>", "yugiohgame": "yug<PERSON>hpartida", "darkmagician": "ma<PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "begiburdakdragoizuria", "yugiohgoat": "yug<PERSON>honena", "briscas": "briskak", "juegocartas": "kartajokoa", "burraco": "burrako", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "karta<PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "karta<PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "carteado", "sueca": "sueka", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "espirit<PERSON><PERSON>rok<PERSON>", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "kartajokoa", "žolíky": "komodinak", "facecard": "aurpeg<PERSON><PERSON><PERSON><PERSON>", "cardfight": "kartaborroka", "biriba": "biriba", "deckbuilders": "mazoeraikitzaileak", "marvelchampions": "marvelchampions", "magiccartas": "kartamagikoak", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "zaldiunikornoeroak", "cyberse": "ciberse", "classicarcadegames": "arkupejokozaharrak", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "ostiralgauekodantza", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "proiektumirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "gita<PERSON><PERSON><PERSON>a", "clonehero": "clonehero", "justdance": "justdance", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "da<PERSON><PERSON><PERSON><PERSON>", "rhythmgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stepmania": "stepmania", "highscorerythmgames": "erritmojokoenpuntuazioaltuak", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "rhythmheaven", "hypmic": "hypmic", "adanceoffireandice": "suetaizotzare<PERSON><PERSON><PERSON>", "auditiononline": "audizioonline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "erritmodoktorea", "cubing": "kuboak", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "<PERSON><PERSON><PERSON><PERSON>", "spotit": "ikusi", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "log<PERSON><PERSON><PERSON><PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "b<PERSON><PERSON><PERSON><PERSON>", "rubikscube": "rubik<PERSON><PERSON>", "crossword": "gurutzegrama", "motscroisés": "hitzk<PERSON>zi", "krzyżówki": "gurutzegrama", "nonogram": "nonograma", "bookworm": "liburuzale", "jigsawpuzzles": "zortzimilaeus<PERSON>n", "indovinello": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riddle": "asmakizun", "riddles": "asmakizunak", "rompecabezas": "buruhausgarriak", "tekateki": "tekateki", "inside": "barruan", "angrybirds": "hasarretutakohegaztiak", "escapesimulator": "ihessimuladore", "minesweeper": "<PERSON><PERSON>na", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesjokoa", "puzzlesport": "puzzle<PERSON><PERSON><PERSON>", "escaperoomgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapegame": "<PERSON><PERSON><PERSON><PERSON>ae<PERSON>", "3dpuzzle": "3dpuzzlea", "homescapesgame": "homescapesjokoa", "wordsearch": "<PERSON>z<PERSON>ake<PERSON>", "enigmistica": "enigmistika", "kulaworld": "k<PERSON><PERSON><PERSON>", "myst": "myst", "riddletales": "ipuinigarkizunak", "fishdom": "fishdom", "theimpossiblequiz": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "match3puzlea", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "bitxia", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "cubor<PERSON>k", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "etxebizitzak", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tycoongames": "magnate<PERSON><PERSON><PERSON>", "cubosderubik": "rubikkuboak", "cruciverba": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ciphers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rätselwörter": "hitzasmakizunak", "buscaminas": "buscaminas", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON>", "turnipboy": "arbio<PERSON><PERSON>a", "adivinanzashot": "igarkilunetsuak", "nobodies": "inornork", "guessing": "<PERSON><PERSON><PERSON><PERSON>", "nonograms": "nonogramak", "kostkirubika": "kostkirubika", "crypticcrosswords": "kriptikohitzgurutzatuak", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON>", "puzzlehunts": "puzleehizak", "catcrime": "katukrimen", "quebracabeça": "buru<PERSON><PERSON><PERSON>", "hlavolamy": "buruhausgarriak", "poptropica": "poptropica", "thelastcampfire": "azkensukanpalekua", "autodefinidos": "autodefinituak", "picopark": "picopark", "wandersong": "bidaiakantak", "carto": "carto", "untitledgoosegame": "untitledgoosegame", "cassetête": "buruhauste", "limbo": "limbo", "rubiks": "rubiks", "maze": "labirinto", "tinykin": "txikitxo", "rubikovakostka": "rubikovakostka", "speedcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pieces": "zatiak", "portalgame": "portaljokoa", "bilmece": "asmakizun", "puzzelen": "<PERSON><PERSON>", "picross": "picross", "rubixcube": "rubik<PERSON><PERSON>", "indovinelli": "asmakizunak", "cubomagico": "kubomajikoa", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "wonderlandokertua", "monopoly": "monopoly", "futurefight": "etorkizunekoborrokaa", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "b<PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemystars", "stateofsurvival": "biziraupenaregoera", "mycity": "nire<PERSON>ia", "arknights": "arknights", "colorfulstage": "koloretakoeszenatokia", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "zaldunlasterketa", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "futbolborroka", "a3": "a3", "phonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "erregeaukerake<PERSON>", "guardiantales": "guardiantales", "petrolhead": "petrolzale", "tacticool": "taktikomoloi", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "eznagogunean", "craftsman": "eskulangile", "supersus": "supersusmoa", "slowdrive": "g<PERSON><PERSON><PERSON><PERSON>", "headsup": "kontuz", "wordfeud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bedwars": "bedwars", "freefire": "freefire", "mobilegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "lilyren_lorategia", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "dardarak", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "poltsikomaitasuna", "androidgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "criminalcase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "sukaldeeroaldia", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "aingeruleague", "lordsmobile": "lordsmobile", "tinybirdgarden": "txoritxolorategia", "gachalife": "gachalife", "neuralcloud": "hodeineural", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "warrobots", "mirrorverse": "ispilu<PERSON>bertso<PERSON>", "pou": "pou", "warwings": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "etorkizunekoaldia", "antiyoy": "antini", "apexlegendmobile": "apexlegendmobile", "ingress": "ingress", "slugitout": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "maskotalagunak", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "arenaihesketa", "wolfy": "otsoa", "runcitygame": "hirikor<PERSON>lar<PERSON>", "juegodemovil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "imitazioa", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "bombmeubrasil", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "<PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "bidegabe<PERSON>", "sealm": "itsuasoren", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "hitzeaklagunekin2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "katutxoistorioperfektua", "showbyrock": "showbyrock", "ladypopular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "mundu_perfektua_mugikorra", "empiresandpuzzles": "inperioaketa3lokiak", "empirespuzzles": "inperioburuhausgarriak", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileindia", "fanny": "ipurdi", "littlenightmare": "amesgaiztoatxikia", "aethergazer": "aethergazer", "mudrunner": "lokatzlasterketa", "tearsofthemis": "themisenmalkoak", "eversoul": "betiereesentzia", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zonbinaufragoak", "eveechoes": "eveechoes", "jogocelular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "streetfighterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "neskenfrontea", "jurassicworldalive": "jurassicworldalive", "soulseeker": "arimabilatzaile", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "ilargikateistoria", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "jokosakelutxoak", "legendofneverland": "never<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "jokomugikorrelegioak", "timeraiders": "denborapira<PERSON>", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON>_jokoak", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "katugudariak", "dnd": "dnd", "quest": "<PERSON><PERSON><PERSON>", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "worldofdarkness": "mund<PERSON><PERSON>", "travellerttrpg": "bidaiariroljo<PERSON><PERSON>", "2300ad": "2300ko", "larp": "larp", "romanceclub": "maitasunkluba", "d20": "d20", "pokemongames": "poke<PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemongor<PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON>a", "pokemonpurpura": "poke<PERSON><PERSON>a", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "rockettaldea", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "pocketmonsterrak", "nuzlocke": "nuzlocke", "pokemonplush": "poke<PERSON><PERSON><PERSON>", "teamystic": "mystictaldea", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "pokemonbri<PERSON><PERSON>n", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "burdineskuak", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "poke<PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON>rak<PERSON><PERSON>mon<PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "txarizar", "shinyhunter": "distiraharrapatzaile", "ajedrez": "xake", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "xake", "schaken": "xakea", "skak": "skak", "ajedres": "x<PERSON><PERSON>", "chessgirls": "xakeneskatilak", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "munduko_blitza", "jeudéchecs": "xakepartida", "japanesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chinesechess": "<PERSON><PERSON><PERSON><PERSON>", "chesscanada": "<PERSON><PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "<PERSON>ake<PERSON><PERSON><PERSON>", "openings": "irekierak", "rook": "dorre", "chesscom": "chesscom", "calabozosydragones": "ziegorraketatragoiak", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "dungeonmaster", "tiamat": "tiamat", "donjonsetdragons": "ziegamorraketatragoak", "oxventure": "oxbentura", "darksun": "eguzkibeltz", "thelegendofvoxmachina": "voxmachinakoleijenda", "doungenoanddragons": "mazmorraketadragoiak", "darkmoor": "ilunmoor", "minecraftchampionship": "minecrafttxapelketa", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmodak", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON>", "fru": "fru", "addons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "moddedminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecrafthiria", "pcgamer": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvideo": "jeuxvideo", "gambit": "gambit", "gamers": "gamerrak", "levelup": "mailaigo", "gamermobile": "gamermugikor", "gameover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "p<PERSON><PERSON><PERSON><PERSON>", "gamen": "gamen", "oyunoynamak": "jokoanjolast<PERSON>", "pcgames": "p<PERSON><PERSON><PERSON><PERSON>", "casualgaming": "jokotxoak", "gamingsetup": "gamingsetup", "pcmasterrace": "pcmasterrace", "pcgame": "pcjo<PERSON><PERSON>", "gamerboy": "gamermutila", "vrgaming": "v<PERSON><PERSON><PERSON>ak", "drdisrespect": "drdisrespect", "4kgaming": "4kjokoak", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "kontsola_jokalaria", "boxi": "boxi", "pro": "pro", "epicgamers": "epikjokalaria", "onlinegaming": "onlinejokoak", "semigamer": "er<PERSON><PERSON><PERSON><PERSON>", "gamergirls": "gamerneskak", "gamermoms": "gamermamak", "gamerguy": "gamermutila", "gamewatcher": "j<PERSON><PERSON><PERSON>", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerneskak", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "teamahaleginak", "mallugaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pawgers": "pawgers", "quests": "mi<PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON>", "cozygaming": "jokogoxoak", "gamelpay": "gamelpay", "juegosdepc": "p<PERSON><PERSON><PERSON><PERSON>", "dsswitch": "<PERSON><PERSON><PERSON><PERSON>", "competitivegaming": "jokoleihaketak", "minecraftnewjersey": "minecraftnewjersey", "faker": "g<PERSON><PERSON><PERSON>", "pc4gamers": "jokoetarakopcak", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heterosexual<PERSON><PERSON><PERSON>", "gamepc": "gamepc", "girlsgamer": "ne<PERSON>jokal<PERSON>", "fnfmods": "fnfmodak", "dailyquest": "egunerokozeregin", "gamegirl": "gameneska", "chicasgamer": "neskagamerrak", "gamesetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "gehiegizkoa", "socialgamer": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "gamejam", "proplayer": "projokalaria", "roleplayer": "roleroa", "myteam": "<PERSON><PERSON><PERSON>a", "republicofgamers": "gamerlakuenerrepublika", "aorus": "aorus", "cougargaming": "pumagaming", "triplelegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerbuddies": "gamerlaguna", "butuhcewekgamers": "neskajokalariakbehar", "christiangamer": "kristaugamer", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "ez_naiz_hemen", "andregamer": "andregamer", "casualgamer": "jokalariinformal", "89squad": "89taldea", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "segurt", "gemers": "gamerrak", "oyunizlemek": "oyunizlemek", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "bideojokozalea", "wspólnegranie": "elkarreanjolastea", "mortdog": "mortdog", "playstationgamer": "playstationjokalaria", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "<PERSON><PERSON><PERSON><PERSON>", "protogen": "protogen", "womangamer": "emakumejokalaria", "obviouslyimagamer": "jakinaridanaizgamer", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "humanfallflat": "humanfallflat", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "i<PERSON>aldizero", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusika", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "fallguys", "switch": "aldatu", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "mariokartnagusia", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "skychildrenofthelight", "tomodachilife": "to<PERSON><PERSON><PERSON><PERSON><PERSON>tz<PERSON>", "ahatintime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "ibiltzekosimuladoreak", "nintendogames": "ninte<PERSON><PERSON><PERSON><PERSON>", "thelegendofzelda": "zeldakoelezandua", "dragonquest": "dragonquest", "harvestmoon": "ilargi<PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "zeldaarnasa", "myfriendpedro": "<PERSON>rel<PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "zeldakoelezaunak", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "<PERSON><PERSON><PERSON>", "earthbound": "lurrari_lotua", "tales": "ipuinak", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermarioanai", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "trianguluestrategia", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "ka<PERSON>ina<PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendos": "nintendos", "new3ds": "3dsberria", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulegudariak", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioetasonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "marioetaweege", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "kani<PERSON><PERSON><PERSON><PERSON>", "vanillalol": "<PERSON><PERSON><PERSON><PERSON>", "wildriftph": "wildriftph", "lolph": "loleh", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "iragarkiaramatea", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendseuskadi", "aatrox": "aatrox", "euw": "nazka", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "kondairetakozbaleak", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexateak", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retro<PERSON><PERSON><PERSON><PERSON><PERSON>", "scaryvideogames": "beldurrezkojokoakmesedezerabatere", "videogamemaker": "bideojokoeg<PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "<PERSON><PERSON><PERSON><PERSON>", "videosgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "jokoaretoak", "acnh": "acnh", "puffpals": "lagunpuztu", "farmingsimulator": "nekazaritzasimulad<PERSON>a", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxalemania", "robloxdeutsch": "robloxeuskera", "erlc": "erlc", "sanboxgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "errodrom<PERSON>", "parasiteeve": "parasitogaua", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "ametspaisaia", "starcitizen": "starcitizen", "yanderesimulator": "ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto": "grandtheftauto", "deadspace": "hildakoespazioa", "amordoce": "ma<PERSON>ungoz<PERSON>", "videogiochi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theoldrepublic": "errepublikazaharra", "videospiele": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "ekintzaabentura", "storyofseasons": "udaberrietaistorioa", "retrogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "arkaderetro", "vintagecomputing": "ordenagail<PERSON><PERSON><PERSON>", "retrogaming": "retrojokoak", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "jola<PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "injustizia2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "zeruajokoa", "zenlife": "zenlife", "beatmaniaiidx": "beatmaniaiidx", "steep": "aldapatsua", "mystgames": "<PERSON><PERSON><PERSON><PERSON>ua<PERSON>", "blockchaingaming": "blokekateajokoak", "medievil": "<PERSON><PERSON><PERSON>", "consolegaming": "kontsolajokoak", "konsolen": "konsolen", "outrun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "beldurrezkojokoetan", "monstergirlquest": "monsterneskaquest", "supergiant": "<PERSON><PERSON><PERSON><PERSON>", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "zaharkituakbainajokoak", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "fikziointeraktiboa", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "ikusizkoeleberri", "visualnovels": "ikusizkoeleberriak", "rgg": "rgg", "shadowolf": "o<PERSON><PERSON><PERSON>", "tcrghost": "tcrmamua", "payday": "nomineguna", "chatherine": "txatherine", "twilightprincess": "il<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "honda<PERSON><PERSON>", "aestheticgames": "j<PERSON>este<PERSON><PERSON>ak", "novelavisual": "ikusizkoeleberria", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "damut", "godhand": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON>", "starrail": "starrail", "keyblade": "keyblade", "aplaguetale": "i<PERSON><PERSON><PERSON><PERSON>", "fnafsometimes": "fnafsometimes", "novelasvisuales": "ikusnobelak", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videojuejos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamedates": "bideojokohitzorduak", "mycandylove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulk<PERSON><PERSON><PERSON>", "batmangames": "batmanpartidak", "returnofreckoning": "itzuleraapaizgarria", "gamstergaming": "gamstergaming", "dayofthetantacle": "tentakulueneguna", "maniacmansion": "eromenetxea", "crashracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "3dplataformak", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "jokoklasikoak", "hellblade": "hellblade", "storygames": "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameuse": "j<PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "offmortisghost", "tinybunny": "t<PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "indartu", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "abenturagrafiko", "quickflash": "<PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "g<PERSON><PERSON><PERSON><PERSON>", "retroarcades": "retroarkadeak", "f123": "f123", "wasteland": "basamor<PERSON>", "powerwashsim": "presiogarbiketa", "coralisland": "k<PERSON>uh<PERSON>a", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON>", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "futbolfusioa", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "metalbirrindua", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "lotsapila", "simulator": "simulag<PERSON>u", "symulatory": "symulatory", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobotgerra", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "be<PERSON><PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "muti<PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "toontownberridatzia", "simracing": "simracing", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "gorputzzerukoak", "seum": "seum", "partyvideogames": "festakobedeojokoak", "graveyardkeeper": "hi<PERSON><PERSON><PERSON>_<PERSON><PERSON>i", "spaceflightsimulator": "espaziohegazkinensimulatzailea", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonworlds": "horizonworlds", "handygame": "<PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "kondairaketabideojo<PERSON>ak", "oldschoolvideogames": "bid<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racingsimulator": "lasterketa_simulagailua", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "abestipop", "famitsu": "famitsu", "gatesofolympus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunternow": "monsterhunternow", "rebelstar": "izarrebeldea", "indievideogaming": "bideojokoindependenteak", "indiegaming": "indiejokoak", "indievideogames": "bideojokoindependenteak", "indievideogame": "bideojokoindependentea", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomne", "bufffortress": "buffgaztelu", "unbeatable": "g<PERSON><PERSON><PERSON><PERSON>", "projectl": "projectl", "futureclubgames": "etorkizunekoklubjolasen", "mugman": "ka<PERSON>utxo", "insomniacgames": "<PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "zirrikituzientzia", "backlog": "atzerapenak", "gamebacklog": "jokozerrenda", "gamingbacklog": "jokozerrenda", "personnagejeuxvidéos": "bideojokopertsonaia", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "<PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "txakurgaiz<PERSON>", "beastlord": "piztienjauna", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "dopaminabiltegia", "staxel": "staxel", "videogameost": "bideojokoost", "dragonsync": "dragoiasinkronia", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ilovekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "iniziald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animutrist<PERSON>", "darkerthanblack": "beltzabaino_beltzago", "animescaling": "animeneurriak", "animewithplot": "animekontrama", "pesci": "pesci", "retroanime": "retroanime", "animes": "animeak", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "80koanimeak", "90sanime": "90ekoanimeak", "darklord": "ilunjauna", "popeetheperformer": "<PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "udanime", "2000sanime": "2000koanimeak", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonelehenlehenengia", "rapanime": "rapanime", "chargemanken": "kargatzailemanken", "animecover": "animeargazkia", "thevisionofescaflowne": "escaflownerenikuspena", "slayers": "hiltzaileak", "tokyomajin": "tokyomajin", "anime90s": "anime90ak", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "platanoarraina", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "komunekoloturahanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "<PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "etor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fairytail": "i<PERSON>in<PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "parasitoa", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "sirenatxomelodia", "kamisamakiss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "<PERSON><PERSON><PERSON><PERSON><PERSON>man<PERSON>", "romancemangas": "mangaromantikoak", "karneval": "inauteriak", "dragonmaid": "dragoizerbitzar<PERSON>", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "genioinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "zenbaitzuentzatmagikoaurkibidea", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monsterneska", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "kiro<PERSON>me", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "tanyagaiztoarensaga", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "mutilaeta<PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "iparraldearrenkozaztaparrada", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "j<PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "ilargibeteanbilatzen", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "politaeta<PERSON><PERSON><PERSON><PERSON>", "martialpeak": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "puntuazioaltuaneskamutila", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerobidarketa", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "munstroneska", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "korrikalaria", "oldanime": "<PERSON><PERSON><PERSON><PERSON>", "chainsawman": "motoserragizona", "bungoustraydogs": "bungoustraydogs", "jogo": "joko", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "belduranimea", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalibre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "maitasundidirekt", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstermanga": "<PERSON><PERSON><PERSON><PERSON>", "yourlieinapril": "zuregezurraapirilan", "buggytheclown": "<PERSON>gy<PERSON><PERSON><PERSON>", "bokunohero": "bokunohero", "seraphoftheend": "azkenekoserafin<PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "itsasosakonekopreso", "jojolion": "jojo<PERSON>", "deadmanwonderland": "deadmanwonderland", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "senarragaia", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "j<PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "debil<PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "zur<PERSON><PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "<PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "aliantzasekretua", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "ezabatuta", "bluelock": "bluelock", "goblinslayer": "iratxiehilt<PERSON>lea", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugi": "mugi", "blueexorcist": "exorzistaurdina", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "<PERSON><PERSON><PERSON>", "spyfamily": "spyfamily", "airgear": "airgear", "magicalgirl": "neskamagikoa", "thesevendeadlysins": "zazpibekatukapitalak", "prisonschool": "kartzelaeskola", "thegodofhighschool": "institutukojainko", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animebertso", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saolaburtu", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "hilezinhilzori", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animemaitasuna", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lolikon", "demonslayertothesword": "demonslayer<PERSON><PERSON><PERSON><PERSON>", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON>", "firepunch": "suukabilkada", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "izarraklerrotzen", "romanceanime": "animerromantikoa", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "maitasunmagia", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "recordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "institutuahilik", "germantechno": "technoalemaniarra", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "tennisprintzea", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "hiltzaileikasgela", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "herio_parada", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejaponiar", "animespace": "animelekua", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "itxaropen<PERSON>li", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "<PERSON><PERSON><PERSON><PERSON>", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animezientziafikzioa", "ratman": "arratoigizona", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gashbell": "gashbell", "peachgirl": "mertxikaneska", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "mangabihot<PERSON>", "deliciousindungeon": "gozatuzimorramaztegian", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "ragnarokerregistroa", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "skiptoloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "gainhornituta", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "sorginentxapelatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "mangabizimoduda", "dropsofgod": "jainkoarentantak", "loscaballerosdelzodia": "zaldunakzodiakoarenak", "animeshojo": "animeneska", "reverseharem": "haremalderantzikatu", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "irakaslehandiaoni<PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "nagusia<PERSON>", "gear5": "gear5", "grandbluedreaming": "itsasurdinhandiaenametsetan", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodc": "odolc", "talesofdemonsandgods": "deabruetajainkoenistorioak", "goreanime": "goreanime", "animegirls": "animeneskak", "sharingan": "<PERSON><PERSON>", "crowsxworst": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "splatteranime": "splatteranime", "splatter": "zapart", "risingoftheshieldhero": "tatenoguardariaren_altxatzea", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animeespainia", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "baleakumeak", "liarliar": "gezurtigezurti", "supercampeones": "supertxapeldunak", "animeidols": "animeidoloak", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "egunerdeakberde", "magicalgirls": "neska_magikoak", "callofthenight": "gau<PERSON>nde<PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "itzallorategia", "tsubasachronicle": "tsubasachronicle", "findermanga": "mangabilatzailea", "princessjellyfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekiss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animebertso", "persocoms": "persocoms", "omniscientreadersview": "irakurleguztijakilearenikuspuntua", "animecat": "animekatu", "animerecommendations": "animegomendio", "openinganime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "nerenirabilekomediaerromatikoa", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundam<PERSON>", "voltesv": "voltesv", "giantrobots": "errobot<PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "meka", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "animehandi", "bleach": "lejia", "deathnote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "militarranime", "greenranger": "greenranger", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animehiria", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonabentura", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonslayer", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "mynheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "ikuskaritzataldea", "onepieceanime": "onepieceanime", "attaquedestitans": "erasotatitanak", "theonepieceisreal": "one<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "revengers": "mendekatzaileak", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "joyboyefek<PERSON>a", "digimonstory": "digimonistorioa", "digimontamers": "digimontamers", "superjail": "superkartzela", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonperfektua", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "sorginhegalaria", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "z<PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "ka<PERSON>uztiensantuak", "recuentosdelavida": "bizitzakokontuak"}