{"2048": "2048", "mbti": "mbti", "enneagram": "enneagramma", "astrology": "astrulugia", "cognitivefunctions": "funzioniognitive", "psychology": "psiculugia", "philosophy": "filosofia", "history": "storia", "physics": "fisi<PERSON>", "science": "scienza", "culture": "cultura", "languages": "lingue", "technology": "tecnulugia", "memes": "meme", "mbtimemes": "mbtimemes", "astrologymemes": "astrologiam<PERSON><PERSON>", "enneagrammemes": "me<PERSON><PERSON><PERSON><PERSON><PERSON>", "showerthoughts": "pen<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "divertente", "videos": "video", "gadgets": "gadgets", "politics": "pulitica", "relationshipadvice": "cunsigliinamore", "lifeadvice": "cunsiglidia<PERSON>ta", "crypto": "cripto", "news": "nutizie", "worldnews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "archeologia", "learning": "amparà", "debates": "<PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "teoriedicuspirazioni", "universe": "universu", "meditation": "meditazione", "mythology": "mitulugia", "art": "arte", "crafts": "artizanatu", "dance": "ballu", "design": "design", "makeup": "truccu", "beauty": "bellezza", "fashion": "moda", "singing": "cantà", "writing": "scrivitura", "photography": "fotografia", "cosplay": "cosplay", "painting": "pittura", "drawing": "disegnu", "books": "libri", "movies": "filmi", "poetry": "puesia", "television": "televisione", "filmmaking": "faresifilmi", "animation": "animazione", "anime": "anime", "scifi": "scifi", "fantasy": "fantasia", "documentaries": "documentari", "mystery": "misteru", "comedy": "cumedia", "crime": "crimine", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "orrore", "romance": "rumanza", "realitytv": "telerealità", "action": "azzione", "music": "musica", "blues": "blues", "classical": "classicu", "country": "paese", "desi": "desi", "edm": "edm", "electronic": "elettronicu", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "casa", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metalu", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "<PERSON>ghj<PERSON>", "concerts": "cuncertu", "festivals": "festivali", "museums": "musei", "standup": "<PERSON><PERSON><PERSON>", "theater": "teatru", "outdoors": "aria_libera", "gardening": "giardinaghju", "partying": "festaghja", "gaming": "<PERSON><PERSON><PERSON><PERSON>", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "dungeonsanddragons", "chess": "scacchi", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "alimentazione", "baking": "infurnata", "cooking": "cucinà", "vegetarian": "vegetarianu", "vegan": "veganu", "birds": "<PERSON><PERSON><PERSON>", "cats": "gatti", "dogs": "cani", "fish": "pesciu", "animals": "animali", "blacklivesmatter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "environmentalism": "ambientalismu", "feminism": "<PERSON>u", "humanrights": "<PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "alleatulgbtq", "stopasianhate": "stopàlodiuasiàticu", "transally": "alleatutrans", "volunteering": "vuluntariatu", "sports": "sport", "badminton": "badminton", "baseball": "baseball", "basketball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boxing": "boxe", "cricket": "cricket", "cycling": "ciclisim<PERSON>", "fitness": "fitness", "football": "ball<PERSON>", "golf": "golf", "gym": "palestra", "gymnastics": "ginnastica", "hockey": "hockey", "martialarts": "art<PERSON>rz<PERSON><PERSON>", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "corsa", "skateboarding": "skateboard", "skiing": "sciata", "snowboarding": "snowboard", "surfing": "surf", "swimming": "natazione", "tennis": "tennis", "volleyball": "pallavolu", "weightlifting": "pesi", "yoga": "yoga", "scubadiving": "immersione", "hiking": "caminata", "capricorn": "<PERSON><PERSON><PERSON><PERSON>", "aquarius": "a<PERSON><PERSON><PERSON><PERSON>", "pisces": "pesci", "aries": "ariete", "taurus": "toru", "gemini": "gemini", "cancer": "cancru", "leo": "leo", "virgo": "vergine", "libra": "bilancia", "scorpio": "scorpione", "sagittarius": "sagittariu", "shortterm": "curtutermini", "casual": "casuale", "longtermrelationship": "rilazionilonghi", "single": "solu", "polyamory": "poliamoria", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "<PERSON><PERSON><PERSON>", "bisexual": "bisessuale", "pansexual": "pansessuale", "asexual": "asessuale", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "canidig<PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "<PERSON><PERSON><PERSON><PERSON>", "soulreaver": "animarubatore", "suikoden": "su<PERSON><PERSON>", "subverse": "sottoversu", "legendofspyro": "leggen<PERSON><PERSON>pyro", "rouguelikes": "rouguelikes", "syberia": "siberia", "rdr2": "rdr2", "spyrothedragon": "spyroildrago", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "tramontuinaghjiru", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "<PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "eroidida<PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "animacomu", "dungeoncrawling": "esploràdungeon", "jetsetradio": "jetsetradio", "tribesofmidgard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "sce<PERSON><PERSON><PERSON><PERSON>", "lordsoftherealm2": "signoridelregnu2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "culurìvuru", "medabots": "medabots", "lodsoftherealm2": "carchidighjocudiuregnu2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "ghjocodirolu", "witcher": "stregone", "dishonored": "disonoratu", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "crea<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "immersiva", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyvechjiascola", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivazionemorbida", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "<PERSON><PERSON><PERSON><PERSON>", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaduttempu", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimensione20", "gaslands": "terredicarburante", "pathfinder": "esploratore", "pathfinder2ndedition": "pathfinder2aedizione", "shadowrun": "corsashadow", "bloodontheclocktower": "sanguenantàatorra", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "scappataversulagravità", "rpg": "gdr", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "uncolpu", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "signore", "yourturntodie": "atoccaàtèdimorì", "persona3": "persona3", "rpghorror": "orroreroleplay", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "<PERSON>ra<PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "testurpg", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "animedimoni", "mu": "mu", "falloutshelter": "rifugiuantiatomicu", "gurps": "gurps", "darkestdungeon": "dunjonupiùscuru", "eclipsephase": "fasideclisse", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "ligaturadisaia", "diabloimmortal": "<PERSON>av<PERSON><PERSON><PERSON><PERSON><PERSON>", "dynastywarriors": "guerrieridid<PERSON><PERSON>", "skullgirls": "skullgirls", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "ereditàdihogwarts", "madnesscombat": "cumbattimentupazzu", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "strada96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelike", "gothamknights": "ca<PERSON><PERSON><PERSON><PERSON><PERSON>", "forgottenrealms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "adivisione2", "lineage2": "lineage2", "digimonworld": "mon<PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "alleva<PERSON><PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xenu", "vulcanverse": "vulcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "orizzonteproibituwest", "twewy": "twewy", "shadowpunk": "ombraribelle", "finalfantasyxv": "finalfantasyxv", "everoasis": "oasisempremai", "hogwartmystery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "ful<PERSON><PERSON>", "lastepoch": "ultimaepoca", "starfinder": "scopritored<PERSON><PERSON>", "goldensun": "<PERSON><PERSON><PERSON>", "divinityoriginalsin": "divinitàpeccatuoriginale", "bladesinthedark": "lame<PERSON><PERSON><PERSON><PERSON>", "twilight2000": "crepusculu2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkrossu", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "ordinecolatu", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "terremaligne", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "survi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "divinità", "pf2": "pf2", "farmrpg": "rpgagriculu", "oldworldblues": "malincunia<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventurequest": "questaavventura", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON>oll<PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON>oll<PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "solehaven", "talesofsymphonia": "storiedisymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "umiofarog", "sacredunderworld": "sac<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainedechoes": "echiincatena", "darksoul": "animascura", "soulslikes": "ghjochist<PERSON><PERSON><PERSON><PERSON>", "othercide": "<PERSON><PERSON><PERSON><PERSON>", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "cronotrigger", "pillarsofeternity": "pilastridieternità", "palladiumrpg": "palladiumrpg", "rifts": "s<PERSON>i", "tibia": "tibia", "thedivision": "adivisione", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "leggendadidragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampir<PERSON><PERSON><PERSON><PERSON>", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "littlewoodcorsu", "childrenofmorta": "figliuli<PERSON><PERSON><PERSON>", "engineheart": "coredimotore", "fable3": "fable3", "fablethelostchapter": "fabulaicapitulupers<PERSON>", "hiveswap": "scambiàdialveare", "rollenspiel": "gh<PERSON><PERSON>_di_rolu", "harpg": "harpg", "baldursgates": "portedibaldur", "edeneternal": "edenueternu", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "campustellare", "oldschoolrevival": "scolavechjarivolta", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "regnicuore1", "ff9": "ff9", "kingdomheart2": "regnicorè2", "darknessdungeon": "zinòfundarasgura", "juegosrpg": "ghjochirpg", "kingdomhearts": "regnodicori", "kingdomheart3": "regnodicuore3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "vendemmia", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "bastione", "drakarochdemoner": "drak<PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "cielidarcadia", "shadowhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "suffludifuoco4", "mother3": "mama3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "altr<PERSON><PERSON>", "roleplaygames": "ghjochidighjucà<PERSON>i", "roleplaygame": "ghjo<PERSON>dirolu", "fabulaultima": "fabulault<PERSON>", "witchsheart": "coredistreia", "harrypottergame": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vamp<PERSON>lma<PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "incantaghjulanu", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "cacciaroyale", "albertodyssey": "albertodissea", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "animaspada", "baldursgate3": "baldursgate3", "kingdomcome": "regn<PERSON><PERSON><PERSON><PERSON>", "awplanet": "pian<PERSON><PERSON>", "theworldendswithyou": "umondufiniscecuntè", "dragalialost": "dragalialost", "elderscroll": "anticulinarii", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "eresiaoscura", "shoptitans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "terramagia", "blackbook": "<PERSON><PERSON><PERSON>", "skychildrenoflight": "figliolidiceludialluce", "gryrpg": "gryrpg", "sacredgoldedition": "edizioneorusalcratu", "castlecrashers": "sfunda<PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "gh<PERSON><PERSON>_goticu", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "fantasimadifilitokyo", "fallout2d20": "fallout2d20", "gamingrpg": "ghjochirpg", "prophunt": "cacci<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starrails": "stelleferroviarie", "cityofmist": "cittàdinebbia", "indierpg": "grpindie", "pointandclick": "cliccaepunta", "emilyisawaytoo": "em<PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "indivisibile", "freeside": "laturiber<PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "stradadiamorteversuucanada", "palladium": "palladi<PERSON>", "knightjdr": "cavalier<PERSON><PERSON>", "monsterhunter": "cacci<PERSON><PERSON><PERSON><PERSON>", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geusup<PERSON>azi<PERSON>", "persona5": "persona5", "ghostoftsushima": "fantas<PERSON><PERSON><PERSON><PERSON>", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nier": "nier", "dothack": "fàlhack", "ys": "ys", "souleater": "mangiacori", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "rpgtatticu", "mahoyo": "mahoyo", "animegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "sonataeternu", "princessconnect": "principessaconnect", "hexenzirkel": "hexenzirkel", "cristales": "crist<PERSON><PERSON>", "vcs": "vcs", "pes": "pes", "pocketsage": "saviuditasca", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valo<PERSON><PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "ghjochielettrònichi", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligadisunniadori", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "ghju<PERSON>à", "overwatchleague": "ligaoverwatch", "cybersport": "cybersport", "crazyraccoon": "pazzufuretu", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "sguassà", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantcumpetitivu", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "mezzavita", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "valvula", "portal": "portale", "teamfortress2": "teamfortress2", "everlastingsummer": "stateeterna", "goatsimulator": "simulatoredicapre", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transformice": "transformice", "justshapesandbeats": "soliformeèritmi", "battlefield4": "battlefield4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "tagliaetrinza", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riscudipioghja2", "metroidvanias": "metroidvania", "overcooked": "troppu_cottu", "interplanetary": "interplanetariu", "helltaker": "helltaker", "inscryption": "inscrizzione", "7d2d": "7g2g", "deadcells": "cellulemorte", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "foxhole": "trincea", "stray": "vagabondu", "battlefield": "campudibattaglia", "battlefield1": "campudibattaglia1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eyeb": "ochji", "blackdesert": "<PERSON><PERSON><PERSON>", "tabletopsimulator": "simulatorediscrivania", "partyhard": "festaàtomba", "hardspaceshipbreaker": "spezzanavispaceduru", "hades": "hades", "gunsmith": "armaghju", "okami": "<PERSON>ami", "trappedwithjester": "intrappulatucughjester", "dinkum": "veru", "predecessor": "antecessore", "rainworld": "mondupiuviale", "cavesofqud": "grottediqud", "colonysim": "simulatoreculunia", "noita": "noita", "dawnofwar": "albadiguerra", "minionmasters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grimdawn": "grimdawn", "darkanddarker": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "animadilavuratore", "datingsims": "sims<PERSON><PERSON>", "yaga": "yaga", "cubeescape": "fugadacubu", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "cittànova", "citiesskylines": "orizzontic<PERSON>din<PERSON>", "defconheavy": "defconpesante", "kenopsia": "chenopsia", "virtualkenopsia": "kenopsiavirtuale", "snowrunner": "corsastradaneve", "libraryofruina": "bibliotecadiruina", "l4d2": "l4d2", "thenonarygames": "ighjochisenzagenere", "omegastrikers": "omegastrikers", "wayfinder": "stradai<PERSON>u", "kenabridgeofspirits": "kena<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placidplasticduck": "paperaplacidudiplasdica", "battlebit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ultimatechickenhorse": "cavalludigallinaultim<PERSON>", "dialtown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "surrìdipermè", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermeatboy", "tinnybunny": "tinnyconigliolu", "cozygrove": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "sventura", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "cunfini", "pubg": "pubg", "callofdutyzombies": "callofdutyzombie", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "gridu", "farcrygames": "ghjochifarcrygames", "paladins": "pala<PERSON><PERSON>", "earthdefenseforce": "forzadidifesadellaterra", "huntshowdown": "cacciamostra", "ghostrecon": "ricunnis<PERSON><PERSON>uf<PERSON><PERSON><PERSON>", "grandtheftauto5": "grandtheftauto5", "warz": "warre", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ammazzàtotale", "joinsquad": "uniscitivialasquadra", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "insurgencysandstormu", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "mortestranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombiezombie", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "divisioni2", "killzone": "zonadimorte", "helghan": "hel<PERSON>", "coldwarzombies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "assudicombattu", "crosscode": "codice<PERSON><PERSON><PERSON><PERSON>", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "cecchinutirelite", "modernwarfare": "guerramoderna", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "borderlands", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "carnageprimitivu", "worldofwarships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "back4blood": "tornu4sangue", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "sicario", "masseffect": "masseffect", "systemshock": "scossadisistema", "valkyriachronicles": "valkyriachronicles", "specopstheline": "liniaspecops", "killingfloor2": "killingfloor2", "cavestory": "storiadegrotte", "doometernal": "doom<PERSON>nu", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mwo": "mwo", "division2": "divisione2", "tythetasmaniantiger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generationzero": "generazionezeró", "enterthegungeon": "entratein_amazmora", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "omusalsicciaghju", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "udolorefantasima", "warface": "faccia<PERSON><PERSON><PERSON>", "crossfire": "fucuc<PERSON><PERSON><PERSON>", "atomicheart": "<PERSON><PERSON><PERSON><PERSON>", "blackops3": "blackops3", "vampiresurvivors": "vampirisopravviventi", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "libertàtutale", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "frag", "tinytina": "<PERSON><PERSON>", "gamepubg": "ghjocupubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "ghjochisparainprimapersona", "convertstrike": "scioperodiconversione", "warzone2": "warzone2", "shatterline": "linia<PERSON><PERSON>", "blackopszombies": "zombiesblackops", "bloodymess": "sanguinariu", "republiccommando": "comandanterepubblicanu", "elitedangerous": "elitedangerous", "soldat": "soldatu", "groundbranch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "squad": "squadra", "destiny1": "destinu1", "gamingfps": "ghjocufps", "redfall": "casca<PERSON>ussa", "pubggirl": "pubggirl", "worldoftanksblitz": "munduditan<PERSON>b<PERSON>", "callofdutyblackops": "callofdutyblackops", "enlisted": "a<PERSON><PERSON><PERSON><PERSON>", "farlight": "luc<PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armuracorsu", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "pagamentu2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgu<PERSON>rain<PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgmania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "merl<PERSON><PERSON><PERSON><PERSON>ne", "ghostcod": "fantosmadibaccalà", "csplay": "csplay", "unrealtournament": "torneusurreale", "callofdutydmz": "callofdutydmz", "gamingcodm": "ghjocodm", "borderlands2": "borderlands2", "counterstrike": "<PERSON><PERSON><PERSON>", "cs2": "cs2", "pistolwhip": "pistulettag<PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "campionidiscossa", "halo3": "halo3", "halo": "aureola", "killingfloor": "pianumacellazione", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "neonebianca", "remnant": "residuu", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "<PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "omudilombra", "quake2": "terramotu2", "microvolts": "microvolti", "reddead": "morturossu", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON>", "rust": "rust", "conqueronline": "cunquistàonline", "dauntless": "intrepid<PERSON>", "warships": "navidig<PERSON>", "dayofdragons": "ghjo<PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "volucrescente", "recroom": "salladiricreazione", "legendsofruneterra": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "misteru", "phantasystaronline2": "phantasystaronline2", "maidenless": "se<PERSON>don<PERSON>", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "<PERSON><PERSON>", "agario": "agario", "secondlife": "secondavi<PERSON>", "aion": "aion", "toweroffantasy": "torreufantasia", "netplay": "ghjo<PERSON>_in_rete", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "cavalier<PERSON><PERSON>a", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "uligamediisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "feccia", "newworld": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "multiplayer", "pirate101": "pirata101", "honorofkings": "onored<PERSON><PERSON>è", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "cittàdic<PERSON><PERSON><PERSON>", "3dchat": "chat3d", "nostale": "nostale", "tauriwow": "toruwow", "wowclassic": "wowclassic", "worldofwarcraft": "mondodiwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "cenneredicreazione", "riotmmo": "riotmmo", "silkroad": "stradadelaseta", "spiralknights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mulegend": "mulegenda", "startrekonline": "startrekonline", "vindictus": "vendetta", "albiononline": "albiononline", "bladeandsoul": "anima<PERSON><PERSON>a", "evony": "evony", "dragonsprophet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multighjucatore", "angelsonline": "angh<PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseenlinia", "growtopia": "growtopia", "starwarsoldrepublic": "guerrastelle_vechja_republica", "grandfantasia": "granfantasia", "blueprotocol": "protocolluturchinu", "perfectworld": "monduperfettu", "riseonline": "riseincorsica", "corepunk": "corepunk", "adventurequestworlds": "avventuredumundovirtuale", "flyforfun": "vulàpàdivertimentu", "animaljam": "animaljam", "kingdomofloathing": "regno<PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "cittàdieroi", "mortalkombat": "mortalkombat", "streetfighter": "cumbattentedistrada", "hollowknight": "caval<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversu", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "cumbattentevirtuale", "streetsofrage": "stradedifuria", "mkdeadlyalliance": "mkalianzamurtale", "nomoreheroes": "eroifiniti", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "cumundragu", "retrofightinggames": "ghjochidicombattimenturetro", "blasphemous": "blasfemu", "rivalsofaether": "rivalidicue<PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supercazzottu", "mugen": "mugen", "warofthemonsters": "g<PERSON><PERSON><PERSON><PERSON>", "jogosdeluta": "ghjochidicombattimentu", "cyberbots": "cyberbot", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "battagliafinale", "poweredgear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "<PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "ghjochidicumbattimentu", "killerinstinct": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoffigthers": "rèdiluttatori", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "chivalria2", "demonssouls": "animedidiavuli", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "seguituhollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "silksongnews": "nutiziedisilksong", "silksong": "silksong", "undernight": "sottuan<PERSON>", "typelumina": "tipulum<PERSON>", "evolutiontournament": "evolutiontorneiu", "evomoment": "e<PERSON><PERSON><PERSON><PERSON>", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "storiediberseria", "bloodborne": "bloodborne", "horizon": "<PERSON><PERSON><PERSON><PERSON>", "pathofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodborne", "uncharted": "inesploratu", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "ultimudino", "infamous": "famosu", "playstationbuddies": "amicidiplaystation", "ps1": "ps1", "oddworld": "mondustranu", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbi<PERSON>", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "cumpagniaribell<PERSON>", "aisomniumfiles": "archivid<PERSON><PERSON>nium<PERSON>", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "te<PERSON>u", "detroitbecomehuman": "detroitdiventaumanu", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "finoàlalba", "touristtrophy": "tro<PERSON><PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "ombradelcolossu", "crashteamracing": "corsateamracing", "fivepd": "cinquepd", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "ghjucàstazione", "samuraiwarriors": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "<PERSON><PERSON><PERSON><PERSON>", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "cacciaallomu", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "cori2upattudilumbra", "pcsx2": "pcsx2", "lastguardian": "ulti<PERSON><PERSON><PERSON>u", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "festa<PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "camp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "psiconauti", "mhw": "mhw", "princeofpersia": "princi<PERSON><PERSON><PERSON>", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "fronte", "dontstarvetogether": "ùnmuriteinseme", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "<PERSON><PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "riparatorecase", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligadiregni", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "telebasura", "skycotl": "cel<PERSON><PERSON><PERSON>", "erica": "erica", "ancestory": "antenati", "cuphead": "cuphead", "littlemisfortune": "misssfortunasgiuvana", "sallyface": "facciudisally", "franbow": "franbow", "monsterprom": "monst<PERSON><PERSON><PERSON>", "projectzomboid": "prughjettozomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "salvaggiuniesterni", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "cultudilagnellu", "duckgame": "ghjoculanatra", "thestanleyparable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerunite": "torreunite", "occulto": "ocultu", "longdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "satisfactory": "sodd<PERSON><PERSON><PERSON>", "pluviophile": "amicudiapioggia", "underearth": "sott<PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "spiritfarer", "darkdome": "cupolaoscura", "pizzatower": "torredipizza", "indiegame": "ghjocu_indipendente", "itchio": "itchio", "golfit": "golfit", "truthordare": "verituosfida", "game": "gh<PERSON>vellu", "rockpaperscissors": "petracartaforbice", "trampoline": "trampolinu", "hulahoop": "cer<PERSON><PERSON>", "dare": "sfida", "scavengerhunt": "cacciaàutesoru", "yardgames": "ghjochidicorte", "pickanumber": "scegli<PERSON><PERSON><PERSON>", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "bierapong", "dicegoblin": "foll<PERSON><PERSON><PERSON><PERSON>", "cosygames": "g<PERSON><PERSON><PERSON><PERSON><PERSON>enti", "datinggames": "ghjochididatazione", "freegame": "ghjoculiberu", "drinkinggames": "ghjochiàbeie", "sodoku": "sudoku", "juegos": "ghjo<PERSON><PERSON><PERSON><PERSON>", "mahjong": "mahjong", "jeux": "<PERSON><PERSON><PERSON><PERSON>", "simulationgames": "giochidisimulazione", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "juegosdepalab<PERSON>", "letsplayagame": "ghjuchemuinseme", "boredgames": "ghjochipesanti", "oyun": "oyun", "interactivegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "cuncorsid<PERSON>issà", "spiele": "<PERSON><PERSON><PERSON><PERSON>", "giochi": "<PERSON><PERSON><PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "ghjochiiphone", "boogames": "boogh<PERSON><PERSON>", "cranegame": "giocudigru", "hideandseek": "cacci<PERSON><PERSON><PERSON>", "hopscotch": "campanellu", "arcadegames": "ghjochidarcade", "yakuzagames": "ghjochiakuza", "classicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindgames": "g<PERSON><PERSON><PERSON>mentali", "guessthelyric": "induvina<PERSON><PERSON><PERSON>", "galagames": "ghjo<PERSON>digala", "romancegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "ghjo<PERSON>yanderegames", "tonguetwisters": "scioglilinguacorsichi", "4xgames": "ghjochistrategichi", "gamefi": "gamefi", "jeuxdarcades": "ghjochiarcade", "tabletopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "ghjochi90", "idareyou": "tisfidu", "mozaa": "mozaa", "fumitouedagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "ghjochidicorsa", "ets2": "ets2", "realvsfake": "verucontrafalsu", "playgames": "ghju<PERSON>à", "gameonline": "ghjoconline", "onlinegames": "g<PERSON><PERSON><PERSON><PERSON><PERSON>a", "jogosonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "roleplays<PERSON><PERSON><PERSON>", "playaballgame": "ghjucàunapartita", "pictionary": "pictionary", "coopgames": "giochicoop", "jenga": "jenga", "wiigames": "g<PERSON><PERSON><PERSON>wiì", "highscore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderôles": "ghjochidiculu", "burgergames": "ghjochiburger", "kidsgames": "ghjochidichjuchi", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwedizionenera", "jeuconcour": "ghjo<PERSON>_cuncorsu", "tcgplayer": "tcgplayer", "juegodepreguntas": "gh<PERSON><PERSON>_di_dumande", "gioco": "gioco", "managementgame": "ghjocudigestione", "hiddenobjectgame": "ghjo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "ghjocu_formula1", "citybuilder": "custruttoredicità", "drdriving": "drdri<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "ghjochiarcade", "memorygames": "ghjochidicortezza", "vulkan": "vulkan", "actiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blowgames": "ghjochiasuffiatu", "pinballmachines": "machineàflippers", "oldgames": "ghjochivecchji", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "gameo", "lasergame": "gh<PERSON><PERSON><PERSON>", "imessagegames": "ghjochiimessage", "idlegames": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "riempiteus<PERSON><PERSON><PERSON>", "jeuxpc": "ghjochipc", "rétrogaming": "retrogaming", "logicgames": "giochilogichi", "japangame": "ghjo<PERSON>lughjapunesu", "rizzupgame": "mi<PERSON><PERSON>àughjocuseduziò", "subwaysurf": "surfumetru", "jeuxdecelebrite": "ghjochidicelebrità", "exitgames": "ghjo<PERSON><PERSON><PERSON><PERSON>", "5vs5": "5contrà5", "rolgame": "ghjo<PERSON>ludirolu", "dashiegames": "dashiegames", "gameandkill": "ghjucàecachjà", "traditionalgames": "ghjochiditradizione", "kniffel": "kniffel", "gamefps": "gamefps", "textbasedgames": "ghjochiditestuale", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fan<PERSON><PERSON><PERSON><PERSON>", "retrospel": "retrospellu", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "ghjochidigiardinellu", "fliperama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heroclix": "heroclix", "tablesoccer": "calciubalilla", "tischfußball": "tischfußball", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "ghjochiforumu", "casualgames": "ghjochicasuali", "fléchettes": "freccette", "escapegames": "ghjochidicaccia", "thiefgameseries": "ghjoculadridicorsica", "cranegames": "ghjochidigrua", "játék": "<PERSON><PERSON><PERSON><PERSON>", "bordfodbold": "bordfodbold", "jogosorte": "jogosorte", "mage": "magu", "cargames": "ghjochidivetture", "onlineplay": "ghjucàonline", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "borsabingos", "randomizer": "aleatorizatore", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "ghjochidipc", "socialdeductiongames": "ghjochidededuzzionesuciale", "dominos": "domino", "domino": "domino", "isometricgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "veritàèsfida", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "caccie<PERSON><PERSON><PERSON>", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "ghje<PERSON><PERSON><PERSON><PERSON>", "free2play": "grat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "gh<PERSON><PERSON>_fantasiu", "gryonline": "gry<PERSON><PERSON>a", "driftgame": "<PERSON><PERSON><PERSON><PERSON>", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "haloserietvèghjo<PERSON>", "mushroomoasis": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "anythingwithanengine": "qualcosacùunmotore", "everywheregame": "gh<PERSON><PERSON>_in_ogni_locu", "swordandsorcery": "spadaèmagia", "goodgamegiving": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "j<PERSON><PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "g<PERSON><PERSON><PERSON><PERSON>gh<PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "<PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "amores<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "mod<PERSON><PERSON><PERSON>", "crimegames": "ghjochidicrimine", "dobbelspellen": "ghjochidicarte", "spelletjes": "<PERSON><PERSON><PERSON><PERSON>", "spacenerf": "nerfspaziale", "charades": "scia<PERSON>", "singleplayer": "sing<PERSON>u", "coopgame": "chjocuincoop", "gamed": "ghjucatu", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "g<PERSON><PERSON><PERSON><PERSON>aest<PERSON>", "kingdiscord": "rèdiscord", "scrabble": "scrabble", "schach": "scacchi", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "trictrac", "onitama": "onitama", "pandemiclegacy": "pandemialegatu", "camelup": "camelup", "monopolygame": "monopolyghjuculà", "brettspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "gh<PERSON><PERSON>_di_tavulinu", "sällskapspel": "ghjochidisucietà", "planszowe": "ghjochidisocietà", "risiko": "r<PERSON>cu", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombicidi<PERSON>", "tabletop": "tavolamesa", "baduk": "baduk", "bloodbowl": "bloodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectfour": "quattruinlinia", "heroquest": "questaeroica", "giochidatavolo": "gio<PERSON><PERSON><PERSON><PERSON>", "farkle": "farkle", "carrom": "carambolu", "tablegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegames": "ghjochididadi", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "ghjoculudipiastra", "jocuridesocietate": "ghjochidicucità", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alfarius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "scontr<PERSON><PERSON><PERSON><PERSON>", "creationludique": "creaziuneludica", "tabletoproleplay": "ghjo<PERSON>dirolu", "cardboardgames": "ghjochidicartone", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "ghjochicommutatore", "infinitythegame": "infinitythegame", "kingdomdeath": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "serpentieiscale", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "g<PERSON><PERSON><PERSON><PERSON>data<PERSON><PERSON>", "planszówki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rednecklife": "vitacampagnola", "boardom": "noia", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "imperocrepusculariu", "horseopoly": "cavallopoly", "deckbuilding": "custruires<PERSON>ù", "mansionsofmadness": "casedi<PERSON>zzia", "gomoku": "gomoku", "giochidatavola": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowsofbrimstone": "ombredibrimstone", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcaby": "warcaby", "táblajátékok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "cuirassatu", "tickettoride": "bigliettuversuaventura", "deskovehry": "ghjochidiscrivania", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "j<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "ghjochidicartulare", "gesellschaftsspiele": "ghjochidicucietà", "starwarslegion": "starwarslegion", "gochess": "<PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "ghjochidicucina", "terraria": "terraria", "dsmp": "dsmp", "warzone": "zonadiguerra", "arksurvivalevolved": "arksurvivalevolved", "dayz": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityv": "identityv", "theisle": "lisola", "thelastofus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "chiam<PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON><PERSON><PERSON><PERSON>", "eco": "eco", "monkeyisland": "is<PERSON>s<PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "daysgone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "stregalu", "pathologic": "patologicu", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7ghsg", "thelongdark": "alongascura", "ark": "arca", "grounded": "terraterra", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "babbupazzu", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "r<PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexen": "streghe", "theevilwithin": "um<PERSON><PERSON><PERSON><PERSON>", "realrac": "realrac", "thebackrooms": "ecammaresecrete", "backrooms": "cammeredideretu", "empiressmp": "empiressmp", "blockstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thequarry": "acava", "tlou": "tlou", "dyinglight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thewalkingdeadgame": "ughjocudimor<PERSON>vi<PERSON><PERSON>", "wehappyfew": "poch<PERSON><PERSON><PERSON><PERSON>", "riseofempires": "nascitadimperi", "stateofsurvivalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagestory": "storiacorsica", "arksurvival": "sopravvivenzaark", "barotrauma": "barotrauma", "breathedge": "respiragh<PERSON>", "alisa": "alisa", "westlendsurvival": "westlendsurvival", "beastsofbermuda": "bestiidibermuda", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "or<PERSON><PERSON>ur<PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "vitadopughjoccu", "survivalgames": "ghjochidisoprravivenza", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "staguerra_mea", "scpfoundation": "fundazionescp", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "pienghjedispaventu", "raft": "zattera", "rdo": "rdo", "greenhell": "inferuverde", "residentevil5": "residentevil5", "deadpoly": "mortupolì", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "nanna", "littlenightmares2": "picculisogninotturni2", "signalis": "signalis", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "figlidiaforesta", "rustvideogame": "ghjoculurust", "outlasttrials": "provediresistenza", "alienisolation": "isolamentualienu", "undawn": "albore", "7day2die": "7ghjorni2more", "sunlesssea": "maresenzasole", "sopravvivenza": "supravivenza", "propnight": "nuttediprop", "deadisland2": "isolamorta2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deathverse": "universudiamorte", "cataclysmdarkdays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soma": "soma", "fearandhunger": "pau<PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkerdiachernobyl", "lifeafter": "dopu", "ageofdarkness": "etàdiscurità", "clocktower3": "clocktower3", "aloneinthedark": "soluinneru", "medievaldynasty": "dinastiamedievale", "projectnimbusgame": "projectnimbusgame", "eternights": "<PERSON><PERSON><PERSON><PERSON>", "craftopia": "craftopia", "theoutlasttrials": "eproveoutlast", "bunker": "bunker", "worlddomination": "dominazionemundiale", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "<PERSON><PERSON><PERSON>", "warhammer40kcrush": "crushwarhammer40k", "wh40": "wh40", "warhammer40klove": "amoreperwarhammer40k", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "tempiuculexus", "vindicare": "vindicà", "ilovesororitas": "<PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "amuvindicà", "iloveassasinorum": "amuassassinorum", "templovenenum": "tempiudamorenovu", "templocallidus": "tempiulocallefredde", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "<PERSON><PERSON>la", "tetris": "tetris", "lioden": "lioden", "ageofempires": "etàdimperii", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON>", "wingspan": "apertura_dale", "terraformingmars": "terraformazionemarte", "heroesofmightandmagic": "eroidiputenzaèmagia", "btd6": "btd6", "supremecommander": "comandantesupreme", "ageofmythology": "etàdimitologia", "args": "args", "rime": "rime", "planetzoo": "pianetazoo", "outpost2": "avamposti2", "banished": "<PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "allertarossa", "civilization6": "civilta6", "warcraft2": "warcraft2", "commandandconquer": "cumandàècunquistà", "warcraft3": "warcraft3", "eternalwar": "guerraeterna", "strategygames": "ghjochistrategichi", "anno2070": "annu2070", "civilizationgame": "ghjocudicivilizazione", "civilization4": "civilizazione4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spora", "totalwar": "guerratotale", "travian": "travian", "forts": "forti", "goodcompany": "bonacumpagnia", "civ": "civ", "homeworld": "mondudicasa", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "pi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "<PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "strategiaintemporeal", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "regnidue<PERSON>rone", "eu4": "eu4", "vainglory": "vanità", "ww40k": "ww40k", "godhood": "divinità", "anno": "annu", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davesfunalgebraclass", "plagueinc": "pesteincl", "theorycraft": "teorizà", "mesbg": "mesbg", "civilization3": "civilizazione3", "4inarow": "4difila", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "guerraavanzate", "ageofempires2": "agedempires2", "disciples2": "discepuli2", "plantsvszombies": "piantecontrazombie", "giochidistrategia": "ghjochistrategichi", "stratejioyunları": "stratejioyunları", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "ageofmaraviglie", "dinosaurking": "r<PERSON><PERSON><PERSON><PERSON>", "worldconquest": "cunquistamundiale", "heartsofiron4": "coridiferru4", "companyofheroes": "cumpagniadieroi", "battleforwesnoth": "battagliaper<PERSON>not<PERSON>", "aoe3": "aoe3", "forgeofempires": "forgiadi<PERSON><PERSON>", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "ocaocapapera", "phobies": "fobie", "phobiesgame": "ghjocudifubie", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "turnbased": "<PERSON><PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civiltà5", "victoria2": "victoria2", "crusaderkings": "rèdicrociate", "cultris2": "cultris2", "spellcraft": "maghjialogia", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategia", "popfulmail": "popfulmail", "shiningforce": "forzaluminosa", "masterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transporttycoon": "magnate<PERSON><PERSON><PERSON><PERSON><PERSON>", "unrailed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "pianidelturmentu", "uplandkingdoms": "<PERSON><PERSON><PERSON><PERSON>", "galaxylife": "vitagalassica", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespire", "battlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "isims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "granturi<PERSON><PERSON>", "needforspeed": "bisognudivelocità", "needforspeedcarbon": "bisognudivelocitàcarbonu", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "isims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "supravvive", "deadbydaylight": "mortuprimudisgiornu", "alicemadnessreturns": "alice<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "antologiacavallusecuru", "phasmophobia": "fasmufobia", "fivenightsatfreddys": "cincunot<PERSON><PERSON><PERSON><PERSON><PERSON>", "saiko": "saiko", "fatalframe": "quadrufatale", "littlenightmares": "picculis<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "mortirisuscitati", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "incasachiusu", "deadisland": "isolamorta", "litlemissfortune": "piccularisfort<PERSON><PERSON>", "projectzero": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horory": "orrore", "jogosterror": "ghjochiorrore", "helloneighbor": "<PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "salutevicinu2", "gamingdbd": "ghjochidbd", "thecatlady": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "ghjochiorrore", "horrorgaming": "ghjocudiorore", "magicthegathering": "magiathegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "cartecontraumanità", "cribbage": "scopa", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinnaculu", "codenames": "nomidecuertu", "dixit": "dixit", "bicyclecards": "carteàbicicletta", "lor": "lor", "euchre": "eucre", "thegwent": "ugwent", "legendofrunetera": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "solitaire": "solitariu", "poker": "poker", "hearthstone": "pietradicaminettu", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "trucchidic<PERSON><PERSON>", "playingcards": "carteàghjucà", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "cartedicullezzione", "pokemoncards": "<PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "cartedesport", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "picche", "warcry": "<PERSON><PERSON>gue<PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "rèdicori", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "aresistenza", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "cartediyugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohocg": "yugiohocg", "dueldisk": "<PERSON>udi<PERSON><PERSON>", "yugiohgame": "yugiohgame", "darkmagician": "<PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yug<PERSON><PERSON><PERSON><PERSON>", "briscas": "briscas", "juegocartas": "gh<PERSON><PERSON>_di_carte", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "ghjochidicarte", "mtgjudge": "giudicemtg", "juegosdecartas": "ghjochidicarte", "duelyst": "duellist", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "gh<PERSON><PERSON>_di_carte", "carteado": "<PERSON><PERSON><PERSON>", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "sagadispirit<PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "gh<PERSON><PERSON>_di_carte", "žolíky": "žolíky", "facecard": "cartadifaccia", "cardfight": "battàgliadicarte", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "marvelchampions", "magiccartas": "cartemagiche", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "ombraversu", "skipbo": "saltabò", "unstableunicorns": "unicorninstabili", "cyberse": "c<PERSON><PERSON>", "classicarcadegames": "ghjochidarcadeclassichi", "osu": "osu", "gitadora": "gitadora", "dancegames": "ghjochididanza", "fridaynightfunkin": "vennerinottefunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "prugettumirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "erocuchitar<PERSON>", "clonehero": "clonehero", "justdance": "justdance", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "scuzzulàimorti", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "ballàcentrale", "rhythmgamer": "ghju<PERSON><PERSON>rit<PERSON>", "stepmania": "stepmania", "highscorerythmgames": "spartit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "rit<PERSON><PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "unballudifoculè<PERSON><PERSON><PERSON><PERSON>", "auditiononline": "audizioneinlinea", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "ghjochiditirmu", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubing": "cubing", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "ghjochidipuzzle", "spotit": "truvalu", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "rumpicapilog<PERSON>", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "r<PERSON><PERSON><PERSON>", "rubikscube": "cubo<PERSON><PERSON><PERSON>", "crossword": "parolle_crociate", "motscroisés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "krzyżówki": "cruciver<PERSON>", "nonogram": "nonogramma", "bookworm": "topudiilibri", "jigsawpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinello": "indovinellu", "riddle": "induvin<PERSON><PERSON>", "riddles": "<PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "rom<PERSON>ap<PERSON>", "tekateki": "tekateki", "inside": "dentru", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "simulatoredifuga", "minesweeper": "<PERSON><PERSON><PERSON><PERSON>", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "ghjoculugiardiniscapes", "puzzlesport": "sportpuzzle", "escaperoomgames": "ghjochidicamerascappata", "escapegame": "ghjoculavasione", "3dpuzzle": "puzzle3d", "homescapesgame": "ghjoculuhomescapes", "wordsearch": "paroledacercà", "enigmistica": "enigmistica", "kulaworld": "mundukula", "myst": "misteru", "riddletales": "storiinduvinaghje", "fishdom": "fishdom", "theimpossiblequiz": "uquizimpussibile", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "puzzle3currispundenze", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "stranu", "rubikcube": "cubumagicu", "cuborubik": "cubbor<PERSON><PERSON>", "yapboz": "yapboz", "thetalosprinciple": "prin<PERSON><PERSON><PERSON><PERSON><PERSON>", "homescapes": "case<PERSON>ng<PERSON>je", "puttputt": "minigolf", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON>", "tycoongames": "ghjochiditycoon", "cubosderubik": "cubidirubik", "cruciverba": "parullechruciate", "ciphers": "cifri", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "risol<PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "z<PERSON><PERSON><PERSON><PERSON><PERSON>", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "<PERSON><PERSON><PERSON>", "guessing": "indu<PERSON><PERSON><PERSON>", "nonograms": "nonogrammi", "kostkirubika": "kostkirubika", "crypticcrosswords": "parolein<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "cacci<PERSON><PERSON><PERSON><PERSON>", "catcrime": "crim<PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "rump<PERSON><PERSON>", "hlavolamy": "r<PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "autodefiniti", "picopark": "picopark", "wandersong": "cantunavi<PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "ghjocudipaparaghjasutenonomi", "cassetête": "casset<PERSON>te", "limbo": "limbu", "rubiks": "rubiks", "maze": "labirintu", "tinykin": "tinykin", "rubikovakostka": "cubettudirubik", "speedcube": "cu<PERSON><PERSON><PERSON>", "pieces": "pezzi", "portalgame": "ghjoculuportale", "bilmece": "bilmece", "puzzelen": "puzzle", "picross": "picross", "rubixcube": "cubumagicu", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubumagicu", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "paesumaravigliusutort<PERSON>", "monopoly": "monopoliu", "futurefight": "futuracumbattimentu", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "stel<PERSON><PERSON><PERSON>", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "stelledialchi<PERSON>", "stateofsurvival": "stateofsurvival", "mycity": "acittàmeia", "arknights": "arknights", "colorfulstage": "scenacoloritu", "bloonstowerdefense": "torridifesabloons", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "iperfronte", "knightrun": "cavalcata", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "battagliadicalciu", "a3": "a3", "phonegames": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "sceltadurè", "guardiantales": "contidi<PERSON><PERSON><PERSON>", "petrolhead": "appassiunatu_di_vitture", "tacticool": "tatticool", "cookierun": "corsacorsa", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "fora_di_u_giru", "craftsman": "<PERSON><PERSON><PERSON>", "supersus": "superdubbitativu", "slowdrive": "guid<PERSON>pian<PERSON>", "headsup": "attenti", "wordfeud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bedwars": "guerradiletti", "freefire": "foculiberu", "mobilegaming": "gjo<PERSON><PERSON>ile", "lilysgarden": "giardin<PERSON><PERSON>ly", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymobile": "callofdutymobile", "thearcana": "<PERSON><PERSON><PERSON>", "8ballpool": "8ballpool", "emergencyhq": "<PERSON><PERSON><PERSON><PERSON>", "enstars": "stel<PERSON><PERSON><PERSON>", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "tremulieagità", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "principessaduttempu", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegenda", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "amorediscarsetta", "androidgames": "ghjochiandroid", "criminalcase": "casucriminale", "summonerswar": "summonerswar", "cookingmadness": "foll<PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "sfidatrivia", "leagueofangels": "ligadiangiuli", "lordsmobile": "lordsmobile", "tinybirdgarden": "giard<PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "nuvolaneural", "mysingingmonsters": "imiomostricantendu", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "arch<PERSON><PERSON><PERSON><PERSON><PERSON>", "raidshadowlegends": "raidshadowlegends", "warrobots": "warrobots", "mirrorverse": "universupecchju", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "futempu", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "intrata", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "amichi<PERSON><PERSON><PERSON>", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "evasioned<PERSON><PERSON>", "wolfy": "wolfy", "runcitygame": "corsaghjocucità", "juegodemovil": "ghjoculum<PERSON>le", "avakinlife": "v<PERSON><PERSON><PERSON>", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "magnate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandchase": "grandchase", "bombmebrasil": "bombardatemibrasile", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON>", "otomegame": "gh<PERSON><PERSON>_otome", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "nikkisplendente", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "ca<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "battimentudombra3", "limbuscompany": "cumpagni<PERSON><PERSON><PERSON>", "demolitionderby3": "derbydidemulizione3", "wordswithfriends2": "paroliècumpagni2", "soulknight": "cavalieridianima", "purrfecttale": "storia<PERSON><PERSON><PERSON><PERSON>", "showbyrock": "showbyrock", "ladypopular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "lolmobile", "harvesttown": "paesediracolta", "perfectworldmobile": "monduperfettumobile", "empiresandpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empirespuzzles": "puzzlesdimperu", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "campudibattagliamobileind", "fanny": "fanu", "littlenightmare": "pic<PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "corsacanghjone", "tearsofthemis": "lacrimeditemis", "eversoul": "animaeterna", "gunbound": "gunbound", "gamingmlbb": "ghjochimlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eveechoes": "eveechoes", "jogocelular": "ghjocu_mobile", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "ma<PERSON><PERSON><PERSON><PERSON>a", "cabalmobile": "cabalmobile", "streetfighterduel": "duelludistreetfighter", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "ghjoculubgmi", "girlsfrontline": "ragazzeinfrontelinia", "jurassicworldalive": "jurassicworldalive", "soulseeker": "cercadianima", "gettingoverit": "<PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "storiadi<PERSON><PERSON><PERSON>", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofneverland": "legendadineverland", "pubglite": "pubglite", "gamemobilelegends": "ghjoculegendimobili", "timeraiders": "razziadicronos", "gamingmobile": "ghjochidemobile", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "ibattaglighatti", "dnd": "dnd", "quest": "cerca", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON>oll<PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "ghjo<PERSON>dirolu", "worldofdarkness": "mon<PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "2300ad": "2300dc", "larp": "larp", "romanceclub": "<PERSON><PERSON><PERSON><PERSON>", "d20": "d20", "pokemongames": "ghjochidipokemon", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON><PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "ipnu", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "poke<PERSON><PERSON><PERSON>", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "squadrarazzu", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "mustricciulidiborsett<PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "pel<PERSON><PERSON><PERSON><PERSON>", "teamystic": "squadramistica", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "manipruferte", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>e", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "maestrupokémon", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "zite<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "cacciatorelucente", "ajedrez": "scacchi", "catur": "scacchi", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "scacchi", "ajedres": "scacchi", "chessgirls": "ragazzechi<PERSON>cchi", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "mon<PERSON><PERSON><PERSON>", "jeudéchecs": "jeudéchecs", "japanesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chinesechess": "s<PERSON><PERSON><PERSON><PERSON>", "chesscanada": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "fide": "fidatu", "xadrezverbal": "sca<PERSON><PERSON><PERSON><PERSON>", "openings": "aperture", "rook": "torre", "chesscom": "scacchipuntcom", "calabozosydragones": "dungeonsedraghi", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "<PERSON><PERSON><PERSON>", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "aleggendadivoxmachina", "doungenoanddragons": "dungeonsèdragoni", "darkmoor": "brughjeraoscura", "minecraftchampionship": "campiunatuminecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modsminecraft", "mcc": "mcc", "candleflame": "fiammadicandela", "fru": "fru", "addons": "componenti", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmuddificatu", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "trasterre", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftcità", "pcgamer": "pcgamer", "jeuxvideo": "ghjochividie", "gambit": "scam<PERSON><PERSON>", "gamers": "gamersisti", "levelup": "passuàunaltrulivellu", "gamermobile": "ghjucatoremobile", "gameover": "ghjocufinitu", "gg": "gg", "pcgaming": "ghjochipc", "gamen": "ghju<PERSON>dor<PERSON>", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "ghjochidipc", "casualgaming": "ghjochidicasa", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmasterrace", "pcgame": "ghjocu_pc", "gamerboy": "ghjucadore", "vrgaming": "ghjocurvr", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "partite", "consoleplayer": "consoleplayer", "boxi": "boxi", "pro": "pro", "epicgamers": "ghjuca<PERSON><PERSON>", "onlinegaming": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "semigamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamergirls": "ragazzigamer", "gamermoms": "mamme<PERSON><PERSON><PERSON><PERSON>", "gamerguy": "ghjucatore", "gamewatcher": "ghjucadore", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerschicas", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "squadrachjàsiforte", "mallugaming": "ghjocucorsu", "pawgers": "<PERSON><PERSON><PERSON>", "quests": "queste", "alax": "alax", "avgn": "avgn", "oldgamer": "vechjiughjucatore", "cozygaming": "gamingaccantu", "gamelpay": "ghjoculpagà", "juegosdepc": "ghjochipc", "dsswitch": "cambiàdids", "competitivegaming": "ghjocucumpetitivu", "minecraftnewjersey": "minecraftnewjersey", "faker": "falsariu", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "ghjo<PERSON>eterose<PERSON><PERSON><PERSON>", "gamepc": "ghjochipc", "girlsgamer": "ragazzegiocatrici", "fnfmods": "fnfmods", "dailyquest": "sfidacutidiana", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "ragazzegiocatrici", "gamesetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overpowered": "tropputente", "socialgamer": "ghjucadoresociale", "gamejam": "gamejam", "proplayer": "ghjucadorepro", "roleplayer": "roleplayer", "myteam": "as<PERSON><PERSON><PERSON><PERSON>", "republicofgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "tripplaleggenda", "gamerbuddies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "butuhcewekgamers": "ciochich<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "ghjucatorecasuale", "89squad": "89squadra", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "insec", "gemers": "ghjucatori", "oyunizlemek": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamertag": "pseudoghju<PERSON>ore", "lanparty": "lanparty", "videogamer": "videogiucatore", "wspólnegranie": "ghjocuinseme", "mortdog": "mortdog", "playstationgamer": "ghjucadorediplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "corsagtracing", "notebookgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "protogen": "protogen", "womangamer": "don<PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "chjaramentesoghjecatore", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "cacciadore", "humanfallflat": "cadu<PERSON>uman<PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "fugaazeru", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "musicanintendo", "sonicthehedgehog": "son<PERSON><PERSON><PERSON><PERSON>", "sonic": "sonic", "fallguys": "fallguys", "switch": "cambia", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "mascheradimaggiore", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "assuavvucatu", "ssbm": "ssbm", "skychildrenofthelight": "<PERSON>gh<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "tomodachicorsu", "ahatintime": "untempodiodiu", "tearsofthekingdom": "lacrimeduregnu", "walkingsimulators": "simulatoridecamina<PERSON>", "nintendogames": "nintendogames", "thelegendofzelda": "aleggendadizelda", "dragonquest": "dragonquest", "harvestmoon": "lunadorba", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "soffiudisalvatichezza", "myfriendpedro": "umeamiccupedro", "legendsofzelda": "legendidizelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "<PERSON><PERSON><PERSON><PERSON>", "earthbound": "terrestre", "tales": "<PERSON><PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "strategiadutriangulu", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "guerrieridihyrule", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "ma<PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "strappa<PERSON>", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON>", "vanillalol": "vanigliamdr", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "publicità", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendscorsica", "aatrox": "aatrox", "euw": "bleah", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadieleggende", "gaminglol": "gh<PERSON><PERSON><PERSON>l", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "port<PERSON><PERSON><PERSON>", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "giochifornite", "fortnitebr": "fortnitebr", "retrovideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scaryvideogames": "ghjo<PERSON><PERSON>aven<PERSON><PERSON>", "videogamemaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "megamanzero": "megamanzero", "videogame": "<PERSON><PERSON><PERSON><PERSON>", "videosgame": "videogiochi", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "arcadi", "acnh": "acnh", "puffpals": "amicidifumu", "farmingsimulator": "simulatorediagricoltura", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "r<PERSON><PERSON><PERSON><PERSON>ag<PERSON>", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "ghjochidisabbia", "videogamelore": "videogiochilore", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "legnudusk", "dreamscape": "paisagh<PERSON>_di_sognu", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "spaziumort<PERSON>", "amordoce": "amoredolce", "videogiochi": "ghjochivideo", "theoldrepublic": "arepubblicavecchia", "videospiele": "<PERSON><PERSON><PERSON><PERSON>", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "ghjochidavventura", "wolfenstein": "wolfenstein", "actionadventure": "aziuniavventura", "storyofseasons": "storiadistagioni", "retrogames": "ghjochidiprima", "retroarcade": "arcaderetro", "vintagecomputing": "informaticavecchia", "retrogaming": "g<PERSON><PERSON><PERSON><PERSON>", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "inghjustizia2", "shadowthehedgehog": "<PERSON><PERSON><PERSON><PERSON>", "rayman": "rayman", "skygame": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "vitazen", "beatmaniaiidx": "beatmaniaiidx", "steep": "ripidu", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "ghjochiblockchain", "medievil": "medievale", "consolegaming": "ghjocudiconsola", "konsolen": "cunsole", "outrun": "corre_più_chè_ellu", "bloomingpanic": "panicafiurita", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monstergirlquest": "<PERSON><PERSON><PERSON><PERSON>", "supergiant": "supergigante", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "simulator<PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "ghjochivecchji", "bethesda": "bethesda", "jackboxgames": "ghjochijackbox", "interactivefiction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "rumanzu_visuale", "visualnovels": "romanzivisuali", "rgg": "rgg", "shadowolf": "lupu_ombra", "tcrghost": "tcrghost", "payday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "principessacrepusculare", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sabbiera", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "novellavisuale", "thecrew2": "ugruppone2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "ghjoculivechji", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamentu", "godhand": "<PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "rivoluzionesopraleafoglie", "wiiu": "wiiu", "leveldesign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starrail": "starrail", "keyblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aplaguetale": "unafiulapestatale", "fnafsometimes": "fnafsometimes", "novelasvisuales": "rumanzivisuali", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "gh<PERSON><PERSON><PERSON><PERSON>", "videojuejos": "videojo<PERSON>", "videogamedates": "appuntament<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "umioamoredolce", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hulkgames": "ghjochihulk", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "ritornodicunti", "gamstergaming": "gamstergaming", "dayofthetantacle": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crashracing": "corsaaimpatti", "3dplatformers": "ghjochi3dpiattaforma", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "spadainfernale", "storygames": "ghjochidistoria", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "aldilàdi<PERSON><PERSON><PERSON>me", "gameuse": "ghjucatrice", "offmortisghost": "offmortisghost", "tinybunny": "cuni<PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "put<PERSON>za", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "avventuregrafiche", "quickflash": "flashrapid<PERSON>", "fzero": "fzero", "gachagaming": "ghjocugacha", "retroarcades": "salle<PERSON><PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "terra_diserta", "powerwashsim": "simlavaghjupressione", "coralisland": "is<PERSON>corall<PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "unaltrumondu", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "footballfusione", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "<PERSON><PERSON>", "legomarvel": "<PERSON><PERSON><PERSON><PERSON>", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "metalluintortu", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "muntagnidivergogna", "simulator": "simulatore", "symulatory": "simulatori", "speedrunner": "speedrunner", "epicx": "epicu", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "ghjocoditerrore", "wonderlandonline": "maravigliaenlinia", "skylander": "skylander", "boyfrienddungeon": "zite<PERSON><PERSON>a", "toontownrewritten": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "simracing", "simrace": "corsadisimulazione", "pvp": "pvp", "urbanchaos": "caosuurbanu", "heavenlybodies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "s<PERSON><PERSON><PERSON>", "partyvideogames": "ghjochiparty", "graveyardkeeper": "guardianudecimiteru", "spaceflightsimulator": "simulatoreduvoluspaziale", "legacyofkain": "ereditàdikain", "hackandslash": "colpie<PERSON><PERSON><PERSON>", "foodandvideogames": "cib<PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thewolfamongus": "ulupufrà<PERSON>i", "truckingsimulator": "simulatoredicamion", "horizonworlds": "mundidiorrizonte", "handygame": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "leyendasyvideojuegos", "oldschoolvideogames": "ghjo<PERSON>videovecchiscola", "racingsimulator": "simulatoredicorsa", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "canzunepop", "famitsu": "famitsu", "gatesofolympus": "portedilolimpu", "monsterhunternow": "cacciatoridicostrinow", "rebelstar": "<PERSON><PERSON><PERSON>", "indievideogaming": "videogiochindipendenti", "indiegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogames": "indievideoghjocu", "indievideogame": "videogiocuindipendente", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spider<PERSON><PERSON>ne", "bufffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "imbattibile", "projectl": "p<PERSON><PERSON><PERSON><PERSON>", "futureclubgames": "ghjochidisocietàfuturi", "mugman": "tazzahome", "insomniacgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ne", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturascienza", "backlog": "a<PERSON><PERSON><PERSON>", "gamebacklog": "ghjochidaghjucà", "gamingbacklog": "arretratudivideugiochi", "personnagejeuxvidéos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "achievementhunter": "cacciatoredaccumpimenti", "cityskylines": "orizzontidicittà", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "canefurbacciu", "beastlord": "signuribestia", "juegosretro": "gh<PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "paraboladistanley", "reservatoriodedopamin": "riservadidopamina", "staxel": "staxel", "videogameost": "<PERSON>lon<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "amokofxv", "arcanum": "<PERSON><PERSON>", "neoy2k": "neoy2k", "pcracing": "corseallanave", "berserk": "sfrenu", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initialed", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animut<PERSON><PERSON>", "darkerthanblack": "pi<PERSON><PERSON>uchè<PERSON>ner<PERSON>", "animescaling": "animescaling", "animewithplot": "animecùtrama", "pesci": "pesci", "retroanime": "<PERSON><PERSON><PERSON><PERSON>", "animes": "anime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80", "90sanime": "anime90", "darklord": "sign<PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "maestrupiacente", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "animanni2000", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonestagione1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "cupertaanime", "thevisionofescaflowne": "avisionedescaflowne", "slayers": "ammazza<PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "pescebananà", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "hanakokundicesu", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "mangaskipbeat", "vanitas": "vanità", "fireforce": "forzadifuoco", "moriartythepatriot": "moriartyilpatri<PERSON>", "futurediary": "ghjurnaleavvene", "fairytail": "fiaba", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parasyte": "parassita", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "melodia<PERSON><PERSON>na", "kamisamakiss": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "mangaditerrore", "romancemangas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "<PERSON>rva<PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "lagunanera", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformatori", "geniusinc": "geniusinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "unciertindicemaghju", "sao": "sao", "blackclover": "trefuneru", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "prioritàdiovumaravigliusu", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hipnosismicro", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animedisport", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shounenanime": "animeshonen", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "bellacura", "theboyandthebeast": "uzitelluelabestia", "fistofthenorthstar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "<PERSON><PERSON><PERSON><PERSON>", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "cumumanteneunamammasecca", "fullmoonwosagashite": "lunasanapienace<PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "simpaticuèspavintevule", "martialpeak": "cimamarziale", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "figliolahiscore", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zer<PERSON><PERSON>e", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "ragazzamostru", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "<PERSON><PERSON><PERSON><PERSON>", "amiti": "amicizia", "sailorsaturn": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "dio": "dio", "sailorpluto": "sailorplutone", "aloy": "aloy", "runa": "runa", "oldanime": "animevechju", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "<PERSON><PERSON><PERSON><PERSON>", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "spadone", "loli": "loli", "horroranime": "animehorrore", "fruitsbasket": "panierdifrutta", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "amorein<PERSON>ta", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "umarinarustraneru", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "ate<PERSON><PERSON><PERSON><PERSON>a", "monstermanga": "<PERSON><PERSON><PERSON><PERSON>", "yourlieinapril": "abugiarditaprile", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "serafinudiafinale", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "terradimortimeravigliosa", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "gh<PERSON><PERSON>_<PERSON>_darwin", "husbu": "maritu", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "curipand<PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "guerraculinaria", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "lineadiudiavulu", "toyoureternity": "versuteperleternità", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "<PERSON>iu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "alleanzas<PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "sguassatu", "bluelock": "bluelock", "goblinslayer": "ammazzagoblins", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "battitudicoresghjuvane", "vampireknight": "cavalierevampiru", "mugi": "mugi", "blueexorcist": "esorcistab<PERSON>", "slamdunk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "fattucrià", "spyfamily": "famigliadiaspie", "airgear": "airgear", "magicalgirl": "fattinamagica", "thesevendeadlysins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "udì<PERSON><PERSON><PERSON>u", "kissxsis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "grandblue": "b<PERSON><PERSON><PERSON>", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "universanimu", "swordartonlineabridge": "<PERSON><PERSON><PERSON><PERSON>", "saoabridged": "saoriduttu", "hoshizora": "stellata", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "mort<PERSON><PERSON><PERSON><PERSON>", "romancemanga": "<PERSON><PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animerumanzu", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animecorsica", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayer<PERSON><PERSON><PERSON><PERSON>", "bloodlad": "bloodlad", "goodbyeeri": "ad<PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adioseri": "addioeri", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "stellesallineate", "romanceanime": "animeromantico", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cherrymagic": "magiadicerase", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "recordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "l<PERSON><PERSON><PERSON><PERSON>", "germantechno": "technotudescu", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "princi<PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "assassinudicorsu", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "paratadiamorte", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animespace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsundpanzer": "ragazzeèpanzer", "akb0048": "akb0048", "hopeanuoli": "speranzaenuova", "animedub": "animedoppiatu", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "anim<PERSON><PERSON><PERSON>", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "ragazzapesca", "cavalieridellozodiaco": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mechamusume": "mecca<PERSON><PERSON><PERSON>", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "coredimanga", "deliciousindungeon": "deliziosuindungeon", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "recordofragnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "saltàversuumocassinu", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "supraquipatu", "toriko": "<PERSON><PERSON>o", "ravemaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "atelierdimaghe", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kamen": "kamen", "mangaislife": "mangaislife", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "icavalieridellodiaco", "animeshojo": "animeshojo", "reverseharem": "haremàriversa", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "grandeprofessoreizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "<PERSON><PERSON><PERSON>", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "giru5", "grandbluedreaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodplus": "bloodplus", "bloodplusanime": "animedisanguepiu", "bloodcanime": "sangueanime", "bloodc": "sangu", "talesofdemonsandgods": "storiedidemoniedidei", "goreanime": "goreanime", "animegirls": "animiragazze", "sharingan": "<PERSON><PERSON>", "crowsxworst": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatteranime": "animesplatter", "splatter": "sb<PERSON><PERSON>", "risingoftheshieldhero": "ascensioned<PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "animecorsicanu", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedhàpresu", "animeyuri": "animeyuri", "animeespaña": "animeespagna", "animeciudadreal": "animecittàreale", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "figliuolidemeraviglie", "liarliar": "bugiardubugiard<PERSON>", "supercampeones": "supercampioni", "animeidols": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isekaiwasmartphone": "isekaierasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON>iver<PERSON>", "magicalgirls": "ragazzemagiche", "callofthenight": "appelludianotte", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "giardinuombra", "tsubasachronicle": "cronicheditsunbasa", "findermanga": "truvamanga", "princessjellyfish": "principessamedusa", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "s<PERSON><PERSON><PERSON>pet<PERSON><PERSON><PERSON>", "animeverse": "animeversu", "persocoms": "persocom", "omniscientreadersview": "vistalettoreonnisciente", "animecat": "gatt<PERSON>ani<PERSON>", "animerecommendations": "animeraccomandazioni", "openinganime": "aperturanimedda", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "amocummediataruman<PERSON>i", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitsgundam", "mech": "mecca", "eurekaseven": "<PERSON><PERSON><PERSON><PERSON>", "eureka7": "eureka7", "thebigoanime": "ugranbigoanime", "bleach": "candeggina", "deathnote": "quadernudimorte", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animemilitare", "greenranger": "<PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "volpedist<PERSON>", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animecity", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animuonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "avventuradidigimon", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "cacciatoredemoni", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attaccuaiti<PERSON>", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "corpusdisundaghju", "onepieceanime": "onepieceanime", "attaquedestitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theonepieceisreal": "theone<PERSON>èver<PERSON>", "revengers": "vindittori", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimonstory": "storiadigi<PERSON>", "digimontamers": "digimontamers", "superjail": "superprigione", "metalocalypse": "metalocalisse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonperfettu", "kemonofriends": "amicidianime", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "campingrelax", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "stregavolante", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "solu", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "tuttiisantivia", "recuentosdelavida": "storiedivita"}