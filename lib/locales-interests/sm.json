{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "f<PERSON><PERSON><PERSON>", "cognitivefunctions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychology": "saikalosia", "philosophy": "filosofia", "history": "talaaga", "physics": "<PERSON><PERSON><PERSON>", "science": "saienisi", "culture": "aganuu", "languages": "gagana", "technology": "<PERSON><PERSON><PERSON><PERSON>", "memes": "mimi", "mbtimemes": "mbtimemes", "astrologymemes": "uigafaituaitu", "enneagrammemes": "enneagrammemes", "showerthoughts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "funny": "malie", "videos": "atatifaga", "gadgets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "politics": "polotiki", "relationshipadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "fautuagamoolimaga", "crypto": "crypto", "news": "<PERSON><PERSON><PERSON><PERSON>", "worldnews": "talafouomail<PERSON>i", "archaeology": "archaeology", "learning": "aʻoaʻoga", "debates": "iauiga", "conspiracytheories": "teoriefaufaututū", "universe": "<PERSON><PERSON><PERSON><PERSON>", "meditation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mythology": "ta<PERSON><PERSON><PERSON><PERSON><PERSON>", "art": "siva", "crafts": "faa<PERSON><PERSON><PERSON>a", "dance": "siva", "design": "<PERSON><PERSON><PERSON><PERSON>", "makeup": "meikiapa", "beauty": "<PERSON><PERSON><PERSON>", "fashion": "mea", "singing": "pese", "writing": "tus<PERSON><PERSON>i", "photography": "ata", "cosplay": "cosplay", "painting": "vali", "drawing": "ata", "books": "tusi", "movies": "filimi", "poetry": "faa<PERSON><PERSON>", "television": "tivi", "filmmaking": "faigaataalaʻaga", "animation": "ata_faagaioiga", "anime": "anime", "scifi": "scifi", "fantasy": "miti", "documentaries": "at<PERSON><PERSON><PERSON><PERSON>", "mystery": "<PERSON><PERSON>", "comedy": "ata", "crime": "solitulafono", "drama": "talaaga", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "<PERSON><PERSON>ia", "romance": "aiga", "realitytv": "tvmoni", "action": "faagaioiga", "music": "faili", "blues": "pala", "classical": "kilasika", "country": "atunuu", "desi": "desi", "edm": "edm", "electronic": "eletise", "folk": "talagamaupu", "funk": "<PERSON><PERSON>i", "hiphop": "hiphop", "house": "fale", "indie": "indie", "jazz": "siasi", "kpop": "kpop", "latin": "latina", "metal": "metala", "pop": "pop", "punk": "panka", "rnb": "rnb", "rap": "rap", "reggae": "rege", "rock": "papa", "techno": "tekeno", "travel": "malaga", "concerts": "faafiafiaga", "festivals": "mafiafiaga", "museums": "falemataaga", "standup": "tula<PERSON><PERSON>", "theater": "faleata", "outdoors": "fafo", "gardening": "faatoaga", "partying": "pati", "gaming": "taaloga", "boardgames": "taaloga", "dungeonsanddragons": "dungeonsanddragons", "chess": "sese", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "meaai", "baking": "uma", "cooking": "<PERSON><PERSON><PERSON>", "vegetarian": "vegetarian", "vegan": "vegan", "birds": "manulele", "cats": "pusi", "dogs": "maile", "fish": "ia", "animals": "tif<PERSON>", "blacklivesmatter": "o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "environmentalism": "si<PERSON>ma<PERSON>", "feminism": "faafiletua", "humanrights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lgbtqally": "lgbtqlagolago", "stopasianhate": "taof<PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "tusolagoagatransgender", "volunteering": "tautua", "sports": "taaloga", "badminton": "pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baseball": "pesipolo", "basketball": "pasi<PERSON><PERSON><PERSON>", "boxing": "pepese", "cricket": "kiri<PERSON><PERSON>", "cycling": "faataavalevale", "fitness": "<PERSON>aa<PERSON><PERSON><PERSON>", "football": "soka", "golf": "kolof", "gym": "gym", "gymnastics": "taaloga_faatagataga", "hockey": "hokī", "martialarts": "aigafaasino", "netball": "netipolo", "pilates": "pilates", "pingpong": "tenisipolo", "running": "tamoe", "skateboarding": "faaskeitipoti", "skiing": "sesee", "snowboarding": "faaseesefagavevela", "surfing": "faas<PERSON>", "swimming": "<PERSON><PERSON><PERSON>", "tennis": "tenisi", "volleyball": "sipolivolei", "weightlifting": "siitauaveamea", "yoga": "ioga", "scubadiving": "f<PERSON>au<PERSON><PERSON>", "hiking": "savalivao", "capricorn": "ka<PERSON><PERSON><PERSON>", "aquarius": "a<PERSON><PERSON>", "pisces": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aries": "alii", "taurus": "taurus", "gemini": "gemini", "cancer": "kanesa", "leo": "leo", "virgo": "virgo", "libra": "lipela", "scorpio": "scorpio", "sagittarius": "sagittarius", "shortterm": "puupuu", "casual": "masani", "longtermrelationship": "tula<PERSON><PERSON>", "single": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "polyamory": "ta<PERSON><PERSON>aiga", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "faafafine", "lesbian": "lesepia", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "tautagatausia", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "talagaatupu", "soulreaver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "<PERSON><PERSON><PERSON><PERSON>", "legendofspyro": "ta<PERSON>yro", "rouguelikes": "rouguelikes", "syberia": "<PERSON><PERSON><PERSON>a", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "laoil<PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "atiafiemblemfa<PERSON>", "yokaiwatch": "yokaiwatch", "rocksteady": "tuumau", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "tauagaisolo", "openworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "tauaogaleafuatau", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "feʻ<PERSON><PERSON><PERSON><PERSON>lemaʻimau", "jetsetradio": "vaaleleemolegamalaga", "tribesofmidgard": "ituaigaomidgard", "planescape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsoftherealm2": "tupu2olemalo", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "lanufia", "medabots": "medabots", "lodsoftherealm2": "tamaaitusiolemalo2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "taalogasusumatua", "okage": "okage", "juegoderol": "taaloga", "witcher": "witcher", "dishonored": "tauleagaina", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "paʻu", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "fautuagata", "immersive": "faatosinavaiga", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasytuai", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motifeituumamate", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "<PERSON><PERSON><PERSON><PERSON>", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "fanualauila", "pathfinder": "alasue", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "ta<PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "tamuimalamalupo", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "alof<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "pa<PERSON><PERSON><PERSON>", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "pule", "yourturntodie": "oetaepot<PERSON>", "persona3": "persona3", "rpghorror": "rpgtausaga", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "tagataotilua", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "maluvaivipu", "gurps": "gurps", "darkestdungeon": "alat<PERSON><PERSON>", "eclipsephase": "v<PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "vae<PERSON>se", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "toatamaialitaua", "skullgirls": "skullgirls", "nightcity": "pot<PERSON><PERSON>", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "tauasolo", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "noiagaloleiloa", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "alaigauata", "childoflight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "laleigadigimon", "monsterrancher": "faafagapuaaaitu", "ecopunk": "ecopunk", "vermintide2": "alamea2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "nofoālag<PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "atapunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "oasispeavai", "hogwartmystery": "mi<PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "tiapolo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "ta", "lastepoch": "taim<PERSON><PERSON><PERSON><PERSON>", "starfinder": "fet<PERSON><PERSON><PERSON><PERSON>", "goldensun": "la<PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "atualetamuamuaaleatua", "bladesinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "pogisalemalama2000", "sandevistan": "sandevistan", "cyberpunk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkredaʻa", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "pa<PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "paia", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "vaʻivailagavale", "adventurequest": "sailigamalaga", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "taalogaroleplay", "roleplayinggames": "taaloga", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "talanoaosymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myfarog": "myfarog", "sacredunderworld": "paiaolalalo", "chainedechoes": "faʻasoloechoes", "darksoul": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "talagat<PERSON>mataaitu", "othercide": "<PERSON><PERSON><PERSON>", "mountandblade": "tautaumaupepese", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "taimita<PERSON>", "pillarsofeternity": "faʻ<PERSON>etuma<PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "rifts", "tibia": "tibia", "thedivision": "levaega", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "talaoledragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werewolftheapocalypse", "aveyond": "aveyond", "littlewood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childrenofmorta": "tamaititagamorta", "engineheart": "fatu_afi", "fable3": "tala3", "fablethelostchapter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "suifefiloi", "rollenspiel": "taaloga", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "fetualelagi", "oldschoolrevival": "toe<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "pogisaalaup<PERSON>", "juegosrpg": "taalogarpg", "kingdomhearts": "<PERSON><PERSON><PERSON><PERSON>", "kingdomheart3": "malosiofatufa3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "<PERSON><PERSON><PERSON><PERSON>", "gloomhaven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wildhearts": "<PERSON><PERSON><PERSON><PERSON>", "bastion": "palesàlomia", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "laginaeokaia", "shadowhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON><PERSON>", "breathoffire4": "manavaola4", "mother3": "tina3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "isia<PERSON>", "roleplaygames": "taaloga_faatusa", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "aitafesupainamatagatafa", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "t<PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "tuligai<PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "siosiotalimata", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "talanoagarpg", "shadowheartscovenant": "<PERSON>ea<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesoul": "<PERSON><PERSON><PERSON><PERSON>", "baldursgate3": "baldursgate3", "kingdomcome": "malo<PERSON><PERSON>", "awplanet": "aw<PERSON><PERSON>", "theworldendswithyou": "uai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dyinglight2": "malapemate2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "faa<PERSON><PERSON><PERSON><PERSON>", "shoptitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forumrpg": "talanotarpg", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON>", "blackbook": "tusipopo", "skychildrenoflight": "skychildrenoflight", "gryrpg": "gryrpg", "sacredgoldedition": "tusipaiaaulamatagofie", "castlecrashers": "ta<PERSON><PERSON><PERSON>ʻoma<PERSON>", "gothicgame": "taalogotiki", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "talofarpg", "prophunt": "tuli<PERSON><PERSON><PERSON>u", "starrails": "alaf<PERSON><PERSON><PERSON>", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "taalofaaletauatoa", "pointandclick": "tusimaona", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "emily<PERSON>e", "indivisible": "efagatasia", "freeside": "autotautoatoa", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "ituamaicyberpunk", "deathroadtocanada": "al<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladium": "palatiuma", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON>", "fireemblem": "afiemblem", "genshinimpact": "genshinimpact", "geosupremancy": "fa<PERSON><PERSON><PERSON><PERSON><PERSON>", "persona5": "persona5", "ghostoftsushima": "aituotsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "sauamanu", "nier": "nier", "dothack": "<PERSON><PERSON><PERSON>", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "taalotevaefaateine", "tacticalrpg": "rpgtauakiga", "mahoyo": "mahoyo", "animegames": "taʻalogaanime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "p<PERSON><PERSON><PERSON><PERSON>", "princessconnect": "fesootaigamasiosina<PERSON>", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "pofuatagata", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valo<PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "taaloga", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "taaloga_komipiuta", "mlg": "mlg", "leagueofdreamers": "vasegataomoemiti", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dreamhack": "mitiaiga", "gaimin": "taalo", "overwatchleague": "ligaoverwatch", "cybersport": "taaloga_komepiuta", "crazyraccoon": "anufema<PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "taalōriot", "eracing": "t<PERSON><PERSON><PERSON>", "brasilgameshow": "taalogatamus<PERSON><PERSON><PERSON><PERSON>", "valorantcompetitive": "valo<PERSON><PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON>", "left4dead": "uaina4mate", "left4dead2": "mate4oti2", "valve": "valve", "portal": "poipoi", "teamfortress2": "teamfortress2", "everlastingsummer": "taumagāpēnā<PERSON>ʻavavau", "goatsimulator": "simulator<PERSON><PERSON>i", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "saotonumaloloto", "transformice": "transformice", "justshapesandbeats": "nauaiitusimagalemeasaʻasaʻa", "battlefield4": "battlefield4", "nightinthewoods": "poimalaivaomatua", "halflife2": "halflife2", "hacknslash": "taiaemaavae", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanias", "overcooked": "kukatele", "interplanetary": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7a2a", "deadcells": "selutamate", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "lua", "stray": "sē", "battlefield": "malaeotaua", "battlefield1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swtor": "swtor", "fallout2": "fallout2", "uboat": "vaalalo", "eyeb": "mata", "blackdesert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "talagataelevavae", "partyhard": "mitiamitivalevale", "hardspaceshipbreaker": "faamalepemeavaalele", "hades": "hades", "gunsmith": "faimea", "okami": "<PERSON>ami", "trappedwithjester": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dinkum": "moni", "predecessor": "muamua", "rainworld": "<PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON>", "colonysim": "talafasikolone", "noita": "noita", "dawnofwar": "puleg<PERSON>alo<PERSON>ua", "minionmasters": "minionmasters", "grimdawn": "pogese<PERSON><PERSON><PERSON><PERSON>", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "tigaloloto", "datingsims": "taalōgafaatusa", "yaga": "<PERSON><PERSON>ia", "cubeescape": "sosicube", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "talofou", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "defconmamafa", "kenopsia": "kenopsia", "virtualkenopsia": "kenopsiafaafoliga", "snowrunner": "taavalesilika", "libraryofruina": "f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "taalavelavaleleaitusiu<PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "alas<PERSON>", "kenabridgeofspirits": "kenatalat<PERSON>losiagaga", "placidplasticduck": "at<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "battlebit": "tauasabit", "ultimatechickenhorse": "solitusimoamoa", "dialtown": "talanoaifale", "smileforme": "ataatuimaimoaʻu", "catnight": "poa<PERSON><PERSON>", "supermeatboy": "tamamalositino", "tinnybunny": "lapititiiti", "cozygrove": "togalauaofiafia", "doom": "malaia", "callofduty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "tuaenga", "pubg": "pubg", "callofdutyzombies": "callofdutysaualiʻi", "apex": "tumutumu", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "taalofafarcrygames", "paladins": "paladins", "earthdefenseforce": "vaegaeseteeleeleiga", "huntshowdown": "tulimanusimoa", "ghostrecon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grandtheftauto5": "grandtheftauto5", "warz": "taua", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "fasiotil<PERSON>", "joinsquad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "ososegalosaneone", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "tagatasasioti3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "maliumate", "b4b": "m4m", "codwarzone": "tauasaili", "callofdutywarzone": "talotauamalae", "codzombies": "codizombie", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "vaevaegatupu2", "killzone": "sonataua", "helghan": "hel<PERSON>", "coldwarzombies": "tauamaluluzombi", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosscode": "<PERSON><PERSON><PERSON><PERSON>", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "fanaelite", "modernwarfare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "tua<PERSON><PERSON>", "owerwatch": "ower<PERSON>", "rtype": "ituaigafausaga", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "<PERSON><PERSON><PERSON>lam<PERSON>", "worldofwarships": "vaatauotaua", "back4blood": "toe4toto", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "tagataotiotauga", "masseffect": "masseffect", "systemshock": "teteesionaiga", "valkyriachronicles": "valkyriachronicles", "specopstheline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killingfloor2": "fasiotifiloa2", "cavestory": "talacavern", "doometernal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "centuryageofashes": "senetulimupogofiailoa", "farcry4": "farcry4", "gearsofwar": "ta<PERSON><PERSON><PERSON><PERSON>", "mwo": "mwo", "division2": "vaevaeluaga2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "tuputupuasef<PERSON>", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "tauaifaaonaponei2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "<PERSON><PERSON><PERSON><PERSON>", "crossfire": "feoloai", "atomicheart": "fatulo<PERSON>", "blackops3": "blackops3", "vampiresurvivors": "vampiresurvivors", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "saolotonuaga", "battlegrounds": "ma<PERSON>efaut<PERSON><PERSON>", "frag": "ta<PERSON><PERSON>", "tinytina": "laititinatina", "gamepubg": "taaloga_pubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "taalafps", "convertstrike": "<PERSON><PERSON><PERSON><PERSON>a", "warzone2": "taualagataone2", "shatterline": "liuliua", "blackopszombies": "atasiuliotu", "bloodymess": "<PERSON><PERSON><PERSON>", "republiccommando": "vaegakaurepublic", "elitedangerous": "elitedangerous", "soldat": "solata", "groundbranch": "<PERSON><PERSON><PERSON>", "squad": "<PERSON><PERSON>ga", "destiny1": "taimi1", "gamingfps": "taalogafps", "redfall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubggirl": "pubgteine", "worldoftanksblitz": "laleigatanikipuupuu", "callofdutyblackops": "callofdutyblackops", "enlisted": "faa<PERSON><PERSON><PERSON>na", "farlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tamaitiitiina<PERSON>", "halo2": "halo2", "payday2": "asotupe2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgiukareina", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "<PERSON><PERSON><PERSON><PERSON>", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "ufamamago", "ghostcod": "a<PERSON><PERSON><PERSON><PERSON>", "csplay": "csplay", "unrealtournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "taaloga_codm", "borderlands2": "borderlands2", "counterstrike": "tauesea", "cs2": "cs2", "pistolwhip": "faasi<PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "talofa", "killingfloor": "falegaosipepa", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "pae<PERSON><PERSON><PERSON>a", "remnant": "toega", "azurelane": "azurelane", "worldofwar": "lalem<PERSON>lē<PERSON>ua", "gunvolt": "gunvolt", "returnal": "toe_foi_mai", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "tagataatama<PERSON>", "quake2": "quake2", "microvolts": "microvolts", "reddead": "<PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "taualogaetolu", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "vasaosāitaaga", "rust": "<PERSON>isi", "conqueronline": "faʻamalosiigaʻonauga", "dauntless": "leofefe", "warships": "vaatau", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "<PERSON><PERSON><PERSON><PERSON>", "flightrising": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recroom": "<PERSON><PERSON><PERSON>", "legendsofruneterra": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2": "pso2", "myster": "misite<PERSON>", "phantasystaronline2": "phantasystaronline2", "maidenless": "leaunoa", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "laleigaotane", "crossout": "tipiese", "agario": "agario", "secondlife": "oluasoifua", "aion": "aion", "toweroffantasy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netplay": "taaloiniteneti", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaituaiga", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "onlineasitoa", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "pala<PERSON>a", "newworld": "lalolaga_fou", "blackdesertonline": "blackdesertonline", "multiplayer": "<PERSON><PERSON><PERSON>ofaatas<PERSON>", "pirate101": "seilā101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "tauasaolelafuainealitaue2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "talanoagilisi3d", "nostale": "le<PERSON><PERSON>", "tauriwow": "tauriuē", "wowclassic": "wowclassic", "worldofwarcraft": "lale<PERSON>ole<PERSON>ua", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "lefulefiogaafoafoaga", "riotmmo": "riotmmo", "silkroad": "alatauvaitau", "spiralknights": "spiralknights", "mulegend": "talamatagafainenefu", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "perofetaaraakona", "grymmo": "grymmo", "warmane": "vevela", "multijugador": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelsonline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseituaiga", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "tu<PERSON><PERSON><PERSON><PERSON>", "blueprotocol": "blueprotocol", "perfectworld": "lalosaoatoatoa", "riseonline": "tulaipaepae", "corepunk": "corepunk", "adventurequestworlds": "talagatuumaloloa", "flyforfun": "<PERSON><PERSON>asa<PERSON>", "animaljam": "animaljam", "kingdomofloathing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "taula<PERSON><PERSON><PERSON>", "mortalkombat": "<PERSON><PERSON><PERSON><PERSON>i", "streetfighter": "<PERSON><PERSON><PERSON>gaala", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "talagiltygia", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "tauaala6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "alao<PERSON><PERSON>le", "mkdeadlyalliance": "m<PERSON><PERSON>asa<PERSON><PERSON>", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "let<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "p<PERSON>seiteragona", "retrofightinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blasphemous": "paia", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "faatauvaamamafa", "mugen": "mugen", "warofthemonsters": "tauafaapuaa", "jogosdeluta": "taalogataua", "cyberbots": "pai<PERSON><PERSON><PERSON><PERSON><PERSON>", "armoredwarriors": "fit<PERSON><PERSON>ume<PERSON>", "finalfight": "tauamu<PERSON><PERSON>", "poweredgear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatemup": "faa<PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "taalofaatau", "killerinstinct": "uigafalepula", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON>eagaga", "chivalry2": "chivalry2", "demonssouls": "a<PERSON><PERSON><PERSON>u", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "tuafatiehollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "taalotosilika", "silksongnews": "talasikisong", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON><PERSON>", "typelumina": "taipil<PERSON>na", "evolutiontournament": "faatauvagaevolution", "evomoment": "taimilalaga", "lollipopchainsaw": "lolipapisau", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talanoabers<PERSON>", "bloodborne": "tiga<PERSON><PERSON>", "horizon": "ogatotonu", "pathofexile": "<PERSON><PERSON><PERSON><PERSON>", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "tiga<PERSON><PERSON>", "uncharted": "leimeasaoao", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infamous": "tautaua", "playstationbuddies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "failuaʻaisomnium", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "ataotau", "gris": "gris", "trove": "oloa", "detroitbecomehuman": "detroitfaitagata", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "tautogataumaliga", "lspdfr": "lspdfr", "shadowofthecolossus": "ataofilepupula", "crashteamracing": "taavagafaatauvaalima<PERSON>ke", "fivepd": "limataifagafulu", "tekken7": "tekken7", "devilmaycry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "<PERSON><PERSON><PERSON><PERSON>", "samuraiwarriors": "toasa<PERSON>i", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "laumata", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "faaoso", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "atufiagatuāalailea2feagaiga", "pcsx2": "pcsx2", "lastguardian": "taus<PERSON>elei", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "meaf<PERSON>fiaaga", "warharmmer40k": "warhammer40k", "fightnightchampion": "paa<PERSON><PERSON><PERSON><PERSON><PERSON>u", "psychonauts": "psychonauts", "mhw": "mhw", "princeofpersia": "perinisioperesia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "tauanofo", "dontstarvetogether": "auafaia<PERSON><PERSON>le<PERSON>a", "ori": "ori", "spelunky": "p<PERSON><PERSON><PERSON>", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "fetaulagataulaga", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "siketi3", "houseflipper": "f<PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ligaotupumaalo", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "tvleaga", "skycotl": "skycotl", "erica": "erica", "ancestory": "tupuaga", "cuphead": "cuphead", "littlemisfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sallyface": "<PERSON><PERSON><PERSON><PERSON>", "franbow": "franbow", "monsterprom": "promtagata", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "afi", "outerwilds": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "agapeopele", "duckgame": "taalopato", "thestanleyparable": "thestanleyparable", "towerunite": "faatasiletaua", "occulto": "lilo", "longdrive": "a<PERSON><PERSON><PERSON><PERSON>", "satisfactory": "malie", "pluviophile": "<PERSON>af<PERSON><PERSON><PERSON><PERSON>", "underearth": "<PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "fai<PERSON><PERSON>aga<PERSON>", "darkdome": "f<PERSON><PERSON><PERSON><PERSON>", "pizzatower": "f<PERSON><PERSON><PERSON><PERSON>", "indiegame": "taalogalapoaa", "itchio": "itchio", "golfit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truthordare": "faamaonipoefaamalaga", "game": "taaloga", "rockpaperscissors": "papa<PERSON><PERSON>aa<PERSON><PERSON>", "trampoline": "ta<PERSON><PERSON><PERSON>i", "hulahoop": "sipatauavale", "dare": "<PERSON>aa<PERSON><PERSON><PERSON>", "scavengerhunt": "sailiesaili", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "filisenamepā", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "fasi<PERSON><PERSON><PERSON>", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "ta<PERSON><PERSON><PERSON><PERSON>tama", "freegame": "taalologafua", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "taaloga", "mahjong": "mahjong", "jeux": "taaloga", "simulationgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "<PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "ta<PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "<PERSON><PERSON>loumataifea", "boredgames": "taalogof<PERSON>", "oyun": "oyun", "interactivegames": "taalogatuufetaʻi", "amtgard": "amtgard", "staringcontests": "ve<PERSON><PERSON><PERSON><PERSON>", "spiele": "taaloga", "giochi": "taaloga", "geoguessr": "geoguessr", "iphonegames": "taaloga_iphone", "boogames": "taaloboo", "cranegame": "taalokerane", "hideandseek": "lafia<PERSON><PERSON><PERSON>", "hopscotch": "sikalulu", "arcadegames": "taalokomea", "yakuzagames": "<PERSON><PERSON>ya<PERSON><PERSON>", "classicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mindgames": "<PERSON>alotomafau<PERSON><PERSON>", "guessthelyric": "faa<PERSON><PERSON><PERSON><PERSON><PERSON>", "galagames": "taaloga", "romancegame": "taalogafiafia", "yanderegames": "taaloyandere", "tonguetwisters": "utafaigata", "4xgames": "taaloga4x", "gamefi": "gamefi", "jeuxdarcades": "taaloga_arcade", "tabletopgames": "taalogata<PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "taaloga90", "idareyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "monivsega", "playgames": "taa<PERSON><PERSON>", "gameonline": "taaloogaituaiga", "onlinegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosonline": "taalogailugaoleinitaneti", "writtenroleplay": "tusitusitaʻaloga", "playaballgame": "taaaloipolo", "pictionary": "atapikitoania", "coopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jenga": "i<PERSON>a", "wiigames": "ta<PERSON>ii", "highscore": "sikoamaualuga", "jeuxderôles": "taaloga", "burgergames": "taalogapiki", "kidsgames": "taalosaatamaiti", "skeeball": "<PERSON><PERSON><PERSON><PERSON>", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "taaloga_fesili", "gioco": "gioco", "managementgame": "ta<PERSON><PERSON>it<PERSON><PERSON>", "hiddenobjectgame": "taʻalogameasanat<PERSON>", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "taaloga_formula1", "citybuilder": "fauaʻ<PERSON>", "drdriving": "ta<PERSON><PERSON><PERSON>", "juegosarcade": "taaloga_arcade", "memorygames": "taalogamanatua", "vulkan": "vulkan", "actiongames": "taalogafaatino", "blowgames": "ta<PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "ma<PERSON><PERSON><PERSON>polo", "oldgames": "taalogatanamua", "couchcoop": "no<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "taaloga", "lasergame": "taalogalasera", "imessagegames": "taalogaimessage", "idlegames": "taalofaaga", "fillintheblank": "faatumutaia", "jeuxpc": "taalogakomepiuta", "rétrogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logicgames": "taalogatamasino", "japangame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "faa<PERSON><PERSON><PERSON><PERSON>", "subwaysurf": "fepisinofesi", "jeuxdecelebrite": "taalogatamaua", "exitgames": "taalogaese", "5vs5": "5vs5", "rolgame": "taaloga", "dashiegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameandkill": "taalogamamate", "traditionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "taalogafps", "textbasedgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "talatuafaasolopito", "thiefgame": "v<PERSON><PERSON>lagi", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "t<PERSON><PERSON><PERSON><PERSON><PERSON>a", "tischfußball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spieleabende": "pofaigataaloga", "jeuxforum": "taaloga", "casualgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "tifuga", "escapegames": "taalofeiloai", "thiefgameseries": "vagabulataaloga", "cranegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "játék": "taaloga", "bordfodbold": "laute<PERSON><PERSON><PERSON>", "jogosorte": "ta<PERSON><PERSON><PERSON>", "mage": "ta<PERSON><PERSON><PERSON>u", "cargames": "taalogataavale", "onlineplay": "taalōiluga", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON>oa<PERSON><PERSON><PERSON>", "pursebingos": "<PERSON><PERSON><PERSON><PERSON>", "randomizer": "faa<PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "taalogakomepiuta", "socialdeductiongames": "taalotāmāsāgaeseseafuamanatu", "dominos": "tomino", "domino": "tomino", "isometricgames": "taalougaisometric", "goodoldgames": "taalogatinamua", "truthanddare": "faʻamaonimeatuatuauʻiga", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "sailiesaili", "jeuxvirtuel": "taalogakomepiuta", "romhack": "romhack", "f2pgamer": "<PERSON><PERSON><PERSON><PERSON>", "free2play": "faataufaaatoa", "fantasygame": "taalogataifale", "gryonline": "gryituaiga", "driftgame": "taaloloataavale", "gamesotomes": "<PERSON><PERSON>ata<PERSON><PERSON>", "halotvseriesandgames": "halotvseriesmagames", "mushroomoasis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anythingwithanengine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodgamegiving": "taalogalelei", "jugamos": "taalo", "lab8games": "lab8games", "labzerogames": "taalologamesefualē", "grykomputerowe": "taaloga", "virgogami": "virgogami", "gogame": "aluita", "jeuxderythmes": "taal<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "taalologaititi", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "alofaiaeoeiaataaloga", "gamemodding": "faa<PERSON><PERSON><PERSON>ala<PERSON>", "crimegames": "taalogasolitulafono", "dobbelspellen": "taaloga", "spelletjes": "taaloga", "spacenerf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "taaloga_faaata", "singleplayer": "o<PERSON><PERSON>", "coopgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamed": "taaloga", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "ta<PERSON>aa<PERSON>u", "kingdiscord": "tupuudiscord", "scrabble": "scrabble", "schach": "<PERSON><PERSON><PERSON><PERSON>", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "taugafaʻamacovid", "camelup": "kamela_i_luga", "monopolygame": "taalogamonopoli", "brettspiele": "taaloga_laupapa", "bordspellen": "taaloga_laupapa", "boardgame": "taalagalaualuga", "sällskapspel": "taaloga", "planszowe": "taaloga_laupapa", "risiko": "risiko", "permainanpapan": "<PERSON><PERSON><PERSON>upapa", "zombicide": "atiaʻi", "tabletop": "laulaga", "baduk": "baduk", "bloodbowl": "<PERSON><PERSON><PERSON><PERSON>", "cluedo": "silailo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "taaloipapa", "connectfour": "faa<PERSON>sootaiefa", "heroquest": "heroquest", "giochidatavolo": "taaloga_laulau", "farkle": "farkle", "carrom": "kalama", "tablegames": "taalosalatē<PERSON><PERSON>", "dicegames": "taalogalaisa<PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON>upapa", "jocuridesocietate": "taalosoga", "deskgames": "taaloga_laulau", "alpharius": "alpharius", "masaoyunları": "ta<PERSON><PERSON>mas<PERSON>", "marvelcrisisprotocol": "marveltauivaletu<PERSON><PERSON>", "cosmicencounter": "fetaiailevaolomiga", "creationludique": "faiaaogāfiafia", "tabletoproleplay": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cardboardgames": "taalogapepa", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "taaloga_switchboard", "infinitythegame": "taalogatuusaoleaga", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "paʻumasiʻi", "társas": "tausaga", "juegodemesa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "planszówki": "taaloga_laulau", "rednecklife": "oloagafaifaatoa", "boardom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "applestoapples": "atuileiat<PERSON>", "jeudesociété": "taaloga", "gameboard": "ta<PERSON>ila<PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "taaloga", "twilightimperium": "twilightimperium", "horseopoly": "solopoli", "deckbuilding": "faa<PERSON>lag<PERSON><PERSON>", "mansionsofmadness": "falepapalaginepepelo", "gomoku": "gomoku", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "ataaliiobrimstone", "kingoftokyo": "tuputokyo", "warcaby": "taala<PERSON>al<PERSON>", "táblajátékok": "taaloga_laulau", "battleship": "vaatau", "tickettoride": "tikitifealuai", "deskovehry": "taalogalamiga", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON>upapa", "stolníhry": "taaloga_laulau", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "taalaga<PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "taaloga", "starwarslegion": "starwarslegion", "gochess": "<PERSON><PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "taaloga_laulau", "terraria": "terraria", "dsmp": "dsmp", "warzone": "onasagataua", "arksurvivalevolved": "arksurvivalevolved", "dayz": "aso", "identityv": "identityv", "theisle": "lemotu", "thelastofus": "oleulugaeoitagata", "nomanssky": "<PERSON><PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eco": "eco", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "faufa<PERSON><PERSON><PERSON><PERSON>", "daysgone": "<PERSON><PERSON><PERSON>", "fobia": "fobia", "witchit": "faa<PERSON><PERSON><PERSON>u", "pathologic": "<PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7asm", "thelongdark": "le<PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "ata", "grounded": "nofoifale", "stateofdecay2": "tulagaootiaiga2", "vrising": "vrising", "madfather": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarve": "<PERSON><PERSON><PERSON>", "eternalreturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathoftitans": "alasaurusotitans", "frictionalgames": "taalofafiamana<PERSON>", "hexen": "mataitu", "theevilwithin": "lemeaamatagāileaga", "realrac": "faʻ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thebackrooms": "lefaleituauma", "backrooms": "potu_i_tua", "empiressmp": "empiressmp", "blockstory": "talafaapoloka", "thequarry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "otigasemulifefiloi", "thewalkingdeadgame": "letaalagatuotitagata", "wehappyfew": "moeitaunaitino", "riseofempires": "tuputupuoatunuutetele", "stateofsurvivalgame": "taalogatu<PERSON>up<PERSON>uloa", "vintagestory": "talafaasolopea", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "breathedge": "man<PERSON><PERSON><PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "olatauvigawestlend", "beastsofbermuda": "faaonapolopolomaitagata<PERSON>", "frostpunk": "aisaaupunk", "darkwood": "togavao", "survivalhorror": "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil": "taugamataʻutia", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeaftergame": "oluagalemeataliaga", "survivalgames": "taalogatataualola", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "polokekimeaola", "kuon": "kuon", "cryoffear": "tag<PERSON><PERSON><PERSON><PERSON>", "raft": "fola", "rdo": "rdo", "greenhell": "togavao", "residentevil5": "residentevil5", "deadpoly": "matepoli", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "v<PERSON><PERSON>ua", "littlenightmares2": "ioalailianaasipolapupu2", "signalis": "faailo", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "taalogameataata", "outlasttrials": "faataitaigataua", "alienisolation": "vaa<PERSON><PERSON><PERSON>", "undawn": "aofao", "7day2die": "7asofitu2oti", "sunlesssea": "sa<PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "sopravvivenza", "propnight": "poa<PERSON><PERSON><PERSON>", "deadisland2": "malaailetumotu2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathverse": "<PERSON><PERSON><PERSON><PERSON>", "cataclysmdarkdays": "asopogeseipog<PERSON>", "soma": "soma", "fearandhunger": "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "<PERSON><PERSON><PERSON><PERSON>limai", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "olopuaomotaimi3", "aloneinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "tuufaaigatuai", "projectnimbusgame": "taalogatuiaganimbus", "eternights": "pōmalōlō", "craftopia": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "theoutlasttrials": "lematauinaf<PERSON><PERSON><PERSON><PERSON><PERSON>", "bunker": "faletalo", "worlddomination": "pulepuleilelalolagi<PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "oficioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "fasi<PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammer40kcrush": "warhammer40kcrush", "wh40": "wh40", "warhammer40klove": "alofa40kwarhammer", "warhammer40klore": "talatuufaasololoawarhammer40k", "warhammer": "tauupega", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "outeal<PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "outealotualoaivindicare", "iloveassasinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templovenenum": "vevel<PERSON><PERSON>ilo<PERSON>", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "tausagaolemalo", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "elua", "wingspan": "aupito", "terraformingmars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofmightandmagic": "to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "btd6": "btd6", "supremecommander": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofmythology": "tausaga<PERSON><PERSON><PERSON><PERSON>", "args": "args", "rime": "aisa", "planetzoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outpost2": "nofoagaituumalo2", "banished": "faa<PERSON><PERSON><PERSON>", "caesar3": "caesar3", "redalert": "pogisa<PERSON>o", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "puletao<PERSON>mauf<PERSON>ton<PERSON>", "warcraft3": "warcraft3", "eternalwar": "tauaefaavavau", "strategygames": "taalogafaataitai", "anno2070": "tausaga2070", "civilizationgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization4": "tusilasilameaaga4", "factorio": "factorio", "dungeondraft": "fanualemausamauga", "spore": "sipo", "totalwar": "tauaatoa", "travian": "travian", "forts": "fuaefa", "goodcompany": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "atunuu", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "vite<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "mogaaliiotaua", "realtimestrategy": "taalogataua<PERSON><PERSON><PERSON>", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "malomaloelua", "eu4": "eu4", "vainglory": "mitamitavale", "ww40k": "ww40k", "godhood": "atua", "anno": "masalo", "battletech": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "vasegaalgebramamanadave", "plagueinc": "taʻu<PERSON><PERSON>a", "theorycraft": "theorycraft", "mesbg": "mesbg", "civilization3": "siavilaisauni3", "4inarow": "4itutaloto", "crusaderkings3": "crusaderkings3", "heroes3": "toa3", "advancewars": "tauafaalualu", "ageofempires2": "tausagaolemalomaloese2", "disciples2": "tausoa2", "plantsvszombies": "<PERSON>auv<PERSON><PERSON><PERSON><PERSON>", "giochidistrategia": "taaloga_tautaua", "stratejioyunları": "taalogafaataitai", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "ta<PERSON><PERSON>of<PERSON><PERSON>a", "dinosaurking": "tup<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "faatomagaloloto", "heartsofiron4": "lototauamea4", "companyofheroes": "auatausitoa", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "kusikusimoa", "phobies": "fefefe", "phobiesgame": "taalofegamafefe", "gamingclashroyale": "taaloclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "vaeseituaautū", "turnbased": "<PERSON><PERSON><PERSON><PERSON>", "bomberman": "<PERSON><PERSON><PERSON><PERSON>", "ageofempires4": "tausagaomalotupuaga4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "tup<PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "togafaifaʻ<PERSON>ulāit<PERSON>", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strategy", "popfulmail": "im<PERSON><PERSON><PERSON><PERSON>", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "masterduel", "dysonsphereprogram": "polokalamatelepaaloanuu", "transporttycoon": "pu<PERSON>le<PERSON>malagaaiga", "unrailed": "faatoilaloina", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "vaevaealā<PERSON>ā", "uplandkingdoms": "maloupuupuga", "galaxylife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespire", "battlecats": "pusita<PERSON>", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "taufesilima", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "manaomialetelepesecarbon", "realracing3": "taavaletagaiema3", "trackmania": "trackmania", "grandtourismo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gt7": "gt7", "simsfreeplay": "taalogasimsefaatoa", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "galosims4", "fnaf": "fnaf", "outlast": "tumumau", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "alicemalumaifoisia", "darkhorseanthology": "tus<PERSON><PERSON><PERSON><PERSON>", "phasmophobia": "<PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "polimaifaleatafreddy", "saiko": "saiko", "fatalframe": "<PERSON><PERSON><PERSON><PERSON>", "littlenightmares": "talitaliititipomusu", "deadrising": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "nofoaale", "deadisland": "nuumate", "litlemissfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectzero": "polokalamasaini", "horory": "<PERSON><PERSON>ia", "jogosterror": "taaloter<PERSON><PERSON>", "helloneighbor": "talo<PERSON>lelag<PERSON>", "helloneighbor2": "talofaleteimea2", "gamingdbd": "taaloga_dbd", "thecatlady": "lefafineposipusi", "jeuxhorreur": "taalogamatautia", "horrorgaming": "taalog<PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "katapaikikala", "lor": "lor", "euchre": "iuka", "thegwent": "legwent", "legendofrunetera": "ta<PERSON><PERSON><PERSON>or<PERSON><PERSON>", "solitaire": "pasese", "poker": "pokā", "hearthstone": "talimalo", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "kipatogafao", "cardtricks": "togafiti_kata", "playingcards": "ta<PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "femaual<PERSON>", "gwent": "gwent", "metazoo": "meta<PERSON><PERSON>", "tradingcards": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "kata<PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sportscards": "kata<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "talotalomakasipep<PERSON>", "duellinks": "duellinks", "spades": "sipati", "warcry": "<PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "letetee", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "kata<PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "<PERSON><PERSON><PERSON><PERSON>", "yugiohgame": "taalogiyugioh", "darkmagician": "togafaituaitu", "blueeyeswhitedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohsili", "briscas": "briscas", "juegocartas": "taalokadi", "burraco": "bur<PERSON>o", "rummy": "rami", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "lua", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "taaligakata", "mtgjudge": "mtgjudge", "juegosdecartas": "taaloga_kata", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "taalokata", "carteado": "carteado", "sueca": "sueca", "beloteonline": "taaloibelo", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON>aga<PERSON>", "battlespiritssaga": "talatalafaaituaaga", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON>", "žolíky": "kaatalipese", "facecard": "foligafotofuatamai", "cardfight": "fufupepa", "biriba": "biriba", "deckbuilders": "faus<PERSON>pal<PERSON>", "marvelchampions": "toapuupuu", "magiccartas": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "unicorn<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberse": "saipapese", "classicarcadegames": "taalogamasinialaelae", "osu": "osu", "gitadora": "gitadora", "dancegames": "taalogesiva", "fridaynightfunkin": "afiafiafipareseitaʻaloga", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "polokalamimirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "kitarakitara", "clonehero": "taalogeapitoagit<PERSON>", "justdance": "siva", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "sa<PERSON><PERSON><PERSON><PERSON><PERSON>", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "taalogasivasiva", "rhythmgamer": "taalopese", "stepmania": "stepmania", "highscorerythmgames": "sikoapitotelemusika", "pkxd": "pkxd", "sidem": "sidemea", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "falemusika", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "foma<PERSON><PERSON>", "cubing": "kiu<PERSON>ga", "wordle": "wordle", "teniz": "tenisi", "puzzlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spotit": "<PERSON><PERSON>", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "polokadoku", "logicpuzzles": "taimilosau", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "taaloga_faaloloto_ma<PERSON>au", "rubikscube": "tapulaarubik", "crossword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motscroisés": "motscroisés", "krzyżówki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nonogram": "nonogram", "bookworm": "<PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "faalagilag<PERSON>", "indovinello": "fesili", "riddle": "tupua", "riddles": "taufetuli", "rompecabezas": "<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "totonu", "angrybirds": "manuita", "escapesimulator": "soseasimulator", "minesweeper": "lame<PERSON><PERSON>", "puzzleanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crosswordpuzzles": "tafaoga_uputiputiga", "kurushi": "k<PERSON>hi", "gardenscapesgame": "taalogamegardenscapes", "puzzlesport": "taalogaenigma", "escaperoomgames": "taalofalepoloka", "escapegame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dpuzzle": "3dpuzzle", "homescapesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wordsearch": "sue<PERSON><PERSON>", "enigmistica": "enigmistica", "kulaworld": "la<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myst": "lilo", "riddletales": "talafagafaga", "fishdom": "iafagamoana", "theimpossiblequiz": "lesutautoalesemasagoa", "candycrush": "candycrush", "littlebigplanet": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "match3puzzle": "taalofetuuga3", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "uigaese", "rubikcube": "tāpulasaʻo", "cuborubik": "taipumea", "yapboz": "yapboz", "thetalosprinciple": "letalasiprinciple", "homescapes": "faleoata", "puttputt": "<PERSON><PERSON><PERSON>", "qbert": "qbert", "riddleme": "faa<PERSON><PERSON><PERSON><PERSON>", "tycoongames": "taalogatycoon", "cubosderubik": "cubosderubik", "cruciverba": "taaloga_faatusa_upu", "ciphers": "tipatipa", "rätselwörter": "<PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "fofovaega", "turnipboy": "turnipboy", "adivinanzashot": "tauma<PERSON>losevevela", "nobodies": "tagatalelautaua", "guessing": "filifili", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia2": "syberia2", "puzzlehunt": "sailimeataga", "puzzlehunts": "sailitalamea", "catcrime": "solitamus<PERSON>usi", "quebracabeça": "ulugatete", "hlavolamy": "faalavelave", "poptropica": "poptropica", "thelastcampfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autodefinidos": "autodefinidos", "picopark": "picopark", "wandersong": "fesi<PERSON><PERSON><PERSON><PERSON>", "carto": "pese", "untitledgoosegame": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "uluf<PERSON><PERSON><PERSON>", "limbo": "limbo", "rubiks": "rubiks", "maze": "fetaui", "tinykin": "tamaitiitiiti", "rubikovakostka": "rubikovakostka", "speedcube": "kiuavaivave", "pieces": "tutuga", "portalgame": "taalogaportala", "bilmece": "bilmece", "puzzelen": "faatāfafaga", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "aitalauga", "cubomagico": "taʻalotapuʻega", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "faavalevalelaufanua", "monopoly": "pasiolo", "futurefight": "ta<PERSON><PERSON><PERSON><PERSON>a", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "o<PERSON><PERSON><PERSON><PERSON>", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "fetuuoloa", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "fetual<PERSON>i", "stateofsurvival": "tūlagaopuʻipuʻiga", "mycity": "<PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "la<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "taualeleitobokuniloto", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "taualedokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "tauavaeloafipaupau", "honkaiimpact": "honkaiimpact", "soccerbattle": "taalopolosiva", "a3": "a3", "phonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "filifiligaatupu", "guardiantales": "talaogatuai", "petrolhead": "tagatafiauumu", "tacticool": "tautikamanaia", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "leitalomalamala<PERSON>", "craftsman": "tufuga", "supersus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "faailo", "wordfeud": "upufeud", "bedwars": "tauamoe", "freefire": "freefire", "mobilegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "faa<PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "taliofia<PERSON>ai<PERSON>", "pjsekai": "pjsekai", "mysticmessenger": "feal<PERSON><PERSON><PERSON><PERSON>", "callofdutymobile": "talagatauamobile", "thearcana": "<PERSON><PERSON><PERSON><PERSON>", "8ballpool": "8ballpool", "emergencyhq": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "alipiona", "hayday": "as<PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "luluimatetete", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "tausagaprincess", "beatstar": "pese<PERSON><PERSON>", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "alofarecordpoto", "androidgames": "taaloapuipuiandroid", "criminalcase": "matagatafaasolitulafono", "summonerswar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cookingmadness": "faiaigamea", "dokkan": "dokkan", "aov": "aov", "triviacrack": "taalogatautalatala", "leagueofangels": "ligaagelu", "lordsmobile": "lordsmobile", "tinybirdgarden": "fualaaumanumealaitiiti", "gachalife": "gachalife", "neuralcloud": "neuroalafu", "mysingingmonsters": "auofaipese", "nekoatsume": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "tautaurobots", "mirrorverse": "fa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertalesa<PERSON><PERSON>", "futime": "taimofiafia", "antiyoy": "faʻafeagai", "apexlegendmobile": "apexlegendmobile", "ingress": "ul<PERSON>ale", "slugitout": "fufui<PERSON>o", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "tauaveavematautia", "petpals": "uomeaman<PERSON><PERSON>", "gameofsultans": "taalotupu", "arenabreakout": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfy": "luko", "runcitygame": "tamoesaaliaiga", "juegodemovil": "taalogafeaveai", "avakinlife": "olaga_avakini", "kogama": "kogama", "mimicry": "faataitai", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "taavalegafefe", "grandchase": "grandchase", "bombmebrasil": "bombmeperesile", "ldoe": "ldoe", "legendonline": "talaonline", "otomegame": "taalogataaloga", "mindustry": "mindustry", "callofdragons": "valaauatanimo", "shiningnikki": "nik<PERSON><PERSON><PERSON>", "carxdriftracing2": "taavalepaeese2", "pathtonowhere": "alailem<PERSON>", "sealm": "sealm", "shadowfight3": "fuaummalu3", "limbuscompany": "kamu<PERSON><PERSON><PERSON><PERSON>", "demolitionderby3": "taavalefalemea3", "wordswithfriends2": "upumauo2", "soulknight": "<PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "purrfecttale", "showbyrock": "showbyrock", "ladypopular": "<PERSON><PERSON><PERSON>amanaia", "lolmobile": "taliatalimatasaele", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiresandpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empirespuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "<PERSON><PERSON><PERSON>ap<PERSON><PERSON>", "garticphone": "garticphone", "battlegroundmobileind": "tauagamobileinidia", "fanny": "nofoaga", "littlenightmare": "itiitimoemoeāleaga", "aethergazer": "aethergazer", "mudrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "lotoimat<PERSON><PERSON><PERSON><PERSON>", "eversoul": "agagaefaavavau", "gunbound": "gunbound", "gamingmlbb": "taalologamemlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "atamamatesemalaga", "eveechoes": "eveechoes", "jogocelular": "taalogatelefonifeaveave", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "tauaalaauala", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "ta<PERSON>ab<PERSON><PERSON>", "girlsfrontline": "teinetauvaeloloto", "jurassicworldalive": "oloola<PERSON>", "soulseeker": "sailiagaga", "gettingoverit": "aloesegalilo", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "ta<PERSON>ole<PERSON><PERSON>", "carxdriftracingonline": "taavalepaeeseeseeituaiga", "jogosmobile": "taalomobile", "legendofneverland": "talanoeverland", "pubglite": "pubglite", "gamemobilelegends": "taalomobilelegends", "timeraiders": "timeraiders", "gamingmobile": "taalogataelefoni<PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "malosimarvel", "thebattlecats": "pusipusita<PERSON>", "dnd": "dnd", "quest": "sailiga", "giochidiruolo": "taaloga_faatusa", "dnd5e": "dnd5e", "rpgdemesa": "rpglauala<PERSON>", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "talatagamalaga", "2300ad": "2300ad", "larp": "talafolafola", "romanceclub": "kluboalofa", "d20": "d20", "pokemongames": "taalogapokemon", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonred", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "tauaso", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "<PERSON><PERSON><PERSON><PERSON>", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "talanoa", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemon<PERSON>u<PERSON>lé", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "augarocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "pocketmonsters", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "teamystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "poke<PERSON><PERSON><PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "limamauamaua", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>e", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "tama<PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "sailitiofu", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "sese", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "taaloga_siekiate", "schaken": "taaloga_siketi", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "taalofatuivave", "jeudéchecs": "<PERSON><PERSON><PERSON><PERSON>", "japanesechess": "sapasapaiapani", "chinesechess": "siek<PERSON>es<PERSON>", "chesscanada": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON>", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "calabozosydragones", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "puleolefagogo", "tiamat": "tiamat", "donjonsetdragons": "donjonsmadragons", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "talatupualeseauovoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "pog<PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "taalosamiogaminecraftchampionship", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "<PERSON><PERSON>a", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "afimolemole", "fru": "fru", "addons": "faaopoopoga", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftfaalelei", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "vaemanuatu", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgamer": "pcgamer", "jeuxvideo": "taaloga", "gambit": "taʻaloga", "gamers": "tagata_taaloga", "levelup": "siitaga", "gamermobile": "taalosamobile", "gameover": "<PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "taaloga_komepiuta", "gamen": "taaloga", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "taaloga_komepiuta", "casualgaming": "ta<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingsetup": "setuptaaloga", "pcmasterrace": "pcmasterrace", "pcgame": "taaloga_komepiuta", "gamerboy": "tamagameboy", "vrgaming": "taalogakomipi<PERSON>", "drdisrespect": "drdisrespect", "4kgaming": "4ktaaloga", "gamerbr": "taalokompiuta", "gameplays": "taaloga", "consoleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "talalogamate", "onlinegaming": "taaloiluga", "semigamer": "gameraeseese", "gamergirls": "teinegametoga", "gamermoms": "tin<PERSON><PERSON><PERSON>a", "gamerguy": "tamaaloatagame", "gamewatcher": "matamataitaluga", "gameur": "taalo", "grypc": "grypc", "rangugamer": "taalotaalo", "gamerschicas": "<PERSON><PERSON><PERSON>", "otoge": "otoge", "dedsafio": "tusigatauvale", "teamtryhard": "teamtaumafai", "mallugaming": "tamataalalomalo", "pawgers": "pawgers", "quests": "faamoemoe", "alax": "alax", "avgn": "avgn", "oldgamer": "taalogamer", "cozygaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamelpay": "taalolepay", "juegosdepc": "taalogipc", "dsswitch": "<PERSON><PERSON><PERSON><PERSON>", "competitivegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftnewjersey": "minecraftnewjersey", "faker": "pepelo", "pc4gamers": "pc4gamers", "gamingff": "ta<PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "taaloga_komepiuta", "girlsgamer": "teinegamers", "fnfmods": "fnfmods", "dailyquest": "misi<PERSON><PERSON>o", "gamegirl": "teinetaʻaloga", "chicasgamer": "teinegamers", "gamesetup": "setiagataaloga", "overpowered": "ma<PERSON><PERSON><PERSON>", "socialgamer": "tagatavataaloga", "gamejam": "taaloga", "proplayer": "taaloop<PERSON>", "roleplayer": "tausuaga", "myteam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republicofgamers": "repupilikaotaaalo", "aorus": "aorus", "cougargaming": "saotaʻaloga", "triplelegend": "tautinogale", "gamerbuddies": "uogaming", "butuhcewekgamers": "fiaavasaafafinetaaalo", "christiangamer": "taalogakerisiano", "gamernerd": "g<PERSON><PERSON><PERSON>", "nerdgamer": "tagataatamaipataʻaloga", "afk": "afk", "andregamer": "andregamer", "casualgamer": "taalofaaleimaua", "89squad": "89squad", "inicaramainnyagimana": "faape<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "insec": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gemers": "taaloga", "oyunizlemek": "oyunizlemek", "gamertag": "<PERSON><PERSON><PERSON>ue<PERSON><PERSON><PERSON>", "lanparty": "tafaogakomi<PERSON><PERSON>", "videogamer": "videogamer", "wspólnegranie": "taalogafaatasi", "mortdog": "mortdog", "playstationgamer": "taalostationgamer", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notebookgamer": "tus<PERSON><PERSON><PERSON><PERSON><PERSON>", "protogen": "protogen", "womangamer": "tamaʻitaʻitaʻalo", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "kof<PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "fai<PERSON><PERSON>", "humanfallflat": "paoeleitagata", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "saosoloaogata", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "lāuganiteneto", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "<PERSON><PERSON><PERSON>", "switch": "faʻ<PERSON><PERSON><PERSON>", "zelda": "zelda", "smashbros": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofzelda": "tala<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "taalogaafimolemama", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariokartmaster": "mariotaavalealuga", "wii": "wii", "aceattorney": "lōiatag<PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "skychildrenofthelight", "tomodachilife": "oluusokaiga", "ahatintime": "taim<PERSON><PERSON><PERSON>i", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogames": "taaloganintendo", "thelegendofzelda": "legende_<PERSON>_zelda", "dragonquest": "talaogataisili", "harvestmoon": "ma<PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "<PERSON><PERSON><PERSON><PERSON>", "breathofthewild": "man<PERSON>ole<PERSON>omao", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "talataleaopulelee<PERSON>pusaa", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51taaloga", "earthbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tales": "faa<PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "taalaigamuielegatoa", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "fuafuaga<PERSON><PERSON><PERSON><PERSON>", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "<PERSON><PERSON><PERSON>", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "<PERSON><PERSON><PERSON><PERSON>", "vanillalol": "van<PERSON><PERSON>l", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "l<PERSON><PERSON><PERSON><PERSON>", "tốcchiến": "ta<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "talitusiauluaga", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "liikaolekenasipeini", "aatrox": "aatrox", "euw": "ue", "leagueoflegendseuw": "likaolegenesieu<PERSON>", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "aiaila", "akshan": "<PERSON><PERSON>han", "milio": "miliona", "shaco": "shaco", "ligadaslegendas": "ligaota<PERSON><PERSON><PERSON>", "gaminglol": "taalolol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "taalofanitegame", "gamingfortnite": "taalofaafortnite", "fortnitebr": "fortnitebr", "retrovideogames": "taalogevituiama", "scaryvideogames": "taalogatamatamata", "videogamemaker": "faikomupiutavitiō", "megamanzero": "megamanzero", "videogame": "taaloga", "videosgame": "taaloga", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "tauasapalefalekata", "arcades": "taʻaloga", "acnh": "acnh", "puffpals": "uopals", "farmingsimulator": "<PERSON><PERSON><PERSON>ato<PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxdeutsch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "taaloganumera", "videogamelore": "ta<PERSON>oga<PERSON>ala<PERSON>", "rollerdrome": "u<PERSON><PERSON><PERSON><PERSON>", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "<PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "taavalegatotogi", "deadspace": "alamate", "amordoce": "alofisuamalie", "videogiochi": "videogiochi", "theoldrepublic": "le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videospiele": "taaloga", "touhouproject": "touhouproject", "dreamcast": "mit<PERSON><PERSON>i", "adventuregames": "taaloga_faigamalaga", "wolfenstein": "wolfenstein", "actionadventure": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "talafaasoamata", "retrogames": "taalōgamala", "retroarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "komipiutafaavasomanuu", "retrogaming": "taalofatuai", "vintagegaming": "taalogamulu", "playdate": "ta<PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "lēsaʻotonu2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "taalolelagi", "zenlife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "<PERSON><PERSON><PERSON><PERSON>", "mystgames": "taalomisiterio", "blockchaingaming": "taalōbloka", "medievil": "<PERSON><PERSON><PERSON><PERSON>", "consolegaming": "taaloga_console", "konsolen": "konsolen", "outrun": "<PERSON><PERSON><PERSON><PERSON>", "bloomingpanic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "taalogataufaʻafefe", "monstergirlquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiant": "ulutele", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "<PERSON><PERSON><PERSON>ato<PERSON>", "juegosviejos": "taʻalogamotu", "bethesda": "bethesda", "jackboxgames": "taalogajackbox", "interactivefiction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "malosilautele", "visualnovel": "talaata", "visualnovels": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON>", "tcrghost": "tcraitu", "payday": "asotaupule", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "tausagamanaia", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "taaloga", "aestheticgames": "taalomaleaga", "novelavisual": "talanovelimata", "thecrew2": "levasao2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "taalogatuai", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "limaatua", "leafblowerrevolution": "laulafofoitigilefuafuāvela", "wiiu": "wiiu", "leveldesign": "faatulagataʻaloga", "starrail": "alaleo", "keyblade": "keyblade", "aplaguetale": "tailamaʻi", "fnafsometimes": "fnafitaimi", "novelasvisuales": "ta<PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxbrasil": "rob<PERSON><PERSON><PERSON><PERSON><PERSON>", "pacman": "pacman", "gameretro": "taalogatuai", "videojuejos": "videotaʻaloga", "videogamedates": "tafaogamemate", "mycandylove": "loualof<PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "naotualilo3", "hulkgames": "taalohulk", "batmangames": "<PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "toe<PERSON>iatu<PERSON><PERSON>", "gamstergaming": "kamatapuaa", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "faleulavavale", "crashracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "3dplatformers", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storygames": "tala<PERSON><PERSON><PERSON><PERSON><PERSON>a", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "aufaupese", "beyondtwosouls": "tuaiualua", "gameuse": "taaloga", "offmortisghost": "offmortisghost", "tinybunny": "lapitimeamea", "retroarch": "retroarch", "powerup": "faʻ<PERSON><PERSON><PERSON>na", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "ataafiliga", "quickflash": "vavevave", "fzero": "fzero", "gachagaming": "<PERSON><PERSON><PERSON><PERSON>", "retroarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "gaogao", "powerwashsim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coralisland": "motuamu", "syberia3": "syberia3", "grymmorpg": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloxfruit": "bloxfruit", "anotherworld": "tisilepasese", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "uameepalanitōfā", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "fau<PERSON>eg<PERSON>aoga", "simulator": "simuleta", "symulatory": "faataitai", "speedrunner": "tamoe_vave", "epicx": "epicx", "superrobottaisen": "superrobottaua", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaituaiga", "korkuoyunu": "taalokor<PERSON>oyunu", "wonderlandonline": "na<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skylander": "skylander", "boyfrienddungeon": "uotauaaleta", "toontownrewritten": "toontowntoeutusia", "simracing": "<PERSON><PERSON><PERSON><PERSON>", "simrace": "taalokalua", "pvp": "pvp", "urbanchaos": "vevesigaalataua", "heavenlybodies": "tinomale<PERSON>ia", "seum": "seum", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "leolivaitogafale", "spaceflightsimulator": "simulatorvaʻalēleasea", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "ta<PERSON><PERSON><PERSON><PERSON>", "foodandvideogames": "meaaimaataaloga", "oyunvideoları": "ataʻaloga", "thewolfamongus": "lelus<PERSON>itatouvaega", "truckingsimulator": "simulatorlo<PERSON>", "horizonworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handygame": "taalogofaigofie", "leyendasyvideojuegos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolvideogames": "taalologamevitio", "racingsimulator": "simulatortaavale", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentsofmayhem", "songpop": "pese<PERSON><PERSON><PERSON>", "famitsu": "famitsu", "gatesofolympus": "f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunternow": "sauligapogesa", "rebelstar": "fetuaitu", "indievideogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indiegaming": "taalofaitua", "indievideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogame": "taalogak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermanleleilemoe", "bufffortress": "malositaua", "unbeatable": "efaeigataua", "projectl": "poloketi", "futureclubgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugman": "ipuinu", "insomniacgames": "taaloamoegatā", "supergiantgames": "taaloteletogia", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "taalogaceleste", "aperturescience": "saienesiapu", "backlog": "faaputuga", "gamebacklog": "taalogataamaliemulimai", "gamingbacklog": "taalomatasioina", "personnagejeuxvidéos": "ta<PERSON>agavat<PERSON>oga", "achievementhunter": "sailiagaʻumia", "cityskylines": "vaalealuga", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "matamuai", "beastlord": "ta<PERSON><PERSON><PERSON><PERSON>", "juegosretro": "taalōgātuai", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "vaofagaodopamine", "staxel": "staxel", "videogameost": "pese<PERSON><PERSON><PERSON><PERSON>", "dragonsync": "tag<PERSON><PERSON><PERSON>", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "outealoakofxv", "arcanum": "<PERSON><PERSON>", "neoy2k": "neoy2k", "pcracing": "taamilopc", "berserk": "faavalevalea", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiaild", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animefaanoanoa", "darkerthanblack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "fuafuaanime", "animewithplot": "animemataitufa", "pesci": "pesci", "retroanime": "animemutua", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80s", "90sanime": "ainimelotausagaivanovaluefulu", "darklord": "al<PERSON><PERSON><PERSON>", "popeetheperformer": "popeetamea", "masterpogi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "<PERSON><PERSON><PERSON>", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneva<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "<PERSON><PERSON><PERSON><PERSON>", "thevisionofescaflowne": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slayers": "faa<PERSON><PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "iapalagi", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitasi", "fireforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "talatusifaa<PERSON>asoamua", "fairytail": "talaitu", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "parasyte": "parasaite", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "talatumamafaigaluega", "kamisamakiss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "tus<PERSON>tal<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemangas": "ta<PERSON><PERSON><PERSON>", "karneval": "karnivale", "dragonmaid": "taavaetalagone", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "<PERSON><PERSON><PERSON><PERSON>", "shamanking": "atiaituta<PERSON><PERSON>", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "alamataopup<PERSON><PERSON>", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "se<PERSON><PERSON><PERSON><PERSON>uagat<PERSON><PERSON><PERSON>", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "<PERSON><PERSON><PERSON><PERSON>", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "ta<PERSON><PERSON><PERSON>", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON>", "angelbeats": "ateneto", "isekaianime": "animeisekai", "sagaoftanyatheevil": "talatalagaotanyaleaga", "shounenanime": "ta<PERSON><PERSON><PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "atunuuoatua", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "onapefaa<PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "at<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "man<PERSON>mamatauti<PERSON>", "martialpeak": "tumugamartial", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "lua<PERSON><PERSON><PERSON><PERSON>", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "teineatua", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "seilasat<PERSON>", "dio": "dio", "sailorpluto": "seilapuluto", "aloy": "aloy", "runa": "runa", "oldanime": "<PERSON>ut<PERSON><PERSON>", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "<PERSON><PERSON><PERSON><PERSON>", "loli": "loli", "horroranime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "atofuata", "devilmancrybaby": "tiapolotagipese", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "alof<PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "turisitaliipese", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "lealalanuufuatamai", "monstermanga": "ataataualimago", "yourlieinapril": "auloupepeloiaaperila", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "tapuailemoaneloloto", "jojolion": "jojo<PERSON>", "deadmanwonderland": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bannafish": "pasi<PERSON><PERSON><PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "tauasaimeaai", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "<PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "iseauefaavavau", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "v<PERSON><PERSON>man<PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "fea<PERSON><PERSON><PERSON>", "mirainikki": "tusitagalumanatua", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "soloia", "bluelock": "bluelock", "goblinslayer": "fagotiperepere", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "fasivaitaloteine", "vampireknight": "toa_su<PERSON>nu", "mugi": "mugi", "blueexorcist": "eks<PERSON><PERSON>lan<PERSON>oan<PERSON>", "slamdunk": "tuup<PERSON>ga", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "vaaifagogo", "spyfamily": "failegataaga", "airgear": "vaematafaga", "magicalgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thesevendeadlysins": "agatefituematega", "prisonschool": "f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "atualeoaogamaualuga", "kissxsis": "feasogasusulu", "grandblue": "grandblue", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swordartonlineabridge": "swordartonlinesefeaoga", "saoabridged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "matip<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "ta<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "alofaleanime", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeariken<PERSON>", "lolicon": "lo<PERSON>on", "demonslayertothesword": "fanaualipenitulau", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "<PERSON><PERSON><PERSON><PERSON>", "adioseri": "adioserimea", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "aloafatafafine", "starsalign": "fetuufaatasi", "romanceanime": "alofalimatagiese", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saof<PERSON><PERSON><PERSON><PERSON><PERSON>", "cherrymagic": "<PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "falekinioku", "recordragnarok": "tusicataeleaga", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "germantechno": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "fanof<PERSON><PERSON><PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "paletituot<PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animeiapani", "animespace": "alatausa<PERSON>", "girlsundpanzer": "teinepaisipane", "akb0048": "akb0048", "hopeanuoli": "faamoemoeimauafolau", "animedub": "animefaasamoa", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "animesamoa", "bungoustray": "bungoumalie", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "isumu", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "tamaaloa_pusi", "gashbell": "gashbell", "peachgirl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonquestdai": "dragonquestdai", "heartofmanga": "<PERSON><PERSON>ana<PERSON>", "deliciousindungeon": "su<PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "talaolefeleeseese", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorialefaigatalele", "overgeared": "overgeareaganequa", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "faletog<PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "tuus<PERSON>mauga", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "tamaitaitasi_aliimamoe", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "faiaogolelei", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "loumatai<PERSON>le", "gear5": "gear5", "grandbluedreaming": "moemoeasinilanumoana", "bloodplus": "totopala", "bloodplusanime": "to<PERSON>aan<PERSON>", "bloodcanime": "to<PERSON><PERSON>me", "bloodc": "totoc", "talesofdemonsandgods": "talanoagaaitumaatua", "goreanime": "aluanime", "animegirls": "animeteinelaiti", "sharingan": "<PERSON><PERSON>", "crowsxworst": "koroseataifega", "splatteranime": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "splatter": "sausau", "risingoftheshieldhero": "siitua<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "<PERSON><PERSON><PERSON>", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "tamaitiaigafola", "liarliar": "pepelopepelo", "supercampeones": "supercampeones", "animeidols": "animeidols", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "asotausinius<PERSON><PERSON>", "magicalgirls": "teinefaaatua", "callofthenight": "valavaala<PERSON><PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "<PERSON><PERSON><PERSON>", "princessjellyfish": "purinisesei<PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradaisikisi", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "talamaliet<PERSON>a", "animeverse": "uigafaailipesepe", "persocoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omniscientreadersview": "v<PERSON><PERSON>ait<PERSON><PERSON><PERSON>ato<PERSON>", "animecat": "pusianime", "animerecommendations": "f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openinganime": "ta<PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "siaualoalo<PERSON>ule<PERSON>", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "robotisiselevave", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mesi", "eurekaseven": "eurekafitu", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "omileega", "deathnote": "atatupot<PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "anime_militeri", "greenranger": "tauasiulauvao", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "aletāfetu", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "alaimeika", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON>aaitu", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "osofaʻigatāitani", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "taalogauō", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "v<PERSON><PERSON><PERSON><PERSON>", "onepieceanime": "onepieceanime", "attaquedestitans": "osofaʻigaotitans", "theonepieceisreal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "revengers": "ta<PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "alia<PERSON><PERSON><PERSON><PERSON>", "digimonstory": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "falepaupaumaualuga", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonlelei", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "faileleelema<PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "naonalava", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "alasāpaia", "recuentosdelavida": "ta<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}