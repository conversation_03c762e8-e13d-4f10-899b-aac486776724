{"2048": "2048", "mbti": "mbti", "enneagram": "اينيگرام", "astrology": "نجوم", "cognitivefunctions": "ذهنيڪمَ", "psychology": "نفسيات", "philosophy": "فلسفو", "history": "تاريخ", "physics": "فزڪس", "science": "سائنس", "culture": "ثقافت", "languages": "ٻوليون", "technology": "ٽيڪنالاجي", "memes": "<PERSON><PERSON><PERSON><PERSON>", "mbtimemes": "mbtimemes", "astrologymemes": "علم_نجوم_جا_ميمز", "enneagrammemes": "اينياگراممِيمز", "showerthoughts": "شاورخيال", "funny": "مزيدار", "videos": "وڊيوز", "gadgets": "گيجيٽس", "politics": "سياست", "relationshipadvice": "رشتن_جي_صلاح", "lifeadvice": "زندگيءَجيصلاح", "crypto": "crypto", "news": "خبرون", "worldnews": "دنياجيونخبرون", "archaeology": "آثارقديمه", "learning": "سکڻ", "debates": "ڊيبيٽس", "conspiracytheories": "سازشيءَجانظريا", "universe": "يونيورس", "meditation": "مراقبو", "mythology": "افسانا", "art": "فن", "crafts": "دستڪاري", "dance": "ڊانس", "design": "ڊيزائن", "makeup": "ميڪ_اپ", "beauty": "<PERSON><PERSON><PERSON>", "fashion": "فيشن", "singing": "ڳائڻ", "writing": "لکڻ", "photography": "فوٽوگرافي", "cosplay": "cosplay", "painting": "رنگڪاري", "drawing": "ڊرائنگ", "books": "ڪتاب", "movies": "فلمون", "poetry": "شاعري", "television": "ٽيليويزن", "filmmaking": "فلمسازي", "animation": "اينيميشن", "anime": "اينمي", "scifi": "سائيفائي", "fantasy": "فينٽاسي", "documentaries": "ڊاڪيومينٽريز", "mystery": "اسرار", "comedy": "ڪاميڊي", "crime": "ج<PERSON><PERSON>", "drama": "ڊرامو", "bollywood": "بالي_وڊ", "kdrama": "ڪيڊراما", "horror": "ڊر", "romance": "رومانس", "realitytv": "رياليٽي_ٽي_وي", "action": "ايڪشن", "music": "موسيقي", "blues": "بلوز", "classical": "ڪلاسيڪل", "country": "ملڪ", "desi": "desi", "edm": "ايڊيايم", "electronic": "اليڪٽرانڪ", "folk": "لوڪ", "funk": "فنڪ", "hiphop": "هپ_هاپ", "house": "گھر", "indie": "انڊي", "jazz": "جاز", "kpop": "ڪيپاپ", "latin": "لاطيني", "metal": "ميٽل", "pop": "پاپ", "punk": "پنڪ", "rnb": "آراينبي", "rap": "ريپ", "reggae": "ريگي", "rock": "راڪ", "techno": "ٽيڪنو", "travel": "سفر", "concerts": "ڪنسرٽ", "festivals": "تہوار", "museums": "ميوزيم", "standup": "اُٿيبيٺ", "theater": "ٿيٽر", "outdoors": "ٻاهر", "gardening": "باغباني", "partying": "پارٽي_ڪرڻ", "gaming": "گيمنگ", "boardgames": "بورڊگيمز", "dungeonsanddragons": "ڊنجنزاينڊڊريگنز", "chess": "شطرنج", "fortnite": "فورٽنائيٽ", "leagueoflegends": "ليگآفليجنڊز", "starcraft": "اسٽاركرافٽ", "minecraft": "مائن_ڪرافٽ", "pokemon": "پوڪيمون", "food": "کاڌو", "baking": "بيڪنگ", "cooking": "پاڪنگ", "vegetarian": "شاڪاهاري", "vegan": "ويگن", "birds": "پکي", "cats": "ٻليون", "dogs": "ڪتا", "fish": "مڇي", "animals": "جانور", "blacklivesmatter": "ڪاريزندگيونضروريآهن", "environmentalism": "ماحوليات_پسندي", "feminism": "فيمنزم", "humanrights": "انسانيحق", "lgbtqally": "lgbtqحامي", "stopasianhate": "ايشيائينفرتبندڪريو", "transally": "ٽرانسحليف", "volunteering": "رضاڪارانه_ڪم", "sports": "ران<PERSON>ين", "badminton": "بيڊمنٽن", "baseball": "بيس_بال", "basketball": "باسڪيٽبال", "boxing": "باڪسنگ", "cricket": "ڪرڪيٽ", "cycling": "سائيڪلنگ", "fitness": "فٽنيس", "football": "فٽبال", "golf": "گولف", "gym": "جم", "gymnastics": "جمناسٽڪس", "hockey": "ہاڪي", "martialarts": "مارشل_آرٽس", "netball": "نيٽبال", "pilates": "پائليٽس", "pingpong": "پنگپانگ", "running": "ڊوڙڻ", "skateboarding": "اسڪيٽبورڊنگ", "skiing": "اسڪيئنگ", "snowboarding": "سنوبورڊنگ", "surfing": "سرفنگ", "swimming": "ترڻ", "tennis": "ٽينس", "volleyball": "والیبال", "weightlifting": "وزنکڻڻ", "yoga": "يوگا", "scubadiving": "اسڪوبا_ڊائيونگ", "hiking": "هائڪنگ", "capricorn": "ڪيپريڪارن", "aquarius": "ڪنڀ", "pisces": "مینو_راشي", "aries": "اريس", "taurus": "ثور", "gemini": "جميني", "cancer": "ڪينسر", "leo": "لیو", "virgo": "ڪنيا", "libra": "لبرا", "scorpio": "اسڪارپيو", "sagittarius": "سيگيٽيريس", "shortterm": "مختصرمدت", "casual": "آرامده", "longtermrelationship": "ڊگھيمديوارورشتو", "single": "سنگل", "polyamory": "پولي_ايموري", "enm": "enm", "lgbt": "ايل_جي_بي_ٽي", "lgbtq": "lgbtq", "gay": "گي", "lesbian": "ليسبيئن", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "غيرجنسي", "reddeadredemption2": "ريڊڊيڊريڊيمپشن2", "dragonage": "ڊريگنايج", "assassinscreed": "اساسنزڪريڊ", "saintsrow": "سينٽسرو", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "ڊيلٽارون", "watchdogs": "واچڊاگس", "dislyte": "ڊسلائيٽ", "rougelikes": "روگلائڪس", "kingsquest": "ڪنگزڪوسٽ", "soulreaver": "روحڇينيندڙ", "suikoden": "سوئيڪوڊن", "subverse": "سبورس", "legendofspyro": "اسپائروجيڪهاڻي", "rouguelikes": "روگلائڪس", "syberia": "سائبيريا", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "ڊريگنزڊوگما", "sunsetoverdrive": "شامجوسفر", "arkham": "آرخم", "deusex": "deusex", "fireemblemfates": "فائرايمبليمفيٽس", "yokaiwatch": "يوڪائيواچ", "rocksteady": "راڪاسٽيڊي", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "گلڊوارز", "openworld": "کليدنيا", "heroesofthestorm": "هيروزآفدئسٽارم", "cytus": "سائيٽس", "soulslike": "روحوارو", "dungeoncrawling": "ڊنجنڪرالنگ", "jetsetradio": "جيٽ_سيٽ_ريڊيو", "tribesofmidgard": "ميڊگارڊجاقبيلا", "planescape": "هوائيجهازمنظر", "lordsoftherealm2": "لارڊزآفدريلم2", "baldursgate": "بالڊرزگيٽ", "colorvore": "رنگخور", "medabots": "ميڊابوٽس", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "غرقڪندڙسمز", "okage": "اوڪيج", "juegoderol": "رولپلي", "witcher": "وِچر", "dishonored": "بي_عزت", "eldenring": "elden<PERSON>", "darksouls": "ڊارڪسولز", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "فالآئوٽ", "fallout3": "فالآئوٽ3", "fallout4": "فالآوٽ4", "skyrim": "اسڪائيرم", "elderscrolls": "ايلڊرسڪرولز", "modding": "موڊنگ", "charactercreation": "ڪردارٺاهڻ", "immersive": "غرقاب", "falloutnewvegas": "فالآئوٽنيووگاس", "bioshock": "بائيوشاڪ", "omori": "omori", "finalfantasyoldschool": "فائنلفينٽسيقديمطرز", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "فائنلفينٽيسي", "finalfantasy14": "فائنلفينٽيسي14", "finalfantasyxiv": "فائنلفينٽاسيايڪسآءِوِي", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "فائنلفينٽيسيماٽويا", "lalafell": "لالافيل", "dissidia": "ڊسيڊيا", "finalfantasy7": "فائنلفينٽيسي7", "ff7": "ff7", "morbidmotivation": "موربڊموٽيويشن", "finalfantasyvii": "فائنلفينٽاسيسيون", "ff8": "ff8", "otome": "اوٽومي", "suckerforlove": "عشقپاگل", "otomegames": "اوٽوميگيمز", "stardew": "stardew", "stardewvalley": "اسٽارڊيوويلي", "ocarinaoftime": "اوڪريناآفٽائيم", "yiikrpg": "yiikrpg", "vampirethemasquerade": "ويمپائرماسڪريڊ", "dimension20": "dimension20", "gaslands": "گيسلينڊز", "pathfinder": "پاٿ_فائنڊر", "pathfinder2ndedition": "پاٿفائنڊر2ndايڊيشن", "shadowrun": "شيڊورن", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "فائنلفانتاسي15", "finalfantasy11": "فائنلفينٽاسي11", "finalfantasy8": "فائنلفينٽاسي8", "ffxvi": "ffxvi", "lovenikki": "لوونڪي", "drakengard": "ڊريڪن_گارڊ", "gravityrush": "ڪشش_ثقل_جو_ڌماڪو", "rpg": "آرپيجي", "dota2": "dota2", "xenoblade": "زينوبليڊ", "oneshot": "ھڪشاٽ", "rpgmaker": "آرپيجيميڪر", "osrs": "osrs", "overlord": "اوورلارڊ", "yourturntodie": "توھانجيموڙمرڻجي", "persona3": "persona3", "rpghorror": "rpgخوفناڪ", "elderscrollsonline": "ايلڊرسڪرولزآنلائن", "reka": "ريڪا", "honkai": "هونڪائي", "marauders": "لُٽيرا", "shinmegamitensei": "شنميگاميتنسي", "epicseven": "ايپڪسيون", "rpgtext": "آرپيجيٽيڪسٽ", "genshin": "genshin", "eso": "ايسو", "diablo2": "diablo2", "diablo2lod": "ڊيابلو2lod", "morrowind": "مورووِنڊ", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "ڊيمنسولز", "mu": "mu", "falloutshelter": "فالآئوٽشيلٽر", "gurps": "gurps", "darkestdungeon": "اونداهيسرنگ", "eclipsephase": "چنڊگرهڻجومرحلو", "disgaea": "ڊسگيا", "outerworlds": "آئوٽرورلڊس", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "ڊيابلوامارٽل", "dynastywarriors": "ڊائنسٽيوارئرز", "skullgirls": "اسڪلگرلز", "nightcity": "راتجوشهر", "hogwartslegacy": "هاگوارٽسليگيسي", "madnesscombat": "جنونيجنگ", "jaggedalliance2": "jaggedalliance2", "neverwinter": "ڪڏهن_نه_سيارو", "road96": "روڊ96", "vtmb": "vtmb", "chimeraland": "چميرالينڊ", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "روگلائڪس", "gothamknights": "گوتھمنائٽس", "forgottenrealms": "وساريلدنيا", "dragonlance": "ڊريگن_لانس", "arenaofvalor": "ايريناآفويلر", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "ٽونٽائون", "childoflight": "روشنيءَجوٻار", "aq3d": "aq3d", "mogeko": "موگيڪو", "thedivision2": "دي_ڊويزن2", "lineage2": "lineage2", "digimonworld": "ڊجيمونورلڊ", "monsterrancher": "مونسٽررينچر", "ecopunk": "ايڪوپنڪ", "vermintide2": "vermintide2", "xeno": "زينو", "vulcanverse": "وولڪنورس", "fracturedthrones": "ڀڃيلتخت", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "شيڊوپنڪ", "finalfantasyxv": "فائنلفينٽيسيايڪسوي", "everoasis": "ايوروَسس", "hogwartmystery": "هاگوارٽ_رهسيو", "deltagreen": "ڊيلٽاگرين", "diablo": "ڊيابلو", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "سمائيٽ", "lastepoch": "آخريدور", "starfinder": "تارونوڳولیندڙ", "goldensun": "گولڊن_سج", "divinityoriginalsin": "ديوينٽيآرجنلسن", "bladesinthedark": "اونداهيبليڊ", "twilight2000": "گودرو2000", "sandevistan": "سانديوستان", "cyberpunk": "سائبرپنڪ", "cyberpunk2077": "سائبرپنڪ2077", "cyberpunkred": "سائبرپنڪريڊ", "dragonballxenoverse2": "ڊريگن_بال_زينوورس_2", "fallenorder": "فالنآرڊر", "finalfantasyxii": "فائنلفينٽيسي12", "evillands": "شيطانيملڪ", "genshinimact": "genshinimpact", "aethyr": "ايٿر", "devilsurvivor": "شيطانبچيويندڙ", "oldschoolrunescape": "پراڻياسڪولرونسڪيپ", "finalfantasy10": "فائنلفينٽيسي10", "anime5e": "anime5e", "divinity": "الوهيت", "pf2": "pf2", "farmrpg": "فارمآرپيجي", "oldworldblues": "پراڻيدنيانجاغم", "adventurequest": "سفرجيڳهه", "dagorhir": "ڊاگورهير", "roleplayingames": "رولپليئنگگيمز", "roleplayinggames": "رولپليئنگگيمز", "finalfantasy9": "فائنلفینٹیسی9", "sunhaven": "سڄڻاآرام", "talesofsymphonia": "ٽيلزآفسمفونيا", "honkaistarrail": "هونڪائيسٽاررائيل", "wolong": "وولونگ", "finalfantasy13": "فائنلفينٽيسي13", "daggerfall": "ڊيگرفال", "torncity": "ڦاٽيلشهر", "myfarog": "منهنجوفاروگ", "sacredunderworld": "مقدسدوزخ", "chainedechoes": "زنجيريگونج", "darksoul": "اونداهيروح", "soulslikes": "سولزلائڪس", "othercide": "ٻيوطرف", "mountandblade": "مائونٽاينڊبليڊ", "inazumaeleven": "انازوماايلون", "acvalhalla": "acvalhalla", "chronotrigger": "ڪرونوٽرگر", "pillarsofeternity": "ابديتجاعمود", "palladiumrpg": "پيليڊيمآرپيجي", "rifts": "رِفٽس", "tibia": "tibia", "thedivision": "ڊويزن", "hellocharlotte": "هيلوشارلوٽ", "legendofdragoon": "ڊريگونجيداستان", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "آڪٽوپاٿٽريولر", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "ويئرولف_دي_اپوڪيلپس", "aveyond": "ايويونڊ", "littlewood": "ننڍيڪاٺ", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON>", "engineheart": "انجڻهارٽ", "fable3": "fable3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "رولناسپيل", "harpg": "harpg", "baldursgates": "بالڊرس_گيٽس", "edeneternal": "عدن<PERSON><PERSON><PERSON>ي", "finalfantasy16": "فائنلفينٽيسي16", "andyandleyley": "اينڊياينڊليلي", "ff15": "ff15", "starfield": "اسٽارفيلڊ", "oldschoolrevival": "پراڻي_اسڪول_جي_واپسي", "finalfantasy12": "فائنلفينٽاسي12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "بيرحمدنيا", "diabloiv": "ڊيابلو4", "pve": "pve", "kingdomheart1": "ڪنگڊمهارٽ1", "ff9": "ff9", "kingdomheart2": "ڪنگڊمھارٽ2", "darknessdungeon": "اونداهيڪوٺڙي", "juegosrpg": "آرپيجيرانديون", "kingdomhearts": "بادشاهتجاڏل", "kingdomheart3": "ڪنگڊمهارٽ3", "finalfantasy6": "فائنلفينٽيسي6", "ffvi": "ffvi", "clanmalkavian": "ڪلانمالڪاويان", "harvestella": "هارويسٽيلا", "gloomhaven": "gloomhaven", "wildhearts": "جهنگليدل", "bastion": "بيسٽين", "drakarochdemoner": "ڊريڪاروچڊيمونر", "skiesofarcadia": "skiesofarcadia", "shadowhearts": "ڇانءِجيونڊليون", "nierreplicant": "نيئرريپليڪنٽ", "gnosia": "gnosia", "pennyblood": "پيني_بلڊ", "breathoffire4": "breathoffire4", "mother3": "ماءُ3", "cyberpunk2020": "سائبرپنڪ2020", "falloutbos": "falloutbos", "anothereden": "ٻيوعدن", "roleplaygames": "رولپليگيمز", "roleplaygame": "رولپلےگیم", "fabulaultima": "fabulault<PERSON>", "witchsheart": "ڊائڻجيدل", "harrypottergame": "هيريپوٽرگيم", "pathfinderrpg": "پاٿفائنڊرآرپيجي", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "ويمپائرنجوماسڪريڊ", "dračák": "ڊريگن", "spelljammer": "اسپيلجامر", "dragonageorigins": "ڊريگناجيآريجنز", "chronocross": "ڪرونوڪراس", "cocttrpg": "cocttrpg", "huntroyale": "شڪاررائل", "albertodyssey": "البرٽوڊيسي", "monsterhunterworld": "مانسٽرهنٽرورلڊ", "bg3": "bg3", "xenogear": "زينوگيئر", "temtem": "ٽيمٽيم", "rpgforum": "آرپيجيفورم", "shadowheartscovenant": "شيڊوھارٽسڪووننٽ", "bladesoul": "بليڊسول", "baldursgate3": "بالڊرزگيٽ3", "kingdomcome": "بادشاهيءَجوآمد", "awplanet": "awplanet", "theworldendswithyou": "دنياتوسانختمٿيندي", "dragalialost": "ڊرگاليالاسٽ", "elderscroll": "بزرگانجوطومار", "dyinglight2": "ڊائينگلائيٽ2", "finalfantasytactics": "فائنلفينٽاسيٽيڪٽڪس", "grandia": "گرینڊيا", "darkheresy": "ڪاروعقيدو", "shoptitans": "شاپٽائيٽنز", "forumrpg": "فورمآرپيجي", "golarion": "گولاريون", "earthmagic": "ڌرتيجادو", "blackbook": "ڪاروڪتاب", "skychildrenoflight": "آسمانجاٻار_روشنيءَجا", "gryrpg": "gryrpg", "sacredgoldedition": "مقدسسوناايڊيشن", "castlecrashers": "قلعيوارڙيندڙ", "gothicgame": "گوٿڪگيم", "scarletnexus": "اسڪارليٽنيڪسس", "ghostwiretokyo": "گھوسٽوائرٽوڪيو", "fallout2d20": "fallout2d20", "gamingrpg": "گیمنگآرپیجی", "prophunt": "نبيشڪار", "starrails": "اسٽاررئلز", "cityofmist": "شهرجيڪوهڙ", "indierpg": "انڊيآرپيجي", "pointandclick": "پوائنٽ_اينڊ_ڪلڪ", "emilyisawaytoo": "ايميليبهتمامگهڻوپريآهي", "emilyisaway": "ايملي_پري_آهي", "indivisible": "ناقابلِتقسيم", "freeside": "فري_سائيڊ", "epic7": "epic7", "ff7evercrisis": "ff7هميشهبحران", "xenogears": "زينوگيئرز", "megamitensei": "ميگاميٽينسي", "symbaroum": "سمباروم", "postcyberpunk": "پوسٽسائبرپنڪ", "deathroadtocanada": "موتجوروڊڪئناڊاڏانهن", "palladium": "پيليڊيم", "knightjdr": "نائيٽجيڊيآر", "monsterhunter": "مانسٽرهنٽر", "fireemblem": "فائرايمبليم", "genshinimpact": "گینشنامپیکٽ", "geosupremancy": "جيو_برتري", "persona5": "پرسونا5", "ghostoftsushima": "ghostoftsushima", "sekiro": "سيڪيرو", "monsterhunterrise": "مونسٽرهنٽررائيز", "nier": "نير", "dothack": "ڊوٿهيڪ", "ys": "ys", "souleater": "روحخور", "fatestaynight": "فيٽاسٽينائيٽ", "etrianodyssey": "etrianodyssey", "nonarygames": "نانريگيمز", "tacticalrpg": "ٽيڪٽيڪلآرپيجي", "mahoyo": "ماهويو", "animegames": "animegames", "damganronpa": "ڊانگانرونپا", "granbluefantasy": "گرانبلوفينٽيسي", "godeater": "خداکائيندڙ", "diluc": "ڊيلڪ", "venti": "وينٽي", "eternalsonata": "هميشه_جي_سُر", "princessconnect": "شهزاديجوڳنڊ", "hexenzirkel": "hexenzirkel", "cristales": "ڪرسٽل", "vcs": "vcs", "pes": "پيس", "pocketsage": "جيبسنجوڄاڻو", "valorant": "ويلورنٽ", "valorante": "ويلرانٽي", "valorantindian": "ويلورنٽانڊين", "dota": "ڊوٽا", "madden": "ميڊن", "cdl": "cdl", "efootbal": "ايفوٽبال", "nba2k": "nba2k", "egames": "اِي_گيمز", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "ايم_ايل_جي", "leagueofdreamers": "خوابن_جي_ليگ", "fifa14": "fifa14", "midlaner": "مڊلينر", "efootball": "ايفٽبال", "dreamhack": "خوابجيهيڪ", "gaimin": "گيمنگ", "overwatchleague": "اوورواچليگ", "cybersport": "سائبراسپورٽ", "crazyraccoon": "پاگلرڪون", "test1test": "test1test", "fc24": "fc24", "riotgames": "رائيٽگيمز", "eracing": "مِٽائيندي", "brasilgameshow": "برازيلگيمشو", "valorantcompetitive": "valorantمقابلي", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "پورٽل2", "halflife": "اڌيزندگي", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "والو", "portal": "پورٽل", "teamfortress2": "ٽيمفورٽريس2", "everlastingsummer": "هميشه_جي_گرمي", "goatsimulator": "goatsimulator", "garrysmod": "گيريزمود", "freedomplanet": "آزاديءَجوسيارو", "transformice": "transformice", "justshapesandbeats": "بسشڪلونڌڙڪنون", "battlefield4": "battlefield4", "nightinthewoods": "راتِجهنگلَ", "halflife2": "هافلائف2", "hacknslash": "هيڪ_اين_سليش", "deeprockgalactic": "ڊيپراڪگيلڪٽڪ", "riskofrain2": "رسڪآفرين2", "metroidvanias": "ميٽرائيڊوينياس", "overcooked": "وڌيڪ_پڪيل", "interplanetary": "بينالسيارو", "helltaker": "جهنمورو", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "ڊيڊسيلز", "nierautomata": "نيئرآٽوماٽا", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "لڪڻ_جي_جاءِ", "stray": "آوارو", "battlefield": "جنگجوميدان", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "فالآئوٽ2", "uboat": "يوبوٽ", "eyeb": "ابروجيسادو", "blackdesert": "ڪاروصحرا", "tabletopsimulator": "ٽيبلٽاپسيموليٽر", "partyhard": "پارٽي_ڪريو_زوردار", "hardspaceshipbreaker": "سختخلائيجهازڀڃيندڙ", "hades": "هيڊس", "gunsmith": "گنسمٿ", "okami": "اوڪامي", "trappedwithjester": "جيسٽرسانگڏڦاسيل", "dinkum": "ڊنڪم", "predecessor": "اڳوڻو", "rainworld": "مينهن_جي_دنيا", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON>", "colonysim": "ڪالونيسم", "noita": "نوائيٽا", "dawnofwar": "جنگجيڪاغاز", "minionmasters": "مائنينماسٽرز", "grimdawn": "grimdawn", "darkanddarker": "اونداهواونداهو", "motox": "موٽوايڪس", "blackmesa": "بليڪميسا", "soulworker": "روحڪار", "datingsims": "ڊيٽنگسمز", "yaga": "ياگا", "cubeescape": "ڪيوبفرار", "hifirush": "هائيفائيرش", "svencoop": "سوينڪوپ", "newcity": "نئونشهر", "citiesskylines": "شهرنجيآسمانيلائينون", "defconheavy": "defconheavy", "kenopsia": "kenopsia", "virtualkenopsia": "ورچوئلڪينوپسيا", "snowrunner": "برفانيرنر", "libraryofruina": "رونئاجيلائبريري", "l4d2": "l4d2", "thenonarygames": "thenonarygames", "omegastrikers": "اوميگااسٽرائڪرز", "wayfinder": "رستو_ڳولڻ_وارو", "kenabridgeofspirits": "kenabridgeofspirits", "placidplasticduck": "آرامپلاسٽڪبتڪ", "battlebit": "جنگبٽ", "ultimatechickenhorse": "آخريچڪنگهوڙو", "dialtown": "ڊائلٽائون", "smileforme": "منهنجيلاءِمسڪرا", "catnight": "ٻليجيرات", "supermeatboy": "سپرميٽ_ڇوڪرو", "tinnybunny": "ننڍومنڍوخرگوش", "cozygrove": "آرامڏيندڙجھُرمُٽ", "doom": "ڊوم", "callofduty": "ڪالآفڊيوٽي", "callofdutyww2": "ڪالآفڊيوٽيڊبليوڊبليو2", "rainbow6": "رينبو6", "apexlegends": "apexlegends", "cod": "ڪاڊ", "borderlands": "بارڊرلينڊز", "pubg": "pubg", "callofdutyzombies": "ڪالآفڊيوٽيزومبيز", "apex": "چوٽي", "r6siege": "r6siege", "megamanx": "ميگامينڪس", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "فارڪرائي", "farcrygames": "farcrygames", "paladins": "پالادين", "earthdefenseforce": "ڌرتيبچاءُفوج", "huntshowdown": "شڪارجوڏيکاريو", "ghostrecon": "ڀوتريڪان", "grandtheftauto5": "گرانڊٿيفٽآٽوپنج", "warz": "وارز", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "الٽراڪِل", "joinsquad": "اسڪواڊشامل", "echovr": "ايڪووي_آر", "discoelysium": "ڊسڪوايليزيم", "insurgencysandstorm": "بغاوت_جو_ريتي_طوفان", "farcry3": "فارڪرائي3", "hotlinemiami": "هاٽلائنميامي", "maxpayne": "ميڪسپين", "hitman3": "هٽمين3", "r6s": "r6s", "rainbowsixsiege": "رينبوسڪسسيج", "deathstranding": "ڊيٿاسٽرينڊنگ", "b4b": "b4b", "codwarzone": "ڪوڊوارزون", "callofdutywarzone": "ڪالآفڊيوٽيوارزون", "codzombies": "ڪاڊزومبيز", "mirrorsedge": "آئيني_ڪنارو", "divisions2": "ڊويزنز2", "killzone": "قتلگاهه", "helghan": "هيلغان", "coldwarzombies": "ٿڌيجنگزومبيز", "metro2033": "ميٽرو2033", "metalgear": "ميٽلگيئر", "acecombat": "ايسڪامبيٽ", "crosscode": "ڪراسڪوڊ", "goldeneye007": "گولڊن_آئي_007", "blackops2": "بليڪاپس2", "sniperelite": "سنائپرايليٽ", "modernwarfare": "جديدجنگ", "neonabyss": "نيونابيس", "planetside2": "پلينيٽسائيڊ2", "mechwarrior": "ميڪواريئر", "boarderlands": "بارڊرلينڊز", "owerwatch": "اوورواچ", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "ميٽلسلگ", "primalcarnage": "پرائيملڪارنيج", "worldofwarships": "جنگيجهازنجيدنيا", "back4blood": "back4blood", "warframe": "وارفريم", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "هٽمين", "masseffect": "ماسايفيڪٽ", "systemshock": "سسٽم_شاڪ", "valkyriachronicles": "والڪيرياڪرونيڪلز", "specopstheline": "specopstheline", "killingfloor2": "ڪلنگفلور2", "cavestory": "غارڪهاڻي", "doometernal": "موتابدي", "centuryageofashes": "صديءَجيعمرجيخاڪ", "farcry4": "فارڪرائي4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "ڊويزن2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "جنريشنزيرو", "enterthegungeon": "گنجنداخلٿيو", "jakanddaxter": "جاڪاندڊيڪسٽر", "modernwarfare2": "جديدجنگجيرانداز2", "blackops1": "بليڪآپس1", "sausageman": "سساجمين", "ratchetandclank": "ratchetandclank", "chexquest": "چيڪسڪوئسٽ", "thephantompain": "ڀوتجوسور", "warface": "جنگجوچهرو", "crossfire": "ڪراس_فائر", "atomicheart": "ايٽامڪدل", "blackops3": "بليڪآپس3", "vampiresurvivors": "ويمپائرسروائيورس", "callofdutybatleroyale": "ڪالآفڊيوٽيبيٽلرائل", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "فريڊوم", "battlegrounds": "جنگ_جا_ميدان", "frag": "فريگ", "tinytina": "ننڍيٽينا", "gamepubg": "گيمپبجي", "necromunda": "نيڪرومنڊا", "metalgearsonsoflibert": "ميٽلگيئرسنزآفلبرٽي", "juegosfps": "ايفپيايسگيمز", "convertstrike": "ڪنورٽاسٽرائيڪ", "warzone2": "وارزون2", "shatterline": "شيٽرلائين", "blackopszombies": "بليڪاپسزومبيز", "bloodymess": "خونيبگاڙ", "republiccommando": "جمهوريڪمانڊو", "elitedangerous": "ايلٽڊينجرس", "soldat": "سپاهي", "groundbranch": "گرائونڊبرانچ", "squad": "اسڪواڊ", "destiny1": "تقدير1", "gamingfps": "گيمنگايفپيايس", "redfall": "ريڊفال", "pubggirl": "پبجيڇوڪري", "worldoftanksblitz": "ورلڊآفٽينڪسبلٽز", "callofdutyblackops": "ڪالآفڊيوٽيبليڪآپس", "enlisted": "شامل_ٿيل", "farlight": "فارلائيٽ", "farcry5": "فارڪرائي5", "farcry6": "فارڪرائي6", "farlight84": "farlight84", "splatoon3": "سپلاٽون3", "armoredcore": "آرمرڊڪور", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "ننڍيٽينازوندرلينڊز", "halo2": "هيلو2", "payday2": "payday2", "cs16": "cs16", "pubgindonesia": "پبجيانڊونيشيا", "pubgukraine": "pubgيوڪرين", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "پبجيمانيا", "empyrion": "ايمپيريئن", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "ٽائيٽن_فال_2", "soapcod": "سوپڪاڊ", "ghostcod": "ڀوتڪوڊ", "csplay": "csplay", "unrealtournament": "غيرحقيقيٽورنامينٽ", "callofdutydmz": "callofdutydmz", "gamingcodm": "گيمنگcodm", "borderlands2": "بارڊرلينڊز2", "counterstrike": "ڪائونٽرسٽرائڪ", "cs2": "cs2", "pistolwhip": "پستولوِھپ", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "هيلو", "killingfloor": "قتلڪندڙفرش", "destiny2": "ڊيسٽني2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "نيونوائيٽ", "remnant": "باقي", "azurelane": "ازورلين", "worldofwar": "جنگجيدنيا", "gunvolt": "گن_وولٽ", "returnal": "واپسي", "halo4": "هيلو4", "haloreach": "هيلوريچ", "shadowman": "شيڊومين", "quake2": "quake2", "microvolts": "مائڪرووولٽس", "reddead": "لالمئو", "standoff2": "اسٽينڊآف2", "harekat": "هرڪت", "battlefield3": "battlefield3", "lostark": "لاسٽارڪ", "guildwars2": "گلڊوارز2", "fallout76": "فالآئوٽ76", "elsword": "ايلسورڊ", "seaofthieves": "سمنڊچورن", "rust": "رسٽ", "conqueronline": "فاتحآنلائن", "dauntless": "بيباڪ", "warships": "جنگيجهاز", "dayofdragons": "ڊريگنن_جو_ڏينهن", "warthunder": "وارٿنڊر", "flightrising": "فلائيٽرائزنگ", "recroom": "ريڪروم", "legendsofruneterra": "ليجنڊزآفرُونيٽيرا", "pso2": "pso2", "myster": "رازداري", "phantasystaronline2": "فينٽاسيسٽارآنلائن2", "maidenless": "بي_ماڙي", "ninokuni": "نينوڪوني", "worldoftanks": "ٽينڪنجيدنيا", "crossout": "ڪراسآئوٽ", "agario": "agario", "secondlife": "ٻيحيات", "aion": "aion", "toweroffantasy": "ٽاورآففينٽيسي", "netplay": "نيٽ_پلي", "everquest": "ايورڪوسٽ", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "نینوڪونيڪراسورلڊ", "reddeadonline": "ريڊڊيڊآنلائن", "superanimalroyale": "سپرانيملرائل", "ragnarokonline": "راگناروڪآنلائن", "knightonline": "نائيٽ_آن_لائين", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "آئزڪجيڪڙي", "dragonageinquisition": "ڊريگنايجانڪوائيزيشن", "codevein": "ڪوڊوين", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "ڪلبپينگوئن", "lotro": "lotro", "wakfu": "wakfu", "scum": "گندگي", "newworld": "نئيدنيا", "blackdesertonline": "بليڪڊيزرٽآنلائن", "multiplayer": "ملٽيپليئر", "pirate101": "قزاق101", "honorofkings": "هانرآفڪنگز", "fivem": "فائيوايم", "starwarsbattlefront": "اسٽارورزبيٽلفرنٽ", "karmaland": "ڪرمالينڊ", "ssbu": "ssbu", "starwarsbattlefront2": "اسٽارورزبيٽلفرنٽ2", "phigros": "فگروس", "mmo": "mmo", "pokemmo": "پوڪيمو", "ponytown": "پونيٽاؤن", "3dchat": "3dچيٽ", "nostale": "ڪڏهن_پراڻو_نه_ٿيندو", "tauriwow": "tauriواہ", "wowclassic": "wowڪلاسڪ", "worldofwarcraft": "ورلڊآفوارڪرافٽ", "warcraft": "وارڪرافٽ", "wotlk": "wotlk", "runescape": "رُنسڪيپ", "neopets": "نيوپيٽس", "moba": "موبا", "habbo": "هابو", "archeage": "آرڪي_ايج", "toramonline": "toramonline", "mabinogi": "مابينوگي", "ashesofcreation": "خاڪجيتخليق", "riotmmo": "riotmmo", "silkroad": "سلڪروڊ", "spiralknights": "سپائرلنائيٽس", "mulegend": "mulegend", "startrekonline": "اسٽارٽريڪآنلائن", "vindictus": "وِنڊڪٽس", "albiononline": "البيونآنلائن", "bladeandsoul": "بليڊاينڊسول", "evony": "evony", "dragonsprophet": "ڊريگنزپروفيٽ", "grymmo": "grymmo", "warmane": "گرمماڻهو", "multijugador": "ڪيترائيرانديگر", "angelsonline": "فرشتا_آنلائن", "lunia": "لونيا", "luniaz": "لونياز", "idleon": "<PERSON>on", "dcuniverseonline": "ڊيسيونيورسآنلائن", "growtopia": "گروٿوپيا", "starwarsoldrepublic": "اسٽارورزاولڊريپبلڪ", "grandfantasia": "گرانڊفينٽاسيا", "blueprotocol": "بليوپروٽوڪول", "perfectworld": "ڀرپورجهان", "riseonline": "آنلائنتياُٺ", "corepunk": "ڪورپنڪ", "adventurequestworlds": "ايڊونچرڪوئسٽورلڊس", "flyforfun": "اُڏامستي", "animaljam": "اينيملجام", "kingdomofloathing": "بادشاهيءَجينفرت", "cityofheroes": "شهرجاهيرو", "mortalkombat": "مورٽلڪومبيٽ", "streetfighter": "اسٽريٽفائٽر", "hollowknight": "هالوڪنائيٽ", "metalgearsolid": "ميٽلگيئرسولڊ", "forhonor": "فورهانر", "tekken": "ٽيڪن", "guiltygear": "گلٽيگيئر", "xenoverse2": "زينوورس2", "fgc": "fgc", "streetfighter6": "اسٽريٽفائٽر6", "multiversus": "ملٽيورسس", "smashbrosultimate": "سماشبروسالٽيميٽ", "soulcalibur": "سولڪيليبر", "brawlhalla": "براولهالا", "virtuafighter": "ورچوئلفائيٽر", "streetsofrage": "روڊرياستن", "mkdeadlyalliance": "mkموتيليگ", "nomoreheroes": "ڪوبہیروناہي", "mhr": "mhr", "mortalkombat12": "مورٽلڪومبيٽ12", "thekingoffighters": "ڪنگآففائٽرز", "likeadragon": "ڊريگنوانگر", "retrofightinggames": "ريٽروفائٽنگگيمز", "blasphemous": "ڪفر", "rivalsofaether": "رائيولزآفايدر", "persona4arena": "persona4arena", "marvelvscapcom": "مارولوسڪيپڪام", "supersmash": "سپرسميش", "mugen": "موگن", "warofthemonsters": "راڪشن_جي_جنگ", "jogosdeluta": "جوگوسڊيلوٽا", "cyberbots": "سائبربوٽس", "armoredwarriors": "لوهپوشجنگجو", "finalfight": "فائنلفائيٽ", "poweredgear": "پاورڊگيئر", "beatemup": "مارڪٽائي", "blazblue": "بليزبلو", "mortalkombat9": "مورٽلڪومبيٽ9", "fightgames": "ويڙهندڙراندون", "killerinstinct": "قاتلانوجذبو", "kingoffigthers": "فائٽرزجوبادشاهه", "ghostrunner": "ڀوتڊوڙندڙ", "chivalry2": "شوالري2", "demonssouls": "ڊيمنزسولز", "blazbluecrosstag": "بليزبلوڪراسٽيگ", "blazbluextagbattle": "بليزبليوڪراسٽيگبيٽل", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "hollowknightجوسيڪوئل", "hollowknightsilksong": "هالوڪنائيٽسلڪسانگ", "silksonghornet": "سلڪسانگهورنيٽ", "silksonggame": "سلڪسونگگيم", "silksongnews": "سلڪسانگخبرون", "silksong": "سلڪسانگ", "undernight": "رات_جي_هيٺان", "typelumina": "ٽائپلومينا", "evolutiontournament": "ارتقاءٽورنامينٽ", "evomoment": "evomoment", "lollipopchainsaw": "لاليپاپچينساء", "dragonballfighterz": "ڊريگنبالفائٽرز", "talesofberseria": "ٽيلزآفبرسيريا", "bloodborne": "بلڊبورن", "horizon": "افق", "pathofexile": "pathofexile", "slimerancher": "سلائيمرينچر", "crashbandicoot": "ڪريشبينڊيڪوٽ", "bloodbourne": "بلڊبورن", "uncharted": "اڻڄاتل", "horizonzerodawn": "هورائزنزيروڊان", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "پليسٽيشنپلس", "lastofus": "لاسٽ_آف_اس", "infamous": "بدنام", "playstationbuddies": "playstationيار", "ps1": "ps1", "oddworld": "عجيبدنيا", "playstation5": "پلےاسٽيشن5", "slycooper": "سلائيڪوپر", "psp": "psp", "rabbids": "رابڊس", "splitgate": "سپلٽ_گيٽ", "persona4": "persona4", "hellletloose": "هيللٽلوز", "gta4": "gta4", "gta": "gta", "roguecompany": "روگڪمپني", "aisomniumfiles": "ايسومنيمفائيلز", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "جنگجوخدا", "gris": "گرس", "trove": "خزانو", "detroitbecomehuman": "ڊيٽرائيٽبيڪمزهيومن", "beatsaber": "بيٽسيبر", "rimworld": "رمورلڊ", "stellaris": "اسٽيلارس", "ps3": "ps3", "untildawn": "صبحتائين", "touristtrophy": "سياحياعزاز", "lspdfr": "lspdfr", "shadowofthecolossus": "شيڊوآفدڪولوسس", "crashteamracing": "ڪريشٽيمريسنگ", "fivepd": "پنجپوليس", "tekken7": "ٽيڪن7", "devilmaycry": "ڊيولمائيڪرائي", "devilmaycry3": "ڊيولمائيڪرائي3", "devilmaycry5": "ڊيولميڪرائي5", "ufc4": "ufc4", "playingstation": "پليئنگاسٽيشن", "samuraiwarriors": "سامورائي_ويڙيل", "psvr2": "psvr2", "thelastguardian": "آخريسنڀاليندڙ", "soulblade": "روحيتلوار", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "پليسٽيشن3", "manhunt": "مردشڪار", "gtavicecity": "جي_ٽي_اي_وائيس_سٽي", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "شيڊوهارٽس2ڪاوننٽ", "pcsx2": "pcsx2", "lastguardian": "آخريمحافظ", "xboxone": "xboxone", "forza": "فورزا", "cd": "cd", "gamepass": "گيم_پاس", "armello": "armello", "partyanimal": "پارٽي_جو_شوقين", "warharmmer40k": "warhammer40k", "fightnightchampion": "فائيٽنائيٽچيمپيئن", "psychonauts": "psychonauts", "mhw": "mhw", "princeofpersia": "شهزادوپارس", "theelderscrollsskyrim": "دیایلڈراسڪرولزاسڪائيرم", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "دي_ايلڊر_اسڪرولز", "gxbox": "gxbox", "battlefront": "بيٽل_فرنٽ", "dontstarvetogether": "گڏجيناکائو", "ori": "اصلي", "spelunky": "غارجيڳلڪڻ", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "ستارن_سان_جڙيل", "xboxonex": "xboxonex", "forzahorizon5": "فورزاهورائزن5", "skate3": "اسڪيٽ3", "houseflipper": "گھرٻرائيندڙ", "americanmcgeesalice": "آمريڪنميڪگيزآليس", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "ليگآفڪنگڊمز", "fable2": "ڪهاڻي2", "xboxgamepass": "xboxgamepass", "undertale": "انڊرٽيل", "trashtv": "ڪچروٽيوي", "skycotl": "skycotl", "erica": "ايريڪا", "ancestory": "نسبنامو", "cuphead": "کپھیڈ", "littlemisfortune": "ننڍيبدقسمتي", "sallyface": "سيليفيس", "franbow": "فرينبائو", "monsterprom": "مانسٽرپروم", "projectzomboid": "پروجيڪٽزومبوڊ", "ddlc": "ddlc", "motos": "موٽرسائيڪلون", "outerwilds": "بيرونيجهنگلات", "pbbg": "pbbg", "anshi": "انشي", "cultofthelamb": "ڀيڙيجوپنٿ", "duckgame": "بتڪراند", "thestanleyparable": "دِاسٽينليپيرابل", "towerunite": "ٽاوريونائيٽ", "occulto": "لڪيل", "longdrive": "لانگڊرائيو", "satisfactory": "ٺيڪ", "pluviophile": "pluviophile", "underearth": "زميندلهيٺ", "assettocorsa": "ايسيٽوڪورسا", "geometrydash": "جيوميٽريڊيش", "kerbal": "ڪربل", "kerbalspaceprogram": "ڪربلاسپيسپروگرام", "kenshi": "ڪينشي", "spiritfarer": "روحسفير", "darkdome": "ڪاروگنبد", "pizzatower": "پيزاٽاور", "indiegame": "انڊيگيم", "itchio": "itchio", "golfit": "گولفکريو", "truthordare": "سچياچيلينج", "game": "گيم", "rockpaperscissors": "پٿرڪاغذقينچي", "trampoline": "ٽرامپولين", "hulahoop": "هولاهوپ", "dare": "ڊيئر", "scavengerhunt": "شڪارجيراند", "yardgames": "باغجونکيڏيون", "pickanumber": "ڪونمبرچونڊيو", "trueorfalse": "سچپچڇاڪُوڙ", "beerpong": "بيئرپانگ", "dicegoblin": "نردوالوڀوت", "cosygames": "آرامدههراند", "datinggames": "ڊيٽنگ_جون_راندون", "freegame": "مفتگيم", "drinkinggames": "شرابجيونرانديون", "sodoku": "سوڊوڪو", "juegos": "جيگوز", "mahjong": "ماهجونگ", "jeux": "jeux", "simulationgames": "سميوليشنگيمز", "wordgames": "لفظيونراند", "jeuxdemots": "جيوڊيمو", "juegosdepalabras": "لفظنجيڪيل", "letsplayagame": "اچومانوکيڏيون", "boredgames": "بورڊگيمز", "oyun": "oyun", "interactivegames": "رانديونجي_راندين", "amtgard": "امٽگارڊ", "staringcontests": "نظرونجيجنگ", "spiele": "<PERSON><PERSON><PERSON>", "giochi": "giochi", "geoguessr": "جيوگيسر", "iphonegames": "آءِفونگيمز", "boogames": "بوگيمز", "cranegame": "کرينگيم", "hideandseek": "لڪاءِ_ڇپاءِ", "hopscotch": "هاپ_اسڪاچ", "arcadegames": "آرڪيڊگيمز", "yakuzagames": "ياڪوزاگيمز", "classicgame": "ڪلاسڪگيم", "mindgames": "دماغيرانديون", "guessthelyric": "لفظَنجواندازولڳايو", "galagames": "گالاگيمز", "romancegame": "رومانسگيم", "yanderegames": "يانڊيريگيمز", "tonguetwisters": "زبانموڙيندڙ", "4xgames": "4xگيمس", "gamefi": "گيمفائي", "jeuxdarcades": "آرڪيڊگيمز", "tabletopgames": "ٽيبل_ٽاپ_رانديون", "metroidvania": "ميٽرائيڊوينيا", "games90": "گيمز90", "idareyou": "توکيڪرينٿو", "mozaa": "موزا", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "ريسنگگيمز", "ets2": "ets2", "realvsfake": "حقيقي_بمقابلہ_جعلي", "playgames": "راندڪريو", "gameonline": "آنلائنگيم", "onlinegames": "آن_لائين_گيمز", "jogosonline": "آن_لائن_رانديون", "writtenroleplay": "لکيلڪردارادائيگي", "playaballgame": "راندڪري", "pictionary": "تصويريلعبت", "coopgames": "coopgames", "jenga": "جينگا", "wiigames": "ويآئيگيمز", "highscore": "هاءِاسڪور", "jeuxderôles": "رولپلےنگگيمز", "burgergames": "برگرگيمز", "kidsgames": "ٻارنجيونراميونون", "skeeball": "سڪيبال", "nfsmwblackedition": "nfsmwڪاروايڊيشن", "jeuconcour": "جيو_مقابلو", "tcgplayer": "tcgplayer", "juegodepreguntas": "سوالنجيراند", "gioco": "gioco", "managementgame": "انتظامراند", "hiddenobjectgame": "لڪيلشيءِجيراند", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "فارمولا1گيم", "citybuilder": "شهرسازي", "drdriving": "ڊاڪٽرڊرائيونگ", "juegosarcade": "آرڪيڊگيمز", "memorygames": "يادداشتجونراندون", "vulkan": "ولڪان", "actiongames": "ايڪشنگيمز", "blowgames": "ڦوڪجيڪاريون", "pinballmachines": "پنبالمشينون", "oldgames": "پراڻيونرايون", "couchcoop": "ڪائوچ_ڪوآپ", "perguntados": "<PERSON><PERSON><PERSON><PERSON>", "gameo": "گيمو", "lasergame": "ليزرگيم", "imessagegames": "آءِميسيجگيمز", "idlegames": "بيڪارراندون", "fillintheblank": "خاليجاءڀريو", "jeuxpc": "پيسيگيمز", "rétrogaming": "ريٽروگيمنگ", "logicgames": "منطقيرانديون", "japangame": "جاپانگيم", "rizzupgame": "رِزاَپگيم", "subwaysurf": "سب_وي_سرف", "jeuxdecelebrite": "جيوڪسڊيسيليبرٽي", "exitgames": "نڪرڻواريونراندڻيون", "5vs5": "5vs5", "rolgame": "رولگيم", "dashiegames": "ڊيشيگيمز", "gameandkill": "راندوماروڪاري", "traditionalgames": "روايتيراندون", "kniffel": "ڪنيفل", "gamefps": "گيمايفپيايس", "textbasedgames": "ٽيڪسٽ_تي_ٻڌل_رانديون", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "فينٽاڪالچو", "retrospel": "ريٽروسپيل", "thiefgame": "چورجيگيم", "lawngames": "لان_راندين", "fliperama": "فليپيراما", "heroclix": "هيروڪلڪس", "tablesoccer": "ٽيبلساڪر", "tischfußball": "ٽيبل_فٽبال", "spieleabende": "spie<PERSON><PERSON>de", "jeuxforum": "jeuxforum", "casualgames": "آسانرانديون", "fléchettes": "فليچيٽس", "escapegames": "فرارجونراميون", "thiefgameseries": "چورجيگيمسيريز", "cranegames": "ڪرينگيمز", "játék": "<PERSON><PERSON><PERSON>", "bordfodbold": "بورڊفٽبال", "jogosorte": "jogosorte", "mage": "جادوگر", "cargames": "ڪارگيمز", "onlineplay": "آنلائنکيڏ", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "راندين_جون_راتيون", "pursebingos": "پرسبنگوز", "randomizer": "randomizer", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "ڪمپيوٽرگيمز", "socialdeductiongames": "سماجيڊيڊڪشنگيمز", "dominos": "ڊومينوز", "domino": "ڊومينو", "isometricgames": "آئسوميٽرڪگيمز", "goodoldgames": "پراڻيونمنپسندرانديون", "truthanddare": "سچڏس", "mahjongriichi": "ماهجونگريچي", "scavengerhunts": "ڳولاڳالهجونرامتون", "jeuxvirtuel": "جيوڪسورچوئل", "romhack": "رومهيڪ", "f2pgamer": "f2pگيمر", "free2play": "م<PERSON><PERSON>_<PERSON>اند", "fantasygame": "تصوراتيواريراند", "gryonline": "gryonline", "driftgame": "ڊرفٽگيم", "gamesotomes": "گيمسوٽومس", "halotvseriesandgames": "haloٽيويسيريزگيمز", "mushroomoasis": "مشرومنخلستان", "anythingwithanengine": "ڪنهنبهشيءِجنهنانجڻآهي", "everywheregame": "هرجاءِراند", "swordandsorcery": "سيفتےجادو", "goodgamegiving": "سٺيراندڻجوڏيڻو", "jugamos": "کيڏيونٿا", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "ڪمپيوٽرگيمز", "virgogami": "ڪنياگامي", "gogame": "ويوراند", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "ننڍيونرانديون", "ridgeracertype4": "رجريسرٽائپ4", "selflovegaming": "سيلف_لو_گيمنگ", "gamemodding": "گيمماڊنگ", "crimegames": "ڏوهن_جون_رانديون", "dobbelspellen": "بورڊگيمز", "spelletjes": "رانديون", "spacenerf": "اسپيسنرف", "charades": "ڊرامابازي", "singleplayer": "اڪيلوکلاڙي", "coopgame": "تعاونواريراند", "gamed": "گيمڪيو", "forzahorizon": "فورزاهورائزن", "nexus": "نيڪسس", "geforcenow": "geforcenow", "maingame": "اصلراند", "kingdiscord": "بادشاهڊسڪارڊ", "scrabble": "اسڪربل", "schach": "شطرنج", "shogi": "شوگي", "dandd": "ڊيانڊڊي", "catan": "ڪٽان", "ludo": "لُڊو", "backgammon": "بيڪگيمن", "onitama": "اونيتاما", "pandemiclegacy": "وباجيورثي", "camelup": "اُٺَجيڪَڙو", "monopolygame": "مونوپوليراند", "brettspiele": "بريٽسپيلي", "bordspellen": "بورڊگيمز", "boardgame": "بورڊگيم", "sällskapspel": "بورڊگيمز", "planszowe": "بورڊ_گيمز", "risiko": "رسڪو", "permainanpapan": "پرمائنن_پاپان", "zombicide": "زومبيڪائيڊ", "tabletop": "ٽيبل_ٽاپ", "baduk": "بادوڪ", "bloodbowl": "بلڊبال", "cluedo": "ڪلوڊو", "xiangqi": "شيانگچي", "senet": "سينيٽ", "goboardgame": "بورڊگيمکيڏيو", "connectfour": "چارڳڏيون", "heroquest": "هيروڪوئسٽ", "giochidatavolo": "بورڊجيمز", "farkle": "فارڪل", "carrom": "ڪيرم", "tablegames": "ٽيبلگيمز", "dicegames": "ڊائسجونرانديون", "yatzy": "يٽزي", "parchis": "پارچيس", "jogodetabuleiro": "جوگوڊيٽابوليرو", "jocuridesocietate": "سماجيرانديون", "deskgames": "ڊيسڪگيمز", "alpharius": "الفاريس", "masaoyunları": "مساءوجيون_رانديون", "marvelcrisisprotocol": "مارولڪرائسسپروٽوڪول", "cosmicencounter": "ڪائناتيملاقات", "creationludique": "تخليقيلعب", "tabletoproleplay": "ٽيبلٽاپروليپلي", "cardboardgames": "ڪارڊبورڊگيمز", "eldritchhorror": "ڊيڄَندڙدهشت", "switchboardgames": "سوچبورڊگيمز", "infinitythegame": "لامحدودراند", "kingdomdeath": "بادشاهتجيموت", "yahtzee": "يھاٽزي", "chutesandladders": "چُوٽيونڏاڙيون", "társas": "سماجي", "juegodemesa": "بورڊگيم", "planszówki": "بورڊگيمز", "rednecklife": "ڳوٺريجيزندگي", "boardom": "بوريت", "applestoapples": "ايپل_سان_ايپل", "jeudesociété": "بورڊگيمز", "gameboard": "گيم_بورڊ", "dominó": "ڊومينو", "kalah": "ڪالهه", "crokinole": "ڪروڪينول", "jeuxdesociétés": "جيوزڊيسوسيٽيز", "twilightimperium": "ٽوائلائيٽامپيريم", "horseopoly": "گهوڙنپولي", "deckbuilding": "ڊيڪٺاهڻ", "mansionsofmadness": "جنونجيمحلات", "gomoku": "گوموڪو", "giochidatavola": "بورڊگيمز", "shadowsofbrimstone": "برمسٽونجاپرڇايون", "kingoftokyo": "ٽوڪيوجوبادشاهه", "warcaby": "وارڪيبي", "táblajátékok": "ٽيبل_گيمز", "battleship": "جنگيجهاز", "tickettoride": "ٽڪيٽ_ته_سواري", "deskovehry": "ڊيسڪ_جون_راندون", "catán": "کتان", "subbuteo": "سبوٽيو", "jeuxdeplateau": "بورڊگيمز", "stolníhry": "ٽيبل_گيمز", "xiángqi": "<PERSON><PERSON><PERSON>", "jeuxsociete": "jeuxsociete", "gesellschaftsspiele": "gesellschaftsspiele", "starwarslegion": "اسٽاروارزليجن", "gochess": "وڃشطرنج", "weiqi": "weiqi", "jeuxdesocietes": "جيوڊيسوسيئيٽس", "terraria": "ٽيراريا", "dsmp": "dsmp", "warzone": "جنگجوميدان", "arksurvivalevolved": "آرڪسروائيولايوولڊ", "dayz": "ڏينهن", "identityv": "identityv", "theisle": "ٻيٽ", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "نومينزاسڪائي", "subnautica": "سبناٽيڪا", "tombraider": "ٽومبريڊر", "callofcthulhu": "callofcthulhu", "bendyandtheinkmachine": "بينڊيانڊدِانڪمشين", "conanexiles": "conanexiles", "eft": "eft", "amongus": "اسانجيوچ", "eco": "ماحوليات", "monkeyisland": "بندرجزيرو", "valheim": "ويلهائيم", "planetcrafter": "سياروسازڪار", "daysgone": "ڏينهنگذريا", "fobia": "فوبيا", "witchit": "ڪريان", "pathologic": "پيٿولاجڪ", "zomboid": "زومبائيڊ", "northgard": "نارٿگارڊ", "7dtd": "7dtd", "thelongdark": "ڊگھيرات", "ark": "آرڪ", "grounded": "زمين_تي", "stateofdecay2": "stateofdecay2", "vrising": "vrرائزنگ", "madfather": "پاڳلپيءُ", "dontstarve": "بُکنہمرو", "eternalreturn": "هميشه_واپسي", "pathoftitans": "pathoftitans", "frictionalgames": "رگڙواريونرانديون", "hexen": "هيڪسن", "theevilwithin": "اندرجيشيطان", "realrac": "حقيقيمقابلو", "thebackrooms": "thebackrooms", "backrooms": "پٺيونڪمرا", "empiressmp": "امپائرزايس_ايم_پي", "blockstory": "بلاڪڪهاڻي", "thequarry": "کوارري", "tlou": "tlou", "dyinglight": "مرندڙروشني", "thewalkingdeadgame": "دي_واڪنگ_ڊيڊ_گيم", "wehappyfew": "اسانخوشٿورا", "riseofempires": "رائزآفايمپائرز", "stateofsurvivalgame": "stateofsurvivalگيم", "vintagestory": "پراڻيڪهاڻي", "arksurvival": "آرڪسروائيول", "barotrauma": "باروٽراما", "breathedge": "ساهڪنارو", "alisa": "عليسا", "westlendsurvival": "ويسٽلينڊسروائيول", "beastsofbermuda": "برمودا_جا_جانور", "frostpunk": "فراسٽپنڪ", "darkwood": "ڊارڪووڊ", "survivalhorror": "سروائيولهارر", "residentevil": "ريزيڊنٽايول", "residentevil2": "ريزيڊنٽايول2", "residentevil4": "ريزيڊنٽايول4", "residentevil3": "residentevil3", "voidtrain": "خاليٽرين", "lifeaftergame": "لائيف_آفٽر_گيم", "survivalgames": "بقاءَجونراندون", "sillenthill": "سائلنٽهِل", "thiswarofmine": "هيجنگمنهنجي", "scpfoundation": "ايسسيپيفائونڊيشن", "greenproject": "سائوپروجيڪٽ", "kuon": "ڪُون", "cryoffear": "ڊپجيخوف", "raft": "رافٽ", "rdo": "rdo", "greenhell": "سائوجهنم", "residentevil5": "ريزيڊنٽايول5", "deadpoly": "ڊيڊپولي", "residentevil8": "ريزيڊنٽايول8", "onironauta": "اونيرونائوٽا", "granny": "دادي", "littlenightmares2": "ننڍيونراتيونخوابخرابيون2", "signalis": "سگنلس", "amandatheadventurer": "امندا_جي_سفرڪهاڻي", "sonsoftheforest": "سنزآفدفارسٽ", "rustvideogame": "زنگجيوِڊيوگيم", "outlasttrials": "آؤٽلاسٽٽرائلز", "alienisolation": "ايلينآئسوليشن", "undawn": "انڊان", "7day2die": "7ڏينهن2مرڻ", "sunlesssea": "سج_کان_سواءِ_سمنڊ", "sopravvivenza": "بقا", "propnight": "propnight", "deadisland2": "ڊيڊآئلينڊ2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "ڊيٿورس", "cataclysmdarkdays": "تباهيءَجاڪاراڏينهن", "soma": "سوما", "fearandhunger": "خوفڀوڪ", "stalkercieńczarnobyla": "اسٽالڪرچرنوبلجوپاڇو", "lifeafter": "زندگيکانپوءِ", "ageofdarkness": "اونداهيءَجودور", "clocktower3": "ڪلاڪٽاور3", "aloneinthedark": "اونداهياڪيلو", "medievaldynasty": "وچوليدورجيسلطنت", "projectnimbusgame": "projectnimbusگيم", "eternights": "ابديراتيون", "craftopia": "ڪرافٽوپيا", "theoutlasttrials": "theoutlasttrials", "bunker": "بنڪر", "worlddomination": "دنياتيقبضو", "rocketleague": "راڪيٽليگ", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "نيڪرون", "wfrp": "wfrp", "dwarfslayer": "ٻانڙومارو", "warhammer40kcrush": "warhammer40kديجذبو", "wh40": "wh40", "warhammer40klove": "warhammer40kسانمحبت", "warhammer40klore": "warhammer40klore", "warhammer": "وارهيمر", "warhammer30k": "وارهيمر30k", "warhammer40k": "وارهيمر40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "ٽوٽل_وار_ھيمر_3", "temploculexus": "temploculexus", "vindicare": "وِنڊيڪيئر", "ilovesororitas": "مونکي_سوروريٽيز_پسند_آهن", "ilovevindicare": "مونکيونڊيڪيئرسانپيارآهي", "iloveassasinorum": "iloveassasinorum", "templovenenum": "templovenenum", "templocallidus": "ٽيمپلوڪليڊس", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "قاتلنجوڪم", "tarkov": "ٽارڪوف", "40k": "40k", "tetris": "ٽيٽرس", "lioden": "لائيوڊن", "ageofempires": "عمرسلطنتن", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "وارهيمرايجآفسگمار", "civilizationv": "civilizationv", "ittakestwo": "ٻنھنجيضرورتآھي", "wingspan": "پرن_جو_ڦيلاءُ", "terraformingmars": "مريخجيسڪيتعمير", "heroesofmightandmagic": "هيروزآفمائٽاينڊميجڪ", "btd6": "btd6", "supremecommander": "سپريمڪمانڊر", "ageofmythology": "عمرجيڪهاڻيون", "args": "دليل", "rime": "رائيم", "planetzoo": "ڌرتيجوچڙياگھر", "outpost2": "آئوٽپوسٽ2", "banished": "منع_ٿيل", "caesar3": "سيزر3", "redalert": "ڳاڙهوخطرو", "civilization6": "تهذيب6", "warcraft2": "وارڪرافٽ2", "commandandconquer": "حڪمڏيوفتحڪريو", "warcraft3": "وارڪرافٽ3", "eternalwar": "دائميجنگ", "strategygames": "حڪمت_عملي_واريون_رانديون", "anno2070": "سال2070", "civilizationgame": "تهذيبجيراند", "civilization4": "سولائيزيشن4", "factorio": "فيڪٽوريو", "dungeondraft": "ڊنجنڊرافٽ", "spore": "spore", "totalwar": "ڪلجنگ", "travian": "ٽريوين", "forts": "قلعا", "goodcompany": "سٺيسنگت", "civ": "سِوِل", "homeworld": "گھرسنسار", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "روشنيکانتيزترين", "forthekings": "بادشاهن_لاءِ", "realtimestrategy": "ريئلٽائيماسٽريٽيجي", "starctaft": "اسٽاركرافٽ", "sidmeierscivilization": "سڊمائيرزسويلائيزيشن", "kingdomtwocrowns": "بادشاهيٻهتاج", "eu4": "eu4", "vainglory": "وڏائي", "ww40k": "ww40k", "godhood": "خدائيت", "anno": "اينو", "battletech": "بيٽلٽيڪ", "malifaux": "مالیفو", "w40k": "w40k", "hattrick": "هيٽرڪ", "davesfunalgebraclass": "ڊيوزفنالجبراڪلاس", "plagueinc": "طاعونانڪ", "theorycraft": "نظريوسازي", "mesbg": "mesbg", "civilization3": "تمدن3", "4inarow": "4لڳاتار", "crusaderkings3": "صليبيبادشاهن3", "heroes3": "heroes3", "advancewars": "اڳواٽجنگيون", "ageofempires2": "ageofempires2", "disciples2": "شاگرد2", "plantsvszombies": "ٻوٽاvsزومبيز", "giochidistrategia": "راند_جا_رانديون", "stratejioyunları": "stratejioyunları", "europauniversalis4": "يورپايونيورسالس4", "warhammervermintide2": "وارهيمرورمائنٽائيڊ2", "ageofwonders": "عجائباتجودور", "dinosaurking": "ڊائناسورڪنگ", "worldconquest": "دنياتيفتح", "heartsofiron4": "آهنيدليون4", "companyofheroes": "ڪمپنيآفهيروز", "battleforwesnoth": "battleforwesnoth", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "وارهيمرڪلٽيم", "goosegooseduck": "بتختبتختبطخ", "phobies": "فوبيز", "phobiesgame": "phobiesگیم", "gamingclashroyale": "گيمنگڪليشرائيل", "adeptusmechanicus": "ايڊيپٽسميڪانيڪس", "outerplane": "outerplane", "turnbased": "ٽرن_بيسڊ", "bomberman": "بمبرمين", "ageofempires4": "ageofempires4", "civilization5": "سولائيزيشن5", "victoria2": "victoria2", "crusaderkings": "صليبيبادشاهن", "cultris2": "cultris2", "spellcraft": "جادوگري", "starwarsempireatwar": "اسٽارورزايمپائراٽوار", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "حڪمت_عملي", "popfulmail": "ڀرپورميل", "shiningforce": "چمڪندڙطاقت", "masterduel": "ماسٽرڊيول", "dysonsphereprogram": "ڊائسنجيدائريپروگرام", "transporttycoon": "ٽرانسپورٽ_ٽائيڪون", "unrailed": "بيريل", "magicarena": "جادوئيميدان", "wolvesville": "wolvesville", "ooblets": "اوبليٽس", "planescapetorment": "planescapetorment", "uplandkingdoms": "uplandkingdoms", "galaxylife": "ڪهڪشانجيزندگي", "wolvesvilleonline": "وولوزوِلآنلائن", "slaythespire": "slaythespireکيسلائي", "battlecats": "جنگيبليون", "sims3": "سمز3", "sims4": "سمس4", "thesims4": "thesims4", "thesims": "د<PERSON><PERSON><PERSON>", "simcity": "سمسٽي", "simcity2000": "سمسٽي2000", "sims2": "سمز2", "iracing": "آئيريسنگ", "granturismo": "گرانٽورزمو", "needforspeed": "رفتارجيضرورت", "needforspeedcarbon": "نيڊفارسپيڊڪاربن", "realracing3": "ريئلريسنگ3", "trackmania": "ٽريڪمينيا", "grandtourismo": "گرانڊٽورزمو", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "دسِمز2", "thesims3": "ديسمز3", "thesims1": "دسمز1", "lossims4": "لاسسمز4", "fnaf": "fnaf", "outlast": "ٻينکانباقيرهڻ", "deadbydaylight": "deadbydaylight", "alicemadnessreturns": "ايلسمئڊنيسرٽرنز", "darkhorseanthology": "ڪاريگهوڙيانٿالاجي", "phasmophobia": "ڀوتنجوخوف", "fivenightsatfreddys": "fivenightsatfreddys", "saiko": "سائيڪو", "fatalframe": "فيٽلفريم", "littlenightmares": "ننڍيونراتجونخوابون", "deadrising": "مئلنجيهڙائڻ", "ladydimitrescu": "ليڊيڊيمٽريسڪيو", "homebound": "گھربند", "deadisland": "مئلوٽاپو", "litlemissfortune": "ننڍيبدقسمتڇوڪري", "projectzero": "پروجيڪٽزيرو", "horory": "خوفناڪ", "jogosterror": "جوڳوسٽرر", "helloneighbor": "هيلونيائبر", "helloneighbor2": "هيلونيڀر2", "gamingdbd": "گيمنگڊيبيڊي", "thecatlady": "ٻلي_واري_عورت", "jeuxhorreur": "j<PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "هارر_گيمنگ", "magicthegathering": "ميجڪدگيدرنگ", "mtg": "mtg", "tcg": "ٽيسيجي", "cardsagainsthumanity": "انسانيتجيخلافڪارڊ", "cribbage": "ڪرببج", "minnesotamtg": "مينيسوٽامtg", "edh": "edh", "monte": "مونٽي", "pinochle": "پنڪل", "codenames": "ڪوڊنيمس", "dixit": "ڊڪسٽ", "bicyclecards": "سائيڪلڪارڊ", "lor": "lor", "euchre": "يوڪر", "thegwent": "دگوينٽ", "legendofrunetera": "ليجنڊآفرونيٽيرا", "solitaire": "سوليٽيئر", "poker": "پوڪر", "hearthstone": "هيرٿ_اسٽون", "uno": "يونو", "schafkopf": "شافڪوف", "keyforge": "ڪيفورج", "cardtricks": "تاشَجونُڪَرَيون", "playingcards": "کارڊکيڏڻ", "marvelsnap": "مارويلسنيپ", "ginrummy": "جنرمي", "netrunner": "نيٽرنر", "gwent": "gwent", "metazoo": "ميٽازو", "tradingcards": "ٽريڊنگڪارڊز", "pokemoncards": "پوڪيمونڪارڊ", "fleshandbloodtcg": "فليشاينڊبلڊٽيسيجي", "sportscards": "اسپورٽسڪارڊ", "cardfightvanguard": "ڪارڊفائيٽوينگارڊ", "duellinks": "ڊيولِنڪس", "spades": "️", "warcry": "جنگيندڙنعرو", "digimontcg": "ڊجيمونٽسيجي", "toukenranbu": "toukenranbu", "kingofhearts": "دليجوبادشاهه", "truco": "ٽروڪو", "loteria": "لوٽيريا", "hanafuda": "هانافودا", "theresistance": "مقابلو", "transformerstcg": "ٽرانسفارمرزٽي_سي_جي", "doppelkopf": "ڊاپيلڪاف", "yugiohcards": "يوگيوڪارڊس", "yugiohtcg": "yugiohtcg", "yugiohduel": "يوگيوهڊيول", "yugiohocg": "يوگيوآيوسيجي", "dueldisk": "ڊيولڊسڪ", "yugiohgame": "يوگيوهگيم", "darkmagician": "ڪاروجادوگر", "blueeyeswhitedragon": "نيريونکنيونچٽوڊريگن", "yugiohgoat": "yugiohgoat", "briscas": "برسڪاس", "juegocartas": "j<PERSON><PERSON><PERSON><PERSON>", "burraco": "بوراڪو", "rummy": "رمي", "grawkarty": "گراوڪارٽي", "dobble": "ڊبل", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "جوگوس_دي_ڪارتاس", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "تاشنجيراند", "carteado": "ڪارٽيڊو", "sueca": "سويڪا", "beloteonline": "بيلوٽآنلائن", "karcianki": "ڪارسيانڪي", "battlespirits": "جنگيروحون", "battlespiritssaga": "بيٽلاسپرٽساگا", "jogodecartas": "تاشجيراند", "žolíky": "زوليڪي", "facecard": "فيسڪارڊ", "cardfight": "ڪارڊفائيٽ", "biriba": "بِرِيبا", "deckbuilders": "ڊيڪٺاهيندڙ", "marvelchampions": "مارولچيمپئنز", "magiccartas": "جادوئيڪارٽا", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "شيڊوورس", "skipbo": "سڪپبو", "unstableunicorns": "غيرمستحڪميونيڪارن", "cyberse": "سائبرسي", "classicarcadegames": "ڪلاسڪآرڪيڊگيمس", "osu": "osu", "gitadora": "gitadora", "dancegames": "ناچ_جون_رانديون", "fridaynightfunkin": "جمعيجيراتجوجوش", "fnf": "fnf", "proseka": "پروسيڪا", "projectmirai": "پروجيڪٽميرائي", "projectdiva": "پروجيڪٽ_ڊيوا", "djmax": "djmax", "guitarhero": "گٽارهيرو", "clonehero": "ڪلونھيرو", "justdance": "بسناچ", "hatsunemiku": "هاتسونيميڪو", "prosekai": "prosekai", "rocksmith": "راڪسمٿ", "idolish7": "idolish7", "rockthedead": "مئيلنکيجوش", "chunithm": "چونيٿم", "idolmaster": "آئيڊولماسٽر", "dancecentral": "ڊانسسينٽرل", "rhythmgamer": "تالگيمر", "stepmania": "اسٽيپمينيا", "highscorerythmgames": "هاءِسڪوررِدمگيمز", "pkxd": "pkxd", "sidem": "سائيڊيم", "ongeki": "اونگيڪي", "soundvoltex": "ساؤنڊوولٽيڪس", "rhythmheaven": "تالتانديل", "hypmic": "هائپمڪ", "adanceoffireandice": "باهءَجونرقصبرف", "auditiononline": "آڊيشن_آن_لائن", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "تالجيراند", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "تالڊاڪٽر", "cubing": "ڪيوبنگ", "wordle": "ورڊل", "teniz": "ٽينس", "puzzlegames": "پزلگيمز", "spotit": "ڏسيو", "rummikub": "رمي_ڪب", "blockdoku": "بلاڪڊوڪو", "logicpuzzles": "منطقيپهيليون", "sudoku": "سوڊوڪو", "rubik": "روبڪ", "brainteasers": "دماغي_ڪسرتون", "rubikscube": "روبڪسڪيوب", "crossword": "ڪراس_ورڊ", "motscroisés": "موٽسڪروازي", "krzyżówki": "لفظيجمبازيون", "nonogram": "ناناگرام", "bookworm": "ڪتابپيارو", "jigsawpuzzles": "جِگساپزل", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "معما", "riddles": "پهيليون", "rompecabezas": "پزل", "tekateki": "ٽيڪاٽيڪي", "inside": "اندر", "angrybirds": "ڪاوڙيلپکي", "escapesimulator": "فرارسيميوليٽر", "minesweeper": "مائنسويپر", "puzzleanddragons": "پزلاینڈڈریگنز", "crosswordpuzzles": "ڪراس_ورڊ_پزلز", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesgame", "puzzlesport": "پزلاسپورٽ", "escaperoomgames": "escaperoomgames", "escapegame": "فرارجيراند", "3dpuzzle": "3ڊيپزل", "homescapesgame": "هومسڪيپسگيم", "wordsearch": "لفظلڀ", "enigmistica": "معمو", "kulaworld": "ڪولاورلڊ", "myst": "myst", "riddletales": "رازجونڪهاڻيون", "fishdom": "مڇيءَجومُلڪ", "theimpossiblequiz": "ناممڪنسوالنامو", "candycrush": "ڪينڊيڪرش", "littlebigplanet": "ننڍيوڏيسياري", "match3puzzle": "ميچ3پزل", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "ڪتماريدماسي", "kwirky": "ڪوِرڪي", "rubikcube": "روبڪس_ڪيوب", "cuborubik": "ڪيوبروبڪ", "yapboz": "ڳالهيونڪريو", "thetalosprinciple": "thetalosprinciple", "homescapes": "گھرسجاڳ", "puttputt": "ٽِڪٽِڪ", "qbert": "qbert", "riddleme": "معماڪيوڪر", "tycoongames": "ٽائيڪونگيمز", "cubosderubik": "روبڪڪيوب", "cruciverba": "ڪراسورڊ", "ciphers": "سائ<PERSON><PERSON>ز", "rätselwörter": "رازجالفظ", "buscaminas": "بسڪاميناس", "puzzlesolving": "پزلحلڪرڻ", "turnipboy": "ٽرنپڇوڪرو", "adivinanzashot": "پهيليونشاٽ", "nobodies": "ڪوبهنه", "guessing": "اندازو", "nonograms": "نونوگرامز", "kostkirubika": "kostkirubika", "crypticcrosswords": "رازدارڪراسورڊ", "syberia2": "سائبيريا2", "puzzlehunt": "puzzlehunt", "puzzlehunts": "پزل_شڪار", "catcrime": "ٻليجرم", "quebracabeça": "پزل", "hlavolamy": "دماغيپزلز", "poptropica": "پاپٽروپيڪا", "thelastcampfire": "آخريڪيمپفائر", "autodefinidos": "خودبيانڪيل", "picopark": "پڪوپارڪ", "wandersong": "ڀٽڪڻ_جو_گيت", "carto": "ڪارٽو", "untitledgoosegame": "بيعنوانجھنگوسواري", "cassetête": "دماغ_جو_جنجال", "limbo": "لمبو", "rubiks": "روبڪس", "maze": "بھولڀُليون", "tinykin": "ننڍڙاماڻهو", "rubikovakostka": "روبڪسڪيوب", "speedcube": "اسپيڊڪيوب", "pieces": "ٽڪرا", "portalgame": "پورٽلگيم", "bilmece": "بِلمَيس", "puzzelen": "puzzelen", "picross": "پڪراس", "rubixcube": "روبڪسڪيوب", "indovinelli": "پهيليون", "cubomagico": "ڪيوبوماجيڪو", "mlbb": "ايم_ايل_بي_بي", "pubgm": "pubgm", "codmobile": "codموبائل", "codm": "codm", "twistedwonderland": "ٽوِسٽيڊونڊرلينڊ", "monopoly": "اجاروداري", "futurefight": "مستقبلجيجنگ", "mobilelegends": "موبائيللجنڊز", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "اڪيلوگدڙ", "gacha": "گاچا", "wr": "wr", "fgo": "fgo", "bitlife": "بٽلائف", "pikminbloom": "پڪمنبلوم", "ff": "ff", "ensemblestars": "اينسمبل_اسٽارز", "asphalt9": "اسفالٽ9", "mlb": "ايم_ايل_بي", "cookierunkingdom": "ڪوڪيرنڪنگڊم", "alchemystars": "الڪيميءجاتارا", "stateofsurvival": "بقاجيحالت", "mycity": "منهنجوشهر", "arknights": "آرڪنائيٽس", "colorfulstage": "رنگينمنڇ", "bloonstowerdefense": "بلونزٽاورڊفينس", "btd": "btd", "clashroyale": "كلاشرائيل", "angela": "اينجيلا", "dokkanbattle": "ڊوڪن_بيٽل", "fategrandorder": "فيٽگرانڊآرڊر", "hyperfront": "هائپرفرنٽ", "knightrun": "نائيٽرن", "fireemblemheroes": "فائرايمبليمهيروز", "honkaiimpact": "honkaiimpact", "soccerbattle": "فٽبالجنگ", "a3": "a3", "phonegames": "فونگيمز", "kingschoice": "بادشاهجيچونڊ", "guardiantales": "محافظقصا", "petrolhead": "پيٽرولجوديوانو", "tacticool": "ٽيڪٽيڪول", "cookierun": "ڪوڪيرن", "pixeldungeon": "پکسلڊنجن", "arcaea": "آرڪيا", "outoftheloop": "خبرنه", "craftsman": "ڪاريگر", "supersus": "انتهائيمشڪوڪ", "slowdrive": "آهستودرائيو", "headsup": "خب<PERSON>دار", "wordfeud": "لفظجنگ", "bedwars": "بيڊوارز", "freefire": "فريفائر", "mobilegaming": "موبائيلگيمنگ", "lilysgarden": "ليليجوباغ", "farmville2": "فارم_ويل2", "animalcrossing": "اينيملڪراسنگ", "bgmi": "bgmi", "teamfighttactics": "ٽيمفائيٽٽيڪٽڪس", "clashofclans": "ڪلاشآفڪلانز", "pjsekai": "pjsekai", "mysticmessenger": "مسٽڪميسينجر", "callofdutymobile": "ڪالآفڊيوٽيموبائل", "thearcana": "آرڪانا", "8ballpool": "8بالپول", "emergencyhq": "ايمرجنسي_هيڊڪوارٽر", "enstars": "اينسٽارس", "randonautica": "randonautica", "maplestory": "ميپلسٽوري", "albion": "البیون", "hayday": "هيڊي", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "ايزورلين", "shakesandfidget": "شيڪسـ_اينڊ_فِجيٽ", "ml": "ml", "bangdream": "بينگڊريم", "clashofclan": "clashofclan", "starstableonline": "اسٽارسٽيبلآنلائن", "dragonraja": "ڊريگنراجا", "timeprincess": "ٽائيمپرنسيس", "beatstar": "بيٽاسٽار", "dragonmanialegend": "ڊريگنمينياليجنڊ", "hanabi": "هانابي", "disneymirrorverse": "ڊزنيمررورس", "pocketlove": "جيبجيمحبت", "androidgames": "اينڊرائيڊگيمز", "criminalcase": "جرميڪيس", "summonerswar": "سمونرزوار", "cookingmadness": "کڪنگجونون", "dokkan": "ڊوڪان", "aov": "aov", "triviacrack": "ٽريوياڪريڪ", "leagueofangels": "فرشتنجيليگ", "lordsmobile": "لارڊزموبائيل", "tinybirdgarden": "ننڍڙوپکيڙوباغ", "gachalife": "گچالائف", "neuralcloud": "نيورلڪلائوڊ", "mysingingmonsters": "منهنجاڳائيندڙراکشس", "nekoatsume": "نيڪواٽسومي", "bluearchive": "بليو_آرڪائيو", "raidshadowlegends": "ريڊشيڊوليجنڊز", "warrobots": "جنگيروبوٽس", "mirrorverse": "آئيني_ڪائنات", "pou": "pou", "warwings": "جنگيجهاز", "fifamobile": "فيفاموبائل", "mobalegendbangbang": "موباليجنڊبينگبينگ", "evertale": "ايورٽيل", "futime": "مزوجووقت", "antiyoy": "اينٽيائي", "apexlegendmobile": "apexlegendmobile", "ingress": "داخلا", "slugitout": "مقابلوڪريو", "mpl": "mpl", "coinmaster": "سڪا_ماسٽر", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "پالتوسنگي", "gameofsultans": "سلطانن_جي_راند", "arenabreakout": "ايريناbreakout", "wolfy": "وُلفي", "runcitygame": "شھرجيراند", "juegodemovil": "موبائيلگيم", "avakinlife": "avakinlife", "kogama": "ڪوگاما", "mimicry": "نقل", "blackdesertmobile": "بليڪڊيزرٽموبائيل", "rollercoastertycoon": "رولرڪوسٽرٽائيڪون", "grandchase": "گرانڊچيس", "bombmebrasil": "بمبميبرازيل", "ldoe": "ldoe", "legendonline": "ليجنڊآنلائن", "otomegame": "اوٽوميگيم", "mindustry": "منڊسٽري", "callofdragons": "ڊريگنزجيسڏ", "shiningnikki": "چمڪندڙنڪي", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "رستونٿي", "sealm": "سيالم", "shadowfight3": "شيڊوفائيٽ3", "limbuscompany": "لمبسڪمپني", "demolitionderby3": "ڊيموليشنڊربي3", "wordswithfriends2": "لفظنسانگڏدوست2", "soulknight": "روحڪشتري", "purrfecttale": "purrfecttaleسنڌي", "showbyrock": "شوبائيراڪ", "ladypopular": "ليڊيپاپيولر", "lolmobile": "lolموبائيل", "harvesttown": "فصلڳوٺ", "perfectworldmobile": "پرفيڪٽورلڊموبائيل", "empiresandpuzzles": "سلطنتونپزلز", "empirespuzzles": "سلطنتونجيونپزلز", "dragoncity": "ڊريگنسٽي", "garticphone": "گارٽڪفون", "battlegroundmobileind": "جنگجوميدانموبائيلانڊ", "fanny": "ڪولهه", "littlenightmare": "ننڍوخوفناڪخواب", "aethergazer": "ايٿرگيزر", "mudrunner": "مٽيءَ__ڊوڙندڙ", "tearsofthemis": "دمشڪجامايون", "eversoul": "هميشه_روح", "gunbound": "گن_باؤنڊ", "gamingmlbb": "گيمنگmlbb", "dbdmobile": "dbdmobile", "arknight": "آرڪنائيٽ", "pristontale": "پرسٽنٽيل", "zombiecastaways": "زومبيڀڄڻوارا", "eveechoes": "eveechoes", "jogocelular": "جوگوسيلولر", "mariokarttour": "ماريوڪارٽٽور", "zooba": "زوبا", "mobilelegendbangbang": "موبائيلليجنڊبينگبينگ", "gachaclub": "گچاڪلب", "v4": "v4", "cookingmama": "ڪوڪنگماما", "cabalmobile": "cabalmobile", "streetfighterduel": "اسٽريٽفائٽرڊيول", "lesecretdhenri": "هينريجوراز", "gamingbgmi": "گيمنگbgmi", "girlsfrontline": "گرلزفرنٽلائن", "jurassicworldalive": "جراسڪورلڊالائيو", "soulseeker": "روحڳولڻوارو", "gettingoverit": "انکوڇڏيڏيڻ", "openttd": "openttd", "onepiecebountyrush": "ونپيسباؤنٽيرش", "moonchaistory": "موڻچائيڪهاڻي", "carxdriftracingonline": "ڪارxڊرفٽريسنگآنلائن", "jogosmobile": "جوگوزموبائل", "legendofneverland": "نيورلينڊجيافسانو", "pubglite": "پبجيلائيٽ", "gamemobilelegends": "گيممبائيلليجنڊس", "timeraiders": "ٽائيمريڊرز", "gamingmobile": "موبائيلگيمنگ", "marvelstrikeforce": "مارولسٽرائڪفورس", "thebattlecats": "ٻليونجيجنگ", "dnd": "ڊياينڊي", "quest": "سوال", "giochidiruolo": "رولپلينگگيمز", "dnd5e": "dnd5e", "rpgdemesa": "آررپيجيٽيبلٽاپ", "worldofdarkness": "اونداهيجيدنيا", "travellerttrpg": "مسافرttrpg", "2300ad": "2300ع", "larp": "لارپ", "romanceclub": "رومانسڪلب", "d20": "d20", "pokemongames": "پوڪيمونراند", "pokemonmysterydungeon": "پوڪيمونمسٽريڊنجن", "pokemonlegendsarceus": "پوڪيمونليجنڊسآرسيس", "pokemoncrystal": "پوڪيمونڪرسٽل", "pokemonanime": "پوڪيمونانيمي", "pokémongo": "پوڪيمونگو", "pokemonred": "پوڪيمونريڊ", "pokemongo": "پوڪيمونگو", "pokemonshowdown": "پوڪيمونشاؤڊائون", "pokemonranger": "پوڪيمونرينجر", "lipeep": "لپپيپ", "porygon": "پوريگون", "pokemonunite": "پوڪيمونيونائيٽ", "entai": "hentai", "hypno": "هپنو", "empoleon": "ايمپولين", "arceus": "آرسيس", "mewtwo": "ميوٽو", "paldea": "پالديا", "pokemonscarlet": "پوڪيمونسڪارليٽ", "chatot": "چيٽاٽ", "pikachu": "pikachu", "roxie": "روڪسي", "pokemonviolet": "پوڪيمونوائلٽ", "pokemonpurpura": "پوڪيمونپرپورا", "ashketchum": "ايشڪيچم", "gengar": "گینگار", "natu": "نيچو", "teamrocket": "ٽيمراڪيٽ", "furret": "فرٽ", "magikarp": "ماڳيڪارپ", "mimikyu": "میمیکیو", "snorlax": "سنورلیکس", "pocketmonsters": "پاڪيٽمانسٽرز", "nuzlocke": "نُزلاڪ", "pokemonplush": "پوڪيمونپلش", "teamystic": "ٽيممِسٽڪ", "pokeball": "پوڪيبال", "charmander": "چارمينڊر", "pokemonromhack": "پوڪيمونرومهيڪ", "pubgmobile": "pubgmobile", "litten": "لٽن", "shinypokemon": "چمڪندڙپوڪيمون", "mesprit": "مسپرٽ", "pokémoni": "پوڪيموني", "ironhands": "لوهي_هٿ", "kabutops": "ڪابوٽپس", "psyduck": "سائيڊڪ", "umbreon": "umbreon", "pokevore": "پوڪيور", "ptcg": "ptcg", "piplup": "پپلپ", "pokemonsleep": "پوڪيمونننڊ", "heyyoupikachu": "هيتونپيڪاچو", "pokémonmaster": "پوڪيمونماسٽر", "pokémonsleep": "پوڪيمونسليپ", "kidsandpokemon": "ٻارَپوڪيمون", "pokemonsnap": "پوڪيمونسنيپ", "bulbasaur": "بلباسور", "lucario": "لوڪاريو", "charizar": "چاريزار", "shinyhunter": "چمڪندڙشڪاري", "ajedrez": "شطرنج", "catur": "چٽر", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "شطرنج", "schaken": "شطرنج", "skak": "سڪاڪ", "ajedres": "aje<PERSON>s", "chessgirls": "شطرنججون_ڇوڪريون", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "ورلڊبلٽز", "jeudéchecs": "شطرنج_جي_راند", "japanesechess": "جاپاني_شطرنج", "chinesechess": "چينيشطرنج", "chesscanada": "شطرنجڪئناڊا", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "کُليون", "rook": "رُڪ", "chesscom": "شطرنجڪام", "calabozosydragones": "ڪالابوزوس__ڊريگونز", "dungeonsanddragon": "ڊنجنزاينڊڊريگن", "dungeonmaster": "ڊنجنماسٽر", "tiamat": "تيامات", "donjonsetdragons": "ڊنجنسآندڊريگنس", "oxventure": "oxventure", "darksun": "اونداهوسج", "thelegendofvoxmachina": "voxmachinaجيڏانهن", "doungenoanddragons": "ڊنجنزاينڊڊريگنز", "darkmoor": "ڊارڪمور", "minecraftchampionship": "minecraftchampionship", "minecrafthive": "مائنڪرافٽهائيو", "minecraftbedrock": "مائنڪرافٽبيڊراڪ", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "هائيپڪسلاسڪائيبلاڪ", "minetest": "مائن_ٽيسٽ", "hypixel": "هائيپڪسل", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "شمعجيشعله", "fru": "فرو", "addons": "ايڊآنز", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "اسڪائيبلاڪ", "minecraftpocket": "مائنڪرافٽپاڪيٽ", "minecraft360": "minecraft360", "moddedminecraft": "موڊڊمائنڪرافٽ", "minecraftps4": "minecraftps4", "minecraftpc": "مائن_ڪرافٽ_پي_سي", "betweenlands": "وچولينڊز", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftشهر", "pcgamer": "پيسيگيمر", "jeuxvideo": "وڊيوگيمز", "gambit": "گيمبٽ", "gamers": "گي<PERSON><PERSON><PERSON>", "levelup": "ليولاَپ", "gamermobile": "گيمرموبائل", "gameover": "گيمختم", "gg": "gg", "pcgaming": "پيسيگيمنگ", "gamen": "گيمن", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "پيسيگيمز", "casualgaming": "آرامسانراند", "gamingsetup": "گيمنگ_سيٽ_اپ", "pcmasterrace": "پي_سي_ماسٽر_ريس", "pcgame": "پيسيگيم", "gamerboy": "گيمربوائي", "vrgaming": "ويآرگيمنگ", "drdisrespect": "ڊاڪٽربيعزتي", "4kgaming": "4kگيمنگ", "gamerbr": "گیمربي", "gameplays": "گيمپلي", "consoleplayer": "ڪنسولپليئر", "boxi": "بوڪسي", "pro": "پرو", "epicgamers": "ايپڪگيمرز", "onlinegaming": "آن_لائن_گيمنگ", "semigamer": "اڌوگيمر", "gamergirls": "گيمرڇوڪريون", "gamermoms": "گيمرماڙيون", "gamerguy": "گيمرڇوڪرو", "gamewatcher": "گيم_واچر", "gameur": "گيمر", "grypc": "grypc", "rangugamer": "رنگوگيمر", "gamerschicas": "گيمرڇوڪريون", "otoge": "otoge", "dedsafio": "ڊيڊسافيو", "teamtryhard": "ٽيمڪوششڪندڙ", "mallugaming": "ملوگيمنگ", "pawgers": "پاؤگرز", "quests": "ڳولائون", "alax": "الیکس", "avgn": "avgn", "oldgamer": "پراڻوگيمر", "cozygaming": "آرامسانگيمنگ", "gamelpay": "گيملپلي", "juegosdepc": "پيسيجيونراميون", "dsswitch": "dsسوچ", "competitivegaming": "مقابلي_گيمنگ", "minecraftnewjersey": "minecraftnewjersey", "faker": "جعلي", "pc4gamers": "pc4gamers", "gamingff": "گ<PERSON><PERSON><PERSON><PERSON>ff", "yatoro": "ياتورو", "heterosexualgaming": "heterosexualgaming", "gamepc": "گيمپيسي", "girlsgamer": "ڇوڪرينگيمر", "fnfmods": "fnfmods", "dailyquest": "روزانوسفر", "gamegirl": "گيمگرل", "chicasgamer": "چڪاسگيمر", "gamesetup": "گيم_سيٽ_اپ", "overpowered": "اوور_پاورڊ", "socialgamer": "سماجيگيمر", "gamejam": "گيم_جام", "proplayer": "پروپليئر", "roleplayer": "رولپليئر", "myteam": "منهنجيٽيم", "republicofgamers": "گيمرزجيجمهوريه", "aorus": "aorus", "cougargaming": "ڪوگرگيمنگ", "triplelegend": "ٽريپلليجنڊ", "gamerbuddies": "گيمربڊيز", "butuhcewekgamers": "گيمرڇوڪرينجيضرورت", "christiangamer": "عيسائيگيمر", "gamernerd": "گيمرنرڊ", "nerdgamer": "نردگيمر", "afk": "afk", "andregamer": "ايندريگيمر", "casualgamer": "آرامگيمر", "89squad": "89اسڪواڊ", "inicaramainnyagimana": "اِنيڪارامائنياگِمانا", "insec": "غيرمحفوظ", "gemers": "گي<PERSON><PERSON><PERSON>", "oyunizlemek": "oyunizlemek", "gamertag": "گيمرٽيگ", "lanparty": "لين_پارٽي", "videogamer": "ويڊيوگيمر", "wspólnegranie": "گڏجيراند", "mortdog": "mortdog", "playstationgamer": "پليسٽيشنگيمر", "justinwong": "جسٽن_وونگ", "healthygamer": "صحتمندگيمر", "gtracing": "جي_ٽي_ريسنگ", "notebookgamer": "نوٽبڪگيمر", "protogen": "پروٽوجين", "womangamer": "عورتگيمر", "obviouslyimagamer": "ظاهرآهيآئونگيمرآهيان", "mario": "ماريو", "papermario": "ڪاغذماريو", "mariogolf": "ماريوگولف", "samusaran": "سامُسارن", "forager": "ڳولهيندڙ", "humanfallflat": "انسانيڪريپڙي", "supernintendo": "سپرننٽينڊو", "nintendo64": "نينٽينڊو64", "zeroescape": "صفرفرار", "waluigi": "والوئيجي", "nintendoswitch": "نینٹینڈوسوِچ", "nintendosw": "نينٽينڊوسوِچ", "nintendomusic": "ننٽينڊوميوزڪ", "sonicthehedgehog": "سونڪدهيجهاگ", "sonic": "سونڪ", "fallguys": "فالگائيز", "switch": "سوچ", "zelda": "زيلڊا", "smashbros": "سماشبروز", "legendofzelda": "ليجنڊآفزيلڊا", "splatoon": "اسپلاٽون", "metroid": "ميٽرائيڊ", "pikmin": "پڪمن", "ringfit": "رنگفٽ", "amiibo": "amiibo", "megaman": "ميگامين", "majorasmask": "مجوراسماسڪ", "mariokartmaster": "ماريوڪارٽماسٽر", "wii": "wii", "aceattorney": "ايسايٽارني", "ssbm": "ssbm", "skychildrenofthelight": "آسمانجاٻار", "tomodachilife": "دوستزندگي", "ahatintime": "ويلاهلئي", "tearsofthekingdom": "بادشاهه_جا_لُڙ", "walkingsimulators": "هلڻجيسيموليٽر", "nintendogames": "نينٽينڊوگيمز", "thelegendofzelda": "زيلڊاجيڪهاڻي", "dragonquest": "ڊريگنڪوئسٽ", "harvestmoon": "چنڊڻوچنڊ", "mariobros": "ماريوڀائر", "runefactory": "رونفيڪٽري", "banjokazooie": "بنجوڪازوئي", "celeste": "سيليسٽ", "breathofthewild": "جهنگلجيسانس", "myfriendpedro": "منهنجودوستپيڊرو", "legendsofzelda": "ليجنڊزآفزيلڊا", "donkeykong": "ڊانڪيڪانگ", "mariokart": "ماريوڪارٽ", "kirby": "ڪربي", "51games": "51گ<PERSON><PERSON><PERSON>", "earthbound": "زمين_سان_جڙيل", "tales": "ڪهاڻيون", "raymanlegends": "ريمانليجنڊز", "luigismansion": "لوئيجيمينشن", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "ٽائيڪونوٽاٽسوجن", "nintendo3ds": "nintendo3ds", "supermariobros": "سپرماريوبروز", "mariomaker2": "ماريوميڪر2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "نينٽينڊوچلي", "tloz": "tloz", "trianglestrategy": "ٽڪنڊيحڪمتعملي", "supermariomaker": "سپرماريوميڪر", "xenobladechronicles3": "زينوبليڊڪرونيڪلز3", "supermario64": "سپرماريو٦٤", "conkersbadfurday": "conkersbadfurday", "nintendos": "نِنٽينڊوز", "new3ds": "نئون3ڊيايس", "donkeykongcountry2": "ڊانڪيڪانگڪنٽري2", "hyrulewarriors": "هائرولواريئرز", "mariopartysuperstars": "ماريوپارٽيسپرسٽارس", "marioandsonic": "ماريوسونڪ", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "نينٽينڊوڪتا", "thezelda": "دزيلڊا", "palia": "پاليا", "marioandluigi": "ماريولوئيجي", "mariorpg": "ماريوآرپيجي", "zeldabotw": "زيلڊابوٽو", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "وائلڊرفٽ", "riven": "رِوِن", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "آرام", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "زائرا", "redcanids": "ڳاڙهاڪتا", "vanillalol": "وينيلا_لول", "wildriftph": "وائلڊرفٽپي_ايڇ", "lolph": "لولف", "leagueoflegend": "ليگآفليجنڊ", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "گراگاس", "leagueoflegendswild": "ليگآفليجنڊزوائلڊ", "adcarry": "اشتهارکڻي", "lolzinho": "لولزينهو", "leagueoflegendsespaña": "leagueoflegendsاسپين", "aatrox": "aatrox", "euw": "يووو", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "ڪائيل", "samira": "سميرا", "akali": "اڪيلي", "lunari": "لوناري", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "ايڪشن", "milio": "مليو", "shaco": "شيڪو", "ligadaslegendas": "ليجنڊزليگ", "gaminglol": "گيمن<PERSON>lol", "nasus": "نسوس", "teemo": "ٽيمو", "zedmain": "زيڊمين", "hexgates": "هيڪسگيٽس", "hextech": "هيڪسٽيڪ", "fortnitegame": "فورٽنائيٽگيم", "gamingfortnite": "گیمنگفورٽنائيٽ", "fortnitebr": "فورٽنائيٽبر", "retrovideogames": "ريٽروويڊيوگيمز", "scaryvideogames": "خوفناڪوڊيوگيمز", "videogamemaker": "ويڊيوگيمٺاهيندڙ", "megamanzero": "ميگامينزيرو", "videogame": "ويڊيوگيم", "videosgame": "وڊيوگيمز", "professorlayton": "پروفيسرليٽن", "overwatch": "اوورواچ", "ow2": "ow2", "overwatch2": "اوورواچ2", "wizard101": "wizard101", "battleblocktheater": "بيٽل_بلاڪ_ٿيٽر", "arcades": "آرڪيڊز", "acnh": "acnh", "puffpals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "فارمنگسميوليٽر", "robloxchile": "roblox<PERSON>لي", "roblox": "رابلاڪس", "robloxdeutschland": "robloxجرمني", "robloxdeutsch": "robloxجرمن", "erlc": "erlc", "sanboxgames": "سينڊباڪس_گيمز", "videogamelore": "ويڊيوگيملور", "rollerdrome": "رولرڊروم", "parasiteeve": "پيراسائيٽايو", "gamecube": "گيمڪيوب", "starcraft2": "اسٽاركرافٽ2", "duskwood": "ڊسڪ_ووڊ", "dreamscape": "خوابنسوانديدار", "starcitizen": "starcitizen", "yanderesimulator": "ياندريسميوليٽر", "grandtheftauto": "گرانڊٿيفٽآٽو", "deadspace": "ڊيڊاسپيس", "amordoce": "مٺيپيار", "videogiochi": "وڊيوگيمز", "theoldrepublic": "پراڻيجمهوريه", "videospiele": "ويڊيوگيمز", "touhouproject": "touhouproject", "dreamcast": "ڊريم_ڪاسٽ", "adventuregames": "ايڊوينچرگيمز", "wolfenstein": "وولفنسٽائن", "actionadventure": "ايڪشن_ايڊوينچر", "storyofseasons": "موسمنجيڪهاڻي", "retrogames": "ريٽروگيمز", "retroarcade": "ريٽروآرڪيڊ", "vintagecomputing": "پراڻوڪمپيوٽنگ", "retrogaming": "ريٽروگيمنگ", "vintagegaming": "پراڻيونگيمنگ", "playdate": "پليڊيٽ", "commanderkeen": "ڪمانڊرڪين", "bugsnax": "بگسنيڪس", "injustice2": "انصاف_نه_ملڻ2", "shadowthehedgehog": "شيڊوھيج_ھاگ", "rayman": "ريمن", "skygame": "آسمانجيراند", "zenlife": "زين_لائف", "beatmaniaiidx": "بيٽمينيائيڊيڪس", "steep": "سخت", "mystgames": "اسرارياڻيونراند", "blockchaingaming": "بلاڪچيئنگيمنگ", "medievil": "قرونوسطيٰ", "consolegaming": "ڪنسولگيمنگ", "konsolen": "ڪنسولن", "outrun": "ڊوڙيوڇڏ", "bloomingpanic": "گلن_جي_ڌڪ", "tobyfox": "ٽوبي_فاڪس", "hoyoverse": "هويوورس", "senrankagura": "سينرانڪاگورا", "gaminghorror": "گیمنگخوف", "monstergirlquest": "monstergirlquest", "supergiant": "سپرجائنٽ", "disneydreamlightvalle": "ڊزنيڊريملائيٽويلي", "farmingsims": "فارمنگسمز", "juegosviejos": "پراڻيونرايون", "bethesda": "بيٿيسڊا", "jackboxgames": "جيڪ_باڪس_گيمز", "interactivefiction": "انٽرايڪٽوفڪشن", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "دالاسٽآفاس2", "amantesamentes": "ساڳيذهنساڳيدل", "visualnovel": "بصريناول", "visualnovels": "وِزوئلناولز", "rgg": "rgg", "shadowolf": "شيڊوولف", "tcrghost": "tcrghost", "payday": "تنخواهوارو_ڏينهن", "chatherine": "چيٽڪيٿرين", "twilightprincess": "شامجيشهزادي", "jakandaxter": "جڪ_اينڊ_ڊيڪسٽر", "sandbox": "سينڊباڪس", "aestheticgames": "جماليخيلون", "novelavisual": "ناولبصري", "thecrew2": "دي_ڪريو2", "alexkidd": "ايلڪسڪڊ", "retrogame": "ريٽروگيم", "tonyhawkproskater": "ٽونيهاڪپروسڪيٽر", "smbz": "smbz", "lamento": "لامينٽو", "godhand": "خدائيهٿ", "leafblowerrevolution": "پتيونوارواروانقلاب", "wiiu": "wiiu", "leveldesign": "ليولڊيزائن", "starrail": "اسٽاررائل", "keyblade": "ڪيبليڊ", "aplaguetale": "اڪوباتڪهاڻي", "fnafsometimes": "fnaفڪڏهنڪڏهن", "novelasvisuales": "بصريناول", "robloxbrasil": "robloxبرازيل", "pacman": "پيڪمين", "gameretro": "گیمریٹرو", "videojuejos": "videoju<PERSON><PERSON>", "videogamedates": "ويڊيوگيمڊيٽس", "mycandylove": "منهنجيمٺائيمحبت", "megaten": "ميگاٽين", "mortalkombat11": "مورٽلڪمبيٽ11", "everskies": "هميشه_آسمان", "justcause3": "justcause3", "hulkgames": "هلڪگيمز", "batmangames": "بيٽمينراندين", "returnofreckoning": "واپسيءَجوحساب", "gamstergaming": "گيمسٽرگيمنگ", "dayofthetantacle": "ٽينٽيڪلجوڏينهن", "maniacmansion": "مينياڪمينشن", "crashracing": "ڪريشريسنگ", "3dplatformers": "3ڊيپليٽفارمرز", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "پراڻيدورجيگيمنگ", "hellblade": "دوزخيتلوار", "storygames": "ڪهاڻيونرانديون", "bioware": "بائيوويئر", "residentevil6": "ريزيڊنٽايول6", "soundodger": "آوازکانبچائيندڙ", "beyondtwosouls": "ٻنسانن_کان_اڳتي", "gameuse": "گيم<PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "offmortisghost", "tinybunny": "ننڍڙوخرگوش", "retroarch": "ريٽروآرچ", "powerup": "پاورءَپ", "katanazero": "ڪتنازيرو", "famicom": "فيميڪام", "aventurasgraficas": "تصويريمهمات", "quickflash": "جلديفليش", "fzero": "ايفزيرو", "gachagaming": "گچاگيمنگ", "retroarcades": "ريٽروآرڪيڊز", "f123": "f123", "wasteland": "ويران_زمين", "powerwashsim": "پاورواشسم", "coralisland": "مرجانيٻيٽ", "syberia3": "سائبيريا3", "grymmorpg": "grymmorpg", "bloxfruit": "بلاڪسفروٽ", "anotherworld": "ٻيدنيا", "metaquest": "ميٽاڪوئسٽ", "animewarrios2": "animewarrios2", "footballfusion": "فٽبالفيوزن", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "خلائيمسافر", "legomarvel": "ليگومارول", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "موڙيندڙڌات", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "شرمجيڪچو", "simulator": "سِميولِيٽر", "symulatory": "سيموليٽري", "speedrunner": "اسپيڊرنر", "epicx": "ايپڪx", "superrobottaisen": "سپرروبوٽٽائيسن", "dcuo": "dcuo", "samandmax": "سامِ_اينڊ_ميڪس", "grywideo": "gry<PERSON>o", "gaiaonline": "گائياآنلائن", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "عجائباتجيدنياآنلائن", "skylander": "اسڪائي_لينڊر", "boyfrienddungeon": "ڇوڪرودوستqaid", "toontownrewritten": "ٽونٽائونريرائيٽڊ", "simracing": "سمريسنگ", "simrace": "سمريس", "pvp": "pvp", "urbanchaos": "شهريبدنظمي", "heavenlybodies": "آسماني_جسم", "seum": "سيوم", "partyvideogames": "پارٽيويڊيوگيمز", "graveyardkeeper": "قبرستان_جو_نگران", "spaceflightsimulator": "خلائيپروازسملیٹر", "legacyofkain": "ڪائنجيورثت", "hackandslash": "ڪٽائيڪرائي", "foodandvideogames": "کاڌووڊيوگيمس", "oyunvideoları": "گيمنگوڊيوز", "thewolfamongus": "اسانجيوچئيڀيڙيو", "truckingsimulator": "ٽرڪنگسميوليٽر", "horizonworlds": "horizonworlds", "handygame": "آسانراند", "leyendasyvideojuegos": "ڏنتڪٿائونوڊيوگيمز", "oldschoolvideogames": "پراڻيونڊيوگيمون", "racingsimulator": "ريسنگسميوليٽر", "beemov": "بي_موو", "agentsofmayhem": "مئيهيمجاايجنٽس", "songpop": "گانوپاپ", "famitsu": "فاميتسو", "gatesofolympus": "اولمپسجادروازا", "monsterhunternow": "مونسٽرهنٽرنائو", "rebelstar": "باغيستارو", "indievideogaming": "انڊيويڊيوگيمنگ", "indiegaming": "انڊيگيمنگ", "indievideogames": "انڊيويڊيوگيمز", "indievideogame": "انڊيوڊيوگيم", "chellfreeman": "چيلفريمن", "spidermaninsomniac": "اسپائيڊرمينانسومنياڪ", "bufffortress": "مضبوطقلعو", "unbeatable": "ناقابلِشڪست", "projectl": "پروجيڪٽl", "futureclubgames": "مستقبلڪلبرانديون", "mugman": "مگمين", "insomniacgames": "بيخوابيجيونرانديون", "supergiantgames": "سپرجائنٽگيمز", "henrystickman": "هينريسٽڪمين", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "سوراخسائنس", "backlog": "بيڪلاگ", "gamebacklog": "گيمنجيڙيل", "gamingbacklog": "گيمنگبيڪلاگ", "personnagejeuxvidéos": "ويڊيوگيمڪردار", "achievementhunter": "ڪاميابيجوشڪاري", "cityskylines": "شهريآسماننُ", "supermonkeyball": "سپرمنڪيبال", "deponia": "ڊيپونيا", "naughtydog": "شيطانڪتو", "beastlord": "جانورن_جو_مالڪ", "juegosretro": "ريٽروگيمز", "kentuckyroutezero": "ڪينٽڪيروٽزيرو", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "ايلنويڪ", "stanleyparable": "اسٽينليپيرابل", "reservatoriodedopamin": "ڊوپامينجوذخيرو", "staxel": "staxel", "videogameost": "ويڊيوگيموسيقي", "dragonsync": "ڊريگنسنڪ", "vivapiñata": "ويواپيناٽا", "ilovekofxv": "منکوفxvسانپيارآهي", "arcanum": "اسرار", "neoy2k": "neoy2k", "pcracing": "پيسيريسنگ", "berserk": "بريڪ", "baki": "باقي", "sailormoon": "سيلرمون", "saintseiya": "سينٽسيا", "inuyasha": "انويوشا", "yuyuhakusho": "يويوهاڪوشو", "initiald": "ابتدائيڊي", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "ڊريگنبالز", "sadanime": "سادانيمي", "darkerthanblack": "ڪاريکانسواءِڳاڙهو", "animescaling": "اينيميجيماپ", "animewithplot": "پلاٽسانانيمي", "pesci": "پيسڪي", "retroanime": "ريٽروانائيم", "animes": "اينيميز", "supersentai": "سُپر_سينٽائي", "samuraichamploo": "سامورائي_چمپلو", "madoka": "مادوڪا", "higurashi": "هِگُراشي", "80sanime": "80جيڏهلانيمي", "90sanime": "90sانيمي", "darklord": "ڊارڪلارڊ", "popeetheperformer": "پوپيپرفارمر", "masterpogi": "ماسٽرپوگي", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "ويرانيم", "2000sanime": "2000sانيمي", "lupiniii": "لوپنٽائي", "drstoneseason1": "ڊاڪٽراسٽونسيزن1", "rapanime": "rapanime", "chargemanken": "چارجمينڪن", "animecover": "اينيميڪور", "thevisionofescaflowne": "thevisionofescaflowne", "slayers": "سليئرز", "tokyomajin": "ٽوڪيوماجن", "anime90s": "anime90s", "animcharlotte": "اينمچارلوٽ", "gantz": "گانٽز", "shoujo": "شوجو", "bananafish": "ڪيلي_مڇي", "jujutsukaisen": "جوجوتسوڪائسن", "jjk": "jjk", "haikyu": "هائيڪيو", "toiletboundhanakokun": "ٽائليٽ_جو_بندھايل_ھناڪو_ڪن", "bnha": "bnha", "hellsing": "ھيلسنگ", "skipbeatmanga": "اسڪپبيٽمانگا", "vanitas": "وينيٽاس", "fireforce": "فائرفورس", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "مستقبلجيڊائري", "fairytail": "فیئرٽیل", "dorohedoro": "dorohedoro", "vinlandsaga": "وِنلينڊساگا", "madeinabyss": "ابيسٺهيل", "parasyte": "پاراسائيٽ", "punpun": "پنپن", "shingekinokyojin": "shingekinokyojin", "mushishi": "موشيشي", "beastars": "بيسٽارز", "vanitasnocarte": "وينيٽاسنوڪارٽي", "mermaidmelody": "جلپريءَجيميلوڊي", "kamisamakiss": "ڪاميساماڪِس", "blmanga": "بي_ايل_مانگا", "horrormanga": "هاررمانگا", "romancemangas": "رومانسمانگاس", "karneval": "ڪارنيوال", "dragonmaid": "ڊريگنميڊ", "blacklagoon": "ڪاروڍنڊ", "kentaromiura": "ڪينٽارو_ميورا", "mobpsycho100": "موبسائيڪو100", "terraformars": "ٽيرافارمرز", "geniusinc": "جينيئسانڪ", "shamanking": "شامنڪنگ", "kurokonobasket": "ڪوروڪونوباسڪيٽ", "jugo": "جوگو", "bungostraydogs": "بنگوآوارهڪتا", "jujustukaisen": "جوجوتسوڪائسن", "jujutsu": "جوجوتسو", "yurionice": "يوريآنآئس", "acertainmagicalindex": "ڪجھ_جادوئي_فهرست", "sao": "سائو", "blackclover": "ڪاروڪلوور", "tokyoghoul": "ٽوڪيوگھول", "onepunchman": "ون_پنچ_مين", "hetalia": "هيتاليا", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "توارو", "crunchyroll": "ڪرنچيرول", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "سيريئسدجيگر", "spyxfamily": "spyxfamily", "rezero": "ريزيرو", "swordartonline": "سوردآرٽآنلائن", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "وندرايگپرائيورٽي", "angelsofdeath": "موت_جا_فرشتا", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "ڊريگنبالسپر", "hypnosismic": "هپناسسمائڪ", "goldenkamuy": "گولڊنڪاموئي", "monstermusume": "مانسٽرموسومي", "konosuba": "konosuba", "aikatsu": "ايڪاتسو", "sportsanime": "اسپورٽسانيمي", "sukasuka": "سُڪاسُڪا", "arwinsgame": "a<PERSON><PERSON><PERSON><PERSON>گيم", "angelbeats": "اينجلبيٽس", "isekaianime": "isekaianime", "sagaoftanyatheevil": "ساگا_آف_تانيا_دي_ايول", "shounenanime": "شونينانيمي", "bandori": "بندوري", "tanya": "تانيا", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "پريٽيڪيور", "theboyandthebeast": "ڇوڪروجانور", "fistofthenorthstar": "اتريوارو_مُٺو", "mazinger": "مازنگر", "blackbuttler": "ڪاروسيوارو", "towerofgod": "ٽاورآفگاڊ", "elfenlied": "ايلفن_لائيڊ", "akunohana": "اڪونوهانا", "chibi": "چبي", "servamp": "سروامپ", "howtokeepamummy": "مامیکيڪائينرکجي", "fullmoonwosagashite": "پورومهينوواسگاشيتي", "shugochara": "شوگوچارا", "tokyomewmew": "ٽوڪيوميوميو", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "پياروخوفناڪ", "martialpeak": "مارشلپيڪ", "bakihanma": "باڪي_هانما", "hiscoregirl": "hiscoregirl", "orochimaru": "اوروچيمارو", "mierukochan": "مييروڪوچان", "dabi": "ڊابي", "johnconstantine": "جاھنڪانسٽنٽائن", "astolfo": "ايسٽولفو", "revanantfae": "revanant<PERSON>e", "shinji": "شنجي", "zerotwo": "زيروٽو", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "نيزوڪو", "monstergirl": "monstergirl", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "ڪاڪاشي", "lenore": "لينور", "benimaru": "بينيمارو", "saitama": "سائيتاما", "sanji": "سانجي", "bakugo": "بڪگو", "griffith": "گرفتھ", "ririn": "ririn", "korra": "ڪوررا", "vanny": "وَني", "vegeta": "ويجيٽا", "goromi": "گورومي", "luci": "لوسي", "reigen": "ريگن", "scaramouche": "اسڪاراموش", "amiti": "amiti", "sailorsaturn": "ملاحساتُرن", "dio": "ڊيو", "sailorpluto": "سيلرپلوٽو", "aloy": "الوئي", "runa": "رونا", "oldanime": "پراڻيانائيم", "chainsawman": "چينساومين", "bungoustraydogs": "بنگوسٽريڊاگس", "jogo": "جوگو", "franziska": "فرانزيسڪا", "nekomimi": "نيڪوميمي", "inumimi": "انوميمي", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "ٽوڪيوريوينجرز", "blackbutler": "ڪاروچاڪر", "ergoproxy": "ايرگوپروڪسي", "claymore": "ڪليمور", "loli": "loli", "horroranime": "هارر_اينيمي", "fruitsbasket": "ميوو_جي_ٽوڪري", "devilmancrybaby": "شيطانانسانروئندڙٻار", "noragami": "نوراگامي", "mangalivre": "منگاليور", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "سينين", "lovelive": "پيارجيوڻ", "sakuracardcaptor": "ساڪوراڪارڊڪيپٽر", "umibenoetranger": "مونکيغيرملڪينهآهيان", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "واعدوڪيلنيورلينڊ", "monstermanga": "مانسٽرمانگا", "yourlieinapril": "توهانجوڪوڙاپريل", "buggytheclown": "buggytheclown", "bokunohero": "بوڪونوهيرو", "seraphoftheend": "سيرافآخرزمانو", "trigun": "ٽرائيگن", "cyborg009": "cyborg009", "magi": "مجوسي", "deepseaprisoner": "گہرےسمندرجوقیدی", "jojolion": "جوجوليون", "deadmanwonderland": "مئلجيءَمُلڪعجيبو", "bannafish": "ڪيلي_مڇي", "sukuna": "سوڪونا", "darwinsgame": "ڊارونزگيم", "husbu": "هزبو", "sugurugeto": "سُگُرُگيٽو", "leviackerman": "ليوي_ايڪرمين", "sanzu": "سانزو", "sarazanmai": "سارازانماءِ", "pandorahearts": "پانڊوراهارٽس", "yoimiya": "يوئيميا", "foodwars": "فوڊوارز", "cardcaptorsakura": "ڪارڊڪيپٽرساڪورا", "stolas": "stolas", "devilsline": "شيطانجيلڪير", "toyoureternity": "تنهنجيابديتائين", "infpanime": "infpانيمي", "eleceed": "elec<PERSON>", "akamegakill": "اڪاميگاڪل", "blueperiod": "نيروزمانو", "griffithberserk": "griffithberserk", "shinigami": "شينيگامي", "secretalliance": "ڳجهوڳاٺ", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "جادوگرجيڪاڻي", "yuki": "يوڪي", "erased": "ختم_ٿي_ويو", "bluelock": "bluelock", "goblinslayer": "گوبلنڪشڪار", "detectiveconan": "ڊٽيڪٽيوڪونان", "shiki": "شيڪي", "deku": "ڊيڪو", "akitoshinonome": "اڪيتوشينونومي", "riasgremory": "رياسگريموري", "shojobeat": "شوجوبيٽ", "vampireknight": "ويمپائرنائيٽ", "mugi": "موگي", "blueexorcist": "بليوايڪسورسسٽ", "slamdunk": "سلامڊنڪ", "zatchbell": "زيچبيل", "mashle": "ميشل", "scryed": "رويو", "spyfamily": "جاسوسخاندان", "airgear": "ايئرگيئر", "magicalgirl": "جادوگرڇوڪري", "thesevendeadlysins": "ستسخترناڪگناهه", "prisonschool": "جيلاسڪول", "thegodofhighschool": "هاءِاسڪولجوخدا", "kissxsis": "ڀيڻچمي", "grandblue": "گرانڊبلو", "mydressupdarling": "منهنجيڊريسڪرڻواريڊارلنگ", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "روزنميڊن", "animeuniverse": "اينيميڪائنات", "swordartonlineabridge": "سورڊآرٽآنلائنابرج", "saoabridged": "saoمختصر", "hoshizora": "هوشيزورا", "dragonballgt": "ڊريگنبالجيٽي", "bocchitherock": "بوچيدروڪ", "kakegurui": "ڪاڪيگوروئي", "mobpyscho100": "موبسائڪو100", "hajimenoippo": "hajimenoippo", "undeadunluck": "مئيمرڻبدقسمتي", "romancemanga": "رومانسمانگا", "blmanhwa": "blمانهوا", "kimetsunoyaba": "kimetsunoyaba", "kohai": "كوهائي", "animeromance": "اينيميرومانس", "senpai": "سينپائي", "blmanhwas": "blمانهواس", "animeargentina": "انيميارجنٽينا", "lolicon": "لوليڪان", "demonslayertothesword": "ڊيمنسليئرڏانهنتلوار", "bloodlad": "بلڊلاڊ", "goodbyeeri": "گڊبائيري", "firepunch": "فائرپنچ", "adioseri": "الوداعايري", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "ڪنيڪومان", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "شوجوآئي", "starsalign": "تارا_مليا", "romanceanime": "رومانسانيمي", "tsundere": "سُنڊيري", "yandere": "ياندير", "mahoushoujomadoka": "ماهوشوجومادوڪا", "kenganashura": "ڪينگن_اشورا", "saointegralfactor": "saointegralfactor", "cherrymagic": "چيريجادو", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "رڪارڊراگناروڪ", "oyasumipunpun": "oyasumipunpun", "meliodas": "ميليوڊاس", "fudanshi": "<PERSON><PERSON><PERSON><PERSON>", "retromanga": "ريٽرومانگا", "highschoolofthedead": "هاءِاسڪولآفدَڊيڊ", "germantechno": "جرمنٽيڪنو", "oshinoko": "اوشينوڪو", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "وِنڊلينڊساگا", "mangaka": "منگاڪا", "dbsuper": "ڊيبيسپر", "princeoftennis": "ٽينسجوشهزادو", "tonikawa": "اُڏامُڏوخوبصورت", "esdeath": "esdeath", "dokurachan": "ڊوڪوراچان", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "قاتلڪلاس", "animemanga": "اينيميمانگا", "bakuman": "بڪومان", "deathparade": "موت_جو_جلوس", "shokugekinosouma": "شوڪوگيڪينوسوما", "japaneseanime": "جاپانيانيمي", "animespace": "اينيميجڳهه", "girlsundpanzer": "ڇوڪرينپينزر", "akb0048": "akb0048", "hopeanuoli": "اميدجونول", "animedub": "اينيميڊب", "animanga": "اينيمانگا", "tsurune": "تسورونے", "uqholder": "uqholder", "indieanime": "انڊيانيمي", "bungoustray": "بنگوسٽري", "dagashikashi": "داگاشيڪاشي", "gundam0": "gundam0", "animescifi": "اينيميسائيفائي", "ratman": "چوہامرد", "haremanime": "haremanime", "kochikame": "ڪوچيڪامي", "nekoboy": "نيڪوبوائي", "gashbell": "گاشبيل", "peachgirl": "آڙُڇوڪري", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "ميڪامسومي", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "يارچنبچڪلب", "dragonquestdai": "ڊريگنڪوئسٽڊائي", "heartofmanga": "مانگاجيدل", "deliciousindungeon": "ڊنجنلذيذ", "manhviyaoi": "مردوياوي", "recordofragnarok": "recordofragnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "مانگاانائيم", "bochitherock": "بوچيدروڪ", "kamisamahajimemashita": "ڪميساماهاجيميماشيتا", "skiptoloafer": "اسڪپٽولوفر", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "ٽيوٽوريلڏاڍوسخت", "overgeared": "overgeared", "toriko": "<PERSON><PERSON>o", "ravemaster": "ريومستاد", "kkondae": "ڪڪونڊي", "chobits": "چوبٽس", "witchhatatelier": "جادوگرڪپڙنجيڪارخاني", "lansizhui": "لانسڙي", "sangatsunolion": "سنگتسُنولائن", "kamen": "ڪامين", "mangaislife": "آمڪاهيحياتآهي", "dropsofgod": "خدا_جا_ڦڙا", "loscaballerosdelzodia": "لاسڪيباليروسڊيلزوڊيا", "animeshojo": "انيميشوجو", "reverseharem": "ريورس_حريم", "saintsaeya": "سينٽسايا", "greatteacheronizuka": "گریٹٽیچرونیزوڪا", "gridman": "گرڊمين", "kokorone": "کوکورونے", "soldato": "سولداتو", "mybossdaddy": "منهنجوباسڊيڊي", "gear5": "گيئر5", "grandbluedreaming": "عظيمنيليخواب", "bloodplus": "بلڊپلس", "bloodplusanime": "بلڊپلسانائيم", "bloodcanime": "bloodcanime", "bloodc": "بلڊڪينسر", "talesofdemonsandgods": "شيطاننديوتائنجيونڪهاڻيون", "goreanime": "goانيمي", "animegirls": "اينيميڇوڪريون", "sharingan": "شيرنگان", "crowsxworst": "ڪائون_بدترين", "splatteranime": "splatteranime", "splatter": "ڇٽڪار", "risingoftheshieldhero": "شيلڊهيروجوابھار", "somalianime": "صومالیانیمے", "riodejaneiroanime": "ريودي_جينيرو_اينيمي", "slimedattaken": "سلائيمڊيٽ_ورتو_ويو", "animeyuri": "animeyuri", "animeespaña": "animeespaña", "animeciudadreal": "animeciudadreal", "murim": "مُورِم", "netjuunosusume": "نيٽجوانوسوسومي", "childrenofthewhales": "وهيلمچينٻار", "liarliar": "ڪوڙوڪوڙو", "supercampeones": "سپرچيمپئنز", "animeidols": "اينيميآئيڊلز", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "ڌرتيرنگڏينهن", "magicalgirls": "جادوئيڇوڪريون", "callofthenight": "راتجوسڏ", "bakuganbrawler": "باڪوگنبرالر", "bakuganbrawlers": "باڪوگنبرالرز", "natsuki": "نٽسڪي", "mahoushoujo": "محوشوجو", "shadowgarden": "پاڇيباغ", "tsubasachronicle": "سوباساڪرانيڪل", "findermanga": "فائنڊرمانگا", "princessjellyfish": "شهزاديڄيليمڇي", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "پيراڊائيزڪس", "kurochan": "ڪروچان", "revuestarlight": "ريويوسٽارلائيٽ", "animeverse": "انيميورس", "persocoms": "پرسوڪومز", "omniscientreadersview": "سڀڪجھڄاڻيندڙپڙهندڙجونظريو", "animecat": "انيميڪيٽ", "animerecommendations": "انيميسفارشون", "openinganime": "کھولڻواريانيمي", "shinichirowatanabe": "شينيچيروواتانابي", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "منهنجيٽينايجرومانٽڪڪاميڊي", "evangelion": "ايوانگيليون", "gundam": "گنڈم", "macross": "macross", "gundams": "گنڊامز", "voltesv": "voltesv", "giantrobots": "وڏاروبوٽ", "neongenesisevangelion": "نيونجينيسيسايوانجيليون", "codegeass": "ڪوڊگياس", "mobilefighterggundam": "موبائيلفائيٽرگنڊم", "neonevangelion": "نيونايوانجيليون", "mobilesuitgundam": "موبائيلسوٽگنڊم", "mech": "ميڪ", "eurekaseven": "يوريڪاسيون", "eureka7": "يوريڪا7", "thebigoanime": "thebigoanime", "bleach": "بليچ", "deathnote": "ڊيٿنوٽ", "cowboybebop": "ڪائوبوائيبيبوپ", "jjba": "جوجوجيعجيبمخاطرو", "jojosbizarreadventure": "جوجوزبزارايڊوينچر", "fullmetalalchemist": "فل_ميٽل_الڪيمسٽ", "ghiaccio": "غياچو", "jojobizarreadventures": "جوجوبزارایڈوینچرز", "kamuiyato": "ڪاموئياتو", "militaryanime": "فوجيانيمي", "greenranger": "گرينرينجر", "jimmykudo": "جيميڪُودو", "tokyorev": "ٽوڪيورِيو", "zorro": "زورو", "leonscottkennedy": "ليونسڪاٽڪينيڊي", "korosensei": "ڪوروسينسي", "starfox": "اسٽارفاڪس", "ultraman": "الٽرامين", "salondelmanga": "سالونڊيلمانگا", "lupinthe3rd": "لوپينٽھيٽھرڊ", "animecity": "اينيميسٽي", "animetamil": "animetamil", "jojoanime": "جوجوانمي", "naruto": "نارتو", "narutoshippuden": "ناروٽوشپوڊن", "onepiece": "ونپيس", "animeonepiece": "انيميونپيس", "dbz": "dbz", "dragonball": "ڊريگن_بال", "yugioh": "يوگيو", "digimon": "ڊجيمون", "digimonadventure": "ڊجيمونايڊوينچر", "hxh": "hxh", "highschooldxd": "هائيسڪولڊيڪسڊي", "goku": "گوکو", "broly": "برولي", "shonenanime": "شونينانائمي", "bokunoheroacademia": "بوڪونوهيرواڪيڊيميا", "jujustukaitsen": "جوجوستوڪائيٽسن", "drstone": "ڊاڪٽرپٿر", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "شونينجمپ", "otaka": "اوٽاڪا", "hunterxhunter": "هنٽرايڪسهنٽر", "mha": "ايم_ايڇ_اي", "demonslayer": "شيطانقاتل", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "ايرنييگر", "myheroacademia": "myheroacademia", "boruto": "بوروتو", "rwby": "rwby", "dandadan": "ڊنڊاڊان", "tomodachigame": "ٽوموداچي_گيم", "akatsuki": "اڪاٽسوڪي", "surveycorps": "سروي_ڪور", "onepieceanime": "ونپيسانائيم", "attaquedestitans": "attaquedestitans", "theonepieceisreal": "theonepieceisreal", "revengers": "ريوينجرز", "mobpsycho": "موبسائيڪو", "aonoexorcist": "aonoexorcist", "joyboyeffect": "جوائي_بوائي_اثر", "digimonstory": "ڊجيمونڪهاڻي", "digimontamers": "ڊجيمونٽيمرز", "superjail": "سپرجيل", "metalocalypse": "metalocalypse", "shinchan": "شنچان", "watamote": "واتاموٽي", "uramichioniisan": "اوراميچي_ڀاءُ", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "گنتاما", "ranma": "رانما", "doraemon": "ڊورايمون", "gto": "gto", "ouranhostclub": "اسانجوميزبانڪلب", "flawlesswebtoon": "بيعيبويبٽون", "kemonofriends": "ڪيمونوفرينڊس", "utanoprincesama": "يوتانوپرنسساما", "animecom": "اينيميڪام", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "يوڪييونا", "nichijou": "نچيجو", "yurucamp": "يوروڪيمپ", "nonnonbiyori": "نانــنانبيوري", "flyingwitch": "اُڏندڙ_جادوگرڻي", "wotakoi": "wotakoi", "konanime": "ڪونانائيم", "clannad": "ڪلاناڊ", "justbecause": "بس_ائين_ئي", "horimiya": "هوريميا", "allsaintsstreet": "آلسينٽساسٽريٽ", "recuentosdelavida": "زندگيءَجاڪهاڻيون"}