{"2048": "2048", "mbti": "mbti", "enneagram": "enneagramm", "astrology": "astrologie", "cognitivefunctions": "kognitivfunktiounen", "psychology": "psychologie", "philosophy": "philosophie", "history": "geschicht", "physics": "physik", "science": "wëssenschaft", "culture": "kultur", "languages": "sproochen", "technology": "technologie", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "astrologiememes", "enneagrammemes": "enneagrammemes", "showerthoughts": "duschengedanken", "funny": "witzeg", "videos": "videoen", "gadgets": "gadgets", "politics": "politik", "relationshipadvice": "bezéiungstipps", "lifeadvice": "<PERSON><PERSON><PERSON><PERSON>", "crypto": "crypto", "news": "news", "worldnews": "weltnews", "archaeology": "archeologie", "learning": "l<PERSON><PERSON><PERSON>", "debates": "debatten", "conspiracytheories": "konspiratiouns", "universe": "universum", "meditation": "meditatioun", "mythology": "mythologie", "art": "konscht", "crafts": "handwierk", "dance": "danz", "design": "design", "makeup": "makeup", "beauty": "sch<PERSON>inheet", "fashion": "fashion", "singing": "sangen", "writing": "schre<PERSON><PERSON>", "photography": "fotografie", "cosplay": "cosplay", "painting": "molen", "drawing": "<PERSON><PERSON><PERSON><PERSON>", "books": "bicher", "movies": "filmer", "poetry": "poesie", "television": "televisioun", "filmmaking": "filmmaking", "animation": "anima<PERSON><PERSON>n", "anime": "anime", "scifi": "scifi", "fantasy": "fantasterei", "documentaries": "dokumentaren", "mystery": "myst<PERSON>", "comedy": "comedy", "crime": "kriminalitéit", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horror", "romance": "romantik", "realitytv": "realitytv", "action": "aktioun", "music": "musek", "blues": "blues", "classical": "klassesch", "country": "land", "desi": "desi", "edm": "edm", "electronic": "elektronesch", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "haus", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "<PERSON><PERSON><PERSON><PERSON>", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "<PERSON><PERSON>n", "concerts": "concerten", "festivals": "festivaler", "museums": "musée<PERSON>n", "standup": "standup", "theater": "theater", "outdoors": "outdoor", "gardening": "gaardenaarbecht", "partying": "feieren", "gaming": "gaming", "boardgames": "brietspiller", "dungeonsanddragons": "dungeonsanddragons", "chess": "schach", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "<PERSON><PERSON>en", "baking": "baachen", "cooking": "kachen", "vegetarian": "vegetarianesch", "vegan": "vegan", "birds": "v<PERSON><PERSON><PERSON>", "cats": "kazen", "dogs": "<PERSON><PERSON><PERSON>", "fish": "<PERSON><PERSON><PERSON>", "animals": "dieren", "blacklivesmatter": "blacklivesmatter", "environmentalism": "ëmweltschutz", "feminism": "feminismus", "humanrights": "mënscherechter", "lgbtqally": "lgbtqally", "stopasianhate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "transally", "volunteering": "benevolat", "sports": "sport", "badminton": "badminton", "baseball": "baseball", "basketball": "basketball", "boxing": "boxen", "cricket": "cricket", "cycling": "vëlo", "fitness": "fitness", "football": "foussball", "golf": "golf", "gym": "fitness", "gymnastics": "gymnastik", "hockey": "hockey", "martialarts": "kampsport", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "lafen", "skateboarding": "skateboarden", "skiing": "ski", "snowboarding": "snowboarden", "surfing": "surfen", "swimming": "schwa<PERSON>n", "tennis": "tennis", "volleyball": "volleyball", "weightlifting": "gewiichterhiewen", "yoga": "yoga", "scubadiving": "tauchen", "hiking": "wanderen", "capricorn": "capricorn", "aquarius": "<PERSON><PERSON><PERSON><PERSON>", "pisces": "pisces", "aries": "widder", "taurus": "s<PERSON><PERSON>", "gemini": "gemini", "cancer": "k<PERSON><PERSON>s", "leo": "leo", "virgo": "virgo", "libra": "waag", "scorpio": "scorpio", "sagittarius": "sagittarius", "shortterm": "kuerzfristeg", "casual": "casual", "longtermrelationship": "langzäitrelatioun", "single": "single", "polyamory": "polyamorie", "enm": "oem", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "<PERSON><PERSON><PERSON>", "bisexual": "bisexuell", "pansexual": "pansexuell", "asexual": "asexuell", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "watchdogs", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kingsquest", "soulreaver": "siele<PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subvers", "legendofspyro": "legend<PERSON><PERSON><PERSON><PERSON>", "rouguelikes": "roguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "sonnenënnergankiwwedegas", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofthestorm": "heldenvumstuerm", "cytus": "cytus", "soulslike": "soulslike", "dungeoncrawling": "kerfënnerduerchstreifen", "jetsetradio": "jetsetradio", "tribesofmidgard": "tribesofmidgard", "planescape": "planescape", "lordsoftherealm2": "lordsoftherealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "patfofexil", "immersivesims": "immersiv<PERSON>ms<PERSON>", "okage": "okage", "juegoderol": "juegoderol", "witcher": "hexer", "dishonored": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "chara<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "immersiv", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "morbidmotivatioun", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "verrécktnoléift", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaofzäit", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "gaslands", "pathfinder": "<PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "shadowrun", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "gravitéitsrush", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "oneshot", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "overlord<PERSON><PERSON><PERSON>", "yourturntodie": "dusbassundrankommenzestrewen", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "maraudeure", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "atomschutzbunker", "gurps": "gurps", "darkestdungeon": "däischterdongong", "eclipsephase": "eclipse<PERSON>s", "disgaea": "disgaea", "outerworlds": "b<PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "dynastywarriors", "skullgirls": "skullgirls", "nightcity": "nightcity", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "madnesscombat", "jaggedalliance2": "jaggedalliance2", "neverwinter": "ni<PERSON><PERSON><PERSON>", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "vergiessewelten", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "kand<PERSON><PERSON><PERSON><PERSON>t", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonwelt", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "ëmmerimmeroase", "hogwartmystery": "hog<PERSON><PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "<PERSON><PERSON><PERSON>", "starfinder": "stärefënner", "goldensun": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "klingenamdonkelen", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "gefalenorden", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "b<PERSON><PERSON>géige<PERSON>", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "deiwelsiwerliewer", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "göttlechkeet", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "alweltblues", "adventurequest": "abenteuerquest", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "roleplay<PERSON>iller", "finalfantasy9": "finalfantasy9", "sunhaven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "talesofsymphonia": "talesofsymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "m<PERSON><PERSON><PERSON><PERSON>", "sacredunderworld": "hellegerënnerwelt", "chainedechoes": "chainedechoes", "darksoul": "<PERSON><PERSON><PERSON><PERSON>", "soulslikes": "soulslikes", "othercide": "<PERSON><PERSON>ä<PERSON>", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "séilenevunderéiwegkeet", "palladiumrpg": "palladiumrpg", "rifts": "rifts", "tibia": "tibia", "thedivision": "thedivision", "hellocharlotte": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "legendev<PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werwollapocalyps", "aveyond": "aveyond", "littlewood": "klengb<PERSON>sch", "childrenofmorta": "ka<PERSON><PERSON><PERSON><PERSON>", "engineheart": "moteurcoeur", "fable3": "fable3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "rollenspill", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "starfield", "oldschoolrevival": "oldschoolrevival", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON>kell<PERSON>", "juegosrpg": "rpgspiller", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bastion": "bastioun", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "skiesofarcadia", "shadowhearts": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyblood", "breathoffire4": "atem_vu_feier4", "mother3": "mamm3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON>", "roleplaygames": "rollespiller", "roleplaygame": "rollespill", "fabulaultima": "f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "witchsheart": "<PERSON><PERSON><PERSON><PERSON>", "harrypottergame": "harry<PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirilamaskerad", "dračák": "draach", "spelljammer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "shadowheartscovenant", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "kinnekräichkënnt", "awplanet": "awplanet", "theworldendswithyou": "dweltenntmatter", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "däischterketzerei", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "äerdmagie", "blackbook": "schwaarzbuch", "skychildrenoflight": "himmelskannervanliicht", "gryrpg": "gryrpg", "sacredgoldedition": "helleggoldeditioun", "castlecrashers": "buer<PERSON><PERSON><PERSON>", "gothicgame": "gothes<PERSON>pill", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "geeschterdrottokio", "fallout2d20": "fallout2d20", "gamingrpg": "gamingrpg", "prophunt": "prophunt", "starrails": "schtäreschinn", "cityofmist": "stadamniewel", "indierpg": "indierpg", "pointandclick": "pointandclick", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "on<PERSON>el<PERSON>", "freeside": "gratissäit", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "doudsstroossopkanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON>", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "geosuprematie", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "seeleniесser", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "nonarygames", "tacticalrpg": "tacticalrpg", "mahoyo": "mahoyo", "animegames": "animespiller", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "éiwegesonata", "princessconnect": "prinzessinconnect", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valo<PERSON><PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "egames", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "liguevundreemer", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gaimen", "overwatchleague": "overwatchliga", "cybersport": "esport", "crazyraccoon": "verrécktewäschbier", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantkompetitiv", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON>", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "ventil", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "éiwegesummer", "goatsimulator": "geesssimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "fräiheetplanet", "transformice": "transformice", "justshapesandbeats": "nëmmefuermenabeats", "battlefield4": "battlefield4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanias", "overcooked": "iwwerkacht", "interplanetary": "interplanetaresch", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7d2d", "deadcells": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "zwergefestung", "foxhole": "fuchsbau", "stray": "stray", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboot", "eyeb": "a<PERSON><PERSON><PERSON>", "blackdesert": "black<PERSON>ert", "tabletopsimulator": "tabletopsimulator", "partyhard": "<PERSON><PERSON><PERSON>", "hardspaceshipbreaker": "hardspaceshipbreaker", "hades": "hades", "gunsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "agespaartmatdemjester", "dinkum": "<PERSON><PERSON><PERSON><PERSON>", "predecessor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rainworld": "reënwelt", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON>", "colonysim": "kolo<PERSON><PERSON>", "noita": "noita", "dawnofwar": "moied<PERSON><PERSON><PERSON><PERSON>", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "däischteranddäischter", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "datingsi<PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "cubeflucht", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "<PERSON><PERSON><PERSON>", "citiesskylines": "stiedsilhouetten", "defconheavy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kenopsia": "kenopsia", "virtualkenopsia": "virtualkenopsia", "snowrunner": "sch<PERSON><PERSON><PERSON><PERSON><PERSON>", "libraryofruina": "bibliothéikvunruina", "l4d2": "l4d2", "thenonarygames": "thenonarygames", "omegastrikers": "omegastrikers", "wayfinder": "weefanner", "kenabridgeofspirits": "k<PERSON><PERSON><PERSON><PERSON><PERSON>ngeeschter", "placidplasticduck": "rouegeplastiksdéckchen", "battlebit": "battlebit", "ultimatechickenhorse": "ultimatechickenhorse", "dialtown": "dialtown", "smileforme": "smilefirmech", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermeatboy", "tinnybunny": "klenghaasje", "cozygrove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doom": "doom", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "paladins", "earthdefenseforce": "äerdverdeedegungstrupp", "huntshowdown": "jee<PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "joinsquad", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "insurgencysandstorm", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "divisioune2", "killzone": "killzone", "helghan": "hel<PERSON>", "coldwarzombies": "coldwarzombies", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "modernwarfare", "neonabyss": "neonofgrond", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "boarderlands", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "primalcarnage", "worldofwarships": "worldofwarships", "back4blood": "back4blood", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "killer", "masseffect": "masseffect", "systemshock": "systemschock", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "cavestory", "doometernal": "doomeewëg", "centuryageofashes": "joerhonnertvomäschen", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "generatiou<PERSON><PERSON>", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "dephantomschmierz", "warface": "warface", "crossfire": "crossfire", "atomicheart": "atomescht", "blackops3": "blackops3", "vampiresurvivors": "vampiriwwerliewer", "callofdutybatleroyale": "callofdutybattleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "fräiheet", "battlegrounds": "battlegrounds", "frag": "frag", "tinytina": "klengtina", "gamepubg": "gamepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "juegosfps", "convertstrike": "convertstrike", "warzone2": "warzone2", "shatterline": "broch<PERSON><PERSON>n", "blackopszombies": "blackopszombies", "bloodymess": "blut<PERSON><PERSON><PERSON><PERSON>", "republiccommando": "republickommando", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "groundbranch", "squad": "squad", "destiny1": "destiny1", "gamingfps": "gamingfps", "redfall": "redfall", "pubggirl": "pubggirl", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "payday2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "seefkabeljau", "ghostcod": "g<PERSON>chterkabeljau", "csplay": "csplay", "unrealtournament": "unrealtournament", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "<PERSON><PERSON><PERSON><PERSON>", "remnant": "iw<PERSON><PERSON><PERSON><PERSON>", "azurelane": "azurelane", "worldofwar": "weltvumkrich", "gunvolt": "gunvolt", "returnal": "returnal", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON><PERSON><PERSON><PERSON>", "quake2": "quake2", "microvolts": "<PERSON><PERSON><PERSON><PERSON>", "reddead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "seaofthieves", "rust": "r<PERSON><PERSON>", "conqueronline": "conqueronline", "dauntless": "<PERSON>rschrocken", "warships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayofdragons": "<PERSON><PERSON><PERSON>ndedra<PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "flightrising", "recroom": "spillkummer", "legendsofruneterra": "legendenofruneterra", "pso2": "pso2", "myster": "myster", "phantasystaronline2": "phantasystaronline2", "maidenless": "ouni_fr<PERSON><PERSON>n", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "worldoftanks", "crossout": "duerchsträichen", "agario": "agario", "secondlife": "secondzweet<PERSON>liewen", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "netplay", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "dreck", "newworld": "neiwelt", "blackdesertonline": "blackdesertonline", "multiplayer": "multiplayer", "pirate101": "pirat101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "aschevunderschaafung", "riotmmo": "riotmmo", "silkroad": "se<PERSON><PERSON><PERSON>", "spiralknights": "spiralknights", "mulegend": "mulegend", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "drakensprofeet", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multijoueur", "angelsonline": "engellenonline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsalrepublik", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "blueprotocol", "perfectworld": "perfectwelt", "riseonline": "risenonline", "corepunk": "corepunk", "adventurequestworlds": "adventurequestworlds", "flyforfun": "fléifirdespaass", "animaljam": "animaljam", "kingdomofloathing": "kingdomofloathing", "cityofheroes": "stadtvunhelden", "mortalkombat": "mortalkombat", "streetfighter": "streetfighter", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "fireier", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "streetsofrage", "mkdeadlyalliance": "mkdeadlyalliance", "nomoreheroes": "<PERSON>eng<PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "thekingofffighters", "likeadragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "retrofightinggames", "blasphemous": "blasphemesch", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "megakrach", "mugen": "mugen", "warofthemonsters": "krichvundemonsteren", "jogosdeluta": "j<PERSON><PERSON><PERSON><PERSON>", "cyberbots": "cyberbot<PERSON>", "armoredwarriors": "rëtterkrieger", "finalfight": "schlosskampf", "poweredgear": "poweredgear", "beatemup": "<PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "killerinstinct", "kingoffigthers": "king<PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "ritterlechkeet2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "silksonggame", "silksongnews": "silksongnews", "silksong": "silksong", "undernight": "ënnertdernuecht", "typelumina": "schrëftlumina", "evolutiontournament": "evolutiounstournoi", "evomoment": "evomoment", "lollipopchainsaw": "lolli<PERSON><PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "bloodborne", "horizon": "horizont", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodbourne", "uncharted": "onentdeckt", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "lastofus", "infamous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstationbuddies": "playstationnkollegen", "ps1": "ps1", "oddworld": "komeschschewelt", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "godof<PERSON>", "gris": "gro", "trove": "trouvaille", "detroitbecomehuman": "detroitgëttmënschlech", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "bismuerges", "touristtrophy": "touristentrophäe", "lspdfr": "lspdfr", "shadowofthecolossus": "schiedvumkoloss", "crashteamracing": "crashteamracing", "fivepd": "fënnefpd", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "spillenopderstation", "samuraiwarriors": "samura<PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "see<PERSON><PERSON>e", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "männerjuegd", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "partyléiw", "warharmmer40k": "warhammer40k", "fightnightchampion": "fightnightchampion", "psychonauts": "psychonauten", "mhw": "mhw", "princeofpersia": "prënzvunpersien", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "battlefront", "dontstarvetogether": "ver<PERSON><PERSON><PERSON><PERSON><PERSON>men", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "stärebonnen", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "houseflipper", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "leagueofkingdoms", "fable2": "fabel2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "trashtv", "skycotl": "himmelfaarweg", "erica": "erica", "ancestory": "vir<PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "monsterprom", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "kultvomlaam", "duckgame": "intenchenspill", "thestanleyparable": "destanleyparable", "towerunite": "towerunite", "occulto": "occulto", "longdrive": "langstreck", "satisfactory": "zefriddestellend", "pluviophile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "underearth": "ënnertdeerierd", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pizzatower": "pizzaturm", "indiegame": "indiegame", "itchio": "itchio", "golfit": "golfit", "truthordare": "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "spill", "rockpaperscissors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trampoline": "trampolin", "hulahoop": "<PERSON><PERSON><PERSON><PERSON>", "dare": "trau_dech", "scavengerhunt": "schnitzeljuegd", "yardgames": "gaardenspiller", "pickanumber": "w<PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "richtegoderfaalsch", "beerpong": "beerpong", "dicegoblin": "wier<PERSON>lk<PERSON>z", "cosygames": "cozygames", "datinggames": "datingspiller", "freegame": "grat<PERSON><PERSON><PERSON>", "drinkinggames": "saufspiller", "sodoku": "sodoku", "juegos": "juegos", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "simulatiounsspiller", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "jeuxdemots", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "kommerspillemer", "boredgames": "langweilegspiller", "oyun": "oyun", "interactivegames": "interaktivspiller", "amtgard": "amtgard", "staringcontests": "starconcouren", "spiele": "spiller", "giochi": "giochi", "geoguessr": "geoguessr", "iphonegames": "iphonespiller", "boogames": "boogames", "cranegame": "kranspill", "hideandseek": "verstoppspillen", "hopscotch": "<PERSON><PERSON><PERSON><PERSON>", "arcadegames": "arcadespiller", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "klassesch_spill", "mindgames": "<PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "guessthe<PERSON><PERSON>", "galagames": "galagames", "romancegame": "romance<PERSON><PERSON>", "yanderegames": "yander<PERSON><PERSON>s", "tonguetwisters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "4xgames": "4xspiller", "gamefi": "gamefi", "jeuxdarcades": "jeuxdarcades", "tabletopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "games90", "idareyou": "echndefiéierdech", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "rennspiller", "ets2": "ets2", "realvsfake": "realgegenfalsch", "playgames": "spillespiller", "gameonline": "gameonline", "onlinegames": "onlinespiller", "jogosonline": "spillenonline", "writtenroleplay": "schrëftlecherollenspill", "playaballgame": "spilleballspill", "pictionary": "pictionary", "coopgames": "coopspiller", "jenga": "jenga", "wiigames": "wii<PERSON><PERSON><PERSON>", "highscore": "héichscore", "jeuxderôles": "rollespiller", "burgergames": "burgerspiller", "kidsgames": "kannerspiller", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "frospill", "gioco": "gioco", "managementgame": "managementspill", "hiddenobjectgame": "verstopptenobjetsspill", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "formel1spill", "citybuilder": "stadbaier", "drdriving": "drdriving", "juegosarcade": "arcadespiller", "memorygames": "gedächtnisspieler", "vulkan": "vulkan", "actiongames": "actionspiller", "blowgames": "blassspiller", "pinballmachines": "pinball<PERSON><PERSON><PERSON>", "oldgames": "alspiller", "couchcoop": "couchcoop", "perguntados": "g<PERSON><PERSON>", "gameo": "gameo", "lasergame": "lasergame", "imessagegames": "imess<PERSON><PERSON><PERSON>", "idlegames": "idlespiller", "fillintheblank": "ausfëllen", "jeuxpc": "jeuxpc", "rétrogaming": "rétrogaming", "logicgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "japangame": "japanspill", "rizzupgame": "rizzupgame", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "jeuxdecelebrite", "exitgames": "escapegames", "5vs5": "5géint5", "rolgame": "rollespill", "dashiegames": "dashiegames", "gameandkill": "gameandkill", "traditionalgames": "traditionellspiller", "kniffel": "kniffel", "gamefps": "gamefps", "textbasedgames": "text<PERSON><PERSON><PERSON><PERSON>piller", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospill", "thiefgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "<PERSON><PERSON>", "tischfußball": "tischfussball", "spieleabende": "spie<PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "casualspiller", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "<PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "déifspillserie", "cranegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "játék": "spill", "bordfodbold": "bordfodbold", "jogosorte": "g<PERSON>cksspill", "mage": "mage", "cargames": "autospiller", "onlineplay": "onlinespillen", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "spillowender", "pursebingos": "täschebingos", "randomizer": "randomizer", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "gamespc", "socialdeductiongames": "sozialdeduktiunsspiller", "dominos": "dominos", "domino": "domino", "isometricgames": "isometrescspiller", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "w<PERSON><PERSON>waatrou<PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "schnitzeljuegden", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "f2pspiller", "free2play": "gratis2spillen", "fantasygame": "<PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "driftspill", "gamesotomes": "gamesotomes", "halotvseriesandgames": "halotvserieangamer", "mushroomoasis": "pilzenoase", "anythingwithanengine": "allesmatengemmotor", "everywheregame": "iwwerallspill", "swordandsorcery": "schwäertazauberei", "goodgamegiving": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "j<PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "gamingspiller", "virgogami": "virgogami", "gogame": "gogame", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "miniaturspiller", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "selflovegaming", "gamemodding": "gamemodding", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "gesellschaftsspiller", "spelletjes": "spillercher", "spacenerf": "spacenerf", "charades": "charades", "singleplayer": "singleplayer", "coopgame": "coopspill", "gamed": "gameniert", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "ha<PERSON>tspill", "kingdiscord": "kingdiscord", "scrabble": "scrabble", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "pandemievermächtnis", "camelup": "ka<PERSON><PERSON>ë<PERSON>", "monopolygame": "monopolyspill", "brettspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "brietspiller", "boardgame": "briet<PERSON><PERSON>", "sällskapspel": "gesellschaftsspiller", "planszowe": "planszowe", "risiko": "risiko", "permainanpapan": "brietspiller", "zombicide": "zombicide", "tabletop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "baduk": "baduk", "bloodbowl": "bloodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "gobrietspill", "connectfour": "viergewennt", "heroquest": "heroquest", "giochidatavolo": "gesellschaftsspiller", "farkle": "farkle", "carrom": "carrom", "tablegames": "gesellschaftsspiller", "dicegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "briet<PERSON><PERSON>", "jocuridesocietate": "gesellschaftsspiller", "deskgames": "deskgames", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "kosmesch_begéignung", "creationludique": "kreativspillerei", "tabletoproleplay": "tabletoprollenspill", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "switchboardgames": "switchboardspiller", "infinitythegame": "infinitythegame", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "leederenanru<PERSON><PERSON>", "társas": "gesellschaftsspill", "juegodemesa": "juegodemesa", "planszówki": "planszów<PERSON>", "rednecklife": "b<PERSON><PERSON><PERSON><PERSON>", "boardom": "langweil", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "gesellschaftsspill", "gameboard": "spillbrett", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "gesellschaftsspiller", "twilightimperium": "twilightimperium", "horseopoly": "päerdsopoly", "deckbuilding": "<PERSON><PERSON><PERSON>", "mansionsofmadness": "villenvum<PERSON><PERSON><PERSON>n", "gomoku": "gomoku", "giochidatavola": "brietspiller", "shadowsofbrimstone": "schietevanbrimstone", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON>", "warcaby": "warcaby", "táblajátékok": "spillbriedervideoen", "battleship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tickettoride": "tickettoride", "deskovehry": "dëschspiller", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "j<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "spillofender", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "jeuxsociete", "gesellschaftsspiele": "gesellschaftsspiller", "starwarslegion": "starwarslegion", "gochess": "<PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "jeuxdes<PERSON>iet<PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "warzone", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identityv", "theisle": "dinsel", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "callofcthulhu", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON><PERSON><PERSON><PERSON>", "eco": "eco", "monkeyisland": "a<PERSON>insel", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "deegvergiess", "fobia": "fobia", "witchit": "hexenes", "pathologic": "pathologesch", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "déilangdäischter", "ark": "ark", "grounded": "<PERSON><PERSON><PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarve": "nethongeren", "eternalreturn": "éiwegretour", "pathoftitans": "pathoftitans", "frictionalgames": "frictionalgames", "hexen": "hexen", "theevilwithin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "realrac", "thebackrooms": "debackrooms", "backrooms": "backrooms", "empiressmp": "empiressmp", "blockstory": "blockgeschicht", "thequarry": "destengréiw", "tlou": "tlou", "dyinglight": "stierftliicht", "thewalkingdeadgame": "thewalkingdeadgame", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "opkomstvunimpeerien", "stateofsurvivalgame": "stateofsurvivalgame", "vintagestory": "vintagestory", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "<PERSON><PERSON><PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "westlendiwwerliewen", "beastsofbermuda": "beastsofber<PERSON>da", "frostpunk": "frostpunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON>", "survivalhorror": "survivalhor<PERSON>r", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "voidtrain", "lifeaftergame": "liewennodemspill", "survivalgames": "iwwerliewensspiller", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "dësekriegvumir", "scpfoundation": "scpf<PERSON><PERSON><PERSON><PERSON><PERSON>", "greenproject": "gréngeprojet", "kuon": "kuon", "cryoffear": "kräischevirunangscht", "raft": "floss", "rdo": "rdo", "greenhell": "g<PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "doud<PERSON>y", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "mammi", "littlenightmares2": "klengnuetsalbtreem2", "signalis": "signaler", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "sonsoftheforest", "rustvideogame": "rustvideospill", "outlasttrials": "outlasttrials", "alienisolation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "undawn", "7day2die": "7<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "i<PERSON><PERSON><PERSON><PERSON>", "propnight": "<PERSON><PERSON><PERSON><PERSON>", "deadisland2": "doudenaninsel2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampir", "deathverse": "deathverse", "cataclysmdarkdays": "katastrophendësterdeeg", "soma": "soma", "fearandhunger": "an<PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkerschietlecharnobyl", "lifeafter": "<PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "zäitalterdukelheet", "clocktower3": "uerentuerm3", "aloneinthedark": "elengamdonkelen", "medievaldynasty": "mëttelalterlechdynastie", "projectnimbusgame": "projectnimbusgame", "eternights": "éiwegnuechten", "craftopia": "bast<PERSON><PERSON><PERSON>", "theoutlasttrials": "theoutlasttrials", "bunker": "bunker", "worlddomination": "weltherrschaft", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "zwergenkiller", "warhammer40kcrush": "warhammer40kcrush", "wh40": "wh40", "warhammer40klove": "warhammer40kléift", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "ech️sororitas", "ilovevindicare": "echleifvindicare", "iloveassasinorum": "echleiwendassasinorum", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "et<PERSON>uchz<PERSON><PERSON>", "wingspan": "fligelspaanweit", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "heldenavomächtastäerkt", "btd6": "btd6", "supremecommander": "supremecommander", "ageofmythology": "ageofmythology", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "verbannt", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "kommandéierenaneroberen", "warcraft3": "warcraft3", "eternalwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strategygames": "strategiespiller", "anno2070": "anno2070", "civilizationgame": "zivilisatiounsspill", "civilization4": "zivilisatioun4", "factorio": "factorio", "dungeondraft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spore": "spore", "totalwar": "<PERSON><PERSON><PERSON>", "travian": "travian", "forts": "festungen", "goodcompany": "guddgesellschaft", "civ": "civ", "homeworld": "heemechtswelt", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "méischnëllw<PERSON><PERSON>icht", "forthekings": "firdkinneken", "realtimestrategy": "echtzäitstrategie", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "kinnekräichzweekrounen", "eu4": "eu4", "vainglory": "<PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "gottheet", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davesluschtegenalgebraklass", "plagueinc": "plagueinc", "theorycraft": "theorycraft", "mesbg": "mesbg", "civilization3": "civilization3", "4inarow": "4<PERSON><PERSON>", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "avancéiertkrichsspiller", "ageofempires2": "ageofempires2", "disciples2": "jünger2", "plantsvszombies": "plantzengegentzombien", "giochidistrategia": "giochidistrategia", "stratejioyunları": "strategiespiller", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "dinosaurierkinneg", "worldconquest": "welteroberen", "heartsofiron4": "heartsofiron4", "companyofheroes": "companyofheroes", "battleforwesnoth": "battleforwesnoth", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "goosegooseduck", "phobies": "phobien", "phobiesgame": "phobiespill", "gamingclashroyale": "gamingroyaleclash", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "turnbased", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "kräizritter", "cultris2": "cultris2", "spellcraft": "zauberhandwierk", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estrategie", "popfulmail": "popvollenmail", "shiningforce": "shiningforce", "masterduel": "masterduel", "dysonsphereprogram": "dysonsphèreprogramm", "transporttycoon": "transportmogul", "unrailed": "ausdemgleis", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planesescapetorment", "uplandkingdoms": "iewegräicher", "galaxylife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespire", "battlecats": "kampkaazen", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "needforspeed", "needforspeedcarbon": "needforspeedcarbon", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "i<PERSON><PERSON><PERSON><PERSON>", "deadbydaylight": "doutbismoies", "alicemadnessreturns": "alicemad<PERSON>ret<PERSON>s", "darkhorseanthology": "darkhorseanthologie", "phasmophobia": "phasmo<PERSON><PERSON><PERSON>", "fivenightsatfreddys": "fënnefnuechtenbeimfreddy", "saiko": "saiko", "fatalframe": "fatalframe", "littlenightmares": "klengnuetselmergen", "deadrising": "deadrising", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON>", "deadisland": "doudegeinsel", "litlemissfortune": "klengmisspech", "projectzero": "projectnull", "horory": "horror", "jogosterror": "laafsterror", "helloneighbor": "moien<PERSON><PERSON>", "helloneighbor2": "moiennoper2", "gamingdbd": "gamingdbd", "thecatlady": "dekazzefra", "jeuxhorreur": "horrorspiller", "horrorgaming": "horrorgaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kaartengeintdemënsch", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinoch<PERSON><PERSON><PERSON>", "codenames": "<PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "<PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "legendofrun<PERSON>a", "solitaire": "solitaire", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "spillkaarten", "marvelsnap": "marvelsnap", "ginrummy": "rom<PERSON>", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "tradingkaarten", "pokemoncards": "pokémonkaarten", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "sportskaarten", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warcry": "<PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "dewidderstand", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "yugiohkaarten", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yug<PERSON>hspill", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohgoat": "yugiohgoat", "briscas": "briscas", "juegocartas": "kaartenspill", "burraco": "bur<PERSON>o", "rummy": "rummy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "kaartespiller", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "<PERSON><PERSON><PERSON><PERSON>", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "battlespirits", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "joker", "facecard": "facecard", "cardfight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON>", "marvelchampions": "marvelchampions", "magiccartas": "magieschekaarten", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberse": "cyberse", "classicarcadegames": "klassesch_arcadespiller", "osu": "osu", "gitadora": "gitadora", "dancegames": "danzspiller", "fridaynightfunkin": "fridaynightfunkin", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "guitarhero", "clonehero": "clonehero", "justdance": "justdanz", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockthedout", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "danzzentral", "rhythmgamer": "rhythmgamer", "stepmania": "stepmania", "highscorerythmgames": "highscorerythmgames", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "<PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "endanzvumfeieranäis", "auditiononline": "auditiononline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cubing": "cubing", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "<PERSON>piller", "spotit": "fannet", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "logeschrätselen", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "rätselspiller", "rubikscube": "rubikswierfel", "crossword": "kräizwuerträtsel", "motscroisés": "motscroisés", "krzyżówki": "kräizwierträtsel", "nonogram": "nonogramm", "bookworm": "bicherwuerm", "jigsawpuzzles": "jigsawpuzzelen", "indovinello": "r<PERSON><PERSON>", "riddle": "r<PERSON><PERSON>", "riddles": "rätselen", "rompecabezas": "puzzel", "tekateki": "tekateki", "inside": "bannen", "angrybirds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapesimulator": "escapesimulator", "minesweeper": "minenraumer", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "kräizwuerträtsel", "kurushi": "k<PERSON>hi", "gardenscapesgame": "gardenscapesspill", "puzzlesport": "puzzlesport", "escaperoomgames": "escaperoomspiller", "escapegame": "escapegame", "3dpuzzle": "3dpuzzle", "homescapesgame": "homescapesspill", "wordsearch": "<PERSON><PERSON><PERSON><PERSON>", "enigmistica": "enigmistica", "kulaworld": "kulaworld", "myst": "myst", "riddletales": "rätselgeschichten", "fishdom": "fishdom", "theimpossiblequiz": "donëmméiglechequiz", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "match3puzzle", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "rubikenswierfel", "cuborubik": "rubikswierfel", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "heemlandschaften", "puttputt": "minigolf", "qbert": "qbert", "riddleme": "rotselmech", "tycoongames": "tycoongames", "cubosderubik": "cubosderubik", "cruciverba": "cruciver<PERSON>", "ciphers": "chifferen", "rätselwörter": "rätselwierder", "buscaminas": "minesweeper", "puzzlesolving": "rätselléisen", "turnipboy": "turnip<PERSON>", "adivinanzashot": "rotespillchen", "nobodies": "k<PERSON><PERSON><PERSON>", "guessing": "roden", "nonograms": "nonogrammen", "kostkirubika": "kostkirubika", "crypticcrosswords": "kryptesch_kräizwuerträtsel", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON><PERSON>", "puzzlehunts": "rätselchallengen", "catcrime": "kazecrimen", "quebracabeça": "quebracabeça", "hlavolamy": "knaschterten", "poptropica": "poptropica", "thelastcampfire": "deleschtelagerfaier", "autodefinidos": "selbstde<PERSON><PERSON><PERSON><PERSON>", "picopark": "picopark", "wandersong": "wanderlidd", "carto": "carto", "untitledgoosegame": "untitledgoosegame", "cassetête": "casset<PERSON>te", "limbo": "limbo", "rubiks": "rubiks", "maze": "labyrinth", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "s<PERSON><PERSON>", "portalgame": "portalgame", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubikswierfel", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "zauberwierfel", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "twistedwonderland", "monopoly": "monopoly", "futurefight": "zukunftskampf", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemystars", "stateofsurvival": "stateofsurvival", "mycity": "mäistad", "arknights": "arknights", "colorfulstage": "faarwegebühn", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "r<PERSON><PERSON><PERSON><PERSON>", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "futt<PERSON><PERSON><PERSON><PERSON>t", "a3": "a3", "phonegames": "handyspiller", "kingschoice": "kënnegschoix", "guardiantales": "guardiantales", "petrolhead": "benzinjun<PERSON>", "tacticool": "tacticool", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "ausderschleef", "craftsman": "handwier<PERSON>", "supersus": "megasus", "slowdrive": "luesfueren", "headsup": "opgepaakt", "wordfeud": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bedwars": "bedwars", "freefire": "freefire", "mobilegaming": "mobilgaming", "lilysgarden": "lilysgarden", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "noutzentral", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "schëddelenarëmfëmmelen", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "zäitprinzessin", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "täscheléift", "androidgames": "androidspiller", "criminalcase": "kriminallfall", "summonerswar": "summonerswar", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "engels<PERSON>gue", "lordsmobile": "lordsmobile", "tinybirdgarden": "klengvullsgaart", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "mängsengendemonsteren", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "warrobots", "mirrorverse": "spigeleuniversum", "pou": "pou", "warwings": "krichsflilleken", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "fuzäit", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "<PERSON><PERSON><PERSON><PERSON>", "slugitout": "sechduellrappen", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "hausdéierenfrënn", "gameofsultans": "gameofsultans", "arenabreakout": "arenaausbroch", "wolfy": "wolfy", "runcitygame": "stadspilllafen", "juegodemovil": "handyspill", "avakinlife": "a<PERSON><PERSON><PERSON><PERSON>", "kogama": "kogama", "mimicry": "<PERSON><PERSON><PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "bombmichbrasilien", "ldoe": "ldoe", "legendonline": "legendonline", "otomegame": "otomes<PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "callofdragonslux", "shiningnikki": "<PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "weeopsnirgendwou", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "wierdermatfrënn2", "soulknight": "soulknight", "purrfecttale": "purrfekttale", "showbyrock": "showbyrock", "ladypopular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "lolmobil", "harvesttown": "harvesttown", "perfectworldmobile": "perfectworldmobile", "empiresandpuzzles": "räicherarätsel", "empirespuzzles": "empiresspuzzles", "dragoncity": "drachestaat", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobilelu", "fanny": "popp", "littlenightmare": "klengtnuetsdram", "aethergazer": "aethergazer", "mudrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "ëmmerseel", "gunbound": "gunbound", "gamingmlbb": "gamingmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiegestrand", "eveechoes": "eveechoes", "jogocelular": "handyspill", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "streetfighterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "gamingbgmi", "girlsfrontline": "meederchesfrontlinn", "jurassicworldalive": "jurassicworldalive", "soulseeker": "<PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "driwwer<PERSON>mme<PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "moonchaistory", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "jogosmobile", "legendofneverland": "legendvunneverland", "pubglite": "pubglite", "gamemobilelegends": "gamemobilelegends", "timeraiders": "timeraiders", "gamingmobile": "gamingmobil", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd": "dnd", "quest": "quest", "giochidiruolo": "spillroller", "dnd5e": "dnd5e", "rpgdemesa": "rollespiller", "worldofdarkness": "wëltvundüüsternis", "travellerttrpg": "travellerttrpg", "2300ad": "2300nc", "larp": "larp", "romanceclub": "romanceclub", "d20": "d20", "pokemongames": "p<PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemon<PERSON>", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "schwätzt", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "pocketmonsters", "nuzlocke": "nuzlocke", "pokemonplush": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "teamystic": "teamystic", "pokeball": "pokéball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "glänzendpokemon", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "eisenhänn", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmee<PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "gl<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "schach", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "schach", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "weltblitz", "jeudéchecs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "japanesechess": "japa<PERSON><PERSON><PERSON><PERSON>", "chinesechess": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "chesscanada": "s<PERSON><PERSON><PERSON><PERSON>", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "ufänk", "rook": "rook", "chesscom": "schachcom", "calabozosydragones": "<PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "dungeonmeeschter", "tiamat": "tiamat", "donjonsetdragons": "donjonsetdragons", "oxventure": "oxventure", "darksun": "<PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "dlegendvumvoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "däischtermoor", "minecraftchampionship": "minecraftmeeschterschaft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "mäitest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "minecraftmods", "mcc": "mcc", "candleflame": "käerzflam", "fru": "fru", "addons": "addons", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "gemoddedminecraft", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "minecraftstad", "pcgamer": "pcgamer", "jeuxvideo": "jeuxvideo", "gambit": "gambit", "gamers": "gamers", "levelup": "levelup", "gamermobile": "gamermobil", "gameover": "spillaus", "gg": "gg", "pcgaming": "pcgaming", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "pcspiller", "casualgaming": "casualgaming", "gamingsetup": "gamingsetup", "pcmasterrace": "pcmasterrace", "pcgame": "pcspill", "gamerboy": "<PERSON><PERSON><PERSON>", "vrgaming": "vrgaming", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "gameplays", "consoleplayer": "consolespiller", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgamers", "onlinegaming": "onlinegaming", "semigamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamergirls": "gamergirls", "gamermoms": "gamermammen", "gamerguy": "gamerboi", "gamewatcher": "gamewatcher", "gameur": "gamer", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerschicas", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "team<PERSON><PERSON>", "mallugaming": "mallugaming", "pawgers": "pawgers", "quests": "quests", "alax": "alax", "avgn": "avgn", "oldgamer": "alespiller", "cozygaming": "gemittlechgaming", "gamelpay": "gamelpay", "juegosdepc": "pcspiller", "dsswitch": "dswissel", "competitivegaming": "competitivegaming", "minecraftnewjersey": "minecraftnewjersey", "faker": "faker", "pc4gamers": "pc4gamers", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "heterosexuellgaming", "gamepc": "gamepc", "girlsgamer": "gamergirls", "fnfmods": "fnfmods", "dailyquest": "alldaaglech_quest", "gamegirl": "game<PERSON>d<PERSON>", "chicasgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamesetup": "gamesetup", "overpowered": "iwwermächteg", "socialgamer": "socialgamer", "gamejam": "gamejam", "proplayer": "proplayer", "roleplayer": "rollespiller", "myteam": "mäinteam", "republicofgamers": "republikvungamer", "aorus": "aorus", "cougargaming": "cougarspillen", "triplelegend": "dreifachlegend", "gamerbuddies": "gamerfrënn", "butuhcewekgamers": "brauch<PERSON><PERSON><PERSON><PERSON>", "christiangamer": "christlechegamer", "gamernerd": "<PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "wbg", "andregamer": "andregamer", "casualgamer": "casualgamer", "89squad": "89squad", "inicaramainnyagimana": "wéiwaardetäerdann", "insec": "<PERSON><PERSON><PERSON><PERSON>", "gemers": "gemers", "oyunizlemek": "oyunizlemek", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "videospiller", "wspólnegranie": "zesummespillen", "mortdog": "mortdog", "playstationgamer": "playstationgamer", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gesondegamer", "gtracing": "gtracing", "notebookgamer": "notebookgamer", "protogen": "protogen", "womangamer": "gamerin", "obviouslyimagamer": "klorimagamer", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "sammler", "humanfallflat": "humanfallflat", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "zeroescape", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomusik", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "fallguys", "switch": "switch", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "skychildrenofthelight", "tomodachilife": "tomodachilife", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "spazéiergangsimulatoren", "nintendogames": "nintendospiller", "thelegendofzelda": "thelegendofzelda", "dragonquest": "dragonquest", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "mäifrendpedro", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51spiller", "earthbound": "äerdgebonnen", "tales": "geschichten", "raymanlegends": "raymanlegends", "luigismansion": "lui<PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "drëiecksstrategie", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulekrieger", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioandsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "dzelda", "palia": "palia", "marioandluigi": "ma<PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "redcanids", "vanillalol": "vanillalol", "wildriftph": "wildriftph", "lolph": "lmao", "leagueoflegend": "leagueoflegends", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendslëtzebuerg", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligavundeelegenden", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexporteën", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "gamingfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideospiller", "scaryvideogames": "grujelegvideospiller", "videogamemaker": "videospillsentwéckler", "megamanzero": "megamanzero", "videogame": "videospill", "videosgame": "videospiller", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "arcades", "acnh": "acnh", "puffpals": "puffku<PERSON><PERSON>n", "farmingsimulator": "baueresimulator", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxlëtzebuerg", "robloxdeutsch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "sanboxspiller", "videogamelore": "videospillgeschichten", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "dreamlandschaft", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "doud<PERSON><PERSON>", "amordoce": "amordoce", "videogiochi": "videospiller", "theoldrepublic": "déalalrepublik", "videospiele": "videospiller", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "adventurespiller", "wolfenstein": "wolfenstein", "actionadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storyofseasons": "geschichtevundesaisonen", "retrogames": "retrogames", "retroarcade": "retroarcade", "vintagecomputing": "vintagecomputing", "retrogaming": "retrogaming", "vintagegaming": "vintagegaming", "playdate": "spilldatum", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "ongerechtegkeet2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "himmelsspill", "zenlife": "zenlife", "beatmaniaiidx": "beatmaniaiidx", "steep": "steil", "mystgames": "mystgames", "blockchaingaming": "blockchaingaming", "medievil": "medievol", "consolegaming": "konsollgaming", "konsolen": "konsolen", "outrun": "fortlafen", "bloomingpanic": "bloomingpanik", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "gaminghorror", "monstergirlquest": "monstergirlquest", "supergiant": "supergéant", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "bauereispiller", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "jackboxspiller", "interactivefiction": "interaktivgeschicht", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "léiftschäftlechléif", "visualnovel": "visualnovel", "visualnovels": "visualromaner", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON>", "tcrghost": "tcrghost", "payday": "payday", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "dämmerprinzessin", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "<PERSON><PERSON><PERSON>", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "visuellnovel", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "retrogame", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "goddesch", "leafblowerrevolution": "leafbliederrevolutioun", "wiiu": "wiiu", "leveldesign": "leveldesign", "starrail": "starrail", "keyblade": "keyblade", "aplaguetale": "engpestgeschicht", "fnafsometimes": "fnafheiansdo", "novelasvisuales": "visualromaner", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "gameretro", "videojuejos": "videospiller", "videogamedates": "videospilldates", "mycandylove": "mäisëssléift", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "hulks<PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "réckkunftvunderrechnong", "gamstergaming": "gamstergaming", "dayofthetantacle": "dagvum<PERSON><PERSON>l", "maniacmansion": "maniacvilla", "crashracing": "crashracing", "3dplatformers": "3dplattformspiller", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "oldschoolgaming", "hellblade": "hellblade", "storygames": "storyturespiller", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "iwwerzweiseelen", "gameuse": "gameuse", "offmortisghost": "offmortisghost", "tinybunny": "klengeskannéngchen", "retroarch": "retroarch", "powerup": "powerup", "katanazero": "katana<PERSON>ll", "famicom": "famicom", "aventurasgraficas": "aventurasgraficas", "quickflash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "retroarcaden", "f123": "f123", "wasteland": "wasteland", "powerwashsim": "powerwashsim", "coralisland": "k<PERSON><PERSON><PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "aneerwelt", "metaquest": "metaquest", "animewarrios2": "animekrieger2", "footballfusion": "foussballfusion", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "verdréintemetall", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simulator", "symulatory": "symulatory", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "frendchendungeon", "toontownrewritten": "too<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "simracing", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "stadchaos", "heavenlybodies": "himmelskierper", "seum": "seum", "partyvideogames": "partyvideospiller", "graveyardkeeper": "kierfechswiechter", "spaceflightsimulator": "raumfluchsimulator", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "iessenagaming", "oyunvideoları": "oyunvideoları", "thewolfamongus": "dewollefënnereischt", "truckingsimulator": "camionsimulator", "horizonworlds": "horizonwelten", "handygame": "spill<PERSON><PERSON>y", "leyendasyvideojuegos": "leyendasyvideojuegos", "oldschoolvideogames": "oldschoolvideospiller", "racingsimulator": "rennsi<PERSON><PERSON><PERSON>", "beemov": "bee<PERSON>v", "agentsofmayhem": "<PERSON><PERSON><PERSON><PERSON>", "songpop": "songpop", "famitsu": "famitsu", "gatesofolympus": "portevumolympus", "monsterhunternow": "monsterhunternow", "rebelstar": "<PERSON><PERSON><PERSON>ä<PERSON>", "indievideogaming": "indievideospiller", "indiegaming": "indiegaming", "indievideogames": "indievideospiller", "indievideogame": "indievideospill", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "buffbuerg", "unbeatable": "onschlobar", "projectl": "projectl", "futureclubgames": "zukunftsclubspiller", "mugman": "mugman", "insomniacgames": "schloflossergames", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "rescht", "gamebacklog": "spillsammlung", "gamingbacklog": "gamingstack", "personnagejeuxvidéos": "videospillsfiguren", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "cityskylines", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "frechen<PERSON>d", "beastlord": "beastlord", "juegosretro": "retrogamen", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "reservoirvundopamin", "staxel": "staxel", "videogameost": "videospillmusek", "dragonsync": "drac<PERSON><PERSON>", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "echleiwekofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkerthanblack": "méischwaarzwéinuecht", "animescaling": "animescaling", "animewithplot": "animematplot", "pesci": "pesci", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "80eranime", "90sanime": "90eranime", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "2000eranime", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstoneseason1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "animecover", "thevisionofescaflowne": "thevisionofescaflowne", "slayers": "slayers", "tokyomajin": "tokyomajin", "anime90s": "anime90er", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "feierkraaft", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "zukunftsdagbuch", "fairytail": "mier<PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "parasit", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "mermaidmelody", "kamisamakiss": "kamisamakoss", "blmanga": "blmanga", "horrormanga": "horrormanga", "romancemangas": "<PERSON><PERSON><PERSON><PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blacklagoon": "blacklagoon", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "genieinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "engecertainmagicalindex", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "sportsanime", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "sagavuntanyaderböser", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "mazinger", "blackbuttler": "blackbuttler", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "wéihältenamummy", "fullmoonwosagashite": "vollmoundwousichsichen", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "lëschtegsengräisseg", "martialpeak": "kampfkunstspëtzt", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "hiscoregirl", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "<PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "alanimenfilmer", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "schwaarzek<PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horror<PERSON>me", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "echsinnenfremden", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "déiversprachenieländ", "monstermanga": "monstermanga", "yourlieinapril": "dengléigenamabrell", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "déifséiprisonéier", "jojolion": "jojo<PERSON>", "deadmanwonderland": "doudmannwonnerland", "bannafish": "<PERSON><PERSON><PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "husbu": "hus<PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "foodwars", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "deiwe<PERSON>länn", "toyoureternity": "andechwegkeet", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "blueperiod", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "geheimallianz", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON><PERSON><PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "go<PERSON><PERSON><PERSON><PERSON><PERSON>", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampirritter", "mugi": "mugi", "blueexorcist": "blauenexorzist", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "gescried", "spyfamily": "spyfamily", "airgear": "airgear", "magicalgirl": "z<PERSON>ber<PERSON>dchen", "thesevendeadlysins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prisonschool": "prisongsschoul", "thegodofhighschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "mydressupdarling": "mengverkleedungspëppchen", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeunivers", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoofgekierzt", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpsycho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON>", "romancemanga": "<PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromanik", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animelëtzebuerg", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayerbisopdschwäert", "bloodlad": "bloodlad", "goodbyeeri": "adieu<PERSON>", "firepunch": "feier<PERSON>ust", "adioseri": "adiosserie", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "stärenalignéieren", "romanceanime": "romance<PERSON>me", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "kiischtenzauber", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "recordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "highschoolofthedead", "germantechno": "däitschtechno", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prënzvumtennis", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "assassinclassroom", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "doudesparad", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "japaneschanime", "animespace": "animespace", "girlsundpanzer": "<PERSON><PERSON><PERSON><PERSON>", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratman", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "peachgirl", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "<PERSON><PERSON><PERSON><PERSON>", "deliciousindungeon": "leckerimdungeon", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "recordofragnarok", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "spréngandsneaker", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "overgeared": "iwwerausgerëscht", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "hexenatelier", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "megasunneleeuw", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animemeedercher", "reverseharem": "reverseharem", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "great<PERSON><PERSON><PERSON>", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gear5": "gear5", "grandbluedreaming": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "bluttkanime", "bloodc": "bluttsiichteg", "talesofdemonsandgods": "geschichtenvundäimonenagëtter", "goreanime": "goreanime", "animegirls": "animemeedercher", "sharingan": "<PERSON><PERSON>", "crowsxworst": "kréienxschlëmmst", "splatteranime": "splatteranime", "splatter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "risingoftheshieldhero": "risingoftheshieldhero", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "schleimdeenegeholl", "animeyuri": "animeyuri", "animeespaña": "animeespagne", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "kannervundewalën", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "superchampiounen", "animeidols": "animeidolen", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "g<PERSON>ngendeeg", "magicalgirls": "<PERSON><PERSON>_me<PERSON><PERSON>er", "callofthenight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "s<PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "finderman<PERSON>", "princessjellyfish": "<PERSON>rin<PERSON><PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "paradisekëss", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeverse", "persocoms": "persocoms", "omniscientreadersview": "allwëssendlesersiicht", "animecat": "animekaz", "animerecommendations": "animerecommandatiounen", "openinganime": "<PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "mäijugendromcomedy", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "risenroboteren", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mech", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "javel", "deathnote": "doudesnotizbuch", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "milit<PERSON><PERSON><PERSON>", "greenranger": "<PERSON><PERSON><PERSON><PERSON>", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animecity": "animecity", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonslayer", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "umfrocorps", "onepieceanime": "onepieceanime", "attaquedestitans": "attackvundentitanen", "theonepieceisreal": "theonepieceisreal", "revengers": "revengers", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "joyboyeffekt", "digimonstory": "digimongeschicht", "digimontamers": "digimontamers", "superjail": "superprisong", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "feelerfräiwebtoon", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "<PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "allhellegenstrooss", "recuentosdelavida": "recuentosdelavida"}