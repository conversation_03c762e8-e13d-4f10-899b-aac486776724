{"2048": "2048", "mbti": "mbti", "enneagram": "енеаграм", "astrology": "астрологија", "cognitivefunctions": "когнитивнифункции", "psychology": "психологија", "philosophy": "филозофија", "history": "историја", "physics": "физика", "science": "наука", "culture": "култура", "languages": "јазици", "technology": "технологија", "memes": "мимови", "mbtimemes": "mbtimemes", "astrologymemes": "астрологијамимови", "enneagrammemes": "енеаграммемиња", "showerthoughts": "размислувањаподтуш", "funny": "смешно", "videos": "видеа", "gadgets": "гаџети", "politics": "политика", "relationshipadvice": "советизаврски", "lifeadvice": "совет_за_живот", "crypto": "крипто", "news": "вести", "worldnews": "светскивести", "archaeology": "археологија", "learning": "учење", "debates": "дебати", "conspiracytheories": "теоријинаконспирација", "universe": "вселена", "meditation": "медитација", "mythology": "митологија", "art": "уметност", "crafts": "ракотворби", "dance": "танц", "design": "диз<PERSON><PERSON><PERSON>", "makeup": "шминка", "beauty": "убавина", "fashion": "мода", "singing": "пеење", "writing": "пишување", "photography": "фотографија", "cosplay": "косплеј", "painting": "сликање", "drawing": "цртање", "books": "книги", "movies": "филмови", "poetry": "поезија", "television": "телевизија", "filmmaking": "филмување", "animation": "анимација", "anime": "аниме", "scifi": "научнафантастика", "fantasy": "фантазија", "documentaries": "документарци", "mystery": "мистерија", "comedy": "комедија", "crime": "крим<PERSON><PERSON><PERSON>", "drama": "драма", "bollywood": "боливуд", "kdrama": "кдрама", "horror": "хорор", "romance": "романса", "realitytv": "реалити<PERSON><PERSON>у", "action": "акција", "music": "музика", "blues": "блуз", "classical": "класика", "country": "земја", "desi": "деси", "edm": "edm", "electronic": "електронска", "folk": "фолк", "funk": "фанк", "hiphop": "хипхоп", "house": "куќа", "indie": "инди", "jazz": "џез", "kpop": "кпоп", "latin": "латински", "metal": "метал", "pop": "поп", "punk": "панк", "rnb": "рнб", "rap": "рап", "reggae": "реге", "rock": "рок", "techno": "техно", "travel": "патување", "concerts": "концерти", "festivals": "фестивали", "museums": "музеи", "standup": "стендап", "theater": "театар", "outdoors": "природа", "gardening": "градинарство", "partying": "журкање", "gaming": "гејминг", "boardgames": "друштвениигри", "dungeonsanddragons": "дандијимакедонија", "chess": "шах", "fortnite": "fortnite", "leagueoflegends": "лиганалегендите", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "покемон", "food": "храна", "baking": "печење", "cooking": "готвење", "vegetarian": "вегетарија<PERSON><PERSON>ц", "vegan": "веган", "birds": "птици", "cats": "мачки", "dogs": "куче", "fish": "риба", "animals": "животни", "blacklivesmatter": "црнитеживотисеважни", "environmentalism": "еколошкисвесност", "feminism": "феминизам", "humanrights": "човековиправа", "lgbtqally": "лгбтксојузник", "stopasianhate": "стопазискаомраза", "transally": "транссојузник", "volunteering": "волонтирање", "sports": "спорт", "badminton": "бадминтон", "baseball": "бејзбол", "basketball": "кошарка", "boxing": "бокс", "cricket": "крикет", "cycling": "велосипедизам", "fitness": "фитнес", "football": "фудбал", "golf": "голф", "gym": "теретана", "gymnastics": "гимнастика", "hockey": "х<PERSON><PERSON><PERSON>ј", "martialarts": "боречкивештини", "netball": "нетбол", "pilates": "пилатес", "pingpong": "пингпонг", "running": "трчање", "skateboarding": "скејтбординг", "skiing": "скијање", "snowboarding": "сноубордување", "surfing": "сурфање", "swimming": "пливање", "tennis": "тенис", "volleyball": "волејбол", "weightlifting": "кревањетегови", "yoga": "јога", "scubadiving": "нуркање", "hiking": "планинарење", "capricorn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aquarius": "водолија", "pisces": "риби", "aries": "овен", "taurus": "бик", "gemini": "близнаци", "cancer": "рак", "leo": "лав", "virgo": "девица", "libra": "весна", "scorpio": "скорпија", "sagittarius": "стрелец", "shortterm": "краткорочно", "casual": "лежерно", "longtermrelationship": "долгавезаљубов", "single": "сингл", "polyamory": "полиаморија", "enm": "енм", "lgbt": "лгбт", "lgbtq": "лг<PERSON><PERSON>к", "gay": "геј", "lesbian": "лезбејка", "bisexual": "бисек<PERSON>у<PERSON><PERSON><PERSON>ц", "pansexual": "пансексуалец", "asexual": "асексуален", "reddeadredemption2": "реддедредемпшн2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "светиредови", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "кучињачувари", "dislyte": "дис<PERSON><PERSON><PERSON><PERSON>", "rougelikes": "rougelikes", "kingsquest": "кралскапотрага", "soulreaver": "душ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "суикоден", "subverse": "субверз", "legendofspyro": "легендазаспајро", "rouguelikes": "rouguelikes", "syberia": "сибир", "rdr2": "rdr2", "spyrothedragon": "спајротедрагон", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "залезнадметал", "arkham": "arkham", "deusex": "деусекс", "fireemblemfates": "fireemblemfates", "yokaiwatch": "јокаивоч", "rocksteady": "цврстокакокамен", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "гилдворс", "openworld": "отворенсвет", "heroesofthestorm": "heroesofthestorm", "cytus": "cytus", "soulslike": "душосличнa", "dungeoncrawling": "истражувањеподземја", "jetsetradio": "jetsetradio", "tribesofmidgard": "племињанамидгард", "planescape": "авионскипејзаж", "lordsoftherealm2": "господаринакралството2", "baldursgate": "балдур<PERSON>гејт", "colorvore": "боилубител", "medabots": "медаботи", "lodsoftherealm2": "лордовинацарството2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "имерзивнисимулации", "okage": "окаге", "juegoderol": "играулоги", "witcher": "witcher", "dishonored": "осрамотен", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "радиоактивенпад", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "модирање", "charactercreation": "создавањеликови", "immersive": "имерзивно", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "финалфантазистарашкола", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "финалфантази", "finalfantasy14": "финалфентази14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasyматоја", "lalafell": "лала<PERSON>ел", "dissidia": "дисидија", "finalfantasy7": "фајналфентази7", "ff7": "ff7", "morbidmotivation": "морбиднамотивација", "finalfantasyvii": "финалфентази7", "ff8": "фф8", "otome": "отоме", "suckerforlove": "лудпољубов", "otomegames": "отомеигри", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "окаринаодвреме", "yiikrpg": "yiikrpg", "vampirethemasquerade": "вампиримаскерадата", "dimension20": "dimension20", "gaslands": "гасленд", "pathfinder": "патоказ", "pathfinder2ndedition": "pathfinder2едиција", "shadowrun": "shadowrun", "bloodontheclocktower": "крвначасовникот", "finalfantasy15": "финалфантази15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "љубовникки", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "гравитацискиудар", "rpg": "рпг", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "еденшут", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "газда", "yourturntodie": "твојредедаумреш", "persona3": "persona3", "rpghorror": "рпгхорор", "elderscrollsonline": "elderscrollsonline", "reka": "река", "honkai": "хонкаи", "marauders": "мародери", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "епикседум", "rpgtext": "rpgтекст", "genshin": "genshin", "eso": "есо", "diablo2": "дијабло2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "му", "falloutshelter": "бу<PERSON><PERSON><PERSON><PERSON>", "gurps": "гурпс", "darkestdungeon": "најтемнотоподземје", "eclipsephase": "фазанаеклипса", "disgaea": "disgaea", "outerworlds": "надворешнисветови", "arpg": "арпг", "crpg": "црпг", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "династискивоини", "skullgirls": "skullgirls", "nightcity": "ноќенград", "hogwartslegacy": "хогвортснаследство", "madnesscombat": "лудилозаборба", "jaggedalliance2": "џагедалајанс2", "neverwinter": "никогашзима", "road96": "пат96", "vtmb": "vtmb", "chimeraland": "<PERSON>им<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>д", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "витезитенаготам", "forgottenrealms": "забораденисветови", "dragonlance": "dragonlance", "arenaofvalor": "арена_на_доблест", "ffxv": "ffxv", "ornarpg": "орнарпг", "toontown": "цртанград", "childoflight": "детенасветлина", "aq3d": "aq3d", "mogeko": "могеко", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonсвет", "monsterrancher": "одгледувачнамонстери", "ecopunk": "екопанк", "vermintide2": "верминтајд2", "xeno": "ксено", "vulcanverse": "вулканскавселена", "fracturedthrones": "скршенипрестоли", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "сенкапанк", "finalfantasyxv": "фајналфентезиxv", "everoasis": "секојаоаза", "hogwartmystery": "хогвартмистерија", "deltagreen": "делтазелена", "diablo": "диј<PERSON><PERSON><PERSON>о", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "удри", "lastepoch": "последнаепоха", "starfinder": "пронаоѓачнаѕвезди", "goldensun": "златносонце", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "сечилавотемнина", "twilight2000": "самрак2000", "sandevistan": "сандевистан", "cyberpunk": "сајберпанк", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "сајберпанкцрвено", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "паднатпоредок", "finalfantasyxii": "фајналфентазиxii", "evillands": "злиземји", "genshinimact": "genshinimpact", "aethyr": "етер", "devilsurvivor": "ѓаволскипреживувач", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "финалфантази10", "anime5e": "anime5e", "divinity": "божественост", "pf2": "pf2", "farmrpg": "фармрпг", "oldworldblues": "тагазастариотсвет", "adventurequest": "авантурапотрага", "dagorhir": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayingames": "игринаулоги", "roleplayinggames": "игрисоулоги", "finalfantasy9": "finalfantasy9", "sunhaven": "сончевопристаниште", "talesofsymphonia": "приказнизасимфонија", "honkaistarrail": "honkaistarrail", "wolong": "волонг", "finalfantasy13": "финалфантази13", "daggerfall": "дагерфол", "torncity": "torncity", "myfarog": "мојфарог", "sacredunderworld": "светподземје", "chainedechoes": "врзаниодгласи", "darksoul": "мрачнадуша", "soulslikes": "ду<PERSON><PERSON><PERSON><PERSON>и<PERSON>с", "othercide": "другастрана", "mountandblade": "mountandblade", "inazumaeleven": "инаџумаилевен", "acvalhalla": "acvalhalla", "chronotrigger": "хронотригер", "pillarsofeternity": "столбовинавечноста", "palladiumrpg": "палладиу<PERSON>рпг", "rifts": "рифтови", "tibia": "тибија", "thedivision": "дивизијата", "hellocharlotte": "здраво<PERSON>а<PERSON>лот", "legendofdragoon": "легендатанаdragoon", "xenobladechronicles2": "ксеноблејдхроники2", "vampirolamascarada": "вампирскатамаскарада", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "вервулфапокалипсата", "aveyond": "aveyond", "littlewood": "малошума", "childrenofmorta": "децатанаморта", "engineheart": "моторсрце", "fable3": "бајка3", "fablethelostchapter": "фејблизгубенотопоглавје", "hiveswap": "заменароеви", "rollenspiel": "ролплеј", "harpg": "хрпг", "baldursgates": "baldurs<PERSON>s", "edeneternal": "вечносткаколеденека", "finalfantasy16": "finalfantazi16", "andyandleyley": "ендиилејли", "ff15": "ff15", "starfield": "starfield", "oldschoolrevival": "старошколскоповраќање", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "моркборг", "savageworlds": "дивисветови", "diabloiv": "diabloiv", "pve": "пве", "kingdomheart1": "kingdomheart1", "ff9": "фф9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "подземјенамракот", "juegosrpg": "рпгигри", "kingdomhearts": "кралствосрца", "kingdomheart3": "kingdomhearts3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "кланмалкавијан", "harvestella": "жетва", "gloomhaven": "мракотека", "wildhearts": "дивисрца", "bastion": "бастион", "drakarochdemoner": "дракароквсдемонер", "skiesofarcadia": "небатанааркадија", "shadowhearts": "сенкинисрца", "nierreplicant": "nierreplicant", "gnosia": "гнозија", "pennyblood": "грошевскакрв", "breathoffire4": "здивотоган4", "mother3": "мајка3", "cyberpunk2020": "сајберпанк2020", "falloutbos": "falloutbos", "anothereden": "уштееденедем", "roleplaygames": "игринаулоги", "roleplaygame": "играназулоги", "fabulaultima": "фабулаултима", "witchsheart": "срцевештерка", "harrypottergame": "харипотеригра", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "вампирскамаскарада", "dračák": "дракула", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "хронокрос", "cocttrpg": "cocttrpg", "huntroyale": "ловкралство", "albertodyssey": "алберто_одисеја", "monsterhunterworld": "монстерхантерворлд", "bg3": "bg3", "xenogear": "ксеногир", "temtem": "темтем", "rpgforum": "рпгфорум", "shadowheartscovenant": "заветотнасенкосрце", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "царствонебесно", "awplanet": "мацкипланета", "theworldendswithyou": "светотзавршувасотебе", "dragalialost": "dragalialostизгубена", "elderscroll": "постаритесвитоци", "dyinglight2": "dyinglight2", "finalfantasytactics": "финалфентазитактика", "grandia": "грандија", "darkheresy": "темнаересија", "shoptitans": "трговскититани", "forumrpg": "форумрпг", "golarion": "голарион", "earthmagic": "земјинамагија", "blackbook": "црнакнига", "skychildrenoflight": "небеснидецанасветлината", "gryrpg": "gryrpg", "sacredgoldedition": "светозлатноиздание", "castlecrashers": "разбивачиназамоци", "gothicgame": "готскаигра", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "духоважицатокио", "fallout2d20": "fallout2d20", "gamingrpg": "гејмингрпг", "prophunt": "проро<PERSON><PERSON>ов", "starrails": "ѕвезденипатеки", "cityofmist": "градмагла", "indierpg": "индирпг", "pointandclick": "покажииклокни", "emilyisawaytoo": "емилиеисточеста", "emilyisaway": "емилинемаја", "indivisible": "неделив", "freeside": "слободнастрана", "epic7": "епски7", "ff7evercrisis": "ff7вечнакриза", "xenogears": "ксеногирс", "megamitensei": "мегамитенсеи", "symbaroum": "симбарум", "postcyberpunk": "посткајберпанк", "deathroadtocanada": "патдосмртканада", "palladium": "паладиум", "knightjdr": "витезјдр", "monsterhunter": "ловецначудовишта", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "геосупремација", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "секиро", "monsterhunterrise": "monsterhunterrise", "nier": "ниер", "dothack": "дотхак", "ys": "ys", "souleater": "душо<PERSON>иј<PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "игриненарични", "tacticalrpg": "тактичкоrpg", "mahoyo": "mahoyo", "animegames": "анимеигри", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "божјоизедувач", "diluc": "diluc", "venti": "венти", "eternalsonata": "вечнасоната", "princessconnect": "принцесконект", "hexenzirkel": "вештерскикруг", "cristales": "кристали", "vcs": "венчурекапиталисти", "pes": "пес", "pocketsage": "џебенмудрец", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantиндиски", "dota": "дота", "madden": "лудница", "cdl": "cdl", "efootbal": "ефудбал", "nba2k": "nba2k", "egames": "електронскиигри", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "еспорт", "mlg": "млг", "leagueofdreamers": "лигасоништа", "fifa14": "fifa14", "midlaner": "мид<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "efootball": "ефудбал", "dreamhack": "dreamhack", "gaimin": "геjминг", "overwatchleague": "overwatchлига", "cybersport": "киберспорт", "crazyraccoon": "лудиот<PERSON><PERSON><PERSON><PERSON>н", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "бришење", "brasilgameshow": "бразилгејмшоу", "valorantcompetitive": "valorantкомпетитивно", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "портал2", "halflife": "половинаживот", "left4dead": "левфордед", "left4dead2": "left4dead2", "valve": "вентил", "portal": "портал", "teamfortress2": "тимфортрес2", "everlastingsummer": "вечнолето", "goatsimulator": "симулаторнакози", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "слободнапланета", "transformice": "transformice", "justshapesandbeats": "самообликиибитови", "battlefield4": "battlefield4", "nightinthewoods": "ноќвошума", "halflife2": "halflife2", "hacknslash": "сечиисекотини", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "метроидванија", "overcooked": "прегорено", "interplanetary": "интерпланетарно", "helltaker": "пеколземец", "inscryption": "вшифрирање", "7d2d": "7д2д", "deadcells": "мртвиќелии", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "тврдинанаџуџиња", "foxhole": "ровче", "stray": "заскитан", "battlefield": "бојнополе", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "подморница", "eyeb": "веѓи", "blackdesert": "црнапустина", "tabletopsimulator": "симулаторнамаса", "partyhard": "журкамедомаксимум", "hardspaceshipbreaker": "тешкоуништувачнавселенскибродови", "hades": "<PERSON>а<PERSON><PERSON><PERSON>", "gunsmith": "оружар", "okami": "оками", "trappedwithjester": "заробенсокловнот", "dinkum": "вистинито", "predecessor": "претходник", "rainworld": "светотнадождот", "cavesofqud": "пеште<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "колонисим", "noita": "noita", "dawnofwar": "зораnavојна", "minionmasters": "минион<PERSON>астерс", "grimdawn": "grimdawn", "darkanddarker": "темноипотемно", "motox": "motox", "blackmesa": "црнамеса", "soulworker": "душевенработник", "datingsims": "симулацииназабавувања", "yaga": "јага", "cubeescape": "бегствоодкоцка", "hifirush": "х<PERSON>ј<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "svencoop": "svencoop", "newcity": "новград", "citiesskylines": "градовинебо", "defconheavy": "defconтешко", "kenopsia": "кенопсија", "virtualkenopsia": "виртуелнакенопсија", "snowrunner": "снежентркач", "libraryofruina": "библиотеканаруини", "l4d2": "l4d2", "thenonarygames": "неномразиграши", "omegastrikers": "омегастрајкери", "wayfinder": "патоказ", "kenabridgeofspirits": "кенамостнадуховите", "placidplasticduck": "мирнапластичнапатка", "battlebit": "битбаталија", "ultimatechickenhorse": "врвнипилешкикон", "dialtown": "бројтелефон", "smileforme": "насмевнимисе", "catnight": "мачковечер", "supermeatboy": "супермесоманлак", "tinnybunny": "малечкозајче", "cozygrove": "удобнагај", "doom": "пропаст", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "цод", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdutyzомби", "apex": "врв", "r6siege": "r6siege", "megamanx": "мегаменикс", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "паладини", "earthdefenseforce": "земјинаодбранбенасила", "huntshowdown": "huntshowdown", "ghostrecon": "духовноизвидување", "grandtheftauto5": "grandtheftauto5", "warz": "војни", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ултраубиство", "joinsquad": "придруж<PERSON><PERSON>енатимот", "echovr": "echovr", "discoelysium": "дискоелизиум", "insurgencysandstorm": "востаниепесочнабура", "farcry3": "farcry3", "hotlinemiami": "жешкалинијамајами", "maxpayne": "макспејн", "hitman3": "хитмен3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "деатхстрандинг", "b4b": "б4б", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "кодзомбија", "mirrorsedge": "огледалорабови", "divisions2": "поделби2", "killzone": "киллзона", "helghan": "хелган", "coldwarzombies": "студенавојназомбија", "metro2033": "метро2033", "metalgear": "ме<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acecombat": "ас_комбат", "crosscode": "вкрстенкод", "goldeneye007": "златнооко007", "blackops2": "blackops2", "sniperelite": "снајперелита", "modernwarfare": "модернавојна", "neonabyss": "неонскабездна", "planetside2": "planetside2", "mechwarrior": "мехвојник", "boarderlands": "граничнизони", "owerwatch": "ower<PERSON>", "rtype": "ртип", "dcsworld": "dcsworld", "escapefromtarkov": "бегствоодтарков", "metalslug": "металслаг", "primalcarnage": "прималнабеснилост", "worldofwarships": "светнавоенибродови", "back4blood": "назад4крв", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "платенубиец", "masseffect": "masseffect", "systemshock": "системскишок", "valkyriachronicles": "валкиријахроники", "specopstheline": "specopsлинијата", "killingfloor2": "killingfloor2", "cavestory": "пештерскаприказна", "doometernal": "доомвечен", "centuryageofashes": "векнаpепел", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "м<PERSON>ау", "division2": "division2", "tythetasmaniantiger": "tythetasmanijskitigar", "generationzero": "генерацијанула", "enterthegungeon": "влезивоподземјето", "jakanddaxter": "џекидекстер", "modernwarfare2": "модернавојна2", "blackops1": "црниоперации1", "sausageman": "кол<PERSON><PERSON><PERSON><PERSON>ч<PERSON>р", "ratchetandclank": "рачетикл<PERSON>нк", "chexquest": "chexquest", "thephantompain": "фантомскаболка", "warface": "војналице", "crossfire": "вкрстенаоган", "atomicheart": "атомскосрце", "blackops3": "црниоперации3", "vampiresurvivors": "вампирипреживеани", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "слобода", "battlegrounds": "бојнополиња", "frag": "фра<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinytina": "мала<PERSON>ина", "gamepubg": "играпубг", "necromunda": "necromunda", "metalgearsonsoflibert": "металгирсиновинаслободата", "juegosfps": "fpсигри", "convertstrike": "конвертстрајк", "warzone2": "warzone2", "shatterline": "распарчено", "blackopszombies": "блекопсзомбија", "bloodymess": "крвавха<PERSON>с", "republiccommando": "републиканскикомандос", "elitedangerous": "елитопасно", "soldat": "солдат", "groundbranch": "основнагранка", "squad": "екипа", "destiny1": "судбина1", "gamingfps": "гејмингfps", "redfall": "црвенпад", "pubggirl": "pubgдевојка", "worldoftanksblitz": "светнатенковиблиц", "callofdutyblackops": "callofdutyblackops", "enlisted": "воен", "farlight": "далекусветлина", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "оклопенојадро", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "хало2", "payday2": "деннаплата2", "cs16": "cs16", "pubgindonesia": "pubgиндонезија", "pubgukraine": "pubgук<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "пабгроманија", "empyrion": "емпирион", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "сапунрипа", "ghostcod": "духкод", "csplay": "косплеј", "unrealtournament": "нереалентурнир", "callofdutydmz": "callofdutydmz", "gamingcodm": "gamingcodm", "borderlands2": "borderlands2", "counterstrike": "контраудар", "cs2": "cs2", "pistolwhip": "пиштолџамбас", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "хало3", "halo": "хало", "killingfloor": "убиственоподие", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "сплинтерсел", "neonwhite": "неонбело", "remnant": "остаток", "azurelane": "azurelane", "worldofwar": "светнавојна", "gunvolt": "ганволт", "returnal": "повторно", "halo4": "halo4", "haloreach": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowman": "сенкачовек", "quake2": "земјотрес2", "microvolts": "микроволти", "reddead": "црвени<PERSON><PERSON><PERSON>ов", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "мореназлосторници", "rust": "раѓа", "conqueronline": "conqueronline", "dauntless": "неустрашлив", "warships": "военибродови", "dayofdragons": "денотнадраконите", "warthunder": "војнагрмеж", "flightrising": "растењедракони", "recroom": "игралница", "legendsofruneterra": "легендинарунетера", "pso2": "pso2", "myster": "мистер", "phantasystaronline2": "фантазистаронлајн2", "maidenless": "безмома", "ninokuni": "ниноќуни", "worldoftanks": "светнатенкови", "crossout": "прецрт<PERSON><PERSON>", "agario": "agario", "secondlife": "вторживот", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "мрежнаигра", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "нинокуникросворлд", "reddeadonline": "редедедонлајн", "superanimalroyale": "суперживотинскокралство", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "тбои", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "клубпингвин", "lotro": "lotro", "wakfu": "wakfu", "scum": "ѓубре", "newworld": "новсвет", "blackdesertonline": "blackdesertonline", "multiplayer": "мултиплејер", "pirate101": "пират101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "понита<PERSON>н", "3dchat": "3дчет", "nostale": "ностале", "tauriwow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wowclassic": "вовкласик", "worldofwarcraft": "worldofwarcraft", "warcraft": "варкрафт", "wotlk": "wotlk", "runescape": "runescape", "neopets": "неопетс", "moba": "моба", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "пепелодсоздавање", "riotmmo": "риотммо", "silkroad": "патотнасвилата", "spiralknights": "спиралнивитези", "mulegend": "легендарномуле", "startrekonline": "стартрекнаинтернет", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "сечилоидуша", "evony": "evony", "dragonsprophet": "пророкотнадраконите", "grymmo": "мрачнамамо", "warmane": "топчина", "multijugador": "повеќеиграчи", "angelsonline": "ангелион<PERSON><PERSON><PERSON>н", "lunia": "луниа", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcунивезумонлајн", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsстаратарепублика", "grandfantasia": "големафантазија", "blueprotocol": "синпротокол", "perfectworld": "совршенсвет", "riseonline": "искачисеонлајн", "corepunk": "корпанк", "adventurequestworlds": "adventurequestworlds", "flyforfun": "летајзазабава", "animaljam": "animaljam", "kingdomofloathing": "кралствонапрезир", "cityofheroes": "градначерои", "mortalkombat": "мортал_комбат", "streetfighter": "стритфајтер", "hollowknight": "hollowknight", "metalgearsolid": "метлгирсолид", "forhonor": "зачест", "tekken": "текен", "guiltygear": "гил<PERSON><PERSON><PERSON><PERSON>р", "xenoverse2": "xenoverse2", "fgc": "борбенигри", "streetfighter6": "стритфајтер6", "multiversus": "мултиверзус", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "виртуелецборец", "streetsofrage": "улиците<PERSON><PERSON><PERSON>с", "mkdeadlyalliance": "мкдедлиа<PERSON><PERSON><PERSON>а<PERSON>с", "nomoreheroes": "немаповеќехерои", "mhr": "мчр", "mortalkombat12": "mortalkomбат12", "thekingoffighters": "кралотнаборците", "likeadragon": "какодракон", "retrofightinggames": "ретроборбениигри", "blasphemous": "богохулно", "rivalsofaether": "ривалитенаетерот", "persona4arena": "persona4arena", "marvelvscapcom": "марвелпротивкапком", "supersmash": "суперсмеса", "mugen": "муген", "warofthemonsters": "војнаначудовишта", "jogosdeluta": "игринаборба", "cyberbots": "сајберботови", "armoredwarriors": "оклопниривоини", "finalfight": "финалнаборба", "poweredgear": "моќнаопрема", "beatemup": "тепачка", "blazblue": "blazblue", "mortalkombat9": "морталкомбат9", "fightgames": "борбениигри", "killerinstinct": "убиственинстинкт", "kingoffigthers": "кралнаборци", "ghostrunner": "духовнитрк<PERSON>ч", "chivalry2": "витештво2", "demonssouls": "демонскидуши", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "продолжениенахолоунајт", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "силксонгстршел", "silksonggame": "silksonggame", "silksongnews": "вестизасилксонг", "silksong": "silksong", "undernight": "подноќе", "typelumina": "типлумина", "evolutiontournament": "турнирнаеволуција", "evomoment": "евомомент", "lollipopchainsaw": "лизгалчепилка", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "bloodborne", "horizon": "хоризонт", "pathofexile": "pathofexile", "slimerancher": "слајмодгледувач", "crashbandicoot": "крешбандикут", "bloodbourne": "bloodbourne", "uncharted": "неистражено", "horizonzerodawn": "хоризонзеродаун", "ps4": "ps4", "ps5": "ps5", "spyro": "спајро", "playstationplus": "playstationplus", "lastofus": "последниодние", "infamous": "инфамен", "playstationbuddies": "playstationдругари", "ps1": "ps1", "oddworld": "чуденсвет", "playstation5": "playstation5", "slycooper": "итриотмечок", "psp": "psp", "rabbids": "рабидс", "splitgate": "сплитгејт", "persona4": "persona4", "hellletloose": "здравоослободисе", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "богнавојната", "gris": "грис", "trove": "најдба", "detroitbecomehuman": "детроитстанувачовек", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "стелар<PERSON>с", "ps3": "ps3", "untildawn": "доизгрејсонце", "touristtrophy": "туристичкитрофеј", "lspdfr": "lspdfr", "shadowofthecolossus": "сенканаколосот", "crashteamracing": "крештимр<PERSON>јсинг", "fivepd": "петпд", "tekken7": "tekken7", "devilmaycry": "ѓаволотможедаплаче", "devilmaycry3": "деволможеплаче3", "devilmaycry5": "деволможедаплаче5", "ufc4": "ufc4", "playingstation": "играњестаница", "samuraiwarriors": "самурајскивоини", "psvr2": "psvr2", "thelastguardian": "последниотчувар", "soulblade": "душевномеч", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "ловнамажи", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "последенчувар", "xboxone": "xboxone", "forza": "форца", "cd": "cd", "gamepass": "гејмпас", "armello": "armello", "partyanimal": "журкаџија", "warharmmer40k": "warhammer40k", "fightnightchampion": "борбенаноќшампион", "psychonauts": "психонаути", "mhw": "mhw", "princeofpersia": "принцотнаперсија", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "пантареи", "theelderscrolls": "старитесвитоци", "gxbox": "gxbox", "battlefront": "бојнополе", "dontstarvetogether": "незаедногладувајте", "ori": "ori", "spelunky": "спелунки", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "звезденопат", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "реноватор", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxserija", "r6xbox": "r6xbox", "leagueofkingdoms": "лиг<PERSON>нанакралствата", "fable2": "басна2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "трештв", "skycotl": "небокотл", "erica": "ерика", "ancestory": "предци", "cuphead": "капхед", "littlemisfortune": "малатанесреќничка", "sallyface": "салифе<PERSON>с", "franbow": "франбоу", "monsterprom": "монстерматура", "projectzomboid": "проектзомбоид", "ddlc": "ddlc", "motos": "мотори", "outerwilds": "надворешнидивини", "pbbg": "pbbg", "anshi": "ан<PERSON>и", "cultofthelamb": "култнајагнето", "duckgame": "паткаигра", "thestanleyparable": "стенлипарабола", "towerunite": "обединетакула", "occulto": "окулто", "longdrive": "долговозење", "satisfactory": "задоволително", "pluviophile": "љубителнадожд", "underearth": "подземја", "assettocorsa": "assettocorsa", "geometrydash": "геометридеш", "kerbal": "кербал", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "духопревозник", "darkdome": "темнакупола", "pizzatower": "pizzatower", "indiegame": "индиигра", "itchio": "itchio", "golfit": "голфи<PERSON><PERSON><PERSON>", "truthordare": "вистинаилипредизвик", "game": "игра", "rockpaperscissors": "камењножнициихартија", "trampoline": "трамболина", "hulahoop": "хул<PERSON><PERSON><PERSON><PERSON>", "dare": "предизвик", "scavengerhunt": "ловнатрага", "yardgames": "дворскиигри", "pickanumber": "избериб<PERSON><PERSON>ј", "trueorfalse": "вистинитоилилажно", "beerpong": "бирпонг", "dicegoblin": "коцкарскиспириду", "cosygames": "уткинитеигри", "datinggames": "играњедејтинг", "freegame": "бесплатнаигра", "drinkinggames": "игризапиење", "sodoku": "содоку", "juegos": "juegos", "mahjong": "маџонг", "jeux": "игри", "simulationgames": "симулациискиигри", "wordgames": "зборовниигри", "jeuxdemots": "игранасозборови", "juegosdepalabras": "игриназборови", "letsplayagame": "ајдедаиграмеигра", "boredgames": "досаднииигри", "oyun": "игра", "interactivegames": "интерактивниигри", "amtgard": "ам<PERSON>г<PERSON><PERSON>д", "staringcontests": "натпреваривогледување", "spiele": "игри", "giochi": "игри", "geoguessr": "геогесар", "iphonegames": "игризаајфон", "boogames": "booигри", "cranegame": "игранакранче", "hideandseek": "криенка", "hopscotch": "школка", "arcadegames": "аркадниигри", "yakuzagames": "игритеякуза", "classicgame": "кла<PERSON><PERSON>чнаигра", "mindgames": "умниигри", "guessthelyric": "погодигостихот", "galagames": "галаигри", "romancegame": "романтичнаигра", "yanderegames": "јандерегејмс", "tonguetwisters": "јазичнибрзалки", "4xgames": "4xигри", "gamefi": "gamefi", "jeuxdarcades": "аркаднииграчки", "tabletopgames": "друштвениигри", "metroidvania": "metroidvania", "games90": "игри90", "idareyou": "тепредизвикувам", "mozaa": "mozaa", "fumitouedagames": "игридофумитоуеда", "racinggames": "тркачкиигри", "ets2": "ets2", "realvsfake": "вистинсковслажно", "playgames": "играјигри", "gameonline": "игрионл<PERSON>јн", "onlinegames": "онлајнигри", "jogosonline": "игринаинтернет", "writtenroleplay": "писменарпг", "playaballgame": "играјтопка", "pictionary": "пикшнари", "coopgames": "коопигри", "jenga": "џенга", "wiigames": "wiiигри", "highscore": "најдобаррезултат", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "бургеригри", "kidsgames": "игризадеца", "skeeball": "скибол", "nfsmwblackedition": "nfsmwцрноиздание", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "играпрашања", "gioco": "gioco", "managementgame": "игранаменаџмент", "hiddenobjectgame": "игранабарањескриенипредмети", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "формула1игра", "citybuilder": "градит<PERSON><PERSON><PERSON>град", "drdriving": "дрвозење", "juegosarcade": "аркаднииигри", "memorygames": "игризамеморија", "vulkan": "вулкан", "actiongames": "акционииигри", "blowgames": "игриздување", "pinballmachines": "пинбал<PERSON><PERSON><PERSON>ини", "oldgames": "стариигри", "couchcoop": "каучкооп", "perguntados": "прашан", "gameo": "гејмо", "lasergame": "ласерскаигра", "imessagegames": "imessageигри", "idlegames": "игриназдосада", "fillintheblank": "пополниго", "jeuxpc": "игринакомп", "rétrogaming": "ретрогејминг", "logicgames": "логичкиигри", "japangame": "јапонскаигра", "rizzupgame": "вежбајризата", "subwaysurf": "метросурфање", "jeuxdecelebrite": "игринапознати", "exitgames": "играизлез", "5vs5": "5на5", "rolgame": "играулога", "dashiegames": "дешигејмс", "gameandkill": "игр<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "традиционалниигри", "kniffel": "јамб", "gamefps": "играфпс", "textbasedgames": "игрисотекст", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "фантакалчо", "retrospel": "ретроспел", "thiefgame": "игра<PERSON><PERSON>лодов", "lawngames": "игринатрева", "fliperama": "флиперама", "heroclix": "heroclix", "tablesoccer": "столнифудбал", "tischfußball": "тишфусбал", "spieleabende": "играноќи", "jeuxforum": "jeuxforum", "casualgames": "кежуалигри", "fléchettes": "стрелички", "escapegames": "излезниигри", "thiefgameseries": "серијатаигризакрадци", "cranegames": "игринакран", "játék": "игра", "bordfodbold": "бордфудбал", "jogosorte": "jogosorte", "mage": "маг", "cargames": "автоигри", "onlineplay": "онлајнигра", "mölkky": "мелки", "gamenights": "вечеризаигри", "pursebingos": "таш<PERSON>бинго", "randomizer": "рандомизатор", "msx": "msx", "anagrammi": "анаграми", "gamespc": "игриpc", "socialdeductiongames": "социјалнидедуктивниигри", "dominos": "домино", "domino": "домино", "isometricgames": "изометрискиигри", "goodoldgames": "добристаригри", "truthanddare": "вистинаипредизвик", "mahjongriichi": "маџонгричи", "scavengerhunts": "ловналетки", "jeuxvirtuel": "виртуелниигри", "romhack": "ромхак", "f2pgamer": "f2pгејмер", "free2play": "бесплатназаиграње", "fantasygame": "фантазиигра", "gryonline": "игрионл<PERSON>јн", "driftgame": "дрифтигра", "gamesotomes": "игризаотоме", "halotvseriesandgames": "halotv<PERSON><PERSON><PERSON><PERSON>", "mushroomoasis": "печуркинаоаза", "anythingwithanengine": "сèштоимамотор", "everywheregame": "игранасекаде", "swordandsorcery": "мечисмагија", "goodgamegiving": "добраигразадавање", "jugamos": "играме", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "компјутерскиигри", "virgogami": "девицаоригами", "gogame": "ајдеигра", "jeuxderythmes": "играсоритам", "minaturegames": "минијатурниигри", "ridgeracertype4": "риџрејсертајп4", "selflovegaming": "себесакањеигри", "gamemodding": "модирањеигри", "crimegames": "криминалниигри", "dobbelspellen": "друштвениигри", "spelletjes": "игрички", "spacenerf": "кос<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "charades": "шаради", "singleplayer": "singleplayer", "coopgame": "коопигра", "gamed": "играв", "forzahorizon": "forzahorizon", "nexus": "нексус", "geforcenow": "geforcenow", "maingame": "главнаигра", "kingdiscord": "кинг<PERSON>искорд", "scrabble": "scrabble", "schach": "шах", "shogi": "шоги", "dandd": "dиd", "catan": "катан", "ludo": "лудо", "backgammon": "табла", "onitama": "onitama", "pandemiclegacy": "пандемисконаследство", "camelup": "камилагоре", "monopolygame": "монополи", "brettspiele": "brettspiele", "bordspellen": "друштвениигри", "boardgame": "друштвенаигра", "sällskapspel": "игризадруштво", "planszowe": "друштвени", "risiko": "рисико", "permainanpapan": "друштвениигри", "zombicide": "зомбициид", "tabletop": "настолни", "baduk": "бадук", "bloodbowl": "крвава<PERSON><PERSON><PERSON>а", "cluedo": "клуедо", "xiangqi": "шах", "senet": "сенет", "goboardgame": "одиграјтабла", "connectfour": "четиривоврска", "heroquest": "хероквест", "giochidatavolo": "настолниигри", "farkle": "фаркл", "carrom": "карам", "tablegames": "настолниигри", "dicegames": "игринасоцки", "yatzy": "јамб", "parchis": "парчис", "jogodetabuleiro": "друштвенаигра", "jocuridesocietate": "друштвениигри", "deskgames": "игринаработа", "alpharius": "алфар<PERSON><PERSON><PERSON>", "masaoyunları": "игриназамаса", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "космичкасредба", "creationludique": "креативназабава", "tabletoproleplay": "масе<PERSON>ролплеј", "cardboardgames": "картонскиигри", "eldritchhorror": "елдричхорор", "switchboardgames": "игринаразделба", "infinitythegame": "бескрајнаигра", "kingdomdeath": "кралствосмрт", "yahtzee": "јаци", "chutesandladders": "змиииштали", "társas": "другарување", "juegodemesa": "друштвенаигра", "planszówki": "друштвенигри", "rednecklife": "селјачкиживот", "boardom": "досада", "applestoapples": "јаболкасојаболка", "jeudesociété": "друштвенаигра", "gameboard": "табла", "dominó": "домино", "kalah": "калах", "crokinole": "крокинол", "jeuxdesociétés": "друштвенигри", "twilightimperium": "сумракнаимперија", "horseopoly": "коњополи", "deckbuilding": "градењ<PERSON><PERSON><PERSON>ил", "mansionsofmadness": "вилинабезумието", "gomoku": "гомоку", "giochidatavola": "игринамаса", "shadowsofbrimstone": "сенкинабримстоун", "kingoftokyo": "кралнатокио", "warcaby": "дама", "táblajátékok": "друштвениигри", "battleship": "бродвобитка", "tickettoride": "билетзавозење", "deskovehry": "десковеигри", "catán": "катан", "subbuteo": "субутео", "jeuxdeplateau": "друштвениигри", "stolníhry": "настолниигри", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "друштвениигри", "gesellschaftsspiele": "друштвениигри", "starwarslegion": "војнаназвездитележија", "gochess": "одишах", "weiqi": "вејчи", "jeuxdesocietes": "друштвенигри", "terraria": "terraria", "dsmp": "dsmp", "warzone": "војназона", "arksurvivalevolved": "arksurvivalevolved", "dayz": "денови", "identityv": "identityv", "theisle": "островот", "thelastofus": "последнитеоднас", "nomanssky": "нечовековонебо", "subnautica": "subnautica", "tombraider": "томбрејдер", "callofcthulhu": "повикотнактулу", "bendyandtheinkmachine": "бендии<PERSON><PERSON><PERSON>инатазамастило", "conanexiles": "conanexiles", "eft": "ефт", "amongus": "мег<PERSON><PERSON><PERSON>", "eco": "еко", "monkeyisland": "мајмунскиостров", "valheim": "valheim", "planetcrafter": "планетаградител", "daysgone": "деновиминати", "fobia": "фобија", "witchit": "вештерајго", "pathologic": "патолошки", "zomboid": "зомбоид", "northgard": "northgard", "7dtd": "7де<PERSON>", "thelongdark": "долгиоттемен", "ark": "арк", "grounded": "казнет", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "лудтатко", "dontstarve": "недејгладувај", "eternalreturn": "вечновраќање", "pathoftitans": "патекаоттитани", "frictionalgames": "фрикшнлгејмс", "hexen": "вештерки", "theevilwithin": "злотоодвнатре", "realrac": "вистинскирас", "thebackrooms": "задничкипростории", "backrooms": "задниипростории", "empiressmp": "empiressmp", "blockstory": "блокприказна", "thequarry": "каменоломот", "tlou": "tlou", "dyinglight": "умирачкасветлина", "thewalkingdeadgame": "thewalkingdeadигра", "wehappyfew": "ниемалкумина", "riseofempires": "подемнаимперии", "stateofsurvivalgame": "stateofsurvivalигра", "vintagestory": "винтажприказна", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "баротраума", "breathedge": "безвоздух", "alisa": "алиса", "westlendsurvival": "преживувањезападенлондон", "beastsofbermuda": "ѕверовиодбермуда", "frostpunk": "фростпанк", "darkwood": "темнашума", "survivalhorror": "хорорпреживување", "residentevil": "резидентивил", "residentevil2": "резидентевил2", "residentevil4": "residentevil4", "residentevil3": "резидентивил3", "voidtrain": "празенвоз", "lifeaftergame": "животпоигра", "survivalgames": "играназаопстанок", "sillenthill": "сајлент<PERSON>ил", "thiswarofmine": "оваавојнамоја", "scpfoundation": "скпфондација", "greenproject": "зеленпроект", "kuon": "kuon", "cryoffear": "плачамодстрав", "raft": "сплав", "rdo": "рдо", "greenhell": "зеленпекол", "residentevil5": "резидентивил5", "deadpoly": "мртвополи", "residentevil8": "резидентевил8", "onironauta": "онеиронавт", "granny": "баба", "littlenightmares2": "малиноќникошмари2", "signalis": "сиг<PERSON><PERSON><PERSON>с", "amandatheadventurer": "авантуристкатааманда", "sonsoftheforest": "синовинашумата", "rustvideogame": "rustvideogame", "outlasttrials": "издржипробите", "alienisolation": "вонземскаизолација", "undawn": "зора", "7day2die": "7дена2умри", "sunlesssea": "моребезсонце", "sopravvivenza": "преживување", "propnight": "пропноќ", "deadisland2": "мртвоостро2", "ikemensengoku": "икеменсенгоку", "ikemenvampire": "убавмажвампир", "deathverse": "смртенвселена", "cataclysmdarkdays": "катаклизматемнидни", "soma": "сома", "fearandhunger": "стравиглад", "stalkercieńczarnobyla": "сталкерсенкаодчернобил", "lifeafter": "животпотоа", "ageofdarkness": "добанатемнината", "clocktower3": "саатнакула3", "aloneinthedark": "самвотемница", "medievaldynasty": "средновековнадинастија", "projectnimbusgame": "проектнимбусигра", "eternights": "вечнивечери", "craftopia": "занаетчилак", "theoutlasttrials": "theoutlasttrials", "bunker": "бу<PERSON><PERSON><PERSON><PERSON>", "worlddomination": "светскадоминација", "rocketleague": "рокетлиг", "tft": "tft", "officioassassinorum": "официоасасинорум", "necron": "некрон", "wfrp": "wfrp", "dwarfslayer": "џуџоубиец", "warhammer40kcrush": "warhammer40kзаљубеност", "wh40": "wh40", "warhammer40klove": "warhammer40kљубов", "warhammer40klore": "warhammer40kлор", "warhammer": "вархамер", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "храмкулексус", "vindicare": "винdicare", "ilovesororitas": "сакамсестринства", "ilovevindicare": "сакамдасеосветам", "iloveassasinorum": "љубамубици", "templovenenum": "храмотровенум", "templocallidus": "темплокалидус", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "темплован", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40к", "tetris": "тетрис", "lioden": "лавски", "ageofempires": "добанаимперии", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "цивилизација5", "ittakestwo": "требаатдвајца", "wingspan": "распонкрилја", "terraformingmars": "тераформирањемарс", "heroesofmightandmagic": "хероиодмоќимагија", "btd6": "btd6", "supremecommander": "врховенкомандант", "ageofmythology": "векнамитологијата", "args": "args", "rime": "рима", "planetzoo": "планетазоо", "outpost2": "испостава2", "banished": "протеран", "caesar3": "цезар3", "redalert": "црвенаалерт", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "командувајивладеј", "warcraft3": "warcraft3", "eternalwar": "вечнавојна", "strategygames": "стратегискиигри", "anno2070": "anno2070", "civilizationgame": "играцивилизација", "civilization4": "цивилизација4", "factorio": "factorio", "dungeondraft": "темница_нацрт", "spore": "спор", "totalwar": "тоталнавојна", "travian": "travian", "forts": "фортови", "goodcompany": "добродруштво", "civ": "цив", "homeworld": "роднокрај", "heidentum": "паганство", "aoe4": "aoe4", "hnefatafl": "хнефатафл", "fasterthanlight": "побрзоодсветлина", "forthekings": "закраловите", "realtimestrategy": "стратегијавореалновреме", "starctaft": "starctaft", "sidmeierscivilization": "сидмаерсцивилизација", "kingdomtwocrowns": "кралстводвекруни", "eu4": "eu4", "vainglory": "суета", "ww40k": "ww40k", "godhood": "божество", "anno": "анно", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "хеттрик", "davesfunalgebraclass": "дејвоватазабавначасалгебрамк", "plagueinc": "чу<PERSON><PERSON><PERSON><PERSON>к", "theorycraft": "теорија<PERSON><PERSON><PERSON><PERSON>т", "mesbg": "mesbg", "civilization3": "цивилизација3", "4inarow": "4вред", "crusaderkings3": "крстоноснитекралеви3", "heroes3": "херои3", "advancewars": "напреднивојни", "ageofempires2": "ageofempires2", "disciples2": "ученици2", "plantsvszombies": "растенијапротивзомбија", "giochidistrategia": "игринастратегија", "stratejioyunları": "стратегискиигри", "europauniversalis4": "европауниверзалис4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "добанадивотии", "dinosaurking": "кралнадиносаурите", "worldconquest": "светскоосвојување", "heartsofiron4": "heartsofiron4", "companyofheroes": "компанијанахерои", "battleforwesnoth": "битказавеснот", "aoe3": "aoe3", "forgeofempires": "ковачницанаимперии", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "гускагускапатка", "phobies": "фобии", "phobiesgame": "играфобии", "gamingclashroyale": "игрикла<PERSON><PERSON><PERSON><PERSON>ал", "adeptusmechanicus": "адептусмеханикус", "outerplane": "надворешнарамнина", "turnbased": "наредување", "bomberman": "бомбермен", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "крстоноснитекралеви", "cultris2": "cultris2", "spellcraft": "магиски_занает", "starwarsempireatwar": "starwarsимперијавовојна", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "стратегија", "popfulmail": "поштасополнапопулација", "shiningforce": "сјајнасила", "masterduel": "мастер<PERSON><PERSON><PERSON>л", "dysonsphereprogram": "програмадисоновасфера", "transporttycoon": "транспортниотмагнат", "unrailed": "безшини", "magicarena": "<PERSON><PERSON>na", "wolvesville": "вервулф", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "горнокралства", "galaxylife": "галак<PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "волчеградонлајн", "slaythespire": "уништигокулата", "battlecats": "мачкиборци", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "симсите", "simcity": "симсити", "simcity2000": "симсити2000", "sims2": "симс2", "iracing": "iracing", "granturismo": "грантуризмо", "needforspeed": "потребазабрзина", "needforspeedcarbon": "потребазабрзинакарбон", "realracing3": "вистинскитрки3", "trackmania": "trackmania", "grandtourismo": "грантуризмо", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "издржи", "deadbydaylight": "мртвидопладне", "alicemadnessreturns": "алисасевраќаволудилото", "darkhorseanthology": "антологијанацрниотконј", "phasmophobia": "фазмофобија", "fivenightsatfreddys": "петноќивокајфреди", "saiko": "сајко", "fatalframe": "фаталнарамка", "littlenightmares": "малиноќникошмари", "deadrising": "мртвовоскресение", "ladydimitrescu": "лејдидимитреску", "homebound": "домадр<PERSON>ани", "deadisland": "мртовостров", "litlemissfortune": "малатанесреќница", "projectzero": "проектнула", "horory": "хорори", "jogosterror": "џогостерор", "helloneighbor": "здравососеде", "helloneighbor2": "здравокомшија2", "gamingdbd": "гејмингdbd", "thecatlady": "мачкарката", "jeuxhorreur": "хорор_игри", "horrorgaming": "хорориграње", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "картипротивчовештвото", "cribbage": "табланет", "minnesotamtg": "минесотамтг", "edh": "edh", "monte": "монте", "pinochle": "табланет", "codenames": "кодовиимиња", "dixit": "дискит", "bicyclecards": "картизавелосипед", "lor": "лор", "euchre": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thegwent": "thegwent", "legendofrunetera": "легендаодрунетера", "solitaire": "паси<PERSON><PERSON><PERSON><PERSON>", "poker": "покер", "hearthstone": "hearthstone", "uno": "уно", "schafkopf": "шафкопф", "keyforge": "keyforge", "cardtricks": "картички", "playingcards": "карти", "marvelsnap": "marvelsnap", "ginrummy": "ркмиџин", "netrunner": "нетранер", "gwent": "гвент", "metazoo": "метазу", "tradingcards": "картички", "pokemoncards": "покемонкарти", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "спортскикартички", "cardfightvanguard": "cardfightvanguard", "duellinks": "ду<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spades": "пикови", "warcry": "воендрек", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "кралнасрца", "truco": "труко", "loteria": "лотерија", "hanafuda": "hana<PERSON>da", "theresistance": "отпорот", "transformerstcg": "трансформерситкг", "doppelkopf": "доплкопф", "yugiohcards": "југиокарти", "yugiohtcg": "yugiohtcg", "yugiohduel": "југиоду<PERSON>л", "yugiohocg": "југиоокг", "dueldisk": "дуелдиск", "yugiohgame": "jugiohigra", "darkmagician": "црнмагичар", "blueeyeswhitedragon": "синиочибелдракон", "yugiohgoat": "yugiohкоза", "briscas": "брискас", "juegocartas": "картичкиигри", "burraco": "бурако", "rummy": "рами", "grawkarty": "гравкарти", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "котро", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "картиигри", "duelyst": "дуелист", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "картичкиигри", "carteado": "карти<PERSON><PERSON><PERSON>о", "sueca": "суека", "beloteonline": "белотонлајн", "karcianki": "карцијанки", "battlespirits": "борбенидухови", "battlespiritssaga": "сагазадуховинабитката", "jogodecartas": "играсокарти", "žolíky": "жолиќи", "facecard": "убавлик", "cardfight": "картичкаборба", "biriba": "бириба", "deckbuilders": "градите<PERSON><PERSON>нашпилови", "marvelchampions": "марвелшампиони", "magiccartas": "волшебникарти", "yugiohmasterduel": "jugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "скипбо", "unstableunicorns": "нестабилниеднорози", "cyberse": "са<PERSON><PERSON><PERSON>р", "classicarcadegames": "класичниаркадниигри", "osu": "osu", "gitadora": "гитадора", "dancegames": "игризатанцување", "fridaynightfunkin": "петокноќнозабавување", "fnf": "пив", "proseka": "просека", "projectmirai": "проектмираи", "projectdiva": "проектдива", "djmax": "djmax", "guitarhero": "гит<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "клонхеро", "justdance": "самотанцувај", "hatsunemiku": "хацунемику", "prosekai": "prosekai", "rocksmith": "рокшмит", "idolish7": "idolish7", "rockthedead": "расцепигимртвите", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "танцувајцентрално", "rhythmgamer": "ритамгејмер", "stepmania": "степманија", "highscorerythmgames": "високискоровиритамигри", "pkxd": "pkxd", "sidem": "сидем", "ongeki": "онгеки", "soundvoltex": "саундволтекс", "rhythmheaven": "рита<PERSON><PERSON><PERSON><PERSON>", "hypmic": "hypmic", "adanceoffireandice": "танцотнаогинилед", "auditiononline": "аудициja<PERSON><PERSON><PERSON><PERSON><PERSON>н", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "игринаритам", "cryptofthenecrodancer": "криптонанекроиграчот", "rhythmdoctor": "докторзаритам", "cubing": "коцкање", "wordle": "wordle", "teniz": "тенис", "puzzlegames": "сложувалки", "spotit": "најдиго", "rummikub": "рамикуб", "blockdoku": "блокдоку", "logicpuzzles": "логичкизагатки", "sudoku": "судоку", "rubik": "рубик", "brainteasers": "главоломки", "rubikscube": "рубиковакоцка", "crossword": "вкрстозбор", "motscroisés": "вкрстенизборови", "krzyżówki": "крстозбори", "nonogram": "нонограм", "bookworm": "книгољубец", "jigsawpuzzles": "сложувалки", "indovinello": "загатка", "riddle": "загатка", "riddles": "загатки", "rompecabezas": "слагалица", "tekateki": "тешкатешка", "inside": "внатре", "angrybirds": "лутиптици", "escapesimulator": "симулаторзабегство", "minesweeper": "минскополе", "puzzleanddragons": "пазлиидракони", "crosswordpuzzles": "кросворди", "kurushi": "кур<PERSON>i", "gardenscapesgame": "gardenscapesигра", "puzzlesport": "сложувалкаспорт", "escaperoomgames": "игранабегање", "escapegame": "игранабегање", "3dpuzzle": "3дпазла", "homescapesgame": "homescapesигра", "wordsearch": "зборалка", "enigmistica": "енигматика", "kulaworld": "светоткула", "myst": "мистерија", "riddletales": "загаткиприказни", "fishdom": "fishdom", "theimpossiblequiz": "невозможенквиз", "candycrush": "candycrush", "littlebigplanet": "малатаголемапланета", "match3puzzle": "матч3сложувалка", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "катамаридамаси", "kwirky": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikcube": "рубиковакоцка", "cuborubik": "рубиковакоцка", "yapboz": "сериозенразговор", "thetalosprinciple": "принципотнаталос", "homescapes": "домашнисцени", "puttputt": "патпат", "qbert": "qbert", "riddleme": "загатками", "tycoongames": "тајкунигри", "cubosderubik": "кубовинарубик", "cruciverba": "крстозбор", "ciphers": "шифри", "rätselwörter": "загатки", "buscaminas": "бускаминас", "puzzlesolving": "решавањезагатки", "turnipboy": "репкобој", "adivinanzashot": "топзагатки", "nobodies": "никои", "guessing": "гаѓање", "nonograms": "нонограми", "kostkirubika": "костиотнарубик", "crypticcrosswords": "криптичникрстозбори", "syberia2": "сибир2", "puzzlehunt": "потерапозагатки", "puzzlehunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "мачкозлосторство", "quebracabeça": "сложувалка", "hlavolamy": "главоломки", "poptropica": "попртопика", "thelastcampfire": "последниотлогорскиоган", "autodefinidos": "самодефинирани", "picopark": "пикопарк", "wandersong": "патникскапесна", "carto": "карто", "untitledgoosegame": "untitledgoosegame", "cassetête": "главоболка", "limbo": "лимбо", "rubiks": "рубикова", "maze": "лав<PERSON><PERSON><PERSON><PERSON>т", "tinykin": "мицка", "rubikovakostka": "рубиковакоцка", "speedcube": "брзокоцка", "pieces": "парчиња", "portalgame": "играпортал", "bilmece": "билмица", "puzzelen": "puzzelen", "picross": "пикрос", "rubixcube": "рубиковакоцка", "indovinelli": "загатки", "cubomagico": "волшебноколце", "mlbb": "млбб", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "извртеначудесназемја", "monopoly": "монопол", "futurefight": "битказаиднината", "mobilelegends": "мобајллегендс", "brawlstars": "brawlstars", "brawlstar": "бролстар", "coc": "coc", "lonewolf": "самотенволк", "gacha": "гача", "wr": "вр", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "ensemblestars", "asphalt9": "asphalt9", "mlb": "млб", "cookierunkingdom": "cookierunkingdom", "alchemystars": "алхемиѕвезди", "stateofsurvival": "состојбанаопстанок", "mycity": "мојградче", "arknights": "arknights", "colorfulstage": "шаренапозорница", "bloonstowerdefense": "блунстауердифенс", "btd": "бтд", "clashroyale": "clashroyale", "angela": "анџела", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "хиперфронт", "knightrun": "витезтрчи", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "хонкајимпакт", "soccerbattle": "фудбалскабитка", "a3": "a3", "phonegames": "игринателефон", "kingschoice": "изборнакралот", "guardiantales": "guardiantales", "petrolhead": "бензинџија", "tacticool": "тактикул", "cookierun": "cookierun", "pixeldungeon": "пикселтамница", "arcaea": "arcaea", "outoftheloop": "невоток", "craftsman": "мајстор", "supersus": "мега<PERSON><PERSON><PERSON>", "slowdrive": "спороскијевозам", "headsup": "внимавај", "wordfeud": "зборобој", "bedwars": "bedwars", "freefire": "фрифајр", "mobilegaming": "мобилнигејминг", "lilysgarden": "гради<PERSON><PERSON><PERSON><PERSON><PERSON>или", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "тимскиборбенитактики", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "мистичнапорака", "callofdutymobile": "callofdutymobile", "thearcana": "арканата", "8ballpool": "8топкибилијард", "emergencyhq": "итнаслужба", "enstars": "енстарс", "randonautica": "рандонаутика", "maplestory": "maplestory", "albion": "албион", "hayday": "добровреме", "onmyoji": "онмјоџи", "azurlane": "azurlane", "shakesandfidget": "тресењеивртење", "ml": "мл", "bangdream": "бангдрим", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "временскапринцеза", "beatstar": "битстар", "dragonmanialegend": "dragonmanialegend", "hanabi": "хана<PERSON>и", "disneymirrorverse": "дизнимирорверс", "pocketlove": "џебнаљубов", "androidgames": "андроидигри", "criminalcase": "криминалистичкислучај", "summonerswar": "summonerswar", "cookingmadness": "готвењелудило", "dokkan": "dokkan", "aov": "aov", "triviacrack": "тривијакрек", "leagueofangels": "лигананаѓели", "lordsmobile": "господарисракаподвижен", "tinybirdgarden": "малаградиназазаптички", "gachalife": "gachalife", "neuralcloud": "невралнаоблак", "mysingingmonsters": "мојтепеачкичудовишта", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "сината_архива", "raidshadowlegends": "raidshadowlegends", "warrobots": "војнироботи", "mirrorverse": "огледалскивселена", "pou": "пау", "warwings": "војнокрили", "fifamobile": "fifamobile", "mobalegendbangbang": "мобалегендбангбанг", "evertale": "evertale", "futime": "забава", "antiyoy": "ан<PERSON><PERSON><PERSON><PERSON><PERSON>", "apexlegendmobile": "apexlegendmobile", "ingress": "влез", "slugitout": "борисе", "mpl": "мпл", "coinmaster": "coinmaster", "punishinggrayraven": "казнувачкосивогаврана", "petpals": "другаримилувчиња", "gameofsultans": "игранасултани", "arenabreakout": "излезвоарена", "wolfy": "волчи", "runcitygame": "трчиградскаигра", "juegodemovil": "мобилнаигра", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "мимикрија", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "магнатнаролеркостери", "grandchase": "гренд<PERSON>е<PERSON>с", "bombmebrasil": "бомбајмебразил", "ldoe": "ldoe", "legendonline": "легендаонлајн", "otomegame": "отомеигра", "mindustry": "mindustry", "callofdragons": "повикнадракони", "shiningnikki": "блескаваники", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "патдоникаде", "sealm": "сиилм", "shadowfight3": "борбавосенка3", "limbuscompany": "limbuscompany", "demolitionderby3": "дербинауништување3", "wordswithfriends2": "зборовисопријатели2", "soulknight": "душевит<PERSON>з", "purrfecttale": "пурфектнасторија", "showbyrock": "шоубајрок", "ladypopular": "дамапопуларна", "lolmobile": "лолмобилен", "harvesttown": "жетвенград", "perfectworldmobile": "совршенсветмобилен", "empiresandpuzzles": "империиипазли", "empirespuzzles": "империисложувалки", "dragoncity": "градотнаџмејот", "garticphone": "гартикфон", "battlegroundmobileind": "бојнополемобајлинд", "fanny": "газ", "littlenightmare": "малкошмар", "aethergazer": "етерскиџер", "mudrunner": "калливозач", "tearsofthemis": "солзинатемида", "eversoul": "вечнадуша", "gunbound": "ган<PERSON><PERSON><PERSON><PERSON>д", "gamingmlbb": "гејмингmlbb", "dbdmobile": "dbdmobile", "arknight": "аркнајт", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "зомбибродоломници", "eveechoes": "eveechoes", "jogocelular": "мобилнаигра", "mariokarttour": "мариокарттура", "zooba": "зооба", "mobilelegendbangbang": "мобајллегендбангбанг", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "мамаготви", "cabalmobile": "cabalmobile", "streetfighterduel": "улична_борба_дуел", "lesecretdhenri": "тајнатанаанри", "gamingbgmi": "гејмингbgmi", "girlsfrontline": "девојкипрвалинија", "jurassicworldalive": "jurassicworldalive", "soulseeker": "бар<PERSON><PERSON><PERSON>д<PERSON><PERSON>и", "gettingoverit": "преболувам", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "сторизалунскиот", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "мобилниигри", "legendofneverland": "легендазаникогашземја", "pubglite": "pubglite", "gamemobilelegends": "гејммобајллегендс", "timeraiders": "времеплачкачи", "gamingmobile": "мобилнигејминг", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "мачкитевоборба", "dnd": "недостапен", "quest": "квест", "giochidiruolo": "игринаулоги", "dnd5e": "днд5е", "rpgdemesa": "рпгнамаса", "worldofdarkness": "светнатемнина", "travellerttrpg": "патникдндигра", "2300ad": "2300година", "larp": "лар<PERSON>", "romanceclub": "клубназаромантика", "d20": "д20", "pokemongames": "покемонигри", "pokemonmysterydungeon": "покемонмистеријатамница", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "покемонкристал", "pokemonanime": "покемонаниме", "pokémongo": "pokémongo", "pokemonred": "покемонцрвена", "pokemongo": "pokemongo", "pokemonshowdown": "покемонборба", "pokemonranger": "покемонренџер", "lipeep": "усниглас", "porygon": "поригон", "pokemonunite": "pokemonunite", "entai": "ентаи", "hypno": "хипно", "empoleon": "емполеон", "arceus": "арце<PERSON>с", "mewtwo": "мјуту", "paldea": "палдеа", "pokemonscarlet": "покемонскарлет", "chatot": "четот", "pikachu": "пикачу", "roxie": "рокси", "pokemonviolet": "покемонвиолет", "pokemonpurpura": "покемонпурпура", "ashketchum": "ешкечум", "gengar": "генгар", "natu": "нату", "teamrocket": "тимракета", "furret": "фурет", "magikarp": "магика<PERSON>п", "mimikyu": "мими<PERSON><PERSON>у", "snorlax": "снорлакс", "pocketmonsters": "џебнисуштества", "nuzlocke": "нузлок", "pokemonplush": "покемонплишани", "teamystic": "тиммистик", "pokeball": "топчезапокемони", "charmander": "ча<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "литен", "shinypokemon": "сјајнипокемони", "mesprit": "меспрајт", "pokémoni": "покемони", "ironhands": "железнираце", "kabutops": "кабутопс", "psyduck": "пајдак", "umbreon": "умбреон", "pokevore": "покевора", "ptcg": "ptcg", "piplup": "пиплап", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "ејтипикачу", "pokémonmaster": "покемонмајстор", "pokémonsleep": "покемонспие", "kidsandpokemon": "децаипокемон", "pokemonsnap": "покемонснап", "bulbasaur": "бу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lucario": "lucario", "charizar": "шармира", "shinyhunter": "ловецнасветликави", "ajedrez": "шах", "catur": "цатур", "xadrez": "шах", "scacchi": "шах", "schaken": "шах", "skak": "скак", "ajedres": "шах", "chessgirls": "шахистки", "magnuscarlsen": "магнускарлсен", "worldblitz": "светскиблиц", "jeudéchecs": "шах", "japanesechess": "јапонскишах", "chinesechess": "кинескишах", "chesscanada": "шахканада", "fide": "фиде", "xadrezverbal": "шаховскизборови", "openings": "отворени_позиции", "rook": "топ", "chesscom": "chesscom", "calabozosydragones": "тамницииаждаи", "dungeonsanddragon": "змејовиитамници", "dungeonmaster": "господарнатемница", "tiamat": "tiamat", "donjonsetdragons": "донјонизмејови", "oxventure": "oxventure", "darksun": "темносонце", "thelegendofvoxmachina": "легендатазавоксмакина", "doungenoanddragons": "данџнздрагони", "darkmoor": "темнавресишта", "minecraftchampionship": "minecraftшампионат", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "мајнтест", "hypixel": "hypixel", "karmaland5": "карmaland5", "minecraftmods": "мајнкрафтмодови", "mcc": "мцц", "candleflame": "пламенодсвеќа", "fru": "фру", "addons": "додатоци", "mcpeaddons": "mcpeдодатоци", "skyblock": "скајблок", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "моди<PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "помеѓусветови", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "мајнкр<PERSON>фтград", "pcgamer": "pcге<PERSON><PERSON><PERSON>р", "jeuxvideo": "видеоигри", "gambit": "гамбит", "gamers": "гејмери", "levelup": "левелап", "gamermobile": "гејмермобилен", "gameover": "крајнаиграта", "gg": "гг", "pcgaming": "пцгејминг", "gamen": "гејмање", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "пцигри", "casualgaming": "гејмингнаодушка", "gamingsetup": "гејминг<PERSON>етап", "pcmasterrace": "pcmasterrace", "pcgame": "пцигра", "gamerboy": "гејмерб<PERSON>ј", "vrgaming": "врилирање", "drdisrespect": "дрдисреспект", "4kgaming": "4kгејминг", "gamerbr": "гејмер<PERSON>р", "gameplays": "геймплеј", "consoleplayer": "играчнаконзола", "boxi": "boxi", "pro": "про", "epicgamers": "епскигејмери", "onlinegaming": "онлајнгејминг", "semigamer": "полугејмер", "gamergirls": "гејмерки", "gamermoms": "гејмермајки", "gamerguy": "гејмер", "gamewatcher": "гејмерскиочи", "gameur": "гејмер", "grypc": "гррр", "rangugamer": "рангугејмер", "gamerschicas": "гејмерки", "otoge": "отоге", "dedsafio": "дедсафио", "teamtryhard": "тимштосемачи", "mallugaming": "mallugaming", "pawgers": "дебелгазери", "quests": "квестови", "alax": "алакс", "avgn": "avgn", "oldgamer": "старге<PERSON>мер", "cozygaming": "cozyгејминг", "gamelpay": "gamelpay", "juegosdepc": "игризапц", "dsswitch": "dsswitch", "competitivegaming": "компетитивногејмање", "minecraftnewjersey": "minecraftњуџерзи", "faker": "лажго", "pc4gamers": "pc4gejмери", "gamingff": "gamingff", "yatoro": "yatoro", "heterosexualgaming": "хетеросексуалногејмирање", "gamepc": "геjмингкомп", "girlsgamer": "гејмерки", "fnfmods": "fnfmodovi", "dailyquest": "дн<PERSON><PERSON>наквест", "gamegirl": "гејмерка", "chicasgamer": "девојкигејмерки", "gamesetup": "поставкананаигра", "overpowered": "преопсилен", "socialgamer": "социјалгејмер", "gamejam": "гејмџем", "proplayer": "професионалец", "roleplayer": "улогаиграч", "myteam": "мојтим", "republicofgamers": "републиканагејмери", "aorus": "aorus", "cougargaming": "кугарг<PERSON>јминг", "triplelegend": "тројналегенда", "gamerbuddies": "гејмерскидругари", "butuhcewekgamers": "требамигејмерка", "christiangamer": "христијанскигејмер", "gamernerd": "гејмернерд", "nerdgamer": "нердгејмер", "afk": "афк", "andregamer": "andregamer", "casualgamer": "повременигејмер", "89squad": "89екипа", "inicaramainnyagimana": "иницарамаиннјагимана", "insec": "несигурен", "gemers": "гејмери", "oyunizlemek": "oyunizlemek", "gamertag": "геjмертаг", "lanparty": "ланпарти", "videogamer": "гејмер", "wspólnegranie": "заедничкаигра", "mortdog": "mortdog", "playstationgamer": "playstationgamer", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "здравгејмер", "gtracing": "гттркање", "notebookgamer": "гејмерсобележник", "protogen": "протоген", "womangamer": "гејмерка", "obviouslyimagamer": "секакосумгејмер", "mario": "марио", "papermario": "папермарио", "mariogolf": "мариоголф", "samusaran": "самусаран", "forager": "соби<PERSON><PERSON><PERSON>", "humanfallflat": "човекпаѓарамно", "supernintendo": "супернинтендо", "nintendo64": "nintendo64", "zeroescape": "нулаизлез", "waluigi": "валуиџи", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "нинтендомузика", "sonicthehedgehog": "соникежот", "sonic": "соник", "fallguys": "fallguys", "switch": "префрли", "zelda": "зелда", "smashbros": "смешброс", "legendofzelda": "легендазаѕелда", "splatoon": "splatoon", "metroid": "метроид", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "мегамен", "majorasmask": "мајораскамаска", "mariokartmaster": "мариокартмајстор", "wii": "wii", "aceattorney": "тужителас", "ssbm": "ssbm", "skychildrenofthelight": "небескидецанасветлината", "tomodachilife": "tomodachilife", "ahatintime": "еденшеширвремето", "tearsofthekingdom": "солзинакралството", "walkingsimulators": "симулаториназаодење", "nintendogames": "нинтендоигри", "thelegendofzelda": "легендатаназелда", "dragonquest": "dragonquest", "harvestmoon": "жетвенамесечина", "mariobros": "мариобраќа", "runefactory": "runefactory", "banjokazooie": "банџоказуи", "celeste": "целесте", "breathofthewild": "зди<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myfriendpedro": "мојотпријателпедро", "legendsofzelda": "легендитеназелда", "donkeykong": "донкиконг", "mariokart": "мариокарт", "kirby": "кирби", "51games": "51игри", "earthbound": "земјотресен", "tales": "приказни", "raymanlegends": "рејменлегенди", "luigismansion": "луиџиевдом", "animalcrosssing": "animalcrossing", "taikonotatsujin": "тајконотацуџин", "nintendo3ds": "nintendo3ds", "supermariobros": "супермариобраќа", "mariomaker2": "марио_мејкер2", "boktai": "бок<PERSON>ј", "smashultimate": "smashultimate", "nintendochile": "nintendoчиле", "tloz": "tloz", "trianglestrategy": "стратегијанатриаголник", "supermariomaker": "супермариомејкер", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "супермарио64", "conkersbadfurday": "лошпетокзакестени", "nintendos": "нинтендоа", "new3ds": "new3ds", "donkeykongcountry2": "донкиконгкантри2", "hyrulewarriors": "ратни<PERSON><PERSON><PERSON>ха<PERSON>рул", "mariopartysuperstars": "марипартисуперѕвезди", "marioandsonic": "мариоисоник", "banjotooie": "банџотуи", "nintendogs": "нинтендокучиња", "thezelda": "thezelda", "palia": "палија", "marioandluigi": "мариоилуиџи", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "дивкорифт", "riven": "распарчен", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "арам", "cblol": "cblol", "leagueoflegendslas": "лиганалегендителас", "urgot": "ургот", "zyra": "зира", "redcanids": "црвенипсиња", "vanillalol": "ванилалол", "wildriftph": "wildriftmk", "lolph": "пукнавододсмеење", "leagueoflegend": "лиганалегендите", "tốcchiến": "брзинскабитка", "gragas": "грагас", "leagueoflegendswild": "лигананалегендитедивјаци", "adcarry": "адкери", "lolzinho": "лолзињо", "leagueoflegendsespaña": "leagueoflegendsшпанија", "aatrox": "aatrox", "euw": "еу", "leagueoflegendseuw": "лиганалегендитеeuw", "kayle": "кејл", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "лунари", "fnatic": "fnatic", "lollcs": "лолцс", "akshan": "а<PERSON><PERSON><PERSON><PERSON>", "milio": "мило", "shaco": "шако", "ligadaslegendas": "лигананалегендите", "gaminglol": "игрилол", "nasus": "насус", "teemo": "teemo", "zedmain": "зедмејн", "hexgates": "хексгејтови", "hextech": "хекстек", "fortnitegame": "fortnitegame", "gamingfortnite": "играњеfortnite", "fortnitebr": "фортнајтbr", "retrovideogames": "ретровидеоигри", "scaryvideogames": "страшнивидеоигри", "videogamemaker": "правителнавидеоигри", "megamanzero": "мегаменнула", "videogame": "видеоигра", "videosgame": "видеоигри", "professorlayton": "професорлејтон", "overwatch": "overwatch", "ow2": "ау2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "аркади", "acnh": "acnh", "puffpals": "пафпалс", "farmingsimulator": "симулаторназемјоделство", "robloxchile": "роблоксчиле", "roblox": "roblo<PERSON>", "robloxdeutschland": "роблоксгерманија", "robloxdeutsch": "роблоксгермански", "erlc": "erlc", "sanboxgames": "песочнициигри", "videogamelore": "видеоигрилегенди", "rollerdrome": "ролердром", "parasiteeve": "паразитева", "gamecube": "гејмкјуб", "starcraft2": "starcraft2", "duskwood": "мрачношуме", "dreamscape": "сонливпејзаж", "starcitizen": "ѕвездограѓанин", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "мртовпростор", "amordoce": "амордоце", "videogiochi": "видеоигри", "theoldrepublic": "старатарепублика", "videospiele": "видеоигри", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "приказниигри", "wolfenstein": "волфенштајн", "actionadventure": "акцијааваантура", "storyofseasons": "приказназасезоните", "retrogames": "ретроигри", "retroarcade": "ретроаркади", "vintagecomputing": "винтиџкомпјутери", "retrogaming": "ретроигри", "vintagegaming": "ретроигри", "playdate": "дружење", "commanderkeen": "командеркин", "bugsnax": "багзнекс", "injustice2": "неправда2", "shadowthehedgehog": "соникежот", "rayman": "рејмен", "skygame": "играсонебо", "zenlife": "зенживот", "beatmaniaiidx": "beatmaniaiidx", "steep": "стрмно", "mystgames": "mistgames", "blockchaingaming": "блокчејнгејминг", "medievil": "медиевил", "consolegaming": "конзолгејминг", "konsolen": "конзоли", "outrun": "избегај", "bloomingpanic": "паникацветна", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "хороригри", "monstergirlquest": "монстердевојкиквест", "supergiant": "супергигант", "disneydreamlightvalle": "дизнидримлајтвели", "farmingsims": "симулаторизафармерство", "juegosviejos": "старигејмови", "bethesda": "bethesda", "jackboxgames": "jackboxgames", "interactivefiction": "интерактивнафикција", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "љубовнициумови", "visualnovel": "визуеленроман", "visualnovels": "визуелниновели", "rgg": "rgg", "shadowolf": "сенкаволк", "tcrghost": "tcrghost", "payday": "платниден", "chatherine": "четеринг", "twilightprincess": "здрачнапринцеза", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "песочница", "aestheticgames": "естетскиигри", "novelavisual": "визуелнановела", "thecrew2": "екипата2", "alexkidd": "алекскид", "retrogame": "ретроигра", "tonyhawkproskater": "тонихокпрофесионаленскејтер", "smbz": "сдкт", "lamento": "ламенто", "godhand": "божјарака", "leafblowerrevolution": "револуцијанадувачилисја", "wiiu": "wiiu", "leveldesign": "дизајннаниво", "starrail": "starrail", "keyblade": "клуч<PERSON><PERSON>ч", "aplaguetale": "чумнаприказна", "fnafsometimes": "fnafsometimes", "novelasvisuales": "визуелниромани", "robloxbrasil": "роблоксбразил", "pacman": "пакмен", "gameretro": "геjмретро", "videojuejos": "видеоигри", "videogamedates": "видеоигридејтови", "mycandylove": "мојатаслаткаљубов", "megaten": "мегатен", "mortalkombat11": "мортал_комбат_11", "everskies": "everskies", "justcause3": "простотака3", "hulkgames": "игрисохалк", "batmangames": "бетменигри", "returnofreckoning": "враќањенаодмаздата", "gamstergaming": "гејмстергејминг", "dayofthetantacle": "денотнаоктоподот", "maniacmansion": "лудатакуќа", "crashracing": "тркасудирање", "3dplatformers": "3дплатформери", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "старашкола", "hellblade": "пеколнотострило", "storygames": "игринаприказни", "bioware": "bioware", "residentevil6": "резидентевил6", "soundodger": "избегнувачназвук", "beyondtwosouls": "преудведуши", "gameuse": "играње", "offmortisghost": "offmortisghost", "tinybunny": "малозајче", "retroarch": "retroarch", "powerup": "засили", "katanazero": "катаназеро", "famicom": "фамиком", "aventurasgraficas": "графичкиавантури", "quickflash": "брзблиц", "fzero": "fzero", "gachagaming": "гачагејминг", "retroarcades": "ретроаркади", "f123": "f123", "wasteland": "пустина", "powerwashsim": "симулаторзачистењесопритисок", "coralisland": "кора<PERSON><PERSON>н<PERSON>стров", "syberia3": "syberia3", "grymmorpg": "суроворгп", "bloxfruit": "bloxfruit", "anotherworld": "другсвет", "metaquest": "metaquest", "animewarrios2": "анимевоини2", "footballfusion": "фудбалскафузија", "edithdlc": "edithdlc", "abzu": "абзу", "astroneer": "астронир", "legomarvel": "легомарвел", "wranduin": "врандуин", "twistedmetal": "исукановоѓе", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "купиштесрам", "simulator": "симулатор", "symulatory": "симулатори", "speedrunner": "спидранер", "epicx": "епскиx", "superrobottaisen": "суперроботскибитки", "dcuo": "dcuo", "samandmax": "самимакс", "grywideo": "грајвидео", "gaiaonline": "gaiaonline", "korkuoyunu": "хороригра", "wonderlandonline": "чудесназемјаонлајн", "skylander": "скајландер", "boyfrienddungeon": "фантдечко", "toontownrewritten": "too<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "симр<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simrace": "симтрка", "pvp": "пвп", "urbanchaos": "урбанхаос", "heavenlybodies": "божественителаа", "seum": "срам", "partyvideogames": "забавнивидеоигри", "graveyardkeeper": "гробар", "spaceflightsimulator": "симулаторнавселенскилет", "legacyofkain": "наследствотонакаин", "hackandslash": "сечиисечи", "foodandvideogames": "хранаивидеоигри", "oyunvideoları": "видеаодигри", "thewolfamongus": "волкотмеѓунас", "truckingsimulator": "симулаторнакамиони", "horizonworlds": "хоризонсветови", "handygame": "играчканарака", "leyendasyvideojuegos": "легендиивидеоигри", "oldschoolvideogames": "старишколскивидеоигри", "racingsimulator": "симулаторзатркање", "beemov": "bee<PERSON>v", "agentsofmayhem": "агенти<PERSON><PERSON><PERSON><PERSON>с", "songpop": "песнипоп", "famitsu": "famitsu", "gatesofolympus": "портитенаолимп", "monsterhunternow": "monsterhunternow", "rebelstar": "бунтовничказвезда", "indievideogaming": "индивидеоигри", "indiegaming": "индигејминг", "indievideogames": "индивидеоигри", "indievideogame": "инди_видео_игра", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "спајдерменинсомнијак", "bufffortress": "мускулскатврдина", "unbeatable": "непобедлив", "projectl": "проектл", "futureclubgames": "игринаклубнаиднината", "mugman": "шолја", "insomniacgames": "игризабудниочи", "supergiantgames": "супергигантигри", "henrystickman": "хенристикмен", "henrystickmin": "хенристикмин", "celestegame": "celestegame", "aperturescience": "наукасоотвори", "backlog": "залостаност", "gamebacklog": "игринакуп", "gamingbacklog": "неодигранигејмови", "personnagejeuxvidéos": "ликовиодвидеоигри", "achievementhunter": "ловецнадостигнувања", "cityskylines": "градскихоризонти", "supermonkeyball": "супермајмунскатопка", "deponia": "депонија", "naughtydog": "палавокуче", "beastlord": "ѕверовладетел", "juegosretro": "ретроигри", "kentuckyroutezero": "кентакирутанула", "oriandtheblindforest": "ориислепаташума", "alanwake": "alanwake", "stanleyparable": "стенлипарабола", "reservatoriodedopamin": "резервоарнадопамин", "staxel": "staxel", "videogameost": "видеоигрисаундтрак", "dragonsync": "дракон<PERSON>инк", "vivapiñata": "вивапињата", "ilovekofxv": "саkамkofxv", "arcanum": "арканум", "neoy2k": "неоу2к", "pcracing": "pcracing", "berserk": "берзерк", "baki": "баки", "sailormoon": "сејлормун", "saintseiya": "сејнтсеија", "inuyasha": "инујаша", "yuyuhakusho": "јујухакушо", "initiald": "почетнод", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "тажноаниме", "darkerthanblack": "поцрноодцрно", "animescaling": "анимескалирање", "animewithplot": "анимесодејство", "pesci": "песи", "retroanime": "ретроаниме", "animes": "аниме", "supersentai": "суперсентаи", "samuraichamploo": "самурајшамплу", "madoka": "мадока", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "80тианиме", "90sanime": "90тианиме", "darklord": "темновладетел", "popeetheperformer": "папаизведувач", "masterpogi": "мајстор<PERSON><PERSON><PERSON>в<PERSON>ц", "samuraix": "самура<PERSON>x", "dbgt": "дбгт", "veranime": "верааниме", "2000sanime": "2000аниме", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "дрстоунпрвасезона", "rapanime": "репаниме", "chargemanken": "полначкен", "animecover": "анимекорица", "thevisionofescaflowne": "визијатанаескафлоун", "slayers": "убијци", "tokyomajin": "токиомаџин", "anime90s": "аниме90ти", "animcharlotte": "ани<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т", "gantz": "gantz", "shoujo": "шуџо", "bananafish": "бананар<PERSON>ба", "jujutsukaisen": "јујуцукаисен", "jjk": "jjk", "haikyu": "хаи<PERSON>у", "toiletboundhanakokun": "тоалетскиотханакокун", "bnha": "bnha", "hellsing": "хе<PERSON><PERSON><PERSON><PERSON><PERSON>", "skipbeatmanga": "skipbeat<PERSON><PERSON>н<PERSON>а", "vanitas": "суета", "fireforce": "огненасила", "moriartythepatriot": "моријартипатриотот", "futurediary": "иднинскидневник", "fairytail": "бајка", "dorohedoro": "дорохедоро", "vinlandsaga": "винландсага", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "паразит", "punpun": "пунпун", "shingekinokyojin": "shingekinokyojin", "mushishi": "мушиши", "beastars": "бист<PERSON><PERSON><PERSON>", "vanitasnocarte": "ванитаснокарте", "mermaidmelody": "сирена_мелодија", "kamisamakiss": "бакнежоднекамисама", "blmanga": "blманга", "horrormanga": "хорорманга", "romancemangas": "романтичнимангас", "karneval": "карневал", "dragonmaid": "змејскаслужавка", "blacklagoon": "црнатолагуна", "kentaromiura": "кентаромиура", "mobpsycho100": "мобпсихо100", "terraformars": "терафоријанци", "geniusinc": "гени<PERSON><PERSON><PERSON><PERSON><PERSON>", "shamanking": "шам<PERSON>н<PERSON><PERSON>нг", "kurokonobasket": "курокоѕбаскет", "jugo": "југо", "bungostraydogs": "бунгострејдогс", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "џуџуцу", "yurionice": "jур<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "acertainmagicalindex": "некојволшебенинтекс", "sao": "сао", "blackclover": "црнадетелина", "tokyoghoul": "токиогул", "onepunchman": "еденудар", "hetalia": "хеталија", "kagerouproject": "проекткагеру", "haikyuu": "хајк<PERSON>у", "toaru": "тоару", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8безграници", "siriusthejaeger": "сириусловецот", "spyxfamily": "шпионxсемејство", "rezero": "rezero", "swordartonline": "сордартонлајн", "dororo": "дороро", "wondereggpriority": "wondereggpriority", "angelsofdeath": "ангелинасмртта", "kakeguri": "какегури", "dragonballsuper": "драгонболсупер", "hypnosismic": "хипнозамик", "goldenkamuy": "зла<PERSON><PERSON>нкамуј", "monstermusume": "монстердевојка", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "спортскианиме", "sukasuka": "сукасука", "arwinsgame": "играатанаарвин", "angelbeats": "ангелскиудари", "isekaianime": "исекаианиме", "sagaoftanyatheevil": "сагазатањазлата", "shounenanime": "shounenанимиња", "bandori": "бандори", "tanya": "тања", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "преткјур", "theboyandthebeast": "момчетоиѕверот", "fistofthenorthstar": "јуџекеннонебото", "mazinger": "мазин<PERSON><PERSON>р", "blackbuttler": "црнбатлер", "towerofgod": "кула<PERSON><PERSON><PERSON>богот", "elfenlied": "елфенлид", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "чиби", "servamp": "сервамп", "howtokeepamummy": "какодасечувамумија", "fullmoonwosagashite": "полнамесечинабарам", "shugochara": "шугочара", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "гугурекоккурисан", "cuteandcreepy": "сладокилутенко", "martialpeak": "борбенв<PERSON>в", "bakihanma": "бакиханма", "hiscoregirl": "hiscoregirl", "orochimaru": "orочимару", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "да<PERSON>и", "johnconstantine": "џонконстантин", "astolfo": "astolfo", "revanantfae": "духвила", "shinji": "шинџи", "zerotwo": "нулдва", "inosuke": "иносуке", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "девојкачудовиште", "kanae": "канае", "yone": "yone", "mitsuki": "мицуки", "kakashi": "какаши", "lenore": "ленор", "benimaru": "бенимару", "saitama": "сајтама", "sanji": "sanji", "bakugo": "бакуго", "griffith": "грифит", "ririn": "рир<PERSON>н", "korra": "korra", "vanny": "вени", "vegeta": "вегета", "goromi": "горами", "luci": "луци", "reigen": "рајген", "scaramouche": "скарамуш", "amiti": "амити", "sailorsaturn": "морнарсатурн", "dio": "дио", "sailorpluto": "sailorpluto", "aloy": "aloj", "runa": "руна", "oldanime": "старианиме", "chainsawman": "chainsawman", "bungoustraydogs": "бангустрејдогс", "jogo": "jogo", "franziska": "францiska", "nekomimi": "некомими", "inumimi": "<PERSON><PERSON>mi", "isekai": "исекаи", "tokyorevengers": "токиоревенџерс", "blackbutler": "црнбатлер", "ergoproxy": "ергопрокси", "claymore": "клејмор", "loli": "лоли", "horroranime": "хорораниме", "fruitsbasket": "кошничкасоовошје", "devilmancrybaby": "деволскиплачибебе", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "мангасвободна", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "сеинен", "lovelive": "љубоввживо", "sakuracardcaptor": "сакуракардкаптор", "umibenoetranger": "умибеноетранже", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "ветенатаземјаникогаш", "monstermanga": "монстерманга", "yourlieinapril": "твојатаапrilскалага", "buggytheclown": "бажиклоунот", "bokunohero": "бокунохеро", "seraphoftheend": "серафнакрајот", "trigun": "trigun", "cyborg009": "киборг009", "magi": "маги", "deepseaprisoner": "затвореникнадлабочина", "jojolion": "џоџолион", "deadmanwonderland": "мртовецчудесназемја", "bannafish": "бананар<PERSON>ба", "sukuna": "сукуна", "darwinsgame": "дарвиноваигра", "husbu": "сопруг", "sugurugeto": "сугуругето", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "санзу", "sarazanmai": "сарадзанмаи", "pandorahearts": "пандориносрца", "yoimiya": "јоимија", "foodwars": "војназахрана", "cardcaptorsakura": "картофаќачкасакура", "stolas": "столас", "devilsline": "ѓаволскалинија", "toyoureternity": "дозасекогаш", "infpanime": "инфпаниме", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "сина_периода", "griffithberserk": "грифитберсерк", "shinigami": "шинигами", "secretalliance": "тајн<PERSON><PERSON><PERSON><PERSON>з", "mirainikki": "мир<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "mahoutsukainoyome": "маги<PERSON>натаневеста", "yuki": "јуки", "erased": "избришана", "bluelock": "синака<PERSON><PERSON><PERSON><PERSON><PERSON>", "goblinslayer": "убиецнадуховци", "detectiveconan": "детективконан", "shiki": "шики", "deku": "деку", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "витезвампир", "mugi": "муги", "blueexorcist": "синегзорцист", "slamdunk": "погодокодкош", "zatchbell": "заџбел", "mashle": "машле", "scryed": "изгледан", "spyfamily": "шпионскосемејство", "airgear": "воздушнатрансмисија", "magicalgirl": "волшебнадевојка", "thesevendeadlysins": "седумтесмртнигрева", "prisonschool": "затворскоучилиште", "thegodofhighschool": "богнагимназија", "kissxsis": "сестрабаци", "grandblue": "grandblue", "mydressupdarling": "мојатакуклазаоблекување", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "анимеуниверзум", "swordartonlineabridge": "сордартонлајнмост", "saoabridged": "сао_скратено", "hoshizora": "хошизора", "dragonballgt": "dragonballgt", "bocchitherock": "бочитерок", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "мобпсихо100", "hajimenoippo": "hajimenoippo", "undeadunluck": "несреќнинемртви", "romancemanga": "романтичнаманга", "blmanhwa": "blманхва", "kimetsunoyaba": "kimetsunoyaba", "kohai": "кохаи", "animeromance": "анимер<PERSON>мansa", "senpai": "сенпај", "blmanhwas": "блманви", "animeargentina": "анимеаргентина", "lolicon": "лоликон", "demonslayertothesword": "demonslayerдомечот", "bloodlad": "крвавиот_господар", "goodbyeeri": "збогумери", "firepunch": "огненудар", "adioseri": "чаосери", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "кини<PERSON>у<PERSON>ан", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "ѕвездитесенаредија", "romanceanime": "романтичнианиме", "tsundere": "цундере", "yandere": "јандере", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "кенга<PERSON>шура", "saointegralfactor": "saoинтегралфактор", "cherrymagic": "волшебствонацреши", "housekinokuni": "домашнокинокралство", "recordragnarok": "снимирагнарок", "oyasumipunpun": "oyasumipunpun", "meliodas": "меливдас", "fudanshi": "фудан<PERSON>и", "retromanga": "ретроманга", "highschoolofthedead": "гимназијанамртвите", "germantechno": "германскотехно", "oshinoko": "осхиноко", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "виндландсага", "mangaka": "мангака", "dbsuper": "д<PERSON><PERSON><PERSON><PERSON>ер", "princeoftennis": "принцоттенис", "tonikawa": "премногу", "esdeath": "esdeath", "dokurachan": "докурачан", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "класанаубијци", "animemanga": "анимеманга", "bakuman": "бакуман", "deathparade": "смртнапарада", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "јапонскианиме", "animespace": "анимепростор", "girlsundpanzer": "девојкиипанцери", "akb0048": "akb0048", "hopeanuoli": "надежданаоли", "animedub": "аниме<PERSON>инхронизација", "animanga": "анимеманга", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "носителнаuq", "indieanime": "индианиме", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "gundam0": "gundam0", "animescifi": "ани<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ј", "ratman": "ста<PERSON><PERSON><PERSON><PERSON>", "haremanime": "хареманиме", "kochikame": "кочикаме", "nekoboy": "некомомче", "gashbell": "гашбел", "peachgirl": "праскадевојка", "cavalieridellozodiaco": "кавалериделлозодиако", "mechamusume": "механичкадевојка", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "јар<PERSON><PERSON><PERSON><PERSON>б<PERSON>чклуб", "dragonquestdai": "dragonquestdai", "heartofmanga": "срцетонамангата", "deliciousindungeon": "вкусновоподземје", "manhviyaoi": "манвијаои", "recordofragnarok": "записнарагнарок", "funamusea": "забавенсоцијал", "hiranotokagiura": "хиранотокагиура", "mangaanime": "мангааниме", "bochitherock": "бочирок", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "скокнидолофери", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "туторијалотпретежок", "overgeared": "преопремен", "toriko": "торико", "ravemaster": "рејвмајстор", "kkondae": "kkondae", "chobits": "чобитс", "witchhatatelier": "вештерскоателје", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "многусонцелав", "kamen": "каменот", "mangaislife": "манга<PERSON>живот", "dropsofgod": "капкинабог", "loscaballerosdelzodia": "витезовиназодијакот", "animeshojo": "анимедевојка", "reverseharem": "обратен<PERSON>а<PERSON>ем", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "одличниотучителонизука", "gridman": "гридмен", "kokorone": "кокороне", "soldato": "солдато", "mybossdaddy": "мојшефтатко", "gear5": "гир5", "grandbluedreaming": "големосинотосонување", "bloodplus": "крвплус", "bloodplusanime": "bloodplusаниме", "bloodcanime": "крваниме", "bloodc": "крвц", "talesofdemonsandgods": "приказнизадемонииботови", "goreanime": "горианиме", "animegirls": "анимедевојки", "sharingan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crowsxworst": "вранисупернајлоши", "splatteranime": "сплатераниме", "splatter": "прскотница", "risingoftheshieldhero": "кревањетонаштитоносниотхерој", "somalianime": "сомалискианиме", "riodejaneiroanime": "риодежанеироаниме", "slimedattaken": "сајтаиттуна", "animeyuri": "анимејури", "animeespaña": "анимешпанија", "animeciudadreal": "анимециудадреал", "murim": "мурим", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "децатанакитовите", "liarliar": "лажголажго", "supercampeones": "суперкампиони", "animeidols": "анимеидоли", "isekaiwasmartphone": "исекаисопаметентелефон", "midorinohibi": "зеленидни", "magicalgirls": "волшебнидевојки", "callofthenight": "повикнаноќта", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "бакуганборци", "natsuki": "натсуки", "mahoushoujo": "магичнадевојка", "shadowgarden": "сенкаградина", "tsubasachronicle": "цубасахроники", "findermanga": "најдимангаaта", "princessjellyfish": "принцезамедуза", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "рајскибакнеж", "kurochan": "куроќан", "revuestarlight": "ревјустар<PERSON><PERSON><PERSON>т", "animeverse": "анимеверзум", "persocoms": "персокомови", "omniscientreadersview": "сезнаечкичитателскивидик", "animecat": "анимемаче", "animerecommendations": "анимепрепораки", "openinganime": "отварањеаниме", "shinichirowatanabe": "шиничироватанабе", "uzumaki": "узумаки", "myteenromanticcomedy": "мојататинејџерскаромантичнакомедија", "evangelion": "евангелион", "gundam": "гандам", "macross": "макр<PERSON>с", "gundams": "гандами", "voltesv": "волтесв", "giantrobots": "џиновскироботи", "neongenesisevangelion": "неонгенезисевангелион", "codegeass": "кодгеас", "mobilefighterggundam": "мобилнифајтергандам", "neonevangelion": "неоневангелион", "mobilesuitgundam": "мобилниоклопигандам", "mech": "механ<PERSON><PERSON>ар", "eurekaseven": "еурекаседум", "eureka7": "еурека7", "thebigoanime": "thebigoanime", "bleach": "белило", "deathnote": "дедноут", "cowboybebop": "каубојбибоп", "jjba": "jjba", "jojosbizarreadventure": "џоџонабизарнатаавантура", "fullmetalalchemist": "фулметалалхемичар", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "џоџобизарниавантури", "kamuiyato": "камујјато", "militaryanime": "военианиме", "greenranger": "зеленренџер", "jimmykudo": "џимикудо", "tokyorev": "токиорев", "zorro": "зоро", "leonscottkennedy": "leonscottkennedy", "korosensei": "коросенсеи", "starfox": "starfox", "ultraman": "ултрамен", "salondelmanga": "салондемангата", "lupinthe3rd": "лупентретиот", "animecity": "анимеград", "animetamil": "аниметамил", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "наруто", "narutoshippuden": "наруто", "onepiece": "еднопарче", "animeonepiece": "анимеонепис", "dbz": "dbz", "dragonball": "драгонбол", "yugioh": "југио", "digimon": "дигимон", "digimonadventure": "дигимонавантура", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "гоку", "broly": "броли", "shonenanime": "шоненаниме", "bokunoheroacademia": "мојатаакадемијазахерои", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "дрстоун", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "отака", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "демонубиец", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "нападнатитани", "erenyeager": "еренјегер", "myheroacademia": "мојатаакадемијазахерои", "boruto": "боруто", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "tomodachigame", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "анкетенкорпус", "onepieceanime": "онепарчеаниме", "attaquedestitans": "нападнатитани", "theonepieceisreal": "theonepieceисправистина", "revengers": "осветници", "mobpsycho": "мобпсихо", "aonoexorcist": "аононегзорцист", "joyboyeffect": "јојбојефект", "digimonstory": "дигимонприказна", "digimontamers": "дигимонтејмери", "superjail": "суперзатвор", "metalocalypse": "металокалипса", "shinchan": "шин<PERSON><PERSON>н", "watamote": "watamote", "uramichioniisan": "урамичионисан", "uruseiyatsura": "уруселјацура", "gintama": "гинтама", "ranma": "ранма", "doraemon": "дораемон", "gto": "гто", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "беспрекоренвебтун", "kemonofriends": "кемонопријатели", "utanoprincesama": "utanoprincesama", "animecom": "анимеком", "bobobobobobobo": "бобобобобобобо", "yuukiyuuna": "јукијуна", "nichijou": "ничиј<PERSON>у", "yurucamp": "јурукамп", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "вештерканаметла", "wotakoi": "вотакои", "konanime": "конананиме", "clannad": "кла<PERSON><PERSON>", "justbecause": "самотака", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "сеедневницитеулици", "recuentosdelavida": "раскажувањаодживотот"}