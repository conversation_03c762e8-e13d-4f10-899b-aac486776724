{"2048": "2048", "mbti": "mbti", "enneagram": "эннеаграмма", "astrology": "ситорашиносӣ", "cognitivefunctions": "функсияҳоикогнитивӣ", "psychology": "психология", "philosophy": "фалсафа", "history": "таърих", "physics": "физика", "science": "илм", "culture": "фарҳанг", "languages": "забонҳо", "technology": "технология", "memes": "мемҳо", "mbtimemes": "mbtimemes", "astrologymemes": "мемҳоидарбораиситорашиносӣ", "enneagrammemes": "мемҳоиэннеаграмма", "showerthoughts": "фикрҳоидуш", "funny": "х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videos": "видеоҳо", "gadgets": "гаджетҳо", "politics": "сиёс<PERSON>т", "relationshipadvice": "нусҳатҳоиошиқӣ", "lifeadvice": "нигоҳдорӣ", "crypto": "крипто", "news": "хабарҳо", "worldnews": "ахбориҷаҳон", "archaeology": "археология", "learning": "омӯзиш", "debates": "баҳсҳо", "conspiracytheories": "назарияҳоимуҳофиза", "universe": "коинот", "meditation": "медитатсия", "mythology": "мифология", "art": "санъат", "crafts": "ҳунарҳоидастӣ", "dance": "рақс", "design": "ди<PERSON><PERSON><PERSON>н", "makeup": "макияж", "beauty": "зебоӣ", "fashion": "мода", "singing": "суруд", "writing": "навиштан", "photography": "аксбардорӣ", "cosplay": "косплей", "painting": "наққошӣ", "drawing": "расмкашӣ", "books": "китобҳо", "movies": "филмҳо", "poetry": "шеър", "television": "телевизион", "filmmaking": "филмсозӣ", "animation": "аниматсия", "anime": "аниме", "scifi": "илмӣтахайюлӣ", "fantasy": "фантазия", "documentaries": "ҳуҷҷатӣ", "mystery": "сирр", "comedy": "ҳазлушӯхӣ", "crime": "ҷиноят", "drama": "драма", "bollywood": "болливуд", "kdrama": "кдрама", "horror": "даҳшат", "romance": "ишқ", "realitytv": "телевизионивоқеӣ", "action": "амал", "music": "мусиқӣ", "blues": "ғамгинӣ", "classical": "классикӣ", "country": "кишвар", "desi": "деси", "edm": "edm", "electronic": "электронӣ", "folk": "фольклор", "funk": "фанк", "hiphop": "хипхоп", "house": "хона", "indie": "инди", "jazz": "ҷаз", "kpop": "kpop", "latin": "latin", "metal": "метал", "pop": "поп", "punk": "панк", "rnb": "рнб", "rap": "рэп", "reggae": "регги", "rock": "рок", "techno": "техно", "travel": "сафар", "concerts": "консертҳо", "festivals": "фестивалҳо", "museums": "осорхонаҳо", "standup": "рост_шав", "theater": "театр", "outdoors": "берунахона", "gardening": "боғдорӣ", "partying": "базмгирӣ", "gaming": "гейминг", "boardgames": "бозиҳоирӯимизӣ", "dungeonsanddragons": "подземельяидраконы", "chess": "шоҳмот", "fortnite": "фортнайт", "leagueoflegends": "лигаиафсонавӣ", "starcraft": "starcraft", "minecraft": "майнкра<PERSON>т", "pokemon": "покемон", "food": "хӯрок", "baking": "нонпазӣ", "cooking": "ошпазӣ", "vegetarian": "вегетариан", "vegan": "вегетариан", "birds": "паррандаҳо", "cats": "гурбаҳо", "dogs": "саг", "fish": "балиқ", "animals": "ҳайвонот", "blacklivesmatter": "ҳаётисиёҳпӯстонмуҳиманд", "environmentalism": "экология", "feminism": "феминизм", "humanrights": "ҳуқуқиинсон", "lgbtqally": "дӯстилгбтқ", "stopasianhate": "авқотибаднисбатиосиёиҳо", "transally": "дӯстидонсеров", "volunteering": "волонтёрӣ", "sports": "варзиш", "badminton": "бадминтон", "baseball": "бейсбол", "basketball": "баскетбол", "boxing": "бокс", "cricket": "крикет", "cycling": "велосипедронӣ", "fitness": "фитнес", "football": "футбол", "golf": "голф", "gym": "варзишгоҳ", "gymnastics": "гимнастика", "hockey": "хоккей", "martialarts": "ҳунарҳоиҷангӣ", "netball": "нетбол", "pilates": "пилатес", "pingpong": "пингпонг", "running": "дави<PERSON>ан", "skateboarding": "скейтбординг", "skiing": "лижаронӣ", "snowboarding": "сноубординг", "surfing": "серфинг", "swimming": "шиноварӣ", "tennis": "теннис", "volleyball": "волейбол", "weightlifting": "бодиби<PERSON><PERSON><PERSON><PERSON>г", "yoga": "йога", "scubadiving": "ғаввосӣ", "hiking": "кӯҳнавардӣ", "capricorn": "ҷадӣ", "aquarius": "да<PERSON><PERSON>", "pisces": "ҳут", "aries": "овен", "taurus": "гов", "gemini": "gemini", "cancer": "саратон", "leo": "шер", "virgo": "бурҷисунбула", "libra": "тарозу", "scorpio": "скорпион", "sagittarius": "стрелец", "shortterm": "кӯтоҳмуддат", "casual": "оддӣ", "longtermrelationship": "дарозмуддат", "single": "муҷаррад", "polyamory": "полиамория", "enm": "enm", "lgbt": "лгбт", "lgbtq": "лгбтқ", "gay": "гей", "lesbian": "лесбиянка", "bisexual": "бисексуал", "pansexual": "пансексуал", "asexual": "асексуал", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "данганронпа", "deltarune": "deltarune", "watchdogs": "сагҳоинигаҳбон", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "подшоҳӣ", "soulreaver": "рӯҳдузд", "suikoden": "суйкоден", "subverse": "субверс", "legendofspyro": "афсонаиспиро", "rouguelikes": "рогулайкҳо", "syberia": "сибир", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "ғурубдарпеш", "arkham": "архам", "deusex": "деусекс", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "устувор", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "ҷаҳониозод", "heroesofthestorm": "қаҳрамонониборон", "cytus": "cytus", "soulslike": "рӯҳмонанд", "dungeoncrawling": "ғорнишинӣ", "jetsetradio": "jetsetradio", "tribesofmidgard": "қабилаҳоимидгард", "planescape": "planescape", "lordsoftherealm2": "лордҳоиолам2", "baldursgate": "балдурсгейт", "colorvore": "рангхӯр", "medabots": "медаботҳо", "lodsoftherealm2": "султонҳоиолам2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "симҳоифарогир", "okage": "окаге", "juegoderol": "бозиинақш", "witcher": "ведьмак", "dishonored": "беобрӯшуда", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "котор", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "скайрим", "elderscrolls": "elderscrolls", "modding": "моддинг", "charactercreation": "сохтаниперсонаж", "immersive": "фаромӯшкунанда", "falloutnewvegas": "falloutnewvegas", "bioshock": "биошок", "omori": "omori", "finalfantasyoldschool": "файналфантасикӯҳна", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "финалфантази", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "файналфантази14", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "лала<PERSON>ел", "dissidia": "дисидия", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "рӯҳияимарговар", "finalfantasyvii": "файналфэнтезивii", "ff8": "ff8", "otome": "отоме", "suckerforlove": "ишқбоз", "otomegames": "бозиҳоиотоме", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "окаринаивақт", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "заминҳоигаз", "pathfinder": "роҳёб", "pathfinder2ndedition": "pathfinder2юм<PERSON>шр", "shadowrun": "shadowrun", "bloodontheclocktower": "қонбарсоатибурҷӣ", "finalfantasy15": "файналфантази15", "finalfantasy11": "фантазияихотима11", "finalfantasy8": "файналфантази8", "ffxvi": "ffxvi", "lovenikki": "лавникки", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "ҳуҷумигравитатсия", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "якбора", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "ҳоким", "yourturntodie": "навбатимурданитуст", "persona3": "persona3", "rpghorror": "rpgдаҳшат", "elderscrollsonline": "элдерскроллсонлайн", "reka": "река", "honkai": "хонкай", "marauders": "ғоратгарон", "shinmegamitensei": "шинмегамитенсей", "epicseven": "epicseven", "rpgtext": "рпгматн", "genshin": "ген<PERSON>ин", "eso": "эсо", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "морровинд", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "му", "falloutshelter": "паноҳгоҳиатомӣ", "gurps": "gurps", "darkestdungeon": "зиндони<PERSON><PERSON><PERSON><PERSON>н", "eclipsephase": "фазаиэклипс", "disgaea": "disgaea", "outerworlds": "ҷаҳонҳоиберунӣ", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "ҷанговаронисулола", "skullgirls": "skullgirls", "nightcity": "шаҳришабона", "hogwartslegacy": "мероситҳогвартс", "madnesscombat": "ҷангимаҷнунона", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelike", "gothamknights": "рыцарыготэма", "forgottenrealms": "олами_фаромушшуда", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "орнарпг", "toontown": "мультшаҳр", "childoflight": "фарзандин<PERSON>р", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "ҷаҳонидигимон", "monsterrancher": "монстрпарварӣ", "ecopunk": "экопанк", "vermintide2": "vermintide2", "xeno": "ксено", "vulcanverse": "ҷаҳонивулкан", "fracturedthrones": "тахтҳоишикаста", "horizonforbiddenwest": "ҳоризонфорбидденвест", "twewy": "twewy", "shadowpunk": "сояпанк", "finalfantasyxv": "фаиналфантазӣ15", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "сирриҳогвартс", "deltagreen": "де<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diablo": "диабло", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "зарба", "lastepoch": "охиринэпоха", "starfinder": "ситор<PERSON><PERSON>б", "goldensun": "офтобизарин", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "тегҳодардорикӣ", "twilight2000": "шафақ2000", "sandevistan": "сандевистан", "cyberpunk": "киб<PERSON><PERSON><PERSON>анк", "cyberpunk2077": "киберпанк2077", "cyberpunkred": "киберпанкисурх", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "тартиботифтода", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "заминҳоибад", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "шайтонзиндамонда", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "аниме5е", "divinity": "илоҳият", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "ғамҳоиҷаҳоникӯҳна", "adventurequest": "саёҳатиҷустуҷӯ", "dagorhir": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayingames": "бозиҳоинақшофарӣ", "roleplayinggames": "бозиҳоинақшофарӣ", "finalfantasy9": "файналфэнтези9", "sunhaven": "sunhaven", "talesofsymphonia": "қиссаҳоисимфония", "honkaistarrail": "honkaistarrail", "wolong": "волонг", "finalfantasy13": "finalfantasy13", "daggerfall": "даггерфол", "torncity": "шаҳрипора", "myfarog": "манваонбадани", "sacredunderworld": "ҷаҳонипоёниимуқаддас", "chainedechoes": "садоҳоизанҷирӣ", "darksoul": "рӯҳиторик", "soulslikes": "рӯҳмонандҳо", "othercide": "дигаркушӣ", "mountandblade": "mountandblade", "inazumaeleven": "инадзумаиёздаҳ", "acvalhalla": "acvalhalla", "chronotrigger": "хронотриггер", "pillarsofeternity": "рукнҳоиҷовидонӣ", "palladiumrpg": "palladiumrpg", "rifts": "ҷудоиҳо", "tibia": "қаламчаипой", "thedivision": "дивизия", "hellocharlotte": "саломшарлотта", "legendofdragoon": "афсонаидрагун", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "вампиригримпӯш", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "гургиапокалипсис", "aveyond": "aveyond", "littlewood": "хурдчӯб", "childrenofmorta": "фарзандонимурда", "engineheart": "дилимотор", "fable3": "қиссаи3", "fablethelostchapter": "афсонаибобигумшуда", "hiveswap": "hiveswap", "rollenspiel": "рольовая_игра", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "абадиятиадан", "finalfantasy16": "файналфантази16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "майдониситорагон", "oldschoolrevival": "эҳёиқадима", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "моркборг", "savageworlds": "ҷаҳонҳоиваҳшӣ", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "дилиқироли1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "зиндонизулмот", "juegosrpg": "бозиҳоиrpg", "kingdomhearts": "қалбҳоишоҳигарӣ", "kingdomheart3": "kingdomheart3", "finalfantasy6": "финалфантази6", "ffvi": "ffvi", "clanmalkavian": "кланмалкавиан", "harvestella": "ҳосилпарварӣ", "gloomhaven": "глумҳейвен", "wildhearts": "дилҳоишӯх", "bastion": "қалъа", "drakarochdemoner": "дракарочдемонер", "skiesofarcadia": "осмонҳоиаркадия", "shadowhearts": "дилҳоисоя", "nierreplicant": "nierreplicant", "gnosia": "гносия", "pennyblood": "хунипул", "breathoffire4": "нафасиоташ4", "mother3": "модар3", "cyberpunk2020": "киберпанк2020", "falloutbos": "falloutbos", "anothereden": "анотередэн", "roleplaygames": "бозиҳоинақшофарӣ", "roleplaygame": "бозиинақшофар", "fabulaultima": "фабулаултима", "witchsheart": "дилиҷодугар", "harrypottergame": "бозииҳаррипоттер", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "вампирдармаскарад", "dračák": "<PERSON><PERSON><PERSON><PERSON>", "spelljammer": "ҷодугарикиҳонӣ", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "хроносаҳна", "cocttrpg": "cocttrpg", "huntroyale": "шикориподшоҳӣ", "albertodyssey": "албертодиссея", "monsterhunterworld": "мonsterhunterworld", "bg3": "bg3", "xenogear": "ксеногир", "temtem": "темтем", "rpgforum": "форумирпг", "shadowheartscovenant": "аҳдишадоухарт", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "подшоҳиомад", "awplanet": "awплонета", "theworldendswithyou": "ҷаҳонбатубаохирмерасад", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "куфритоморат", "shoptitans": "shoptitans", "forumrpg": "форумрпг", "golarion": "голарион", "earthmagic": "сеҳризамин", "blackbook": "китобисиёҳ", "skychildrenoflight": "кӯдаконинурдаросмон", "gryrpg": "gryrpg", "sacredgoldedition": "нусхаиолоимуқаддас", "castlecrashers": "қалъашикан", "gothicgame": "бозии_готик", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "бозиҳоиrpg", "prophunt": "шикоричайғамбар", "starrails": "рельсҳоиситорагӣ", "cityofmist": "шаҳримуаммо", "indierpg": "инди_рпг", "pointandclick": "нуқтазанӣ", "emilyisawaytoo": "эмилихампартаст", "emilyisaway": "emilyғоиб", "indivisible": "ҷудонашаванда", "freeside": "озодҷониб", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "мегамитен<PERSON>ей", "symbaroum": "симбарум", "postcyberpunk": "постсиберпанк", "deathroadtocanada": "роҳимаргбасӯиканада", "palladium": "палладиум", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "шикоричигаҳрамон", "fireemblem": "ота<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "genshinimpact": "genshinimpact", "geosupremancy": "геосупремасия", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "секиро", "monsterhunterrise": "шикоримонстерrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "рӯҳхӯр", "fatestaynight": "қисматишабона", "etrianodyssey": "etrianodyssey", "nonarygames": "бозиҳоибеҷинсият", "tacticalrpg": "rpgтактикӣ", "mahoyo": "mahoyo", "animegames": "бозиҳоианиме", "damganronpa": "данганронпа", "granbluefantasy": "granbluefantasy", "godeater": "худоҳӯр", "diluc": "дил<PERSON>к", "venti": "венти", "eternalsonata": "навоиабадӣ", "princessconnect": "принцессконнект", "hexenzirkel": "hexenzirkel", "cristales": "кристаллҳо", "vcs": "венчур", "pes": "пес", "pocketsage": "ҷебидон", "valorant": "valorant", "valorante": "валоранте", "valorantindian": "valorantҳинд", "dota": "dota", "madden": "мадден", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "бозиҳоиэлектронӣ", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "киберспорт", "mlg": "mlg", "leagueofdreamers": "лигаихобдидагон", "fifa14": "fifa14", "midlaner": "мид<PERSON><PERSON><PERSON><PERSON><PERSON>р", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "гейминг", "overwatchleague": "лигаиoverwatch", "cybersport": "киберспорт", "crazyraccoon": "девонагурбача", "test1test": "test1test", "fc24": "fc24", "riotgames": "riotgames", "eracing": "мусобиқаиэлектронӣ", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantрақобатӣ", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "нимумр", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "клапан", "portal": "портал", "teamfortress2": "teamfortress2", "everlastingsummer": "тобистонинҷовидон", "goatsimulator": "симуляторибуз", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "озодисайёра", "transformice": "transformice", "justshapesandbeats": "танҳошаклҳовазарбҳо", "battlefield4": "battlefield4", "nightinthewoods": "шабдарҷангал", "halflife2": "halflife2", "hacknslash": "ҳакнслэш", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "метроидванияҳо", "overcooked": "ҳаддандзиёдпухта", "interplanetary": "байнисайёравӣ", "helltaker": "ҷаҳаннамгир", "inscryption": "рамзгузорӣ", "7d2d": "7d2d", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "қалъаипаккалон", "foxhole": "сангар", "stray": "саргардон", "battlefield": "майдониҷанг", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "зерэобӣ", "eyeb": "абрӯ", "blackdesert": "биёбонисиёҳ", "tabletopsimulator": "симулятори_мизи_бозӣ", "partyhard": "базмазан", "hardspaceshipbreaker": "шикастанисафинаиқаттӣ", "hades": "hades", "gunsmith": "силоҳсоз", "okami": "оками", "trappedwithjester": "дардомиҷестер", "dinkum": "ҳақиқӣ", "predecessor": "пешгузашта", "rainworld": "ҷаҳониборон", "cavesofqud": "ғорҳоиқуд", "colonysim": "симколония", "noita": "noita", "dawnofwar": "ҷангибоздошт", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "торикутарикӣ", "motox": "мотокс", "blackmesa": "blackmesa", "soulworker": "рӯҳкор", "datingsims": "симҳоиошиқӣ", "yaga": "яга", "cubeescape": "фирориазкуб", "hifirush": "салом_рашинг", "svencoop": "svencoop", "newcity": "шаҳринав", "citiesskylines": "шаҳрҳоосмонхароҳо", "defconheavy": "defconвазнин", "kenopsia": "кеноpsия", "virtualkenopsia": "кенопсияимаҷозӣ", "snowrunner": "барфрон", "libraryofruina": "китобхонаидевона", "l4d2": "l4d2", "thenonarygames": "thenonarygames", "omegastrikers": "omegastrikers", "wayfinder": "роҳнамо", "kenabridgeofspirits": "кенапулирӯҳҳо", "placidplasticduck": "ордакипластикииором", "battlebit": "ҷангиbit", "ultimatechickenhorse": "мурғиаспиниҳоӣ", "dialtown": "шаҳрителефон", "smileforme": "табассумкун", "catnight": "шабигурба", "supermeatboy": "supermeatboy", "tinnybunny": "хаpгӯшчаи_хурдакак", "cozygrove": "бодомзориосоишта", "doom": "ҳалокат", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "сарҳадот", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "апекс", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "бозиҳоиfarcry", "paladins": "паладинҳо", "earthdefenseforce": "мудофиаизамин", "huntshowdown": "шикоришоуда<PERSON>н", "ghostrecon": "арвоҳразведка", "grandtheftauto5": "grandtheftauto5", "warz": "ҷангҳо", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ултракиллинг", "joinsquad": "да<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>д", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "шӯришисандсторм", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "марговарсонӣ", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "зомбиҳоикод", "mirrorsedge": "лабиоина", "divisions2": "тақсимот2", "killzone": "майдониқатл", "helghan": "хелган", "coldwarzombies": "зомбиҳоиҷангисард", "metro2033": "метро2033", "metalgear": "metalgear", "acecombat": "ҷангиосмонӣ", "crosscode": "кросскод", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "снайперэлит", "modernwarfare": "ҷангимуосир", "neonabyss": "неонӣторикӣ", "planetside2": "planetside2", "mechwarrior": "механикиҷанг", "boarderlands": "сарҳадҳо", "owerwatch": "ower<PERSON>", "rtype": "намудиман", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON>ир<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "металслаг", "primalcarnage": "ғазабивахшӣ", "worldofwarships": "ҷаҳониоништиҳоиҷангӣ", "back4blood": "назад4хун", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "қотил", "masseffect": "masseffect", "systemshock": "шокисистема", "valkyriachronicles": "вальхўриякроника", "specopstheline": "хатти<PERSON><PERSON><PERSON><PERSON><PERSON>с", "killingfloor2": "killingfloor2", "cavestory": "ғорикоя", "doometernal": "думидоимӣ", "centuryageofashes": "асрихокистар", "farcry4": "farcry4", "gearsofwar": "ҷангичархдандонҳо", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "наслисифр", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "ҷакваддакстер", "modernwarfare2": "ҷангимуосири2", "blackops1": "blackops1", "sausageman": "колбасамард", "ratchetandclank": "рэтчетваклэнк", "chexquest": "chexquest", "thephantompain": "дардиафсонавӣ", "warface": "ҷангирӯй", "crossfire": "оташ<PERSON>ишон", "atomicheart": "дилиатомӣ", "blackops3": "blackops3", "vampiresurvivors": "вампирҳозинда", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "мурғиҷангал", "freedoom": "озодӣ", "battlegrounds": "майдонҳоиҷанг", "frag": "фраг", "tinytina": "тина<PERSON>ина", "gamepubg": "gamepubg", "necromunda": "некрomunда", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "бозиҳоиfps", "convertstrike": "табдилиза<PERSON>ба", "warzone2": "warzone2", "shatterline": "хатишикаста", "blackopszombies": "зомбиҳоиблэкопс", "bloodymess": "<PERSON>у<PERSON>ин<PERSON>а<PERSON><PERSON><PERSON>ум", "republiccommando": "фармондеҳиҷумҳурӣ", "elitedangerous": "elitedangerous", "soldat": "солдат", "groundbranch": "шохаизаминӣ", "squad": "гурӯҳ", "destiny1": "тақдир1", "gamingfps": "гаймингfps", "redfall": "афтодан", "pubggirl": "pubgд<PERSON><PERSON><PERSON><PERSON>р", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "сафбаста", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "арморедкор", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "рӯзимузд2", "cs16": "cs16", "pubgindonesia": "pubgиндонезия", "pubgukraine": "pubgук<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgомания", "empyrion": "эмпирион", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "собункод", "ghostcod": "арвоҳкод", "csplay": "косплей", "unrealtournament": "unrealtournament", "callofdutydmz": "callofdutydmz", "gamingcodm": "геймингcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "тозиёнаипистолет", "callofdutymw2": "калофдутимв2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "гало", "killingfloor": "майдонимарг", "destiny2": "destiny2", "exoprimal": "экзопраймал", "splintercell": "splintercell", "neonwhite": "неонсафед", "remnant": "боқимонда", "azurelane": "azurelane", "worldofwar": "ҷангиҷаҳонӣ", "gunvolt": "ганволт", "returnal": "барга<PERSON>т", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "одамисоя", "quake2": "зилзила2", "microvolts": "микроволт", "reddead": "мурдасурх", "standoff2": "standoff2", "harekat": "ҳаракат", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "баҳридуздон", "rust": "занг", "conqueronline": "conqueronline", "dauntless": "ҷасур", "warships": "киштиҳоиҷангӣ", "dayofdragons": "рӯзиаждаҳоҳо", "warthunder": "warthunder", "flightrising": "парвози<PERSON><PERSON><PERSON>а<PERSON>д", "recroom": "рекрум", "legendsofruneterra": "legendsofruneterra", "pso2": "pso2", "myster": "сирр", "phantasystaronline2": "фантазистаронлайн2", "maidenless": "безухтар", "ninokuni": "ниноkunи", "worldoftanks": "ҷаҳонитанкҳо", "crossout": "ха<PERSON><PERSON>адан", "agario": "agario", "secondlife": "ҳаётидуюм", "aion": "айон", "toweroffantasy": "бурҷифантазия", "netplay": "бозии<PERSON><PERSON><PERSON><PERSON><PERSON>н", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "редҳалокпуронлайн", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "тбои", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "кодивейн", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "вакфу", "scum": "лаънатӣ", "newworld": "ҷаҳонинав", "blackdesertonline": "blackdesertonline", "multiplayer": "боз<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>рнафара", "pirate101": "пират101", "honorofkings": "шарафиподшоҳон", "fivem": "fivem", "starwarsbattlefront": "ҷангистораҳо", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "шаҳракипони", "3dchat": "чат3d", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowклас<PERSON>ик", "worldofwarcraft": "worldofwarcraft", "warcraft": "варкрафт", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "моба", "habbo": "habbo", "archeage": "архейҷ", "toramonline": "торамо<PERSON><PERSON><PERSON><PERSON>н", "mabinogi": "mabinogi", "ashesofcreation": "хокистарисозӣ", "riotmmo": "riotmmo", "silkroad": "роҳиабрешим", "spiralknights": "спира<PERSON><PERSON><PERSON>тс", "mulegend": "muафсона", "startrekonline": "стартрекионлайн", "vindictus": "виндиктус", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "пайғамбариаждаҳоҳо", "grymmo": "гриммо", "warmane": "гармкунанда", "multijugador": "би<PERSON><PERSON><PERSON><PERSON><PERSON>зингар", "angelsonline": "фариштаҳодарсайт", "lunia": "луния", "luniaz": "лун<PERSON><PERSON><PERSON>", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "ҷумҳурииқадимистарворс", "grandfantasia": "грандфантазия", "blueprotocol": "протоколикабуд", "perfectworld": "дунёиолӣ", "riseonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "corepunk": "корпанк", "adventurequestworlds": "ҷаҳонҳоисаргузашт", "flyforfun": "парвозбарои_кайф", "animaljam": "ҷамъомадиҳайвонот", "kingdomofloathing": "подшоҳиинафрат", "cityofheroes": "шаҳриқаҳрамонон", "mortalkombat": "мортолкомбат", "streetfighter": "ҷангҷӯиикӯча", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "барои_шараф", "tekken": "теккен", "guiltygear": "гил<PERSON><PERSON><PERSON><PERSON>р", "xenoverse2": "xenoverse2", "fgc": "хатнаизанон", "streetfighter6": "streetfighter6", "multiversus": "мултивер<PERSON>ус", "smashbrosultimate": "смашбросултимейт", "soulcalibur": "soulcalibur", "brawlhalla": "бравлхалла", "virtuafighter": "ҷангҷӯйимаҷозӣ", "streetsofrage": "кӯчаҳоиғазаб", "mkdeadlyalliance": "иттифоқимаргбор", "nomoreheroes": "қаҳрамононнест", "mhr": "mhr", "mortalkombat12": "mortalkomбат12", "thekingoffighters": "шоҳибозиҳоиҷангӣ", "likeadragon": "мислидраконлар", "retrofightinggames": "бозиҳоиҷангииретро", "blasphemous": "кашида", "rivalsofaether": "рақибонидарэфир", "persona4arena": "persona4arena", "marvelvscapcom": "марвелбаробарикапком", "supersmash": "супершикаст", "mugen": "муген", "warofthemonsters": "ҷангиҳайулоҳо", "jogosdeluta": "бозиҳоилута", "cyberbots": "киберботҳо", "armoredwarriors": "ҷангҷӯёниҷавшандор", "finalfight": "ҷангиохирин", "poweredgear": "таҷҳизотиқудратманд", "beatemup": "лату_куб_кунед", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "бозиҳоиҷангӣ", "killerinstinct": "ғаризаиқотил", "kingoffigthers": "шоҳипаҳлавонон", "ghostrunner": "рӯҳдавон", "chivalry2": "рыцарство2", "demonssouls": "деномдонҳоисулз", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "давома<PERSON>холлоукнайт", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "боз<PERSON><PERSON><PERSON><PERSON>song", "silksongnews": "хабарҳоиsilksong", "silksong": "silksong", "undernight": "шабона", "typelumina": "типлюмина", "evolutiontournament": "турнириэволюция", "evomoment": "лаҳзаибомодарӣ", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "ҳикояҳоиберсерия", "bloodborne": "bloodborne", "horizon": "уфуқ", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "крашбандикут", "bloodbourne": "bloodbourne", "uncharted": "ноошинохта", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "спайро", "playstationplus": "playstationplus", "lastofus": "охирин<PERSON><PERSON><PERSON><PERSON>н", "infamous": "машҳур", "playstationbuddies": "ҳамбозиҳойplaystation", "ps1": "ps1", "oddworld": "ҷаҳониаҷиб", "playstation5": "playstation5", "slycooper": "слайкупер", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "саломбачаҳо", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "худоиҷанг", "gris": "гриш", "trove": "ганҷина", "detroitbecomehuman": "детройтмешаваданинсон", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "тоқиёмат", "touristtrophy": "ҷоизаисайёҳ", "lspdfr": "lspdfr", "shadowofthecolossus": "сояиколосс", "crashteamracing": "crashteamracing", "fivepd": "панҷпд", "tekken7": "tekken7", "devilmaycry": "шайтонметавонадгирякунад", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "playingstation", "samuraiwarriors": "ҷанговаронисамурай", "psvr2": "psvr2", "thelastguardian": "охиринмуҳофиз", "soulblade": "рӯҳишамшер", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "ҷустуҷӯимард", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "охиринмуҳофиз", "xboxone": "xboxone", "forza": "форза", "cd": "cd", "gamepass": "геймпасс", "armello": "armello", "partyanimal": "тусбоз", "warharmmer40k": "warhammer40k", "fightnightchampion": "шабиҷангичемпион", "psychonauts": "психонавтҳо", "mhw": "mhw", "princeofpersia": "шоҳзодаиэрон", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "пантарҳей", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "майдониҷанг", "dontstarvetogether": "нагуруснемонед", "ori": "ори", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "ситор<PERSON><PERSON><PERSON><PERSON>д", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "хонаташкилкунанда", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "лигаипадшоҳӣ", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "телевизиониахлот", "skycotl": "осмонкотл", "erica": "эрика", "ancestory": "аҷдодӣ", "cuphead": "cuphead", "littlemisfortune": "бадбахтидухтарак", "sallyface": "саллифейс", "franbow": "franbow", "monsterprom": "дискотекаичиноят", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "мотосиклҳо", "outerwilds": "сайёратҳоиберунӣ", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "қавмибарра", "duckgame": "бозиимурғобӣ", "thestanleyparable": "афсонаистэнли", "towerunite": "башниякҷоя", "occulto": "пинҳонӣ", "longdrive": "рохидур", "satisfactory": "қаноатбахш", "pluviophile": "боронпараст", "underearth": "зеризамин", "assettocorsa": "assettocorsa", "geometrydash": "геометридаш", "kerbal": "кербал", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "кенши", "spiritfarer": "роҳнамоиарвоҳ", "darkdome": "гумбазиторик", "pizzatower": "пицца<PERSON><PERSON><PERSON><PERSON>р", "indiegame": "бозиҳоимустақил", "itchio": "itchio", "golfit": "golfit", "truthordare": "ростӣёдурӯғ", "game": "бозӣ", "rockpaperscissors": "сангқоғазқайчӣ", "trampoline": "трамплин", "hulahoop": "ҳулахуп", "dare": "ҷуръат", "scavengerhunt": "ҷустуҷӯисайёд", "yardgames": "бозиҳоидарҳавлӣ", "pickanumber": "рақамеинтихобкун", "trueorfalse": "ростёдурӯғ", "beerpong": "пивопонг", "dicegoblin": "зарпараст", "cosygames": "бозиҳоиороми", "datinggames": "бозиҳоиошноӣ", "freegame": "бозиибепул", "drinkinggames": "бозиҳоинӯшокӣ", "sodoku": "судоку", "juegos": "бозиҳо", "mahjong": "маҷонг", "jeux": "jeux", "simulationgames": "бозиҳоисимулятсия", "wordgames": "бозиҳоикалимавӣ", "jeuxdemots": "бозиҳоибакалимаҳо", "juegosdepalabras": "бозиҳоибокалимаҳо", "letsplayagame": "биёбозӣкунем", "boredgames": "бозиҳоизеҳркунанда", "oyun": "бозӣ", "interactivegames": "бозиҳоиинтерактивӣ", "amtgard": "amtgard", "staringcontests": "нигоҳазмоишҳо", "spiele": "бозӣ", "giochi": "гейм", "geoguessr": "геогессор", "iphonegames": "бозиҳоиiphone", "boogames": "бозиҳоибoo", "cranegame": "бозиик<PERSON><PERSON>н", "hideandseek": "пинҳонбозӣ", "hopscotch": "классикӣ", "arcadegames": "бозиҳоиаркада", "yakuzagames": "бозиҳоиякудза", "classicgame": "бозииклассикӣ", "mindgames": "бозиҳоизеҳнӣ", "guessthelyric": "лабҳойшеъргӯй", "galagames": "бозиҳоигала", "romancegame": "бозиишқ", "yanderegames": "бозиҳоиёндере", "tonguetwisters": "забонбозӣ", "4xgames": "4xбозиҳо", "gamefi": "gamefi", "jeuxdarcades": "бозиҳоиаркада", "tabletopgames": "бозиҳоирӯимизӣ", "metroidvania": "метроидвания", "games90": "бозиҳо90", "idareyou": "ҷуръаткун", "mozaa": "мозаа", "fumitouedagames": "бозиҳоифумитоуеда", "racinggames": "бозиҳоипойга", "ets2": "ets2", "realvsfake": "воқеӣёқалбакӣ", "playgames": "бозӣкун", "gameonline": "бозиҳоионлайн", "onlinegames": "бозиҳоионлайн", "jogosonline": "бозиҳоионлайн", "writtenroleplay": "ролевойбозӣ", "playaballgame": "бозиикунтӯбро", "pictionary": "pictionary", "coopgames": "бозиҳоикооператив", "jenga": "ҷенга", "wiigames": "бозиҳоиwii", "highscore": "ҳисобиболо", "jeuxderôles": "бозиҳоинақшӣ", "burgergames": "бозиҳоибургер", "kidsgames": "бозиҳоикӯдакон", "skeeball": "скибол", "nfsmwblackedition": "nfsmwнусхаисиёҳ", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "бозииҳосаволҳо", "gioco": "бозӣ", "managementgame": "бозииидоракунӣ", "hiddenobjectgame": "бозииашёипинҳон", "roolipelit": "рольовыебозихо", "formula1game": "бозииформула1", "citybuilder": "шаҳрсоз", "drdriving": "дрдр<PERSON><PERSON><PERSON><PERSON>нг", "juegosarcade": "бозиҳоиаркада", "memorygames": "бозиҳоихотира", "vulkan": "вулкан", "actiongames": "бозиҳоиҳаракатӣ", "blowgames": "бозиҳоидамидан", "pinballmachines": "мошинҳоипинбол", "oldgames": "бозиҳоикӯҳна", "couchcoop": "дарканапабозӣ", "perguntados": "саволҳо", "gameo": "бозӣ", "lasergame": "бозиилазерӣ", "imessagegames": "бозиҳоиimessage", "idlegames": "бозиҳоибекор", "fillintheblank": "холигиҷойропуркун", "jeuxpc": "бозиҳоиpc", "rétrogaming": "ретрогейминг", "logicgames": "бозиҳоимантиқӣ", "japangame": "бозиҳоиҷопонӣ", "rizzupgame": "бозииҷозибият", "subwaysurf": "субвей<PERSON>ёрф", "jeuxdecelebrite": "бозиҳоимашҳур", "exitgames": "бозиҳоибаромад", "5vs5": "5ба5", "rolgame": "ролгейм", "dashiegames": "dashiegames", "gameandkill": "бозӣкунубикуш", "traditionalgames": "бозиҳоианъанавӣ", "kniffel": "книффель", "gamefps": "бозиfps", "textbasedgames": "бозиҳоиматнӣ", "gryparagrafowe": "грипарографӣ", "fantacalcio": "фантакалчо", "retrospel": "ретроспел", "thiefgame": "бозиидузд", "lawngames": "бозиҳоидармайдон", "fliperama": "флиперама", "heroclix": "heroclix", "tablesoccer": "футболирӯимизӣ", "tischfußball": "tischfußball", "spieleabende": "шабҳоибозӣ", "jeuxforum": "jeuxforum", "casualgames": "бозиҳоиоддӣ", "fléchettes": "флешет", "escapegames": "бозиҳоифирор", "thiefgameseries": "бозиҳоидузд", "cranegames": "бозиҳоикрейн", "játék": "бозӣ", "bordfodbold": "теннисирӯимизӣ", "jogosorte": "jogosorte", "mage": "ҷодугар", "cargames": "бозиҳоимошин", "onlineplay": "бозии<PERSON><PERSON><PERSON><PERSON><PERSON>н", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "шабҳоибозӣ", "pursebingos": "сумкабинго", "randomizer": "тасодуфсоз", "msx": "msx", "anagrammi": "анаграммӣ", "gamespc": "бозиҳоиpc", "socialdeductiongames": "бозиҳоидедуктивииҷтимоӣ", "dominos": "доминос", "domino": "домино", "isometricgames": "бозиҳоиизометрӣ", "goodoldgames": "бозиҳоиқадимихуб", "truthanddare": "ростӣёдурӯғ", "mahjongriichi": "маҷонгричи", "scavengerhunts": "ҷустуҷӯҳо", "jeuxvirtuel": "бозиҳоимаҷозӣ", "romhack": "ромхак", "f2pgamer": "f2pgamer", "free2play": "бепулбозӣ", "fantasygame": "бозиифантазӣ", "gryonline": "бозиҳоионлайн", "driftgame": "бозиидрифт", "gamesotomes": "бозиҳоиотоме", "halotvseriesandgames": "haloсериалваозунҳо", "mushroomoasis": "ҷазираизамбуруғӣ", "anythingwithanengine": "ҳарчизебомотор", "everywheregame": "бозииҳамаҷо", "swordandsorcery": "шамшерусеҳр", "goodgamegiving": "бозиихубдодан", "jugamos": "мебозем", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "бозиҳоикомпютерӣ", "virgogami": "бикргами", "gogame": "gogame", "jeuxderythmes": "бозиҳоиритм", "minaturegames": "бозиҳоиминиатюрӣ", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "худдӯстиигейминг", "gamemodding": "моддингибозӣ", "crimegames": "бозиҳоиҷиноят", "dobbelspellen": "бозиҳоитахтарезӣ", "spelletjes": "бозичаҳо", "spacenerf": "spacenerf", "charades": "пантомима", "singleplayer": "якканафара", "coopgame": "бозии_кооп", "gamed": "бозӣкардам", "forzahorizon": "forzahorizon", "nexus": "нексус", "geforcenow": "geforcenow", "maingame": "бозииасосӣ", "kingdiscord": "шоҳидискорд", "scrabble": "скраббл", "schach": "шоҳмот", "shogi": "шоги", "dandd": "данддд", "catan": "катан", "ludo": "лудо", "backgammon": "нард", "onitama": "онитама", "pandemiclegacy": "меросипандемия", "camelup": "шутурбозӣ", "monopolygame": "бозимонополия", "brettspiele": "бозиҳоирӯимизӣ", "bordspellen": "бозиҳоирӯимизӣ", "boardgame": "бозиирӯимизӣ", "sällskapspel": "бозиҳоидастаҷамъӣ", "planszowe": "бозиҳоирӯимизӣ", "risiko": "рискӣ", "permainanpapan": "бозиҳоитахтагӣ", "zombicide": "зомбикушӣ", "tabletop": "рӯимизӣ", "baduk": "бадук", "bloodbowl": "bloodbowl", "cluedo": "клуэдо", "xiangqi": "шоҳмот", "senet": "сенет", "goboardgame": "рафтбозиирӯимизӣ", "connectfour": "чорпайваст", "heroquest": "саргузашти_қаҳрамон", "giochidatavolo": "бозиҳоирӯимизӣ", "farkle": "фаркл", "carrom": "каррам", "tablegames": "бозиҳоирӯимизӣ", "dicegames": "бозиҳоиқубба", "yatzy": "ятзи", "parchis": "пар<PERSON><PERSON>с", "jogodetabuleiro": "бозиҳоирӯимизӣ", "jocuridesocietate": "бозиҳоиҷамъиятӣ", "deskgames": "бозиҳоирӯимизӣ", "alpharius": "алфар<PERSON><PERSON><PERSON>", "masaoyunları": "бозиҳоимасо", "marvelcrisisprotocol": "марвелкризиспротокол", "cosmicencounter": "вохӯрдикайҳонӣ", "creationludique": "эҷодиёти_бозинок", "tabletoproleplay": "бозиҳоинақшӣ", "cardboardgames": "бозиҳоикартонӣ", "eldritchhorror": "даҳшатиқадима", "switchboardgames": "бозиҳоисвичборд", "infinitythegame": "бозииабадӣ", "kingdomdeath": "марги_подшоҳӣ", "yahtzee": "ятзи", "chutesandladders": "нардбонӣ", "társas": "ҳамроҳ", "juegodemesa": "бозиирӯимизӣ", "planszówki": "тахтаибозӣ", "rednecklife": "ҳаётидеҳотӣ", "boardom": "беҳузурӣ", "applestoapples": "себсебба", "jeudesociété": "бозиҳоиҷамъиятӣ", "gameboard": "лавҳаибозӣ", "dominó": "домино", "kalah": "калаҳ", "crokinole": "крокинол", "jeuxdesociétés": "бозиҳоиастолӣ", "twilightimperium": "империяишафақ", "horseopoly": "аспополия", "deckbuilding": "созмонидастабозӣ", "mansionsofmadness": "қасрҳоидевонагӣ", "gomoku": "гомоку", "giochidatavola": "бозиҳоирӯимизӣ", "shadowsofbrimstone": "сояҳоибримстон", "kingoftokyo": "шоҳитокио", "warcaby": "вархаби", "táblajátékok": "тахтабозиҳо", "battleship": "киштиҷангӣ", "tickettoride": "саёҳатибасӯихушбахтӣ", "deskovehry": "бозиҳоирӯимизӣ", "catán": "катан", "subbuteo": "субутео", "jeuxdeplateau": "бозиҳоирӯимизӣ", "stolníhry": "столнихры", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "бозиҳоиғунҷоишӣ", "gesellschaftsspiele": "бозиҳоиҷамъиятӣ", "starwarslegion": "starwarslegion", "gochess": "шоҳмотравед", "weiqi": "weiqi", "jeuxdesocietes": "бозиҳоиҷамъиятӣ", "terraria": "terraria", "dsmp": "dsmp", "warzone": "майдониҷанг", "arksurvivalevolved": "arksurvivalevolved", "dayz": "рӯзҳо", "identityv": "identityv", "theisle": "ҷазира", "thelastofus": "the<PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "томбрейдер", "callofcthulhu": "садоиктулҳу", "bendyandtheinkmachine": "бендиваохариноресоз", "conanexiles": "conanexiles", "eft": "eft", "amongus": "дарб<PERSON>йнимо", "eco": "эко", "monkeyisland": "ҷазираимаймун", "valheim": "valheim", "planetcrafter": "сайё<PERSON><PERSON><PERSON><PERSON><PERSON>", "daysgone": "рӯзҳогузашт", "fobia": "фобия", "witchit": "witchit", "pathologic": "патологӣ", "zomboid": "зомбоид", "northgard": "northgard", "7dtd": "7рӯзтоҷиҳаннам", "thelongdark": "шабидарозизиндагӣ", "ark": "арк", "grounded": "заминӣ", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "падаридевона", "dontstarve": "гуруснанамон", "eternalreturn": "бозгаштиабадӣ", "pathoftitans": "роҳиубурҳо", "frictionalgames": "бозиҳоитаъсирбахш", "hexen": "ҷодугарон", "theevilwithin": "шайтондардарун", "realrac": "realrac", "thebackrooms": "ҳуҷраҳоипушт", "backrooms": "утоқҳоипушт", "empiressmp": "empiressmp", "blockstory": "ҳикояиблок", "thequarry": "кон", "tlou": "tlou", "dyinglight": "марғзиндагӣ", "thewalkingdeadgame": "бозиимурдагонироҳгард", "wehappyfew": "мокамнафарем", "riseofempires": "пешравииимперияҳо", "stateofsurvivalgame": "бозииҳолатизинда", "vintagestory": "ҳикояикӯҳна", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "баротравма", "breathedge": "breathedge", "alisa": "алиса", "westlendsurvival": "westlendоҷомонд", "beastsofbermuda": "аҷоиботибермуда", "frostpunk": "фростпанк", "darkwood": "ҷангалиторик", "survivalhorror": "хавфнокизинда", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "қатористодан", "lifeaftergame": "баъдазбозӣҳаёт", "survivalgames": "бозиҳоизиндамонӣ", "sillenthill": "сайлентҳилл", "thiswarofmine": "инҷангимман", "scpfoundation": "scpfoundation", "greenproject": "лоиҳаисабз", "kuon": "kuon", "cryoffear": "гир<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "сел", "rdo": "rdo", "greenhell": "дӯзахисабз", "residentevil5": "residentevil5", "deadpoly": "поликимурда", "residentevil8": "residentevil8", "onironauta": "онейронавт", "granny": "момо", "littlenightmares2": "кобусҳоишабонаихурд2", "signalis": "сиг<PERSON><PERSON><PERSON>с", "amandatheadventurer": "амандасайёҳ", "sonsoftheforest": "писаронипарк", "rustvideogame": "бозиивидеоииrust", "outlasttrials": "таҷрибаҳоиoutlast", "alienisolation": "бегонагиҷудоӣ", "undawn": "субҳидам", "7day2die": "7рӯз2мурдан", "sunlesssea": "баҳрибенур", "sopravvivenza": "зинда_мондан", "propnight": "шабипроп", "deadisland2": "ҷазираимурдаҳо2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "вампирихушрӯй", "deathverse": "марголам", "cataclysmdarkdays": "рӯзҳоиторикифалокат", "soma": "сома", "fearandhunger": "тарсухуруснагӣ", "stalkercieńczarnobyla": "сталкерсояичернобил", "lifeafter": "баъдазҳаёт", "ageofdarkness": "асридзулмот", "clocktower3": "соатибурҷи3", "aloneinthedark": "дартанҳоӣторикӣ", "medievaldynasty": "сулолаиасримиёна", "projectnimbusgame": "бозии_projectnimbus", "eternights": "шабҳоиҷовидонӣ", "craftopia": "косибикорӣ", "theoutlasttrials": "санҷишҳоиoutlast", "bunker": "бу<PERSON><PERSON><PERSON><PERSON>", "worlddomination": "ҷаҳонгирӣ", "rocketleague": "rocketleague", "tft": "ямба", "officioassassinorum": "офисиассасинорум", "necron": "некрон", "wfrp": "wfrp", "dwarfslayer": "қотилипаккалонон", "warhammer40kcrush": "вархаммер40кишқ", "wh40": "wh40", "warhammer40klove": "ишқивархаммер40к", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "маъбадиҷонкуш", "vindicare": "интиқом", "ilovesororitas": "мансороритихородӯстмедорам", "ilovevindicare": "манvindicareдӯстмедорам", "iloveassasinorum": "маношиқиассасинорумам", "templovenenum": "муҳаббативақтӣрақам", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "40k": "40ҳазор", "tetris": "тетрис", "lioden": "шерон", "ageofempires": "асриимператорияҳо", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "ду<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>д", "wingspan": "болпаҳн", "terraformingmars": "терраформингиморих", "heroesofmightandmagic": "қаҳрамонониқудратусеҳр", "btd6": "btd6", "supremecommander": "командириолӣ", "ageofmythology": "асримифология", "args": "аргументҳо", "rime": "вақт", "planetzoo": "сайёраибоғивайҳшон", "outpost2": "дарвоза2", "banished": "бадарға", "caesar3": "сезар3", "redalert": "ҳушдорисурх", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "фармонфармоӣвазабткунӣ", "warcraft3": "warcraft3", "eternalwar": "ҷангиабадӣ", "strategygames": "бозиҳоистратегӣ", "anno2070": "anno2070", "civilizationgame": "бозии_тамаддун", "civilization4": "тамаддун4", "factorio": "factorio", "dungeondraft": "харит<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "spore": "спора", "totalwar": "ҷангикулл", "travian": "travian", "forts": "қалъаҳо", "goodcompany": "ширка<PERSON><PERSON><PERSON><PERSON>б", "civ": "медании", "homeworld": "ҷаҳониватан", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "тезтаразнур", "forthekings": "барои_подшоҳон", "realtimestrategy": "стратегияивақтиҳозира", "starctaft": "starcraft", "sidmeierscivilization": "сидмейерсцивилизация", "kingdomtwocrowns": "шоҳигарииду_тоҷ", "eu4": "eu4", "vainglory": "худписандӣ", "ww40k": "ww40k", "godhood": "худогӣ", "anno": "анно", "battletech": "мубориза_технологӣ", "malifaux": "малифо", "w40k": "w40k", "hattrick": "хеттрик", "davesfunalgebraclass": "синфиалгебраидавуддор", "plagueinc": "ваборо", "theorycraft": "назариябофӣ", "mesbg": "mesbg", "civilization3": "тамаддун3", "4inarow": "4дарқатор", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "ҷангҳоипешрафта", "ageofempires2": "ageofempires2", "disciples2": "шогирдон2", "plantsvszombies": "растаниҳозидзомбиҳо", "giochidistrategia": "бозиҳоистратегӣ", "stratejioyunları": "бозиҳоистратегӣ", "europauniversalis4": "европауниверсалис4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "асримоҷароҳо", "dinosaurking": "шоҳидинозавр", "worldconquest": "ҷаҳонгирӣ", "heartsofiron4": "дилҳоиоҳанин4", "companyofheroes": "ширкатиқаҳрамонон", "battleforwesnoth": "ҷангбароивеснот", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "wархаммеркиллтеам", "goosegooseduck": "ғозғоззанад", "phobies": "фобияҳо", "phobiesgame": "бозиифобияҳо", "gamingclashroyale": "gamingclashroyale", "adeptusmechanicus": "адептусмеханикус", "outerplane": "ҳавопаймоиберунӣ", "turnbased": "навбатӣ", "bomberman": "bomberman", "ageofempires4": "аждҳоиимператорияҳо4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "шоҳонипоксозон", "cultris2": "cultris2", "spellcraft": "ҷодугарӣ", "starwarsempireatwar": "ҷангиситорагонимпериядарҷанг", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "стратегия", "popfulmail": "popfulmail", "shiningforce": "қувваидурахшон", "masterduel": "дуэлимастер", "dysonsphereprogram": "барномаисфераидайсон", "transporttycoon": "магнатинақлиёт", "unrailed": "берел<PERSON>ашуда", "magicarena": "арренаисеҳр", "wolvesville": "вулфсвил", "ooblets": "ooblets", "planescapetorment": "паланчегириазоб", "uplandkingdoms": "подшоҳиятҳоикӯҳӣ", "galaxylife": "ҳаётигалактика", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythesp<PERSON>р<PERSON>_кушам", "battlecats": "гурбакҳоиҷангӣ", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "симсити", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "грантуризмо", "needforspeed": "суръатлозим", "needforspeedcarbon": "нидфорспидкарбон", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "грандтуризмо", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "давомдор", "deadbydaylight": "мурдатоҳарӯз", "alicemadnessreturns": "alicemad<PERSON>ret<PERSON>s", "darkhorseanthology": "антологияиасписиёҳ", "phasmophobia": "фасмофобия", "fivenightsatfreddys": "панҷшабдарфредди", "saiko": "сайко", "fatalframe": "чорчӯбаимарг", "littlenightmares": "кобусҳоишабона", "deadrising": "мурдаҳозиставанд", "ladydimitrescu": "леди_димитреску", "homebound": "д<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadisland": "ҷазираимурдагон", "litlemissfortune": "бадбахтдухтарак", "projectzero": "лоиҳаисифр", "horory": "да<PERSON><PERSON><PERSON><PERSON>нок", "jogosterror": "даҳшатисаломатӣ", "helloneighbor": "саломҳамсоя", "helloneighbor2": "саломҳамсоя2", "gamingdbd": "gamingdbd", "thecatlady": "гурбадуст", "jeuxhorreur": "бозиҳоитарсовар", "horrorgaming": "бозиҳоитарсовар", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "кортҳобаралайҳиинсоният", "cribbage": "криббедж", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "монте", "pinochle": "пинокл", "codenames": "номҳоирамзӣ", "dixit": "дихит", "bicyclecards": "кортҳоивелосипед", "lor": "lor", "euchre": "юкер", "thegwent": "thegwent", "legendofrunetera": "афсонаирунетерра", "solitaire": "пасьянс", "poker": "покер", "hearthstone": "hearthstone", "uno": "уно", "schafkopf": "шафкопф", "keyforge": "keyforge", "cardtricks": "фокусибокорт", "playingcards": "бозиикорт", "marvelsnap": "мар<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ginrummy": "гинрам<PERSON>и", "netrunner": "netrunner", "gwent": "гвент", "metazoo": "метазу", "tradingcards": "кортҳоимубодила", "pokemoncards": "кортҳоипокемон", "fleshandbloodtcg": "фleshandbloodtcg", "sportscards": "кортҳоиварзишӣ", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "пик", "warcry": "ҷангнидо", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "шоҳидилҳо", "truco": "ҳила", "loteria": "лотерея", "hanafuda": "ханафуда", "theresistance": "муқовимат", "transformerstcg": "трансформертеҷиҷи", "doppelkopf": "доппелкопф", "yugiohcards": "кортҳоиюгиох", "yugiohtcg": "yugiohtcg", "yugiohduel": "югиохдуэл", "yugiohocg": "yugiohocg", "dueldisk": "дуэлдиск", "yugiohgame": "бозииюгиох", "darkmagician": "ҷодугарисиёҳ", "blueeyeswhitedragon": "чашмоникабудбаждаҳосафед", "yugiohgoat": "yugiohбеҳтарин", "briscas": "бриска", "juegocartas": "бозиикортхо", "burraco": "буррако", "rummy": "рамми", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "дабл", "mtgcommander": "mtgкомандир", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "бозиҳоикорт", "mtgjudge": "mtgjudge", "juegosdecartas": "бозиҳоикорт", "duelyst": "дуэлист", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "бозиикорт", "carteado": "картабозӣ", "sueca": "суека", "beloteonline": "белото<PERSON><PERSON><PERSON><PERSON>н", "karcianki": "кортчаҳо", "battlespirits": "рӯҳиҷанг", "battlespiritssaga": "рӯҳиҷангсага", "jogodecartas": "бозиикорт", "žolíky": "жоликҳо", "facecard": "рӯйкарт", "cardfight": "ҷангикортҳо", "biriba": "biriba", "deckbuilders": "созандагониколода", "marvelchampions": "қаҳрамонониmarvel", "magiccartas": "ҷодукортҳо", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "скипбо", "unstableunicorns": "unstableunicorns", "cyberse": "ки<PERSON><PERSON><PERSON>", "classicarcadegames": "бозиҳоиклассикииаркада", "osu": "osu", "gitadora": "гитадора", "dancegames": "бозиҳоирақс", "fridaynightfunkin": "ҷумъашабпартов", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "лоиҳамирай", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "гитарагаҳрамон", "clonehero": "clonehero", "justdance": "танскун", "hatsunemiku": "хатсунэмику", "prosekai": "prosekai", "rocksmith": "рокусто", "idolish7": "idolish7", "rockthedead": "раққомагарг", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "рақсимарказӣ", "rhythmgamer": "бозингарироҳанг", "stepmania": "stepmania", "highscorerythmgames": "бозиҳоиритмӣбобаллҳоибаланд", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "онгеки", "soundvoltex": "soundvoltex", "rhythmheaven": "ритмиҷаннат", "hypmic": "hypmic", "adanceoffireandice": "рақсиоташуях", "auditiononline": "аудитсия<PERSON><PERSON><PERSON><PERSON><PERSON>н", "itgmania": "itgмания", "juegosderitmo": "бозиҳоиритм", "cryptofthenecrodancer": "криптонекромансер", "rhythmdoctor": "духтурритм", "cubing": "кубикбозӣ", "wordle": "вордл", "teniz": "тенис", "puzzlegames": "бозиҳоимуаммо", "spotit": "дидӣ", "rummikub": "раммикуб", "blockdoku": "блокдоку", "logicpuzzles": "муаммоҳоимантиқӣ", "sudoku": "судоку", "rubik": "рубик", "brainteasers": "муаммоҳоизеҳнӣ", "rubikscube": "кубикрубик", "crossword": "кроссворд", "motscroisés": "калимаҳоипечида", "krzyżówki": "кроссвордҳо", "nonogram": "нонограмма", "bookworm": "китобхӯр", "jigsawpuzzles": "ҷигсавпазлҳо", "indovinello": "муаммо", "riddle": "муаммо", "riddles": "муаммоҳо", "rompecabezas": "муаммо", "tekateki": "текатеки", "inside": "<PERSON>а<PERSON><PERSON>н", "angrybirds": "парандагонихашмгин", "escapesimulator": "симуляторигу<PERSON>ез", "minesweeper": "сапёрбозӣ", "puzzleanddragons": "муаммоватанинҳо", "crosswordpuzzles": "ҷадвалҳоикроссворд", "kurushi": "курушӣ", "gardenscapesgame": "gardenscapesgame", "puzzlesport": "варзишимуаммо", "escaperoomgames": "бозиҳоиутоқифирор", "escapegame": "бозии<PERSON><PERSON><PERSON><PERSON>з", "3dpuzzle": "муаммои3d", "homescapesgame": "бозииhomescapes", "wordsearch": "калимаҷӯӣ", "enigmistica": "муаммоҳо", "kulaworld": "ҷаҳонизебо", "myst": "сирр", "riddletales": "муаммоҳикояҳо", "fishdom": "моҳидом", "theimpossiblequiz": "викторинаиимконнопазир", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "паззли3таа", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "катамаридамаси", "kwirky": "аҷибу_ғариб", "rubikcube": "кубикрубик", "cuborubik": "кубикрубик", "yapboz": "yapboz", "thetalosprinciple": "принсипиталос", "homescapes": "хонаороӣ", "puttputt": "путтпутт", "qbert": "qbert", "riddleme": "муаммогӯй", "tycoongames": "бозиҳоимагнат", "cubosderubik": "кубик<PERSON><PERSON><PERSON><PERSON><PERSON>к", "cruciverba": "кроссворд", "ciphers": "рамзҳо", "rätselwörter": "калимаҳоимуаммо", "buscaminas": "минаҷӯй", "puzzlesolving": "ҳалкарданимуаммоҳо", "turnipboy": "бачаишалғам", "adivinanzashot": "муаммоишот", "nobodies": "ҳеҷкасҳо", "guessing": "тахминзанӣ", "nonograms": "нонограммаҳо", "kostkirubika": "костикирубика", "crypticcrosswords": "кроссвордҳоипинҳонӣ", "syberia2": "сибир2", "puzzlehunt": "ҷустуҷӯимуаммо", "puzzlehunts": "муаммоёбӣ", "catcrime": "ҷиноятигурба", "quebracabeça": "муаммо", "hlavolamy": "муаммоҳо", "poptropica": "пуптропика", "thelastcampfire": "ота<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "autodefinidos": "худмуайянкарда", "picopark": "пикопарк", "wandersong": "сурудис<PERSON>ргардон", "carto": "карто", "untitledgoosegame": "бозиимурғобиебеном", "cassetête": "муаммо", "limbo": "лимбо", "rubiks": "кубикрубик", "maze": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinykin": "майдачаҳо", "rubikovakostka": "кубик<PERSON><PERSON><PERSON><PERSON><PERSON>к", "speedcube": "кубикрубик", "pieces": "порчаҳо", "portalgame": "бозииportal", "bilmece": "муаммо", "puzzelen": "муаммогузорӣ", "picross": "пикросс", "rubixcube": "кубикрубик", "indovinelli": "муаммоҳо", "cubomagico": "кубирубик", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "аҷоиботипечида", "monopoly": "монополия", "futurefight": "ҷангиоянда", "mobilelegends": "мобайллегендс", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "гургияккакас", "gacha": "гача", "wr": "нн", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "пикмин<PERSON>лум", "ff": "ff", "ensemblestars": "ситораҳоиансамбл", "asphalt9": "асфалт9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemystars", "stateofsurvival": "ҳолатизинда", "mycity": "шаҳрикам", "arknights": "аркнайтс", "colorfulstage": "саҳнаирангоранг", "bloonstowerdefense": "bloonstowerdefense", "btd": "трз", "clashroyale": "clashroyale", "angela": "анҷела", "dokkanbattle": "доканбаттл", "fategrandorder": "fategrandorder", "hyperfront": "гиперфронт", "knightrun": "ҳамлаирицор", "fireemblemheroes": "қаҳрамонониоташинэмблем", "honkaiimpact": "honkaiimpact", "soccerbattle": "ҷангифутбол", "a3": "a3", "phonegames": "бозиҳоидартелефон", "kingschoice": "интихобишоҳ", "guardiantales": "қиссаҳоинигаҳбон", "petrolhead": "бензин<PERSON><PERSON><PERSON>", "tacticool": "тактикул", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "беха<PERSON><PERSON>р", "craftsman": "ҳунарманд", "supersus": "суперашӯбҳанок", "slowdrive": "рондансуст", "headsup": "огоҳӣ", "wordfeud": "вордфеуд", "bedwars": "ҷангиқат", "freefire": "фрифа<PERSON>р", "mobilegaming": "бозиҳоимобилӣ", "lilysgarden": "боғилили", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "паёмҳоимистикӣ", "callofdutymobile": "callofdutymobileтоҷикӣ", "thearcana": "аркана", "8ballpool": "8ballpool", "emergencyhq": "штабифавқулода", "enstars": "enstars", "randonautica": "рандонавтика", "maplestory": "мэйплстори", "albion": "албион", "hayday": "рӯзигармӣ", "onmyoji": "онмёдзи", "azurlane": "azurlane", "shakesandfidget": "ларзишуҷунбиш", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "вақтшоҳдухт", "beatstar": "ситорамусиқӣ", "dragonmanialegend": "dragonmanialegend", "hanabi": "оташбозӣ", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "ишқикисагӣ", "androidgames": "бозиҳоиандроид", "criminalcase": "парвандаиҷиноятӣ", "summonerswar": "ҷанговаронифаррухфол", "cookingmadness": "ҷунуниошпазӣ", "dokkan": "доккан", "aov": "aov", "triviacrack": "саволчакчак", "leagueofangels": "лигаи<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>агон", "lordsmobile": "lordsmobile", "tinybirdgarden": "боғчаипарандаҳоихурд", "gachalife": "гача<PERSON><PERSON><PERSON><PERSON>", "neuralcloud": "абрируна", "mysingingmonsters": "монстрҳоисаройандаиман", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "роботҳоиҷангӣ", "mirrorverse": "оламиоина", "pou": "pou", "warwings": "ҷангиболҳо", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "эвертейл", "futime": "вақтихурсандӣ", "antiyoy": "антий<PERSON>й", "apexlegendmobile": "apexlegendmobile", "ingress": "даромад", "slugitout": "дарза<PERSON>укунед", "mpl": "mpl", "coinmaster": "устодисикка", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "рафиқонихайвонот", "gameofsultans": "бозииподшоҳон", "arenabreakout": "ариенабрейкаут", "wolfy": "гургӣ", "runcitygame": "бозиишаҳрдавидан", "juegodemovil": "бозиимобилӣ", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "тақлид", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "магнатипаркиистироҳат", "grandchase": "grandchase", "bombmebrasil": "бомбебразил", "ldoe": "ldoe", "legendonline": "афсонаонл<PERSON><PERSON>н", "otomegame": "бозиҳоиошиқона", "mindustry": "mindustry", "callofdragons": "садоидраконҳо", "shiningnikki": "никкидур<PERSON><PERSON><PERSON>он", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "роҳбаҳеҷкуҷо", "sealm": "силм", "shadowfight3": "ҷангисоя3", "limbuscompany": "компанияиlimbus", "demolitionderby3": "дербиивайронкунӣ3", "wordswithfriends2": "калимаҳободӯстон2", "soulknight": "рӯҳимуборизон", "purrfecttale": "мяурокардагикомил", "showbyrock": "намоишимақомакбозӣ", "ladypopular": "духтаримашҳур", "lolmobile": "lo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>л", "harvesttown": "шаҳрчаидаравӣ", "perfectworldmobile": "perfectworldmobile", "empiresandpuzzles": "империяҳовагуммаҳо", "empirespuzzles": "муаммоҳоиимперияҳо", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "майдониҷангимобайлиҳинд", "fanny": "кун", "littlenightmare": "кобусичаканди", "aethergazer": "aethergazer", "mudrunner": "лойравон", "tearsofthemis": "ашкҳоитемида", "eversoul": "ҷонидоимӣ", "gunbound": "gunbound", "gamingmlbb": "бозӣmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombieҳоиҷобшуда", "eveechoes": "eveechoes", "jogocelular": "бозиҳоимобилӣ", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "модаркалонашпаз", "cabalmobile": "cabalmobile", "streetfighterduel": "ҷангикӯчагӣдуэл", "lesecretdhenri": "сирридонри", "gamingbgmi": "гейминбгми", "girlsfrontline": "духтаронипешисаф", "jurassicworldalive": "ҷаҳониҷурасизинда", "soulseeker": "ҷустуҷӯигарирӯҳ", "gettingoverit": "азонфаромӯшкунӣ", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "ҳикояимоончай", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "бозиҳоимобилӣ", "legendofneverland": "афсонаисарзаминиҷовидон", "pubglite": "pubglite", "gamemobilelegends": "гейммобайллегендс", "timeraiders": "вақтғоратгарон", "gamingmobile": "бозиҳоимобилӣ", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "мубориконгурбаҳо", "dnd": "dnd", "quest": "квест", "giochidiruolo": "бозиҳоинақшӣ", "dnd5e": "dnd5e", "rpgdemesa": "рпгрӯимизӣ", "worldofdarkness": "ҷаҳонизулмот", "travellerttrpg": "сайёҳrpg", "2300ad": "2300сол", "larp": "larp", "romanceclub": "клуби_ишқ", "d20": "d20", "pokemongames": "бозиҳоипокемон", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "покемонкристал", "pokemonanime": "pokemonanime", "pokémongo": "покемонго", "pokemonred": "pokemonсурх", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "ла<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "porygon": "поригон", "pokemonunite": "покемонунайт", "entai": "х<PERSON><PERSON><PERSON><PERSON><PERSON>", "hypno": "гипно", "empoleon": "эмполеон", "arceus": "арсеус", "mewtwo": "мевту", "paldea": "палдеа", "pokemonscarlet": "покемонқирмизӣ", "chatot": "чатот", "pikachu": "пикачу", "roxie": "рокси", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "покемонпурпура", "ashketchum": "ашкетчум", "gengar": "gengar", "natu": "табиӣ", "teamrocket": "даста<PERSON>ракета", "furret": "фуррет", "magikarp": "магика<PERSON>п", "mimikyu": "мимикю", "snorlax": "снорлакс", "pocketmonsters": "покетмонстерҳо", "nuzlocke": "нузлокк", "pokemonplush": "покемонмулоим", "teamystic": "дастаиасрорӣ", "pokeball": "покебол", "charmander": "ча<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "гурбача<PERSON>_оташин", "shinypokemon": "покемонидурахшон", "mesprit": "месприт", "pokémoni": "покемонӣ", "ironhands": "дастҳоиоҳанин", "kabutops": "кабутопс", "psyduck": "psyduck", "umbreon": "умбреон", "pokevore": "покевор", "ptcg": "ptcg", "piplup": "пиплуп", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "саломэйпикачу", "pokémonmaster": "устоди_покемон", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "кӯдаконвапокемон", "pokemonsnap": "покемонснап", "bulbasaur": "бу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lucario": "лукарио", "charizar": "чоризор", "shinyhunter": "шикорчииду<PERSON><PERSON><PERSON><PERSON>он", "ajedrez": "шоҳмот", "catur": "шатранҷ", "xadrez": "шоҳмот", "scacchi": "шахмат", "schaken": "шахмат", "skak": "skak", "ajedres": "шоҳмот", "chessgirls": "духтаронишатранҷ", "magnuscarlsen": "магнускарлсен", "worldblitz": "блитсиҷаҳонӣ", "jeudéchecs": "шоҳмот", "japanesechess": "шоҳмотиҷопонӣ", "chinesechess": "шоҳмотичинӣ", "chesscanada": "шахматканада", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "кушодашавӣ", "rook": "рух", "chesscom": "chesscom", "calabozosydragones": "зиндонҳовааждаҳоҳо", "dungeonsanddragon": "зиндонҳовааждаҳо", "dungeonmaster": "устоди_зиндон", "tiamat": "тиамат", "donjonsetdragons": "зиндонҳовааждаҳоҳо", "oxventure": "oxventure", "darksun": "офтобисиёҳ", "thelegendofvoxmachina": "афсонаивоксмашина", "doungenoanddragons": "дандд", "darkmoor": "торикмур", "minecraftchampionship": "чемпионатиминecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "минекрафтбедрок", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "майнтест", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "модҳоимайнкрафт", "mcc": "mcc", "candleflame": "алан<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ъ", "fru": "хафа", "addons": "иловаҳо", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "майнкрафтпокет", "minecraft360": "minecraft360", "moddedminecraft": "майнкрафтбомод", "minecraftps4": "minecraftps4", "minecraftpc": "майнкрафтпк", "betweenlands": "байниқаламравҳо", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "шаҳримайнкрафт", "pcgamer": "геймерикомпютерӣ", "jeuxvideo": "видеобозӣ", "gambit": "гамбит", "gamers": "геймерҳо", "levelup": "сатҳболо", "gamermobile": "геймермобайл", "gameover": "бозӣтамом", "gg": "гг", "pcgaming": "бозиҳоикомпютерӣ", "gamen": "гейм", "oyunoynamak": "oyunoynak", "pcgames": "бозиҳоикомпютерӣ", "casualgaming": "бозиҳоиоддӣ", "gamingsetup": "танзимотибозӣ", "pcmasterrace": "pcустодон", "pcgame": "бозиикомпютерӣ", "gamerboy": "гамербача", "vrgaming": "бозиҳоиvr", "drdisrespect": "дрдисреспект", "4kgaming": "4kгейминг", "gamerbr": "гeймертҷ", "gameplays": "бозиҳо", "consoleplayer": "бозингарикомпютерӣ", "boxi": "бокси", "pro": "про", "epicgamers": "бозингарониафсонавӣ", "onlinegaming": "бозиҳоионлайн", "semigamer": "нимгеймер", "gamergirls": "гeймeрдухтарон", "gamermoms": "модаронигеймер", "gamerguy": "геймербача", "gamewatcher": "бозибин", "gameur": "геймер", "grypc": "grypc", "rangugamer": "рангугеймер", "gamerschicas": "гeймeр<PERSON><PERSON>т<PERSON><PERSON><PERSON>н", "otoge": "отоге", "dedsafio": "dedsafio", "teamtryhard": "teamмеҳнаткаш", "mallugaming": "mallugaming", "pawgers": "паверҳо", "quests": "саёҳатҳо", "alax": "alax", "avgn": "avgn", "oldgamer": "бозингарикалонсол", "cozygaming": "бозиҳоидилнишин", "gamelpay": "gamelpay", "juegosdepc": "бозиҳоикомпютерӣ", "dsswitch": "dsswitch", "competitivegaming": "бозиҳоирақобатӣ", "minecraftnewjersey": "майнкрафтнюҷерси", "faker": "қалбакӣ", "pc4gamers": "pc4геймерон", "gamingff": "гейминг<PERSON>ф", "yatoro": "ятору", "heterosexualgaming": "бозиҳоигетеро", "gamepc": "бозипк", "girlsgamer": "духтаронигеймер", "fnfmods": "fnfмодҳо", "dailyquest": "вазифаирӯзона", "gamegirl": "духтарибозигар", "chicasgamer": "духтаронигеймер", "gamesetup": "танзимотибозӣ", "overpowered": "қудратманд", "socialgamer": "геймериҷтимоӣ", "gamejam": "gamejam", "proplayer": "профессионалиҳуш", "roleplayer": "рольбоз", "myteam": "да<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "republicofgamers": "ҷумҳурииgamerҳо", "aorus": "aorus", "cougargaming": "гейминтайкужа", "triplelegend": "легендасекарата", "gamerbuddies": "дӯстониgamer", "butuhcewekgamers": "духтаронигеймерлозим", "christiangamer": "христианбозингар", "gamernerd": "геймернерд", "nerdgamer": "нердгеймер", "afk": "afk", "andregamer": "andregamer", "casualgamer": "gamer_casual", "89squad": "89squad", "inicaramainnyagimana": "чӣхелабозӣмекунӣ", "insec": "худбовариро", "gemers": "гемерҳо", "oyunizlemek": "тамошоибозӣ", "gamertag": "геймертег", "lanparty": "лонпарти", "videogamer": "геймер", "wspólnegranie": "бозиимуштарак", "mortdog": "mortdog", "playstationgamer": "геймериplaystation", "justinwong": "ҷастинвонг", "healthygamer": "геймерисолим", "gtracing": "gtracing", "notebookgamer": "геймерибад<PERSON>фтар", "protogen": "протоген", "womangamer": "гeймeрдyxтap", "obviouslyimagamer": "албатт<PERSON>г<PERSON>ймерам", "mario": "марио", "papermario": "қоғазимарио", "mariogolf": "голфимарио", "samusaran": "самусаран", "forager": "ҷамъкунанда", "humanfallflat": "одамафтодҳамвор", "supernintendo": "супернинтендо", "nintendo64": "nintendo64", "zeroescape": "саломатазсифр", "waluigi": "валуиҷӣ", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "мусиқииниттендо", "sonicthehedgehog": "ҷуҷаимолотӣсоник", "sonic": "соник", "fallguys": "fallguys", "switch": "свич", "zelda": "zelda", "smashbros": "смэшброс", "legendofzelda": "легендаозелда", "splatoon": "splatoon", "metroid": "метроид", "pikmin": "пикмин", "ringfit": "рингфит", "amiibo": "amiibo", "megaman": "мегамен", "majorasmask": "маскамайора", "mariokartmaster": "устодимариокарт", "wii": "wii", "aceattorney": "воқилиҷаз", "ssbm": "ssbm", "skychildrenofthelight": "кӯдаконосмонрӯшноӣ", "tomodachilife": "зинда<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "ahatintime": "адолатвақташрасид", "tearsofthekingdom": "ашкҳоишоҳигарӣ", "walkingsimulators": "симуляторҳоипиёдагардӣ", "nintendogames": "бозиҳоинintendo", "thelegendofzelda": "афсонаизелда", "dragonquest": "dragonquest", "harvestmoon": "моҳибарорат", "mariobros": "мариобродарон", "runefactory": "runefactory", "banjokazooie": "банҷоказуи", "celeste": "селесте", "breathofthewild": "нафасисаҳро", "myfriendpedro": "дӯстаммупедро", "legendsofzelda": "афсонаҳоизелда", "donkeykong": "donkeykong", "mariokart": "мариокарт", "kirby": "кирби", "51games": "51бозӣ", "earthbound": "за<PERSON><PERSON><PERSON><PERSON><PERSON>р", "tales": "қиссаҳо", "raymanlegends": "raymanlegends", "luigismansion": "қасримарғонаиилуиҷи", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "супермариоброс", "mariomaker2": "марио2месозад", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendoчиле", "tloz": "tloz", "trianglestrategy": "стратегияисекунҷа", "supermariomaker": "созандаисупермарио", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "супермарио64", "conkersbadfurday": "ҷумъаипашмолудиконкерс", "nintendos": "nintendoҳо", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "ҷанговаронихайрул", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "мариовасоник", "banjotooie": "банҷотуи", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "мариовалуиҷи", "mariorpg": "марiorpg", "zeldabotw": "зелдаботw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "ривен", "ahri": "аҳри", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "арам", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "гургҳоисурх", "vanillalol": "ванилӣлол", "wildriftph": "wildriftph", "lolph": "<PERSON>андам", "leagueoflegend": "лигаилегенда", "tốcchiến": "ҷангисуръат", "gragas": "грагас", "leagueoflegendswild": "лигаиафсонавиёнвайлд", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsиспания", "aatrox": "aatrox", "euw": "эвв", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "кайле", "samira": "самира", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "а<PERSON><PERSON><PERSON><PERSON>", "milio": "милион", "shaco": "шако", "ligadaslegendas": "лигаиафсонаҳо", "gaminglol": "гейминглол", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "дарвозаҳоишашкунҷа", "hextech": "ҳекстех", "fortnitegame": "фортнайтгейм", "gamingfortnite": "гейминфор<PERSON>найт", "fortnitebr": "фортнайтбр", "retrovideogames": "видеобозиҳоиретро", "scaryvideogames": "бозиҳоивидеоиитарсовар", "videogamemaker": "созандаибозиҳоивидеоӣ", "megamanzero": "мегамэнзеро", "videogame": "видеобозӣ", "videosgame": "видеобозӣ", "professorlayton": "профессорлейтон", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "бозиҳоиаркада", "acnh": "acnh", "puffpals": "puffрафиқон", "farmingsimulator": "симуляторикишоварзӣ", "robloxchile": "robloxчиле", "roblox": "roblo<PERSON>", "robloxdeutschland": "роблоксолмон", "robloxdeutsch": "robloxтоҷикӣ", "erlc": "erlc", "sanboxgames": "бозиҳоисандбокс", "videogamelore": "донишиҳоякомпютерӣ", "rollerdrome": "роллердром", "parasiteeve": "паразитқайд", "gamecube": "геймкуб", "starcraft2": "starcraft2", "duskwood": "ҷангалиторик", "dreamscape": "манзараирӯё", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "фазоимурда", "amordoce": "ишқиширин", "videogiochi": "видеобозиҳо", "theoldrepublic": "ҷумҳурияткӯҳна", "videospiele": "видеобозиҳо", "touhouproject": "лоиҳаитоухоу", "dreamcast": "dreamcast", "adventuregames": "бозиҳоисаргузашт", "wolfenstein": "волфенштайн", "actionadventure": "саргузаштиҷангӣ", "storyofseasons": "қиссаифаслҳо", "retrogames": "бозиҳоиретро", "retroarcade": "ретроаркада", "vintagecomputing": "компютерҳоикӯҳна", "retrogaming": "бозиҳоиретро", "vintagegaming": "гейминги_винтажӣ", "playdate": "вохӯрии_кӯдакона", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "буг<PERSON><PERSON><PERSON>с", "injustice2": "беадолатӣ2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "бозииосмон", "zenlife": "ҳаётизен", "beatmaniaiidx": "beatmaniaiidx", "steep": "тунд", "mystgames": "бозиҳоисирри", "blockchaingaming": "бозиҳоиблокчейн", "medievil": "асримиёна", "consolegaming": "бозиконсол", "konsolen": "консолҳо", "outrun": "пешидавон", "bloomingpanic": "гулофшониваҳима", "tobyfox": "tobyfox", "hoyoverse": "ҳойоверс", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "бозиҳоитарсовар", "monstergirlquest": "саволидухтариаҷоиб", "supergiant": "суперкалон", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "бозиҳоидеҳқонӣ", "juegosviejos": "бозиҳоикӯҳна", "bethesda": "бетесда", "jackboxgames": "jackboxgames", "interactivefiction": "адабиётиинтерактивӣ", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "ошиқонимағзҳо", "visualnovel": "романиграфикӣ", "visualnovels": "романҳоитасвирӣ", "rgg": "rgg", "shadowolf": "сояигурги", "tcrghost": "tcrghost", "payday": "рӯзимузд", "chatherine": "чате<PERSON><PERSON>н", "twilightprincess": "шоҳдухтарифурӯбшуда", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "қуттии_рег", "aestheticgames": "бозиҳоизебо", "novelavisual": "романивизуалӣ", "thecrew2": "thecrew2", "alexkidd": "алекскидд", "retrogame": "бозиҳоиретро", "tonyhawkproskater": "тонихоукпрофессионалскейтер", "smbz": "smbz", "lamento": "lamento", "godhand": "дастиҳудо", "leafblowerrevolution": "инқилобибаргпарронак", "wiiu": "wiiu", "leveldesign": "тарҳрезиисатҳ", "starrail": "starrail", "keyblade": "калид<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "aplaguetale": "офатиқисса", "fnafsometimes": "фнафбаъзевақтҳо", "novelasvisuales": "романҳоивизуалӣ", "robloxbrasil": "robloxтоҷикистон", "pacman": "пакман", "gameretro": "бозиҳоиретро", "videojuejos": "videoju<PERSON><PERSON>", "videogamedates": "санаҳоидарбозиҳоивидеоӣ", "mycandylove": "ишқиширинам", "megaten": "мегатен", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "танҳобароихотир3", "hulkgames": "бозиҳоихалк", "batmangames": "бозиҳоибэтмен", "returnofreckoning": "бозгаштиҳисобрасӣ", "gamstergaming": "геймергейминг", "dayofthetantacle": "рӯзихартум", "maniacmansion": "хонаиҷинниҳо", "crashracing": "мусобиқаипуртасодум", "3dplatformers": "платформерҳои3d", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "бозиҳоизамонигузашта", "hellblade": "шамшериҷаҳаннам", "storygames": "бозиҳоибаҳикоя", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "овозҷаббарӣ", "beyondtwosouls": "беруназдуҷон", "gameuse": "бозиб<PERSON>з", "offmortisghost": "offmortisghost", "tinybunny": "харгӯшакикамона", "retroarch": "retroarch", "powerup": "қувватгир", "katanazero": "katanazero", "famicom": "фамиком", "aventurasgraficas": "саргузаштҳоиграфикӣ", "quickflash": "лаҳзавӣ", "fzero": "фзеро", "gachagaming": "гачабозӣ", "retroarcades": "ретроаркадаҳо", "f123": "f123", "wasteland": "вайроназамин", "powerwashsim": "симуляторишустшӯй", "coralisland": "ҷазираимарҷон", "syberia3": "сибир3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "дун<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "футболомезӣ", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "фазонавард", "legomarvel": "легомарвел", "wranduin": "вайрондуин", "twistedmetal": "металлипечида", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "ҷилдикисборинанг", "simulator": "симулятор", "symulatory": "симуляторҳо", "speedrunner": "speedrunner", "epicx": "эпикx", "superrobottaisen": "суперроботҷанг", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "бозиит<PERSON><PERSON><PERSON><PERSON>нак", "wonderlandonline": "аҷоиботонлайн", "skylander": "skylander", "boyfrienddungeon": "ҷанговаридӯстдор", "toontownrewritten": "too<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "симрей<PERSON>инг", "simrace": "симрейс", "pvp": "pvp", "urbanchaos": "шӯришишаҳрӣ", "heavenlybodies": "ҷисмҳоиосмонӣ", "seum": "ҳасад", "partyvideogames": "бозиҳоивидеоӣбарои", "graveyardkeeper": "муҳофизигӯристон", "spaceflightsimulator": "симуляторипарвозифазоӣ", "legacyofkain": "мероситоичон", "hackandslash": "ҳакандслэш", "foodandvideogames": "хӯрокувидеобозиҳо", "oyunvideoları": "видеоҳоибозӣ", "thewolfamongus": "гургдарбайнимо", "truckingsimulator": "симуляториронандагӣ", "horizonworlds": "ҷаҳонҳоиуфуқ", "handygame": "бозии_дастрас", "leyendasyvideojuegos": "афсонаҳоивидеобозиҳо", "oldschoolvideogames": "бозиҳоивидеоиикӯҳна", "racingsimulator": "симуляториронандагӣ", "beemov": "bee<PERSON>v", "agentsofmayhem": "агентонипарешонӣ", "songpop": "сурудпоп", "famitsu": "фамитсу", "gatesofolympus": "дарвозаҳоиолимп", "monsterhunternow": "шикориҳозириаҷуба", "rebelstar": "ситораибоғӣ", "indievideogaming": "бозиҳоивидеоииндӣ", "indiegaming": "бозиҳоиинди", "indievideogames": "бозиҳоивидеоииндӣ", "indievideogame": "бозиҳоивидеоииндӣ", "chellfreeman": "челлфриман", "spidermaninsomniac": "spidermanбехобӣ", "bufffortress": "қалъаизӯрманд", "unbeatable": "бебар<PERSON>б<PERSON>р", "projectl": "лоиҳа", "futureclubgames": "бозиҳоиклубиоянда", "mugman": "кружкачаи", "insomniacgames": "бозиҳоибехобӣ", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "бозии_селест", "aperturescience": "aperturescience", "backlog": "бозмондагӣ", "gamebacklog": "бозиҳоинаохир", "gamingbacklog": "бозиҳоинабозидашуда", "personnagejeuxvidéos": "персонажиҳоибозӣ", "achievementhunter": "дастовардҷӯ", "cityskylines": "силуэтҳоишаҳрӣ", "supermonkeyball": "supermonkeyball", "deponia": "депония", "naughtydog": "сагибадқавл", "beastlord": "подшоҳизавр", "juegosretro": "бозиҳоиретро", "kentuckyroutezero": "кентуккирутзеро", "oriandtheblindforest": "oriиҷангалинобино", "alanwake": "alanwake", "stanleyparable": "стэнлипарабл", "reservatoriodedopamin": "захира<PERSON>дофамин", "staxel": "staxel", "videogameost": "саундтрекибозиҳо", "dragonsync": "ҳамоҳангиажнаҳо", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "манкофxvдӯстмедорам", "arcanum": "асрор", "neoy2k": "neoy2k", "pcracing": "pcracing", "berserk": "берсерк", "baki": "баки", "sailormoon": "сейлормун", "saintseiya": "сентсейя", "inuyasha": "инуяша", "yuyuhakusho": "юююхакушо", "initiald": "initiald", "elhazard": "элҳазард", "dragonballz": "dragonballz", "sadanime": "анимеигамгин", "darkerthanblack": "торикӣаздашсиёҳтар", "animescaling": "аниме_миқёс", "animewithplot": "анимебосюжет", "pesci": "песси", "retroanime": "аниметарихӣ", "animes": "анимеҳо", "supersentai": "суперсентай", "samuraichamploo": "samuraichamploo", "madoka": "мадока", "higurashi": "ҳигураши", "80sanime": "80аниме", "90sanime": "90аниме", "darklord": "сардорисиёҳ", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "устодипогӣ", "samuraix": "самурайx", "dbgt": "dbgt", "veranime": "верааниме", "2000sanime": "аниме2000", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "дрстоунмавсими1", "rapanime": "рэпаниме", "chargemanken": "кенчардкун", "animecover": "анимекавер", "thevisionofescaflowne": "thevisionofescaflowne", "slayers": "қотилон", "tokyomajin": "tokyomajin", "anime90s": "аниме90ум", "animcharlotte": "аним<PERSON><PERSON><PERSON><PERSON><PERSON>отта", "gantz": "gantz", "shoujo": "сёдзё", "bananafish": "моҳиибанан", "jujutsukaisen": "ҷуҷутсукайсен", "jjk": "jjk", "haikyu": "ҳайкю", "toiletboundhanakokun": "ҳанакокунидастшӯӣ", "bnha": "bnha", "hellsing": "ҳелсинг", "skipbeatmanga": "скипбитманга", "vanitas": "суръати_зудгузар", "fireforce": "оташнерӯ", "moriartythepatriot": "мориартипатриот", "futurediary": "дневникоянда", "fairytail": "афсона", "dorohedoro": "дорохедоро", "vinlandsaga": "винландсага", "madeinabyss": "сохташудадарубрӯ", "parasyte": "паразит", "punpun": "пунпун", "shingekinokyojin": "shingekinokyojin", "mushishi": "мушиши", "beastars": "бист<PERSON><PERSON><PERSON>", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "садои<PERSON><PERSON><PERSON><PERSON>на", "kamisamakiss": "бӯсаихудо", "blmanga": "blmanga", "horrormanga": "мангаидаҳшат", "romancemangas": "мангаҳоиошиқона", "karneval": "кар<PERSON>вал", "dragonmaid": "хизматкоридраконӣ", "blacklagoon": "ҳавзисиёҳ", "kentaromiura": "кентаромиура", "mobpsycho100": "моб100", "terraformars": "terraformars", "geniusinc": "гениу<PERSON><PERSON><PERSON>к", "shamanking": "шамонкинг", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "бунгострейдогс", "jujustukaisen": "ҷуҷутсукайсен", "jujutsu": "ҷуҷутсу", "yurionice": "yurionice", "acertainmagicalindex": "индексиҷодуииmuайян", "sao": "сао", "blackclover": "blackclover", "tokyoghoul": "токиогул", "onepunchman": "якмуштодам", "hetalia": "хеталия", "kagerouproject": "kagerouproject", "haikyuu": "ха<PERSON><PERSON>ю", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "сириусохотникзавампирами", "spyxfamily": "ҷосусхонавода", "rezero": "резеро", "swordartonline": "сао", "dororo": "дороро", "wondereggpriority": "wondereggpriority", "angelsofdeath": "фариштаҳоимарг", "kakeguri": "какегури", "dragonballsuper": "драгонболлсупер", "hypnosismic": "гипнозмик", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "духтариаҷоиб", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "анимеиварзишӣ", "sukasuka": "сукасука", "arwinsgame": "бозииа<PERSON><PERSON>ин", "angelbeats": "angelbeats", "isekaianime": "исекайаниме", "sagaoftanyatheevil": "достонитаняибадкор", "shounenanime": "шоуненаниме", "bandori": "бандори", "tanya": "таня", "durarara": "дурарара", "prettycure": "prettycure", "theboyandthebeast": "писарувадаҳшат", "fistofthenorthstar": "шимшерипанҷаситора", "mazinger": "мазин<PERSON><PERSON>р", "blackbuttler": "blackbuttler", "towerofgod": "бурҷихудо", "elfenlied": "элфенлид", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "чиби", "servamp": "хизматгор_вампир", "howtokeepamummy": "чӣтавртанашкиламумияро", "fullmoonwosagashite": "моҳипурраҷӯёнем", "shugochara": "шугочара", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "зеборухофнок", "martialpeak": "қуллаимубориза", "bakihanma": "бакиханма", "hiscoregirl": "духтариб<PERSON>холбаланд", "orochimaru": "орочимару", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "рухипарӣ", "shinji": "шинҷи", "zerotwo": "дунол", "inosuke": "иносуке", "nezuko": "незуко", "monstergirl": "ду<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "мицуки", "kakashi": "какаши", "lenore": "ленор", "benimaru": "бенимару", "saitama": "саитама", "sanji": "санҷи", "bakugo": "бакуго", "griffith": "гриффит", "ririn": "рир<PERSON>н", "korra": "корра", "vanny": "вонна", "vegeta": "вегета", "goromi": "горomi", "luci": "люси", "reigen": "рейген", "scaramouche": "скарамуш", "amiti": "дӯстӣ", "sailorsaturn": "сейлорсатурн", "dio": "дио", "sailorpluto": "сайлорплуто", "aloy": "алой", "runa": "руна", "oldanime": "анимеикӯҳна", "chainsawman": "chainsawman", "bungoustraydogs": "итҳарнавардонисаргардон", "jogo": "jogo", "franziska": "franziska", "nekomimi": "некомими", "inumimi": "инумими", "isekai": "исекай", "tokyorevengers": "токиоинтиқомгирандагон", "blackbutler": "дворецкоисиёҳ", "ergoproxy": "эргопрокси", "claymore": "клеймор", "loli": "лоли", "horroranime": "анимеидаҳшат", "fruitsbasket": "мева<PERSON><PERSON><PERSON><PERSON><PERSON>д", "devilmancrybaby": "шайтонмардодаккагиря", "noragami": "норагами", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "сейнен", "lovelive": "зиндагиибоишқ", "sakuracardcaptor": "сакуракартҷамъкунанда", "umibenoetranger": "умибеноэтранже", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "ваъдадодашудаҳеҷгоҳзамин", "monstermanga": "мангаидаҳшатнок", "yourlieinapril": "дурӯғиапрелат", "buggytheclown": "баггиҷанги", "bokunohero": "бокунохеро", "seraphoftheend": "серафими<PERSON><PERSON><PERSON>р", "trigun": "тригун", "cyborg009": "cyborg009", "magi": "ҷодугар", "deepseaprisoner": "маҳбусибаҳриамиқ", "jojolion": "jojo<PERSON>", "deadmanwonderland": "мамлакатиаҷоиботимурда", "bannafish": "ба<PERSON><PERSON><PERSON><PERSON>", "sukuna": "суукуна", "darwinsgame": "бозиида<PERSON>вин", "husbu": "ҳасбу", "sugurugeto": "сугуругето", "leviackerman": "левиакерман", "sanzu": "санзу", "sarazanmai": "саразан<PERSON>ай", "pandorahearts": "дилҳоипандора", "yoimiya": "ёимия", "foodwars": "ҷангиғизо", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "столас", "devilsline": "ха<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>он", "toyoureternity": "тоабадият", "infpanime": "infpаниме", "eleceed": "элисид", "akamegakill": "akamegakill", "blueperiod": "дав<PERSON><PERSON><PERSON><PERSON><PERSON>буд", "griffithberserk": "griffithberserk", "shinigami": "шинигами", "secretalliance": "иттифоқимахфӣ", "mirainikki": "дафтарчаиоянда", "mahoutsukainoyome": "маҳоутсукаинойоме", "yuki": "юки", "erased": "нестшуда", "bluelock": "bluelock", "goblinslayer": "қотилигоблин", "detectiveconan": "детективконан", "shiki": "шики", "deku": "деку", "akitoshinonome": "акитошиноном", "riasgremory": "риасгремори", "shojobeat": "шоҷобит", "vampireknight": "рыцарьвампир", "mugi": "муги", "blueexorcist": "экзорсистикабуд", "slamdunk": "сламданк", "zatchbell": "<PERSON><PERSON>bell", "mashle": "машл", "scryed": "скрайд", "spyfamily": "ҷосусиоила", "airgear": "airgear", "magicalgirl": "духтарисеҳрнок", "thesevendeadlysins": "ҳафтгуноҳимарговар", "prisonschool": "мактабизиндон", "thegodofhighschool": "худоимактабимиёна", "kissxsis": "хоҳарпарастӣ", "grandblue": "грандблю", "mydressupdarling": "либосбозиамман", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "розенмайден", "animeuniverse": "аниметавон", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "сакмухтасар", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "боччирок", "kakegurui": "какегуруи", "mobpyscho100": "моб100", "hajimenoippo": "hajimenoippo", "undeadunluck": "мурданашансӣ", "romancemanga": "романсманга", "blmanhwa": "blмангаваманҳва", "kimetsunoyaba": "kimetsunoyaba", "kohai": "кохай", "animeromance": "анимеишқ", "senpai": "сенпай", "blmanhwas": "blмангаҳо", "animeargentina": "анимеаргентина", "lolicon": "лоликон", "demonslayertothesword": "қотилишайтонтошамшер", "bloodlad": "bloodlad", "goodbyeeri": "хайрэри", "firepunch": "зарба<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "adioseri": "хайрэрӣ", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "кинн<PERSON><PERSON>у", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "шуҷоай", "starsalign": "ситораҳомезанд", "romanceanime": "аниме_ошиқона", "tsundere": "сундере", "yandere": "яндере", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "кенга<PERSON>шура", "saointegralfactor": "saointegralfactor", "cherrymagic": "сеҳричаноб", "housekinokuni": "хонаикинокуни", "recordragnarok": "сабтирагнарок", "oyasumipunpun": "ояасумипунпун", "meliodas": "мелиодас", "fudanshi": "фудан<PERSON>и", "retromanga": "мангаиретро", "highschoolofthedead": "мактабимурдагон", "germantechno": "техногерманӣ", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "виндландсага", "mangaka": "мангака", "dbsuper": "dbsuper", "princeoftennis": "шоҳзодаитеннис", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "эсдет", "dokurachan": "да<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "синфиқотилон", "animemanga": "анимеманга", "bakuman": "бакуман", "deathparade": "парадимарг", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "анимеиҷопонӣ", "animespace": "аниме", "girlsundpanzer": "духтаронватанкҳо", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON>", "animedub": "аниме_дубляж", "animanga": "аниманга", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "инди_аниме", "bungoustray": "бунгоустрей", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "gundam0": "gundam0", "animescifi": "анимефантастика", "ratman": "одамкаламуш", "haremanime": "ҳареманиме", "kochikame": "кочикаме", "nekoboy": "некобой", "gashbell": "гашбелл", "peachgirl": "духтарчошаҳлӯ", "cavalieridellozodiaco": "наворонигузорандагонизодиак", "mechamusume": "мехамусуме", "nijigasaki": "нижигасаки", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "дили<PERSON><PERSON><PERSON><PERSON>a", "deliciousindungeon": "дарзиндонлазиз", "manhviyaoi": "манҳвиёй", "recordofragnarok": "рекордрагнарок", "funamusea": "funamusea", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "мангааниме", "bochitherock": "бочитҳерок", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "ҷастухезбалоферҳо", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "дар<PERSON><PERSON><PERSON>душворзиёд", "overgeared": "бароба<PERSON><PERSON><PERSON><PERSON>т", "toriko": "торико", "ravemaster": "равемастер", "kkondae": "kkondae", "chobits": "чобитс", "witchhatatelier": "устохонаисеҳргар", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "сангатсунолион", "kamen": "камен", "mangaislife": "мангаинҳаёт", "dropsofgod": "қатраҳоихудо", "loscaballerosdelzodia": "шоҳсаворонизодиак", "animeshojo": "анимешоҷо", "reverseharem": "реверсҳарем", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "муаллимибузургонизука", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "солдат", "mybossdaddy": "падарамбосам", "gear5": "гир5", "grandbluedreaming": "орзуҳоиобиибузург", "bloodplus": "хунплюс", "bloodplusanime": "bloodplusанимэ", "bloodcanime": "хунканиме", "bloodc": "ху<PERSON><PERSON><PERSON><PERSON>", "talesofdemonsandgods": "қиссаҳоидеваҳовохудоён", "goreanime": "гореаниме", "animegirls": "духтаронианиме", "sharingan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crowsxworst": "қарғаҳохудтарин", "splatteranime": "ани<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н", "splatter": "пошидан", "risingoftheshieldhero": "қаҳрамонисипардор", "somalianime": "аниметоҷикӣ", "riodejaneiroanime": "риодежанейроаниме", "slimedattaken": "ликтудоддардодам", "animeyuri": "аниме_юри", "animeespaña": "анимеиспания", "animeciudadreal": "анимешаҳривоқеӣ", "murim": "мурим", "netjuunosusume": "netjuuносусуме", "childrenofthewhales": "кӯдаконикитҳо", "liarliar": "дурӯғгӯйдурӯғгӯй", "supercampeones": "қаҳрамононисупер", "animeidols": "аниме_идолҳо", "isekaiwasmartphone": "исекайбосмартфон", "midorinohibi": "рӯзҳоисабз", "magicalgirls": "духтаронисеҳрнок", "callofthenight": "садоишаб", "bakuganbrawler": "ҷангҷӯибакуган", "bakuganbrawlers": "бакуганбравлерс", "natsuki": "натсуки", "mahoushoujo": "духтарисеҳрнок", "shadowgarden": "боғипинҳонӣ", "tsubasachronicle": "tsubasachronicle", "findermanga": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON>", "princessjellyfish": "шоҳдухтармедуза", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "бӯсаибиҳишт", "kurochan": "курочан", "revuestarlight": "ситораисаҳна", "animeverse": "анимеолам", "persocoms": "персокомҳо", "omniscientreadersview": "назариякитобхониҳамачидон", "animecat": "гурбакианиме", "animerecommendations": "тавсияҳоианиме", "openinganime": "анимеикушодан", "shinichirowatanabe": "шиничировотанабе", "uzumaki": "узумаки", "myteenromanticcomedy": "комедияиромантикииман", "evangelion": "евангелион", "gundam": "гандам", "macross": "мак<PERSON><PERSON><PERSON>с", "gundams": "гундамҳо", "voltesv": "волтесв", "giantrobots": "роботҳоикалон", "neongenesisevangelion": "неонгенезисевангелион", "codegeass": "кодгиас", "mobilefighterggundam": "мобайл<PERSON>айтергандам", "neonevangelion": "неонэвангелион", "mobilesuitgundam": "мобил<PERSON><PERSON><PERSON>тгундам", "mech": "механик", "eurekaseven": "эврикахафт", "eureka7": "эврика7", "thebigoanime": "thebigoanime", "bleach": "рангбар", "deathnote": "да<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventure", "fullmetalalchemist": "алхимикипурраифулузӣ", "ghiaccio": "ях", "jojobizarreadventures": "ҷоҷосаргузаштиаҷиб", "kamuiyato": "камуиято", "militaryanime": "анимеиҳарбӣ", "greenranger": "рейнҷарисабз", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "токиорев", "zorro": "зорро", "leonscottkennedy": "leonscottkennedy", "korosensei": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starfox": "старфокс", "ultraman": "ултраман", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "аниме_шаҳр", "animetamil": "анимэтамил", "jojoanime": "ҷоҷоаниме", "naruto": "наруто", "narutoshippuden": "наруточӯҷонбозан", "onepiece": "якпорча", "animeonepiece": "аниме_ванпис", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "юзиё", "digimon": "дигимон", "digimonadventure": "саргузаштидигимон", "hxh": "hxh", "highschooldxd": "мактабимиёнаdxd", "goku": "гоку", "broly": "броли", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "академияикаҳрамонониман", "jujustukaitsen": "ҷуҷутсукайсен", "drstone": "дрстоун", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "шоненҷамп", "otaka": "отака", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "қотилишайтон", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "эренйегер", "myheroacademia": "қаҳрамониакадемияиман", "boruto": "боруто", "rwby": "rwby", "dandadan": "да<PERSON><PERSON>а<PERSON><PERSON>н", "tomodachigame": "tomodachigame", "akatsuki": "акацуки", "surveycorps": "корпусипурсиш", "onepieceanime": "анимеиякпорча", "attaquedestitans": "ҳуҷумититанҳо", "theonepieceisreal": "ванписҳақиқиаст", "revengers": "интиқомгирон", "mobpsycho": "мобпсихо", "aonoexorcist": "экзорсистиобий", "joyboyeffect": "таъ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimonstory": "ҳикояидигимон", "digimontamers": "дигимонтамерс", "superjail": "супермаҳбас", "metalocalypse": "металокалипсис", "shinchan": "шин<PERSON><PERSON>н", "watamote": "ватамоте", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "уруseиятсуара", "gintama": "гинтама", "ranma": "ранма", "doraemon": "дораэмон", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "вебтунибеайб", "kemonofriends": "дӯстониҳайвонот", "utanoprincesama": "utanoprincesama", "animecom": "анимеком", "bobobobobobobo": "бобобобобобобо", "yuukiyuuna": "юук<PERSON>ю<PERSON>на", "nichijou": "ничиҷоу", "yurucamp": "кемпингиосон", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "ҷодугарипарвозкунанда", "wotakoi": "вотакой", "konanime": "конаниме", "clannad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justbecause": "ҳаминтавр", "horimiya": "хоримия", "allsaintsstreet": "кӯчаиҳамаавлиёҳо", "recuentosdelavida": "ҳикояҳоиҳаёт"}