{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "astrolojia", "cognitivefunctions": "fonctionscognitives", "psychology": "psikolojia", "philosophy": "filozofia", "history": "tantara", "physics": "fizika", "science": "si<PERSON>a", "culture": "kolo<PERSON><PERSON>a", "languages": "teny", "technology": "teknolojia", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "memesastrolojia", "enneagrammemes": "memesennéagramme", "showerthoughts": "hevitramandroandro", "funny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videos": "horonantsary", "gadgets": "<PERSON><PERSON><PERSON>", "politics": "politika", "relationshipadvice": "to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lifeadvice": "torohevitra", "crypto": "crypto", "news": "vaovao", "worldnews": "vaovaoanerantany", "archaeology": "arkeolojia", "learning": "<PERSON><PERSON><PERSON>", "debates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "universe": "univers", "meditation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mythology": "angano", "art": "<PERSON><PERSON><PERSON>", "crafts": "zavatanana", "dance": "dihy", "design": "design", "makeup": "makiazy", "beauty": "<PERSON><PERSON><PERSON>", "fashion": "la<PERSON><PERSON>", "singing": "<PERSON><PERSON>a", "writing": "fanoratana", "photography": "sary", "cosplay": "cosplay", "painting": "fando<PERSON><PERSON>", "drawing": "fanao<PERSON>_sary", "books": "boky", "movies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "poetry": "<PERSON>on<PERSON><PERSON>", "television": "televiziona", "filmmaking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animation": "<PERSON><PERSON><PERSON><PERSON>", "anime": "anime", "scifi": "scifi", "fantasy": "nofinofy", "documentaries": "dokumentera", "mystery": "mistery", "comedy": "<PERSON><PERSON><PERSON>", "crime": "heloka", "drama": "resaka", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "horora", "romance": "<PERSON><PERSON><PERSON>", "realitytv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "hets<PERSON>", "music": "mozika", "blues": "blues", "classical": "klasika", "country": "<PERSON><PERSON><PERSON>", "desi": "desi", "edm": "edm", "electronic": "elektronika", "folk": "folklôra", "funk": "funk", "hiphop": "hiphop", "house": "trano", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latina", "metal": "metaly", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "techno", "travel": "dia", "concerts": "<PERSON><PERSON><PERSON><PERSON>", "festivals": "fetibe", "museums": "tranombakiteny", "standup": "<PERSON><PERSON><PERSON>", "theater": "teatra", "outdoors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gardening": "fambolena", "partying": "fety", "gaming": "kilalao", "boardgames": "kilalaoamboadiny", "dungeonsanddragons": "dungeonsanddragons", "chess": "echec", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "sakafo", "baking": "mofotra", "cooking": "<PERSON><PERSON><PERSON>", "vegetarian": "vegetariana", "vegan": "veganina", "birds": "vorona", "cats": "saka", "dogs": "alika", "fish": "trondro", "animals": "biby", "blacklivesmatter": "nym<PERSON><PERSON><PERSON><PERSON><PERSON>", "environmentalism": "tontolozavaboahary", "feminism": "feminisma", "humanrights": "zondolombelona", "lgbtqally": "lgbtqmpiaro", "stopasianhate": "atsakyfankahalanaaziatika", "transally": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "volunteering": "fan<PERSON><PERSON>sa_antsitrapo", "sports": "fanatanjahantena", "badminton": "badminton", "baseball": "baseball", "basketball": "basykety", "boxing": "ady_totohondry", "cricket": "cricket", "cycling": "bisikileta", "fitness": "fanatanjahantena", "football": "baolina", "golf": "golf", "gym": "gym", "gymnastics": "<PERSON><PERSON><PERSON><PERSON>", "hockey": "hockey", "martialarts": "artmartialy", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skateboarding": "skateboard", "skiing": "ski", "snowboarding": "snowboarding", "surfing": "surf", "swimming": "mi<PERSON><PERSON>o", "tennis": "tennis", "volleyball": "basikety", "weightlifting": "fibobankahozana", "yoga": "yoga", "scubadiving": "anatirano", "hiking": "<PERSON><PERSON><PERSON><PERSON>", "capricorn": "<PERSON>ric<PERSON><PERSON>", "aquarius": "aquarius", "pisces": "pisces", "aries": "bibilavy", "taurus": "taora", "gemini": "gemini", "cancer": "ho<PERSON>miadana", "leo": "leo", "virgo": "vira<PERSON>", "libra": "libra", "scorpio": "scorpion", "sagittarius": "sagittarius", "shortterm": "vetivety", "casual": "<PERSON><PERSON><PERSON>a", "longtermrelationship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "single": "tokanatokana", "polyamory": "polyamory", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "pelaka", "lesbian": "lesbiana", "bisexual": "bisexuelle", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "<PERSON><PERSON><PERSON><PERSON>", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "fangalan<PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "subverse", "legendofspyro": "tantaranispy<PERSON>", "rouguelikes": "rouguelikes", "syberia": "siberia", "rdr2": "rdr2", "spyrothedragon": "spyrotoebiby", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "ton<PERSON><PERSON>amiso<PERSON><PERSON>", "heroesofthestorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "firenentsyfintaranaflanary", "planescape": "planescape", "lordsoftherealm2": "tomponnyfaritra2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "lokompilokotra", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "okage", "juegoderol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "witcher": "witcher", "dishonored": "voa<PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "maloto", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "f<PERSON><PERSON><PERSON>", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive": "mampiditra_anaty", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfantasyoldschool", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "fan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegames": "lalaotantara", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "ocarinaoftime", "yiikrpg": "yiikrpg", "vampirethemasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dimension20": "dimension20", "gaslands": "tanindora<PERSON>", "pathfinder": "mpit<PERSON>kal<PERSON>a", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "hazakazana_aloka", "bloodontheclocktower": "radimpeoramporavampirajirafitra", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "zarampitianynikki", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "tsinjaketraezaka", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "<PERSON><PERSON><PERSON><PERSON>", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yourturntodie": "anjaranaoizayhomaty", "persona3": "persona3", "rpghorror": "rpgmatahotra", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "m<PERSON><PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "texterpg", "genshin": "genshin", "eso": "esomanadro", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "<PERSON><PERSON><PERSON><PERSON>atombô<PERSON>", "gurps": "gurps", "darkestdungeon": "toeranafafymaizina<PERSON>", "eclipsephase": "fototralanana", "disgaea": "disgaea", "outerworlds": "erantanykivetra", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "mpiadynydinastia", "skullgirls": "skullgirls", "nightcity": "tanànalina", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "neverwinter", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "gothamknights", "forgottenrealms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "zanakanyfahazavana", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "mpitaizabibidia", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "v<PERSON>ny", "vulcanverse": "vulcanverse", "fracturedthrones": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "oaziskoa", "hogwartmystery": "mi<PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "asio", "lastepoch": "epokiafarany", "starfinder": "m<PERSON><PERSON><PERSON><PERSON>", "goldensun": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divinityoriginalsin": "divin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bladesinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilight2000": "alakamisy2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "<PERSON><PERSON><PERSON><PERSON>", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "mpandre<PERSON>_devoly", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "divinité", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "al<PERSON><PERSON>_tontolo_taloha", "adventurequest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplayinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy9": "finalfantasy9", "sunhaven": "ma<PERSON><PERSON><PERSON>", "talesofsymphonia": "tantaranyisymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "myfarog", "sacredunderworld": "toeranamasinavaoambany", "chainedechoes": "<PERSON><PERSON><PERSON>", "darksoul": "fanahydamaizina", "soulslikes": "soulslikes", "othercide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "palladiumrpg": "palladiumrpg", "rifts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tibia": "tibia", "thedivision": "thedivision", "hellocharlotte": "salamamianady<PERSON><PERSON><PERSON>", "legendofdragoon": "legendofdragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "vampirolamascarada", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "werewolfnyapocalypse", "aveyond": "aveyond", "littlewood": "<PERSON><PERSON><PERSON><PERSON>", "childrenofmorta": "zafinnymort<PERSON>", "engineheart": "fomototra", "fable3": "tantara3", "fablethelostchapter": "tantaratokomankapotravery", "hiveswap": "hiveswap", "rollenspiel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "edenmandrak<PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "fan<PERSON>oz<PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "gaingainarotsy", "juegosrpg": "lalaorpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "vokatra", "gloomhaven": "gloomhaven", "wildhearts": "fotralambondrantaona", "bastion": "bastion", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "langitrarcadia", "shadowhearts": "f<PERSON><PERSON><PERSON>na", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "<PERSON><PERSON><PERSON>", "breathoffire4": "breathoffire4", "mother3": "reny3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "<PERSON><PERSON><PERSON><PERSON>", "roleplaygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleplaygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fabulaultima": "fabulault<PERSON>", "witchsheart": "fontsyvehivavy", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "forumrpg", "shadowheartscovenant": "fan<PERSON><PERSON><PERSON><PERSON>w<PERSON>", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "awplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theworldendswithyou": "nyfiavanalokoantopimiromanao", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "heresimainty", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "magikatany", "blackbook": "bok<PERSON><PERSON><PERSON>", "skychildrenoflight": "zafikanitrafahazavana", "gryrpg": "gryrpg", "sacredgoldedition": "fandikinazolaitravolamena", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "lalaorpg", "prophunt": "fakanta<PERSON>", "starrails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofmist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indierpg": "rpgindependant", "pointandclick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "tsy_a<PERSON>_<PERSON><PERSON><PERSON><PERSON>a", "freeside": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "<PERSON><PERSON>ym<PERSON><PERSON>atyankanykanada", "palladium": "palladium", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "mpihazambiby", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "jeosip<PERSON>asia", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "rpgtaktika", "mahoyo": "mahoyo", "animegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "mpandanindolo<PERSON>", "diluc": "diluc", "venti": "venti", "eternalsonata": "rehetramankalazamandraka", "princessconnect": "princessconnect", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorant<PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "<PERSON><PERSON><PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "liganympanonofynanareo", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "nofihack", "gaimin": "gaimin", "overwatchleague": "ligaoverwatch", "cybersport": "baolinalaotrainternet", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "<PERSON><PERSON><PERSON><PERSON>", "eracing": "eracing", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "antsasainaina", "left4dead": "matygasygasy", "left4dead2": "left4dead2", "valve": "valva", "portal": "vava<PERSON>", "teamfortress2": "teamfortress2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "simu<PERSON><PERSON><PERSON><PERSON>", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "planetanka<PERSON><PERSON>", "transformice": "transformice", "justshapesandbeats": "endrikasykapoakafotsiny", "battlefield4": "battlefield4", "nightinthewoods": "alinynambanal<PERSON>", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanias", "overcooked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "iraisambinypian<PERSON>", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7a2a", "deadcells": "deadcells", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "lavaka_basy", "stray": "<PERSON><PERSON><PERSON><PERSON>", "battlefield": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield1": "adytonadytonadevolovolo1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "samboranoambodrano", "eyeb": "maso", "blackdesert": "sekoleymainty", "tabletopsimulator": "simu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partyhard": "milanjamafy", "hardspaceshipbreaker": "famakysambosakelysarotra", "hades": "hades", "gunsmith": "mpanefy_basy", "okami": "<PERSON>ami", "trappedwithjester": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "dinkum": "dinkum", "predecessor": "teo_aloha", "rainworld": "tontoloranomandona", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON>", "colonysim": "colonysim", "noita": "noita", "dawnofwar": "tolomanady", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "maintysamainty", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "fitsokakyoba", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "tanànavàozana", "citiesskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "defconmavesatra", "kenopsia": "kenopsia", "virtualkenopsia": "kenopisamaoderana", "snowrunner": "mpan<PERSON><PERSON>_aminny_lanezy", "libraryofruina": "trano_firaketanboky_ruina", "l4d2": "l4d2", "thenonarygames": "nylalaononary", "omegastrikers": "omegastrikers", "wayfinder": "mpit<PERSON>kal<PERSON>a", "kenabridgeofspirits": "kenambridgetampyfanahy", "placidplasticduck": "bibyplastikamiandry", "battlebit": "adybit", "ultimatechickenhorse": "akohosoavalofarany", "dialtown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smileforme": "tsik<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "alikadimpiso", "supermeatboy": "supermeatboy", "tinnybunny": "soavalabitika", "cozygrove": "cozygrove", "doom": "fahoriana", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "lalaofarcrygames", "paladins": "paladins", "earthdefenseforce": "famoretantanyefivelomants<PERSON>tany", "huntshowdown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "famonolavi<PERSON>", "joinsquad": "miditra_ekipa", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "adyntieranampiade", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "sisintanitara<PERSON>", "divisions2": "divisions2", "killzone": "faritanymahafaty", "helghan": "hel<PERSON>", "coldwarzombies": "zombiesfadymangatsiaka", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "<PERSON><PERSON><PERSON><PERSON>", "crosscode": "crosscode", "goldeneye007": "masongavolam_bola007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON>te", "modernwarfare": "ad<PERSON><PERSON><PERSON><PERSON>", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "boarderlands", "owerwatch": "ower<PERSON>", "rtype": "karazanataratasy", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "adybibydia", "worldofwarships": "worldofwarships", "back4blood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "mpamono", "masseffect": "masseffect", "systemshock": "taitra_rafitra", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doometernal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "centuryageofashes": "taonjatoanantandronalav<PERSON>", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "taranakafototra", "enterthegungeon": "id<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "adyntataolamaoderina2", "blackops1": "blackops1", "sausageman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warface": "<PERSON><PERSON><PERSON><PERSON>", "crossfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "atomicheart": "foatomika", "blackops3": "blackops3", "vampiresurvivors": "vampiresvelona", "callofdutybatleroyale": "callofdutybatleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tinytina": "kek<PERSON>ina", "gamepubg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "<PERSON><PERSON><PERSON><PERSON>", "convertstrike": "fandavanahetsihetsy", "warzone2": "adiadyrano2", "shatterline": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackopszombies": "blackopszombies", "bloodymess": "ditsimbambo", "republiccommando": "republiccommando", "elitedangerous": "elitedangerous", "soldat": "<PERSON><PERSON><PERSON><PERSON>", "groundbranch": "groundbranch", "squad": "ekipa", "destiny1": "anjara1", "gamingfps": "lalaovitifirany", "redfall": "redfall", "pubggirl": "pubgzazavavy", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON>", "farlight": "hazavamaizina", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "karamambola2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "pubgu<PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgdel<PERSON><PERSON>", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "savoka<PERSON>dra", "ghostcod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "csplay": "csplay", "unrealtournament": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutydmz": "callofdutydmz", "gamingcodm": "lalaocodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "kapokabasihakafo", "callofdutymw2": "callofdutymw2", "quakechampions": "quakechampions", "halo3": "halo3", "halo": "halo", "killingfloor": "fahafatesangorosy", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "fotsy_manjelanjelatra", "remnant": "sisa", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "mi<PERSON>ina", "halo4": "halo4", "haloreach": "fiezakamangatsiaka", "shadowman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "horohorontany2", "microvolts": "microvolts", "reddead": "matimena", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "conqueronline": "fehezoonline", "dauntless": "tsy_matah<PERSON>ra", "warships": "sambobelona", "dayofdragons": "androndragon<PERSON>", "warthunder": "warthunder", "flightrising": "flightrising", "recroom": "efitranompihaino", "legendsofruneterra": "legendsofruneterra", "pso2": "pso2", "myster": "mistery", "phantasystaronline2": "phantasystaronline2", "maidenless": "tsymbanyvady", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON>", "crossout": "fafao", "agario": "agario", "secondlife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "<PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "thebindingofisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "loto", "newworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "kila<PERSON><PERSON><PERSON><PERSON>", "pirate101": "pirata101", "honorofkings": "honorofkings", "fivem": "fivem", "starwarsbattlefront": "adystar<PERSON>s", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "tanànapony", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "tauriwow", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riotmmo": "riotmmo", "silkroad": "<PERSON><PERSON><PERSON><PERSON>", "spiralknights": "spiralknights", "mulegend": "<PERSON><PERSON><PERSON><PERSON>", "startrekonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "profetandragona", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelsonline": "anjelymivadi<PERSON>", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseenligne", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "blueprotocol", "perfectworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riseonline": "mit<PERSON><PERSON><PERSON>li<PERSON>", "corepunk": "corepunk", "adventurequestworlds": "adventurequestworlds", "flyforfun": "man<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "animaljam", "kingdomofloathing": "fanjakananytsikambokilahy", "cityofheroes": "tanànanmaherifo", "mortalkombat": "mortalkombat", "streetfighter": "mpi<PERSON><PERSON><PERSON><PERSON>", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "lalanmosarymena", "mkdeadlyalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nomoreheroes": "t<PERSON><PERSON>ymaherifointsony", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "thekingoffighters", "likeadragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "lalaoniadytranainy", "blasphemous": "<PERSON><PERSON><PERSON><PERSON>", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "fikapokavaentana", "mugen": "mugen", "warofthemonsters": "adindrakizomby", "jogosdeluta": "ladikaadykely", "cyberbots": "cyberbots", "armoredwarriors": "mpiady_mifono_vy", "finalfight": "<PERSON><PERSON><PERSON><PERSON>", "poweredgear": "poweredgear", "beatemup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killerinstinct": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoffigthers": "mpanjakampiadyady", "ghostrunner": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chivalry2": "chivalry2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "tohinymbolohollovainafahoatra", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "la<PERSON>onsil<PERSON><PERSON>", "silksongnews": "vaovaosilksong", "silksong": "silksong", "undernight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "typelumina": "typelum<PERSON>", "evolutiontournament": "fiadianantsolokaevo", "evomoment": "vetsovetsoboo", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "talesofberseria", "bloodborne": "bloodborne", "horizon": "horizona", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "r<PERSON><PERSON><PERSON>", "uncharted": "<PERSON><PERSON><PERSON>lav<PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "faranynyamintsika", "infamous": "malaza_ratsy", "playstationbuddies": "na<PERSON><PERSON><PERSON><PERSON><PERSON>", "ps1": "ps1", "oddworld": "ton<PERSON>lohafaha<PERSON>", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "helloamiadana", "gta4": "gta4", "gta": "gta", "roguecompany": "roguecompany", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gris": "gris", "trove": "firaketsambatravoadina", "detroitbecomehuman": "detroitlasataolonamg", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touristtrophy": "trofetoristy", "lspdfr": "lspdfr", "shadowofthecolossus": "alokanjombamosary", "crashteamracing": "crashteamracing", "fivepd": "dimypolisy", "tekken7": "tekken7", "devilmaycry": "devilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "milalalanstation", "samuraiwarriors": "mpiady_samurai", "psvr2": "psvr2", "thelastguardian": "nygardianafar<PERSON>", "soulblade": "soulblade", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "<PERSON><PERSON><PERSON><PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "mpiarovafototra", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "bibyfiesta", "warharmmer40k": "warhammer40k", "fightnightchampion": "tolonandryalahadinamata<PERSON><PERSON>", "psychonauts": "psychonauts", "mhw": "mhw", "princeofpersia": "princedepersia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "adyanoloana", "dontstarvetogether": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "<PERSON><PERSON><PERSON><PERSON>", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "mpanovafotrantrano", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "liganympanjaka", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "<PERSON><PERSON>aranahanjamba<PERSON>", "skycotl": "skycotl", "erica": "erica", "ancestory": "razana", "cuphead": "cuphead", "littlemisfortune": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sallyface": "sallyface", "franbow": "franbow", "monsterprom": "fetimpianabe", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "môt<PERSON>", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "fanatikatondrozana", "duckgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thestanleyparable": "thestanleyparable", "towerunite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "occulto": "<PERSON><PERSON><PERSON>", "longdrive": "diarandehalavitrabelavi<PERSON>", "satisfactory": "<PERSON><PERSON><PERSON><PERSON>", "pluviophile": "mpankafizandorana", "underearth": "ambonytany", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "mpit<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkdome": "tafofisimaizina", "pizzatower": "pizzatower", "indiegame": "<PERSON><PERSON><PERSON><PERSON>", "itchio": "itchio", "golfit": "golfy", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "kilalao", "rockpaperscissors": "batofeuillegunting", "trampoline": "trampoline", "hulahoop": "h<PERSON><PERSON><PERSON>", "dare": "<PERSON><PERSON><PERSON><PERSON>", "scavengerhunt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "misafidy<PERSON>", "trueorfalse": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cosygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "sodoku", "juegos": "la<PERSON>o", "mahjong": "mahjong", "jeux": "la<PERSON>o", "simulationgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "kilalaomteny", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "oyun", "interactivegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "fifanijerypijery", "spiele": "la<PERSON>o", "giochi": "la<PERSON>o", "geoguessr": "geoguessr", "iphonegames": "lalaoiphone", "boogames": "boolalao", "cranegame": "<PERSON><PERSON><PERSON><PERSON>", "hideandseek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hopscotch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arcadegames": "<PERSON><PERSON><PERSON><PERSON>", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "<PERSON><PERSON><PERSON><PERSON>", "mindgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "fantar<PERSON><PERSON>on<PERSON><PERSON>", "galagames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancegame": "lalaofifankat<PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "fitenitsakitsaka", "4xgames": "lalao4x", "gamefi": "gamefi", "jeuxdarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopgames": "lalaoambonylata<PERSON><PERSON>", "metroidvania": "metroidvania", "games90": "lalao90", "idareyou": "<PERSON><PERSON>no", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "lalaokihazakazaka", "ets2": "ets2", "realvsfake": "tenavslainga", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "lalaokonnexion", "onlinegames": "lalaohatratoenligne", "jogosonline": "lalaokadyantserasera", "writtenroleplay": "roleplay<PERSON><PERSON><PERSON>", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jenga": "jenga", "wiigames": "<PERSON><PERSON>", "highscore": "isatambony", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "la<PERSON>ob<PERSON><PERSON><PERSON>", "kidsgames": "lalaomiza<PERSON>", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwedisymmainty", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gioco": "gioco", "managementgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "k<PERSON><PERSON>of<PERSON>roanan<PERSON>", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "lalaokiraformula1", "citybuilder": "mpanorinatanàna", "drdriving": "d<PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "lalaotohetsika", "blowgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "fanontania<PERSON>", "gameo": "kilalao", "lasergame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "imessagegames": "<PERSON><PERSON><PERSON><PERSON>", "idlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillintheblank": "fenoin<PERSON>banga", "jeuxpc": "jeuxpc", "rétrogaming": "retrogaming", "logicgames": "la<PERSON><PERSON><PERSON><PERSON>", "japangame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rizzupgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exitgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "5vs5": "5vs5", "rolgame": "kilalar<PERSON>le", "dashiegames": "dashiegames", "gameandkill": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "kniffel", "gamefps": "<PERSON><PERSON><PERSON><PERSON>", "textbasedgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospel", "thiefgame": "la<PERSON>onjan<PERSON>dy", "lawngames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "babyfoot", "tischfußball": "tischfußball", "spieleabende": "spie<PERSON><PERSON>de", "jeuxforum": "jeuxforum", "casualgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "lalaoandosit<PERSON>", "thiefgameseries": "lalaonympangalatra", "cranegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "játék": "<PERSON><PERSON><PERSON><PERSON>", "bordfodbold": "bordfodbold", "jogosorte": "jogosorte", "mage": "mage", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "kilalaoenligne", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "kitapobingo", "randomizer": "randomizer", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "socialdeductiongames": "lalaosivakanas<PERSON>ialy", "dominos": "domino", "domino": "domino", "isometricgames": "lalaoisometrika", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "fahama<PERSON><PERSON>y<PERSON><PERSON><PERSON>", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxvirtuel": "lalaovirtual", "romhack": "romhack", "f2pgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "free2play": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "la<PERSON>of<PERSON><PERSON>a", "gryonline": "gryonline", "driftgame": "lalagadrift", "gamesotomes": "lalaonotom<PERSON>", "halotvseriesandgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mushroomoasis": "oasisholatra", "anythingwithanengine": "na<PERSON><PERSON>ren<PERSON><PERSON><PERSON><PERSON>", "everywheregame": "la<PERSON><PERSON><PERSON><PERSON>", "swordandsorcery": "sabanysyody", "goodgamegiving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jugamos": "<PERSON><PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grykomputerowe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON>", "minaturegames": "<PERSON><PERSON><PERSON><PERSON>", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "gamemodding", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "lalaovidintsobato", "spelletjes": "<PERSON><PERSON><PERSON><PERSON>", "spacenerf": "spacenerf", "charades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "singleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coopgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamed": "<PERSON><PERSON><PERSON>", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdiscord": "mpanjakapiropio", "scrabble": "scrabble", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "camelup": "ramevamakiat<PERSON>", "monopolygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "brettspiele": "<PERSON><PERSON>ova<PERSON>tabat<PERSON>", "bordspellen": "b<PERSON><PERSON><PERSON><PERSON>", "boardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sällskapspel": "kila<PERSON><PERSON><PERSON>a", "planszowe": "kilalaobatrano", "risiko": "risiko", "permainanpapan": "<PERSON><PERSON>oamba<PERSON><PERSON><PERSON>", "zombicide": "zombicide", "tabletop": "tambatralakilasy", "baduk": "baduk", "bloodbowl": "bloodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectfour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroquest": "fit<PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidatavolo": "giochidata<PERSON>lo", "farkle": "farkle", "carrom": "carrom", "tablegames": "<PERSON><PERSON>oambat<PERSON><PERSON>", "dicegames": "lalaorangon<PERSON>", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jocuridesocietate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alpharius": "alpharius", "masaoyunları": "masaoyunları", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "creationludique": "famoronankilalao", "tabletoproleplay": "lalaomiarytaratasy", "cardboardgames": "kilalaoka<PERSON><PERSON><PERSON>", "eldritchhorror": "horot<PERSON><PERSON><PERSON>yolo", "switchboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infinitythegame": "infinitythegame", "kingdomdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yahtzee": "yahtzee", "chutesandladders": "todikatoetika", "társas": "t<PERSON><PERSON><PERSON>", "juegodemesa": "<PERSON><PERSON>oambat<PERSON><PERSON>", "planszówki": "planszów<PERSON>", "rednecklife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardom": "fadupfadup", "applestoapples": "paomaaminnypaoma", "jeudesociété": "kilalaoniaraka", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "imperatrademenombra", "horseopoly": "horseopoly", "deckbuilding": "fanaovanadecks", "mansionsofmadness": "tranontsampona", "gomoku": "gomoku", "giochidatavola": "giochidatavola", "shadowsofbrimstone": "alokanivoajanahary", "kingoftokyo": "mpanjakany<PERSON><PERSON>", "warcaby": "warcaby", "táblajátékok": "<PERSON><PERSON><PERSON>", "battleship": "sambofiady", "tickettoride": "lalanahol<PERSON><PERSON>", "deskovehry": "deskovehry", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "kilalaobampanabato", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "<PERSON><PERSON><PERSON>notant<PERSON>", "starwarslegion": "starwarslegion", "gochess": "<PERSON><PERSON><PERSON>", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "<PERSON><PERSON><PERSON><PERSON>", "arksurvivalevolved": "arksurvivalevolved", "dayz": "andro", "identityv": "identityv", "theisle": "nynosy", "thelastofus": "nyfaritraisany", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "amintsika", "eco": "eko", "monkeyisland": "nosyntsarambo", "valheim": "valheim", "planetcrafter": "mpanefito<PERSON>lo", "daysgone": "andromandeha", "fobia": "fobia", "witchit": "witchit", "pathologic": "<PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7jao", "thelongdark": "nyal<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "ark", "grounded": "voatery", "stateofdecay2": "lanjanylohalavitra2", "vrising": "vrising", "madfather": "rai<PERSON><PERSON><PERSON><PERSON>", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "fiverenanymandrakizay", "pathoftitans": "la<PERSON><PERSON><PERSON>", "frictionalgames": "lalaomifanindry", "hexen": "mpamosavy", "theevilwithin": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "realrac", "thebackrooms": "nyefitranomihit<PERSON><PERSON>", "backrooms": "backrooms", "empiressmp": "empiressmp", "blockstory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thequarry": "nyvatofasiana", "tlou": "tlou", "dyinglight": "fahafahanamaizina", "thewalkingdeadgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofsurvivalgame": "lalaovelontsipaka", "vintagestory": "tantaratranainy", "arksurvival": "velonaaomaint<PERSON>ark<PERSON>r<PERSON>val", "barotrauma": "barotrauma", "breathedge": "mifokafoka", "alisa": "alisa", "westlendsurvival": "mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastsofbermuda": "bibidinybermu<PERSON>", "frostpunk": "frostpunk", "darkwood": "aladomotemainty", "survivalhorror": "<PERSON><PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "voidtrain", "lifeaftergame": "vodila<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "lalaonafamelomana", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "<PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "scpfoundation", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "mitomanianatahotra", "raft": "r<PERSON><PERSON>", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "polymiaty", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "dadabe", "littlenightmares2": "tahotraalavitrafonja2", "signalis": "signalis", "amandatheadventurer": "amandampitsangatsangana", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "lalaovideorust", "outlasttrials": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alienisolation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undawn": "ma<PERSON><PERSON>ovao", "7day2die": "7andro2maty", "sunlesssea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sopravvivenza": "sopravvivenza", "propnight": "alinampropan", "deadisland2": "nosynamatary2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "vampiratsetraikemen", "deathverse": "fah<PERSON>ahan<PERSON><PERSON><PERSON>", "cataclysmdarkdays": "androntsymaintinandongotra", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "mpitsikitsikaalokanstalker", "lifeafter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "vanintsaizim", "clocktower3": "clocktower3", "aloneinthedark": "ireryaoanatyhaizina", "medievaldynasty": "dinastiam<PERSON><PERSON><PERSON>", "projectnimbusgame": "la<PERSON><PERSON><PERSON><PERSON>", "eternights": "al<PERSON><PERSON><PERSON>", "craftopia": "famoronanazavatra", "theoutlasttrials": "nyfitsapanatheoutlast", "bunker": "bunker", "worlddomination": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "mpamonomandidyofisialy", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "mpamonomidina", "warhammer40kcrush": "crushwarhammer40k", "wh40": "wh40", "warhammer40klove": "fitiavana_warhammer40k", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "tiakonys<PERSON><PERSON>", "ilovevindicare": "tiakozahovindicare", "iloveassasinorum": "tiakoassasinorum", "templovenenum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "oficioasesinorum", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "roamihamanaraka", "wingspan": "elatranelatra", "terraformingmars": "teraformingmars", "heroesofmightandmagic": "fankalahanaazydamagika", "btd6": "btd6", "supremecommander": "komandiransambatra", "ageofmythology": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "args": "args", "rime": "rime", "planetzoo": "planetazoo", "outpost2": "tobyvaovao2", "banished": "voaroaka", "caesar3": "caesar3", "redalert": "lanjamena", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "baikonnytompony", "warcraft3": "warcraft3", "eternalwar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strategygames": "lalaotetsika", "anno2070": "anno2070", "civilizationgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization4": "sivilizasiona4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "adintaninarehetra", "travian": "travian", "forts": "trano_mafy", "goodcompany": "fiarahantsara", "civ": "civ", "homeworld": "tan<PERSON><PERSON><PERSON>", "heidentum": "heidentum", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forthekings": "hoantompoko", "realtimestrategy": "strategytokonynytenymivantana", "starctaft": "starcraft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "fanjakanarya<PERSON><PERSON>aba<PERSON>", "eu4": "eu4", "vainglory": "avonavona", "ww40k": "ww40k", "godhood": "andriamanitra", "anno": "taona", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "kilasynalgèbramahafinarakanidave", "plagueinc": "aretinaplague", "theorycraft": "theorycraft", "mesbg": "mesbg", "civilization3": "sivilizasiona3", "4inarow": "4<PERSON><PERSON>", "crusaderkings3": "crusaderkings3", "heroes3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancewars": "<PERSON><PERSON><PERSON><PERSON>", "ageofempires2": "ageofempires2", "disciples2": "tsikera2", "plantsvszombies": "zavamandyvsmaty", "giochidistrategia": "lalaotetika", "stratejioyunları": "lalaotetika", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "mpanjak<PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "heartsofiron4", "companyofheroes": "fiarahankamah<PERSON>", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "gana<PERSON><PERSON><PERSON><PERSON>", "phobies": "<PERSON><PERSON><PERSON>", "phobiesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingclashroyale": "gamingroyaleclash", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "isampivadika", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "mpanjakampiady", "cultris2": "cultris2", "spellcraft": "odyhaycasting", "starwarsempireatwar": "adynyempirestar<PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "estratejia", "popfulmail": "popfulmail", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dysonsphereprogram": "programantspheredyson", "transporttycoon": "mpanjakantransport", "unrailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "fijalianyfiaramanidina", "uplandkingdoms": "fanjakanambony", "galaxylife": "fiainanagalaks<PERSON>", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespire", "battlecats": "sakamiadymalagasy", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "ma<PERSON><PERSON>", "deadbydaylight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alicemadnessreturns": "alicemi<PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "rakitsorymaintyal<PERSON>", "phasmophobia": "tahotratymat<PERSON>", "fivenightsatfreddys": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saiko": "saiko", "fatalframe": "sarymatotra", "littlenightmares": "alokaalokakely", "deadrising": "velonomaty", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "antranotratsy", "deadisland": "nosynymate", "litlemissfortune": "vinehitraratsy", "projectzero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horory": "horory", "jogosterror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor": "salamamp<PERSON><PERSON>", "helloneighbor2": "salanamapifanila2", "gamingdbd": "lalaodbd", "thecatlady": "nyzorampisaka", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "kart<PERSON><PERSON><PERSON><PERSON><PERSON>", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "<PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "kartabaisikileta", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "an<PERSON><PERSON><PERSON>a", "solitaire": "solitera", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "karatralalaovina", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kartatrataranymividy", "pokemoncards": "kart<PERSON><PERSON><PERSON><PERSON>", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "kartysport", "cardfightvanguard": "adykara<PERSON>", "duellinks": "duellinks", "spades": "b<PERSON><PERSON>", "warcry": "akoranady", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "mpanjakanyny_fo", "truco": "truco", "loteria": "loteria", "hanafuda": "hana<PERSON>da", "theresistance": "nyfiandohana", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "masomangamaintramainty", "yugiohgoat": "yug<PERSON>ht<PERSON><PERSON>", "briscas": "briscas", "juegocartas": "lalaokarta", "burraco": "bur<PERSON>o", "rummy": "romy", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "mtgcommander", "cotorro": "resadresaka", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "<PERSON><PERSON><PERSON><PERSON>", "battlespiritssaga": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jogodecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "žolíky": "žolíky", "facecard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfight": "adykara<PERSON>", "biriba": "biriba", "deckbuilders": "mpanaokirary", "marvelchampions": "marvelchampions", "magiccartas": "kartamagika", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cyberse": "cyberse", "classicarcadegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "projectmirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "dihydihy", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "tsapakanymatay", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "afakamand<PERSON><PERSON>", "rhythmgamer": "mpilalakorhythm", "stepmania": "stepmania", "highscorerythmgames": "ambonymarikarythmgames", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "fetran<PERSON>ng<PERSON>na", "hypmic": "hypmic", "adanceoffireandice": "dihiniraafosynyranomandry", "auditiononline": "<PERSON><PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "lalaovibaizina", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "doktymozika", "cubing": "cubing", "wordle": "wordle", "teniz": "tenisy", "puzzlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spotit": "hitako", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "ankamantatra_lojika", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rubikscube": "rubikscube", "crossword": "tetybola", "motscroisés": "tenymiampita", "krzyżówki": "krzyżówki", "nonogram": "nonogram", "bookworm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "puzzlejigsaw", "indovinello": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riddle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riddles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inside": "anatiny", "angrybirds": "voronamenaka", "escapesimulator": "mandosikasimulator", "minesweeper": "fandriky_bombakely", "puzzleanddragons": "puzzlesydragona", "crosswordpuzzles": "famintinana<PERSON>lina", "kurushi": "k<PERSON>hi", "gardenscapesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puzzlesport": "puzzlesport", "escaperoomgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "escapegame": "lalaoandosit<PERSON>", "3dpuzzle": "3dpuzzle", "homescapesgame": "lalaohomescape", "wordsearch": "fikaraokanteny", "enigmistica": "enigmistica", "kulaworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myst": "mistery", "riddletales": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fishdom": "fishdom", "theimpossiblequiz": "quiz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "puzzle3mitovy", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "rubikcube", "cuborubik": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "yapboz": "yapboz", "thetalosprinciple": "thetalosprinciple", "homescapes": "tokatranomitoetra", "puttputt": "puttputt", "qbert": "qbert", "riddleme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tycoongames": "lalaotycoon", "cubosderubik": "cubosderubik", "cruciverba": "tetikatenypifandimby", "ciphers": "fanafody", "rätselwörter": "rätselwörter", "buscaminas": "buscaminas", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "zazalayturnip", "adivinanzashot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nobodies": "tsy<PERSON><PERSON><PERSON>y", "guessing": "vinavina", "nonograms": "nonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "tenymiafinaravakavaka", "syberia2": "syberia2", "puzzlehunt": "fikarakarapuzzle", "puzzlehunts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catcrime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quebracabeça": "<PERSON><PERSON><PERSON>", "hlavolamy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "nya<PERSON><PERSON>far<PERSON>mp<PERSON><PERSON><PERSON>", "autodefinidos": "autodefinidos", "picopark": "picopark", "wandersong": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cassetête": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "limbo": "ambin<PERSON><PERSON>y", "rubiks": "rubiks", "maze": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tinykin": "tinykin", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "sombiny", "portalgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cubomagico": "cubomagika", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "anke<PERSON>njanah<PERSON>", "monopoly": "monopoly", "futurefight": "ad<PERSON><PERSON><PERSON>y", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "<PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "fiainanybity", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "kintonamiravorona", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "alchemystars", "stateofsurvival": "fah<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycity": "<PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "hazakazatria", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "<PERSON><PERSON><PERSON><PERSON>", "a3": "a3", "phonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guardiantales": "tantarampiam<PERSON>", "petrolhead": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticool": "tacticool", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "craftsman": "mpanao_asa_tanana", "supersus": "supersus", "slowdrive": "fi<PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "famp<PERSON><PERSON><PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "adylalina", "freefire": "freefire", "mobilegaming": "lalaomobil<PERSON>", "lilysgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "thearcana", "8ballpool": "8ballpool", "emergencyhq": "hqurgence", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "horohoronasynifintifiny", "ml": "ml", "bangdream": "nofinofivarotra", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "androidgames": "lalaokand<PERSON><PERSON>", "criminalcase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "summonerswar": "summonerswar", "cookingmadness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dokkan": "dokkan", "aov": "aov", "triviacrack": "fanontanianakihevitra", "leagueofangels": "ligan<PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "tsikitsikivoryvorona", "gachalife": "gachalife", "neuralcloud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mysingingmonsters": "nymonsteramihirako", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "robômp<PERSON>dian<PERSON>", "mirrorverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pou": "pou", "warwings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "fet<PERSON><PERSON>na", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "fidirana", "slugitout": "<PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "namanabiby", "gameofsultans": "lalaoansul<PERSON>", "arenabreakout": "arenamivoaka", "wolfy": "loup", "runcitygame": "hazakazakantanàna", "juegodemovil": "lalaomobilà", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "mi<PERSON><PERSON>", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "baotyahybrasil", "ldoe": "ldoe", "legendonline": "<PERSON><PERSON><PERSON><PERSON>", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "ants<PERSON><PERSON>ng<PERSON><PERSON>", "shiningnikki": "nik<PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "la<PERSON><PERSON>yn<PERSON><PERSON>", "sealm": "sealm", "shadowfight3": "adyalokafaniry3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "tenyaminnama2", "soulknight": "soulknight", "purrfecttale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showbyrock": "showbyrock", "ladypopular": "zazavaovyvao", "lolmobile": "lolfinday", "harvesttown": "tanànamamboly", "perfectworldmobile": "tontolotsaramobile", "empiresandpuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empirespuzzles": "empirespuzzles", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fanny": "lolo", "littlenightmare": "nofykely", "aethergazer": "aethergazer", "mudrunner": "mpihazakazakafotaka", "tearsofthemis": "lotrannyhelokany", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "gaminngmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "z<PERSON><PERSON><PERSON><PERSON><PERSON>", "eveechoes": "eveechoes", "jogocelular": "lalaomobilà", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "adyarahobestreetfighter", "lesecretdhenri": "nysekrea<PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "girlsfrontline": "zaz<PERSON><PERSON>_eo_andaharana", "jurassicworldalive": "jurassicworldalive", "soulseeker": "m<PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "miala_aminizany", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracingonline": "fiara_drift_kilalao_anaty_aterineto", "jogosmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofneverland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pubglite": "pubglite", "gamemobilelegends": "lalaokiniamobilelegends", "timeraiders": "mpandroba_fotoana", "gamingmobile": "lalaomobilygasy", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "<PERSON><PERSON><PERSON><PERSON>", "dnd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quest": "<PERSON><PERSON><PERSON><PERSON>", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgambonylatabatra", "worldofdarkness": "tontolomaizina", "travellerttrpg": "travellerttrpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "clubnyfitiavana", "d20": "d20", "pokemongames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "poke<PERSON><PERSON>a", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "resadresaka", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonpurpura", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "ekipanympanaroba", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "poke<PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "ekipamistery", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "tanora", "shinypokemon": "pokemonmamirap<PERSON><PERSON>", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "tanamby", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "pokemontory", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "mpitady<PERSON>ton<PERSON>", "ajedrez": "echec", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "blitzprapancha", "jeudéchecs": "jeudéchecs", "japanesechess": "échyjaponesy", "chinesechess": "echekasinoa", "chesscanada": "echecscanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rook": "rook", "chesscom": "chesscom", "calabozosydragones": "kalabozosydragones", "dungeonsanddragon": "<PERSON>rang<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonmaster": "tomponlabirinty", "tiamat": "tiamat", "donjonsetdragons": "donjonasydragona", "oxventure": "oxventure", "darksun": "masoandrofeno", "thelegendofvoxmachina": "nyanganoandryvoxmachina", "doungenoanddragons": "doungenoanddragons", "darkmoor": "darkmoor", "minecraftchampionship": "fiadianampanjakannyminecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modsminecraft", "mcc": "mcc", "candleflame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fru": "fru", "addons": "fanampy", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftnovaina", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "eoneoneotany", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "tanànanaminecraft", "pcgamer": "mpilalaopc", "jeuxvideo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gambit": "gambit", "gamers": "<PERSON><PERSON><PERSON><PERSON>", "levelup": "mit<PERSON><PERSON>gat<PERSON><PERSON>", "gamermobile": "gamerfinday", "gameover": "tapitra", "gg": "gg", "pcgaming": "lalaop<PERSON><PERSON>", "gamen": "kilalao", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "lalaop<PERSON><PERSON>", "casualgaming": "la<PERSON><PERSON><PERSON>", "gamingsetup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcmasterrace": "pcmasterrace", "pcgame": "lalaop<PERSON><PERSON>", "gamerboy": "<PERSON><PERSON><PERSON>", "vrgaming": "<PERSON><PERSON><PERSON><PERSON>", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "la<PERSON>o", "consoleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boxi": "boxi", "pro": "pro", "epicgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "semigamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamergirls": "gamergirls", "gamermoms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerguy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamewatcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamervavy", "otoge": "otoge", "dedsafio": "dedsafio", "teamtryhard": "ekipamiezakabefatratra", "mallugaming": "lalaomalagasy", "pawgers": "pawgers", "quests": "te<PERSON><PERSON><PERSON>", "alax": "alax", "avgn": "avgn", "oldgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cozygaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamelpay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepc": "<PERSON><PERSON><PERSON><PERSON>", "dsswitch": "dsswitch", "competitivegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftnewjersey": "minecraftnewjersey", "faker": "mpanao_sandoka", "pc4gamers": "pc4gamers", "gamingff": "gamingarysautrafa", "yatoro": "yatoro", "heterosexualgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "lalaokompiotera", "girlsgamer": "zazavavyfilalaosary", "fnfmods": "fnfmods", "dailyquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamegirl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chicasgamer": "tovovavygamer", "gamesetup": "lalaovoalamina", "overpowered": "<PERSON><PERSON><PERSON><PERSON>", "socialgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamejam": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proplayer": "jokerypro", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "<PERSON><PERSON><PERSON>", "republicofgamers": "repoblikannympilaroaloka", "aorus": "aorus", "cougargaming": "cougargaming", "triplelegend": "tripleleg<PERSON>a", "gamerbuddies": "namanampilarokilalao", "butuhcewekgamers": "milacewekgamers", "christiangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamernerd": "gameradala", "nerdgamer": "nerdgamer", "afk": "<PERSON><PERSON><PERSON><PERSON>", "andregamer": "andregamer", "casualgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "89squad": "89squad", "inicaramainnyagimana": "inicaramainnyagimana", "insec": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gemers": "gemers", "oyunizlemek": "oyunizlemek", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "mpi<PERSON><PERSON><PERSON><PERSON><PERSON>", "wspólnegranie": "wspólnegranie", "mortdog": "mortdog", "playstationgamer": "mpilalakaplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gtracing": "gtracing", "notebookgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "protogen": "protogen", "womangamer": "<PERSON>hiva<PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "golfamario", "samusaran": "sa<PERSON><PERSON>", "forager": "mpanangona", "humanfallflat": "olombelonalatsaka", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "mozikanin<PERSON>o", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "kilalaomalagagasy", "switch": "switch", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "aceattorney", "ssbm": "ssbm", "skychildrenofthelight": "zanakandanit<PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "<PERSON><PERSON><PERSON><PERSON>", "ahatintime": "fanatiranandrofotoana", "tearsofthekingdom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "walkingsimulators": "simu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nintendogames": "lalaonintendo", "thelegendofzelda": "thelegendofzelda", "dragonquest": "dragonquest", "harvestmoon": "volambary", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "ainarivolinnytany", "myfriendpedro": "namakopedro", "legendsofzelda": "legendsofzelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "<PERSON><PERSON><PERSON><PERSON>", "earthbound": "etytany", "tales": "tantara", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrosssing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "tetikadytriangola", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "conkersbadfurday", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "hyrulewarriors", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioandsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "voadi<PERSON><PERSON>", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "alikamena", "vanillalol": "vanillamdrlol", "wildriftph": "wildriftmg", "lolph": "mdrlg", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "pubohatra", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsespaña", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "kilalaolol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "lalaofornite", "gamingfortnite": "lalaofornite", "fortnitebr": "fortnitebr", "retrovideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "scaryvideogames": "lalaovideomampata<PERSON><PERSON>", "videogamemaker": "mpanaovalalaovid<PERSON>o", "megamanzero": "megamanzero", "videogame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videosgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "adysakanapeniteatra", "arcades": "arcade", "acnh": "acnh", "puffpals": "namampofoka", "farmingsimulator": "simulateurmpiompy", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxalemania", "robloxdeutsch": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "erlc": "erlc", "sanboxgames": "lalaosanbox", "videogamelore": "tantaranylal<PERSON>vid<PERSON><PERSON>", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "nofinofy", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "toeranamatymat<PERSON>", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "videogiochi", "theoldrepublic": "nyrepoblikataloha", "videospiele": "kilalaobevideo", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "aksyavonkavandriana", "storyofseasons": "tantaranytaona", "retrogames": "<PERSON><PERSON><PERSON><PERSON>", "retroarcade": "retroarcade", "vintagecomputing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "tsirariny2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beatmaniaiidx": "beatmaniaiidx", "steep": "henjana", "mystgames": "lalaot<PERSON><PERSON><PERSON>", "blockchaingaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "consolegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "konsolen", "outrun": "mandositra", "bloomingpanic": "tahotramirobiroby", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "lalaovideota<PERSON><PERSON>", "monstergirlquest": "<PERSON><PERSON><PERSON><PERSON>ikamba<PERSON>", "supergiant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "lalaonajackbox", "interactivefiction": "tantaraifeno", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "ampiantrapoampiananatra", "visualnovel": "tantaraantsary", "visualnovels": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rgg": "rgg", "shadowolf": "omb<PERSON><PERSON>", "tcrghost": "tcrmatoatoa", "payday": "<PERSON><PERSON><PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "la<PERSON>ohatsarafijery", "novelavisual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "revolisionabitsybitsyrivotra", "wiiu": "wiiu", "leveldesign": "famolavolanahaavo", "starrail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyblade": "keyblade", "aplaguetale": "talimpeste", "fnafsometimes": "fnafsometimes", "novelasvisuales": "novelasvisuales", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videojuejos": "videoju<PERSON><PERSON>", "videogamedates": "lalaovideoambanymena", "mycandylove": "<PERSON>ia<PERSON><PERSON><PERSON>", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "antonyanao3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "tranomaniakamasina", "crashracing": "hazakazadyfiara", "3dplatformers": "3dplatformers", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "adimpery", "storygames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beyondtwosouls": "ankoatranyfaritryroahafaina", "gameuse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offmortisghost": "offmortisghost", "tinybunny": "bitikakel<PERSON>", "retroarch": "retroarch", "powerup": "fanomeherinavaovao", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "aventuragrafika", "quickflash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "gachagaming", "retroarcades": "arcadefahagola", "f123": "f123", "wasteland": "tany_maina", "powerwashsim": "powerwashsim", "coralisland": "nosy_koraly", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "ton<PERSON>loha<PERSON>", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "astroneer", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "vybevakalozoro", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simulator", "symulatory": "simulatera", "speedrunner": "speedrunner", "epicx": "epikax", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wonderlandonline": "wonderland<PERSON>li<PERSON>", "skylander": "skylander", "boyfrienddungeon": "sakaizandungeon", "toontownrewritten": "too<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "<PERSON><PERSON>of<PERSON>", "simrace": "hazakazana_simulation", "pvp": "pvp", "urbanchaos": "korontanantanàna", "heavenlybodies": "vata<PERSON><PERSON><PERSON>na", "seum": "sosotra", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spaceflightsimulator": "simulateurfialàmpanondranahaba<PERSON>", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "tetikampiadymikapoka", "foodandvideogames": "sakafosykilalaovideo", "oyunvideoları": "lalaovideoary", "thewolfamongus": "nyalika<PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "simulaterakamiao", "horizonworlds": "tontolomisava", "handygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leyendasyvideojuegos": "anganosynykilalaohoronantsary", "oldschoolvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racingsimulator": "simulaterafihazakazaka", "beemov": "bee<PERSON>v", "agentsofmayhem": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "songpop": "<PERSON><PERSON><PERSON>", "famitsu": "famitsu", "gatesofolympus": "vavahadiniolympus", "monsterhunternow": "monsterhunternow", "rebelstar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "indievideogaming": "lalaontsarymadinikamavamalava", "indiegaming": "lalaoindependanta", "indievideogames": "lalaokiravideotsytoe<PERSON>", "indievideogame": "lalaovideoindépandante", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermantsymatory", "bufffortress": "rova<PERSON><PERSON><PERSON><PERSON>", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "te<PERSON><PERSON><PERSON>", "futureclubgames": "lalaonykliobyhoavy", "mugman": "mugman", "insomniacgames": "tsylalaotsymatory", "supergiantgames": "lalaomasupergiant", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "lalaonaceleste", "aperturescience": "siansydiafragma", "backlog": "efa_ela", "gamebacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "personanjitralalaovideofy", "achievementhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityskylines": "lani<PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "<PERSON><PERSON><PERSON><PERSON>", "beastlord": "tomponbiby", "juegosretro": "<PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "fitahiandopamine", "staxel": "staxel", "videogameost": "mozika<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "tiakokofxv", "arcanum": "mistery", "neoy2k": "neoy2k", "pcracing": "hazakazampcracing", "berserk": "misa<PERSON><PERSON>", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animemalahelo", "darkerthanblack": "maintykokoanoamainty", "animescaling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animewithplot": "animemisysoratra", "pesci": "pesci", "retroanime": "animeretrô", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "anime80s", "90sanime": "anime90s", "darklord": "tompondaizina", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonevakim1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "animecover", "thevisionofescaflowne": "thevisionofescaflowne", "slayers": "mpamono", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "tron<PERSON><PERSON>akond<PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "ka<PERSON><PERSON>", "fireforce": "herimbaomby", "moriartythepatriot": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "horona<PERSON>o", "fairytail": "anganomasina", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "sirenava<PERSON><PERSON>", "kamisamakiss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "mangatahotra", "romancemangas": "<PERSON><PERSON><PERSON><PERSON>", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "zazavavintsampymamba", "blacklagoon": "ranomainty", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "genieinc", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "bungostraydogs", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "indexmagikairay", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "kagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animesportiva", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "animeisekai", "sagaoftanyatheevil": "tantaranidratsynytanyatheevil", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "iazalahysy<PERSON>by", "fistofthenorthstar": "tsinjombazotramiavaratra", "mazinger": "mazinger", "blackbuttler": "borikymainty", "towerofgod": "towerofgod", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmoonwosagashite": "volanafenonitadiako", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "tsar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "tampontadydohoka", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "fahaizananyvavy", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "zazavavygoavaman<PERSON>", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "animedaolo", "chainsawman": "chainsawman", "bungoustraydogs": "bungoustraydogs", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "claymore", "loli": "loli", "horroranime": "horo<PERSON><PERSON>", "fruitsbasket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "devilmancrybaby": "zanakylemalemy", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "thepromisedneverland", "monstermanga": "mangabibidia", "yourlieinapril": "laingankaoaminnyaprily", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "gadramasonybemat<PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "fahafates<PERSON>ymatymahagaga", "bannafish": "voantsila<PERSON>", "sukuna": "<PERSON>kuna", "darwinsgame": "lalaontevoluisiona", "husbu": "bady", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "<PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "tsipikademonia", "toyoureternity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "fototraandavanandro", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "voafafa", "bluelock": "bluelock", "goblinslayer": "mpamonomatoatoa", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampireknight", "mugi": "mugi", "blueexorcist": "blueexorcist", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "<PERSON><PERSON><PERSON>", "spyfamily": "spyfamily", "airgear": "airgear", "magicalgirl": "zazavavymagika", "thesevendeadlysins": "nyhelokafitofandringanaina", "prisonschool": "sekoly_fongarana", "thegodofhighschool": "nyandriamanampiana<PERSON>", "kissxsis": "orolafonorala<PERSON>", "grandblue": "ranomangamangatramainty", "mydressupdarling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "tontoloanimé", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "sa<PERSON><PERSON><PERSON>", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "<PERSON><PERSON><PERSON><PERSON>", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "<PERSON><PERSON><PERSON><PERSON>", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayeraminnytsabatra", "bloodlad": "bloodlad", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "tsinjombygasy", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceanime": "tantarampitiavanaanime", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "housekinokuni": "tranofan<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "recordragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "retromanga", "highschoolofthedead": "liseanymaty", "germantechno": "technoalemana", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "princedatenisy", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "assassinclassroom", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animejaponey", "animespace": "animespace", "girlsundpanzer": "zazavavysynytanky", "akb0048": "akb0048", "hopeanuoli": "fantenafanolitra", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "mpitanauq", "indieanime": "animeindependanta", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ratman", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "zazavavypaiso", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinklubyvavy", "dragonquestdai": "dragonquestdai", "heartofmanga": "fotsimanga", "deliciousindungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "recordofragnarok", "funamusea": "kilalovana", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "sarotrabediamianatra", "overgeared": "overequipé", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "atelierampamosavy", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "nympiralalindra<PERSON>diana", "animeshojo": "animeshojo", "reverseharem": "haremamito<PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "mpampianatramahay", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "nytalekolahy", "gear5": "gear5", "grandbluedreaming": "nofinofisinymangamanga", "bloodplus": "bloodplus", "bloodplusanime": "bloodplusanime", "bloodcanime": "ranime", "bloodc": "<PERSON><PERSON>na", "talesofdemonsandgods": "tantarannydemoniasynanyandr<PERSON>ani<PERSON>", "goreanime": "goreanime", "animegirls": "zazavavy_anime", "sharingan": "<PERSON><PERSON>", "crowsxworst": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "splatteranime": "animemandripika", "splatter": "fiparitahandrà", "risingoftheshieldhero": "<PERSON>aka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "voadaotynyizy", "animeyuri": "animeyuri", "animeespaña": "animeespana", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "zanakynybalena", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "supercampeones", "animeidols": "animeidols", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "zazavavymagika", "callofthenight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsubasachronicle": "tsubasachronicle", "findermanga": "mitadymanga", "princessjellyfish": "princess<PERSON><PERSON><PERSON>", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "molotraparadisy", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeverse", "persocoms": "persocoms", "omniscientreadersview": "mpamakymahitarehetra", "animecat": "sa<PERSON><PERSON><PERSON>", "animerecommendations": "animetolokar<PERSON>", "openinganime": "fanokafananimelaza", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "nykomediaromantikaadolescent", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundam", "voltesv": "voltesv", "giantrobots": "robotsgoavambe", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mekanika", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "<PERSON><PERSON><PERSON><PERSON>", "deathnote": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "jojosbizarreadventuremahagaga", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "animemiaramila", "greenranger": "rangerymaintso", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "tanànanime", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "mpamonodémonia", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "corpsmp<PERSON>rok<PERSON>", "onepieceanime": "onepieceanime", "attaquedestitans": "fanafihantitana", "theonepieceisreal": "nythenykinyonepieceioyio", "revengers": "mpamaly_faty", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "efetrajoyboy", "digimonstory": "tantarandi<PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "fonjampiadidiana", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoontongasoa", "kemonofriends": "kemonofriends", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "mpamosakyvolitolitr", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "arabelamasina", "recuentosdelavida": "tantaranyfiaiana<PERSON>"}