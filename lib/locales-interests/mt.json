{"2048": "2048", "mbti": "mbti", "enneagram": "enneagramma", "astrology": "astroloġija", "cognitivefunctions": "funzjonijietkonjittivi", "psychology": "psikoloġija", "philosophy": "filosofija", "history": "storja", "physics": "<PERSON><PERSON><PERSON>", "science": "xje<PERSON>", "culture": "kultura", "languages": "lingwi", "technology": "teknoloġija", "memes": "memes", "mbtimemes": "mbtimemes", "astrologymemes": "memestastrologija", "enneagrammemes": "memestallenneagramma", "showerthoughts": "ħsibijiettaxxawer", "funny": "aħbargrazzjuża", "videos": "vidjows", "gadgets": "gadgets", "politics": "politika", "relationshipadvice": "relazzjonijietpariri", "lifeadvice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crypto": "crypto", "news": "aħbarijiet", "worldnews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archaeology": "arkeoloġija", "learning": "<PERSON><PERSON><PERSON>", "debates": "<PERSON><PERSON><PERSON><PERSON>", "conspiracytheories": "teorijikonspirattivi", "universe": "universu", "meditation": "meditazzjoni", "mythology": "mitoloġija", "art": "seng<PERSON>a", "crafts": "<PERSON><PERSON><PERSON><PERSON>", "dance": "<PERSON><PERSON>", "design": "disinn", "makeup": "makeup", "beauty": "s<PERSON><PERSON><PERSON>", "fashion": "moda", "singing": "kantant", "writing": "kitba", "photography": "fotografi", "cosplay": "cosplay", "painting": "pittura", "drawing": "tpinġija", "books": "kotba", "movies": "films", "poetry": "poeż<PERSON>", "television": "televiżjoni", "filmmaking": "ħolqientalfilms", "animation": "animazzjoni", "anime": "anime", "scifi": "scifi", "fantasy": "fantasija", "documentaries": "dokumentarji", "mystery": "misteru", "comedy": "kummiedja", "crime": "kriminalità", "drama": "drama", "bollywood": "bollywood", "kdrama": "<PERSON><PERSON><PERSON>", "horror": "<PERSON><PERSON><PERSON>", "romance": "romantika", "realitytv": "televixin_realtà", "action": "<PERSON><PERSON><PERSON><PERSON>", "music": "muż<PERSON>", "blues": "blues", "classical": "klassiku", "country": "<PERSON><PERSON><PERSON><PERSON>", "desi": "desi", "edm": "edm", "electronic": "elettroniku", "folk": "folk", "funk": "funk", "hiphop": "hiphop", "house": "dar", "indie": "indie", "jazz": "jazz", "kpop": "kpop", "latin": "latin", "metal": "metal", "pop": "pop", "punk": "punk", "rnb": "rnb", "rap": "rap", "reggae": "reggae", "rock": "rock", "techno": "tekno", "travel": "ivv<PERSON><PERSON>ġ<PERSON>", "concerts": "kunċerti", "festivals": "festivali", "museums": "mużewijiet", "standup": "standup", "theater": "teatru", "outdoors": "barra", "gardening": "<PERSON><PERSON><PERSON>", "partying": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gaming": "gaming", "boardgames": "logħobtalbord", "dungeonsanddragons": "dungeonsanddragons", "chess": "ċess", "fortnite": "fortnite", "leagueoflegends": "leagueoflegends", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "ikel", "baking": "<PERSON><PERSON>", "cooking": "ċukerina", "vegetarian": "veġ<PERSON><PERSON><PERSON>", "vegan": "vegan", "birds": "g<PERSON><PERSON><PERSON>", "cats": "qattus", "dogs": "k<PERSON>b", "fish": "siemel", "animals": "anni<PERSON><PERSON>", "blacklivesmatter": "ħajjetsewdaimporta", "environmentalism": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "feminism": "feminiżmu", "humanrights": "drittijietilbniedem", "lgbtqally": "alleatlgbtq", "stopasianhate": "ieqfuod<PERSON><PERSON><PERSON><PERSON>ku", "transally": "transally", "volunteering": "volontarjat", "sports": "sport", "badminton": "badminton", "baseball": "bejżbol", "basketball": "basketball", "boxing": "boksing", "cricket": "cricket", "cycling": "ċikliżmu", "fitness": "fitness", "football": "futbol", "golf": "golf", "gym": "<PERSON><PERSON>", "gymnastics": "ġinnastika", "hockey": "<PERSON><PERSON><PERSON>", "martialarts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "netball", "pilates": "pilates", "pingpong": "pingpong", "running": "<PERSON><PERSON>", "skateboarding": "skateboarding", "skiing": "<PERSON><PERSON><PERSON>", "snowboarding": "snowboarding", "surfing": "surfing", "swimming": "għawm", "tennis": "tennis", "volleyball": "pallavolo", "weightlifting": "<PERSON><PERSON><PERSON>", "yoga": "yoga", "scubadiving": "għadsilmub<PERSON>ar", "hiking": "mixja", "capricorn": "capricorn", "aquarius": "<PERSON><PERSON><PERSON><PERSON>", "pisces": "piscis", "aries": "<PERSON><PERSON><PERSON>", "taurus": "tawru", "gemini": "gemini", "cancer": "kanċer", "leo": "leo", "virgo": "virgo", "libra": "libra", "scorpio": "scorpio", "sagittarius": "sagittarju", "shortterm": "għalftitżmien", "casual": "<PERSON><PERSON><PERSON>", "longtermrelationship": "relazzjonifittermlutwil", "single": "<PERSON><PERSON><PERSON>", "polyamory": "polijamorja", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "<PERSON><PERSON><PERSON>", "bisexual": "bisesswali", "pansexual": "pansesswali", "asexual": "<PERSON><PERSON><PERSON>", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "kliebtagħassa", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "kingsquest", "soulreaver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "suikoden": "su<PERSON><PERSON>", "subverse": "sottovers", "legendofspyro": "leġġendataspyro", "rouguelikes": "rouguelikes", "syberia": "siberja", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "xamstramp<PERSON>t", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "rocksteady", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "din<PERSON><PERSON><PERSON><PERSON>ħ<PERSON>", "heroesofthestorm": "ero<PERSON><PERSON><PERSON><PERSON>", "cytus": "cytus", "soulslike": "b<PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "nag<PERSON><PERSON><PERSON><PERSON><PERSON>", "jetsetradio": "jetsetradio", "tribesofmidgard": "trib<PERSON><PERSON><PERSON><PERSON><PERSON>", "planescape": "planescape", "lordsoftherealm2": "lordsoftherealm2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "kuluriv", "medabots": "medabots", "lodsoftherealm2": "lodsoftherealm2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "simsgħaddissinkemmjista", "okage": "okage", "juegoderol": "juegoderol", "witcher": "witcher", "dishonored": "<PERSON><PERSON><PERSON><PERSON>", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "fallout", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "modding", "charactercreation": "ħolqienkarattri", "immersive": "immersiv", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "finalfant<PERSON><PERSON>alqadim", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "motivazzjonimorbida", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegames": "logħobotome", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "okarinatażżmien", "yiikrpg": "yiikrpg", "vampirethemasquerade": "vampirethemasquerade", "dimension20": "dimension20", "gaslands": "gaslands", "pathfinder": "pathfinder", "pathfinder2ndedition": "pathfinder2nded", "shadowrun": "shadowrun", "bloodontheclocktower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "ġibdatgħaġla", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "darbawaħda", "rpgmaker": "rpgmaker", "osrs": "osrs", "overlord": "overlord", "yourturntodie": "imsiektekkmut", "persona3": "persona3", "rpghorror": "rpghorror", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "marauders", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rpgtext", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "demonsouls", "mu": "mu", "falloutshelter": "refu<PERSON><PERSON><PERSON><PERSON><PERSON>", "gurps": "gurps", "darkestdungeon": "darkestdungeon", "eclipsephase": "fażitaleklis<PERSON>", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "gwerjerjiddinastija", "skullgirls": "skullgirls", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "wariethogwarts", "madnesscombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "qattxitwa", "road96": "road96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "iljeritagħotham", "forgottenrealms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonlance": "dragonlance", "arenaofvalor": "arenaof<PERSON><PERSON>", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "toontown", "childoflight": "tiflataddawl", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "digimonworld", "monsterrancher": "<PERSON><PERSON><PERSON>", "ecopunk": "ecopunk", "vermintide2": "vermintide2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonforbiddenwest": "horizonforbiddenwest", "twewy": "twewy", "shadowpunk": "shadowpunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deltagreen": "deltagreen", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "smite", "lastepoch": "<PERSON><PERSON>ħ<PERSON>po<PERSON>", "starfinder": "starfinder", "goldensun": "xemxdehbi", "divinityoriginalsin": "divinityoriginalsin", "bladesinthedark": "xfafietfidlam", "twilight2000": "twilight2000", "sandevistan": "sandevistan", "cyberpunk": "cyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkred", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "fallenorder", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "artjaħżiena", "genshinimact": "genshinimpact", "aethyr": "aethyr", "devilsurvivor": "soprav<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "divinità", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "bluestadjanjadmintliet", "adventurequest": "avventuraqqueste", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "logħobtarwol", "roleplayinggames": "logħobtatrwol", "finalfantasy9": "finalfantasy9", "sunhaven": "sunhaven", "talesofsymphonia": "ħrejjeftasymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "torncity", "myfarog": "myfarog", "sacredunderworld": "dinjainfernali", "chainedechoes": "lekottikkatenati", "darksoul": "ruħ<PERSON><PERSON>", "soulslikes": "soulslikes", "othercide": "alterocidju", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "kronotigg<PERSON>", "pillarsofeternity": "kolonnitatleternità", "palladiumrpg": "palladiumrpg", "rifts": "rifts", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "leġġendataddragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "<PERSON><PERSON><PERSON><PERSON>", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON>lap<PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "littlewood", "childrenofmorta": "<PERSON><PERSON><PERSON><PERSON>", "engineheart": "qalbmagna", "fable3": "fable3", "fablethelostchapter": "fable<PERSON><PERSON><PERSON><PERSON><PERSON>", "hiveswap": "hiveswap", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "starfield", "oldschoolrevival": "oldschoolrevival", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "dinjietselvaġġi", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "logħobrpg", "kingdomhearts": "kingdomhearts", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "clanmal<PERSON><PERSON>", "harvestella": "harvestella", "gloomhaven": "gloomhaven", "wildhearts": "qalbsalvaġġ", "bastion": "bast<PERSON>", "drakarochdemoner": "drak<PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "smewietaarcadia", "shadowhearts": "qlubdell", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "pennyblood", "breathoffire4": "nefsinnarterbgħa", "mother3": "oxxommi3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "edengħodwa", "roleplaygames": "logħobtarwoli", "roleplaygame": "rollplaygame", "fabulaultima": "fabulault<PERSON>", "witchsheart": "qalbissaħħara", "harrypottergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "vampirilamasquerade", "dračák": "dragun", "spelljammer": "spelljammer", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "chronocross", "cocttrpg": "cocttrpg", "huntroyale": "kaccatasultana", "albertodyssey": "albertodissea", "monsterhunterworld": "monsterhunterworld", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "forumrpg", "shadowheartscovenant": "shadowheartscovenant", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "saltnaġejja", "awplanet": "awplanet", "theworldendswithyou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragalialost": "dragalialost", "elderscroll": "elderscroll", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "<PERSON><PERSON><PERSON><PERSON>", "shoptitans": "shoptitans", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackbook": "k<PERSON>bliswed", "skychildrenoflight": "tfalsemaxjieli", "gryrpg": "gryrpg", "sacredgoldedition": "edizzjoniaddehbisagrali", "castlecrashers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gothicgame": "gothicgame", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "logħobrpg", "prophunt": "prophunt", "starrails": "starrails", "cityofmist": "ċittàtaċċpar", "indierpg": "indierpg", "pointandclick": "ippuntauikklikkja", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "indivisibbli", "freeside": "naħalibera", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "triqilmewtversonilkanada", "palladium": "<PERSON><PERSON><PERSON><PERSON>", "knightjdr": "<PERSON><PERSON><PERSON>", "monsterhunter": "kaċċaturitalfantażmi", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "ġeosupremacy", "persona5": "persona5", "ghostoftsushima": "ghostoftsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "monsterhunterrise", "nier": "nier", "dothack": "dothack", "ys": "ys", "souleater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "logħobmhuxbinarji", "tacticalrpg": "rpgtattiku", "mahoyo": "mahoyo", "animegames": "animejagħbu", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "godeater", "diluc": "diluc", "venti": "venti", "eternalsonata": "sonataeternal", "princessconnect": "princessconnect", "hexenzirkel": "hexenzirkel", "cristales": "cristales", "vcs": "vcs", "pes": "pes", "pocketsage": "għareftafepep", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valo<PERSON><PERSON><PERSON>", "dota": "dota", "madden": "madden", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "logħobelettroniku", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "esport", "mlg": "mlg", "leagueofdreamers": "ligatalħolmiena", "fifa14": "fifa14", "midlaner": "midlaner", "efootball": "efootball", "dreamhack": "dreamhack", "gaimin": "gaimin", "overwatchleague": "ligaoċverwoċċ", "cybersport": "ċibersport", "crazyraccoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test1test": "test1test", "fc24": "fc24", "riotgames": "logħobriot", "eracing": "imħassar", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valorantkompetittiv", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "nofs<PERSON><PERSON><PERSON><PERSON>", "left4dead": "left4dead", "left4dead2": "left4dead2", "valve": "valve", "portal": "portal", "teamfortress2": "teamfortress2", "everlastingsummer": "sajflijdumgħallejn", "goatsimulator": "simulaturilmogħża", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "pjanetatalħelsien", "transformice": "transformice", "justshapesandbeats": "formiunħsijiet", "battlefield4": "battlefield4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "hacknslash", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "metroidvanias", "overcooked": "maħruqażżej<PERSON>d", "interplanetary": "interplanetarju", "helltaker": "helltaker", "inscryption": "inscryption", "7d2d": "7ijiem2ij", "deadcells": "ċellulimorti", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "<PERSON><PERSON><PERSON><PERSON>", "foxhole": "ħ<PERSON><PERSON>", "stray": "vagabond", "battlefield": "kamptalġwerra", "battlefield1": "battlefield1", "swtor": "swtor", "fallout2": "fallout2", "uboat": "uboat", "eyeb": "għajnejnb", "blackdesert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "simulaturtattabletop", "partyhard": "partyħarsa", "hardspaceshipbreaker": "ksurtalispazjuiebsa", "hades": "hades", "gunsmith": "fabbri<PERSON><PERSON><PERSON><PERSON>", "okami": "<PERSON>ami", "trappedwithjester": "imħassarmaġġester", "dinkum": "dinkum", "predecessor": "predeċessur", "rainworld": "dinjataxita", "cavesofqud": "għerien_ta_qud", "colonysim": "colonysim", "noita": "noita", "dawnofwar": "telqetilgwerra", "minionmasters": "minionmasters", "grimdawn": "grimdawn", "darkanddarker": "dlamutadlam", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datingsims": "simulazzjonijiettadating", "yaga": "yaga", "cubeescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "beltġdida", "citiesskylines": "b<PERSON><PERSON><PERSON><PERSON>", "defconheavy": "defconħqil", "kenopsia": "kenopsja", "virtualkenopsia": "virtualkenopsia", "snowrunner": "snowrunner", "libraryofruina": "librer<PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "illogħobtannonarji", "omegastrikers": "omegastrikers", "wayfinder": "sabtriqa", "kenabridgeofspirits": "kenponttalispirtu", "placidplasticduck": "papradiplastikalaqgħ<PERSON>hemm", "battlebit": "battlebit", "ultimatechickenhorse": "tiġieġataħħamużultimat", "dialtown": "dialtown", "smileforme": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermeatboy", "tinnybunny": "tinnybunny", "cozygrove": "cozygrove", "doom": "doom", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "borderlands", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "apex", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "farcrygames", "paladins": "paladins", "earthdefenseforce": "forziġħaddifensataddinja", "huntshowdown": "huntshowdown", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "warz", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "ultrakill", "joinsquad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "hitman3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "deathstranding", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "xfarmirja", "divisions2": "diviżjonijiet2", "killzone": "<PERSON><PERSON><PERSON><PERSON>", "helghan": "hel<PERSON>", "coldwarzombies": "zombiesgwerrakiesħa", "metro2033": "metro2033", "metalgear": "metalgear", "acecombat": "acecombat", "crosscode": "crosscode", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "<PERSON><PERSON><PERSON><PERSON>", "modernwarfare": "gwerramoderna", "neonabyss": "neonabyss", "planetside2": "planetside2", "mechwarrior": "mechwarrior", "boarderlands": "boarderlands", "owerwatch": "ower<PERSON>", "rtype": "rtype", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "metalslug", "primalcarnage": "massakruprimittiv", "worldofwarships": "worldofwarships", "back4blood": "lura4demm", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "qattiel", "masseffect": "masseffect", "systemshock": "xokktassistema", "valkyriachronicles": "valkyriachronicles", "specopstheline": "specopstheline", "killingfloor2": "killingfloor2", "cavestory": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doometernal": "doometernal", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "division2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "ġenerazzjonizero", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "bniedemżalza", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "l<PERSON>ġiegħfant<PERSON>żma", "warface": "warface", "crossfire": "crossfire", "atomicheart": "atomicheart", "blackops3": "blackops3", "vampiresurvivors": "vampiressurvivors", "callofdutybatleroyale": "callofdutybattleroyale", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "frag", "tinytina": "żgħiratinatina", "gamepubg": "logħobpubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "logħobfps", "convertstrike": "convertstrike", "warzone2": "warzone2", "shatterline": "l<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackopszombies": "blackopszombies", "bloodymess": "tgerbixkbira", "republiccommando": "republiccommando", "elitedangerous": "elitedangerous", "soldat": "soldat", "groundbranch": "fergħaprinċipali", "squad": "squad", "destiny1": "destin1", "gamingfps": "logħobfps", "redfall": "redfall", "pubggirl": "pubggirl", "worldoftanksblitz": "worldoftanksblitz", "callofdutyblackops": "callofdutyblackops", "enlisted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farlight": "farlight", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "tinytinaswonderlands", "halo2": "halo2", "payday2": "juminħlas2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "pubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "merluzztassapun", "ghostcod": "merluzzifantażma", "csplay": "csplay", "unrealtournament": "unrealtournament", "callofdutydmz": "callofdutydmz", "gamingcodm": "logħobcodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "pistolwhip", "callofdutymw2": "callofdutymw2", "quakechampions": "ka<PERSON><PERSON><PERSON>tter<PERSON>ot", "halo3": "halo3", "halo": "halo", "killingfloor": "killingfloor", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "a<PERSON><PERSON><PERSON><PERSON>", "remnant": "b<PERSON><PERSON>", "azurelane": "azurelane", "worldofwar": "<PERSON><PERSON><PERSON>ġ<PERSON><PERSON>", "gunvolt": "gunvolt", "returnal": "returnal", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "quake2", "microvolts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reddead": "deadred", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "battlefield3", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "ba<PERSON><PERSON><PERSON>ħ<PERSON>lin", "rust": "rust", "conqueronline": "conqueronline", "dauntless": "bla_bi<PERSON>a", "warships": "vapuritalġwerra", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "titjirajtilgħolja", "recroom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofruneterra": "leġġenditaruneterra", "pso2": "pso2", "myster": "misteri", "phantasystaronline2": "phantasystaronline2", "maidenless": "mingħajrsieħba", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "dinjatattanks", "crossout": "aqta", "agario": "agario", "secondlife": "ħajjasek<PERSON>", "aion": "aion", "toweroffantasy": "toweroffantasy", "netplay": "logħbonline", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "ninokunicrossworld", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "il<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "clubpenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON>", "newworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "logħobħafna", "pirate101": "pirata101", "honorofkings": "onurofkings", "fivem": "fivem", "starwarsbattlefront": "starwarsbattlefront", "karmaland": "karmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "ponytown", "3dchat": "3dchat", "nostale": "nostale", "tauriwow": "<PERSON><PERSON><PERSON><PERSON>", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "rmiedtalħolqien", "riotmmo": "riotmmo", "silkroad": "silkroad", "spiralknights": "spiralknights", "mulegend": "leġġendatalmul", "startrekonline": "startrekonline", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "draguniprofeta", "grymmo": "grymmo", "warmane": "warmane", "multijugador": "multijugador", "angelsonline": "anġlionline", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverseonline", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "fanteżijagrandjuża", "blueprotocol": "blueprotocol", "perfectworld": "dinjaperfetta", "riseonline": "telgħonline", "corepunk": "corepunk", "adventurequestworlds": "avventuriquestworlds", "flyforfun": "itirbiexi<PERSON>ħ<PERSON>", "animaljam": "animaljam", "kingdomofloathing": "renjutal<PERSON>hing", "cityofheroes": "beltataleroj", "mortalkombat": "mortalkombat", "streetfighter": "ġliedtattriq", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "g<PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "virtuafighter", "streetsofrage": "toroqtara<PERSON>ja", "mkdeadlyalliance": "mkalleanzmortali", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "ilmeliktagħġielda", "likeadragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrofightinggames": "logħobtalġlidjaretro", "blasphemous": "blasfemu", "rivalsofaether": "<PERSON><PERSON><PERSON><PERSON>", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "supersmash", "mugen": "mugen", "warofthemonsters": "gwerratalmostri", "jogosdeluta": "logħoblittalotta", "cyberbots": "ċajberrobots", "armoredwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "finaliġlieda", "poweredgear": "tagħmirbqawwa", "beatemup": "saħħ<PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "logħobtalġlied", "killerinstinct": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingoffigthers": "sultan<PERSON><PERSON>ħ<PERSON><PERSON>", "ghostrunner": "ghostrunner", "chivalry2": "chivalry2", "demonssouls": "demonssouls", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "segwitahollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "silksonghornet", "silksonggame": "logħbasilksong", "silksongnews": "silksongnews", "silksong": "silksong", "undernight": "biljlejl", "typelumina": "ittajpjalumina", "evolutiontournament": "evoluzzjonitturnament", "evomoment": "mumentevo", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "ħrejjefdeberseria", "bloodborne": "bloodborne", "horizon": "<PERSON><PERSON><PERSON><PERSON>", "pathofexile": "pathofexile", "slimerancher": "slimerancher", "crashbandicoot": "crashbandicoot", "bloodbourne": "bloodbourne", "uncharted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "laħħarminnatagħna", "infamous": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstationbuddies": "ħbiebtaplaystation", "ps1": "ps1", "oddworld": "oddworld", "playstation5": "playstation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "rabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "hellletloose", "gta4": "gta4", "gta": "gta", "roguecompany": "kumpanijamħassra", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "allatalgwerra", "gris": "gris", "trove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detroitbecomehuman": "detroitsirbniedem", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "salgħodwa", "touristtrophy": "trofeoturist", "lspdfr": "lspdfr", "shadowofthecolossus": "iċċilliskolossu", "crashteamracing": "crashteamracing", "fivepd": "ħamesp", "tekken7": "tekken7", "devilmaycry": "iddevilmaycry", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "stazzjonlogħa", "samuraiwarriors": "samurrajgwerrieri", "psvr2": "psvr2", "thelastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulblade": "xafraspi<PERSON>li", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "g<PERSON><PERSON>xkie<PERSON>", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "gamepass", "armello": "armello", "partyanimal": "party<PERSON><PERSON>", "warharmmer40k": "warharmmer40k", "fightnightchampion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psychonauts": "psikonauti", "mhw": "mhw", "princeofpersia": "principtalpersja", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "theelderscrolls", "gxbox": "gxbox", "battlefront": "battlefront", "dontstarvetogether": "morm<PERSON><PERSON>ħalla", "ori": "ori", "spelunky": "spelunky", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "starbound", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "renovaturitaddjar", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "leagueofkingdoms", "fable2": "fable2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "televizjoniżibel", "skycotl": "skycotl", "erica": "erica", "ancestory": "an<PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "xortaħaż<PERSON>", "sallyface": "wiċċsally", "franbow": "franbow", "monsterprom": "monsterprom", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "motos", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duckgame": "<PERSON>ħbatapap<PERSON>", "thestanleyparable": "ilparabolatastanley", "towerunite": "towerunite", "occulto": "occulto", "longdrive": "vjaġġtwil", "satisfactory": "sodisfaċenti", "pluviophile": "imħabbtalxita", "underearth": "<PERSON><PERSON><PERSON><PERSON>", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "spiritfarer", "darkdome": "kubbaskura", "pizzatower": "pizzatower", "indiegame": "logħbaindipendenti", "itchio": "itchio", "golfit": "golfit", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON>ħ<PERSON>", "rockpaperscissors": "ġ<PERSON>lakartalqarta<PERSON>", "trampoline": "trampolin", "hulahoop": "ċirkulahoop", "dare": "sfida", "scavengerhunt": "kaċċateżor", "yardgames": "logħobtalbitħa", "pickanumber": "għażelnumru", "trueorfalse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beerpong": "beerpong", "dicegoblin": "goblintatad", "cosygames": "logħobkomdi", "datinggames": "logħobtalappuntamenti", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "logħobxorb", "sodoku": "sodoku", "juegos": "<PERSON><PERSON><PERSON>", "mahjong": "mahjong", "jeux": "jeux", "simulationgames": "logħobsimulazzjoni", "wordgames": "żgħożija_kliem", "jeuxdemots": "jeuxdemots", "juegosdepalabras": "logħobtalkliem", "letsplayagame": "nilgħ<PERSON><PERSON>ħ<PERSON>", "boredgames": "logħobħarxa", "oyun": "oyun", "interactivegames": "logħobinterattivi", "amtgard": "amtgard", "staringcontests": "kompetizzjonijatafstaħħara", "spiele": "spiele", "giochi": "<PERSON><PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "logħobtaliphone", "boogames": "boogames", "cranegame": "logħbatalgru", "hideandseek": "ħabbifittex", "hopscotch": "ħabba", "arcadegames": "logħobtalarcade", "yakuzagames": "logħobtalyakuza", "classicgame": "logħbaklassika", "mindgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guessthelyric": "għożża_lirika", "galagames": "logħobgala", "romancegame": "logħbataromanza", "yanderegames": "logħobyandere", "tonguetwisters": "ilsiengħalixkiel", "4xgames": "4x<PERSON>ħ<PERSON>", "gamefi": "gamefi", "jeuxdarcades": "logħobtalarcades", "tabletopgames": "logħobtalmejda", "metroidvania": "metroidvania", "games90": "logħob90", "idareyou": "nis<PERSON>dak", "mozaa": "mozaa", "fumitouedagames": "fumi<PERSON><PERSON><PERSON><PERSON>", "racinggames": "logħobtattiġrijiet", "ets2": "ets2", "realvsfake": "veru_vs_falz", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "logħbaonline", "onlinegames": "logħobonline", "jogosonline": "logħobonline", "writtenroleplay": "roleplayasskriving", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "pictionary", "coopgames": "logħobkooperattivi", "jenga": "jenga", "wiigames": "logħobwiigames", "highscore": "punteġġgħoli", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "logħobtalburgħers", "kidsgames": "logħobtattfal", "skeeball": "skeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "juegodepreguntas", "gioco": "gioco", "managementgame": "ġugartattmexxija", "hiddenobjectgame": "ħbijietoġ<PERSON>", "roolipelit": "logħobtarrwol", "formula1game": "logħbaformula1", "citybuilder": "binnejtalbliet", "drdriving": "drdriving", "juegosarcade": "logħobarkejd", "memorygames": "logħobtamemorja", "vulkan": "vulkan", "actiongames": "logħobazzjonijiet", "blowgames": "logħobtannofs", "pinballmachines": "flippers", "oldgames": "logħobqodma", "couchcoop": "couchcoop", "perguntados": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameo": "gameo", "lasergame": "logħballejżer", "imessagegames": "logħobimessage", "idlegames": "logħobikkwietkellu", "fillintheblank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxpc": "jeuxpc", "rétrogaming": "rétrogaming", "logicgames": "loġċilogħob", "japangame": "logħobġappuniż", "rizzupgame": "rizzupgejm", "subwaysurf": "subwaysurf", "jeuxdecelebrite": "jeuxdecelebrite", "exitgames": "logħobtaħruġ", "5vs5": "5vs5", "rolgame": "rlogħbarôl", "dashiegames": "dashiegames", "gameandkill": "gameandjoqtol", "traditionalgames": "logħobqodma", "kniffel": "kniffel", "gamefps": "logħobfps", "textbasedgames": "logħobtattest", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "fantacalcio", "retrospel": "retrospel", "thiefgame": "logħbataħaraq", "lawngames": "logħobtallawn", "fliperama": "fliperama", "heroclix": "heroclix", "tablesoccer": "fużboljtalmejda", "tischfußball": "tischfußball", "spieleabende": "iljielifgħalgħobijiet", "jeuxforum": "jeuxforum", "casualgames": "logħobkażwali", "fléchettes": "<PERSON><PERSON><PERSON><PERSON>", "escapegames": "logħobtaħarba", "thiefgameseries": "logħobtasserq", "cranegames": "logħobtalkreyn", "játék": "ħeġġa", "bordfodbold": "bordfodbold", "jogosorte": "jogosorte", "mage": "mage", "cargames": "<PERSON><PERSON><PERSON>", "onlineplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "le<PERSON><PERSON>illogħob", "pursebingos": "bingospurses", "randomizer": "<PERSON><PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "pcgames", "socialdeductiongames": "logħobdeduzzjonisoċjali", "dominos": "dominos", "domino": "domino", "isometricgames": "ġegħieletisometriċi", "goodoldgames": "<PERSON>ħ<PERSON><PERSON><PERSON><PERSON><PERSON>ħ", "truthanddare": "verity<PERSON><PERSON>da", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "kaċċatatteżor", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "f2pgamer", "free2play": "free2play", "fantasygame": "logħbafantażija", "gryonline": "gryonline", "driftgame": "logħbatadrift", "gamesotomes": "gamesotomes", "halotvseriesandgames": "haloserjetele<PERSON>żivaulogħob", "mushroomoasis": "<PERSON><PERSON><PERSON>", "anythingwithanengine": "kwalunkweħaġabhmagnafiha", "everywheregame": "logħbakullimkien", "swordandsorcery": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goodgamegiving": "logħbatajba", "jugamos": "nil<PERSON><PERSON><PERSON><PERSON>", "lab8games": "lab8games", "labzerogames": "labzerogames", "grykomputerowe": "logħobkompjuter", "virgogami": "virgogami", "gogame": "għaddiedlogħba", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "logħobżgħar", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "moddingtaʼ<PERSON>ħob", "crimegames": "kriminalità", "dobbelspellen": "logħobtattavla", "spelletjes": "<PERSON><PERSON><PERSON>", "spacenerf": "spacenerf", "charades": "ċarada", "singleplayer": "logħobwaħdu", "coopgame": "logħbatakooperazzjoni", "gamed": "gejmat", "forzahorizon": "forzahorizon", "nexus": "nexus", "geforcenow": "geforcenow", "maingame": "logħobprinċipali", "kingdiscord": "reġdiscord", "scrabble": "scrabble", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "ludo", "backgammon": "backgammon", "onitama": "onitama", "pandemiclegacy": "pandemijawirt", "camelup": "<PERSON><PERSON><PERSON><PERSON>", "monopolygame": "logħbatamonopoly", "brettspiele": "brettspiele", "bordspellen": "logħobtattavla", "boardgame": "logħbatalbord", "sällskapspel": "logħobtassoċjetà", "planszowe": "logħobtalmejda", "risiko": "risiko", "permainanpapan": "logħobtalbord", "zombicide": "zombicide", "tabletop": "tabletop", "baduk": "baduk", "bloodbowl": "bloodbowl", "cluedo": "cluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "murgħallogħba", "connectfour": "connectfour", "heroquest": "heroquest", "giochidatavolo": "logħobtattabella", "farkle": "farkle", "carrom": "carrom", "tablegames": "logħobfuqilmejda", "dicegames": "logħobtatdadi", "yatzy": "yatzy", "parchis": "parchis", "jogodetabuleiro": "logħobtattabella", "jocuridesocietate": "jocuridesocietate", "deskgames": "logħobtaliskrivanija", "alpharius": "alpharius", "masaoyunları": "logħobmasa", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "laqgħakosmika", "creationludique": "ħolqienludiku", "tabletoproleplay": "logħobfuqilmejda", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "or<PERSON><PERSON>dr<PERSON><PERSON>", "switchboardgames": "logħobtasswitchboard", "infinitythegame": "logħbainfinità", "kingdomdeath": "irenjumewt", "yahtzee": "yahtzee", "chutesandladders": "turġienuslielem", "társas": "sħab", "juegodemesa": "logħobtalmejda", "planszówki": "logħobtattavla", "rednecklife": "ħ<PERSON><PERSON><PERSON><PERSON><PERSON>", "boardom": "<PERSON>j<PERSON>", "applestoapples": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudesociété": "jeudesociété", "gameboard": "tabella<PERSON><PERSON>ħ<PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "jeuxdesociétés", "twilightimperium": "twilightimperium", "horseopoly": "żiemeloppoli", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "palazzitalġenn", "gomoku": "gomoku", "giochidatavola": "logħobtalmejda", "shadowsofbrimstone": "dellbrimstone", "kingoftokyo": "<PERSON><PERSON><PERSON>", "warcaby": "warcaby", "táblajátékok": "logħobtattabella", "battleship": "vapurtalgwerra", "tickettoride": "biljettgħallivjaġġ", "deskovehry": "deskovehry", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "j<PERSON><PERSON><PERSON><PERSON>", "stolníhry": "logħobtalmejda", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "jeuxsociete", "gesellschaftsspiele": "gesellschaftsspiele", "starwarslegion": "starwarslegion", "gochess": "goċess", "weiqi": "weiqi", "jeuxdesocietes": "logħobtassoċjetà", "terraria": "terraria", "dsmp": "dsmp", "warzone": "żonagwerra", "arksurvivalevolved": "arksurvivalevolved", "dayz": "dayz", "identityv": "identityv", "theisle": "theisle", "thelastofus": "<PERSON>ħ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "tombraider", "callofcthulhu": "callofcthulhu", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "fostna", "eco": "eko", "monkeyisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valheim": "valheim", "planetcrafter": "planetcrafter", "daysgone": "jummgħadda", "fobia": "fobija", "witchit": "witchit", "pathologic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zomboid": "zomboid", "northgard": "northgard", "7dtd": "7<PERSON>em", "thelongdark": "<PERSON><PERSON><PERSON>twi<PERSON>", "ark": "ark", "grounded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stateofdecay2": "stateofdecay2", "vrising": "vrising", "madfather": "missier<PERSON><PERSON>", "dontstarve": "morixxmilġuħ", "eternalreturn": "ritornuetern", "pathoftitans": "rottatitani", "frictionalgames": "frictionalgames", "hexen": "saħħ<PERSON>", "theevilwithin": "<PERSON>ħ<PERSON>żinfik<PERSON>", "realrac": "realrac", "thebackrooms": "ilkmamartalwara", "backrooms": "kmarijiettalwara", "empiressmp": "empiressmp", "blockstory": "storjablokk", "thequarry": "il<PERSON><PERSON><PERSON>", "tlou": "tlou", "dyinglight": "dawlmejjet", "thewalkingdeadgame": "illog<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wehappyfew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseofempires": "qawmiettalimperji", "stateofsurvivalgame": "logħbatassopravivenza", "vintagestory": "stor<PERSON><PERSON><PERSON>", "arksurvival": "a<PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "westlendjibqgħajjien", "beastsofbermuda": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "frostpunk", "darkwood": "darkwood", "survivalhorror": "orrursopravivenza", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "voidtrain", "lifeaftergame": "ħajjawaragħlogħba", "survivalgames": "logħobtassopravivenza", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "danilg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scpfoundation": "fondazzjoniscp", "greenproject": "<PERSON>ġ<PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "raft": "raft", "rdo": "rdo", "greenhell": "infernu<PERSON>ħ<PERSON>", "residentevil5": "residentevil5", "deadpoly": "polimejjet", "residentevil8": "residentevil8", "onironauta": "onironauta", "granny": "nanna", "littlenightmares2": "littlenightmares2", "signalis": "signalis", "amandatheadventurer": "amandalaventuriera", "sonsoftheforest": "uliedilforest<PERSON>", "rustvideogame": "rustvideogame", "outlasttrials": "outlast<PERSON>alsmal<PERSON>", "alienisolation": "iżolamenttaljen", "undawn": "undawn", "7day2die": "7jiem2mmutu", "sunlesssea": "baħarblaxxemx", "sopravvivenza": "sopravivenza", "propnight": "lejlilprop", "deadisland2": "gżiramejta2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "vampirs<PERSON>ħ", "deathverse": "deathverse", "cataclysmdarkdays": "katakliżmijietskuri", "soma": "soma", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "stalkercieńczarnob<PERSON>", "lifeafter": "<PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "żmieniddlam", "clocktower3": "torrataċċiklu3", "aloneinthedark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievaldynasty": "dinast<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectnimbusgame": "loghbaprojectnimbus", "eternights": "eternights", "craftopia": "craftopia", "theoutlasttrials": "iltrialstaloutlast", "bunker": "bunker", "worlddomination": "dominazzjonididdinja", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "officioassassinorum", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "qattielnaniet", "warhammer40kcrush": "warhammer40kcrush", "wh40": "wh40", "warhammer40klove": "imħabbagħalwarhammer40k", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "temploculexus", "vindicare": "vindicare", "ilovesororitas": "inħobbissororitas", "ilovevindicare": "inħobbvindicare", "iloveassasinorum": "inħobbassasinorum", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "temploma<PERSON><PERSON>", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "tetris", "lioden": "lioden", "ageofempires": "ageofempires", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "<PERSON><PERSON><PERSON>lf<PERSON>q<PERSON>mieħ<PERSON>", "wingspan": "firxa", "terraformingmars": "terraformingmars", "heroesofmightandmagic": "erojitalqawwajaumaġija", "btd6": "btd6", "supremecommander": "kmandantsupremu", "ageofmythology": "aġeofdelmitologija", "args": "args", "rime": "rime", "planetzoo": "planetzoo", "outpost2": "outpost2", "banished": "miċħud", "caesar3": "caesar3", "redalert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "kommandiukonkwista", "warcraft3": "warcraft3", "eternalwar": "guerraperpetwa", "strategygames": "logħobtastrateġija", "anno2070": "anno2070", "civilizationgame": "logħbataċċiviltà", "civilization4": "civilizzazzjoni4", "factorio": "factorio", "dungeondraft": "dungeondraft", "spore": "spore", "totalwar": "g<PERSON><PERSON><PERSON><PERSON>", "travian": "travian", "forts": "fortiz<PERSON>", "goodcompany": "kumpanijatajba", "civ": "civ", "homeworld": "dinja_tiegħi", "heidentum": "<PERSON><PERSON>ż<PERSON>", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "aktarmgħaġ<PERSON><PERSON><PERSON>ddawl", "forthekings": "għasslaten", "realtimestrategy": "strateg<PERSON><PERSON><PERSON><PERSON><PERSON>", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "ren<PERSON>żewgk<PERSON><PERSON>", "eu4": "ewropa4", "vainglory": "glor<PERSON><PERSON>", "ww40k": "ww40k", "godhood": "divinità", "anno": "anno", "battletech": "battletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "hattrick", "davesfunalgebraclass": "davesfunalgebraclass", "plagueinc": "plageinc", "theorycraft": "theorycraft", "mesbg": "mesbg", "civilization3": "ċiviltà3", "4inarow": "4fliena", "crusaderkings3": "crusaderkings3", "heroes3": "heroes3", "advancewars": "logħobtagwierra", "ageofempires2": "ageofempires2", "disciples2": "dixxipli2", "plantsvszombies": "pjantivszombies", "giochidistrategia": "logħobtastrateġija", "stratejioyunları": "logħobstrateġiċi", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "et<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "resaddajnosawr", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "qlubtalħadid4", "companyofheroes": "kump<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "forgeofempires", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "ganżganżpapra", "phobies": "fobiji", "phobiesgame": "logħbatalfobjiji", "gamingclashroyale": "logħobclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "outerplane", "turnbased": "dawrb<PERSON><PERSON><PERSON>", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cultris2": "cultris2", "spellcraft": "<PERSON><PERSON><PERSON><PERSON>", "starwarsempireatwar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "strateġ<PERSON>", "popfulmail": "popfulmail", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "logħbamejjiet", "dysonsphereprogram": "program<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transporttycoon": "magnattattrasport", "unrailed": "b<PERSON><PERSON><PERSON>", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "turmentplanescapetorment", "uplandkingdoms": "saltnetagħgħolja", "galaxylife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "slaythespire", "battlecats": "qtatestagħlaqin", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "iracing", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needforspeedcarbon": "ħtiġiettagħajraspeedilfaħam", "realracing3": "realracing3", "trackmania": "trackmania", "grandtourismo": "grandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplay", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "lossims4", "fnaf": "fnaf", "outlast": "inżommsakemmdumjien", "deadbydaylight": "mejjetbiljum", "alicemadnessreturns": "alicereġġgħatilġenn", "darkhorseanthology": "antoloġijażż<PERSON>melskur", "phasmophobia": "phasmophobia", "fivenightsatfreddys": "ħamisljelietgħandfreddys", "saiko": "saiko", "fatalframe": "fatalframe", "littlenightmares": "ħolmienżgħartaĥżiena", "deadrising": "qajmamintilmewt", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "<PERSON><PERSON><PERSON><PERSON>", "deadisland": "gżiramejta", "litlemissfortune": "sfortunataċkejkna", "projectzero": "proġettzero", "horory": "horory", "jogosterror": "ġ<PERSON><PERSON><PERSON><PERSON>", "helloneighbor": "hellogħarejja", "helloneighbor2": "helloneighbor2", "gamingdbd": "logħobdbd", "thecatlady": "<PERSON><PERSON><PERSON><PERSON>", "jeuxhorreur": "logħobtalbiża", "horrorgaming": "horrorgaming", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "cardskontralbaniedem", "cribbage": "cribbage", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "pinochle", "codenames": "kodiċijiet", "dixit": "dixit", "bicyclecards": "kartitalbicycles", "lor": "lor", "euchre": "euchre", "thegwent": "thegwent", "legendofrunetera": "leġġendatarunetera", "solitaire": "solitarju", "poker": "poker", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "keyforge", "cardtricks": "kart<PERSON><PERSON><PERSON>", "playingcards": "ko<PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "ginrummy", "netrunner": "netrunner", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "kartitattrading", "pokemoncards": "kartipoke<PERSON>", "fleshandbloodtcg": "fleshandbloodtcg", "sportscards": "kartitalisport", "cardfightvanguard": "cardfightvanguard", "duellinks": "duellinks", "spades": "spejds", "warcry": "għajta_talgwerra", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "sultanilq<PERSON>", "truco": "truco", "loteria": "lotterija", "hanafuda": "hana<PERSON>da", "theresistance": "irre<PERSON><PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "kartijinyugioh", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "dueldisk", "yugiohgame": "yugiohgame", "darkmagician": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blueeyeswhitedragon": "għajnejnbluddragunjnab<PERSON>d", "yugiohgoat": "<PERSON>ug<PERSON>hkbir", "briscas": "briska", "juegocartas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "burraco": "burrako", "rummy": "<PERSON><PERSON>", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dobble", "mtgcommander": "kmandantmtg", "cotorro": "co<PERSON><PERSON>", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "kartenspiel", "carteado": "carteado", "sueca": "sueca", "beloteonline": "beloteonline", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "spirtijitagħġlieda", "battlespiritssaga": "saġġatalispiritutalbattalji", "jogodecartas": "j<PERSON><PERSON><PERSON><PERSON>", "žolíky": "karti", "facecard": "wiċċkarti", "cardfight": "ġliedtalkards", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "eroj_ta_marvel", "magiccartas": "kartijietmaġiċi", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "shadowverse", "skipbo": "skip<PERSON>", "unstableunicorns": "unicornsmhuxstabbli", "cyberse": "cyberse", "classicarcadegames": "logħobarkejdklassiċi", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON>ħ<PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonehero": "clonehero", "justdance": "sempliċimentżfin", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "rockthedead", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "ċentrudanż", "rhythmgamer": "logħobtarritmu", "stepmania": "stepmania", "highscorerythmgames": "highscoreslogrietritmiku", "pkxd": "pkxd", "sidem": "sidem", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "rhythmheaven", "hypmic": "hypmic", "adanceoffireandice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditiononline": "audizzjonionline", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "juegosderitmo", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "tabibtarritmu", "cubing": "kubing", "wordle": "wordle", "teniz": "teniz", "puzzlegames": "puzzlesgames", "spotit": "araha", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "rompikaptaxloġiċi", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "rompi<PERSON><PERSON>", "rubikscube": "k<PERSON><PERSON><PERSON>ku", "crossword": "tisliba", "motscroisés": "tigaqsimiet", "krzyżówki": "tislibiet", "nonogram": "nonogram", "bookworm": "<PERSON><PERSON><PERSON><PERSON>", "jigsawpuzzles": "puzzlesjigsaws", "indovinello": "<PERSON><PERSON><PERSON><PERSON>", "riddle": "riedka", "riddles": "għeġubijiet", "rompecabezas": "r<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "ġewwa", "angrybirds": "tajrimbilkollha", "escapesimulator": "ħarbamissimulatur", "minesweeper": "minesweeper", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "tislibietilkliem", "kurushi": "k<PERSON>hi", "gardenscapesgame": "logħbatagardenscapes", "puzzlesport": "logħbasport", "escaperoomgames": "ħarbaminnka<PERSON>ħ<PERSON>", "escapegame": "loġbataħrab", "3dpuzzle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homescapesgame": "homescapesgame", "wordsearch": "fittexilkliem", "enigmistica": "enigmistika", "kulaworld": "kulaworld", "myst": "mist", "riddletales": "riddletales", "fishdom": "fishdom", "theimpossiblequiz": "ilkwiżżimpossibli", "candycrush": "candycrush", "littlebigplanet": "littlebigplanet", "match3puzzle": "logħbatabbina3", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "stramb", "rubikcube": "kuburubiks", "cuborubik": "kubtalrubik", "yapboz": "yapboz", "thetalosprinciple": "ilprinċipjutatalos", "homescapes": "darhomescapes", "puttputt": "minigolf", "qbert": "qbert", "riddleme": "ħallinigawbek", "tycoongames": "logħobtattycooni", "cubosderubik": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cruciverba": "cruciver<PERSON>", "ciphers": "kodiċijietsigrieti", "rätselwörter": "kliemsigriet", "buscaminas": "buscaminas", "puzzlesolving": "soluzzjonipuzzles", "turnipboy": "turnipboy", "adivinanzashot": "aqtaxhit", "nobodies": "ħaddċkun", "guessing": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nonograms": "nonogrammi", "kostkirubika": "kostkirubika", "crypticcrosswords": "tislibietkripptiċi", "syberia2": "syberia2", "puzzlehunt": "puzzlehunt", "puzzlehunts": "kaccataluzzles", "catcrime": "del<PERSON><PERSON><PERSON>", "quebracabeça": "rompi<PERSON><PERSON>", "hlavolamy": "puzzles", "poptropica": "<PERSON><PERSON>pi<PERSON>", "thelastcampfire": "laħħarkampfajjar", "autodefinidos": "awtodefiniti", "picopark": "picopark", "wandersong": "wandersong", "carto": "carto", "untitledgoosegame": "loghbatiewz", "cassetête": "casset<PERSON>te", "limbo": "limbu", "rubiks": "rubiks", "maze": "labirint", "tinykin": "ħlejjaq<PERSON><PERSON>", "rubikovakostka": "rubikovakostka", "speedcube": "speedcube", "pieces": "biċċiet", "portalgame": "logħbataportal", "bilmece": "bilmece", "puzzelen": "puzzelen", "picross": "picross", "rubixcube": "rubixcube", "indovinelli": "<PERSON><PERSON><PERSON><PERSON>", "cubomagico": "kubużmaġiku", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "twistedwonderland", "monopoly": "monopolju", "futurefight": "futuruglieda", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "bitlife", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "stilletlaħliem", "stateofsurvival": "stateofsurvival", "mycity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arknights": "arknights", "colorfulstage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloonstowerdefense": "bloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "fategrandorder", "hyperfront": "hyperfront", "knightrun": "ġ<PERSON>jak<PERSON><PERSON>", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "battaljakaċċa", "a3": "a3", "phonegames": "telefonlogħob", "kingschoice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "guardiantales": "guardiantales", "petrolhead": "petrolhead", "tacticool": "tattikukul", "cookierun": "cookierun", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "barra_mill_linja", "craftsman": "<PERSON><PERSON><PERSON><PERSON>", "supersus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slowdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headsup": "attenti", "wordfeud": "wordfeud", "bedwars": "gwererfilfraxx", "freefire": "freefire", "mobilegaming": "logħobmobile", "lilysgarden": "ilġnenntalily", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "clashofclans", "pjsekai": "pjsekai", "mysticmessenger": "mystic<PERSON><PERSON>ger", "callofdutymobile": "callofdutymobile", "thearcana": "larkana", "8ballpool": "8ballpool", "emergencyhq": "ċentruemenġenza", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "hayday", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "tħawwidutremil", "ml": "ml", "bangdream": "bangdream", "clashofclan": "clashofclan", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "prinċipessatażżmien", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "imħabbataskep", "androidgames": "androidgames", "criminalcase": "każkriminali", "summonerswar": "summonerswar", "cookingmadness": "ġenntag<PERSON><PERSON>bbieħ", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "liga<PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "għasafarkejknirgarden", "gachalife": "gachalife", "neuralcloud": "neuralcloud", "mysingingmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "raidshadowlegends", "warrobots": "warrobots", "mirrorverse": "mirrorverse", "pou": "pou", "warwings": "warwings", "fifamobile": "fifamobile", "mobalegendbangbang": "mobalegendbangbang", "evertale": "evertale", "futime": "ħinsbieħ", "antiyoy": "<PERSON><PERSON><PERSON>", "apexlegendmobile": "apexlegendmobile", "ingress": "<PERSON><PERSON><PERSON>", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "punishing<PERSON><PERSON><PERSON>", "petpals": "ħbiebtalannimali", "gameofsultans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arenabreakout": "ħruġmillarena", "wolfy": "lupett", "runcitygame": "logħbataċċittà", "juegodemovil": "logħbamowbajl", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "imitazzjoni", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "bombmebrasil", "ldoe": "ldoe", "legendonline": "legendaonline", "otomegame": "loġbaotome", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "nikkilampanti", "carxdriftracing2": "carxdriftracing2", "pathtonowhere": "darbtalistess", "sealm": "sealm", "shadowfight3": "shadowfight3", "limbuscompany": "limbuscompany", "demolitionderby3": "demolitionderby3", "wordswithfriends2": "wordsmalsħab2", "soulknight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "purrfecttale": "storjapurrfetta", "showbyrock": "showbyrock", "ladypopular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "harvesttown": "ra<PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "ddin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empiresandpuzzles": "imperjietupuzzles", "empirespuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "battlegroundmobileindjana", "fanny": "<PERSON><PERSON>", "littlenightmare": "ħolmaħżinżgħir", "aethergazer": "aethergazer", "mudrunner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tearsofthemis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eversoul": "eversoul", "gunbound": "gunbound", "gamingmlbb": "logħobmlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "zombiestaxxbin", "eveechoes": "eveechoes", "jogocelular": "logħobmowbajl", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "streetfighterduel", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "<PERSON>ħobg<PERSON>", "girlsfrontline": "bnejtfrontline", "jurassicworldalive": "jurassicworldalive", "soulseeker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gettingoverit": "ngħadduhekk", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "moonchaistory", "carxdriftracingonline": "carxdriftracingonline", "jogosmobile": "logħobmobile", "legendofneverland": "leġġendataneverland", "pubglite": "pubglite", "gamemobilelegends": "logħobmobilelegends", "timeraiders": "timeraiders", "gamingmobile": "gamingmobbli", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "ilqattusgħallġlieda", "dnd": "mhux_disponibbli", "quest": "kwesta", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgtafuqilmejda", "worldofdarkness": "dinjataċċlam", "travellerttrpg": "ivvjaġġaturtrpg", "2300ad": "2300ad", "larp": "larp", "romanceclub": "klubbirromanza", "d20": "d20", "pokemongames": "<PERSON>ħob<PERSON>kemon", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonred", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "xufftejnpeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "ipno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "chatot", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonpurpura": "p<PERSON><PERSON><PERSON><PERSON><PERSON>", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "teamrocket", "furret": "furret", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nuzlocke": "nuzlocke", "pokemonplush": "pokemonplush", "teamystic": "teamystic", "pokeball": "pokeball", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "poke<PERSON><PERSON>y", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "għaddej<PERSON><PERSON><PERSON><PERSON>", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON>p", "heyyoupikachu": "ejjintpikacju", "pokémonmaster": "pokémonmaster", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "itt<PERSON>liupokemon", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "bulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "kaċċaturtalleqq", "ajedrez": "<PERSON><PERSON><PERSON><PERSON>", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "skak", "schaken": "schaken", "skak": "skak", "ajedres": "aje<PERSON>s", "chessgirls": "tfulijietxaxx", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "blitz<PERSON><PERSON><PERSON>", "jeudéchecs": "logħbataċċess", "japanesechess": "ċessġappuniż", "chinesechess": "ċessċiniż", "chesscanada": "chessilkanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON>", "rook": "torn", "chesscom": "chesscom", "calabozosydragones": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragon": "dungeonsanddragon", "dungeonmaster": "<PERSON>n<PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "dungeonsanddragons", "oxventure": "oxventure", "darksun": "xem<PERSON>sk<PERSON>", "thelegendofvoxmachina": "illeġġendatavoxmachina", "doungenoanddragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkmoor": "darkmoor", "minecraftchampionship": "kampjonattalminecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "modstalminecraft", "mcc": "mcc", "candleflame": "ħuġġieġa", "fru": "fru", "addons": "addons", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecraftmoddat", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "bejnlartijiet", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "beltaminecraft", "pcgamer": "pcgamer", "jeuxvideo": "logħobkompjuter", "gambit": "gambit", "gamers": "gamers", "levelup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermobile": "gamermowbajl", "gameover": "logħbaspej<PERSON>ż", "gg": "gg", "pcgaming": "logħobtalpc", "gamen": "gamen", "oyunoynamak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pcgames": "pcgamespc", "casualgaming": "logħobkażwali", "gamingsetup": "settupgaming", "pcmasterrace": "pcmasterrace", "pcgame": "logħbapc", "gamerboy": "tifelgamer", "vrgaming": "loghobvr", "drdisrespect": "drdisrespect", "4kgaming": "4kgaming", "gamerbr": "gamerbr", "gameplays": "<PERSON><PERSON><PERSON>", "consoleplayer": "konsolplayer", "boxi": "boxi", "pro": "pro", "epicgamers": "epicgamers", "onlinegaming": "logħobonline", "semigamer": "semigamer", "gamergirls": "gamermara", "gamermoms": "ommijietgamers", "gamerguy": "gamermaskil", "gamewatcher": "osservat<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameur": "gameur", "grypc": "grypc", "rangugamer": "<PERSON><PERSON><PERSON>", "gamerschicas": "gamerschicas", "otoge": "otoge", "dedsafio": "dedsafju", "teamtryhard": "team<PERSON><PERSON>", "mallugaming": "logħobmalti", "pawgers": "pawgers", "quests": "kwestijiet", "alax": "alax", "avgn": "avgn", "oldgamer": "gamerqadim", "cozygaming": "logħobkomdi", "gamelpay": "gamelpay", "juegosdepc": "juegosdepc", "dsswitch": "dsswitch", "competitivegaming": "loghobkompetittiv", "minecraftnewjersey": "minecraftnewjersey", "faker": "fakers", "pc4gamers": "pc4gamers", "gamingff": "<PERSON><PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "logħobheterosexwali", "gamepc": "gamepc", "girlsgamer": "tfjietgamer", "fnfmods": "fnfmods", "dailyquest": "sfidatadijum", "gamegirl": "gejmerja", "chicasgamer": "tfarġamers", "gamesetup": "setuptallogħba", "overpowered": "opżejjed", "socialgamer": "socialgamer", "gamejam": "gamejam", "proplayer": "lugaturserjuz", "roleplayer": "roleplayer", "myteam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republicofgamers": "repubblikatalgamers", "aorus": "aorus", "cougargaming": "logħobcougar", "triplelegend": "leġġendatripla", "gamerbuddies": "ħbiebgamers", "butuhcewekgamers": "bzonncewekgamers", "christiangamer": "gamerinsara", "gamernerd": "g<PERSON><PERSON><PERSON><PERSON>", "nerdgamer": "nerdgamer", "afk": "afk", "andregamer": "andregamer", "casualgamer": "logħobkażwali", "89squad": "89squad", "inicaramainnyagimana": "inizjukemmilħinilu", "insec": "<PERSON><PERSON><PERSON>", "gemers": "ġejmers", "oyunizlemek": "oyunizlemek", "gamertag": "gamertag", "lanparty": "lanparty", "videogamer": "gejmer", "wspólnegranie": "logħoblimkien", "mortdog": "mortdog", "playstationgamer": "gamerplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "gamerbsaħħtu", "gtracing": "gtracing", "notebookgamer": "gamernotebook", "protogen": "protogen", "womangamer": "gamern<PERSON>", "obviouslyimagamer": "ovvjamentjiena<PERSON>", "mario": "mario", "papermario": "papermario", "mariogolf": "<PERSON><PERSON><PERSON><PERSON>", "samusaran": "sa<PERSON><PERSON>", "forager": "qattiegħ", "humanfallflat": "humanfallflat", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "ħarbaħda", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "nintendomużika", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "fallguys", "switch": "<PERSON><PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "leġġendatazelda", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "mario<PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "avukatazza", "ssbm": "ssbm", "skychildrenofthelight": "tfalsmawtielfdawl", "tomodachilife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ahatintime": "sajjamfiżżmien", "tearsofthekingdom": "dmugħtassaltna", "walkingsimulators": "simulat<PERSON><PERSON><PERSON>", "nintendogames": "logħobnintendo", "thelegendofzelda": "leġġendatazelda", "dragonquest": "dragonquest", "harvestmoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "nifs<PERSON>ġewwa", "myfriendpedro": "ħabibipedro", "legendsofzelda": "leġġenditagħżelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "51logħba", "earthbound": "earthbound", "tales": "<PERSON><PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "smashultimate", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "strate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermariomaker": "supermariomaker", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "ġelandbażinfurday", "nintendos": "nintendos", "new3ds": "new3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "gwer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "marioandsonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "nintendogs", "thezelda": "thezelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "riven", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "kliebħomor", "vanillalol": "van<PERSON><PERSON><PERSON>", "wildriftph": "wildriftph", "lolph": "lolph", "leagueoflegend": "leagueoflegend", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspanja", "aatrox": "aatrox", "euw": "euw", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "hexgates", "hextech": "hextech", "fortnitegame": "fortnitegame", "gamingfortnite": "logħobfortnite", "fortnitebr": "fortnitebr", "retrovideogames": "retrovideogames", "scaryvideogames": "vidjogewmsbejża", "videogamemaker": "ħallieqtallogħobvideo", "megamanzero": "megamanzero", "videogame": "videogame", "videosgame": "videogamesijiet", "professorlayton": "<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "battleblocktheater", "arcades": "arkejds", "acnh": "acnh", "puffpals": "ħbiebtatpuff", "farmingsimulator": "simulaturtagħbiedja", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "rob<PERSON><PERSON>", "robloxdeutsch": "robloxdeutsch", "erlc": "erlc", "sanboxgames": "sanboxgames", "videogamelore": "loretalvidjoġejms", "rollerdrome": "rollerdrome", "parasiteeve": "parasiteeve", "gamecube": "gamecube", "starcraft2": "starcraft2", "duskwood": "duskwood", "dreamscape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grand<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadspace": "spazjumejjet", "amordoce": "imħabbagħażiża", "videogiochi": "videogiochi", "theoldrepublic": "ilrepubblikaantika", "videospiele": "logħobtalkompjuter", "touhouproject": "touhouproject", "dreamcast": "dreamcast", "adventuregames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "azjoniaventura", "storyofseasons": "stejjertagħżmien", "retrogames": "retrogames", "retroarcade": "retroarkejd", "vintagecomputing": "komputersantik", "retrogaming": "logħobantiki", "vintagegaming": "ludżikuantiki", "playdate": "<PERSON>ħ<PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "injustizzja2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "logħobtassema", "zenlife": "ħajjażen", "beatmaniaiidx": "beatmaniaiidx", "steep": "<PERSON><PERSON><PERSON><PERSON>", "mystgames": "logħobmisterjużi", "blockchaingaming": "logħobblockchain", "medievil": "medievil", "consolegaming": "logħobtalconsole", "konsolen": "konsolen", "outrun": "aq<PERSON>ż", "bloomingpanic": "panikugħajbien", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "logħobtalbiża", "monstergirlquest": "tfajlaragunamostruwa", "supergiant": "supergigant", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "simulazzjonijietatalfarms", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "logħobjieljackbox", "interactivefiction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "visualnovel": "rumanzviżwali", "visualnovels": "novelliviżwali", "rgg": "rgg", "shadowolf": "<PERSON><PERSON><PERSON>", "tcrghost": "fantasmatlettrix<PERSON><PERSON><PERSON>", "payday": "ġurnatapaga", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "prinċipessatasserqin", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "sandbox", "aestheticgames": "estetikagejms", "novelavisual": "rumanzviżwali", "thecrew2": "thecrew2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "lamento", "godhand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leafblowerrevolution": "rivoluzzjonileafblower", "wiiu": "wiiu", "leveldesign": "disin<PERSON><PERSON><PERSON><PERSON>", "starrail": "starrail", "keyblade": "keyblade", "aplaguetale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fnafsometimes": "fnafxidruni", "novelasvisuales": "rumanzigrafici", "robloxbrasil": "robloxbrasil", "pacman": "pacman", "gameretro": "logħobretro", "videojuejos": "videoju<PERSON><PERSON>", "videogamedates": "laq<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "imħabbtiħelwa", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "everskies", "justcause3": "justcause3", "hulkgames": "logħobhulk", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "irritorntalħisba", "gamstergaming": "gamstergaming", "dayofthetantacle": "ġurnatattentaklu", "maniacmansion": "dart<PERSON>ġ<PERSON><PERSON>", "crashracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "tridimen<PERSON><PERSON><PERSON>", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "hellblade", "storygames": "logħobtastejjer", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "nevi<PERSON><PERSON><PERSON>", "beyondtwosouls": "lilhinnminnżewġerwieħ", "gameuse": "użtalgħab", "offmortisghost": "offmortisghost", "tinybunny": "fenek_żgħir", "retroarch": "retroarch", "powerup": "powerup", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "aventurasgraficas", "quickflash": "<PERSON><PERSON><PERSON><PERSON>", "fzero": "fzero", "gachagaming": "logħobgacha", "retroarcades": "retroarkejds", "f123": "f123", "wasteland": "art", "powerwashsim": "powerwashsim", "coralisland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "dinjaoħra", "metaquest": "metaquest", "animewarrios2": "animewarrios2", "footballfusion": "fużjonitalfutbol", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "<PERSON><PERSON><PERSON><PERSON>", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "ħadidmgħawweġ", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "ħaf<PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "simulatur", "symulatory": "simulaturi", "speedrunner": "speedrunner", "epicx": "epicx", "superrobottaisen": "superrobot<PERSON>sen", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "gry<PERSON>o", "gaiaonline": "gaiaonline", "korkuoyunu": "kor<PERSON><PERSON><PERSON>u", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "ħ<PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "too<PERSON><PERSON><PERSON><PERSON><PERSON>", "simracing": "simracing", "simrace": "simrace", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "ġismijiessmawijin", "seum": "seum", "partyvideogames": "partyvideogames", "graveyardkeeper": "kustodjiċimiterju", "spaceflightsimulator": "spaceflightsimulator", "legacyofkain": "<PERSON><PERSON><PERSON><PERSON>", "hackandslash": "hackand<PERSON><PERSON>", "foodandvideogames": "ikeluvideogames", "oyunvideoları": "logħobvideo", "thewolfamongus": "illupu_fostna", "truckingsimulator": "simulaturtrukkijiet", "horizonworlds": "dinjietorizzont", "handygame": "ħielafidjejja", "leyendasyvideojuegos": "leġġendiuvideogames", "oldschoolvideogames": "logħobvideotalbidu", "racingsimulator": "simulaturtaċċorsa", "beemov": "bee<PERSON>v", "agentsofmayhem": "agentsofmayhem", "songpop": "kanzunettipop", "famitsu": "famitsu", "gatesofolympus": "ħbiberjetaolimpju", "monsterhunternow": "monsterhunternow", "rebelstar": "<PERSON><PERSON><PERSON><PERSON>", "indievideogaming": "logħobvideoindipendenti", "indiegaming": "logħobindiegaming", "indievideogames": "indievideogames", "indievideogame": "videogameindipendenti", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermaninsomniac", "bufffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectl": "projectl", "futureclubgames": "futureclubgames", "mugman": "mugman", "insomniacgames": "insomnjakgejms", "supergiantgames": "supergiantgames", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "celestegame", "aperturescience": "aperturescience", "backlog": "backlog", "gamebacklog": "logħobmhuxmilgħuba", "gamingbacklog": "logħobtalura", "personnagejeuxvidéos": "karattrilogħobvideo", "achievementhunter": "kaccaturitalksebiet", "cityskylines": "orizzontitalbliet", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastlord": "sinjurbestal", "juegosretro": "logħobretro", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "parabolast<PERSON><PERSON>", "reservatoriodedopamin": "ħażnadaddopamina", "staxel": "staxel", "videogameost": "videogameost", "dragonsync": "dragunisinkronizzat", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "inħobbkofxv", "arcanum": "arcanum", "neoy2k": "neoy2k", "pcracing": "tiġrijiet_pc", "berserk": "berserk", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "initiald", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "animesadanime", "darkerthanblack": "iswedmillkemm", "animescaling": "animescaling", "animewithplot": "animebilplot", "pesci": "pesci", "retroanime": "retroanime", "animes": "animes", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "<PERSON><PERSON><PERSON>", "90sanime": "anime90s", "darklord": "sidizzlam", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON>", "masterpogi": "masterpogi", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "anime2000s", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "animecover", "thevisionofescaflowne": "ilvizjonitaescaflowne", "slayers": "slayers", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON>ħut", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "toiletboundhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "skip<PERSON><PERSON><PERSON>", "vanitas": "vanitas", "fireforce": "<PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "djar<PERSON>_tal_futur", "fairytail": "ħraj<PERSON><PERSON>", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON>", "parasyte": "<PERSON><PERSON><PERSON><PERSON>", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "beastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "melodi<PERSON><PERSON><PERSON><PERSON>", "kamisamakiss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blmanga": "blmanga", "horrormanga": "mangatalġrub", "romancemangas": "mangasromantiċi", "karneval": "<PERSON><PERSON><PERSON>", "dragonmaid": "dragonmaid", "blacklagoon": "lagunaiswed", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "terraformars", "geniusinc": "ġenjufirma", "shamanking": "shamanking", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "kliebmitlufinbungo", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "indiċimaġikuċert", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "onepunchman", "hetalia": "hetalia", "kagerouproject": "proġettkagerou", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8theinfinity", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "anġlidilmewt", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "ipnożimużika", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "monstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "animesport", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "fistofthenorthstar", "mazinger": "mazinger", "blackbuttler": "ma<PERSON><PERSON><PERSON><PERSON>", "towerofgod": "torntaalla", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "ċibi", "servamp": "servamp", "howtokeepamummy": "kiftżommilmummy", "fullmoonwosagashite": "sagħtuġermla", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "martialpeak": "qucctatalmartial", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotnejn", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "ħasħtagsaħħara", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "vanny", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "sailorsaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "runa", "oldanime": "animeqadim", "chainsawman": "chainsawman", "bungoustraydogs": "kelbibtajjietastray", "jogo": "jogo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON>mi", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "blackbutler", "ergoproxy": "ergoproxy", "claymore": "klemore", "loli": "loli", "horroranime": "animetalbiża", "fruitsbasket": "fruitsbasket", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "ħajja<PERSON>ħab<PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "thepromisedneverland", "monstermanga": "monstermanga", "yourlieinapril": "gidbiektekfapril", "buggytheclown": "buggytheclown", "bokunohero": "bokunohero", "seraphoftheend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "magi", "deepseaprisoner": "priġuniertalbaħarfond", "jojolion": "jojo<PERSON>", "deadmanwonderland": "deadmanwonderland", "bannafish": "bannafish", "sukuna": "<PERSON>kuna", "darwinsgame": "dar<PERSON><PERSON><PERSON>", "husbu": "<PERSON><PERSON><PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "gwer<PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "linja<PERSON><PERSON><PERSON><PERSON>", "toyoureternity": "għaddejjiemietà", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "akamegakill", "blueperiod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "alleanzasig<PERSON><PERSON>", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "<PERSON><PERSON><PERSON>", "bluelock": "bluelock", "goblinslayer": "qattielgoblins", "detectiveconan": "detective<PERSON><PERSON>", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "shojobeat", "vampireknight": "vampirilvant", "mugi": "mugi", "blueexorcist": "exor<PERSON><PERSON><PERSON>", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "mashle", "scryed": "scryed", "spyfamily": "familjaspija", "airgear": "airgear", "magicalgirl": "tfajlamaġika", "thesevendeadlysins": "isdnubimwiet", "prisonschool": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "thegodofhighschool": "allatasskola", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "grandblue", "mydressupdarling": "ippop<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animunivers", "swordartonlineabridge": "<PERSON><PERSON><PERSON>lineabridge", "saoabridged": "saoabridged", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "xortaħaż<PERSON>", "romancemanga": "mangataromanza", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "animeromantiċi", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "demonslayergħ<PERSON><PERSON><PERSON>", "bloodlad": "bloodlad", "goodbyeeri": "għ<PERSON><PERSON><PERSON>dd<PERSON>eri", "firepunch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adioseri": "<PERSON><PERSON><PERSON><PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romanceanime": "animeromantiku", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "saħarċirasa", "housekinokuni": "<PERSON><PERSON><PERSON><PERSON>", "recordragnarok": "irrekordjarragnarok", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "<PERSON><PERSON><PERSON><PERSON>", "highschoolofthedead": "liskolasekondarjatalmejtin", "germantechno": "teknotedeska", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "prinċtattennis", "tonikawa": "<PERSON>ika<PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "<PERSON><PERSON><PERSON>", "animemanga": "anim<PERSON><PERSON>", "bakuman": "bakuman", "deathparade": "deathparade", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "animaġappu<PERSON>ż", "animespace": "animespace", "girlsundpanzer": "girlsundpanzer", "akb0048": "akb0048", "hopeanuoli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animedub": "animedub", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "possessurtaluq", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "ġurdien", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "neko<PERSON>", "gashbell": "gashbell", "peachgirl": "tfajlabressqana", "cavalieridellozodiaco": "kavaljeritażżodjaku", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "qalbilmanga", "deliciousindungeon": "bnintaf<PERSON><PERSON><PERSON><PERSON>", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "recordofragnarok", "funamusea": "gostaqti<PERSON>ħallas", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "aqbeżgħallloafer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "tutorial<PERSON><PERSON><PERSON><PERSON>", "overgeared": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "ravemaster", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "ateljuewieħedsaħħara", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "sangatsunolion", "kamen": "kamen", "mangaislife": "ilmangahijiħajja", "dropsofgod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loscaballerosdelzodia": "loscaballerosdelzodia", "animeshojo": "animeshojo", "reverseharem": "ħ<PERSON><PERSON>revers", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "għallimkbirtonizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "ilbosspapàtiegħi", "gear5": "gear5", "grandbluedreaming": "ħolmasupergrandiġ<PERSON>lu", "bloodplus": "demmpluss", "bloodplusanime": "bloodplusanime", "bloodcanime": "<PERSON><PERSON><PERSON><PERSON>", "bloodc": "demmc", "talesofdemonsandgods": "stejjertaxejjatinusiuwijiet", "goreanime": "goreanime", "animegirls": "b<PERSON><PERSON><PERSON>", "sharingan": "<PERSON><PERSON>", "crowsxworst": "crowsxlagħar", "splatteranime": "animesplatter", "splatter": "splatter", "risingoftheshieldhero": "ittellgħtalerojtattarka", "somalianime": "animesomali", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedaħ<PERSON>", "animeyuri": "animeyuri", "animeespaña": "animeespanja", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "tfaldilbalieni", "liarliar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supercampeones": "superkampjuni", "animeidols": "animeidols", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalgirls": "tiflatmaġiċi", "callofthenight": "illejlgħaddejjinsejjaħulek", "bakuganbrawler": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "ġninataċċellel", "tsubasachronicle": "tsubasachronicle", "findermanga": "sib<PERSON>ga", "princessjellyfish": "princepessabringuż", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "bewsatalġ<PERSON>na", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "animeverse", "persocoms": "persokoms", "omniscientreadersview": "ħarsatalqaritallijafoħxix", "animecat": "qattusanime", "animerecommendations": "rakkomandazzjonijietanime", "openinganime": "animebidwi", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "ilkummiedjaromantikatatteenagerta", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "gundams", "voltesv": "voltesv", "giantrobots": "<PERSON><PERSON><PERSON><PERSON>", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "mobilefighterggundam", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "mekk", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "thebigoanime", "bleach": "bleach", "deathnote": "deathnote", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmetalalchemist": "fullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "militaryjanime", "greenranger": "greenranger", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "lupinthe3rd", "animecity": "animecity", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "digimonadventure", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "juju<PERSON><PERSON><PERSON><PERSON>", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "demonslayer", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "attackontitan", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "surveycorps", "onepieceanime": "animeonepiecemt", "attaquedestitans": "attaquedestitans", "theonepieceisreal": "theonepiecehuwireal", "revengers": "vendi<PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "effettjoyboymt", "digimonstory": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "ħabssuprem", "metalocalypse": "metalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "ouranhostclub", "flawlesswebtoon": "webtoonflekkless", "kemonofriends": "ħbiebkemono", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "nichijou", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "ħaġragħ<PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "għaxiva", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "triqil<PERSON>dd<PERSON>nkolllha", "recuentosdelavida": "ġrajjietminnħajja"}