{"2048": "2048", "mbti": "mbti", "enneagram": "enneagram", "astrology": "izinkwezi", "cognitivefunctions": "imisebenziyengqondo", "psychology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "philosophy": "filos<PERSON><PERSON>", "history": "imbali", "physics": "ifiziksi", "science": "<PERSON><PERSON><PERSON><PERSON>", "culture": "inkcubeko", "languages": "iilwimi", "technology": "iteknoloji", "memes": "iimemes", "mbtimemes": "imbtimemes", "astrologymemes": "iimemeszezinkwenkwezi", "enneagrammemes": "iimemezeenneagram", "showerthoughts": "iingcingauphuma", "funny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videos": "<PERSON><PERSON><PERSON><PERSON>", "gadgets": "izixhobo", "politics": "ezopolitiko", "relationshipadvice": "amacebongothando", "lifeadvice": "icebisongobomi", "crypto": "crypto", "news": "iindaba", "worldnews": "iindabaehlabathi", "archaeology": "imbali_ye<PERSON><PERSON>_zak<PERSON>la", "learning": "ukufunda", "debates": "iingxoxo", "conspiracytheories": "ii<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "universe": "indawo_yonke", "meditation": "ukucamngca", "mythology": "intsomi", "art": "ubug<PERSON>a", "crafts": "imisebenzi_yezandla", "dance": "danisa", "design": "<PERSON><PERSON><PERSON>", "makeup": "makeup", "beauty": "u<PERSON><PERSON>e", "fashion": "<PERSON><PERSON><PERSON>", "singing": "ucula", "writing": "ukubhala", "photography": "ukufota", "cosplay": "cosplay", "painting": "<PERSON><PERSON><PERSON><PERSON>", "drawing": "ukuzoba", "books": "iincwadi", "movies": "iimovie", "poetry": "umbongo", "television": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filmmaking": "ukwen<PERSON><PERSON>lim<PERSON>", "animation": "<PERSON><PERSON><PERSON><PERSON>", "anime": "anime", "scifi": "<PERSON><PERSON><PERSON><PERSON>", "fantasy": "<PERSON><PERSON><PERSON><PERSON>", "documentaries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mystery": "<PERSON><PERSON><PERSON><PERSON>", "comedy": "hle<PERSON>a", "crime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drama": "idrama", "bollywood": "ibollywood", "kdrama": "idrama_yasekorea", "horror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romance": "uthando", "realitytv": "umdlalowetv", "action": "ntshuku<PERSON>", "music": "umculo", "blues": "ukukhathazeka", "classical": "iklasiki", "country": "<PERSON><PERSON><PERSON>", "desi": "umxhosa", "edm": "edm", "electronic": "ombane", "folk": "a<PERSON><PERSON>", "funk": "funk", "hiphop": "ihiphop", "house": "indlu", "indie": "indie", "jazz": "umculo_wejazz", "kpop": "kpop", "latin": "ilatin", "metal": "insimbi", "pop": "pop", "punk": "ipunk", "rnb": "irb", "rap": "irap", "reggae": "reggae", "rock": "inyawo", "techno": "itekno", "travel": "<PERSON><PERSON><PERSON>", "concerts": "ii<PERSON><PERSON><PERSON>", "festivals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "museums": "iimyuziyam", "standup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theater": "<PERSON><PERSON><PERSON><PERSON>", "outdoors": "phandle", "gardening": "ukulima", "partying": "ukugxada", "gaming": "umdlalo", "boardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeonsanddragons": "imigodiekunyeimikhulu", "chess": "i<PERSON>i", "fortnite": "fortnite", "leagueoflegends": "umhlanganoweentshabalalo", "starcraft": "starcraft", "minecraft": "minecraft", "pokemon": "pokemon", "food": "ukutya", "baking": "<PERSON><PERSON><PERSON><PERSON>", "cooking": "ukupheka", "vegetarian": "<PERSON><PERSON><PERSON>", "vegan": "ngomnge<PERSON>yama", "birds": "iintaka", "cats": "iikati", "dogs": "<PERSON><PERSON><PERSON>", "fish": "intlanzi", "animals": "i<PERSON>lwanyana", "blacklivesmatter": "ubomibabantumnyamabulungile", "environmentalism": "uk<PERSON><PERSON>uk<PERSON><PERSON>", "feminism": "<PERSON><PERSON><PERSON><PERSON>", "humanrights": "amalungoluntu", "lgbtqally": "umxhasilgbtq", "stopasianhate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transally": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "volunteering": "ukuvolontiya", "sports": "zemidlalo", "badminton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "baseball": "ib<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i", "basketball": "ibhol<PERSON>_yo<PERSON><PERSON>ma", "boxing": "imbhoxo", "cricket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cycling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fitness": "ukuz<PERSON>longa", "football": "ib<PERSON><PERSON>", "golf": "i<PERSON><PERSON>a", "gym": "ijim", "gymnastics": "ukuzivocavoca", "hockey": "ihoki", "martialarts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "netball": "ib<PERSON><PERSON>", "pilates": "ipilates", "pingpong": "ipingpong", "running": "ukubaleka", "skateboarding": "ukutyibiliza_ngesikeyitib<PERSON>di", "skiing": "ukutyibiliza", "snowboarding": "ukutyibiliza_ekhephuni", "surfing": "ukusefa", "swimming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tennis": "ithen<PERSON>", "volleyball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weightlifting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yoga": "iyoga", "scubadiving": "ukuntywiliselwandle", "hiking": "ukuh<PERSON><PERSON>_entabeni", "capricorn": "icapricorn", "aquarius": "aquarius", "pisces": "intlanzi", "aries": "inyanga", "taurus": "itaurus", "gemini": "gemini", "cancer": "umhlaza", "leo": "leo", "virgo": "inyanga", "libra": "<PERSON><PERSON><PERSON>", "scorpio": "iskor<PERSON>yon<PERSON>", "sagittarius": "sagittarius", "shortterm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casual": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "longtermrelationship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "single": "ngash<PERSON><PERSON>", "polyamory": "uthan<PERSON>_lwab<PERSON><PERSON>_a<PERSON>", "enm": "enm", "lgbt": "lgbt", "lgbtq": "lgbtq", "gay": "gay", "lesbian": "lesbian", "bisexual": "bisexual", "pansexual": "pansexual", "asexual": "asexual", "reddeadredemption2": "reddeadredemption2", "dragonage": "dragonage", "assassinscreed": "assassinscreed", "saintsrow": "saintsrow", "danganronpa": "dangan<PERSON><PERSON>", "deltarune": "deltarune", "watchdogs": "izinjazomhlaba", "dislyte": "dislyte", "rougelikes": "rougelikes", "kingsquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulreaver": "umthuthikomphefumlo", "suikoden": "su<PERSON><PERSON>", "subverse": "inda<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofspyro": "intsomikaspyro", "rouguelikes": "rouguelikes", "syberia": "syberia", "rdr2": "rdr2", "spyrothedragon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsdogma": "dragonsdogma", "sunsetoverdrive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arkham": "arkham", "deusex": "deusex", "fireemblemfates": "fireemblemfates", "yokaiwatch": "yokaiwatch", "rocksteady": "qina", "litrpg": "litrpg", "haloinfinite": "haloinfinite", "guildwars": "guildwars", "openworld": "umhlabavulekile", "heroesofthestorm": "amaqhawelaludongondongo", "cytus": "cytus", "soulslike": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dungeoncrawling": "ukuphenyaphenyango<PERSON>liba", "jetsetradio": "jetsetradio", "tribesofmidgard": "iintlangazasemidgard", "planescape": "indawoyenqwelomoya", "lordsoftherealm2": "iinkosiezomhlaba2", "baldursgate": "<PERSON><PERSON><PERSON>", "colorvore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "medabots": "iimedabots", "lodsoftherealm2": "abalawulibommandla2", "patfofexile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersivesims": "iisimim<PERSON><PERSON><PERSON><PERSON><PERSON>", "okage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "juegoderol": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "witcher": "<PERSON><PERSON><PERSON><PERSON>", "dishonored": "ihlazekile", "eldenring": "elden<PERSON>", "darksouls": "darksouls", "kotor": "kotor", "wynncraft": "wynn<PERSON>", "witcher3": "witcher3", "fallout": "<PERSON><PERSON><PERSON><PERSON>", "fallout3": "fallout3", "fallout4": "fallout4", "skyrim": "skyrim", "elderscrolls": "elderscrolls", "modding": "ukumod<PERSON>", "charactercreation": "ukwenzaumlinganiswa", "immersive": "ngxubekezayo", "falloutnewvegas": "falloutnewvegas", "bioshock": "bioshock", "omori": "omori", "finalfantasyoldschool": "<PERSON><PERSON><PERSON><PERSON><PERSON>dala", "ffvii": "ffvii", "ff6": "ff6", "finalfantasy": "finalfantasy", "finalfantasy14": "finalfantasy14", "finalfantasyxiv": "finalfantasyxiv", "ff14": "ff14", "ffxiv": "ffxiv", "ff13": "ff13", "finalfantasymatoya": "finalfantasymatoya", "lalafell": "<PERSON><PERSON><PERSON>", "dissidia": "dissidia", "finalfantasy7": "finalfantasy7", "ff7": "ff7", "morbidmotivation": "ukuphembelelwakufileyo", "finalfantasyvii": "finalfantasyvii", "ff8": "ff8", "otome": "otome", "suckerforlove": "ndi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otomegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stardew": "stardew", "stardewvalley": "stardewvalley", "ocarinaoftime": "iocarinayexesha", "yiikrpg": "yiikrpg", "vampirethemasquerade": "ivampirethemasquerade", "dimension20": "dimension20", "gaslands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pathfinder2ndedition": "pathfinder2ndedition", "shadowrun": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodontheclocktower": "igazikunomtlikwelokhula", "finalfantasy15": "finalfantasy15", "finalfantasy11": "finalfantasy11", "finalfantasy8": "finalfantasy8", "ffxvi": "ffxvi", "lovenikki": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drakengard": "<PERSON><PERSON><PERSON><PERSON>", "gravityrush": "ukubaleka_komxhuzulane", "rpg": "rpg", "dota2": "dota2", "xenoblade": "xenoblade", "oneshot": "isiboniso1", "rpgmaker": "umdlaliwerpg", "osrs": "osrs", "overlord": "<PERSON><PERSON><PERSON>", "yourturntodie": "lithubalakulokufa", "persona3": "persona3", "rpghorror": "rpgoyikisayo", "elderscrollsonline": "elderscrollsonline", "reka": "reka", "honkai": "honkai", "marauders": "o<PERSON><PERSON><PERSON>", "shinmegamitensei": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "epicseven": "epicseven", "rpgtext": "rp<PERSON><PERSON><PERSON>", "genshin": "genshin", "eso": "eso", "diablo2": "diablo2", "diablo2lod": "diablo2lod", "morrowind": "morrowind", "starwarskotor": "<PERSON><PERSON><PERSON><PERSON>", "demonsouls": "idimoni", "mu": "mu", "falloutshelter": "indawoyokufihlakunyukabhombu", "gurps": "gurps", "darkestdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eclipsephase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disgaea": "disgaea", "outerworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arpg": "arpg", "crpg": "crpg", "bindingofisaac": "bindingofisaac", "diabloimmortal": "diabloimmortal", "dynastywarriors": "imikhosiyemfazwe", "skullgirls": "skullgirls", "nightcity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hogwartslegacy": "hogwartslegacy", "madnesscombat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jaggedalliance2": "jaggedalliance2", "neverwinter": "akusozekubebusika", "road96": "indlela96", "vtmb": "vtmb", "chimeraland": "chimeraland", "homm3": "homm3", "fe3h": "fe3h", "roguelikes": "roguelikes", "gothamknights": "amagorhalikang<PERSON>ye", "forgottenrealms": "imimangorealms", "dragonlance": "dragonlance", "arenaofvalor": "arenayobugorha", "ffxv": "ffxv", "ornarpg": "ornarpg", "toontown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childoflight": "umntwanawokukhanya", "aq3d": "aq3d", "mogeko": "mogeko", "thedivision2": "thedivision2", "lineage2": "lineage2", "digimonworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterrancher": "umfuyiwezilwanyana", "ecopunk": "ecopunk", "vermintide2": "uhlaselo_lweempuku2", "xeno": "xeno", "vulcanverse": "vulcanverse", "fracturedthrones": "iitroneziqhekekileyo", "horizonforbiddenwest": "<PERSON>ng<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twewy": "twewy", "shadowpunk": "umthunzipunk", "finalfantasyxv": "finalfantasyxv", "everoasis": "<PERSON><PERSON><PERSON>", "hogwartmystery": "hogwartmystery", "deltagreen": "deltaluhlaza", "diablo": "diablo", "diablo3": "diablo3", "diablo4": "diablo4", "smite": "gilima", "lastepoch": "ixeshaelokugqibela", "starfinder": "umfumaninkwenkwezi", "goldensun": "ilangalegolide", "divinityoriginalsin": "ubunkulunkulubokuqala", "bladesinthedark": "iimvijeloxhwebeni", "twilight2000": "iphakade2000", "sandevistan": "sandevistan", "cyberpunk": "icyberpunk", "cyberpunk2077": "cyberpunk2077", "cyberpunkred": "cyberpunkbomvu", "dragonballxenoverse2": "dragonballxenoverse2", "fallenorder": "ukuwaehlayo", "finalfantasyxii": "finalfantas<PERSON>ii", "evillands": "amazwelububi", "genshinimact": "genshinimact", "aethyr": "aethyr", "devilsurvivor": "<PERSON><PERSON><PERSON>ip<PERSON><PERSON><PERSON><PERSON>", "oldschoolrunescape": "oldschoolrunescape", "finalfantasy10": "finalfantasy10", "anime5e": "anime5e", "divinity": "ubunkulunkulu", "pf2": "pf2", "farmrpg": "farmrpg", "oldworldblues": "iintlungumntuwehlabathindala", "adventurequest": "uhambolufuna", "dagorhir": "<PERSON><PERSON><PERSON>", "roleplayingames": "imidlaloyokuzenzisa", "roleplayinggames": "imidlaloykuzenzisa", "finalfantasy9": "finalfantasy9", "sunhaven": "ilanga", "talesofsymphonia": "iintsomizesymphonia", "honkaistarrail": "honkaistarrail", "wolong": "wolong", "finalfantasy13": "finalfantasy13", "daggerfall": "daggerfall", "torncity": "umzimhlabaoqhekekileyo", "myfarog": "myfarog", "sacredunderworld": "<PERSON>bu<PERSON><PERSON><PERSON><PERSON><PERSON>", "chainedechoes": "chained_echoes", "darksoul": "umphe<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "soulslikes": "imiphefum<PERSON>efanayo", "othercide": "ngamanye", "mountandblade": "mountandblade", "inazumaeleven": "inazumaeleven", "acvalhalla": "acvalhalla", "chronotrigger": "chronotrigger", "pillarsofeternity": "iintsikayobunaphakade", "palladiumrpg": "palladiumrpg", "rifts": "izingxabano", "tibia": "tibia", "thedivision": "<PERSON><PERSON><PERSON>", "hellocharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofdragoon": "intsomikaludragoon", "xenobladechronicles2": "xenobladechronicles2", "vampirolamascarada": "ivampayiribaling<PERSON><PERSON>", "octopathtraveler": "octop<PERSON><PERSON><PERSON><PERSON>", "afkarena": "<PERSON><PERSON><PERSON><PERSON>", "werewolftheapocalypse": "<PERSON><PERSON><PERSON><PERSON>", "aveyond": "aveyond", "littlewood": "encane", "childrenofmorta": "abantwana<PERSON><PERSON><PERSON><PERSON>", "engineheart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fable3": "intsomi3", "fablethelostchapter": "intsomiyobalilahlekileyo", "hiveswap": "tshintshiselananesibuba", "rollenspiel": "rollenspiel", "harpg": "harpg", "baldursgates": "baldurs<PERSON>s", "edeneternal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfantasy16": "finalfantasy16", "andyandleyley": "<PERSON><PERSON><PERSON><PERSON>", "ff15": "ff15", "starfield": "<PERSON><PERSON><PERSON><PERSON>", "oldschoolrevival": "ukuvuselelwak<PERSON><PERSON><PERSON>", "finalfantasy12": "finalfantasy12", "ff12": "ff12", "morkborg": "morkborg", "savageworlds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "diabloiv": "diabloiv", "pve": "pve", "kingdomheart1": "kingdomheart1", "ff9": "ff9", "kingdomheart2": "kingdomheart2", "darknessdungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosrpg": "imidlalorpg", "kingdomhearts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomheart3": "kingdomheart3", "finalfantasy6": "finalfantasy6", "ffvi": "ffvi", "clanmalkavian": "usapholomalkavian", "harvestella": "<PERSON><PERSON><PERSON>", "gloomhaven": "gloomhaven", "wildhearts": "iintliziywezasendle", "bastion": "inqaba", "drakarochdemoner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiesofarcadia": "isibhakabhakasearcadia", "shadowhearts": "iintliziywezithunzi", "nierreplicant": "nierreplicant", "gnosia": "gnosia", "pennyblood": "iga<PERSON>_elincinci", "breathoffire4": "umlilowephefumlo4", "mother3": "umama3", "cyberpunk2020": "cyberpunk2020", "falloutbos": "falloutbos", "anothereden": "enkoloenye", "roleplaygames": "imidlaloyokuzenzisa", "roleplaygame": "umdlalowokudlalaidima", "fabulaultima": "um<PERSON>lis<PERSON><PERSON><PERSON><PERSON><PERSON>", "witchsheart": "intliziywegqwirha", "harrypottergame": "<PERSON>dl<PERSON>ka<PERSON><PERSON><PERSON><PERSON>", "pathfinderrpg": "pathfinderrpg", "pathfinder2e": "pathfinder2e", "vampirilamasquerade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dračák": "id<PERSON><PERSON>", "spelljammer": "umlingo", "dragonageorigins": "dragonage<PERSON><PERSON>", "chronocross": "ixeshalonqamlezayo", "cocttrpg": "cocttrpg", "huntroyale": "<PERSON><PERSON><PERSON>", "albertodyssey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunterworld": "umhlabawokuzingelaizilo", "bg3": "bg3", "xenogear": "xenogear", "temtem": "temtem", "rpgforum": "rpgforum", "shadowheartscovenant": "isivumelwanosikashodowheart", "bladesoul": "blades<PERSON>l", "baldursgate3": "baldursgate3", "kingdomcome": "kufikilelizwekangumkani", "awplanet": "awplanet", "theworldendswithyou": "iphelelangomhlabangawe", "dragalialost": "dragalialost", "elderscroll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dyinglight2": "dyinglight2", "finalfantasytactics": "finalfantasytactics", "grandia": "grandia", "darkheresy": "ubuxokixoki", "shoptitans": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forumrpg": "forumrpg", "golarion": "golarion", "earthmagic": "umlingo_womhlaba", "blackbook": "incwa<PERSON><PERSON><PERSON><PERSON>", "skychildrenoflight": "abantwanabezuluekhanyeni", "gryrpg": "gryrpg", "sacredgoldedition": "ingcweleigolide", "castlecrashers": "iinqabayokugqobhoza", "gothicgame": "umdlalowegothic", "scarletnexus": "<PERSON><PERSON><PERSON>", "ghostwiretokyo": "ghostwire<PERSON><PERSON>", "fallout2d20": "fallout2d20", "gamingrpg": "imidumdlobeyerpg", "prophunt": "ukuzingela", "starrails": "iindlelazenkwenkwezi", "cityofmist": "isixekosasebumfiliba", "indierpg": "indierpg", "pointandclick": "cofa_ucofele", "emilyisawaytoo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emilyisaway": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indivisible": "ngekwahlukaniyo", "freeside": "<PERSON><PERSON><PERSON><PERSON>", "epic7": "epic7", "ff7evercrisis": "ff7evercrisis", "xenogears": "xenogears", "megamitensei": "megamitensei", "symbaroum": "symbaroum", "postcyberpunk": "postcyberpunk", "deathroadtocanada": "indlelayokufaecanada", "palladium": "<PERSON><PERSON><PERSON><PERSON>", "knightjdr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "monsterhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fireemblem": "fireemblem", "genshinimpact": "genshinimpact", "geosupremancy": "<PERSON>bunga<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "persona5": "persona5", "ghostoftsushima": "umoyawetsushima", "sekiro": "<PERSON><PERSON>ro", "monsterhunterrise": "ukuz<PERSON>lau<PERSON>khov<PERSON>", "nier": "nier", "dothack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ys": "ys", "souleater": "umtyimbawomphefumlo", "fatestaynight": "fatestaynight", "etrianodyssey": "etrianodyssey", "nonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tacticalrpg": "umdlaloweqhingarpg", "mahoyo": "mahoyo", "animegames": "imidlaloeanime", "damganronpa": "damganronpa", "granbluefantasy": "granbluefantasy", "godeater": "umtyingqo", "diluc": "diluc", "venti": "venti", "eternalsonata": "umculoongapheli<PERSON>", "princessconnect": "ukudibanaisaprincess", "hexenzirkel": "umbuthowamagqwirha", "cristales": "iikristale", "vcs": "iivcs", "pes": "pes", "pocketsage": "is<PERSON><PERSON><PERSON>ep<PERSON><PERSON><PERSON>", "valorant": "valorant", "valorante": "valorante", "valorantindian": "valorantum<PERSON>", "dota": "dota", "madden": "ndiqumbile", "cdl": "cdl", "efootbal": "efootbal", "nba2k": "nba2k", "egames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa23": "fifa23", "wwe2k": "wwe2k", "esport": "<PERSON>dlalo<PERSON>kh<PERSON><PERSON><PERSON><PERSON>", "mlg": "mlg", "leagueofdreamers": "umbut<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fifa14": "fifa14", "midlaner": "umid<PERSON><PERSON>", "efootball": "ib<PERSON><PERSON>", "dreamhack": "iphup<PERSON>yakh<PERSON>", "gaimin": "<PERSON><PERSON><PERSON><PERSON>", "overwatchleague": "umdlaloweoverwatchleague", "cybersport": "umdl<PERSON>_we<PERSON><PERSON><PERSON><PERSON>a", "crazyraccoon": "irakhunikiyahlanya", "test1test": "test1test", "fc24": "fc24", "riotgames": "imidlalokalunyano", "eracing": "icimaboo", "brasilgameshow": "brasilgameshow", "valorantcompetitive": "valo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "t3arena": "t3arena", "valorantbr": "valorantbr", "csgo": "csgo", "tf2": "tf2", "portal2": "portal2", "halflife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "left4dead": "bashiyeaba<PERSON><PERSON>leyo", "left4dead2": "left4dead2", "valve": "<PERSON><PERSON><PERSON>", "portal": "<PERSON><PERSON><PERSON><PERSON>", "teamfortress2": "teamfortress2", "everlastingsummer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goatsimulator": "imfulasimulator", "garrysmod": "gar<PERSON><PERSON><PERSON>", "freedomplanet": "inkululekoyomhlaba", "transformice": "transformice", "justshapesandbeats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlefield4": "imfazweyomlo4", "nightinthewoods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halflife2": "halflife2", "hacknslash": "nqakranxib<PERSON>", "deeprockgalactic": "deeprockgalactic", "riskofrain2": "riskofrain2", "metroidvanias": "imetroidvanias", "overcooked": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interplanetary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helltaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inscryption": "ukubhala_okufihliweyo", "7d2d": "7d2d", "deadcells": "iiselidead", "nierautomata": "ni<PERSON><PERSON><PERSON>", "gmod": "gmod", "dwarffortress": "dwarffortress", "foxhole": "umngxuma", "stray": "zim<PERSON><PERSON>", "battlefield": "idabiyokudubula", "battlefield1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swtor": "swtor", "fallout2": "fallout2", "uboat": "inqanawa", "eyeb": "<PERSON><PERSON><PERSON>", "blackdesert": "intlang<PERSON><PERSON><PERSON><PERSON>", "tabletopsimulator": "umdlalowetafileophezulu", "partyhard": "groovakakhulu", "hardspaceshipbreaker": "hardspaceshipbreaker", "hades": "<PERSON><PERSON><PERSON><PERSON>", "gunsmith": "umkhandi_wemipu", "okami": "<PERSON>ami", "trappedwithjester": "kuvaleleke_nejester", "dinkum": "nyani", "predecessor": "<PERSON><PERSON><PERSON><PERSON>", "rainworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cavesofqud": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colonysim": "umlinganisowekholoni", "noita": "noita", "dawnofwar": "ekuqalenikomfazwe", "minionmasters": "iinkosizeminionslekha", "grimdawn": "grimdawn", "darkanddarker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "motox": "motox", "blackmesa": "blackmesa", "soulworker": "umsebenzikaziphefumlo", "datingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yaga": "yaga", "cubeescape": "ukubalekacube", "hifirush": "hifirush", "svencoop": "svencoop", "newcity": "isixekots<PERSON>", "citiesskylines": "imiz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defconheavy": "defconzimbi", "kenopsia": "ikenopsia", "virtualkenopsia": "ivirtualkenopsia", "snowrunner": "iq<PERSON>_eligi<PERSON><PERSON>", "libraryofruina": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "l4d2": "l4d2", "thenonarygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "omegastrikers": "omegastrikers", "wayfinder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kenabridgeofspirits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placidplasticduck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battlebit": "idabilikhom<PERSON>utha", "ultimatechickenhorse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dialtown": "<PERSON><PERSON><PERSON>", "smileforme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catnight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supermeatboy": "supermeatboy", "tinnybunny": "inci<PERSON><PERSON>", "cozygrove": "cozygrovexh", "doom": "inkathazo", "callofduty": "callofduty", "callofdutyww2": "callofdutyww2", "rainbow6": "rainbow6", "apexlegends": "apexlegends", "cod": "cod", "borderlands": "imida", "pubg": "pubg", "callofdutyzombies": "callofdutyzombies", "apex": "incopho", "r6siege": "r6siege", "megamanx": "megamanx", "touhou": "<PERSON><PERSON><PERSON>", "farcry": "farcry", "farcrygames": "imidlalofarcrygames", "paladins": "amapaladins", "earthdefenseforce": "umkhosiwokukhuselajong<PERSON>zwe", "huntshowdown": "ukuzingelaokubonisa", "ghostrecon": "ghostrecon", "grandtheftauto5": "grandtheftauto5", "warz": "imfazwe", "sierra117": "sierra117", "dayzstandalone": "dayzstandalone", "ultrakill": "uk<PERSON><PERSON><PERSON>_ngamandla", "joinsquad": "ng<PERSON><PERSON><PERSON><PERSON><PERSON>", "echovr": "echovr", "discoelysium": "discoelysium", "insurgencysandstorm": "uvukelolwesip<PERSON>th<PERSON>", "farcry3": "farcry3", "hotlinemiami": "hotlinemiami", "maxpayne": "maxpayne", "hitman3": "umgulukudu3", "r6s": "r6s", "rainbowsixsiege": "rainbowsixsiege", "deathstranding": "ukufaikwelizwe", "b4b": "b4b", "codwarzone": "codwarzone", "callofdutywarzone": "callofdutywarzone", "codzombies": "codzombies", "mirrorsedge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "divisions2": "imiyalelo2", "killzone": "umbulali", "helghan": "hel<PERSON>", "coldwarzombies": "iizombizemfazweenqabeneyo", "metro2033": "metro2033", "metalgear": "imetalgear", "acecombat": "ukunqandaulwa", "crosscode": "ikhowudicross", "goldeneye007": "goldeneye007", "blackops2": "blackops2", "sniperelite": "umgwebiwekhethekileyo", "modernwarfare": "imfazweyanamhlanje", "neonabyss": "indongamnyama", "planetside2": "planetside2", "mechwarrior": "umechanikiwemfazwe", "boarderlands": "borderlands", "owerwatch": "ower<PERSON>", "rtype": "ruhlobo", "dcsworld": "dcsworld", "escapefromtarkov": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metalslug": "imetalieslug", "primalcarnage": "unyan<PERSON><PERSON>_lwenyama", "worldofwarships": "worldofwarships", "back4blood": "buyela4gazi", "warframe": "warframe", "rainbow6siege": "rainbow6siege", "xcom": "xcom", "hitman": "umbulali", "masseffect": "masseffect", "systemshock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valkyriachronicles": "valkyriachronicles", "specopstheline": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "killingfloor2": "bulalaumgangatho2", "cavestory": "intsomiyomqolomba", "doometernal": "ukutshatyalaliswakokuphakade", "centuryageofashes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry4": "farcry4", "gearsofwar": "gearsofwar", "mwo": "mwo", "division2": "umbhalo2", "tythetasmaniantiger": "tythetasmaniantiger", "generationzero": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterthegungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jakanddaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modernwarfare2": "modernwarfare2", "blackops1": "blackops1", "sausageman": "indodayesoseji", "ratchetandclank": "ratchetandclank", "chexquest": "chexquest", "thephantompain": "intlunguengekhoyo", "warface": "ubusodazimpi", "crossfire": "<PERSON><PERSON><PERSON>i", "atomicheart": "intl<PERSON><PERSON><PERSON><PERSON>u", "blackops3": "blackops3", "vampiresurvivors": "abaphilamivampire", "callofdutybatleroyale": "ubizolomdlalowemfazwe", "moorhuhn": "<PERSON><PERSON><PERSON><PERSON>", "freedoom": "<PERSON><PERSON><PERSON><PERSON>", "battlegrounds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frag": "bulala", "tinytina": "<PERSON><PERSON>", "gamepubg": "umdlalowepubg", "necromunda": "necromunda", "metalgearsonsoflibert": "metalgearsonsoflibert", "juegosfps": "imidlalofps", "convertstrike": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warzone2": "imfazweyomthandazo2", "shatterline": "<PERSON>g<PERSON><PERSON><PERSON>", "blackopszombies": "iizombieszeblackops", "bloodymess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republiccommando": "republiccommando", "elitedangerous": "elitedangerous", "soldat": "<PERSON><PERSON><PERSON>", "groundbranch": "ugatyabomhlaba", "squad": "<PERSON><PERSON><PERSON><PERSON>", "destiny1": "isiphelo1", "gamingfps": "umdlalofps", "redfall": "redfall", "pubggirl": "intombipubg", "worldoftanksblitz": "umhlabawe<PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutyblackops": "ubizolobunzimamnyama", "enlisted": "ngena", "farlight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "farcry5": "farcry5", "farcry6": "farcry6", "farlight84": "farlight84", "splatoon3": "splatoon3", "armoredcore": "armoredcore", "pavlovvr": "pavlovvr", "xdefiant": "xdefiant", "tinytinaswonderlands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halo2": "halo2", "payday2": "usukuokuhlawulwa2", "cs16": "cs16", "pubgindonesia": "pubgindonesia", "pubgukraine": "<PERSON><PERSON><PERSON><PERSON>", "pubgeu": "pubgeu", "pubgczsk": "pubgczsk", "wotblitz": "wotblitz", "pubgromania": "ipubgromania", "empyrion": "empyrion", "pubgczech": "<PERSON><PERSON><PERSON><PERSON>", "titanfall2": "titanfall2", "soapcod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostcod": "icodeyesipoki", "csplay": "csplay", "unrealtournament": "umdlaloongekhoyo", "callofdutydmz": "callofdutydmz", "gamingcodm": "umdlalocodm", "borderlands2": "borderlands2", "counterstrike": "counterstrike", "cs2": "cs2", "pistolwhip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymw2": "callofdutymw2", "quakechampions": "iintshatsheli_zenyikima", "halo3": "halo3", "halo": "halo", "killingfloor": "umbalaweziganeko", "destiny2": "destiny2", "exoprimal": "exoprimal", "splintercell": "splintercell", "neonwhite": "amhlophe<PERSON><PERSON><PERSON><PERSON>", "remnant": "insalela", "azurelane": "azurelane", "worldofwar": "umhlabewemfazwe", "gunvolt": "gunvolt", "returnal": "<PERSON><PERSON><PERSON><PERSON>", "halo4": "halo4", "haloreach": "haloreach", "shadowman": "<PERSON>do<PERSON><PERSON><PERSON><PERSON><PERSON>", "quake2": "inyikima2", "microvolts": "iimicrovolt", "reddead": "u<PERSON>nyubom<PERSON>", "standoff2": "standoff2", "harekat": "harekat", "battlefield3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lostark": "lostark", "guildwars2": "guildwars2", "fallout76": "fallout76", "elsword": "els<PERSON>", "seaofthieves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rust": "<PERSON>hlwa", "conqueronline": "umdingimanqob<PERSON>", "dauntless": "a<PERSON>yi<PERSON>", "warships": "iinqanawa", "dayofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warthunder": "warthunder", "flightrising": "ukunyukaindiza", "recroom": "igumbilokuzonwabisa", "legendsofruneterra": "iintsomizelizweruneterra", "pso2": "pso2", "myster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phantasystaronline2": "phantasystaronline2", "maidenless": "nginamtshato", "ninokuni": "nino<PERSON><PERSON>", "worldoftanks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossout": "gcima", "agario": "agario", "secondlife": "bokuphindabephilayo", "aion": "aion", "toweroffantasy": "itoweroffantasy", "netplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "everquest": "everquest", "metin2": "metin2", "gtaonline": "gtaonline", "ninokunicrossworld": "uyandicross", "reddeadonline": "reddeadonline", "superanimalroyale": "superanimalroyale", "ragnarokonline": "ragnar<PERSON>nline", "knightonline": "knight<PERSON><PERSON>", "gw2": "gw2", "tboi": "tboi", "thebindingofisaac": "ukubotshwakukaisaac", "dragonageinquisition": "dragonageinquisition", "codevein": "codevein", "eveonline": "<PERSON><PERSON><PERSON>", "clubpenguin": "iklabipenguin", "lotro": "lotro", "wakfu": "wakfu", "scum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blackdesertonline": "blackdesertonline", "multiplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pirate101": "umkhombe101", "honorofkings": "imbonakaloefukings", "fivem": "fivem", "starwarsbattlefront": "imfazwezestarwars", "karmaland": "ikharmaland", "ssbu": "ssbu", "starwarsbattlefront2": "starwarsbattlefront2", "phigros": "phi<PERSON>s", "mmo": "mmo", "pokemmo": "pokemmo", "ponytown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dchat": "3dingxoxo", "nostale": "asozesinqabe", "tauriwow": "tauriwaw", "wowclassic": "wowclassic", "worldofwarcraft": "worldofwarcraft", "warcraft": "warcraft", "wotlk": "wotlk", "runescape": "runescape", "neopets": "neopets", "moba": "moba", "habbo": "habbo", "archeage": "archeage", "toramonline": "toramonline", "mabinogi": "mabinogi", "ashesofcreation": "umlothowokudala", "riotmmo": "riotmmo", "silkroad": "umendlelelosilika", "spiralknights": "spiralknights", "mulegend": "umlegend", "startrekonline": "startrekk<PERSON><PERSON><PERSON><PERSON>", "vindictus": "vindictus", "albiononline": "albiononline", "bladeandsoul": "bladeandsoul", "evony": "evony", "dragonsprophet": "intsimbikadragon", "grymmo": "grymmo", "warmane": "umntu_ofudumeleyo", "multijugador": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelsonline": "iingelosi_online", "lunia": "lunia", "luniaz": "luniaz", "idleon": "<PERSON>on", "dcuniverseonline": "dcuniverse<PERSON><PERSON><PERSON><PERSON>", "growtopia": "growtopia", "starwarsoldrepublic": "starwarsoldrepublic", "grandfantasia": "grandfant<PERSON>a", "blueprotocol": "blueprotocol", "perfectworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "riseonline": "<PERSON><PERSON><PERSON><PERSON>", "corepunk": "corepunk", "adventurequestworlds": "ilizwelokufunaizintomondlebe", "flyforfun": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animaljam": "animaljam", "kingdomofloathing": "ubuku<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cityofheroes": "<PERSON><PERSON><PERSON>osawamaq<PERSON><PERSON>", "mortalkombat": "mortalkombat", "streetfighter": "umlwifuqodsitrato", "hollowknight": "hollowknight", "metalgearsolid": "metalgearsolid", "forhonor": "<PERSON><PERSON><PERSON><PERSON>", "tekken": "tekken", "guiltygear": "guiltygear", "xenoverse2": "xenoverse2", "fgc": "fgc", "streetfighter6": "streetfighter6", "multiversus": "multiversus", "smashbrosultimate": "smashbrosultimate", "soulcalibur": "soulcalibur", "brawlhalla": "brawlhalla", "virtuafighter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "streetsofrage": "izitratwozasiphithiphithi", "mkdeadlyalliance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nomoreheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mhr": "mhr", "mortalkombat12": "mortalkombat12", "thekingoffighters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "likeadragon": "njengenyoka", "retrofightinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blasphemous": "ubuhedeni", "rivalsofaether": "abaphikisanibomoyawomhlaba", "persona4arena": "persona4arena", "marvelvscapcom": "marvelvscapcom", "supersmash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugen": "mugen", "warofthemonsters": "imfazweyeziqhwaga", "jogosdeluta": "imidlaloyomzabalazo", "cyberbots": "ii<PERSON><PERSON>", "armoredwarriors": "am<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalfight": "umlogokugqibela", "poweredgear": "amandlabuxhobile", "beatemup": "bawa<PERSON><PERSON>", "blazblue": "blazblue", "mortalkombat9": "mortalkombat9", "fightgames": "imidlaloyokulwa", "killerinstinct": "umoyow<PERSON><PERSON>la", "kingoffigthers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ghostrunner": "umbal<PERSON><PERSON>_we<PERSON><PERSON>ho", "chivalry2": "imbeko2", "demonssouls": "idimoni", "blazbluecrosstag": "blazbluecrosstag", "blazbluextagbattle": "blazbluextagbattle", "blazbluextag": "blazbluextag", "guiltygearstrive": "guiltygearstrive", "hollowknightsequel": "umlandelandelahollowknight", "hollowknightsilksong": "hollowknightsilksong", "silksonghornet": "isilksonghornet", "silksonggame": "umdlalowesilksong", "silksongnews": "iindabazesilksong", "silksong": "silksong", "undernight": "ngaphantsikobus<PERSON><PERSON><PERSON>", "typelumina": "ta<PERSON>pal<PERSON>na", "evolutiontournament": "umjikle<PERSON>_wokuvela", "evomoment": "umzuzwaseboo", "lollipopchainsaw": "lollipop<PERSON><PERSON><PERSON>", "dragonballfighterz": "dragonballfighterz", "talesofberseria": "amabalieberseriya", "bloodborne": "igazi_eliphilayo", "horizon": "umkhathizwe", "pathofexile": "umendow<PERSON>nq<PERSON><PERSON>", "slimerancher": "umfuyislime", "crashbandicoot": "crashbandicoot", "bloodbourne": "igazi_elithweleyo", "uncharted": "akungaziwa", "horizonzerodawn": "horizonzerodawn", "ps4": "ps4", "ps5": "ps5", "spyro": "spyro", "playstationplus": "playstationplus", "lastofus": "yokugqibelakwethu", "infamous": "<PERSON><PERSON><PERSON>", "playstationbuddies": "abahlobebesiplaystation", "ps1": "ps1", "oddworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playstation5": "iplaystation5", "slycooper": "sly<PERSON><PERSON>", "psp": "psp", "rabbids": "oorabbids", "splitgate": "splitgate", "persona4": "persona4", "hellletloose": "<PERSON><PERSON><PERSON><PERSON>", "gta4": "gta4", "gta": "gta", "roguecompany": "inkampanieng<PERSON><PERSON><PERSON><PERSON>", "aisomniumfiles": "aisomniumfiles", "gta5": "gta5", "gtasanandreas": "gtasanand<PERSON>s", "godofwar": "unkulunkuluwempi", "gris": "gris", "trove": "umthombo", "detroitbecomehuman": "detroityibangumntu", "beatsaber": "beats<PERSON>r", "rimworld": "rimworld", "stellaris": "stellaris", "ps3": "ps3", "untildawn": "kutshonelanga", "touristtrophy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lspdfr": "lspdfr", "shadowofthecolossus": "umthunziweqhaweenkulu", "crashteamracing": "crashteamracing", "fivepd": "intlanu", "tekken7": "tekken7", "devilmaycry": "udeviliangakhali", "devilmaycry3": "devilmaycry3", "devilmaycry5": "devilmaycry5", "ufc4": "ufc4", "playingstation": "ndidlalastation", "samuraiwarriors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "psvr2": "psvr2", "thelastguardian": "umkh<PERSON>lihokugqibela", "soulblade": "umphefumlowekrele", "gta5rp": "gta5rp", "gtav": "gtav", "playstation3": "playstation3", "manhunt": "ukuzingela_amadoda", "gtavicecity": "gtavicecity", "wwe2k23": "wwe2k23", "shadowhearts2covenant": "shadowhearts2covenant", "pcsx2": "pcsx2", "lastguardian": "umkh<PERSON><PERSON><PERSON>rokugqibel<PERSON>", "xboxone": "xboxone", "forza": "forza", "cd": "cd", "gamepass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "armello": "armello", "partyanimal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warharmmer40k": "warhammer40k", "fightnightchampion": "umtshayiwobusukubobugqatsiboo", "psychonauts": "iipsychonauts", "mhw": "mhw", "princeofpersia": "inkosanayepersia", "theelderscrollsskyrim": "theelderscrollsskyrim", "pantarhei": "pan<PERSON><PERSON><PERSON>", "theelderscrolls": "iiskrowulamadala", "gxbox": "gxbox", "battlefront": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dontstarvetogether": "singalambikunye", "ori": "ori", "spelunky": "ukungen<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xbox1": "xbox1", "xbox360": "xbox360", "starbound": "imvakaloeenkwenkwezi", "xboxonex": "xboxonex", "forzahorizon5": "forzahorizon5", "skate3": "skate3", "houseflipper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "americanmcgeesalice": "americanmcgeesalice", "xboxs": "xboxs", "xboxseriesx": "xboxseriesx", "xboxseries": "xboxseries", "r6xbox": "r6xbox", "leagueofkingdoms": "umbuthowamagosa", "fable2": "intsomi2", "xboxgamepass": "xboxgamepass", "undertale": "undertale", "trashtv": "umabonakudewehluleka", "skycotl": "isibhakabhakacotl", "erica": "erica", "ancestory": "<PERSON><PERSON><PERSON><PERSON>", "cuphead": "cuphead", "littlemisfortune": "intomba<PERSON><PERSON><PERSON><PERSON><PERSON>", "sallyface": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "franbow": "franbow", "monsterprom": "ipromdy<PERSON><PERSON><PERSON><PERSON>", "projectzomboid": "projectzomboid", "ddlc": "ddlc", "motos": "iimotos", "outerwilds": "outerwilds", "pbbg": "pbbg", "anshi": "anshi", "cultofthelamb": "inqolokamatakane", "duckgame": "umdlalowedada", "thestanleyparable": "ithestanleyparable", "towerunite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "occulto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "longdrive": "uhambolwelude", "satisfactory": "wa<PERSON><PERSON><PERSON>", "pluviophile": "um<PERSON><PERSON>_wemvula", "underearth": "ngaphantsikomhlaba", "assettocorsa": "assettocorsa", "geometrydash": "geometrydash", "kerbal": "kerbal", "kerbalspaceprogram": "kerbalspaceprogram", "kenshi": "kenshi", "spiritfarer": "umkhaphimp<PERSON>umlo", "darkdome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pizzatower": "ipizzatower", "indiegame": "umdlaloozimeleyo", "itchio": "itchio", "golfit": "yigalfa", "truthordare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "umdlalo", "rockpaperscissors": "ilitye_iphepha_isikere", "trampoline": "itrampoline", "hulahoop": "<PERSON><PERSON><PERSON><PERSON>", "dare": "naba", "scavengerhunt": "ukuzingelaumbuzo", "yardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickanumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trueorfalse": "yinyaniokangabubuxoki", "beerpong": "i<PERSON><PERSON><PERSON><PERSON><PERSON>", "dicegoblin": "amaxelegu_amancunga", "cosygames": "imidlaloepholileyo", "datinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drinkinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sodoku": "isodoku", "juegos": "<PERSON><PERSON><PERSON><PERSON>", "mahjong": "mahjong", "jeux": "<PERSON><PERSON><PERSON><PERSON>", "simulationgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxdemots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosdepalabras": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "letsplayagame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boredgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyun": "umdlalo", "interactivegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amtgard": "amtgard", "staringcontests": "ukujamana", "spiele": "<PERSON><PERSON><PERSON><PERSON>", "giochi": "<PERSON><PERSON><PERSON><PERSON>", "geoguessr": "geoguessr", "iphonegames": "imidlaloyeiphone", "boogames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cranegame": "umdlalowecrane", "hideandseek": "mtshetshelwano", "hopscotch": "<PERSON><PERSON><PERSON><PERSON>qoqo", "arcadegames": "imidlaloyearcade", "yakuzagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classicgame": "umdlalowakudala", "mindgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ond<PERSON>", "guessthelyric": "qagelelumculo", "galagames": "imidlaloyegalagames", "romancegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yanderegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tonguetwisters": "amagamaqhubulo", "4xgames": "imidlalo4x", "gamefi": "gamefi", "jeuxdarcades": "imidlaloyearcade", "tabletopgames": "imidlaloyetafile", "metroidvania": "metroidvania", "games90": "imidlalo90", "idareyou": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "mozaa": "mozaa", "fumitouedagames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racinggames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ets2": "ets2", "realvsfake": "okonilevsokungeyonyaniso", "playgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameonline": "<PERSON>dl<PERSON>wei<PERSON><PERSON><PERSON>", "onlinegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "writtenroleplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playaballgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pictionary": "ipictionary", "coopgames": "imidlaloedibeneyo", "jenga": "jenga", "wiigames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "highscore": "<PERSON>rek<PERSON><PERSON>", "jeuxderôles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "burgergames": "im<PERSON><PERSON><PERSON>ebhe<PERSON>", "kidsgames": "imidlaloyabantwana", "skeeball": "iskeeball", "nfsmwblackedition": "nfsmwblackedition", "jeuconcour": "jeuconcour", "tcgplayer": "tcgplayer", "juegodepreguntas": "umdlalowemibuzo", "gioco": "umdlalo", "managementgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hiddenobjectgame": "umdlalowokufumanezintofihliweyo", "roolipelit": "r<PERSON><PERSON><PERSON><PERSON>", "formula1game": "umdlaloweformula1", "citybuilder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drdriving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosarcade": "imidlaloarcade", "memorygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vulkan": "vulkan", "actiongames": "imidlaloamatshantliziyo", "blowgames": "im<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pinballmachines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "couchcoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perguntados": "buzwa", "gameo": "umdlalo", "lasergame": "umdlalowelaser", "imessagegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "idlegames": "imidlaloeyekudlalahlukanye", "fillintheblank": "gcwalisaindawoengenalutontu", "jeuxpc": "imidlaloyepc", "rétrogaming": "umdlalowamanduloretro", "logicgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ond<PERSON>", "japangame": "umdlalowasejapan", "rizzupgame": "ukuphuculaumtsalane", "subwaysurf": "ukusefa_emgaqweni", "jeuxdecelebrite": "<PERSON><PERSON><PERSON><PERSON>baziweyo", "exitgames": "imid<PERSON><PERSON><PERSON><PERSON>", "5vs5": "5vs5", "rolgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dashiegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameandkill": "d<PERSON><PERSON><PERSON><PERSON>", "traditionalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kniffel": "<PERSON><PERSON><PERSON>", "gamefps": "umdlalowefps", "textbasedgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryparagrafowe": "gryparagrafowe", "fantacalcio": "ifantacalcio", "retrospel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgame": "umdlalowesela", "lawngames": "imidlaloyeng<PERSON>", "fliperama": "ifliperama", "heroclix": "heroclix", "tablesoccer": "itafilefootball", "tischfußball": "tischfußball", "spieleabende": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxforum": "jeuxforum", "casualgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fléchettes": "iidati", "escapegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thiefgameseries": "umdlalowamas<PERSON>", "cranegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "játék": "<PERSON><PERSON><PERSON><PERSON>", "bordfodbold": "i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosorte": "umdlalowethamsanqa", "mage": "<PERSON><PERSON><PERSON><PERSON>", "cargames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlineplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mölkky": "<PERSON><PERSON><PERSON><PERSON>", "gamenights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pursebingos": "iibhing<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "randomizer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "msx": "msx", "anagrammi": "anagrammi", "gamespc": "imidlalopc", "socialdeductiongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominos": "idominos", "domino": "idomino", "isometricgames": "imidlaloyisometric", "goodoldgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truthanddare": "inyaninomsindo", "mahjongriichi": "ma<PERSON><PERSON><PERSON><PERSON>", "scavengerhunts": "ukuzingela", "jeuxvirtuel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romhack": "romhack", "f2pgamer": "umdlalif2p", "free2play": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fantasygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gryonline": "gryonline", "driftgame": "umdlalowokudrift", "gamesotomes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "halotvseriesandgames": "ihalotvseriesandgames", "mushroomoasis": "ioasiskamahlamvu", "anythingwithanengine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "everywheregame": "umdlalowonkeyondawo", "swordandsorcery": "ikrelenemilingo", "goodgamegiving": "umdlaloomnandiwokupha", "jugamos": "sidlala", "lab8games": "lab8games", "labzerogames": "imidlalokalabzero", "grykomputerowe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virgogami": "virgogami", "gogame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jeuxderythmes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minaturegames": "imidlaloemincinane", "ridgeracertype4": "ridgeracertype4", "selflovegaming": "ukuz<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamemodding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crimegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dobbelspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spelletjes": "<PERSON><PERSON><PERSON><PERSON>", "spacenerf": "spacenerf", "charades": "imid<PERSON><PERSON>_yoku<PERSON><PERSON><PERSON>", "singleplayer": "umdlalowomntu", "coopgame": "umdlalowebambiswano", "gamed": "idlalile", "forzahorizon": "forzahorizon", "nexus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "geforcenow": "geforcengoku", "maingame": "umdlalowenkulu", "kingdiscord": "ukumkanikazi_discord", "scrabble": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "schach": "schach", "shogi": "shogi", "dandd": "dandd", "catan": "catan", "ludo": "iludo", "backgammon": "i<PERSON><PERSON><PERSON><PERSON><PERSON>", "onitama": "onitama", "pandemiclegacy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "camelup": "inkamandla", "monopolygame": "umdlalowemonopoly", "brettspiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bordspellen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "boardgame": "umdlalowebhodi", "sällskapspel": "imidlaloyentlalontle", "planszowe": "iibhodигейму", "risiko": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "permainanpapan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zombicide": "zombicide", "tabletop": "itafile", "baduk": "baduk", "bloodbowl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cluedo": "icluedo", "xiangqi": "<PERSON>ian<PERSON><PERSON>", "senet": "senet", "goboardgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON>", "connectfour": "dibanazone", "heroquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "giochidatavolo": "imidlaloyetafile", "farkle": "farkle", "carrom": "icarrom", "tablegames": "imidlaloyetafile", "dicegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yatzy": "yatzy", "parchis": "ipar<PERSON>s", "jogodetabuleiro": "umdlalowebhodi", "jocuridesocietate": "imid<PERSON><PERSON><PERSON><PERSON><PERSON>", "deskgames": "imidlaloyetafile", "alpharius": "alpharius", "masaoyunları": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelcrisisprotocol": "marvelcrisisprotocol", "cosmicencounter": "ukudibanakwendalo", "creationludique": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabletoproleplay": "umdlalowetafile", "cardboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eldritchhorror": "ul<PERSON>ikoolungaqondakaliyo", "switchboardgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infinitythegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingdomdeath": "ukufakwenkosi", "yahtzee": "yahtzee", "chutesandladders": "ukwehlaokunyuka", "társas": "intlanga", "juegodemesa": "umdlalowetafile", "planszówki": "iibhodgeyimz", "rednecklife": "ubomintsango", "boardom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applestoapples": "iapilekwiapile", "jeudesociété": "imid<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameboard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dominó": "do<PERSON><PERSON>", "kalah": "kalah", "crokinole": "crokinole", "jeuxdesociétés": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twilightimperium": "ukongamalus<PERSON><PERSON><PERSON>", "horseopoly": "ihashelopoly", "deckbuilding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mansionsofmadness": "imizika<PERSON><PERSON><PERSON><PERSON>", "gomoku": "igomoku", "giochidatavola": "imidlaloyetafile", "shadowsofbrimstone": "ithunzielasebrimstone", "kingoftokyo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warcaby": "iidrafti", "táblajátékok": "ii<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleship": "iinqanawa", "tickettoride": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deskovehry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "catán": "catán", "subbuteo": "subbuteo", "jeuxdeplateau": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stolníhry": "imidlaloyetafile", "xiángqi": "x<PERSON><PERSON><PERSON><PERSON>", "jeuxsociete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gesellschaftsspiele": "imid<PERSON><PERSON><PERSON><PERSON><PERSON>", "starwarslegion": "starwarslegion", "gochess": "yachess", "weiqi": "weiqi", "jeuxdesocietes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "terraria": "terraria", "dsmp": "dsmp", "warzone": "imfazwe", "arksurvivalevolved": "arksurvivalevolved", "dayz": "iintsuku", "identityv": "identityv", "theisle": "<PERSON><PERSON><PERSON><PERSON>", "thelastofus": "lowamagqi<PERSON><PERSON>", "nomanssky": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subnautica": "subnautica", "tombraider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofcthulhu": "ubi<PERSON><PERSON><PERSON><PERSON><PERSON>", "bendyandtheinkmachine": "bendyandtheinkmachine", "conanexiles": "conanexiles", "eft": "eft", "amongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eco": "eko", "monkeyisland": "<PERSON><PERSON>lengwemon<PERSON>", "valheim": "valheim", "planetcrafter": "umakhiwezwe", "daysgone": "iintsukuzidlulile", "fobia": "fobia", "witchit": "wit<PERSON>t", "pathologic": "isifo", "zomboid": "izomboid", "northgard": "northgard", "7dtd": "7dtd", "thelongdark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ark": "intlanga", "grounded": "ibanjwa", "stateofdecay2": "umonakaloophela2", "vrising": "vrising", "madfather": "utataophambeneyo", "dontstarve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eternalreturn": "ukub<PERSON><PERSON>phaka<PERSON>", "pathoftitans": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frictionalgames": "imidlaloyokuxubana", "hexen": "ubu<PERSON><PERSON>", "theevilwithin": "ubugwen<PERSON><PERSON><PERSON><PERSON><PERSON>", "realrac": "inyanirac", "thebackrooms": "amagumbaese<PERSON>gel<PERSON><PERSON><PERSON>", "backrooms": "iigumbiezemva", "empiressmp": "empiressmp", "blockstory": "<PERSON><PERSON><PERSON><PERSON>", "thequarry": "umgodi", "tlou": "tlou", "dyinglight": "kukufakukhanya", "thewalkingdeadgame": "umdlalowabafiewalkingdead", "wehappyfew": "simbalwa", "riseofempires": "ukuvukakobukh<PERSON>i", "stateofsurvivalgame": "umdlalowokusindaisimo", "vintagestory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arksurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "barotrauma": "barotrauma", "breathedge": "phe<PERSON><PERSON><PERSON>mphe<PERSON><PERSON><PERSON>", "alisa": "alisa", "westlendsurvival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beastsofbermuda": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frostpunk": "iqhazepunk", "darkwood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalhorror": "ukus<PERSON>um<PERSON><PERSON><PERSON>", "residentevil": "residentevil", "residentevil2": "residentevil2", "residentevil4": "residentevil4", "residentevil3": "residentevil3", "voidtrain": "ul<PERSON><PERSON>q<PERSON><PERSON>", "lifeaftergame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "survivalgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sillenthill": "<PERSON><PERSON><PERSON><PERSON>", "thiswarofmine": "lembileomyam", "scpfoundation": "scpfoundation", "greenproject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kuon": "kuon", "cryoffear": "khalaukukoyika", "raft": "umkhombe", "rdo": "rdo", "greenhell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "residentevil5": "residentevil5", "deadpoly": "polyfile", "residentevil8": "residentevil8", "onironauta": "umaphupho_aphilayo", "granny": "ugogo", "littlenightmares2": "amaphuphonyasebusukunkunane2", "signalis": "umqondiso", "amandatheadventurer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sonsoftheforest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rustvideogame": "umdlalowevidiyoirust", "outlasttrials": "ukulingaokuhlalakakade", "alienisolation": "ukwahlulwamhlaba", "undawn": "ekuseni", "7day2die": "iintsuku7ukufa", "sunlesssea": "ulwandleolungenalanga", "sopravvivenza": "ukup<PERSON>a", "propnight": "ubusukubeprop", "deadisland2": "umhlabawabafiwayo2", "ikemensengoku": "ikemensengoku", "ikemenvampire": "ikemenvampire", "deathverse": "umhlabawokufa", "cataclysmdarkdays": "intshabalalisenkulu", "soma": "funda", "fearandhunger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stalkercieńczarnobyla": "umlandeliwasomthunziwasetshernobyl", "lifeafter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ageofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clocktower3": "clocktower3", "aloneinthedark": "ndindod<PERSON>eb<PERSON>", "medievaldynasty": "imilondayamaxes<PERSON>", "projectnimbusgame": "umdlaloweprojectnimbus", "eternights": "ubusukuobungunaphakade", "craftopia": "craftopia", "theoutlasttrials": "uvavanyo_lweoutlast", "bunker": "<PERSON><PERSON><PERSON><PERSON>", "worlddomination": "ukuthathaumhlabawonke", "rocketleague": "rocketleague", "tft": "tft", "officioassassinorum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "necron": "necron", "wfrp": "wfrp", "dwarfslayer": "umbulali_wamadwarf", "warhammer40kcrush": "warhammer<PERSON><PERSON><PERSON><PERSON>", "wh40": "wh40", "warhammer40klove": "uthandomlwewarhammer40k", "warhammer40klore": "warhammer40klore", "warhammer": "warhammer", "warhammer30k": "warhammer30k", "warhammer40k": "warhammer40k", "warhammer40kdarktide": "warhammer40kdarktide", "totalwarhammer3": "totalwarhammer3", "temploculexus": "itempeloloculexus", "vindicare": "vindicare", "ilovesororitas": "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilovevindicare": "n<PERSON><PERSON><PERSON><PERSON><PERSON>_ivindicare", "iloveassasinorum": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "templovenenum": "templovenenum", "templocallidus": "templocall<PERSON>us", "templomaerorus": "itemplomaerorus", "templovanus": "<PERSON><PERSON><PERSON><PERSON>", "oficioasesinorum": "umsebenziwab<PERSON><PERSON><PERSON>", "tarkov": "<PERSON><PERSON><PERSON>", "40k": "40k", "tetris": "itetris", "lioden": "ii<PERSON><PERSON><PERSON>", "ageofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe2": "aoe2", "hoi4": "hoi4", "warhammerageofsigmar": "warhammerageofsigmar", "civilizationv": "civilizationv", "ittakestwo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wingspan": "ubu<PERSON><PERSON>_b<PERSON><PERSON>ko", "terraformingmars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heroesofmightandmagic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "btd6": "btd6", "supremecommander": "umlawuliomkhulu", "ageofmythology": "ixeshamythology", "args": "args", "rime": "rime", "planetzoo": "planetizoo", "outpost2": "iposi2", "banished": "gxothiwe", "caesar3": "caesar3", "redalert": "umngciphekoobomvu", "civilization6": "civilization6", "warcraft2": "warcraft2", "commandandconquer": "<PERSON><PERSON><PERSON><PERSON>", "warcraft3": "warcraft3", "eternalwar": "imfazwe<PERSON><PERSON><PERSON><PERSON>", "strategygames": "imidlaloyobuchule", "anno2070": "anno2070", "civilizationgame": "umdlalowentlalo", "civilization4": "umphakathi4", "factorio": "factorio", "dungeondraft": "imizob<PERSON><PERSON><PERSON><PERSON><PERSON>", "spore": "intsholongwane", "totalwar": "imfazonke", "travian": "travian", "forts": "iinqaba", "goodcompany": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "civ": "civ", "homeworld": "<PERSON><PERSON><PERSON><PERSON>", "heidentum": "ubuhedeni", "aoe4": "aoe4", "hnefatafl": "hnefatafl", "fasterthanlight": "ngokukhawulezakunokukhanya", "forthekings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "realtimestrategy": "iqhingaumthingowoqobo", "starctaft": "starctaft", "sidmeierscivilization": "sidmeierscivilization", "kingdomtwocrowns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eu4": "eu4", "vainglory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ww40k": "ww40k", "godhood": "ubuthixo", "anno": "mnyaka", "battletech": "ibattletech", "malifaux": "malifa<PERSON>", "w40k": "w40k", "hattrick": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "davesfunalgebraclass": "iklasikadaveyealgebraemnandi", "plagueinc": "intsholongoyisifo", "theorycraft": "ithiyoricraft", "mesbg": "mesbg", "civilization3": "impucuko3", "4inarow": "4ngqo", "crusaderkings3": "crusaderkings3", "heroes3": "amaqhawe3", "advancewars": "iimfazophambili", "ageofempires2": "iminyakayobukhosibesi2", "disciples2": "abafundi2", "plantsvszombies": "izimivelonxamnee<PERSON>i", "giochidistrategia": "imidlaloyobuchule", "stratejioyunları": "imidlaloyobuchule", "europauniversalis4": "europauniversalis4", "warhammervermintide2": "warhammervermintide2", "ageofwonders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dinosaurking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "worldconquest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heartsofiron4": "iintliziywentsimbi4", "companyofheroes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "battleforwesnoth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aoe3": "aoe3", "forgeofempires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warhammerkillteam": "warhammerkillteam", "goosegooseduck": "ngqazangqazadada", "phobies": "iifob<PERSON>ya", "phobiesgame": "umdlalowamaphobia", "gamingclashroyale": "umdlaloclashroyale", "adeptusmechanicus": "adeptusmechanicus", "outerplane": "ngaphandlekomhlaba", "turnbased": "ngokwethuba", "bomberman": "bomberman", "ageofempires4": "ageofempires4", "civilization5": "civilization5", "victoria2": "victoria2", "crusaderkings": "crusaderkings", "cultris2": "cultris2", "spellcraft": "ubugqirha", "starwarsempireatwar": "imfazwezestarwars", "pikmin4": "pikmin4", "anno1800": "anno1800", "estratégia": "i<PERSON><PERSON><PERSON>", "popfulmail": "ipopfulme<PERSON>li", "shiningforce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterduel": "umdlaloomkhulu", "dysonsphereprogram": "iprogramyesphereya<PERSON>son", "transporttycoon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unrailed": "ndiphume_emgqeni", "magicarena": "<PERSON><PERSON>na", "wolvesville": "wolvesville", "ooblets": "ooblets", "planescapetorment": "planescapetorment", "uplandkingdoms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "galaxylife": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolvesvilleonline": "wolvesvilleonline", "slaythespire": "bulalainqaba", "battlecats": "iikatizemfazwe", "sims3": "sims3", "sims4": "sims4", "thesims4": "thesims4", "thesims": "thesims", "simcity": "simcity", "simcity2000": "simcity2000", "sims2": "sims2", "iracing": "<PERSON><PERSON><PERSON><PERSON>", "granturismo": "<PERSON><PERSON><PERSON>", "needforspeed": "ukufunekakwesantya", "needforspeedcarbon": "k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "realracing3": "ukubaleka3", "trackmania": "trackmania", "grandtourismo": "igrandtourismo", "gt7": "gt7", "simsfreeplay": "simsfreeplayayidlalwa", "ts4": "ts4", "thesims2": "thesims2", "thesims3": "thesims3", "thesims1": "thesims1", "lossims4": "iisimsezinezelahlekelweyo", "fnaf": "fnaf", "outlast": "ukugqithi<PERSON>", "deadbydaylight": "u<PERSON><PERSON>ini", "alicemadnessreturns": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "darkhorseanthology": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phasmophobia": "uk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fivenightsatfreddys": "ubusukubuhlanukunofreddy", "saiko": "saiko", "fatalframe": "intlethuyokugqibela", "littlenightmares": "amaphuphooman<PERSON><PERSON><PERSON><PERSON><PERSON>", "deadrising": "uk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ladydimitrescu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homebound": "e<PERSON>ya", "deadisland": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "litlemissfortune": "litllemissfortune", "projectzero": "iprojekthikanye", "horory": "hororyi", "jogosterror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "helloneighbor": "molo<PERSON><PERSON><PERSON><PERSON>", "helloneighbor2": "molomdmelwane2", "gamingdbd": "gamingdbd", "thecatlady": "umamawekati", "jeuxhorreur": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "horrorgaming": "umdlalooyoyiki<PERSON>o", "magicthegathering": "magicthegathering", "mtg": "mtg", "tcg": "tcg", "cardsagainsthumanity": "am<PERSON><PERSON><PERSON>_okuch<PERSON><PERSON>_nobuntu", "cribbage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minnesotamtg": "minnesotamtg", "edh": "edh", "monte": "monte", "pinochle": "ipinochle", "codenames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dixit": "dixit", "bicyclecards": "am<PERSON><PERSON><PERSON>_e<PERSON><PERSON><PERSON><PERSON><PERSON>", "lor": "lor", "euchre": "euchre", "thegwent": "ig<PERSON>t", "legendofrunetera": "intsomikaruneterra", "solitaire": "isolitaire", "poker": "<PERSON><PERSON><PERSON>", "hearthstone": "hearthstone", "uno": "uno", "schafkopf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyforge": "ikhiifonje", "cardtricks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playingcards": "<PERSON><PERSON><PERSON><PERSON>", "marvelsnap": "marvelsnap", "ginrummy": "igin<PERSON><PERSON>", "netrunner": "<PERSON>bal<PERSON><PERSON><PERSON>", "gwent": "gwent", "metazoo": "metazoo", "tradingcards": "am<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncards": "am<PERSON><PERSON><PERSON>_epokemon", "fleshandbloodtcg": "tcgyegazi", "sportscards": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardfightvanguard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duellinks": "duellinks", "spades": "is<PERSON>edi", "warcry": "<PERSON><PERSON><PERSON><PERSON>", "digimontcg": "digimontcg", "toukenranbu": "toukenranbu", "kingofhearts": "inkosiyezintliziyo", "truco": "truco", "loteria": "ilotho", "hanafuda": "hana<PERSON>da", "theresistance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transformerstcg": "transformerstcg", "doppelkopf": "doppelkopf", "yugiohcards": "am<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>h", "yugiohtcg": "yugiohtcg", "yugiohduel": "yugiohduel", "yugiohocg": "yugiohocg", "dueldisk": "iduelidisk", "yugiohgame": "umdaloweyugioh", "darkmagician": "umlingo_omnyama", "blueeyeswhitedragon": "amehloaluhlweudragonomhlophe", "yugiohgoat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "briscas": "ukudlala_ibhriska", "juegocartas": "umdl<PERSON><PERSON>makhadi", "burraco": "bur<PERSON>o", "rummy": "<PERSON><PERSON><PERSON>", "grawkarty": "g<PERSON><PERSON><PERSON>", "dobble": "dob<PERSON>e", "mtgcommander": "mtgcommander", "cotorro": "hleba", "jeuxdecartes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mtgjudge": "mtgjudge", "juegosdecartas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duelyst": "<PERSON><PERSON>t", "mtgplanschase": "mtgplanschase", "mtgpreconcommander": "mtgpreconcommander", "kartenspiel": "umdl<PERSON><PERSON>makhadi", "carteado": "carteado", "sueca": "sueca", "beloteonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "karcianki": "ka<PERSON><PERSON><PERSON>", "battlespirits": "imoyolwazimlo", "battlespiritssaga": "umdlalowamanqanabamoya", "jogodecartas": "umdl<PERSON><PERSON>makhadi", "žolíky": "žolíky", "facecard": "ubuso", "cardfight": "ukwabulimaxhaka", "biriba": "biriba", "deckbuilders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelchampions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "magiccartas": "iicardzentlantla", "yugiohmasterduel": "yugiohmasterduel", "shadowverse": "umqondop<PERSON><PERSON>", "skipbo": "skip<PERSON>", "unstableunicorns": "iiunicornsezingaqinanga", "cyberse": "icyberse", "classicarcadegames": "imidlaloyamanduloyearcade", "osu": "osu", "gitadora": "gitadora", "dancegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fridaynightfunkin": "umdlalowakafridayebusuku", "fnf": "fnf", "proseka": "<PERSON><PERSON>", "projectmirai": "iprojekthimirai", "projectdiva": "projectdiva", "djmax": "djmax", "guitarhero": "<PERSON><PERSON><PERSON><PERSON>migi<PERSON>", "clonehero": "clonehero", "justdance": "dansamanje", "hatsunemiku": "<PERSON><PERSON><PERSON><PERSON>", "prosekai": "prosekai", "rocksmith": "rocksmith", "idolish7": "idolish7", "rockthedead": "k<PERSON><PERSON>abangasekhoyo", "chunithm": "chunithm", "idolmaster": "idolmaster", "dancecentral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rhythmgamer": "umdlaliwesingqi", "stepmania": "stepmania", "highscorerythmgames": "amane<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pkxd": "pkxd", "sidem": "isith<PERSON><PERSON>", "ongeki": "on<PERSON><PERSON>", "soundvoltex": "soundvoltex", "rhythmheaven": "umoyawomculo", "hypmic": "hypmic", "adanceoffireandice": "umdanisowumlilonomkhenkce", "auditiononline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itgmania": "<PERSON>gman<PERSON>", "juegosderitmo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cryptofthenecrodancer": "cryptofthenecrodancer", "rhythmdoctor": "ugqirharhythm", "cubing": "icubing", "wordle": "wordle", "teniz": "<PERSON><PERSON><PERSON>", "puzzlegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ond<PERSON>", "spotit": "yibone", "rummikub": "rum<PERSON><PERSON><PERSON>", "blockdoku": "blockdoku", "logicpuzzles": "iingxakiengqondo", "sudoku": "sudoku", "rubik": "rubik", "brainteasers": "imicimbiyengqondo", "rubikscube": "rubikscube", "crossword": "amazwi_aphindeneyo", "motscroisés": "motscroisés", "krzyżówki": "amagama_apha<PERSON>yisiweyo", "nonogram": "iinonogram", "bookworm": "incwadishologu", "jigsawpuzzles": "amaphazilizilojigsaw", "indovinello": "umcimbi", "riddle": "iq<PERSON>a", "riddles": "amaq<PERSON>a", "rompecabezas": "<PERSON><PERSON><PERSON><PERSON>", "tekateki": "tekateki", "inside": "<PERSON><PERSON><PERSON><PERSON>", "angrybirds": "iintakaezinomsonaxhosa", "escapesimulator": "balekaisimulator", "minesweeper": "<PERSON><PERSON><PERSON><PERSON>", "puzzleanddragons": "puzzleanddragons", "crosswordpuzzles": "amazwib<PERSON><PERSON><PERSON>", "kurushi": "k<PERSON>hi", "gardenscapesgame": "umdlalowasegardenscapes", "puzzlesport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escaperoomgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapegame": "umdlalowokubaleka", "3dpuzzle": "ipuzzleye3d", "homescapesgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wordsearch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enigmistica": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kulaworld": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myst": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "riddletales": "iintsomindida", "fishdom": "fishdom", "theimpossiblequiz": "umbuzonzima", "candycrush": "candycrush", "littlebigplanet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "match3puzzle": "umdlalowokudibanisa3", "huniepop": "h<PERSON><PERSON><PERSON>", "katamaridamacy": "katamaridamacy", "kwirky": "<PERSON><PERSON><PERSON>", "rubikcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuborubik": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yapboz": "<PERSON><PERSON><PERSON><PERSON>", "thetalosprinciple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homescapes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "puttputt": "iputtputt", "qbert": "qbert", "riddleme": "ndibuzeubugebenga", "tycoongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cubosderubik": "iicubeszikarubik", "cruciverba": "amagama_aphambene", "ciphers": "iim<PERSON><PERSON><PERSON>", "rätselwörter": "amagame<PERSON><PERSON><PERSON><PERSON>", "buscaminas": "buscaminas", "puzzlesolving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "turnipboy": "umfanawetheniphu", "adivinanzashot": "umdlalowokuqashela", "nobodies": "abangananto", "guessing": "uk<PERSON><PERSON>gela", "nonograms": "inonograms", "kostkirubika": "kostkirubika", "crypticcrosswords": "amagqabantsontsoxhosa", "syberia2": "syberia2", "puzzlehunt": "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>", "puzzlehunts": "imidlaloemibuzibuzi", "catcrime": "ubugebengu_bekati", "quebracabeça": "quebracabeça", "hlavolamy": "<PERSON><PERSON><PERSON><PERSON>", "poptropica": "poptropica", "thelastcampfire": "<PERSON><PERSON>lowokugqibela", "autodefinidos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "picopark": "picopark", "wandersong": "umcu<PERSON><PERSON><PERSON><PERSON>", "carto": "carto", "untitledgoosegame": "umdlaloweqazengeligama", "cassetête": "<PERSON><PERSON><PERSON>", "limbo": "umgibe", "rubiks": "irubiksi", "maze": "imbizo", "tinykin": "tinykin", "rubikovakostka": "ikhyubhukadyazo", "speedcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pieces": "iingceba", "portalgame": "umdlaloweportal", "bilmece": "imfumba", "puzzelen": "puzzelen", "picross": "ipicross", "rubixcube": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indovinelli": "amaq<PERSON>a", "cubomagico": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lingo", "mlbb": "mlbb", "pubgm": "pubgm", "codmobile": "codmobile", "codm": "codm", "twistedwonderland": "<PERSON>hlaba<PERSON>liyongiman<PERSON><PERSON>o", "monopoly": "monopoly", "futurefight": "umzabalazowexesha", "mobilelegends": "mobilelegends", "brawlstars": "brawlstars", "brawlstar": "brawlstar", "coc": "coc", "lonewolf": "inkedlamauhambe", "gacha": "gacha", "wr": "wr", "fgo": "fgo", "bitlife": "ubomilwehluko", "pikminbloom": "pik<PERSON><PERSON><PERSON>", "ff": "ff", "ensemblestars": "iinkwenkwezizemidlalo", "asphalt9": "asphalt9", "mlb": "mlb", "cookierunkingdom": "cookierunkingdom", "alchemystars": "iinkwenkwezizealchemy", "stateofsurvival": "imekoy<PERSON><PERSON>la", "mycity": "umdolobam", "arknights": "arknights", "colorfulstage": "umbalaweqonga", "bloonstowerdefense": "ibloonstowerdefense", "btd": "btd", "clashroyale": "clashroyale", "angela": "angela", "dokkanbattle": "dokkanbattle", "fategrandorder": "ifategrandorder", "hyperfront": "phambiliphambilikakh<PERSON>", "knightrun": "ukubalekakweqhawe", "fireemblemheroes": "fireemblemheroes", "honkaiimpact": "honkaiimpact", "soccerbattle": "umlogolwebhola", "a3": "a3", "phonegames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kingschoice": "ukhetholwenkosi", "guardiantales": "iintsomingumlindi", "petrolhead": "iphetrolhead", "tacticool": "<PERSON><PERSON><PERSON><PERSON>", "cookierun": "cookierunxhos<PERSON>", "pixeldungeon": "pixeldungeon", "arcaea": "arcaea", "outoftheloop": "andazikho", "craftsman": "umchwe<PERSON>", "supersus": "k<PERSON><PERSON><PERSON><PERSON>ayo", "slowdrive": "uhambolucothayo", "headsup": "q<PERSON><PERSON>", "wordfeud": "wordfeud", "bedwars": "imfazweyebhedi", "freefire": "freefire", "mobilegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lilysgarden": "igadilik<PERSON>ly", "farmville2": "farmville2", "animalcrossing": "animalcrossing", "bgmi": "bgmi", "teamfighttactics": "teamfighttactics", "clashofclans": "imfazaneyeclashofclans", "pjsekai": "pjsekai", "mysticmessenger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "callofdutymobile": "ukhwazakomsebenziweselula", "thearcana": "iarcana", "8ballpool": "i8ballpool", "emergencyhq": "ikomkhanyelwezing<PERSON>miseko", "enstars": "enstars", "randonautica": "randonautica", "maplestory": "maplestory", "albion": "albion", "hayday": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onmyoji": "<PERSON><PERSON><PERSON><PERSON>", "azurlane": "azurlane", "shakesandfidget": "nyakazi<PERSON>up<PERSON><PERSON><PERSON>e", "ml": "ml", "bangdream": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clashofclan": "ukunqwanqwadwana<PERSON>i", "starstableonline": "starstableonline", "dragonraja": "<PERSON><PERSON><PERSON>", "timeprincess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beatstar": "beatstar", "dragonmanialegend": "dragonmanialegend", "hanabi": "hanabi", "disneymirrorverse": "disney<PERSON>rorverse", "pocketlove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>kotho", "androidgames": "imidlalozeandroid", "criminalcase": "<PERSON>yalay<PERSON><PERSON><PERSON><PERSON>", "summonerswar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cookingmadness": "ubuphambenekompheko", "dokkan": "dokkan", "aov": "aov", "triviacrack": "triviacrack", "leagueofangels": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lordsmobile": "lordsmobile", "tinybirdgarden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gachalife": "gachalife", "neuralcloud": "ilifuengqondo", "mysingingmonsters": "iimonsterzendiculayo", "nekoatsume": "ne<PERSON>atsume", "bluearchive": "bluearchive", "raidshadowlegends": "uhlaselelokhanyalwelomlingo", "warrobots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mirrorverse": "isibukongqameko", "pou": "pou", "warwings": "ii<PERSON><PERSON><PERSON><PERSON><PERSON>", "fifamobile": "fifamobile", "mobalegendbangbang": "mlbbm<PERSON><PERSON>", "evertale": "evertale", "futime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "antiyoy": "antiyoy", "apexlegendmobile": "apexlegendmobile", "ingress": "ngena", "slugitout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mpl": "mpl", "coinmaster": "coinmaster", "punishinggrayraven": "ukunqandaumbanjingwevu", "petpals": "<PERSON><PERSON><PERSON>oboz<PERSON><PERSON><PERSON>yana", "gameofsultans": "umdlalowosultan", "arenabreakout": "ukuphuman<PERSON><PERSON><PERSON><PERSON>", "wolfy": "wolfy", "runcitygame": "bale<PERSON><PERSON>dlalowedoloph<PERSON>", "juegodemovil": "umdlaloweselfow<PERSON>", "avakinlife": "avakinlife", "kogama": "kogama", "mimicry": "ukulinganisa", "blackdesertmobile": "blackdesertmobile", "rollercoastertycoon": "rollercoastertycoon", "grandchase": "grandchase", "bombmebrasil": "ndiqhushumbumbebrazil", "ldoe": "ldoe", "legendonline": "ints<PERSON>yangeintan<PERSON>i", "otomegame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mindustry": "mindustry", "callofdragons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shiningnikki": "<PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carxdriftracing2": "carxdriftracingka2", "pathtonowhere": "indlelayokungeyondawo", "sealm": "sealm", "shadowfight3": "umlomelwesithunzi3", "limbuscompany": "iinkampanilimbus", "demolitionderby3": "idembidolwanye3", "wordswithfriends2": "amagamaabangane2", "soulknight": "umkh<PERSON><PERSON><PERSON>_womphefumlo", "purrfecttale": "purrfecttale", "showbyrock": "showbyrock", "ladypopular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lolmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "harvesttown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "perfectworldmobile": "umhlabawogqibeleleyo", "empiresandpuzzles": "ambukh<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "empirespuzzles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragoncity": "dragoncity", "garticphone": "garticphone", "battlegroundmobileind": "umhlabawedabixhosa", "fanny": "<PERSON><PERSON><PERSON><PERSON>", "littlenightmare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aethergazer": "aethergazer", "mudrunner": "umgibeli_wodaka", "tearsofthemis": "iinyembezizomthetho", "eversoul": "umphefumlowonaphakade", "gunbound": "gunbound", "gamingmlbb": "ukudlalamlbb", "dbdmobile": "dbdmobile", "arknight": "arknight", "pristontale": "<PERSON><PERSON><PERSON><PERSON>", "zombiecastaways": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eveechoes": "eveechoes", "jogocelular": "umdlalowesel<PERSON>", "mariokarttour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zooba": "zooba", "mobilelegendbangbang": "mobilelegendbangbang", "gachaclub": "gachaclub", "v4": "v4", "cookingmama": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cabalmobile": "cabalmobile", "streetfighterduel": "imfazweyasesital<PERSON>weni", "lesecretdhenri": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamingbgmi": "umdlalowebgmi", "girlsfrontline": "amantombazana<PERSON><PERSON><PERSON>", "jurassicworldalive": "jurassicworldalive", "soulseeker": "umfuniwomphefumlo", "gettingoverit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openttd": "openttd", "onepiecebountyrush": "onepiecebountyrush", "moonchaistory": "ibalilikamoonchai", "carxdriftracingonline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jogosmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendofneverland": "intsomikaneneverland", "pubglite": "pubglite", "gamemobilelegends": "umdlalowemobilelegends", "timeraiders": "aba<PERSON><PERSON><PERSON>_be<PERSON>ha", "gamingmobile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marvelstrikeforce": "marvelstrikeforce", "thebattlecats": "imadodaemidala", "dnd": "<PERSON><PERSON><PERSON><PERSON>", "quest": "umngeni", "giochidiruolo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dnd5e": "dnd5e", "rpgdemesa": "rpgsetafileni", "worldofdarkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "travellerttrpg": "umdlaliwetrpgwomhambi", "2300ad": "2300ad", "larp": "larp", "romanceclub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "d20": "d20", "pokemongames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonmysterydungeon": "pokemonmysterydungeon", "pokemonlegendsarceus": "poke<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemoncrystal": "pokemoncrystal", "pokemonanime": "pokemonanime", "pokémongo": "pokémongo", "pokemonred": "pokemonbomvu", "pokemongo": "pokemongo", "pokemonshowdown": "pokemonshowdown", "pokemonranger": "poke<PERSON><PERSON>er", "lipeep": "lipeep", "porygon": "porygon", "pokemonunite": "pokemonunite", "entai": "entai", "hypno": "hypno", "empoleon": "empoleon", "arceus": "arceus", "mewtwo": "mewtwo", "paldea": "paldea", "pokemonscarlet": "poke<PERSON><PERSON><PERSON>", "chatot": "incoko", "pikachu": "pikachu", "roxie": "roxie", "pokemonviolet": "pokemonviolet", "pokemonpurpura": "pokemonmfusa", "ashketchum": "ashketchum", "gengar": "gengar", "natu": "natu", "teamrocket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "furret": "<PERSON><PERSON><PERSON>", "magikarp": "magi<PERSON><PERSON>", "mimikyu": "mi<PERSON><PERSON><PERSON>", "snorlax": "snorlax", "pocketmonsters": "iipokethimonstaz", "nuzlocke": "nuzlocke", "pokemonplush": "ipokemonplush", "teamystic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokeball": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mon<PERSON>", "charmander": "charmander", "pokemonromhack": "pokemonromhack", "pubgmobile": "pubgmobile", "litten": "litten", "shinypokemon": "ipokemonenyakazayo", "mesprit": "mesprit", "pokémoni": "pokémoni", "ironhands": "iingalongalanga", "kabutops": "kabutops", "psyduck": "psyduck", "umbreon": "umbreon", "pokevore": "pokevore", "ptcg": "ptcg", "piplup": "pip<PERSON>p", "pokemonsleep": "poke<PERSON><PERSON><PERSON>a", "heyyoupikachu": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokémonsleep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kidsandpokemon": "aban<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pokemonsnap": "poke<PERSON>nap", "bulbasaur": "ibulbasaur", "lucario": "lucario", "charizar": "charizar", "shinyhunter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ajedrez": "itshesi", "catur": "<PERSON>ur", "xadrez": "<PERSON><PERSON><PERSON>", "scacchi": "scacchi", "schaken": "schaken", "skak": "skak", "ajedres": "itshesi", "chessgirls": "amantombaz<PERSON><PERSON>", "magnuscarlsen": "magn<PERSON><PERSON><PERSON><PERSON>", "worldblitz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jeudéchecs": "umdlalowe<PERSON>", "japanesechess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chinesechess": "iqhing<PERSON>hing<PERSON>lasitshayina", "chesscanada": "chesscanada", "fide": "fide", "xadrezverbal": "xadrezverbal", "openings": "<PERSON><PERSON><PERSON><PERSON>", "rook": "ir<PERSON><PERSON>", "chesscom": "chesscom", "calabozosydragones": "imibonozeenkwenkwezi", "dungeonsanddragon": "imingxunyakunodragoni", "dungeonmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tiamat": "tiamat", "donjonsetdragons": "iminothoth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oxventure": "oxventure", "darksun": "l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofvoxmachina": "intsomikavoxmachina", "doungenoanddragons": "dungeonanddragons", "darkmoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftchampionship": "ubunts<PERSON><PERSON><PERSON>_beminecraft", "minecrafthive": "minecrafthive", "minecraftbedrock": "minecraftbedrock", "dreamsmp": "dreamsmp", "hermitcraft": "hermitcraft", "minecraftjava": "minecraftjava", "hypixelskyblock": "hypixelskyblock", "minetest": "minetest", "hypixel": "hypixel", "karmaland5": "karmaland5", "minecraftmods": "iimods_zeminecraft", "mcc": "mcc", "candleflame": "id<PERSON><PERSON><PERSON>", "fru": "fru", "addons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mcpeaddons": "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "skyblock": "skyblock", "minecraftpocket": "minecraftpocket", "minecraft360": "minecraft360", "moddedminecraft": "minecrafteditshiweyo", "minecraftps4": "minecraftps4", "minecraftpc": "minecraftpc", "betweenlands": "phak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minecraftdungeons": "minecraftdungeons", "minecraftcity": "isixekosiminecraft", "pcgamer": "<PERSON>dl<PERSON><PERSON>kh<PERSON><PERSON><PERSON><PERSON>", "jeuxvideo": "<PERSON><PERSON><PERSON><PERSON>", "gambit": "i<PERSON><PERSON><PERSON>", "gamers": "amdlali", "levelup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamermobile": "umdlalo<PERSON><PERSON>", "gameover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gg": "gg", "pcgaming": "pcgaming", "gamen": "<PERSON><PERSON><PERSON><PERSON>", "oyunoynamak": "oyunoyadlala", "pcgames": "imidlalopc", "casualgaming": "imidlaloyonwabiso", "gamingsetup": "isetaphiyokudlala", "pcmasterrace": "pcmasterrace", "pcgame": "<PERSON>dlalo<PERSON>kh<PERSON><PERSON><PERSON><PERSON>", "gamerboy": "umfanawem<PERSON><PERSON><PERSON>", "vrgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drdisrespect": "drdisrespect", "4kgaming": "4kumidlalo", "gamerbr": "umdlalibr", "gameplays": "<PERSON><PERSON><PERSON><PERSON>", "consoleplayer": "umdlaliwekhonsoli", "boxi": "boxi", "pro": "ngcupheki", "epicgamers": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onlinegaming": "<PERSON>dl<PERSON>wei<PERSON><PERSON><PERSON>", "semigamer": "mdl<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamergirls": "amantombazanedlala", "gamermoms": "omamabokudlala", "gamerguy": "umdlaligeyim", "gamewatcher": "umbukeli_womdlalo", "gameur": "um<PERSON><PERSON>", "grypc": "grypc", "rangugamer": "umdlaliwerangugamer", "gamerschicas": "gamerschicas", "otoge": "otoge", "dedsafio": "umngeniwekathukela", "teamtryhard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mallugaming": "umdlalowasemalayalam", "pawgers": "iipawgers", "quests": "imibuzo", "alax": "alax", "avgn": "avgn", "oldgamer": "umdlalogeyima", "cozygaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamelpay": "gamelpay", "juegosdepc": "imidlaloyepc", "dsswitch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "competitivegaming": "<PERSON>dlaloweq<PERSON>", "minecraftnewjersey": "minecraftnewjersey", "faker": "umhoxi", "pc4gamers": "pc4<PERSON><PERSON><PERSON>", "gamingff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yatoro": "yatoro", "heterosexualgaming": "ukudlalai<PERSON>dl<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamepc": "<PERSON>dlalo<PERSON>kh<PERSON><PERSON><PERSON><PERSON>", "girlsgamer": "amantombazanadlala", "fnfmods": "iimodyefnf", "dailyquest": "umbuthowelus<PERSON><PERSON>kel<PERSON><PERSON>", "gamegirl": "intshaneyomdlalo", "chicasgamer": "amantombazanagamer", "gamesetup": "ukusethelwal<PERSON><PERSON><PERSON>", "overpowered": "igqi<PERSON><PERSON><PERSON>", "socialgamer": "umdlaliwezenhlalo", "gamejam": "gamejam", "proplayer": "mdlaliophambili", "roleplayer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myteam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "republicofgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aorus": "aorus", "cougargaming": "imidlaloyamacougar", "triplelegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerbuddies": "abanganibomdlalo", "butuhcewekgamers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "christiangamer": "umkrelixhosa", "gamernerd": "umdlaliwegeek", "nerdgamer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "afk": "n<PERSON><PERSON><PERSON>", "andregamer": "andregamer", "casualgamer": "umdlaliwesix<PERSON>", "89squad": "89squad", "inicaramainnyagimana": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "insec": "insec", "gemers": "<PERSON><PERSON><PERSON><PERSON>", "oyunizlemek": "oyunizlemek", "gamertag": "igama_lomdlali", "lanparty": "lanparty", "videogamer": "umdlalividiyo", "wspólnegranie": "<PERSON><PERSON><PERSON><PERSON>un<PERSON>", "mortdog": "mortdog", "playstationgamer": "umdlaliwaseplaystation", "justinwong": "justin<PERSON><PERSON>", "healthygamer": "umdlali<PERSON><PERSON><PERSON><PERSON>", "gtracing": "<PERSON><PERSON>o", "notebookgamer": "mdlalidyincwadi", "protogen": "protogen", "womangamer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "obviouslyimagamer": "ngokusobalamdlaliyemidlalo", "mario": "mario", "papermario": "papermario", "mariogolf": "umariogolfu", "samusaran": "sa<PERSON><PERSON>", "forager": "umfunafuna", "humanfallflat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supernintendo": "supernintendo", "nintendo64": "nintendo64", "zeroescape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "waluigi": "wa<PERSON><PERSON>", "nintendoswitch": "nintendoswitch", "nintendosw": "nintendosw", "nintendomusic": "umculowenintendo", "sonicthehedgehog": "sonicthehedgehog", "sonic": "sonic", "fallguys": "abagqatsiufall<PERSON><PERSON>", "switch": "t<PERSON>ts<PERSON>", "zelda": "zelda", "smashbros": "smashbros", "legendofzelda": "<PERSON><PERSON><PERSON><PERSON>", "splatoon": "splatoon", "metroid": "metroid", "pikmin": "pikmin", "ringfit": "ringfit", "amiibo": "amiibo", "megaman": "megaman", "majorasmask": "majorasmask", "mariokartmaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wii": "wii", "aceattorney": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>we<PERSON><PERSON>", "ssbm": "ssbm", "skychildrenofthelight": "abantwana<PERSON><PERSON><PERSON><PERSON><PERSON>", "tomodachilife": "ubomilweqabane", "ahatintime": "<PERSON><PERSON><PERSON><PERSON>", "tearsofthekingdom": "iinyembezizobukumkani", "walkingsimulators": "imithat<PERSON><PERSON><PERSON><PERSON>", "nintendogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thelegendofzelda": "intsomikazelda", "dragonquest": "dragonquest", "harvestmoon": "inyangayokuvuna", "mariobros": "mario<PERSON>s", "runefactory": "runefactory", "banjokazooie": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "celeste": "celeste", "breathofthewild": "umoyaweganga", "myfriendpedro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "legendsofzelda": "iintsomizelda", "donkeykong": "donkeykong", "mariokart": "<PERSON><PERSON><PERSON><PERSON>", "kirby": "kirby", "51games": "imidlalo51", "earthbound": "<PERSON><PERSON><PERSON><PERSON>", "tales": "<PERSON><PERSON><PERSON>", "raymanlegends": "raymanlegends", "luigismansion": "<PERSON>dl<PERSON><PERSON><PERSON><PERSON>", "animalcrosssing": "animalcrossing", "taikonotatsujin": "taikonotatsujin", "nintendo3ds": "nintendo3ds", "supermariobros": "supermariobros", "mariomaker2": "mariomaker2", "boktai": "boktai", "smashultimate": "idlaloyokugqibela", "nintendochile": "nintendochile", "tloz": "tloz", "trianglestrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermariomaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xenobladechronicles3": "xenobladechronicles3", "supermario64": "supermario64", "conkersbadfurday": "ikonkileiyambiingolwesihlanu", "nintendos": "iinintendo", "new3ds": "entsha3ds", "donkeykongcountry2": "donkeykongcountry2", "hyrulewarriors": "amada<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariopartysuperstars": "mariopartysuperstars", "marioandsonic": "umariokasonic", "banjotooie": "<PERSON><PERSON><PERSON><PERSON>", "nintendogs": "izinjana_zenintendo", "thezelda": "izelda", "palia": "palia", "marioandluigi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mariorpg": "mariorpg", "zeldabotw": "zeldabotw", "yuumimain": "y<PERSON><PERSON><PERSON><PERSON>", "wildrift": "wildrift", "riven": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ahri": "ahri", "illaoi": "<PERSON><PERSON><PERSON>", "aram": "aram", "cblol": "cblol", "leagueoflegendslas": "leagueoflegendslas", "urgot": "urgot", "zyra": "zyra", "redcanids": "iingcukamazibomvu", "vanillalol": "vanillalol", "wildriftph": "wildriftph", "lolph": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leagueoflegend": "umbuthowamagorha", "tốcchiến": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gragas": "gragas", "leagueoflegendswild": "leagueoflegendswild", "adcarry": "adcarry", "lolzinho": "<PERSON><PERSON><PERSON><PERSON>", "leagueoflegendsespaña": "leagueoflegendsspain", "aatrox": "aatrox", "euw": "yhu", "leagueoflegendseuw": "leagueoflegendseuw", "kayle": "kayle", "samira": "<PERSON><PERSON>a", "akali": "akali", "lunari": "<PERSON><PERSON>", "fnatic": "fnatic", "lollcs": "lollcs", "akshan": "<PERSON><PERSON>han", "milio": "milio", "shaco": "shaco", "ligadaslegendas": "ligadaslegendas", "gaminglol": "gaminglol", "nasus": "nasus", "teemo": "teemo", "zedmain": "<PERSON><PERSON><PERSON><PERSON>", "hexgates": "iihexgates", "hextech": "hextech", "fortnitegame": "umdlalowefortnite", "gamingfortnite": "umdlalowefortnite", "fortnitebr": "fortnitebr", "retrovideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scaryvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamemaker": "umenziwemdlalo", "megamanzero": "megamanzero", "videogame": "umdlalo", "videosgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "professorlayton": "profe<PERSON><PERSON><PERSON>", "overwatch": "overwatch", "ow2": "ow2", "overwatch2": "overwatch2", "wizard101": "wizard101", "battleblocktheater": "idabilebhlokoyimidlaloweqonga", "arcades": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acnh": "acnh", "puffpals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "farmingsimulator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "robloxchile": "rob<PERSON><PERSON><PERSON><PERSON>", "roblox": "roblo<PERSON>", "robloxdeutschland": "robloxdeutschland", "robloxdeutsch": "robloxisixhosa", "erlc": "erlc", "sanboxgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamelore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rollerdrome": "irollerdrome", "parasiteeve": "intsholongomane", "gamecube": "igamecube", "starcraft2": "starcraft2", "duskwood": "<PERSON><PERSON><PERSON>", "dreamscape": "iphuphothombi", "starcitizen": "starcitizen", "yanderesimulator": "yanderesimulator", "grandtheftauto": "grandtheftauto", "deadspace": "indawoefileyo", "amordoce": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogiochi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "theoldrepublic": "<PERSON>rip<PERSON><PERSON><PERSON><PERSON><PERSON>", "videospiele": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touhouproject": "touhouproject", "dreamcast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adventuregames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wolfenstein": "wolfenstein", "actionadventure": "umdlaloaven<PERSON><PERSON>", "storyofseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retrogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vintagecomputing": "ikh<PERSON><PERSON><PERSON><PERSON>a", "retrogaming": "umdlalowamanqaku<PERSON>la", "vintagegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "playdate": "<PERSON><PERSON><PERSON><PERSON>", "commanderkeen": "commander<PERSON><PERSON>", "bugsnax": "bugsnax", "injustice2": "ubungabulungiswa2", "shadowthehedgehog": "shadowthehedgehog", "rayman": "rayman", "skygame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zenlife": "ubomiluzolileyo", "beatmaniaiidx": "beatmaniaiidx", "steep": "gqithileyo", "mystgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blockchaingaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "medievil": "intlamntwanaleyamedievil", "consolegaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "konsolen": "iikonsoli", "outrun": "baleka", "bloomingpanic": "ukup<PERSON><PERSON><PERSON><PERSON>", "tobyfox": "tobyfox", "hoyoverse": "hoyoverse", "senrankagura": "sen<PERSON><PERSON><PERSON>", "gaminghorror": "umdlalooyoyiki<PERSON>o", "monstergirlquest": "umbuthoweentombidezilo", "supergiant": "isigebenga", "disneydreamlightvalle": "disneydreamlightvalley", "farmingsims": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "juegosviejos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bethesda": "bethesda", "jackboxgames": "imidlalojackbox", "interactivefiction": "intsomiyamabali", "pso2ngs": "pso2ngs", "grimfandango": "grimfandango", "thelastofus2": "thelastofus2", "amantesamentes": "amantesamentes", "visualnovel": "inovelihibonwayo", "visualnovels": "iinoveli_zokubona", "rgg": "rgg", "shadowolf": "ingcukaikrele", "tcrghost": "tcrimoya", "payday": "imini_yom<PERSON><PERSON>", "chatherine": "<PERSON><PERSON>ine", "twilightprincess": "nkosazanayokuhlwa", "jakandaxter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sandbox": "ib<PERSON><PERSON><PERSON>_yesanti", "aestheticgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "novelavisual": "inovelihlekayo", "thecrew2": "iqelakabobini2", "alexkidd": "<PERSON><PERSON><PERSON><PERSON>", "retrogame": "umdlalowakuludala", "tonyhawkproskater": "tonyhawkpros<PERSON><PERSON>", "smbz": "smbz", "lamento": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "godhand": "isandlasothixo", "leafblowerrevolution": "uk<PERSON>vukelau<PERSON><PERSON><PERSON>", "wiiu": "wiiu", "leveldesign": "uyilolweqonga", "starrail": "starrail", "keyblade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aplaguetale": "intsomiyesifo", "fnafsometimes": "fnafngamanye", "novelasvisuales": "iinovelaezibonakalayo", "robloxbrasil": "robloxbrasil", "pacman": "ipacman", "gameretro": "um<PERSON><PERSON><PERSON><PERSON>a", "videojuejos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "videogamedates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mycandylove": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>candy", "megaten": "megaten", "mortalkombat11": "mortalkombat11", "everskies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "justcause3": "kungenasizathu3", "hulkgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "batmangames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnofreckoning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamstergaming": "gamstergaming", "dayofthetantacle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maniacmansion": "indluyengqondo", "crashracing": "<PERSON>dl<PERSON>wo<PERSON><PERSON><PERSON><PERSON><PERSON>", "3dplatformers": "imidlalo3dyokugxumagxuma", "nfsmw": "nfsmw", "kimigashine": "kimigashine", "oldschoolgaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hellblade": "isiphokos<PERSON><PERSON><PERSON>", "storygames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bioware": "bioware", "residentevil6": "residentevil6", "soundodger": "ukugxothalumsindo", "beyondtwosouls": "ngaphayakomphefumlombini", "gameuse": "umdlalo", "offmortisghost": "offmortisghost", "tinybunny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarch": "retroarch", "powerup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "katanazero": "katanazero", "famicom": "famicom", "aventurasgraficas": "aventurasgraficas", "quickflash": "ngokukhawuleza", "fzero": "fzero", "gachagaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "retroarcades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "f123": "f123", "wasteland": "ink<PERSON>la", "powerwashsim": "umdlalowokuhlambangamandla", "coralisland": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "syberia3": "syberia3", "grymmorpg": "grymmorpg", "bloxfruit": "bloxfruit", "anotherworld": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metaquest": "iphephaq<PERSON><PERSON><PERSON>", "animewarrios2": "animewarrios2", "footballfusion": "ibholaekhatywayo", "edithdlc": "edithdlc", "abzu": "ab<PERSON>", "astroneer": "umham<PERSON>_wezinkwenkwezi", "legomarvel": "legomarvel", "wranduin": "w<PERSON><PERSON>", "twistedmetal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beamngdrive": "beamngdrive", "twdg": "twdg", "pileofshame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simulator": "<PERSON><PERSON><PERSON><PERSON>", "symulatory": "iisimulati", "speedrunner": "<PERSON><PERSON><PERSON><PERSON>_we<PERSON><PERSON>a", "epicx": "epicx", "superrobottaisen": "umloqowerobhotienkulukazi", "dcuo": "dcuo", "samandmax": "sa<PERSON><PERSON>", "grywideo": "ividiyo_ephilayo", "gaiaonline": "gaiaonline", "korkuoyunu": "umdlalowoyiki", "wonderlandonline": "wonderlandonline", "skylander": "skylander", "boyfrienddungeon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toontownrewritten": "toontownibhalwangokusha", "simracing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simrace": "mdlaloweemoto", "pvp": "pvp", "urbanchaos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "heavenlybodies": "imizim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seum": "buh<PERSON><PERSON>", "partyvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "graveyardkeeper": "umgciniamangcwaba", "spaceflightsimulator": "umlinganisiwo<PERSON><PERSON><PERSON><PERSON>", "legacyofkain": "ushiyekokukakain", "hackandslash": "sika_usike", "foodandvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oyunvideoları": "ii<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "thewolfamongus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "truckingsimulator": "ulinganisotransport", "horizonworlds": "ii<PERSON><PERSON>thiehlang<PERSON>", "handygame": "umdlalool<PERSON>", "leyendasyvideojuegos": "iintsominemidlalo", "oldschoolvideogames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "racingsimulator": "umdlalowomdyarhowokuqhuba", "beemov": "bee<PERSON>v", "agentsofmayhem": "iarhentezikamayhem", "songpop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "famitsu": "famitsu", "gatesofolympus": "amasangokaolympus", "monsterhunternow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rebelstar": "inkwenkweziyovukelo", "indievideogaming": "umdlalowekhompyuthayasezimele", "indiegaming": "imidlaloezimeleyo", "indievideogames": "imidlaloindependenti", "indievideogame": "<PERSON>dlalo<PERSON>kh<PERSON><PERSON><PERSON><PERSON>", "chellfreeman": "chell<PERSON>man", "spidermaninsomniac": "spidermanong<PERSON>li", "bufffortress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unbeatable": "akukwazekikunqoba", "projectl": "iprojekthi", "futureclubgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mugman": "indodaekofu", "insomniacgames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supergiantgames": "imidlaloyinkulu", "henrystickman": "hen<PERSON><PERSON><PERSON>", "henrystickmin": "hen<PERSON><PERSON><PERSON>", "celestegame": "umdlaloceleste", "aperturescience": "isayensiyesivalo", "backlog": "<PERSON><PERSON><PERSON><PERSON>", "gamebacklog": "imidlalongaph<PERSON>lang<PERSON>", "gamingbacklog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personnagejeuxvidéos": "umlinganiswawemdlalowekhompyutha", "achievementhunter": "umzingeliwemp<PERSON><PERSON><PERSON>", "cityskylines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supermonkeyball": "supermonkeyball", "deponia": "deponia", "naughtydog": "injangaxaka", "beastlord": "inkosiyezilwanyana", "juegosretro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentuckyroutezero": "kentuckyroutezero", "oriandtheblindforest": "oriandtheblindforest", "alanwake": "alanwake", "stanleyparable": "stanleyparable", "reservatoriodedopamin": "umthombokayadopamine", "staxel": "staxel", "videogameost": "um<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonsync": "dragonsync", "vivapiñata": "vivapiña<PERSON>", "ilovekofxv": "ndiyayithandakofxv", "arcanum": "ubu<PERSON><PERSON>", "neoy2k": "neoy2k", "pcracing": "umdyarholwepcngembhali", "berserk": "phambene", "baki": "baki", "sailormoon": "sailormoon", "saintseiya": "<PERSON><PERSON><PERSON>", "inuyasha": "<PERSON><PERSON><PERSON><PERSON>", "yuyuhakusho": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initiald": "<PERSON><PERSON>thiweyo", "elhazard": "<PERSON><PERSON><PERSON>", "dragonballz": "dragonballz", "sadanime": "anime_ebuhlungu", "darkerthanblack": "m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animescaling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animewithplot": "animenomxholo", "pesci": "pesci", "retroanime": "i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animes": "ianime", "supersentai": "supersentai", "samuraichamploo": "samuraichamploo", "madoka": "<PERSON>oka", "higurashi": "<PERSON><PERSON><PERSON><PERSON>", "80sanime": "i80sanime", "90sanime": "ama<PERSON><PERSON><PERSON>", "darklord": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popeetheperformer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masterpogi": "<PERSON><PERSON><PERSON><PERSON>", "samuraix": "samuraix", "dbgt": "dbgt", "veranime": "veranime", "2000sanime": "ianime2000s", "lupiniii": "lup<PERSON><PERSON>", "drstoneseason1": "drstonesesini1", "rapanime": "rapanime", "chargemanken": "chargemanken", "animecover": "animegquma", "thevisionofescaflowne": "umbonowescaflowne", "slayers": "aba<PERSON><PERSON>", "tokyomajin": "tokyomajin", "anime90s": "anime90s", "animcharlotte": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gantz": "gantz", "shoujo": "shoujo", "bananafish": "<PERSON>tl<PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jjk": "jjk", "haikyu": "haikyu", "toiletboundhanakokun": "umkhonziwasend<PERSON><PERSON>i_yangasese_uhanakokun", "bnha": "bnha", "hellsing": "hellsing", "skipbeatmanga": "gwethaisikipbeatmanga", "vanitas": "ubu<PERSON><PERSON>_obu<PERSON><PERSON>li", "fireforce": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moriartythepatriot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futurediary": "ikamvadiary", "fairytail": "intsomi", "dorohedoro": "dorohedoro", "vinlandsaga": "v<PERSON><PERSON><PERSON>", "madeinabyss": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parasyte": "parasyte", "punpun": "punpun", "shingekinokyojin": "shingekinokyojin", "mushishi": "mushishi", "beastars": "iibeastars", "vanitasnocarte": "vanitasnocarte", "mermaidmelody": "umcu<PERSON><PERSON><PERSON>bikhoka<PERSON>lwandle", "kamisamakiss": "kamisamancanca", "blmanga": "blmanga", "horrormanga": "amanga<PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemangas": "iimangaromance", "karneval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragonmaid": "intombizomngcwevu", "blacklagoon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kentaromiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho100": "mobpsycho100", "terraformars": "iiterraformars", "geniusinc": "iqaqambileink", "shamanking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kurokonobasket": "kurokonobasket", "jugo": "jugo", "bungostraydogs": "izinjaezintolikwanyobungou", "jujustukaisen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jujutsu": "jujutsu", "yurionice": "yurionice", "acertainmagicalindex": "inqakumloyothakathiethile", "sao": "sao", "blackclover": "blackclover", "tokyoghoul": "tokyoghoul", "onepunchman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hetalia": "hetalia", "kagerouproject": "iprojekthikagerouproject", "haikyuu": "haikyuu", "toaru": "toaru", "crunchyroll": "crunchyroll", "aot": "aot", "sk8theinfinity": "sk8<PERSON><PERSON><PERSON>", "siriusthejaeger": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "spyxfamily": "spyxfamily", "rezero": "rezero", "swordartonline": "swordartonline", "dororo": "<PERSON><PERSON><PERSON>", "wondereggpriority": "wondereggpriority", "angelsofdeath": "iingelosilokufa", "kakeguri": "<PERSON><PERSON><PERSON><PERSON>", "dragonballsuper": "dragonballsuper", "hypnosismic": "hypnosismic", "goldenkamuy": "<PERSON><PERSON><PERSON><PERSON>", "monstermusume": "imonstermusume", "konosuba": "konosuba", "aikatsu": "aikatsu", "sportsanime": "umdlalowecomic", "sukasuka": "<PERSON><PERSON><PERSON>", "arwinsgame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "angelbeats": "angelbeats", "isekaianime": "isekaianime", "sagaoftanyatheevil": "imbalikatanyao<PERSON>ndawo", "shounenanime": "shou<PERSON><PERSON><PERSON>", "bandori": "bandori", "tanya": "tanya", "durarara": "<PERSON><PERSON><PERSON>", "prettycure": "prettycure", "theboyandthebeast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fistofthenorthstar": "inqakuyelasenyakatho", "mazinger": "mazinger", "blackbuttler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "towerofgod": "inqabakankulunkulu", "elfenlied": "elfenlied", "akunohana": "a<PERSON><PERSON><PERSON>", "chibi": "chibi", "servamp": "servamp", "howtokeepamummy": "indlelayokugcinamama", "fullmoonwosagashite": "inyang<PERSON><PERSON><PERSON><PERSON>o", "shugochara": "shu<PERSON><PERSON><PERSON>", "tokyomewmew": "tokyomewmew", "gugurekokkurisan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cuteandcreepy": "cutekodwacrepy", "martialpeak": "ukungcamelakwezemfazwe", "bakihanma": "b<PERSON><PERSON><PERSON>", "hiscoregirl": "intombeyehighscore", "orochimaru": "<PERSON><PERSON><PERSON><PERSON>", "mierukochan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dabi": "dabi", "johnconstantine": "joh<PERSON><PERSON><PERSON><PERSON>", "astolfo": "astolfo", "revanantfae": "revanant<PERSON>e", "shinji": "shinji", "zerotwo": "zerotwo", "inosuke": "<PERSON><PERSON><PERSON>", "nezuko": "<PERSON><PERSON><PERSON>", "monstergirl": "intombazasemoyeni", "kanae": "kanae", "yone": "yone", "mitsuki": "<PERSON><PERSON><PERSON>", "kakashi": "<PERSON><PERSON>hi", "lenore": "lenore", "benimaru": "<PERSON><PERSON><PERSON>", "saitama": "saitama", "sanji": "sanji", "bakugo": "bakugo", "griffith": "griffith", "ririn": "ririn", "korra": "korra", "vanny": "iveni", "vegeta": "vegeta", "goromi": "goromi", "luci": "luci", "reigen": "reigen", "scaramouche": "scaramouche", "amiti": "amiti", "sailorsaturn": "umkhwelisaturn", "dio": "dio", "sailorpluto": "sailorpluto", "aloy": "aloy", "runa": "baleka", "oldanime": "anime_yamandulo", "chainsawman": "umkh<PERSON><PERSON>_wesarha", "bungoustraydogs": "bungoustraydogs", "jogo": "umdlalo", "franziska": "franziska", "nekomimi": "<PERSON><PERSON><PERSON><PERSON>", "inumimi": "<PERSON><PERSON><PERSON><PERSON>", "isekai": "<PERSON><PERSON>i", "tokyorevengers": "tokyorevengers", "blackbutler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ergoproxy": "ergoproxy", "claymore": "idlomo", "loli": "loli", "horroranime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fruitsbasket": "isitya_sezi<PERSON><PERSON>o", "devilmancrybaby": "devilmancrybaby", "noragami": "<PERSON><PERSON><PERSON>", "mangalivre": "mangalivre", "kuroshitsuji": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seinen": "seinen", "lovelive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sakuracardcaptor": "sakuracardcaptor", "umibenoetranger": "um<PERSON><PERSON><PERSON><PERSON><PERSON>", "owarinoseraph": "owarinoseraph", "thepromisedneverland": "umhlabaobalindelweyo", "monstermanga": "imangayesilwan<PERSON>", "yourlieinapril": "ubuxokibakh<PERSON>ngoaprili", "buggytheclown": "bhagit<PERSON><PERSON><PERSON>", "bokunohero": "bokunohero", "seraphoftheend": "iseraphi_yesiphelo", "trigun": "trigun", "cyborg009": "cyborg009", "magi": "<PERSON><PERSON><PERSON><PERSON>", "deepseaprisoner": "imban<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON>", "jojolion": "jojo<PERSON>", "deadmanwonderland": "umhlabawomfilelimagwala", "bannafish": "bananafish", "sukuna": "<PERSON>kuna", "darwinsgame": "umdlalokadarwin", "husbu": "n<PERSON><PERSON><PERSON>", "sugurugeto": "<PERSON><PERSON><PERSON><PERSON>", "leviackerman": "<PERSON><PERSON><PERSON><PERSON>", "sanzu": "sanzu", "sarazanmai": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pandorahearts": "iintliziyozepandora", "yoimiya": "<PERSON><PERSON><PERSON>", "foodwars": "imfuloe<PERSON><PERSON><PERSON><PERSON>", "cardcaptorsakura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stolas": "stolas", "devilsline": "umgcadekadeveli", "toyoureternity": "kude_kube_ngunaphakade", "infpanime": "infpanime", "eleceed": "elec<PERSON>", "akamegakill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "blueperiod": "ixeshaeliblue", "griffithberserk": "griffithberserk", "shinigami": "shinigami", "secretalliance": "imbumbanomfihlakalo", "mirainikki": "<PERSON><PERSON><PERSON><PERSON>", "mahoutsukainoyome": "mahoutsukainoyome", "yuki": "yuki", "erased": "cinyiwe", "bluelock": "isik<PERSON><PERSON><PERSON><PERSON>", "goblinslayer": "umbulali_wamagoblin", "detectiveconan": "umcupheconan", "shiki": "shiki", "deku": "deku", "akitoshinonome": "akitoshinonome", "riasgremory": "riasgremory", "shojobeat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vampireknight": "umkhosi_wevampire", "mugi": "mugi", "blueexorcist": "umgxuzisimhlubilanga", "slamdunk": "slamdunk", "zatchbell": "<PERSON><PERSON>bell", "mashle": "muhle", "scryed": "ndibonile", "spyfamily": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "airgear": "airgear", "magicalgirl": "intombazaneeyimilingo", "thesevendeadlysins": "izononesikhombaiziholovayo", "prisonschool": "ijalenesikoloxh", "thegodofhighschool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kissxsis": "<PERSON><PERSON><PERSON>", "grandblue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mydressupdarling": "<PERSON>mb<PERSON><PERSON><PERSON>thigin<PERSON>", "dgrayman": "<PERSON><PERSON><PERSON>", "rozenmaiden": "rozen<PERSON>iden", "animeuniverse": "animeuniverse", "swordartonlineabridge": "iinkembeswordartonlineabridge", "saoabridged": "saoli<PERSON>ts<PERSON>tshiweyo", "hoshizora": "hoshizora", "dragonballgt": "dragonballgt", "bocchitherock": "boc<PERSON><PERSON><PERSON>", "kakegurui": "ka<PERSON><PERSON><PERSON><PERSON>", "mobpyscho100": "mobpyscho100", "hajimenoippo": "hajimenoippo", "undeadunluck": "<PERSON>ga<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "romancemanga": "amangaothando", "blmanhwa": "blman<PERSON>", "kimetsunoyaba": "kimetsunoyaba", "kohai": "kohai", "animeromance": "uthando_lweanime", "senpai": "senpai", "blmanhwas": "b<PERSON><PERSON><PERSON>", "animeargentina": "animeargentina", "lolicon": "lo<PERSON>on", "demonslayertothesword": "umbulali_wedemon_uye_krelemba", "bloodlad": "igazi_lenkosi", "goodbyeeri": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "firepunch": "mpama_yomlilo", "adioseri": "adios<PERSON>", "tatsukifujimoto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kinnikuman": "<PERSON><PERSON><PERSON>", "mushokutensei": "mush<PERSON><PERSON><PERSON>", "shoujoai": "<PERSON><PERSON><PERSON><PERSON>", "starsalign": "iinkwenkweziziyanx<PERSON>", "romanceanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsundere": "tsundere", "yandere": "yandere", "mahoushoujomadoka": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kenganashura": "k<PERSON><PERSON><PERSON><PERSON>", "saointegralfactor": "saointegralfactor", "cherrymagic": "umlingo_wecherry", "housekinokuni": "indluyelizwe", "recordragnarok": "rekhoda_utshabalalo", "oyasumipunpun": "oyasumipunpun", "meliodas": "meliodas", "fudanshi": "fuda<PERSON><PERSON>", "retromanga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "highschoolofthedead": "isikololesifundilefileyo", "germantechno": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oshinoko": "<PERSON><PERSON><PERSON>", "ansatsukyoushitsu": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vindlandsaga": "vindlandsaga", "mangaka": "mangaka", "dbsuper": "dbsuper", "princeoftennis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tonikawa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "esdeath": "esdeath", "dokurachan": "<PERSON><PERSON><PERSON>", "bjalex": "b<PERSON><PERSON>", "assassinclassroom": "iklasiya<PERSON><PERSON><PERSON>", "animemanga": "animemanga", "bakuman": "bakuman", "deathparade": "umngcwaboluntu", "shokugekinosouma": "shoku<PERSON><PERSON><PERSON><PERSON>", "japaneseanime": "ianimeyasejapan", "animespace": "indawoyoopopayi", "girlsundpanzer": "amantombazaneetanki", "akb0048": "akb0048", "hopeanuoli": "ithembaumsebenzile", "animedub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animanga": "animanga", "tsurune": "<PERSON><PERSON><PERSON><PERSON>", "uqholder": "uqholder", "indieanime": "indieanime", "bungoustray": "bungoustray", "dagashikashi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gundam0": "gundam0", "animescifi": "animescifi", "ratman": "indodaempuku", "haremanime": "haremanime", "kochikame": "kochikame", "nekoboy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gashbell": "gashbell", "peachgirl": "intombikamdumbu", "cavalieridellozodiaco": "cavalieridellozodiaco", "mechamusume": "mechamusume", "nijigasaki": "<PERSON><PERSON><PERSON><PERSON>", "yarichinbitchclub": "yarichinbitchclub", "dragonquestdai": "dragonquestdai", "heartofmanga": "intliziyweyimanga", "deliciousindungeon": "kumnandie<PERSON><PERSON><PERSON><PERSON>i", "manhviyaoi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recordofragnarok": "irekhodi_yer<PERSON><PERSON>ok", "funamusea": "funamusiziki", "hiranotokagiura": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mangaanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bochitherock": "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kamisamahajimemashita": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "skiptoloafer": "tsi<PERSON>_uye_kwi<PERSON>afer", "shuumatsunovalkyrie": "shuumatsunovalkyrie", "tutorialistoohard": "itutorialinzima", "overgeared": "ugqi<PERSON><PERSON><PERSON>", "toriko": "<PERSON><PERSON>o", "ravemaster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kkondae": "kkondae", "chobits": "chobits", "witchhatatelier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lansizhui": "<PERSON><PERSON><PERSON><PERSON>", "sangatsunolion": "nding<PERSON><PERSON><PERSON><PERSON>", "kamen": "kamen", "mangaislife": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dropsofgod": "amath<PERSON><PERSON>_kathixo", "loscaballerosdelzodia": "amaqhawelomnya<PERSON>", "animeshojo": "animeshojo", "reverseharem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saintsaeya": "<PERSON><PERSON><PERSON>", "greatteacheronizuka": "utitshalamnandionizuka", "gridman": "gridman", "kokorone": "koko<PERSON>", "soldato": "soldato", "mybossdaddy": "umqeshiwam<PERSON>", "gear5": "gear5", "grandbluedreaming": "ukuphuphagranblue", "bloodplus": "igazi_plus", "bloodplusanime": "iga<PERSON>_kunye_neanime", "bloodcanime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bloodc": "<PERSON><PERSON><PERSON>", "talesofdemonsandgods": "amabaliamademonkanothixo", "goreanime": "buyaanime", "animegirls": "iintombizeanime", "sharingan": "<PERSON><PERSON>", "crowsxworst": "am<PERSON><PERSON>_a<PERSON>a", "splatteranime": "splatteranime", "splatter": "chachaza", "risingoftheshieldhero": "ukuvukakwe<PERSON><PERSON><PERSON><PERSON><PERSON>", "somalianime": "somalianime", "riodejaneiroanime": "riodejaneiroanime", "slimedattaken": "slimedondatyiweyo", "animeyuri": "animeyuri", "animeespaña": "animespain", "animeciudadreal": "animeciudadreal", "murim": "murim", "netjuunosusume": "netjuunosusume", "childrenofthewhales": "abantwanabeminenga", "liarliar": "umxokimxoki", "supercampeones": "supercampeones", "animeidols": "abalinganebeeanimeidol", "isekaiwasmartphone": "isekaiwasmartphone", "midorinohibi": "imihlagweluhlaza", "magicalgirls": "amantombazaneangomlingo", "callofthenight": "ubizong<PERSON><PERSON><PERSON>", "bakuganbrawler": "umdinibakugan", "bakuganbrawlers": "bakuganbrawlers", "natsuki": "natsuki", "mahoushoujo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shadowgarden": "umyezowesgadi", "tsubasachronicle": "tsubasachronicle", "findermanga": "fumanangemanga", "princessjellyfish": "inkosazanayejellyfish", "kuragehime": "k<PERSON><PERSON>e", "paradisekiss": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kurochan": "<PERSON><PERSON><PERSON>", "revuestarlight": "revuestarlight", "animeverse": "i<PERSON><PERSON><PERSON><PERSON><PERSON>", "persocoms": "iipersocoms", "omniscientreadersview": "umbukel<PERSON>_ofunda_yonke_into", "animecat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animerecommendations": "iingcebisozeanimation", "openinganime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shinichirowatanabe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uzumaki": "<PERSON><PERSON><PERSON><PERSON>", "myteenromanticcomedy": "iromantikakomeyaseyintsha", "evangelion": "evangelion", "gundam": "gundam", "macross": "macross", "gundams": "iigundams", "voltesv": "voltesv", "giantrobots": "iirobothezikhulu", "neongenesisevangelion": "neongenesisevangelion", "codegeass": "codegeass", "mobilefighterggundam": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "neonevangelion": "neonevangelion", "mobilesuitgundam": "mobilesuitgundam", "mech": "<PERSON><PERSON><PERSON>", "eurekaseven": "eurekaseven", "eureka7": "eureka7", "thebigoanime": "i<PERSON><PERSON><PERSON>", "bleach": "i<PERSON><PERSON><PERSON>", "deathnote": "incwadiyokufa", "cowboybebop": "cowboybebop", "jjba": "jjba", "jojosbizarreadventure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fullmetalalchemist": "ifullmetalalchemist", "ghiaccio": "<PERSON><PERSON><PERSON><PERSON>", "jojobizarreadventures": "jojobizarreadventures", "kamuiyato": "<PERSON><PERSON><PERSON><PERSON>", "militaryanime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "greenranger": "greenranger", "jimmykudo": "jimmy<PERSON><PERSON>", "tokyorev": "<PERSON><PERSON><PERSON><PERSON>", "zorro": "zorro", "leonscottkennedy": "leonscottkennedy", "korosensei": "k<PERSON><PERSON>sei", "starfox": "starfox", "ultraman": "ultraman", "salondelmanga": "salondelmanga", "lupinthe3rd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "animecity": "idolopholeanimation", "animetamil": "animetamil", "jojoanime": "jo<PERSON><PERSON><PERSON>", "naruto": "<PERSON><PERSON><PERSON>", "narutoshippuden": "narutoshippuden", "onepiece": "onepiece", "animeonepiece": "animeonepiece", "dbz": "dbz", "dragonball": "dragonball", "yugioh": "yugioh", "digimon": "digimon", "digimonadventure": "uhambodigimon<PERSON>", "hxh": "hxh", "highschooldxd": "highschooldxd", "goku": "goku", "broly": "broly", "shonenanime": "shone<PERSON><PERSON>", "bokunoheroacademia": "bokunoheroacademia", "jujustukaitsen": "jujubukel<PERSON>zo", "drstone": "d<PERSON><PERSON>", "kimetsunoyaiba": "kimetsunoyaiba", "shonenjump": "shonenjump", "otaka": "otaka", "hunterxhunter": "hunterxhunter", "mha": "mha", "demonslayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hinokamikagurademonsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attackontitan": "ukuhlaselwabamagorha", "erenyeager": "erenyeager", "myheroacademia": "myheroacademia", "boruto": "boruto", "rwby": "rwby", "dandadan": "da<PERSON><PERSON>", "tomodachigame": "umdlalowabahlobo", "akatsuki": "<PERSON><PERSON><PERSON>", "surveycorps": "abahlolis<PERSON><PERSON>", "onepieceanime": "onepieceanime", "attaquedestitans": "attaquedestitans", "theonepieceisreal": "theonepieceiyinyani", "revengers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mobpsycho": "mobpsycho", "aonoexorcist": "aonoexorcist", "joyboyeffect": "umoyowenkwenkwe", "digimonstory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "digimontamers": "digimontamers", "superjail": "ijelekakhulu", "metalocalypse": "imetalocalypse", "shinchan": "shinchan", "watamote": "watamote", "uramichioniisan": "uramichioniisan", "uruseiyatsura": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gintama": "gintama", "ranma": "ranma", "doraemon": "doraemon", "gto": "gto", "ouranhostclub": "iklabhuyethuhostclub", "flawlesswebtoon": "webcartoonegqibeleleyo", "kemonofriends": "abahloboasilwanyana", "utanoprincesama": "utanoprincesama", "animecom": "animecom", "bobobobobobobo": "bobob<PERSON><PERSON><PERSON><PERSON><PERSON>", "yuukiyuuna": "yu<PERSON><PERSON><PERSON>", "nichijou": "ubo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yurucamp": "yurucamp", "nonnonbiyori": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flyingwitch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wotakoi": "wotakoi", "konanime": "konanime", "clannad": "clannad", "justbecause": "ngobangalutho", "horimiya": "<PERSON><PERSON><PERSON>", "allsaintsstreet": "wonkesitalatolofini", "recuentosdelavida": "iintsomiencinci"}