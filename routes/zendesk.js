const express = require('express');
const router = express.Router();
const asyncHandler = require('express-async-handler');
const axios = require('axios');
const moment = require('moment');
const openai = require('../lib/openai');
const ZendeskAgent = require('../models/zendesk-agent');

const ZENDESK_API_TOKEN = process.env.ZENDESK_API_TOKEN;

let supportGroupId = 26850221996690;
let legalGroupId = 27005031563154;
let marketingGroupId = 27148558137618;
let ZENDESK_DOMAIN = 'boo-68123.zendesk.com';
let ZENDESK_EMAIL = '<EMAIL>';

if (process.env.NODE_ENV == 'beta') {
  supportGroupId = 47576789717913;
  legalGroupId = 47579559035161;
  marketingGroupId = 47641804710937;
  ZENDESK_DOMAIN = 'boo-40501.zendesk.com';
  ZENDESK_EMAIL = '<EMAIL>';
}

const AUTH = {
  username: `${ZENDESK_EMAIL}/token`,
  password: ZENDESK_API_TOKEN
};

// Get agents in group, sorted by ID, using Zendesk API
async function getSortedGroupAgentIds(groupId) {

  const res = await axios.get(
    `https://${ZENDESK_DOMAIN}/api/v2/groups/${groupId}/users.json`,
    {
      auth: AUTH,
    }
  );

  // "active" means not suspended or deleted
  const agents = res.data.users
    .filter(u => u.active)
    .sort((a, b) => a.id - b.id);

  return agents.map(x => x.id);
}

// Get round-robin agent from group
async function getNextAgent(groupId) {
  let agentIds;
  const doc = await ZendeskAgent.findOne({ groupId });
  if (doc && moment().diff(doc.cachedAt, 'hours') < 1) {
    agentIds = doc.agentIds;
  } else {
    agentIds = await getSortedGroupAgentIds(groupId);
    await ZendeskAgent.updateOne(
      { groupId },
      { agentIds, cachedAt: Date.now() },
      { upsert: true },
    );
  }
  if (agentIds.length == 0) {
    throw new Error('No active agents in group');
  }

  let lastId = doc?.lastUsedAgentId;
  const index = agentIds.findIndex(a => a === lastId);
  const nextIndex = index === -1 || index === agentIds.length - 1
    ? 0
    : index + 1;

  const nextAgent = agentIds[nextIndex];
  await ZendeskAgent.updateOne(
    { groupId },
    { lastUsedAgentId: nextAgent },
  );

  return nextAgent;
}

async function assignTicketToAgent(ticketId, agentId, comment) {
  const updateParams = {
    assignee_id: agentId,
  };
  if (comment) {
    updateParams.comment = {
      body: comment,
      public: false // internal note only
    }
  }
  console.log(`zendesk: assigning ticket ${ticketId} to agent ${agentId}`);
  await axios.put(
    `https://${ZENDESK_DOMAIN}/api/v2/tickets/${ticketId}.json`,
    {
      ticket: updateParams,
    },
    {
      auth: AUTH,
    }
  );
}

async function assignTicketToAgentInGroup(ticketId, groupId) {
  const agentId = await getNextAgent(groupId);
  await assignTicketToAgent(ticketId, agentId);
}

async function assignTicketToGroup(ticketId, groupId, comment) {
  const updateParams = {
    group_id: groupId,
  };
  if (comment) {
    updateParams.comment = {
      body: comment,
      public: false // internal note only
    }
  }

  await axios.put(
    `https://${ZENDESK_DOMAIN}/api/v2/tickets/${ticketId}.json`,
    {
      ticket: updateParams,
    },
    {
      auth: AUTH,
    }
  );
}

async function addTagsToTicket(ticketId, tags) {
  if (!Array.isArray(tags)) {
    tags = [ tags ];
  }

  await axios.put(
    `https://${ZENDESK_DOMAIN}/api/v2/tickets/${ticketId}/tags`,
    { tags },
    {
      auth: AUTH,
    }
  );
}

async function findPreviousAgent(requesterId, groupId) {
  const query = `type:ticket requester_id:"${requesterId}" group_id:${groupId} -assignee:none`;
  const res = await axios.get(
    `https://${ZENDESK_DOMAIN}/api/v2/search.json`,
    {
      params: {
        query,
        sort_by: 'updated_at',
        sort_order: 'desc',
        per_page: 1
      },
      auth: AUTH,
    }
  );

  return res.data.results[0]?.assignee_id;
}

async function markTicketSolved(ticketId, comment) {
  const updateParams = {
    status: 'solved',
  };
  if (comment) {
    updateParams.comment = {
      body: comment,
      public: false // internal note only
    }
  }

  await axios.put(
    `https://${ZENDESK_DOMAIN}/api/v2/tickets/${ticketId}.json`,
    {
      ticket: updateParams,
    },
    {
      auth: AUTH,
    }
  );
}

module.exports = function () {
  router.post('/', asyncHandler(async (req, res, next) => {
    console.log('zendesk: new ticket received:', JSON.stringify(req.body,null,2));
    const ticketId = req.body.detail.id;
    const subject = req.body.detail.subject;
    const body = req.body.detail.description;
    const requesterId = req.body.detail.requester_id;
    console.log(`zendesk: processing ticket ${ticketId}:`, JSON.stringify(req.body,null,2));

    // if requester has any prior tickets in legal group, this ticket should go to legal group
    const legalAgent = await findPreviousAgent(requesterId, legalGroupId);
    if (legalAgent) {
      console.log(`zendesk: requester ${requesterId} has prior tickets in legal group, assigning ticket ${ticketId} to legal group and agent ${legalAgent}`);
      await assignTicketToGroup(ticketId, legalGroupId, 'Assigning ticket to legal group because requester has prior tickets in legal group.');
      await assignTicketToAgent(ticketId, legalAgent);
      return res.json({});
    }

    const category = await openai.categorizeTicket(subject, body);
    if (category == 'legal') {
      console.log(`zendesk: assigning ticket ${ticketId} to legal group`);
      await assignTicketToGroup(ticketId, legalGroupId, 'Assigning ticket to legal group because classification returned legal.');
      await assignTicketToAgentInGroup(ticketId, legalGroupId);
    }
    else if (category == 'marketing') {
      console.log(`zendesk: assigning ticket ${ticketId} to marketing group`);
      await assignTicketToGroup(ticketId, marketingGroupId, 'Assigning ticket to marketing group because classification returned marketing.');
      await assignTicketToAgentInGroup(ticketId, marketingGroupId);
    }
    else {
      console.log(`zendesk: assigning ticket ${ticketId} to support group with tag ${category}`);
      await assignTicketToGroup(ticketId, supportGroupId, `Assigning ticket to support group with tag ${category}.`);
      await addTagsToTicket(ticketId, category);
      if (['unintelligible', 'spam'].includes(category)) {
        console.log(`zendesk: auto-closing ticket ${ticketId} due to ${category}`);
        await markTicketSolved(ticketId, `Auto-closing ticket due to ${category}.`);
      }
      else {
        const priorAgent = await findPreviousAgent(requesterId, supportGroupId);
        if (priorAgent) {
          console.log(`zendesk: requester ${requesterId} has prior tickets in support group, assigning ticket ${ticketId} to agent ${priorAgent}`);
          await assignTicketToAgent(ticketId, priorAgent, 'Assigning ticket to agent because agent previously handled tickets from this requester.');
        } else {
          console.log(`zendesk: ticket ${ticketId} will remain unassigned`);
        }
      }
    }
    return res.json({});
  }));

  return router;
};
