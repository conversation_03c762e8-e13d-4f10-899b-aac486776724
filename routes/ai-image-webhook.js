const express = require("express");
const router = express.Router();
const asyncHandler = require("express-async-handler");
const { handle } = require("../lib/ai-image/ai-image-data-update");
const httpErrors = require("../lib/http-errors");

module.exports = function () {
  router.post(
    "/",
    asyncHandler(async (req, res, next) => {
      const data = req.body;
      if (data?.file_prefix && data?.file_prefix === 'BOO') {
        await handle(data);
        return res.json({});
      }else{
        console.log(`Image Pipeline sent invalid webhook file_prefix : ${data.file_prefix}`);
        throw httpErrors.badRequestError();
      }
    })
  );

  return router;
};
